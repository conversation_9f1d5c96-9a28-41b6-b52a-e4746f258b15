# 工厂物品管理系统 (BizLinkSpeedy IDM)

基于 FastAPI + React + SQLite 的工厂物品耗材管理系统

## 🚀 快速开始

### 环境要求
- Python 3.12+
- Poetry (Python依赖管理)
- Node.js 16+
- npm

### 一键启动（推荐）

```bash
./dev.sh
```

这个脚本会自动：
- 检查环境依赖
- 使用Poetry安装后端依赖
- 安装前端依赖  
- 同时启动前后端服务
- 按 **Ctrl+C** 停止所有服务

### 手动启动（调试时使用）

**启动后端:**
```bash
cd backend
poetry install
poetry run uvicorn app.main:app --reload --port 8000
```

**启动前端:**
```bash
cd frontend
npm install
npm start
```

## 📋 访问地址

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 🔐 测试账号

| 用户名 | 密码 | 角色 |
|--------|------|------|
| admin | admin123 | 管理员 |
| test | test123 | 普通用户 |

## 📦 项目结构

```
bizlinkspeedy-idm/
├── backend/                 # FastAPI 后端
│   ├── app/
│   │   ├── api/            # API 路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据库模型
│   │   ├── schemas/        # Pydantic 模型
│   │   ├── services/       # 业务逻辑
│   │   └── main.py         # FastAPI 应用入口
│   ├── pyproject.toml      # Poetry 依赖配置
│   └── database.db         # SQLite 数据库
├── frontend/               # React 前端
│   ├── src/
│   │   ├── components/     # React 组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API 调用
│   │   └── App.tsx         # 主应用组件
│   └── package.json        # Node.js 依赖
├── dev.sh                  # 🚀 一键启动脚本
└── README.md               # 项目说明
```

## ✨ 功能特性

### 已完成功能
- ✅ 用户登录认证 (JWT)
- ✅ 物品管理和搜索
- ✅ 分类管理
- ✅ 供应商管理
- ✅ 库存管理
- ✅ 采购申请流程
- ✅ Pad端扫码领取
- ✅ 报表仪表板
- ✅ 通知系统
- ✅ 数据导入导出

### 技术亮点
- 🔥 **现代化技术栈**: FastAPI + React 18 + TypeScript
- 🤖 **AI 开发友好**: 完整类型提示，自动API文档
- ⚡ **开发体验优化**: 热重载，一键启动
- 📱 **响应式设计**: 支持桌面和移动端
- 🔐 **权限管理**: 基于JWT的用户认证

## 🛠️ 技术栈

**后端:**
- FastAPI - 现代化Python Web框架
- SQLAlchemy - ORM数据库操作
- Poetry - 依赖管理
- SQLite - 轻量级数据库
- JWT - 用户认证
- Pydantic - 数据验证

**前端:**
- React 18 - 用户界面框架
- TypeScript - 类型安全
- Ant Design - UI组件库
- Axios - HTTP客户端

## 🔧 故障排除

### 端口被占用
```bash
# 查看端口占用
lsof -i :8000  # 后端
lsof -i :3000  # 前端

# 杀死占用进程
kill -9 <PID>
```

### Poetry问题
```bash
# 安装Poetry
curl -sSL https://install.python-poetry.org | python3 -

# 重新安装依赖
cd backend && poetry install
```

### 清理环境
```bash
# 清理前端依赖
cd frontend && rm -rf node_modules && npm install

# 重新初始化数据库
cd backend && rm -f database.db && poetry run python -m app.init_db
```