---
description: 本规则规定了系统中库存的类型及适用范围及转换方式. 当涉及到库存, 物品数量时适用此规则
globs:
alwaysApply: false
---

# 库存单位转换规则

## 核心概念
- **库存单位**: 物品在库存管理中使用的单位（如"双"、"个"、"支"）
- **采购单位**: 物品在采购时使用的包装单位（如"盒"、"箱"、"包"）
- **转换系数(qty_per_up)**: 每个采购单位包含多少个库存单位
- **SPQ**: 标准包装数量，采购时必须以SPQ的整数倍进行

## 核心业务规则
1. **库存管理**: 使用库存单位记录和显示数量
2. **采购申请**: 使用采购单位提交，数量必须是SPQ的整数倍
3. **购物车**: 存储采购单位数量，显示时转换为库存单位
4. **数量计算**: 涉及库存和采购的计算都需要单位转换
5. **边界控制**: 采购后总库存不能超过最大库存

## 单位转换计算

### 基础转换公式
```
库存单位数量 = 采购单位数量 × qty_per_up
采购单位数量 = 库存单位数量 ÷ qty_per_up
```

### 核心计算函数
```typescript
// 库存单位 → 采购单位（向上取整）
function inventoryToPurchaseUnitCeil(invQty: number, qtyPerUp: number): number {
  return Math.ceil(invQty / qtyPerUp);
}

// 计算符合SPQ要求的采购数量
function calculateSPQQuantity(needed: number, max: number, spq: number): number {
  const spqNeeded = Math.ceil(needed / spq) * spq;
  const spqMax = Math.floor(max / spq) * spq;
  return Math.min(spqNeeded, spqMax);
}
```

## 数据存储规则

### 数据库字段
- **库存表**: `current_quantity`, `min_quantity`, `max_quantity` 使用库存单位
- **购物车表**: `spq_count` 使用采购单位
- **物品表**: `inventory_unit`, `purchase_unit`, `qty_per_up`
- **供应商表**: `spq` 标准包装数量

### 界面显示
- **库存页面**: 显示库存单位和数量
- **购物车页面**: 显示采购单位和SPQ数量，同时显示对应的库存单位数量
- **采购申请**: 提交采购单位数量，必须是SPQ的整数倍

## 常见错误和避免方法

### 1. 单位混用错误
```typescript
// ❌ 错误：直接将库存数量当作采购数量
const cartQuantity = inventory.current_quantity;

// ✅ 正确：进行单位转换
const cartQuantity = Math.ceil(inventory.current_quantity / inventory.qty_per_up);
```

### 2. SPQ计算错误
```typescript
// ❌ 错误：忽略SPQ限制
const purchaseQuantity = neededQuantity;

// ✅ 正确：应用SPQ限制
const purchaseQuantity = Math.ceil(neededQuantity / spq) * spq;
```

### 3. 库存溢出错误
```typescript
// ❌ 错误：不检查最大库存限制
const finalQuantity = Math.ceil(neededQuantity / spq) * spq;

// ✅ 正确：确保不超过最大库存
const maxAllowed = Math.floor(maxAdditionalQuantity / spq) * spq;
const finalQuantity = Math.min(Math.ceil(neededQuantity / spq) * spq, maxAllowed);
```

## 实际应用场景

### 自动补货计算
1. 检测缺货物品（库存 < 最小库存）
2. 计算补货到目标库存所需的采购数量
3. 应用SPQ限制和最大库存限制

### 购物车数量验证
1. 用户输入采购单位数量
2. 系统验证是否为SPQ的整数倍
3. 转换为库存单位显示预计增加的库存量

## 相关文件引用
- 核心逻辑实现: [frontend/src/apps/admin/pages/InventoryManagement.tsx](mdc:frontend/src/apps/admin/pages/InventoryManagement.tsx)
- 数据模型定义: [backend/app/models/item.py](mdc:backend/app/models/item.py)
- 库存服务: [backend/app/services/inventory.py](mdc:backend/app/services/inventory.py)
- 核心逻辑实现: [frontend/src/apps/admin/pages/InventoryManagement.tsx](mdc:frontend/src/apps/admin/pages/InventoryManagement.tsx)
- 数据模型定义: [backend/app/models/item.py](mdc:backend/app/models/item.py)
- 供应商模型: [backend/app/models/supplier.py](mdc:backend/app/models/supplier.py)
- 库存服务: [backend/app/services/inventory.py](mdc:backend/app/services/inventory.py)