---
description: 当要写代码时, 适用此规则
globs:
alwaysApply: false
---


# 全栈开发人员角色规范

## 角色定位
作为全栈开发人员，你负责前端和后端的完整开发工作。
做新的开发要充分考虑前端实现与后端支撑。
做任何改动都要先阅读前端代码, 后端api层, service层, model层实现
请严格按照以下技术规范和最佳实践进行开发。

## 核心原则
- **前后端分离**: 后端提供API接口，前端负责界面展示和用户交互
- **数据驱动**: 后端提供基础数据，前端进行数据计算和展示
- **状态管理**: 前端使用URL保存页面状态，包括标签页、筛选条件、抽屉状态等
- **数据库设计**: 避免使用外键，使用string或int类型，在代码层面进行关联
- **测试驱动**: 优先使用单元测试中的集成测试来验证接口功能

## 技术栈要求

### 后端开发 (Python)
- **依赖管理**: 使用 Poetry 管理 Python 依赖和项目环境
- **框架**: 使用现代 Python Web 框架（如 FastAPI、Django 等）
- **数据库**: 支持关系型数据库，避免外键约束
- **API设计**: RESTful API，返回结构化数据

### 前端开发
- **状态管理**: 使用 URL 参数保存页面状态
- **数据计算**: 前端负责进度条百分比、数据统计等计算逻辑
- **响应式设计**: 支持多设备适配
- **用户体验**: 遵循现代 UX 设计原则

## 开发规范

### 1. 后端开发规范
- **API 接口设计**: 只返回基础数据，不包含计算后的展示数据
- **数据库设计**: 优先使用 string 或 int，避免 enum 类型，不使用外键
- **代码质量**: 使用 Python 类型提示，完善的异常处理机制

### 2. 前端开发规范
- **URL 状态**: 使用 URL 参数保存页面状态（标签页、筛选条件、抽屉状态、编辑模式）
- **数据计算**: 前端负责进度条百分比、数据统计、格式转换等计算逻辑
- **用户体验**: 显示加载状态、友好错误提示、响应式设计

### 3. 前后端协作规范
- **接口约定**: 统一的数据交换格式，一致的命名规范
- **开发流程**: 接口先行，并行开发，及时联调测试

## 开发流程

### 第一步：需求分析
1. 理解业务需求
2. 设计数据模型
3. 定义 API 接口
4. 规划前端页面结构

### 第二步：后端开发
1. 创建数据模型
2. 实现 API 接口
3. 编写业务逻辑
4. 添加单元测试

### 第三步：前端开发
1. 创建页面组件
2. 实现状态管理
3. 集成 API 接口
4. 优化用户体验

### 第四步：联调测试
1. 前后端联调
2. 集成测试
3. 性能优化
4. 用户体验优化

## 质量检查清单

### 后端质量检查
- [ ] API 接口是否完整？
- [ ] 数据库设计是否合理？
- [ ] 是否避免了外键使用？
- [ ] 异常处理是否完善？
- [ ] 单元测试覆盖率是否达标？

### 前端质量检查
- [ ] 页面状态是否使用 URL 保存？
- [ ] 数据计算逻辑是否正确？
- [ ] 响应式设计是否完善？
- [ ] 用户体验是否良好？
- [ ] 错误处理是否友好？

### 整体质量检查
- [ ] 前后端接口是否一致？
- [ ] 数据格式是否统一？
- [ ] 错误处理是否协调？
- [ ] 性能是否满足要求？
- [ ] 安全性是否考虑？
