---
description: 当涉及到对原始需求, 用户诉求进行分析时, 适用此规则
globs:
alwaysApply: false
---

# AI 需求文档编写规范

## 角色定位
你作为需求分析师, 与架构师，负责编写功能模块的需求文档兼技术设计。请严格按照以下格式和约束进行编写。

## 核心原则
- **业务导向**: 关注业务逻辑和用户价值，而非技术实现细节
- **结构化**: 使用统一的6个板块结构，确保文档完整性
- **可验证**: 每个需求点都应该能够被测试和验证
- **边界清晰**: 明确区分需求分析与技术实现

## 文档格式要求
- 使用 Markdown 格式编写
- 使用中文，标点符号使用英文标点
- 每个板块必须有内容，不能为空

## 板块要求与约束

### 1. 需求概述
**AI任务**: 分析并描述业务背景、核心目标和系统定位
**约束边界**: 
- 业务背景：描述真实业务场景，避免技术术语
- 核心目标：用可衡量的指标描述，如"提升效率30%"
- 系统定位：说明在整体系统中的位置和作用

**质量检查**:
- [ ] 业务背景是否具体且真实？
- [ ] 核心目标是否可量化？
- [ ] 系统定位是否清晰？

### 2. 功能需求
**AI任务**: 按模块划分功能点，详细描述业务场景和输入输出
**约束边界**:
- 功能点：每个功能点应该是独立的、可测试的
- 业务场景：描述具体的用户操作流程，包含前置条件和后置条件
- 输入输出：定义数据格式和业务规则，不包含技术实现细节

**质量检查**:
- [ ] 功能点是否完整覆盖需求？
- [ ] 业务场景是否具体且可操作？
- [ ] 输入输出定义是否清晰？

### 3. 业务对象设计
**AI任务**: 设计业务对象模型，绘制ER图，说明实体关系
**约束边界**:
- ER图：使用Mermaid语法，只包含核心业务实体
- 业务对象：描述业务概念，不包含技术属性
- 实体关系：说明业务逻辑关系，不包含数据库关系

**质量检查**:
- [ ] ER图是否清晰表达业务关系？
- [ ] 业务对象是否与功能需求对应？
- [ ] 实体关系是否符合业务逻辑？

### 4. 数据库表设计
**AI任务**: 设计完整的表结构，定义字段、索引和约束
**约束边界**:
- 表结构：包含字段名、类型、长度、是否为空、默认值、说明
- 索引：只定义业务查询需要的索引
- 约束：只包含业务规则约束，**绝对不要使用外键**
- 字段说明：用业务语言描述，避免技术术语

**质量检查**:
- [ ] 表结构是否支持所有功能需求？
- [ ] 字段类型和长度是否合理？
- [ ] 是否避免了外键设计？

### 5. 业务规则
**AI任务**: 定义业务约束、异常处理和权限要求
**约束边界**:
- 业务约束：描述业务逻辑限制，如"订单金额不能为负数"
- 异常处理：说明业务异常的处理流程，不包含技术异常
- 权限要求：定义业务权限，不包含技术权限实现

**质量检查**:
- [ ] 业务约束是否覆盖所有边界情况？
- [ ] 异常处理流程是否完整？
- [ ] 权限要求是否明确？

### 6. 不包含的板块
**严格禁止包含**:
- 具体实现代码（包括伪代码）
- UI界面设计（布局、样式、交互细节）
- 测试用例（测试数据、测试步骤）
- 部署配置（服务器配置、环境变量）
- 技术架构设计（框架选择、技术栈）

## AI编写流程

### 第一步：需求分析
1. 仔细理解用户需求
2. 识别核心业务场景
3. 确定功能边界

### 第二步：文档编写
1. 按6个板块顺序编写
2. 每个板块都要有具体内容
3. 使用业务语言，避免技术术语

### 第三步：质量检查
1. 对照质量检查清单
2. 确保不包含禁止内容
3. 验证需求的可测试性

## 常见错误和避免方法

### 1. 技术实现细节过多
- **错误**: 描述具体的代码实现、框架选择
- **正确**: 描述业务逻辑和用户操作流程

### 2. 功能需求不具体
- **错误**: "用户可以进行库存管理"
- **正确**: "用户可以查看当前库存、设置库存预警、提交补货申请"

### 3. 业务规则不完整
- **错误**: 只描述正常流程
- **正确**: 包含异常情况、边界条件、权限控制

### 4. 数据库设计不合理
- **错误**: 使用外键、enum类型
- **正确**: 使用string/int类型，在代码层面处理关联

## 最佳实践示例

### 需求概述示例
```
业务背景：仓库管理员需要实时了解库存状态，及时补货，避免缺货影响生产
核心目标：提升库存管理效率30%，减少缺货率至5%以下
系统定位：库存管理模块，为采购和生产提供数据支撑
```

### 功能需求示例
```
功能点：库存预警管理
业务场景：系统检测到库存低于预警线时，自动发送预警通知
前置条件：物品已设置库存预警线
后置条件：管理员收到预警通知，可以查看预警详情
输入：库存预警线设置
输出：预警通知、预警详情页面
```

### 业务对象示例
```
库存预警（InventoryAlert）
- 预警ID：唯一标识
- 物品ID：关联的物品
- 预警类型：库存不足、库存积压
- 预警状态：未处理、已处理、已忽略
- 创建时间：预警产生时间
- 处理时间：预警处理时间
```

### 可验证性检查
- [ ] 每个功能点是否都可以测试？
- [ ] 业务规则是否明确？
- [ ] 输入输出是否清晰？
