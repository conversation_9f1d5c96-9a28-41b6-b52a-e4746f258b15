---
description: 本规则规定后端FastAPI项目的API层异常处理模式. 当编写后端API时适用此规则
globs: *.py
alwaysApply: false
---

# FastAPI 异常处理规范

## 核心原则

### 1. 异常处理策略
- **服务层异常直接传播**：服务层的异常应该直接向上传播，不要在API层包装
- **避免过度包装**：不要使用复杂的try-catch包装，保持代码简洁
- **异常信息完整性**：确保异常信息不丢失，便于问题排查

### 2. 标准异常处理方式

#### 推荐方式（简洁直接）
```python
@router.post("", response_model=ResponseModel)
async def create_resource(
    request_data: CreateRequest,
    db: Session = Depends(get_db)
):
    """创建资源"""
    service = ResourceService(db)
    resource = service.create_resource(request_data)
    return resource
```

## 异常类型和HTTP状态码

### 标准异常类型
- **400 Bad Request**: 请求参数错误、业务逻辑错误
- **401 Unauthorized**: 未认证或认证失败
- **403 Forbidden**: 权限不足
- **404 Not Found**: 资源不存在
- **409 Conflict**: 资源冲突（如重复创建）
- **422 Unprocessable Entity**: 请求数据验证失败
- **500 Internal Server Error**: 系统内部错误

### 自定义异常类
```python
class BusinessException(Exception):
    """业务逻辑异常"""
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class ResourceNotFoundException(Exception):
    """资源不存在异常"""
    pass

class ValidationException(Exception):
    """数据验证异常"""
    pass
```

## 最佳实践

### 1. 异常处理流程
1. **服务层抛出异常**：使用具体的异常类型
2. **API层直接调用**：不包装异常，让FastAPI处理
3. **全局异常处理器**：统一处理异常，返回标准格式

### 2. 日志记录
- **服务层**：记录详细的业务操作日志和异常上下文
- **API层**：记录请求日志，不重复记录异常详情
- **全局处理器**：记录未处理的异常


## 相关文件引用
- [全局异常处理器](mdc:backend/app/core/exceptions.py)
- [自定义异常类](mdc:backend/app/core/exceptions.py)
- [服务层示例](mdc:backend/app/services/base_service.py)

