---
alwaysApply: true
---


# 单服务多客户端Monorepo项目规则

## 项目结构概述

```
bizlink-idm/
├── backend/                    # FastAPI 后端服务
│   ├── app/
│   │   ├── apps/              # 应用特定模块
│   │   │   ├── admin/         # 管理应用
│   │   │   ├── kiosk/         # 自助机应用  
│   │   │   └── inbound/       # 入库应用
│   │   ├── models/            # 共享SQLAlchemy模型
│   │   ├── services/          # 共享业务逻辑
│   │   ├── core/              # 核心配置和工具
│   │   └── schemas/           # 共享Pydantic模式
│   ├── static/                # 静态文件服务
│   │   ├── admin/             # 管理SPA静态文件
│   │   ├── kiosk/             # 自助机SPA静态文件
│   │   └── inbound/           # 入库SPA静态文件
│   ├── uploads/               # 文件上传存储
│   ├── tests/                 # 后端测试套件
│   ├── scripts/               # 数据初始化脚本
│   ├── pyproject.toml         # Poetry依赖管理
│   └── poetry.lock            # Poetry锁定文件
├── frontend/                   # React前端应用
│   ├── src/
│   │   ├── apps/              # 应用特定模块
│   │   │   ├── admin/         # 管理页面和组件
│   │   │   ├── kiosk/         # 自助机页面和组件
│   │   │   └── inbound/       # 入库页面和组件
│   │   ├── shared/            # 共享模块
│   │   │   ├── components/    # 通用UI组件
│   │   │   ├── hooks/         # 通用React钩子
│   │   │   ├── services/      # 通用API服务
│   │   │   ├── utils/         # 工具函数
│   │   │   └── config/        # 配置文件
│   │   ├── App.tsx            # 主路由配置
│   │   └── index.tsx          # 应用入口点
│   ├── scripts/               # 构建和部署脚本
│   ├── craco.config.js        # CRACO路径别名配置
│   ├── tsconfig.json          # TypeScript配置
│   ├── package.json           # NPM依赖管理
│   └── package-lock.json      # NPM锁定文件
└── doc/                       # 项目文档
```

## 后端架构原则

### 1. 单一FastAPI服务
- **一个后端服务**: 处理所有三个客户端应用
- **应用模块结构**: 每个应用拥有自己的API路由和模式
- **共享资源**: 模型、服务和核心逻辑在应用间共享
- **无数据库约束**: 避免DB枚举和外键，在代码层面强制关系

### 2. 代码质量和标准

#### 导入路径约定
- **应用模块**: 在应用目录内使用相对路径
- **共享模块**: 使用 `@shared/*` 别名访问共享资源
- **跨应用**: 使用 `@admin/*`, `@kiosk/*`, `@inbound/*` 别名

#### 状态管理
- **URL状态**: 所有UI状态（标签页、过滤器、抽屉、编辑）必须在URL中
- **React钩子**: 使用 `useSearchParams` 和 `useNavigate` 进行状态管理
- **无本地状态**: 避免不在URL中反映的组件级状态

### 3. 测试策略
- **后端**: 使用pytest + httpx进行集成测试
- **前端**: 使用React Testing Library进行组件测试
- **API测试**: 分别测试所有三个应用的API端点

### 4. 数据库设计
- **无外键**: 使用string/int字段，在代码层面强制关系
- **无枚举**: 使用string字段，在模式中进行验证
- **灵活模式**: 支持多个数据库后端（PostgreSQL、MySQL）

## 前端架构原则

### 1. 应用隔离
- **模块化设计**: 每个应用模块自包含
- **共享资源**: 通用功能提取到共享模块
- **无重复**: 避免在应用模块间重复服务或模型

### 2. 状态管理
- **URL驱动**: 所有页面状态保存在URL中
- **路由管理**: 支持浏览器前进后退
- **状态同步**: 确保URL与页面状态同步

### 3. 构建和部署
- **单一构建**: 前端构建分发到三个静态目录
- **SPA回退**: 后端为深度链接提供回退

## 迁移和维护

### 1. 导入路径更新
- **可用脚本**: `frontend/scripts/` 中有多个路径更新脚本
- **自动修复**: 脚本自动处理导入路径迁移
- **路径别名**: 使用别名避免相对路径复杂性

### 2. 代码组织
- **应用隔离**: 每个应用模块自包含
- **共享资源**: 通用功能提取到共享模块
- **无重复**: 避免在应用模块间重复服务或模型

### 3. 部署考虑
- **单一后端**: 一个FastAPI服务处理所有三个客户端
- **静态分发**: 前端构建分发到三个静态目录
- **SPA回退**: 后端为深度链接提供回退

## 开发最佳实践

### 1. 模块开发
- **单一职责**: 每个模块专注于特定功能域
- **接口设计**: 定义清晰的模块间接口
- **依赖管理**: 最小化模块间依赖

### 2. 代码共享
- **通用组件**: 提取可重用的UI组件
- **工具函数**: 共享通用工具和实用函数
- **类型定义**: 共享TypeScript类型定义

### 3. 测试策略
- **单元测试**: 测试单个模块的功能
- **集成测试**: 测试模块间的交互
- **端到端测试**: 测试完整的用户流程


- Backend dependency management and execution must use Poetry.

