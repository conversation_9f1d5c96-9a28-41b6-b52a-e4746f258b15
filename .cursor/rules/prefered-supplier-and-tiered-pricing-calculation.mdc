---
description: 当涉及物品供应商与价格时, 适用此规则
globs:
alwaysApply: false
---


# 物品预估价格计算规则

## 核心原则

* 物品默认供应商为最优先供应商, 物品价格是该供应商提供的物品价格
* 物品的预估价格必须从**最优先供应商**（priority = 0）提供的**阶梯价格**计算得出，而不是使用固定价格或其他供应商的价格。

## 价格计算逻辑

### 1. 供应商优先级规则
- **Preferred供应商**: `priority = 0` - 最优先，用于计算预估价格
- **Alternative供应商**: `priority >= 1` - 备选供应商，按数值排序, 最优先供应商缺失或不可用时选取优先级最高的那个

### 2. 阶梯价格匹配规则
```python
# 计算总数量：SPQ数量 × SPQ个数
total_quantity = float(item.spq_quantity) * item.spq_count

# 查询最优先供应商的阶梯价格
price_record = db.query(SupplierPrice).join(
    ItemSupplier, ItemSupplier.id == SupplierPrice.item_supplier_id
).filter(
    ItemSupplier.item_id == item.item_id,
    ItemSupplier.priority == 0,  # 最优先供应商
    ItemSupplier.status == "active",
    SupplierPrice.status == "active",
    SupplierPrice.valid_from <= current_time,
    (SupplierPrice.valid_to.is_(None) | (SupplierPrice.valid_to > current_time)),
    SupplierPrice.min_quantity <= total_quantity,
    (SupplierPrice.max_quantity.is_(None) | (SupplierPrice.max_quantity >= total_quantity))
).order_by(SupplierPrice.unit_price.asc()).first()
```

### 3. 价格计算步骤
1. **确定供应商**: 选择 `priority = 0` 的活跃供应商
2. **匹配阶梯**: 根据申请数量匹配对应的阶梯价格区间
3. **计算单价**: 使用匹配阶梯的单价
4. **计算总价**: `总数量 × 单价 + 运费`

## 实现要求

### 在 PurchaseRequestService 中
- `_fill_missing_item_info()` 方法必须使用阶梯价格计算
- `_lock_final_prices()` 方法必须使用阶梯价格锁定最终价格
- 所有价格计算必须优先考虑 `priority = 0` 的供应商

### 在数据模型中
- `SupplierPrice`: `min_quantity` 和 `max_quantity` 定义阶梯区间，`unit_price` 为对应阶梯的单价
- `ItemSupplier`: `priority` 字段决定供应商优先级，`priority = 0` 表示最优先供应商

## 价格回退机制

如果最优先供应商没有阶梯价格，按以下顺序回退：
1. 最优先供应商的基础价格（`updated_at` 最新的）
2. 备选供应商的阶梯价格
3. 备选供应商的基础价格
4. 默认价格为 0

## 注意事项

1. **优先级不可变**: 一旦确定最优先供应商，必须使用其价格计算
2. **阶梯价格优先**: 阶梯价格比基础价格优先级更高
3. **数量匹配**: 申请数量必须在阶梯价格的数量范围内
4. **有效期检查**: 只使用当前有效的价格记录
5. **运费计算**: 运费必须计入总价计算

## 相关文件
- [PurchaseRequestService](mdc:backend/app/services/purchase_request_service.py)
- [SupplierPrice Model](mdc:backend/app/models/supplier.py)
- [ItemSupplier Model](mdc:backend/app/models/supplier.py)

- [PurchaseRequestService](mdc:backend/app/services/purchase_request_service.py)
- [SupplierPrice Model](mdc:backend/app/models/supplier.py)
- [ItemSupplier Model](mdc:backend/app/models/supplier.py)
- [PurchaseRequestItem Model](mdc:backend/app/models/purchase.py)
