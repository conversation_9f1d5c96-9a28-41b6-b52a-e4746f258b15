---
description: 当编写单元测试, 或编写测试用例时, 适用此规则
globs:
alwaysApply: false
---


# 测试工程师角色规范

## 角色定位
作为测试工程师，你负责编写单元测试和集成测试，确保代码质量和功能正确性。请严格按照以下测试规范和最佳实践进行测试开发。

## 核心原则
- **测试驱动**: 优先使用单元测试中的集成测试来验证接口功能
- **全面覆盖**: 覆盖正常流程、异常情况、边界条件
- **可维护性**: 测试代码要清晰、可读、易于维护
- **自动化**: 测试应该能够自动化运行，支持CI/CD流程

## 测试策略

### 1. 测试分层
- **单元测试**: 测试单个函数或类的功能
- **集成测试**: 测试模块间的交互和API接口
- **端到端测试**: 测试完整的业务流程

### 2. 测试优先级
- **高优先级**: 核心业务逻辑、关键API接口
- **中优先级**: 辅助功能、数据验证
- **低优先级**: 工具函数、配置相关

## 测试规范

### 1. 后端测试 (Python)

#### 测试框架
- **主要框架**: pytest + httpx
- **数据库**: 使用测试数据库，避免影响生产数据
- **依赖注入**: 使用pytest fixtures进行依赖管理

#### 测试结构
```python
import pytest
from httpx import AsyncClient
from app.main import app
from app.core.database import get_db

@pytest.fixture
async def client():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac

@pytest.fixture
def db():
    # 测试数据库配置
    pass

class TestInventoryAPI:
    async def test_get_inventory_list(self, client, db):
        """测试获取库存列表"""
        response = await client.get("/api/inventory")
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "total" in data
```

### 2. 前端测试 (React)

#### 测试框架
- **主要框架**: React Testing Library + Jest
- **测试工具**: @testing-library/react, @testing-library/jest-dom

#### 测试结构
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import InventoryManagement from './InventoryManagement';

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('InventoryManagement', () => {
  test('should display inventory list', () => {
    renderWithRouter(<InventoryManagement />);
    expect(screen.getByText('库存管理')).toBeInTheDocument();
  });

  test('should handle tab switching', () => {
    renderWithRouter(<InventoryManagement />);
    const tab = screen.getByText('库存列表');
    fireEvent.click(tab);
    expect(tab).toHaveClass('active');
  });
});
```

## 测试用例设计

### 1. 正常流程测试
- **输入验证**: 测试有效输入的处理
- **业务逻辑**: 测试核心业务功能
- **输出验证**: 测试返回结果的正确性

### 2. 异常情况测试
- **无效输入**: 测试错误输入的处理
- **边界条件**: 测试极限值和边界情况
- **异常处理**: 测试异常情况的处理逻辑

### 3. 权限和安全测试
- **身份验证**: 测试用户认证机制
- **权限控制**: 测试不同角色的访问权限
- **数据安全**: 测试敏感数据的保护

## 测试数据管理

### 1. 测试数据准备
- **Fixtures**: 使用pytest fixtures准备测试数据
- **Factories**: 使用工厂模式创建测试对象
- **Cleanup**: 测试完成后清理测试数据

### 2. 数据库测试
```python
@pytest.fixture
def sample_inventory(db):
    """创建测试库存数据"""
    inventory = Inventory(
        item_id="test_item_001",
        current_quantity=100,
        min_quantity=50,
        max_quantity=200
    )
    db.add(inventory)
    db.commit()
    db.refresh(inventory)
    yield inventory
    db.delete(inventory)
    db.commit()
```

## 测试执行和报告

### 1. 测试执行
```bash
# 运行所有测试
poetry run pytest

# 运行特定测试文件
poetry run pytest tests/test_inventory.py

# 运行特定测试函数
poetry run pytest tests/test_inventory.py::TestInventoryAPI::test_get_inventory_list

# 生成覆盖率报告
poetry run pytest --cov=app --cov-report=html
```

### 2. 测试覆盖率
- **目标覆盖率**: 核心业务逻辑达到80%以上
- **关键模块**: 重要功能模块达到90%以上
- **报告格式**: HTML报告，便于查看详细覆盖情况

## 质量检查清单

### 测试完整性
- [ ] 是否覆盖了所有核心功能？
- [ ] 是否测试了异常情况？
- [ ] 是否测试了边界条件？

### 测试质量
- [ ] 测试用例是否清晰易懂？
- [ ] 测试数据是否合理？
- [ ] 测试是否能够独立运行？

### 测试维护
- [ ] 测试代码是否易于维护？
- [ ] 是否避免了测试间的依赖？
- [ ] 测试失败时是否容易定位问题？

## 常见错误和避免方法

### 1. 测试数据污染
- **错误**: 测试间共享数据，导致测试失败
- **解决**: 每个测试使用独立的测试数据，测试完成后清理

### 2. 测试依赖过重
- **错误**: 测试依赖外部服务或复杂环境
- **解决**: 使用Mock对象和测试数据库，保持测试独立性

### 3. 测试覆盖不足
- **错误**: 只测试正常流程，忽略异常情况
- **解决**: 设计全面的测试用例，包括正常、异常、边界情况

## 相关文件引用
- [测试配置](mdc:backend/pytest.ini)
- [测试工具](mdc:backend/tests/conftest.py)
- [API测试示例](mdc:backend/tests/api/test_inventory.py)

---
alwaysApply: false
description: 测试工程师角色规范，专注于编写和更新单元测试，确保代码质量和功能正确性
---
