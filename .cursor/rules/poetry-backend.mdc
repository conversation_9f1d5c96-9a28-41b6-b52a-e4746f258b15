---
description: 本规则规定了后端项目使用的技术, 当涉及后端内容时适用该规则
globs:
alwaysApply: false
---


# Poetry 后端管理规则

## 重要说明

- **依赖管理**: 使用 `pyproject.toml` 和 `poetry.lock` 管理Python包依赖
- **虚拟环境**: Poetry自动创建和管理虚拟环境
- **安装依赖**: 使用 `poetry install` 而不是 `pip install`
- **运行脚本**: 使用 `poetry run python script.py` 在虚拟环境中运行Python脚本
- **添加依赖**: 使用 `poetry add package_name` 添加新依赖
- **开发依赖**: 使用 `poetry add --group dev package_name` 添加开发依赖

## 常用命令

```bash
# 安装所有依赖
poetry install

# 在虚拟环境中运行Python脚本
poetry run python script.py

# 添加新依赖
poetry add pandas openpyxl

# 添加开发依赖
poetry add --group dev pytest

# 激活虚拟环境
poetry shell

# 查看项目信息
poetry show
```

## 注意事项

- 不要直接使用 `pip install`，应该使用 `poetry add`
- 运行Python脚本时使用 `poetry run` 前缀
- 项目配置文件是 `pyproject.toml`，不是 `requirements.txt`
- 确保在backend目录中执行poetry命令

- 项目配置文件是 `pyproject.toml`，不是 `requirements.txt`
- 确保在backend目录中执行poetry命令
