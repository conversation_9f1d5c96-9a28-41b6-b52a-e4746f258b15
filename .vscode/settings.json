{
    "python.venvPath": "~/.cache/pypoetry/virtualenvs",
    "python.analysis.extraPaths": [
      "${workspaceFolder}/backend"
    ],    
    "python.testing.pytestArgs": [
        "${workspaceFolder}/backend"
    ],
    "python.testing.unittestEnabled": false,
    "python.testing.pytestEnabled": true,
    "files.exclude": {
        "**/.git": true,
        "**/.svn": true,
        "**/.hg": true,
        "**/CVS": true,
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
        "**/__pycache__": true,
        "**/node_modules": true,
        "frontend/build": true,
    }    
}