# 购物车库存检查功能说明

## 功能概述

本功能为购物车中的物品添加了库存量检查，当购物车中的物品数量超过最大库存限制时，系统会自动生成本地文件提示管理员，并在前端界面显示相应的警告信息。

## 主要特性

### 1. 自动库存检查
- 在添加物品到购物车时自动检查库存状态
- 实时计算购买后的库存总量
- 自动识别超储物品

### 2. 超储预警
- 当物品购买后将超过最大库存时，自动生成预警文件
- 预警文件包含详细的物品信息和库存数据
- 支持多部门独立预警

### 3. 前端提示
- 在购物车页面显示库存状态概览
- 超储物品高亮显示，包含详细警告信息
- 提供手动库存检查功能

## 技术实现

### 后端服务

#### 1. 库存检查服务 (`InventoryCheckService`)
```python
# 主要方法
- check_cart_item_inventory(): 检查单个购物车项目的库存
- check_department_cart_inventory(): 检查部门购物车的所有物品
- generate_overstock_alert_file(): 生成超储预警文件
- check_and_alert_cart_inventory(): 检查并生成预警
```

#### 2. 购物车服务集成
- 在获取购物车项目时自动包含库存检查信息
- 在添加物品到购物车时自动检查库存状态
- 支持库存检查结果的实时更新

#### 3. API端点
```
GET /api/admin/purchase/cart/department/{department_id}/inventory-check
- 检查部门购物车的库存状态

GET /api/admin/purchase/cart/inventory-alerts/files
- 获取库存预警文件列表

DELETE /api/admin/purchase/cart/inventory-alerts/files/{filename}
- 删除指定的预警文件
```

### 前端组件

#### 1. 库存检查提示组件 (`InventoryCheckAlert`)
- 显示超储警告信息
- 提供库存状态概览
- 支持查看详情操作

#### 2. 购物车页面集成
- 在购物车页面顶部显示库存检查提示
- 提供手动库存检查按钮
- 实时显示库存状态信息

## 使用方法

### 1. 自动检查
当用户添加物品到购物车时，系统会自动：
1. 检查当前库存量
2. 计算购买后的总库存量
3. 与最大库存限制比较
4. 如果超储，自动生成预警文件

### 2. 手动检查
用户可以在购物车页面：
1. 点击"检查库存"按钮
2. 查看库存检查结果
3. 识别超储物品
4. 调整采购数量

### 3. 查看预警文件
系统会在 `inventory_alerts` 目录下生成JSON格式的预警文件：
```json
{
  "alert_type": "overstock_warning",
  "department_id": 1,
  "department_name": "测试部门",
  "generated_at": "2024-01-01T10:00:00",
  "overstock_items": [...],
  "summary": {
    "total_items": 2,
    "total_cart_quantity": 100,
    "total_after_purchase": 150
  }
}
```

## 配置说明

### 1. 库存预警目录
默认预警文件存储目录：`inventory_alerts/`
可通过修改 `InventoryCheckService` 中的 `alerts_dir` 属性来自定义

### 2. 预警文件命名规则
```
overstock_alert_{部门名称}_{时间戳}.json
例如：overstock_alert_生产部_20240101_100000.json
```

### 3. 权限要求
- `cart.view`: 查看购物车和库存检查结果
- `cart.add_item`: 添加物品到购物车（自动触发库存检查）
- `cart.update_item`: 更新购物车项目（自动触发库存检查）

## 测试

### 运行单元测试
```bash
cd backend
poetry run pytest tests/test_inventory_check_service.py -v
```

### 测试场景
1. 无库存记录的情况
2. 有最大库存限制的正常情况
3. 超储警告情况
4. 部门购物车批量检查
5. 预警文件生成和删除

## 注意事项

### 1. 性能考虑
- 库存检查在获取购物车项目时进行，可能影响响应速度
- 建议对大量物品的购物车进行分页处理

### 2. 文件管理
- 预警文件会持续累积，建议定期清理
- 可通过API删除不需要的预警文件

### 3. 错误处理
- 库存检查失败不会影响购物车的基本功能
- 所有错误都会记录到日志中

## 扩展功能

### 1. 邮件通知
可以扩展功能，在生成预警文件的同时发送邮件通知给相关人员

### 2. 预警级别
可以添加不同的预警级别（如：警告、严重、紧急）

### 3. 自动建议
可以基于库存状态自动建议调整采购数量

### 4. 历史记录
可以保存库存检查的历史记录，用于趋势分析

## 故障排除

### 1. 预警文件未生成
- 检查 `inventory_alerts` 目录是否存在
- 确认用户有相应的权限
- 查看后端日志中的错误信息

### 2. 前端显示异常
- 检查浏览器控制台的错误信息
- 确认API端点返回正确的数据格式
- 验证库存检查结果的字段完整性

### 3. 性能问题
- 考虑对大量物品进行分批检查
- 优化数据库查询
- 添加缓存机制

## 更新日志

- **v1.0.0**: 初始版本，支持基本的库存检查和预警功能
- 自动库存检查集成到购物车服务
- 前端库存检查提示组件
- 超储预警文件生成
- 完整的测试覆盖
