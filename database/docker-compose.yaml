services:
  # PostgreSQL数据库
  postgres:
    env_file:
      - .env
    image: postgres:15-alpine
    container_name: bizlink-idm-postgres
    restart: unless-stopped
    volumes:
      - ./postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=${DATABASE_NAME}
      - POSTGRES_USER=${DATABASE_USER}
      - POSTGRES_PASSWORD=${DATABASE_PASSWORD}
    ports:
      - "${DATABASE_PORT}:5432"
