# 库存使用统计重构方案

## 问题描述
库存管理中有领用记录，但使用统计仍然为空。原方案使用额外的统计表 `InventoryUsageStatistics` 来存储统计数据，但存在以下问题：
1. **数据不一致**：统计表与实际使用记录不同步
2. **维护复杂**：需要额外的同步机制
3. **性能开销**：每次操作都要更新统计表

## 解决方案
**直接统计方案**：不再使用额外的统计表，直接从 `ItemUsageRecord` 表进行实时统计。

## 重构内容

### 1. 移除的表
- ❌ `item_usages` - 旧的物品使用记录表
- ❌ `inventory_usage_statistics` - 库存使用统计表

### 2. 保留的表
- ✅ `item_usage_records` - 新的物品使用记录表
- ✅ `inventory_change_records` - 库存变更记录表
- ✅ 其他库存相关表

### 3. 新的统计服务
- **文件**: `Backend (Python)/app/services/usage_statistics_service.py`
- **特点**: 直接统计，实时计算，无需同步

## 技术实现

### 1. 统计服务核心功能

#### 基础统计查询
```python
def get_usage_statistics(
    db: Session, 
    department_id: int,
    item_id: Optional[int] = None,
    year: Optional[int] = None,
    month: Optional[int] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    skip: int = 0,
    limit: int = 100
):
    """获取使用统计数据"""
```

#### 月度统计
```python
def get_monthly_statistics(
    db: Session,
    department_id: int,
    year: int,
    month: Optional[int] = None
):
    """获取月度统计数据"""
```

#### 部门摘要
```python
def get_department_summary(db: Session, department_id: int):
    """获取部门使用统计摘要"""
```

#### 最常用物品排行
```python
def get_top_used_items(
    db: Session,
    department_id: int,
    limit: int = 10,
    days: int = 30
):
    """获取最常用物品排行"""
```

### 2. API端点更新

#### 使用统计
- **端点**: `GET /admin/inventory/usage-statistics`
- **功能**: 获取使用统计数据
- **参数**: 支持物品ID、年月、日期范围筛选

#### 统计摘要
- **端点**: `GET /admin/inventory/usage-statistics-summary`
- **功能**: 获取统计摘要
- **参数**: 支持年份、月份筛选

#### 最常用物品
- **端点**: `GET /admin/inventory/top-used-items`
- **功能**: 获取最常用物品排行
- **参数**: 支持记录数限制、统计天数

### 3. 前端接口更新

#### 类型定义
```typescript
export interface UsageStatisticsItem {
  item_id: number;
  department_id: number;
  item_name: string;
  item_code: string;
  department_name: string;
  total_usage: number;
  usage_count: number;
  avg_unit_price: number;
  first_usage: string;
  last_usage: string;
  unit: string;
}

export interface TopUsedItem {
  item_id: number;
  item_name: string;
  item_code: string;
  total_usage: number;
  usage_count: number;
  unit: string;
}
```

#### API方法
```typescript
// 获取使用统计
getUsageStatistics(params: UsageStatisticsFilters): Promise<UsageStatisticsItem[]>

// 获取使用统计摘要
getUsageStatisticsSummary(params?: { year?: number; month?: number }): Promise<any>

// 获取最常用物品排行
getTopUsedItems(params?: { limit?: number; days?: number }): Promise<TopUsedItem[]>
```

## 使用方法

### 1. 执行数据库迁移
```bash
cd /home/<USER>/bizlink-idm/Backend\ \(Python\)
python scripts/remove_unused_tables.py
```

### 2. 重启后端服务
```bash
# 重启FastAPI服务
```

### 3. 测试新的统计API
```bash
# 测试使用统计
curl "http://localhost:8000/admin/inventory/usage-statistics?department_id=1"

# 测试统计摘要
curl "http://localhost:8000/admin/inventory/usage-statistics-summary?year=2024"

# 测试最常用物品
curl "http://localhost:8000/admin/inventory/top-used-items?limit=5&days=30"
```

## 优势对比

### 原方案（已废弃）
- ❌ 需要额外的统计表
- ❌ 数据同步复杂
- ❌ 维护成本高
- ❌ 可能出现数据不一致

### 新方案（推荐）
- ✅ 直接统计，实时计算
- ✅ 无需额外表结构
- ✅ 数据始终一致
- ✅ 维护简单
- ✅ 性能更好

## 注意事项

### 1. 数据迁移
- 执行迁移前请备份数据库
- 确认 `item_usage_records` 表包含完整的使用记录
- 验证统计结果准确性

### 2. 性能考虑
- 对于大量数据的查询，建议添加适当的数据库索引
- 考虑使用缓存机制优化频繁查询
- 监控查询性能，必要时进行优化

### 3. 兼容性
- 前端代码需要更新以使用新的API接口
- 确保所有相关的统计功能都使用新的服务
- 测试所有统计相关的功能

## 后续优化

### 1. 缓存机制
- 实现Redis缓存，缓存常用的统计结果
- 设置合理的缓存过期时间
- 在数据更新时清除相关缓存

### 2. 索引优化
- 为 `ItemUsageRecord` 表添加合适的索引
- 优化查询性能
- 监控慢查询

### 3. 统计报表
- 实现更丰富的统计报表
- 支持导出功能
- 添加图表展示

## 总结

通过这次重构，我们：
1. **简化了架构**：移除了复杂的统计表同步机制
2. **提高了性能**：直接统计，无需额外的表操作
3. **保证了数据一致性**：统计数据始终与实际记录一致
4. **降低了维护成本**：减少了代码复杂度和维护工作

新的统计方案更加简洁、高效、可靠，完全解决了原有的数据不一致问题。

