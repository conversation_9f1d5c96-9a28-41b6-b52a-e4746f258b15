services:
  # 后端FastAPI服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: bizlink-idm-backend
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - UPLOAD_DIR=uploads
      - DATABASE_URL=${DATABASE_URL}
      - DEBUG=${DEBUG}
      - LOG_LEVEL=${LOG_LEVEL}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_ALGORITHM=${JWT_ALGORITHM}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS}
    volumes:
      # 上传文件目录挂载为数据卷
      - ./uploads_data:/app/uploads
      # 静态文件目录挂载为数据卷
      - ./backend/static/sample-images:/app/static/sample-images
    ports:
      - "8000:8000"

  # 前端Nginx服务
  frontend:
    env_file:
      - .env
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: bizlink-idm-frontend
    restart: unless-stopped
    ports:
      - "${APP_PORT}:80"
    depends_on:
      - backend


