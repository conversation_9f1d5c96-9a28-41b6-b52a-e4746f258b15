#!/bin/bash
# Start both frontend and backend development servers

# Start backend in background
cd backend && poetry run uvicorn app.main:app --reload --port 8000 &
BACKEND_PID=$!
echo "Backend server started with PID $BACKEND_PID"

# Start frontend
cd frontend && BROWSER=none PORT=3000 npm start

# When frontend is terminated, kill the backend process
echo "Frontend server terminated, killing backend process $BACKEND_PID"
kill $BACKEND_PID 
