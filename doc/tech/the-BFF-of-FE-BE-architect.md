# 前后端分享项目架构的BFF

* 前端和后端有两个力量在博弈
  * 前端要处理多变, 复杂的业务, 需要定制化的Api
  * 后端要保证稳定性与性能, 希望能提供标准化的Api
* 涉及前端项目必须会有一个BFF层
* 传统是后端MVC的V层承担了这个职责
* 前后端分离项目出现后, 曾出现BFF层无人负责的情况
* 大分离: Frontend - Frontend Server - Backend
  * 适用于大型项目, 后端尽量标准化统一化的接口
  * Frontend Server承担BFF层, 组装后端接口, 定制出前端接口. 
  * 但一般前端人开发人员没有这个能力, Backend会逐渐承担BFF层的职责, Frontend Server仅透传
* 小分离: Frontend - Nginx - Backend
  * Backend承担BFF层. 要么Backend团队维护BFF服务与Api服务; 要么Backend api层承担BFF职责, Server层提供标准化业务功能
  * 容易出现治理问题, 要么让Server层承担太多BFF职责, 要么Api层承担了太多业务具体实现