# 图片上传404错误故障排除指南

## 问题描述

前端上传图片时遇到404错误：
```
INFO: None:0 - "POST /api/admin/upload/images HTTP/1.1" 404 Not Found
```

## 问题分析

经过测试，后端API路由是正常工作的。404错误通常由以下原因造成：

1. **前端未登录或token过期**
2. **API路由配置问题**
3. **代理配置问题**

## 解决方案

### 1. 检查前端登录状态

确保前端已正确登录并获取了有效的token：

#### 登录凭据
- **用户名**: `admin`
- **密码**: `admin123`

#### 检查步骤
1. 打开浏览器开发者工具 (F12)
2. 进入 Application/Storage 标签
3. 查看 Local Storage
4. 检查是否有 `access_token` 字段
5. 如果token存在，检查是否过期

#### 重新登录
如果token不存在或已过期：
1. 访问登录页面
2. 使用上述凭据登录
3. 登录成功后重试图片上传

### 2. 检查API路由配置

后端路由已正确配置：
- `POST /api/admin/upload/images` - 上传单个图片
- `POST /api/admin/upload/images/batch` - 批量上传图片
- `GET /api/admin/upload/images/{sub_dir}/{filename}` - 获取图片
- `DELETE /api/admin/upload/images/{sub_dir}/{filename}` - 删除图片

### 3. 检查代理配置

确保前端开发服务器正确代理API请求到后端：

#### 检查 package.json 中的代理配置
```json
{
  "proxy": "http://localhost:8000"
}
```

#### 或者检查 vite.config.js (如果使用Vite)
```javascript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  }
})
```

### 4. 验证后端服务状态

确保后端服务正在运行：

```bash
# 检查后端服务状态
curl http://localhost:8000/health

# 应该返回: {"status": "healthy"}
```

### 5. 测试API连接

使用curl测试API是否可访问：

```bash
# 测试登录
curl -X POST http://localhost:8000/api/admin/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# 如果登录成功，使用返回的token测试上传
curl -X POST http://localhost:8000/api/admin/upload/images \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@test-image.jpg" \
  -F "sub_dir=items"
```

## 常见问题及解决方案

### 问题1: 前端显示"Not authenticated"
**原因**: 用户未登录或token已过期
**解决**: 重新登录获取新的token

### 问题2: 前端显示"Could not validate credentials"
**原因**: token格式错误或已过期
**解决**: 清除localStorage并重新登录

### 问题3: 上传成功但图片无法显示
**原因**: 图片文件路径或权限问题
**解决**: 
1. 检查 `uploads/` 目录权限
2. 确保后端服务有读写权限
3. 检查图片文件是否实际保存

### 问题4: 上传大文件失败
**原因**: 文件大小超过限制(500KB)
**解决**: 
1. 压缩图片文件
2. 或调整后端文件大小限制

## 调试步骤

### 1. 检查网络请求
1. 打开浏览器开发者工具
2. 进入 Network 标签
3. 尝试上传图片
4. 查看请求详情：
   - 请求URL是否正确
   - 请求头是否包含Authorization
   - 响应状态码和内容

### 2. 检查控制台错误
1. 打开浏览器开发者工具
2. 进入 Console 标签
3. 查看是否有JavaScript错误
4. 检查是否有网络请求错误

### 3. 检查后端日志
```bash
# 查看后端服务日志
tail -f logs/app.log

# 或启动后端时查看输出
poetry run python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 预防措施

### 1. 定期检查token状态
前端可以定期检查token是否有效，如果无效则自动跳转到登录页面。

### 2. 添加错误处理
前端上传组件应该添加适当的错误处理，显示用户友好的错误信息。

### 3. 添加重试机制
对于网络错误，可以添加自动重试机制。

## 联系支持

如果按照以上步骤仍然无法解决问题，请：

1. 收集错误日志
2. 记录复现步骤
3. 提供环境信息（浏览器、操作系统等）
4. 联系技术支持团队

## 验证修复

修复后，可以通过以下方式验证：

1. **登录测试**: 使用admin/admin123登录
2. **上传测试**: 上传一个小图片文件
3. **显示测试**: 检查图片是否正确显示
4. **删除测试**: 测试删除图片功能

如果所有测试都通过，说明问题已解决。 