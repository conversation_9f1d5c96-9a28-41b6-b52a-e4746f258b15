# 单服务多客户端架构实施方案

## 1. 需求概述

- **业务背景**: 现有系统已完成管理端功能, 但未考虑多APP, 需要新增两个面向不同场景的客户端APP: 物品领用客户端与采购商品入库客户端。
- **核心目标**:
  - 单后端服务, 按应用划分 API 前缀: `/api/admin`, `/api/admin/kiosk`, `/api/admin/inbound`。
  - 单前端服务, 提供三个静态入口: `/admin`, `/kiosk`, `/inbound`。
  - 复用现有业务与数据模型, 不引入外键与 enum。
- **系统定位**: 单体后端 + 单体前端 SPA 的单仓库结构, 统一认证与权限, 通过路径实现多客户端隔离与部署简化。

## 2. 功能需求

- **应用与路径**
  - **Admin 管理端**: 页面路径 `/admin`, API 前缀 `/api/admin/*`。
  - **Kiosk 领用端**: 页面路径 `/kiosk`, API 前缀 `/api/admin/kiosk/*`。
  - **Inbound 入库端**: 页面路径 `/inbound`, API 前缀 `/api/admin/inbound/*`。

- **接口分区与示例**
  - 认证复用: `/api/admin/auth/*` 保持不变, 三端统一使用 JWT。
  - Admin 区域:
    - 用户与权限: `/api/admin/users`, `/api/admin/roles`。
    - 基础资料: `/api/admin/items`, `/api/admin/categories`, `/api/admin/suppliers`。
    - 报表与系统: `/api/admin/reports`, `/api/admin/image-management`, `/api/admin/upload`。
  - Kiosk 区域:
    - 领用流程: `/api/admin/kiosk/usage/scan`, `/api/admin/kiosk/usage/confirm`, `/api/admin/kiosk/usage/history`。
  - Inbound 区域:
    - 入库流程: `/api/admin/inbound/receipts`, `/api/admin/inbound/receipts/{id}`, `/api/admin/inbound/putaway`, `/api/admin/inbound/inventory`。

- **前端要求**
  - 前端项目在 `frontend/src/apps/` 内按客户端划分目录: `admin`, `kiosk`, `inbound`。
  - 通过路由路径区分三个客户端APP: `/admin/*`, `/kiosk/*`, `/inbound/*`。
  - 前端必须使用 URL 保存页面状态, 包含: 标签页, 筛选条件, 抽屉开关, 编辑态等。
  - 抽取共享模块为公共目录: 鉴权与权限守卫, API SDK, axios 实例, 通用组件, `useUrlState` 等。
  - 三端属于一个 SPA（单体前端）, 不是三个独立的 SPA; 通过路径 `/admin/*`、`/kiosk/*`、`/inbound/*` 区分客户端。

- **访问地址**
  - 页面: `/admin`, `/kiosk`, `/inbound`
  - 接口: `/api/admin/*`, `/api/admin/kiosk/*`, `/api/admin/inbound/*`
  - API 文档: `/docs`

## 5. 业务规则

- **权限与鉴权**
  - 统一采用 JWT, 登录与刷新保留现有 `/api/admin/auth/*`。
  - 管理端具备全量配置与运营权限; Kiosk 仅限领用流程; Inbound 仅限入库流程。

- **URL 即状态**
  - 三端必须使用 URL 保存交互状态, 包括: 标签页, 筛选条件, 抽屉开关, 编辑态等。

- **接口前缀规范**
  - 后端路由按应用分组: `/api/admin/*`, `/api/admin/kiosk/*`, `/api/admin/inbound/*`。
  - 页面根路径: `/admin`, `/kiosk`, `/inbound`。静态资源与 SPA fallback 在这些路径下提供。

- **CORS 与来源**
  - 开发环境允许 `http://localhost:3000`。
  - 生产环境仅允许部署域名白名单。

- **错误处理与返回格式**
  - 错误统一返回结构体, 包含 `code`, `message`, `details`。
  - 鉴权失败返回 401, 权限不足返回 403, 业务冲突返回 409。

- **测试与验收**
  - 接口使用基于单元测试的集成测试验证 (pytest + httpx)。
  - 验收标准:
    - 访问 `/admin`, `/kiosk`, `/inbound` 可直接刷新与深链进入子路由。
    - 三组 API 前缀路由工作正常, 与现有通用接口兼容。
    - 不引入 enum 与外键, 数据一致性通过应用层保障。

## 6. 实施步骤

### 6.1 后端重构
- **创建应用模块结构**:
  ```
  backend/app/apps/
  ├── admin/          # 管理端 API
  ├── kiosk/          # 领用端 API  
  └── inbound/        # 入库端 API
  ```

- **路由重新分配**:
  - 现有管理功能 → `/api/admin/*`
  - 领用相关 → `/api/admin/kiosk/*`
  - 入库相关 → `/api/admin/inbound/*`
  - 认证 → 保持 `/api/admin/auth/*`

- **修改 main.py**:
  - 移除旧的 `app.include_router` 调用
  - 新增三个应用路由挂载
  - 配置静态文件挂载和 SPA fallback

- **当前路由现状（需迁移）**:
  ```47:59:backend/app/main.py
  # 包含路由
  app.include_router(auth_router, prefix="/api/admin/auth", tags=["认证"])
  app.include_router(users_router, prefix="/api/admin/users", tags=["用户管理"])
  app.include_router(roles_router, prefix="/api/admin/roles", tags=["角色权限管理"])
  app.include_router(items_router, prefix="/api/admin/items", tags=["物品管理"])
  app.include_router(suppliers_router, prefix="/api/admin", tags=["供应商管理"])
  app.include_router(usage_router, prefix="/api/admin", tags=["物品领取"])
  app.include_router(reports_router, prefix="/api/admin/reports", tags=["报表仪表板"])
  app.include_router(inventory_router, prefix="/api/admin/inventory", tags=["库存管理"])
  
  app.include_router(upload_router, prefix="/api/admin/upload", tags=["文件上传"])
  app.include_router(image_management_router, prefix="/api/admin/image-management", tags=["图片管理"])
  ```

- **目标路由挂载（示例）**:
  ```python
  # backend/app/main.py（示意）
  from app.apps.admin.routes import router as admin_router
  from app.apps.kiosk.routes import router as kiosk_router
  from app.apps.inbound.routes import router as inbound_router

  app.include_router(auth_router, prefix="/api/admin/auth", tags=["认证"])  # 保持不变
  app.include_router(admin_router, prefix="/api/admin", tags=["Admin 管理端"])
  app.include_router(kiosk_router, prefix="/api/admin/kiosk", tags=["Kiosk 领用端"])
  app.include_router(inbound_router, prefix="/api/admin/inbound", tags=["Inbound 入库端"])
  ```

- **后端目录迁移映射**:
  - 归属 Admin 管理端（移动到 `backend/app/apps/admin/` 并由 `routes.py` 聚合）:
    - `backend/app/api/admin/users.py`
    - `backend/app/api/admin/roles.py`
    - `backend/app/api/admin/items.py`
    - `backend/app/api/admin/suppliers.py`
    - `backend/app/api/admin/reports.py`
    - `backend/app/api/admin/upload.py`
    - `backend/app/api/admin/image_management.py`
  - 归属 Kiosk 领用端（移动到 `backend/app/apps/kiosk/`）:
    - `backend/app/api/admin/usage.py`（二维码领取、扫码、确认等）
  - 归属 Inbound 入库端（移动到 `backend/app/apps/inbound/`）:
    - `backend/app/api/admin/inventory.py`（库存查询/调整作为入库域的一部分暴露在 `/api/admin/inbound/inventory/*`）
    - 新增 `receipts`, `putaway` 路由骨架（后续迭代补充）
  - 共享层（保持不动）:
    - 模型: `backend/app/models/*`
    - 服务: `backend/app/services/*`
    - 核心: `backend/app/core/*`

- **应用 Router 聚合示例**:
  ```python
  # backend/app/apps/admin/routes.py（示意）
  from fastapi import APIRouter
  from app.api import users, roles, items, suppliers, reports, upload, image_management

  router = APIRouter()
  router.include_router(users.router, prefix="/users")
  router.include_router(roles.router, prefix="/roles")
  router.include_router(items.router, prefix="/items")
  router.include_router(suppliers.router, prefix="/suppliers")
  router.include_router(reports.router, prefix="/reports")
  router.include_router(upload.router, prefix="/upload")
  router.include_router(image_management.router, prefix="/image-management")
  ```

### 6.2 前端重构
- **目录结构**:
  ```
  frontend/src/
  ├── apps/
  │   ├── admin/      # 管理端页面和组件
  │   ├── kiosk/      # 领用端页面和组件
  │   └── inbound/    # 入库端页面和组件
  ├── shared/          # 共享模块
  │   ├── components/  # 通用组件
  │   ├── hooks/       # 通用 hooks
  │   ├── services/    # API 客户端
  │   └── utils/       # 工具函数
  └── App.tsx          # 主路由配置
  ```

- **路由配置**:
  - `/admin/*` → Admin 管理端
  - `/kiosk/*` → Kiosk 领用端
  - `/inbound/*` → Inbound 入库端

- **当前前端路由现状（单体 Admin）**:
  - 现有页面均在 `frontend/src/pages/*`，通过根路径如 `/items`, `/suppliers`, `/users` 等访问。
  - `frontend/src/App.tsx` 使用受保护布局与模块权限路由。

- **前端迁移动作**:
  - 新建 `frontend/src/apps/admin/` 并将以下页面与模块迁入（保持路径一致）:
    - `ItemManagement`, `ItemDetail`, `ItemEdit`
    - `CategoryManagement`, `PrimaryCategoryManagement`
    - `SupplierManagement`, `SupplierDetail`, `SupplierEdit`, `SupplierItems`, `SupplierItemPrices`
    - `InventoryManagement`, `PurchaseRequests`, `ReportsDashboard`
    - `UserManagement`, `RoleManagement`, `DepartmentManagement`, `ImageManagement`
  - 新建 `frontend/src/apps/kiosk/`:
    - 首批页面：`KioskScan`, `KioskConfirm`, `KioskHistory`（与后端 `/api/admin/kiosk/usage/*` 对应）
  - 新建 `frontend/src/apps/inbound/`:
    - 首批页面：`InboundReceipts`, `InboundReceiptDetail`, `InboundPutaway`, `InboundInventory`
  - 抽取共享模块至 `frontend/src/shared/`:
    - 鉴权与权限守卫：`AuthContext`, `PermissionRoute`
    - axios 实例与 API SDK：从 `frontend/src/services/*` 迁入 `shared/services`
    - 通用组件与 hooks：表格、表单、`useUrlState` 等
  - 调整 `frontend/src/App.tsx`:
    - 将原根级业务路由迁入 `/<app>/*` 子路由
    - 示例：`/admin/items` 映射到 AdminApp 内部的 `items` 页面

#### Admin 代码迁移详解（一步一步）

1) 创建 Admin 子应用与 Shared 目录骨架（保留现有缩进与文件风格）
   ```bash
   mkdir -p /home/<USER>/bizlink-idm/frontend/src/apps/admin/{pages,components,layouts,routes}
   mkdir -p /home/<USER>/bizlink-idm/frontend/src/shared/{components,hooks,services,utils}
   ```

2) 迁移现有 Admin 页面与模块至 `apps/admin`
   - 使用 `git mv` 保留历史：
   ```bash
   git mv /home/<USER>/bizlink-idm/frontend/src/pages /home/<USER>/bizlink-idm/frontend/src/apps/admin/pages
   # 如有 admin 专属的 components/layouts/modules 等, 同样移动：
   # 示例（按你项目实际命名执行）
   # git mv /home/<USER>/bizlink-idm/frontend/src/components/Admin* /home/<USER>/bizlink-idm/frontend/src/apps/admin/components/ || true
   # git mv /home/<USER>/bizlink-idm/frontend/src/layouts/Admin* /home/<USER>/bizlink-idm/frontend/src/apps/admin/layouts/ || true
   ```
   - 保持与原有路由路径一致, 仅调整物理位置到 `apps/admin`。

3) 抽取通用模块到 `shared`
   - 将通用 hooks、组件、API 客户端、工具函数迁至 `frontend/src/shared/*`。
   - 更新 Admin 代码中的导入路径指向 `shared`（建议使用路径别名, 见第 6 步）。

4) 新建 AdminApp 路由入口
   - 新建文件 `frontend/src/apps/admin/App.tsx`（示意, 非业务实现）：
   ```tsx
   import { Suspense } from 'react';
   import { Routes, Route, Navigate } from 'react-router-dom';
   import ItemManagement from './pages/ItemManagement';
   // ... 其他 Admin 页面按需导入
   
   export default function AdminApp() {
     return (
       <Suspense fallback={null}>
         <Routes>
           <Route index element={<Navigate to="items" replace />} />
           <Route path="items" element={<ItemManagement />} />
           {/* 其余 admin 路由保持与原先一致的相对结构 */}
         </Routes>
       </Suspense>
     );
   }
   ```

5) 更新顶层 `frontend/src/App.tsx` 将 Admin 子应用挂到 `/admin/*`
   - 关键路由结构（示意）：
   ```tsx
   import { Routes, Route } from 'react-router-dom';
   import AdminApp from './apps/admin/App';
   import KioskApp from './apps/kiosk/App';
   import InboundApp from './apps/inbound/App';
   
   export default function App() {
     return (
       <Routes>
         <Route path="/admin/*" element={<AdminApp />} />
         <Route path="/kiosk/*" element={<KioskApp />} />
         <Route path="/inbound/*" element={<InboundApp />} />
       </Routes>
     );
   }
   ```

6) 配置路径别名（可选, 便于从 `shared` 导入）
   - 在 `frontend/tsconfig.json` 中添加：
   ```json
   {
     "compilerOptions": {
       "baseUrl": "src",
       "paths": {
         "@shared/*": ["shared/*"],
         "@admin/*": ["apps/admin/*"],
         "@kiosk/*": ["apps/kiosk/*"],
         "@inbound/*": ["apps/inbound/*"]
       }
     }
   }
   ```

7) URL 即状态
   - 将原本在内存中的 UI 状态（如筛选、标签、抽屉）替换为 URL 查询参数或路径段。
   - 将 `useUrlState` 放在 `shared/hooks` 并在 Admin 页面中替换使用。

8) 自检
   - `npm start` 后验证：`http://localhost:3000/admin` 可访问原 Admin 页面；子路由刷新与深链可直接进入。
   - 修正遗漏的导入路径与类型错误。

- **单次构建与部署路径**:
  - 保持单次构建（CRA），产物相同，拷贝到三个静态目录供后端分别挂载:
    - 构建后复制 `frontend/build/*` 到：
      - `backend/static/admin/`
      - `backend/static/kiosk/`
      - `backend/static/inbound/`
  - 可通过 `frontend/package.json` 添加 `postbuild` 脚本执行上述拷贝（示例）:
    ```json
    {
      "scripts": {
        "build": "react-scripts build",
      }
    }
    ```

### 6.3 构建配置
- **单次构建（单 SPA）**: 一个 `npm run build` 生成所有客户端（单体前端, 三端共享同一构建产物, 通过路径区分显示）。
- **产物分发**: 为方便后端按不同前缀挂载, 将同一构建产物分别拷贝到三个静态目录（不是三次构建, 而是一次构建产物的三份拷贝）。

### 6.4 迁移清单（可执行顺序）
- 后端
  - 新建目录：`backend/app/apps/{admin,kiosk,inbound}`，分别创建 `routes.py`（初版使用现有 `app.api.*` 子路由聚合）。
  - 将 `backend/app/api/admin/usage.py` 归入 `kiosk`，其余管理向路由归入 `admin`，`inventory.py` 归入 `inbound`。
  - 在 `backend/app/main.py` 中替换旧的 `include_router` 为三个应用路由挂载，并增加 `/admin`、`/kiosk`、`/inbound` 的静态挂载与（可选）fallback。
- 前端
  - 新建 `frontend/src/apps/{admin,kiosk,inbound}`，迁移现有管理端页面至 `admin`。
  - 新建 `kiosk` 与 `inbound` 的首批页面骨架，与对应 API 前缀对齐。
  - 新建 `frontend/src/shared/{components,hooks,services,utils}` 并迁移通用模块。
  - 更新 `frontend/src/App.tsx` 为三段式子应用路由：`/admin/*`、`/kiosk/*`、`/inbound/*`。
- 部署/静态
  - 创建 `backend/static/{admin,kiosk,inbound}` 目录。
  - 前端构建后拷贝到上述目录，由后端提供 SPA 入口与深链访问。

### 6.5 注意事项
- 严禁引入数据库外键与数据库级枚举；关系与枚举通过代码层面 string/int 维护与校验。
- CORS 保留本地开发源 `http://localhost:3000`，生产使用域名白名单。
- 认证保持 `/api/admin/auth/*`；三端统一使用 JWT 与权限守卫。
- 接口返回结构统一：`code`, `message`, `details`；401/403/409 语义保持一致。

### 6.4 开发模式
- **单前端服务**: 继续使用 `npm start` (端口 3000)
- **路由访问**: 
  - `http://localhost:3000/admin` → 管理端
  - `http://localhost:3000/kiosk` → 领用端
  - `http://localhost:3000/inbound` → 入库端

## 7. 不包含的板块

- 不包含具体实现代码。
- 不包含 UI 界面设计。
- 不包含测试用例。
- 不包含部署配置。


