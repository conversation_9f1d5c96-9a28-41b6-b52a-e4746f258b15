# 采购申请管理统一化

## 概述

为了简化采购申请管理流程，提升用户体验，我们将原来的申请管理和审批管理两个独立模块合并为一个统一的采购申请管理界面。

**重要说明**: 采购申请只能通过购物车发起，不能直接在申请管理页面创建。申请管理页面主要用于查看、审批和管理已创建的申请。

## 主要改进

### 1. 统计卡片导航系统

新的统一管理界面使用统计卡片作为主要导航方式：
- **全部申请**: 当前登录用户可以看到的所有申请
- **我的申请**: 我提交的申请
- **待处理**: 需要我处理的申请（包括所有待审批状态）
- **已批准**: 当前我能看到的所有已完全批准的申请
- **已拒绝/退回**: 当前我能看到的所有已拒绝/退回的申请

**交互功能**: 统计卡片支持点击操作，点击后自动切换到对应的申请列表，提供直观的导航体验。

### 2. 简化的审批流程

不再区分具体的审批级别：
- 原来的"待部门经理复核"、"待主管审批"、"待公司主管审批"统一显示为"待审批"
- 简化了状态流转逻辑
- 减少了用户对审批级别的困惑

### 3. 统一的统计视图

在页面顶部提供统一的统计卡片：
- 全部申请数量
- 我的申请数量
- 待处理申请数量
- 已批准申请数量
- 已拒绝/退回申请数量

**交互功能**: 统计卡片支持点击操作，点击后自动切换到对应的申请列表，提供直观的导航体验。

### 4. 智能导航系统

- **统计卡片导航**: 点击统计卡片可直接切换到对应的申请列表
- **视觉反馈**: 当前选中的统计卡片会有特殊的视觉样式（边框、阴影、背景色）
- **悬停效果**: 统计卡片支持悬停效果，提升交互体验
- **无标签页设计**: 移除了传统的标签页，使用统计卡片作为主要导航方式

### 5. URL状态管理

所有页面状态都保存在URL中，包括：
- 当前激活的统计卡片（对应申请类型）
- 支持浏览器前进后退和书签功能

## 申请创建流程

### 购物车发起申请

1. 用户在购物车中添加所需物品
2. 设置数量、单位、需求说明等
3. 提交购物车生成采购申请
4. 申请进入审批流程

### 申请管理页面

申请管理页面仅用于：
- 查看申请状态和详情
- 进行审批操作
- 管理申请生命周期
- 查看流转历史

## 技术实现

### 组件结构

```
UnifiedPurchaseManagement.tsx  # 主要的统一管理组件
├── PurchaseRequestTable.tsx   # 申请列表表格
└── ApprovalForm.tsx          # 审批表单弹窗
```

### 权限控制

- **canEdit**: 是否可以编辑申请
- **canDelete**: 是否可以删除申请
- **canApprove**: 是否可以审批申请
- **canCreate**: 固定为false（采购申请只能由购物车发起）

### 统计卡片权限配置

- **全部申请**: 显示所有可见申请，支持编辑、删除、审批操作
- **我的申请**: 显示当前用户提交的申请，支持编辑、删除操作，不支持审批
- **待处理**: 显示需要审批的申请，不支持编辑、删除，支持审批操作
- **已批准**: 显示已批准的申请，不支持编辑、审批，支持删除操作
- **已拒绝/退回**: 显示被拒绝或退回的申请，支持编辑、删除操作，不支持审批

### 数据过滤逻辑

1. **标签页过滤**: 根据当前选中的统计卡片过滤数据
2. **权限过滤**: 根据用户权限过滤可见的申请

## 使用说明

### 查看申请

**统计卡片导航**: 直接点击页面顶部的统计卡片来查看不同类型的申请：
- 点击"全部申请"卡片 → 查看所有可见的申请
- 点击"我的申请"卡片 → 查看自己提交的申请
- 点击"待处理"卡片 → 查看需要审批的申请
- 点击"已批准"卡片 → 查看已批准的申请
- 点击"已拒绝/退回"卡片 → 查看被拒绝或退回的申请

### 操作权限说明

- **全部申请**: 支持查看、编辑、删除、审批等所有操作
- **我的申请**: 支持查看、编辑、删除操作，不支持审批（不能审批自己的申请）
- **待处理**: 支持查看、审批操作，不支持编辑、删除
- **已批准**: 支持查看、删除操作，不支持编辑、审批
- **已拒绝/退回**: 支持查看、编辑、删除操作，不支持审批

### 审批申请

1. 点击"待处理"统计卡片
2. 在申请列表中找到需要审批的申请
3. 点击"审批"按钮
4. 选择审批决定（批准/拒绝/退回）
5. 填写审批意见
6. 提交审批

### 创建新申请

**注意**: 采购申请不能在此页面直接创建，必须通过以下流程：

1. 进入购物车管理页面
2. 添加所需物品到购物车
3. 填写申请信息（部门、优先级、业务说明等）
4. 提交购物车生成采购申请

## 优势

1. **流程规范化**: 采购申请必须通过购物车发起，确保流程的规范性
2. **用户体验提升**: 统一的界面减少了用户在不同模块间切换的困扰
3. **操作效率提高**: 在一个界面内完成所有采购申请相关操作
4. **状态管理简化**: 不再需要理解复杂的审批级别概念
5. **维护成本降低**: 减少了重复的代码和组件

## 注意事项

1. 采购申请只能通过购物车发起，不能在申请管理页面直接创建
2. 原有的审批级别逻辑在后端仍然保留，只是前端显示进行了简化
3. 权限控制逻辑保持不变，确保数据安全性
4. URL状态管理确保了页面的可分享性和书签功能
5. 统计数据的计算基于当前用户的权限范围

## 后续优化建议

1. 可以考虑添加批量操作功能（批量审批、批量导出等）
2. 可以增加审批流程的可视化展示
3. 可以添加流转历史的时间线展示
4. 可以考虑增加邮件通知功能
5. 可以优化购物车到申请的转换流程
