# 图片上传功能实现文档

## 概述

本文档描述了物品管理系统中图片上传功能的实现细节，包括后端API、前端组件和数据库变更。

## 功能特性

### 1. 图片上传限制
- **文件大小限制**: 最大500KB
- **图片尺寸限制**: 最大2048x2048像素
- **支持格式**: JPEG、PNG、GIF、WebP
- **自动压缩**: 超过尺寸限制的图片会自动压缩
- **格式转换**: 所有图片统一转换为JPEG格式，质量85%

### 2. 文件命名规则
- **图片命名**: `{item-code}-image-{YYYYMMDDHHMMSS}.jpg`

### 3. 图片获取功能
- **原图获取**: 通过API获取完整尺寸的图片
- **图片信息获取**: 获取图片的元数据信息（尺寸、格式、大小等）
- **图片删除**: 支持删除已上传的图片

## 数据库变更



## 后端实现

### 1. 数据库模型更新
**文件**: `backend/app/models/item.py`
```python
# 基本信息
image_url = Column(String(500))  # 物品图片URL
```

### 2. Schema更新
**文件**: `backend/app/schemas/item.py`
```python
class ItemBase(BaseModel):
    # ... 其他字段
    image_url: Optional[str] = Field(None, description="物品图片URL")
```

### 3. 文件上传服务
**文件**: `backend/app/services/file_upload_service.py`

#### 主要功能
- 图片文件验证
- 图片压缩和格式转换
- 文件存储管理

#### 关键方法
```python
async def upload_image(self, file: UploadFile, sub_dir: str = "items") -> Dict[str, Any]:
    """上传图片文件"""
    
def _validate_image_file(self, file: UploadFile):
    """验证图片文件"""
    
async def _save_and_process_file(self, file: UploadFile, file_path: Path):
    """保存并处理文件"""
    
def _compress_image(self, img: Image.Image) -> Image.Image:
    """压缩图片"""
```

### 4. API接口
**文件**: `backend/app/api/upload.py`

#### 主要接口
- `POST /api/admin/upload/images`: 上传单个图片
- `POST /api/admin/upload/images/batch`: 批量上传图片
- `GET /api/admin/upload/images/{sub_dir}/{filename}`: 获取图片

- `DELETE /api/admin/upload/images/{sub_dir}/{filename}`: 删除图片
- `GET /api/admin/upload/images/info/{sub_dir}/{filename}`: 获取图片信息

#### 图片上传接口详解

##### 上传图片
```http
POST /api/admin/upload/images
Content-Type: multipart/form-data

Parameters:
- file: 图片文件 (required)
- sub_dir: 子目录 (default: "items")
- item_code: 物品编码 (optional)
```

**请求示例**:
```bash
curl -X POST "http://localhost:8000/api/admin/upload/images" \
  -H "Authorization: Bearer {token}" \
  -F "file=@image.jpg" \
  -F "sub_dir=items" \
  -F "item_code=ITEM001"
```

**响应示例**:
```json
{
  "success": true,
  "message": "图片上传成功",
  "data": {
    "url": "/api/admin/upload/images/items/ITEM001-image-20250802135753.jpg",
    "filename": "ITEM001-image-20250802135753.jpg",
    "file_size": 361,
    "content_type": "image/jpeg"
  }
}
```

##### 获取原图
```http
GET /api/admin/upload/images/items/{item-code}-image-20250802143022.jpg
```
- **功能**: 获取完整尺寸的图片文件
- **响应**: 图片文件流
- **Content-Type**: `image/jpeg`
- **认证**: 不需要认证



##### 获取图片信息
```http
GET /api/admin/upload/images/info/items/{filename}
```
- **功能**: 获取图片的元数据信息
- **响应**: JSON格式的图片信息
- **认证**: 需要认证
- **返回数据**:
```json
{
  "success": true,
  "data": {
    "width": 800,
    "height": 600,
    "format": "JPEG",
    "mode": "RGB",
    "size": 361
  }
}
```

##### 删除图片
```http
DELETE /api/admin/upload/images/items/{filename}
```
- **功能**: 删除指定的图片文件
- **响应**: JSON格式的删除结果
- **认证**: 需要认证
- **返回数据**:
```json
{
  "success": true,
  "message": "图片删除成功"
}
```

## 前端实现

### 6.1 前端实现
- 使用React + TypeScript开发
- 集成Ant Design的Image和Button组件
- 实现点击上传功能
- 通过API接口上传图片到后端
- 简化交互：有图片时直接点击图片区域更换，无需删除按钮
- 移除预览功能，点击图片直接触发上传 