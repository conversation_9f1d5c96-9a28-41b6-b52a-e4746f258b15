# 汇率管理重构进度文档

## 概述

本次重构将原有的 `purchase_request_exchange_rates` 表功能整合到 `purchase_request_items` 表中，实现更简洁的汇率锁定机制。

## 🔍 **问题调研结果**

### 1. **前端调用流程分析**

#### 提交申请流程
```
前端: purchaseRequestService.submitRequest(id)
  ↓
后端接口: POST /{request_id}/submit
  ↓
调用: service.update_request_status()
  ↓
触发: lock_exchange_rates_on_submit()
```

#### 审批流程
```
前端: purchaseRequestService.approveRequest()
  ↓
后端接口: POST /purchase/approval/requests/{id}/review
  ↓
调用: approval_service.submit_for_review()
  ↓
触发: clear_exchange_rate_locks_on_review()
```

### 2. **发现的问题**

#### 问题1: 汇率锁定逻辑不完整
- **原因**: 原有的汇率锁定方法只处理非USD货币，没有为所有物品设置汇率锁定信息
- **表现**: 提交后汇率列为空
- **影响**: 无法追踪汇率锁定状态

#### 问题2: 缺少默认值处理
- **原因**: 没有找到供应商价格的物品，汇率字段不会被设置
- **表现**: 部分物品的汇率字段为空
- **影响**: 数据不一致

### 3. **解决方案**

#### 修复汇率锁定方法
为所有三个汇率锁定方法添加完整的逻辑：

1. **为所有物品设置汇率锁定信息**
2. **USD物品设置默认汇率1.0**
3. **非USD物品设置实际汇率**
4. **没有找到价格的物品设置默认值**

## 主要变更

### 1. 数据模型变更

#### 新增字段到 `purchase_request_items` 表

```sql
-- 锁定的汇率信息（按阶段锁定）
locked_currency_code VARCHAR(10) COMMENT '锁定的货币代码'
locked_exchange_rate NUMERIC(10, 6) COMMENT '锁定的汇率值'
locked_exchange_rate_month DATE COMMENT '锁定的汇率生效月份'
locked_exchange_rate_stage VARCHAR(20) COMMENT '汇率锁定阶段: submitted, principle_approved, final_approved'
locked_exchange_rate_at DATETIME COMMENT '汇率锁定时间'
```

#### 新增索引

```sql
-- 汇率相关索引
CREATE INDEX idx_request_items_exchange_rate_stage ON purchase_request_items(locked_exchange_rate_stage)
CREATE INDEX idx_request_items_exchange_rate_month ON purchase_request_items(locked_exchange_rate_month)
```

### 2. 汇率锁定流程

#### 第一阶段：提交时锁定
- **触发时机**: 采购申请提交时
- **锁定内容**: 当前有效汇率
- **锁定阶段**: `submitted`
- **调用方法**: `lock_exchange_rates_on_submit()`
- **修复内容**: ✅ 确保所有物品都设置汇率锁定信息

#### 第二阶段：复核时清除
- **触发时机**: 部门经理复核时
- **操作内容**: 清除所有锁定的汇率信息
- **调用方法**: `clear_exchange_rate_locks_on_review()`

#### 第三阶段：主管审批时锁定
- **触发时机**: 物品管理员主管审批通过时
- **锁定内容**: 当前有效汇率
- **锁定阶段**: `principle_approved`
- **调用方法**: `lock_exchange_rates_on_principle_approval()`
- **修复内容**: ✅ 确保所有物品都设置汇率锁定信息

#### 第四阶段：最终审批时锁定
- **触发时机**: 公司主管最终审批通过时
- **锁定内容**: 当前有效汇率
- **锁定阶段**: `final_approved`
- **调用方法**: `lock_exchange_rates_on_final_approval()`
- **修复内容**: ✅ 确保所有物品都设置汇率锁定信息

### 3. 服务层变更

#### 新增方法

```python
class PurchaseRequestService:
    def lock_exchange_rates_on_submit(self, request_id: int) -> None:
        """提交时锁定汇率（第一阶段锁定）"""
        # 修复：确保所有物品都设置汇率锁定信息
        
    def clear_exchange_rate_locks_on_review(self, request_id: int) -> None:
        """复核时清除汇率锁定"""
        
    def lock_exchange_rates_on_principle_approval(self, request_id: int) -> None:
        """主管审批时锁定汇率（第二阶段锁定）"""
        # 修复：确保所有物品都设置汇率锁定信息
        
    def lock_exchange_rates_on_final_approval(self, request_id: int) -> None:
        """最终审批时锁定汇率（第三阶段锁定）"""
        # 修复：确保所有物品都设置汇率锁定信息
```

#### 审批服务集成

```python
class ApprovalService:
    def submit_for_review(self, ...):
        # 清除汇率锁定，准备重新锁定
        purchase_service.clear_exchange_rate_locks_on_review(request_id)
        
    def principle_approval(self, ...):
        # 锁定汇率（第二阶段锁定）
        purchase_service.lock_exchange_rates_on_principle_approval(request_id)
        
    def final_approval(self, ...):
        # 锁定汇率（第三阶段锁定）
        purchase_service.lock_exchange_rates_on_final_approval(request_id)
```

### 4. 数据迁移

#### 迁移脚本

1. **添加字段**: `add_exchange_rate_fields_to_purchase_request_items.py`
   - 为现有表添加汇率锁定字段
   - 创建相关索引

2. **删除旧表**: `drop_purchase_request_exchange_rates_table.py`
   - 删除原有的 `purchase_request_exchange_rates` 表

#### 执行顺序

```bash
# 1. 添加新字段
cd backend/scripts/migrations
python add_exchange_rate_fields_to_purchase_request_items.py

# 2. 删除旧表
python drop_purchase_request_exchange_rates_table.py --check
python drop_purchase_request_exchange_rates_table.py
```

### 5. 测试验证

#### 测试脚本
创建了 `test_exchange_rate_locking.py` 脚本来验证汇率锁定功能：

```bash
cd backend/scripts
python test_exchange_rate_locking.py
```

#### 测试内容
1. **提交时汇率锁定**: 验证所有物品都设置了汇率锁定信息
2. **清除汇率锁定**: 验证汇率锁定信息被正确清除
3. **数据完整性**: 验证汇率字段不为空

## 修复详情

### 1. 汇率锁定逻辑修复

#### 修复前的问题
```python
# 只处理非USD货币，没有为所有物品设置汇率锁定信息
for price in prices:
    if price.currency_code != "USD":
        # 只处理非USD货币
        pass
    # USD货币和没有价格的物品被忽略
```

#### 修复后的逻辑
```python
# 为所有物品设置汇率锁定信息
for item in request_items:
    price_found = False
    
    # 查找供应商价格
    for price in prices:
        price_found = True
        if price.currency_code != "USD":
            # 非USD货币：设置实际汇率
            item.locked_currency_code = price.currency_code
            item.locked_exchange_rate = exchange_rate.rate
        else:
            # USD货币：设置默认汇率1.0
            item.locked_currency_code = "USD"
            item.locked_exchange_rate = 1.0
        
        item.locked_exchange_rate_stage = "submitted"
        item.locked_exchange_rate_at = datetime.now()
        break
    
    # 没有找到价格的物品：设置默认值
    if not price_found:
        item.locked_currency_code = "USD"
        item.locked_exchange_rate = 1.0
        item.locked_exchange_rate_stage = "submitted"
        item.locked_exchange_rate_at = datetime.now()
```

### 2. 确保汇率字段不为空

#### 修复策略
1. **所有物品都必须设置汇率锁定信息**
2. **USD物品设置默认汇率1.0**
3. **非USD物品设置实际汇率**
4. **没有找到价格的物品设置默认值**

#### 字段完整性
- `locked_currency_code`: 始终有值（USD或实际货币代码）
- `locked_exchange_rate`: 始终有值（1.0或实际汇率）
- `locked_exchange_rate_stage`: 始终有值（submitted/principle_approved/final_approved）
- `locked_exchange_rate_at`: 始终有值（锁定时间）

## 前端适配

### 1. 新增字段显示

前端需要在申请明细中显示锁定的汇率信息：

```typescript
interface RequestItem {
  // ... 现有字段
  
  // 新增汇率锁定字段
  locked_currency_code?: string;
  locked_exchange_rate?: number;
  locked_exchange_rate_month?: string;
  locked_exchange_rate_stage?: string;
  locked_exchange_rate_at?: string;
}
```

### 2. 汇率状态展示

- **已锁定**: 显示锁定的汇率和阶段
- **未锁定**: 显示当前有效汇率
- **阶段标识**: 用不同颜色标识不同锁定阶段

## 优势

### 1. 数据一致性
- 汇率信息与申请明细在同一表中，避免关联查询
- 减少数据不一致的风险
- **新增**: 确保所有物品都有汇率锁定信息

### 2. 性能提升
- 减少表连接操作
- 简化查询逻辑

### 3. 维护性
- 减少表数量，简化数据库结构
- 汇率锁定逻辑更清晰
- **新增**: 完整的错误处理和日志记录

### 4. 业务逻辑
- 汇率锁定与审批流程紧密结合
- 支持多阶段汇率锁定策略
- **新增**: 确保汇率锁定信息的完整性

## 注意事项

### 1. 数据迁移
- 执行迁移前请备份数据库
- 确保在业务低峰期执行

### 2. 兼容性
- 新字段允许为空，不影响现有数据
- 前端需要适配新字段

### 3. 测试
- 迁移后需要全面测试汇率锁定功能
- 验证各阶段汇率锁定是否正确
- **新增**: 使用测试脚本验证功能

## 后续工作

### 1. 前端适配
- 更新申请明细组件
- 添加汇率锁定状态显示

### 2. 测试验证
- 单元测试
- 集成测试
- 端到端测试
- **新增**: 运行测试脚本验证功能

### 3. 文档更新
- 更新API文档
- 更新用户手册

## 总结

本次重构将汇率管理功能整合到申请明细表中，实现了更简洁、高效的汇率锁定机制。通过多阶段锁定策略，确保了汇率在关键业务节点的稳定性，同时简化了数据库结构和查询逻辑。

**关键修复**: 解决了汇率锁定逻辑不完整的问题，确保所有物品都能正确设置汇率锁定信息，解决了提交后汇率列为空的问题。
