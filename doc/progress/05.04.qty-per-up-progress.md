## 05.04 采购单位与库存单位（Qty per UP）改造影响与计划

### 1) 背景与目标
- 基于 `doc/requirement/05.04.qty-per-up.md`：采购单位与库存单位存在固定整数倍关系，由 `qty_per_up` 管理；库存以库存单位为基本单位；供应商按采购单位定价；入库默认采购单位、调整/领用默认库存单位，同时允许切换单位。
- 现状：系统仅有一个 `unit` 与 `qty_per_up` 字段，所有库存、领用、报表均以该 `unit` 展示/计算，尚未区分采购单位与库存单位。

参考现有实现（节选）：

```1:20:backend/app/models/item.py
# Item 目前仅有一个 unit 与 qty_per_up 字段
unit = Column(String(20), default="个")
qty_per_up = Column(Integer, default=1)
```

```45:56:backend/app/models/inventory.py
# 库存数量为 Decimal 精度，作为系统“库存单位”的基数
current_quantity = Column(Numeric(15, 4), default=0)
available_stock = Column(Numeric(15, 4), default=0)
```

### 2) 涉及功能模块
- 物品管理：`Item` 基础信息、编辑表单、详情展示。
- 库存管理：库存列表、手工入库、库存调整、库存详情、统计与报表。
- 领用/发放：Pad 领取页面、扫码返回的物品单位显示与数量录入。
- 供应商与采购：供应商报价、采购申请/下单数量单位与价格单位一致性。
- 报表：消耗量、金额统计，单位展示及换算。

### 3) 受影响的前端页面与改造要点
- `frontend/src/pages/ItemEdit.tsx`、`ItemDetail.tsx`、`ItemManagement.tsx`
  - 新增字段编辑与展示：`purchase_unit`、`inventory_unit`、`qty_per_up`。
  - 表单校验：`qty_per_up` 为正整数；当两个单位不同必须配置 `qty_per_up > 1`。

- `frontend/src/pages/InventoryManagement.tsx` 与组件 `components/inventory/*`
  - 列表与详情：同时显示库存单位数量与按采购单位换算后的数量，例如“120 双（6 盒）”。
  - 手工入库表单：默认按采购单位录入；提供单位切换（采购/库存），录入后在前端做即时换算预览；URL 持久化表单状态（符合“用 URL 保存页面状态”的规则）。
  - 库存调整表单：默认按库存单位；同样支持单位切换与换算预览。

- `frontend/src/pages/PadItemPickup.tsx`
  - 领取数量录入：默认按库存单位（最小发放单位）；当 `qty_per_up > 1` 时，允许逐件领取（库存单位）；若用户切换到采购单位，按 `qty_per_up` 做转换并校验可用库存。

- `frontend/src/pages/SupplierItems.tsx`、`SupplierItemPrices.tsx`、`PurchaseRequests.tsx`
  - 价格单位统一说明为采购单位；在需要展示库存或最小发放量时，附带库存单位及换算。

- `frontend/src/pages/ReportsDashboard.tsx`
  - 报表维度：以库存单位为统计基础，提供按采购单位的换算列（仅展示层换算，不影响统计口径）。

### 4) 受影响的后端 API 与 Schema（新增/调整）
- 物品相关（`backend/app/schemas/item.py`、`backend/app/models/item.py`）
  - 新增字段：
    - `purchase_unit: string`（采购单位）
    - `inventory_unit: string`（库存单位，现有 `unit` 语义迁移为库存单位）
    - `qty_per_up: int`（每“采购单位”内包含的“库存单位”数量，正整数）
  - 兼容策略：
    - 当前为开发阶段，直接更新模型并重建数据，不做旧字段兼容。

- 库存 API（`backend/app/api/inventory.py`、`backend/app/schemas/inventory.py`）
  - 手工入库/库存调整请求体新增 `quantity_unit: string`，取值：`"purchase" | "inventory"`；后端统一换算为库存单位入库与记账。
  - 列表/详情响应新增：`purchase_unit_quantity`（基于 `qty_per_up` 的只读换算值）、`purchase_unit`、`inventory_unit`。

- 领用 API（`backend/app/api/usage.py`、`backend/app/models/usage.py`、`backend/app/schemas/usage.py`）
  - `requested_quantity`/`issued_quantity` 改为 `Decimal(15, 4)` 或保持整数但采用库存单位作为唯一口径；推荐采用与库存一致的 Decimal 口径，避免后续扩展受限。
  - 批量/扫码接口返回单位：同时返回 `purchase_unit`、`inventory_unit` 与 `qty_per_up`，前端据此提供单位切换。

- 供应商价格与采购（`backend/app/api/suppliers.py`、`backend/app/models/supplier.py`、`backend/app/models/purchase.py`）
  - 明确价格单位为采购单位；若需要与库存口径对齐，仅在展示层做转换，不改变计价口径。

### 5) 数据模型改造与校验
- `items` 表：新增字段
  - `purchase_unit VARCHAR(20) NOT NULL DEFAULT '个'`
  - `inventory_unit VARCHAR(20) NOT NULL DEFAULT '个'`
  - `qty_per_up INTEGER NOT NULL DEFAULT 1 CHECK(qty_per_up >= 1)`
- 约束与规则
  - `qty_per_up >= 1`。
  - 库存底账、出入库、领用等一律以“库存单位”入库与记账；采购价与采购单据一律以“采购单位”计价。
  - 展示层可附加按采购单位换算的只读列。

### 6) 业务规则与交互
- 单位换算：
  - `inventory_quantity = purchase_quantity * qty_per_up`
  - `purchase_quantity = inventory_quantity / qty_per_up`
- 默认单位：
  - 入库：默认采购单位；调整/领用：默认库存单位；均可切换。
- 逐件领用规则：
  - 当 `qty_per_up > 1` 时，允许用户以“库存单位”逐件操作；否则仅能按“采购单位”的整包/整罐发放（与 00.overview 约束一致）。
- 价格口径：
  - 供应商价格为采购单位单价；金额计算在采购域保持采购单位；库存域不进行价格口径转换（仅展示层显示折算单价时转换）。
- URL 状态：
  - 表单单位选择、抽屉开关、筛选条件均入 URL，以便刷新/分享后恢复。

### 7) 兼容与迁移方案
- 不必考虑数据迁移, 现在还处于开发阶段, 可更新model后重建数据数据库
- 需要更新初始数据脚本a_..., b_..., c_..., 后重新初始化数据

### 8) 测试与验收
- 集成测试（优先）：
  - 物品创建/编辑：单位组合与 `qty_per_up` 校验场景。
  - 手工入库：按采购单位/库存单位分别入库，库存底账一致性。
  - 库存调整：按两种单位调整，变更记录正确。
  - 领用：逐件领用开关前后行为；扫码领取单位显示与校验。
  - 供应商价格：金额口径稳定为采购单位；展示换算不影响计价。
- 验收要点：
  - 页面 URL 状态完整恢复；列表/详情双单位显示；默认单位策略符合要求；换算与库存底账一致；无新外键；字段均为 string/int/decimal，不新增 enum。

### 9) 改造任务清单（按优先级）
- 后端（P0）
  - [ ] `items` 表新增 `purchase_unit`、`inventory_unit`, 移除 `unit` ，完善校验
  - [ ] 手工入库/库存调整请求体新增 `quantity_unit`，服务层统一换算
  - [ ] 物品、库存、领用、供应商相关响应补充双单位与换算值
  - [ ] 使用域数量类型与库存口径统一（建议 Decimal），并更新测试

- 前端（P0）
  - [ ] `ItemEdit/Detail/Management` 增加双单位的编辑/展示
  - [ ] `InventoryManagement` 列表/详情显示双单位；入库/调整表单单位切换与 URL 状态
  - [ ] `PadItemPickup` 逐件领用与单位切换，按返回的 `qty_per_up` 做校验

- 采购与供应商（P1）
  - [ ] 价格单位在 UI 与文案中明确为采购单位；必要时展示库存单位换算

- 报表（P2）
  - [ ] 以库存单位为基础统计；补充采购单位的换算显示

- 数据迁移与文档（P0）
  - [ ] 更新已存在的需求文档

### 10) 风险与对策
- 复合口径导致的理解偏差：在所有 UI 明确标注单位，默认规则一致。
- 舍入与显示：底账严格按库存单位记账；展示层换算向下取整并显示余数或小数，避免误差累积。
- 历史报表一致性：保持库存单位为唯一统计口径，转换仅用于展示。

### 11) 里程碑
- M1：后端完成（模型与 API 改造）
- M2：前端完成（页面改造、联调与集成测试）


