# Primary Price Management Pages

- [x] 1. SupplierItemPrices (/admin/suppliers/:id/items/:itemId/prices)
- [x] 2. ItemDetail (/admin/items/:id)
- [x] 3. PriceManagement (Component used in ItemDetail)
- [x] 4. Purchase<PERSON>art (/admin/purchase/cart)
- [x] 5. EditPurchaseRequest (/admin/purchase/requests/:id/edit)
- [x] 6. PurchaseRequestDetail (/admin/purchase/requests/:id)
- [x] 7. PurchaseRequestSummary (/admin/purchase/summary)
- [ ] 8. PurchaseExecutionBatchDetail (/admin/purchase/execution/batch/:id)
- [ ] 9. PriceTrendChart (Component)
- [ ] 10. ItemPriceTrendChart (Component)
- [ ] 11. PriceTrendModal (Component)
- [ ] 13. ExchangeRateManagement (/admin/exchange-rates)
- [ ] 14. InventoryManagement (/admin/inventory)
- [ ] 15. ItemManagement (/admin/items)
- [x] 16. Lock currency on submit
- [x] 16. Lock currency on principle approval
