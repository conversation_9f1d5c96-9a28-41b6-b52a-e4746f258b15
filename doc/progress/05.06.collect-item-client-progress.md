# 领取物品客户端开发进度

## 项目概述
基于需求文档 `05.06.collect-item-client.md`，开发领取物品客户端，为 Android PAD 设备优化的移动端领取系统。

## 开发任务分解

### 第一阶段：后端基础架构 (预计 2-3 天)

#### 1.1 数据库模型扩展
- [ ] **新增 ITEM_USAGE_RECORD 表**
  - 创建物品使用记录模型
  - 支持小数数量（4位精度）
  - 关联库存变更记录
  - 建立索引和约束

- [ ] **创建初始化数据脚本**
  - 编写权限数据初始化脚本
  - 编写角色数据创建脚本
  - 编写用户账号生成脚本
  - 测试数据初始化流程

- [ ] **完善现有模型关联**
  - 更新 ItemUsage 模型，重命名为 ItemUsageRecord
  - 确保与 InventoryChangeRecord 的关联关系
  - 添加必要的字段和关系

#### 1.2 认证和权限服务
- [ ] **部门仓库认证服务**
  - 实现 `dept_warehouse` 角色验证
  - 部门数据隔离逻辑
  - 登录状态持久化

- [ ] **权限中间件**
  - 部门数据访问控制
  - 操作权限验证
  - 异常处理和错误响应

- [ ] **数据初始化服务**
  - 权限数据初始化
  - 角色数据创建
  - 用户账号生成
  - 初始化状态检查

#### 1.3 核心业务服务
- [ ] **物品领取服务**
  - 库存验证逻辑
  - 数量计算和单位换算
  - 双重记录机制实现
  - 事务管理

- [ ] **库存管理服务**
  - 实时库存查询
  - 库存变更记录
  - 低库存预警

- [ ] **员工验证服务**
  - 工号查询和验证
  - 部门信息获取
  - 权限范围检查

### 第二阶段：后端 API 开发 (预计 2-3 天)

#### 2.1 认证 API
- [ ] **登录接口**
  - POST `/api/admin/client/auth/login`
  - 部门仓库账号验证
  - JWT token 生成

- [ ] **权限验证接口**
  - GET `/api/admin/client/auth/verify`
  - 角色和部门权限检查
  - 用户信息获取

#### 2.2 物品识别 API
- [ ] **二维码扫描接口**
  - POST `/api/admin/client/items/scan`
  - 物品信息解析
  - 库存状态查询

- [ ] **手动输入接口**
  - POST `/api/admin/client/items/input`
  - 物品编码验证
  - 物品信息获取

#### 2.3 员工验证 API
- [ ] **工卡扫描接口**
  - POST `/api/admin/client/employees/scan`
  - 员工信息解析
  - 部门权限验证

- [ ] **工号输入接口**
  - POST `/api/admin/client/employees/input`
  - 工号验证
  - 员工信息获取

#### 2.4 物品领取 API
- [ ] **领取操作接口**
  - POST `/api/admin/client/pickup/execute`
  - 库存验证
  - 双重记录创建
  - 库存更新

- [ ] **库存查询接口**
  - GET `/api/admin/client/inventory/query`
  - 部门库存查询
  - 可用数量计算

#### 2.5 历史记录 API
- [ ] **领取历史接口**
  - GET `/api/admin/client/history/pickup`
  - 时间范围筛选
  - 分页查询
  - 统计信息

- [ ] **使用统计接口**
  - GET `/api/admin/client/statistics/usage`
  - 部门消耗统计
  - 物品使用频率
  - 库存周转率

### 第三阶段：前端基础架构 (预计 2-3 天)

#### 3.1 项目结构搭建
- [ ] **创建客户端应用**
  - 在 `frontend/src/apps/` 下创建 `client` 目录
  - 配置路由和导航
  - 设置基础组件结构

- [ ] **依赖安装**
  - 二维码扫描库（jsQR 或 ZXing）
  - 摄像头访问库
  - UI 组件库（Ant Design Mobile 或 Material-UI）
  - 状态管理（Redux Toolkit 或 Zustand）

#### 3.2 基础组件开发
- [ ] **布局组件**
  - 响应式容器
  - 触摸友好的按钮
  - 移动端导航

- [ ] **通用组件**
  - 加载状态组件
  - 错误提示组件
  - 确认对话框

#### 3.3 状态管理
- [ ] **认证状态管理**
  - 用户登录状态
  - 权限信息缓存
  - 部门信息存储

- [ ] **业务状态管理**
  - 物品信息状态
  - 员工信息状态
  - 领取流程状态

### 第四阶段：前端核心功能 (预计 3-4 天)

#### 4.1 主操作页面开发
- [ ] **一键领取页面**
  - 页面布局和响应式设计
  - 物品扫描区域（摄像头 + 手动输入）
  - 员工验证区域（工卡扫描 + 工号输入）
  - 物品信息展示和数量输入
  - 领取确认和结果反馈
  - 实时状态更新和错误处理

#### 4.2 辅助功能页面
- [ ] **历史记录页面**
  - 领取历史列表展示
  - 时间筛选和搜索
  - 分页加载和详情查看

- [ ] **设置页面**
  - 用户信息和部门信息
  - 系统设置和帮助
  - 退出登录功能

#### 4.3 核心功能模块
- [ ] **二维码扫描模块**
  - 摄像头调用和权限管理
  - 实时视频流和二维码识别
  - 扫描结果处理和验证

- [ ] **数据验证模块**
  - 实时输入验证
  - 业务规则检查
  - 错误提示和处理

- [ ] **状态管理模块**
  - 操作流程状态管理
  - 数据同步和缓存
  - 异常状态处理

### 第五阶段：界面优化和适配 (预计 2-3 天)

#### 5.1 移动端优化
- [ ] **触摸操作优化**
  - 按钮尺寸和间距
  - 手势操作支持
  - 触摸反馈效果

- [ ] **响应式布局**
  - 不同屏幕尺寸适配
  - 横竖屏切换处理
  - 字体大小和间距调整

#### 5.2 用户体验优化
- [ ] **操作流程优化**
  - 简化操作步骤
  - 清晰的视觉引导
  - 错误提示和帮助

- [ ] **性能优化**
  - 页面加载优化
  - 图片和资源优化
  - 缓存策略

### 第六阶段：测试和部署 (预计 2-3 天)

#### 6.1 功能测试
- [ ] **单元测试**
  - 后端 API 测试
  - 前端组件测试
  - 业务逻辑测试

- [ ] **集成测试**
  - 端到端流程测试
  - 数据库操作测试
  - 权限验证测试

#### 6.2 兼容性测试
- [ ] **设备兼容性**
  - Android PAD 设备测试
  - 不同浏览器测试
  - 不同分辨率测试

- [ ] **网络环境测试**
  - 内网环境测试
  - 网络异常处理测试
  - 离线模式测试

#### 6.3 部署配置
- [ ] **构建配置**
  - 生产环境构建
  - 静态资源优化
  - 环境变量配置

- [ ] **部署脚本**
  - 自动化部署
  - 环境检查
  - 回滚机制

## 开发时间估算

| 阶段 | 任务 | 预计时间 | 负责人 |
|------|------|----------|--------|
| 第一阶段 | 后端基础架构 | 2-3 天 | 后端开发 |
| 第二阶段 | 后端 API 开发 | 2-3 天 | 后端开发 |
| 第三阶段 | 前端基础架构 | 2-3 天 | 前端开发 |
| 第四阶段 | 前端核心功能 | 3-4 天 | 前端开发 |
| 第五阶段 | 界面优化和适配 | 2-3 天 | 前端开发 |
| 第六阶段 | 测试和部署 | 2-3 天 | 全栈开发 |

**总计预计时间：13-19 天**

## 技术要点和注意事项

### 后端技术要点
1. **双重记录机制**：确保物品领取时同时记录到 `ITEM_USAGE_RECORD` 和 `InventoryChangeRecord` 表
2. **事务管理**：库存更新、使用记录创建、变更记录创建必须在同一事务中完成
3. **权限控制**：严格的部门数据隔离，防止跨部门数据访问
4. **数据验证**：数量精度、单位一致性、库存充足性等业务规则验证

### 前端技术要点
1. **摄像头集成**：使用 WebRTC API 调用设备摄像头，支持 HTTPS 环境
2. **二维码识别**：集成专业的二维码识别库，支持多种格式
3. **响应式设计**：针对触摸操作优化，支持不同屏幕尺寸
4. **状态管理**：合理的前端状态管理，确保数据一致性和用户体验

### 业务规则要点
1. **用户权限**：只有 `dept_warehouse` 角色用户才能进行物品领取操作
2. **部门隔离**：用户只能操作所属部门的数据
3. **库存管理**：实时库存验证，支持小数数量，单位换算显示
4. **数据追溯**：完整的操作记录，支持双向数据查询

## 风险评估和应对

### 技术风险
1. **摄像头兼容性**：不同设备的摄像头 API 支持差异
   - 应对：提供降级方案，支持手动输入
2. **二维码识别精度**：复杂环境下的识别准确率
   - 应对：优化识别算法，提供手动输入备选方案

### 业务风险
1. **权限控制复杂性**：多层级权限验证可能影响性能
   - 应对：合理的权限缓存策略，优化验证流程
2. **数据一致性**：双重记录机制的数据同步问题
   - 应对：严格的事务管理，完善的异常处理

## 页面和功能模块清单

### 7.1 需要开发的页面

#### 7.1.1 主操作页面 (`/client`)
- [ ] **一键领取页面** - 单页面完成所有操作
  - 顶部：部门信息显示 + 用户信息 + 设置按钮
  - 左侧：物品扫描区域（摄像头预览 + 手动输入备选）
  - 右侧：员工验证区域（工卡扫描 + 工号输入备选）
  - 底部：物品信息展示 + 数量输入 + 领取确认按钮
  - 状态栏：实时显示操作进度和结果

#### 7.1.2 辅助功能页面
- [ ] **历史记录页面** (`/client/history`) - 查看领取历史
- [ ] **设置页面** (`/client/settings`) - 用户信息和系统设置

### 7.2 需要改造的现有页面

#### 7.2.1 初始化数据脚本
- [ ] **权限数据初始化**
  - 新增 `dept_warehouse` 相关权限点
  - 配置角色权限关联
  - 设置部门数据隔离规则

- [ ] **角色数据初始化**
  - 创建 `dept_warehouse` 角色
  - 分配相关权限点
  - 设置角色状态为启用

- [ ] **用户数据初始化**
  - 创建部门仓库用户账号模板
  - 按部门生成初始账号
  - 分配 `dept_warehouse` 角色
  - 设置默认密码和状态

#### 7.2.2 现有管理功能确认
- [ ] **验证现有功能**
  - 确认用户管理功能满足需求
  - 确认角色权限管理功能完整
  - 确认部门管理功能可用
  - 确认库存管理功能完整

### 7.3 功能模块详细说明

#### 7.3.1 认证模块
- **功能描述**: 部门仓库用户登录和权限验证
- **核心功能**:
  - 账号密码验证
  - JWT token 生成和管理
  - 角色权限检查
  - 部门数据隔离
  - 登录状态持久化

#### 7.3.2 物品识别模块
- **功能描述**: 通过二维码扫描或手动输入识别物品
- **核心功能**:
  - 摄像头调用和权限管理
  - 二维码实时识别
  - 物品信息解析
  - 库存状态查询
  - 手动输入备选方案

#### 7.3.3 员工验证模块
- **功能描述**: 验证领取物品的员工身份
- **核心功能**:
  - 工卡二维码扫描
  - 员工信息查询
  - 部门权限验证
  - 身份确认流程
  - 手动工号输入

#### 7.3.4 领取操作模块
- **功能描述**: 执行物品领取操作
- **核心功能**:
  - 数量输入和验证
  - 库存充足性检查
  - 单位换算显示
  - 双重记录创建
  - 库存实时更新

#### 7.3.5 历史查询模块
- **功能描述**: 查询和统计物品使用历史
- **核心功能**:
  - 领取记录查询
  - 时间范围筛选
  - 多维度统计
  - 数据导出功能
  - 趋势分析图表

#### 7.3.6 数据初始化模块
- **功能描述**: 初始化必要的权限和角色数据
- **核心功能**:
  - 权限数据初始化脚本
  - 角色数据创建脚本
  - 用户账号生成脚本
  - 数据验证和检查

### 7.4 页面路由结构

```
/client
├── /                        # 主操作页面（一键领取）
├── /history                 # 领取历史页面
└── /settings                # 设置页面
```

### 7.5 主操作页面布局设计

#### 7.5.1 页面布局结构
```
┌─────────────────────────────────────────────────────────┐
│  [部门名称] [用户名] [设置] [历史] [退出]              │ ← 顶部导航栏
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌─────────────┐                    ┌─────────────┐    │
│  │             │                    │             │    │
│  │  物品扫描   │                    │  员工验证   │    │
│  │             │                    │             │    │
│  │ [摄像头预览] │                    │ [工卡扫描]   │    │
│  │             │                    │             │    │
│  │ [手动输入]  │                    │ [工号输入]   │    │
│  └─────────────┘                    └─────────────┘    │
│                                                         │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌─────────────────────────────────────────────────┐    │
│  │                                                 │    │
│  │              物品信息展示区域                    │    │
│  │  [物品名称] [规格] [库存] [单位]                │    │
│  │                                                 │    │
│  │  [数量输入] [领取确认] [取消]                    │    │
│  │                                                 │    │
│  └─────────────────────────────────────────────────┘    │
│                                                         │
├─────────────────────────────────────────────────────────┤
│  [状态信息] [操作提示] [错误信息]                      │ ← 底部状态栏
└─────────────────────────────────────────────────────────┘
```

#### 7.5.2 操作流程设计
1. **初始化**：页面加载时显示部门信息和用户信息
2. **物品识别**：扫描物品二维码或手动输入物品编码
3. **员工验证**：扫描员工工卡或手动输入工号
4. **信息确认**：自动显示物品信息和库存状态
5. **数量输入**：输入领取数量（支持小数）
6. **领取确认**：点击确认完成领取操作
7. **结果反馈**：显示领取成功信息和库存变化

#### 7.5.3 智能提示和验证
- **实时验证**：输入时实时验证数据有效性
- **智能提示**：根据当前状态显示下一步操作提示
- **错误处理**：友好的错误提示和解决建议
- **状态同步**：实时显示操作进度和结果

### 7.6 页面开发优先级

#### 高优先级（必须完成）
1. **主操作页面** (`/client`) - 一键完成所有领取操作
2. **历史记录页面** (`/client/history`) - 查看领取历史
3. **设置页面** (`/client/settings`) - 基本设置功能

#### 低优先级（增强功能）
1. 管理系统中关于领用操作的Dashboard
2. 高级统计报表功能
3. 批量操作功能

## 下一步行动

1. **确认开发计划**：与团队确认开发任务分解和时间安排
2. **环境准备**：搭建开发环境，安装必要依赖
3. **开始第一阶段**：后端基础架构开发
4. **定期同步**：每日站会同步开发进度和问题

---

*本文档将根据开发进度持续更新，记录实际完成情况和遇到的问题。*
