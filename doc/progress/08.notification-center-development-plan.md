# 消息中心与通知功能开发计划

## 📋 开发概述

### 开发目标
实现完整的消息中心与通知功能，包括数据模型、API接口、前端页面和业务集成。

### 开发原则
- **测试驱动**: 每完成一组API后立即编写单元测试
- **分步实施**: 按模块逐步开发，确保质量
- **及时测试**: 开发完成后及时通知测试
- **业务集成**: 最后在现有业务代码中集成通知功能
- **admin app开发**: 所有功能都在admin app下开发

### TODO
- **需要敲定各环节需要通知的人员**

## 🗓️ 开发阶段规划

### 第一阶段：数据模型开发 (预计2-3天)

#### 1.1 数据库表创建
- [ ] 创建 `notifications` 表
- [ ] 创建 `notification_emails` 表  
- [ ] 扩展 `users` 表，添加 `email_notifications_enabled` 字段
- [ ] 创建 `system_configs` 表

#### 1.2 数据模型定义
- [ ] 创建 `Notification` 模型
- [ ] 创建 `NotificationEmail` 模型
- [ ] 扩展 `User` 模型
- [ ] 创建 `SystemConfig` 模型
- [ ] 更新 `__init__.py` 文件

#### 1.3 数据库迁移
- [ ] 创建数据库迁移脚本
- [ ] 执行迁移，创建表结构
- [ ] 验证表结构正确性

**交付物**: 完整的数据模型和数据库表结构

---

### 第二阶段：核心服务开发 (预计3-4天)

#### 2.1 通知服务 (NotificationService)
- [ ] 通知创建服务
- [ ] 通知查询和筛选服务
- [ ] 通知状态管理服务
- [ ] 通知删除服务

#### 2.2 邮件服务 (EmailService)
- [ ] SMTP邮件发送服务
- [ ] 邮件模板渲染服务
- [ ] 邮件状态管理服务
- [ ] 邮件发送失败处理

#### 2.3 配置管理服务 (ConfigService)
- [ ] 系统配置读取服务
- [ ] 配置更新服务
- [ ] 环境变量配置支持

#### 2.4 定时任务服务 (EmailScheduler)
- [ ] 邮件发送定时任务
- [ ] 任务状态管理
- [ ] 手动触发邮件发送

**交付物**: 核心业务服务层代码

---

### 第三阶段：API接口开发 (预计4-5天)

#### 3.1 通知管理API (预计2天)
- [ ] 获取通知列表 API (`GET /api/admin/notifications`)
- [ ] 获取通知详情 API (`GET /api/admin/notifications/{id}`)
- [ ] 标记通知已读 API (`PUT /api/admin/notifications/{id}/read`)
- [ ] 批量标记已读 API (`PUT /api/admin/notifications/batch-read`)
- [ ] 删除通知 API (`DELETE /api/admin/notifications/{id}`)
- [ ] 批量删除通知 API (`DELETE /api/admin/notifications/batch`)

**测试要求**: 完成此组API后立即编写单元测试

#### 3.2 邮件管理API (预计1天)
- [ ] 获取邮件发送状态 API (`GET /api/admin/notification-emails`)
- [ ] 手动触发邮件发送 API (`POST /api/admin/notification-emails/trigger`)
- [ ] 获取邮件发送日志 API (`GET /api/admin/notification-emails/logs`)

**测试要求**: 完成此组API后立即编写单元测试

#### 3.3 系统配置API (预计1天)
- [ ] 获取系统配置 API (`GET /api/admin/system-configs`)
- [ ] 更新系统配置 API (`PUT /api/admin/system-configs`)
- [ ] 发送测试邮件 API (`POST /api/admin/system-configs/test-email`)

**测试要求**: 完成此组API后立即编写单元测试

#### 3.4 统计查询API (预计1天)
- [ ] 获取通知统计 API (`GET /api/admin/notifications/stats`)
- [ ] 获取邮件发送统计 API (`GET /api/admin/notification-emails/stats`)

**测试要求**: 完成此组API后立即编写单元测试

**交付物**: 完整的API接口和单元测试

---

### 第四阶段：前端页面开发 (预计5-6天) ✅ 已完成

#### 4.1 通知列表页面 (预计2天) ✅ 已完成
- [x] 通知列表组件
- [x] 筛选和搜索功能
- [x] 分页组件
- [x] 批量操作功能
- [x] 通知状态显示

**测试要求**: 完成此页面后通知测试

#### 4.2 通知详情页面 (预计1天) ✅ 已完成
- [x] 通知内容展示
- [x] 业务操作链接
- [x] 状态信息显示
- [x] 返回列表功能

**测试要求**: 完成此页面后通知测试

#### 4.3 系统配置页面 (预计1天) ✅ 已完成
- [x] SMTP配置表单
- [x] 通知开关设置
- [x] 配置保存功能
- [x] 测试邮件发送

**测试要求**: 完成此页面后通知测试

#### 4.4 通知管理页面 (超级管理员) (预计2天) ✅ 已完成
- [x] 所有通知列表
- [x] 统计图表展示
- [x] 邮件发送状态监控
- [x] 手动触发邮件发送
- [x] 发送日志查看

**测试要求**: 完成此页面后通知测试

**交付物**: 完整的前端页面和功能 ✅ 已完成

---

### 第五阶段：业务集成 (预计3-4天)

#### 5.1 库存告警集成
- [ ] 在库存变更时调用通知服务
- [ ] 库存阈值检查逻辑
- [ ] 告警通知创建
- [ ] 测试库存告警流程

#### 5.2 审批流程集成
- [ ] 采购申请提交通知
- [ ] 审批状态变更通知
- [ ] 审批完成通知
- [ ] 测试审批通知流程

#### 5.3 集成测试
- [ ] 端到端通知流程测试
- [ ] 邮件发送功能测试
- [ ] 权限控制测试
- [ ] 性能测试

**交付物**: 完整的业务集成和测试报告

---

## 🧪 测试策略

### 单元测试
- **覆盖率要求**: 核心服务层 > 90%，API层 > 85%
- **测试框架**: pytest + httpx
- **测试数据**: 使用测试数据库，不依赖生产数据
- **Mock策略**: 邮件发送使用Mock，避免实际发送

### 集成测试
- **API测试**: 测试所有API端点的功能正确性
- **数据库测试**: 测试数据模型的完整性和约束
- **权限测试**: 测试不同用户角色的权限控制

### 端到端测试
- **业务流程测试**: 测试完整的通知创建和发送流程
- **邮件发送测试**: 测试邮件发送的完整流程
- **用户界面测试**: 测试前端页面的功能完整性

## 📁 文件结构规划

### 后端文件结构 (admin app)
```
backend/app/
├── models/                           # 共享数据模型
│   ├── notification.py              # 通知模型
│   ├── notification_email.py        # 邮件记录模型
│   └── system_config.py            # 系统配置模型
├── services/                        # 共享业务服务
│   ├── notification_service.py      # 通知服务
│   ├── email_service.py            # 邮件服务
│   ├── config_service.py           # 配置服务
│   └── scheduler_service.py        # 定时任务服务
├── schemas/                         # 共享数据模式
│   ├── notification.py              # 通知相关模式
│   ├── email.py                    # 邮件相关模式
│   └── config.py                   # 配置相关模式
├── core/                           # 核心功能模块
│   ├── email.py                    # 邮件发送核心
│   └── scheduler.py                # 定时任务核心
└── apps/
    └── admin/                      # admin应用
        ├── api/                    # admin专用API
        │   ├── notifications.py    # 通知管理API
        │   ├── emails.py           # 邮件管理API
        │   ├── configs.py          # 系统配置API
        │   └── stats.py            # 统计查询API
        └── routes.py               # admin路由更新
```

### 前端文件结构 (admin app)
```
frontend/src/
└── apps/
    └── admin/                      # admin应用
        ├── notifications/          # 通知管理模块
        │   ├── NotificationList.tsx        # 通知列表页面
        │   ├── NotificationDetail.tsx      # 通知详情页面
        │   ├── NotificationManagement.tsx  # 通知管理页面(超级管理员)
        │   └── components/                 # 通知相关组件
        │       ├── NotificationCard.tsx    # 通知卡片组件
        │       ├── NotificationFilter.tsx  # 通知筛选组件
        │       ├── NotificationSearch.tsx  # 通知搜索组件
        │       └── NotificationBatchActions.tsx # 批量操作组件
        ├── settings/               # 系统设置模块
        │   ├── NotificationConfig.tsx      # 通知配置页面
        │   └── components/                 # 配置相关组件
        │       ├── SmtpConfigForm.tsx      # SMTP配置表单
        │       ├── NotificationSwitches.tsx # 通知开关设置
        │       └── TestEmailButton.tsx     # 测试邮件按钮
        ├── dashboard/              # 仪表板模块
        │   ├── NotificationStats.tsx       # 通知统计组件
        │   └── EmailStatusChart.tsx        # 邮件状态图表
        ├── services/               # admin专用服务
        │   └── notificationService.ts      # 通知API服务
        ├── types/                  # admin专用类型定义
        │   └── notification.ts             # 通知相关类型
        ├── components/             # admin专用组件
        │   └── NotificationBadge.tsx      # 通知徽章组件
        ├── hooks/                  # admin专用钩子
        │   └── useNotifications.ts        # 通知相关钩子
        └── routes.tsx              # admin路由配置
```

## 🚀 开发里程碑

### 里程碑1: 数据模型完成 (第3天)
- ✅ 数据库表结构创建完成
- ✅ 数据模型定义完成
- ✅ 数据库迁移执行完成

### 里程碑2: 核心服务完成 (第7天)
- ✅ 通知服务开发完成
- ✅ 邮件服务开发完成
- ✅ 配置服务开发完成
- ✅ 定时任务服务开发完成

### 里程碑3: API接口完成 (第12天)
- ✅ 通知管理API完成
- ✅ 邮件管理API完成
- ✅ 系统配置API完成
- ✅ 统计查询API完成
- ✅ 所有API单元测试完成

### 里程碑4: 前端页面完成 (第18天) ✅ 已完成
- ✅ 通知列表页面完成
- ✅ 通知详情页面完成
- ✅ 系统配置页面完成
- ✅ 通知管理页面完成
- ✅ 所有页面功能测试完成

### 里程碑5: 业务集成完成 (第22天)
- ✅ 库存告警集成完成
- ✅ 审批流程集成完成
- ✅ 端到端测试完成
- ✅ 系统整体测试完成

## 📋 开发检查清单

### 数据模型阶段
- [ ] 数据库表结构设计合理
- [ ] 模型关系定义正确
- [ ] 索引和约束设置合理
- [ ] 迁移脚本执行成功

### 核心服务阶段
- [ ] 服务接口设计合理
- [ ] 业务逻辑实现正确
- [ ] 异常处理完善
- [ ] 日志记录完整

### API接口阶段
- [ ] API设计符合RESTful规范
- [ ] 参数验证完整
- [ ] 权限控制正确
- [ ] 错误处理统一
- [ ] 单元测试覆盖充分

### 前端页面阶段
- [ ] 页面布局合理美观
- [ ] 功能实现完整
- [ ] 用户体验良好
- [ ] 响应式设计支持
- [ ] 国际化支持完整

### 业务集成阶段
- [ ] 集成点选择合理
- [ ] 通知触发时机正确
- [ ] 业务数据关联正确
- [ ] 端到端流程完整

## 🔧 技术要点

### 后端技术要点
- **admin app开发**: 所有功能都在admin app下开发
- **共享模型**: 数据模型和核心服务放在共享目录
- **admin专用API**: API接口放在admin app的api目录下
- **异步处理**: 邮件发送使用异步处理，避免阻塞
- **事务管理**: 通知创建和邮件记录使用事务保证一致性
- **配置管理**: 支持环境变量和数据库配置的动态更新
- **定时任务**: 使用APScheduler或Celery实现定时任务

### 前端技术要点
- **admin app开发**: 所有页面都在admin app下开发
- **状态管理**: 使用URL状态管理页面状态
- **组件复用**: 提取通用组件，提高代码复用性
- **国际化**: 使用react-i18next实现多语言支持
- **响应式设计**: 支持不同屏幕尺寸的设备

### 集成要点
- **事件驱动**: 在关键业务点触发通知事件
- **数据一致性**: 确保通知数据与业务数据的一致性
- **性能考虑**: 避免通知功能影响主要业务流程
- **错误隔离**: 通知功能异常不影响主要业务功能

## 📞 沟通和测试安排

### 开发沟通
- **每日进度**: 每天汇报开发进度和遇到的问题
- **代码审查**: 关键功能完成后进行代码审查
- **问题讨论**: 遇到技术难题及时讨论解决方案

### 测试安排
- **阶段性测试**: 每个开发阶段完成后进行测试
- **功能测试**: 重点测试新开发的功能
- **回归测试**: 确保新功能不影响现有功能
- **用户验收**: 最终进行用户验收测试

### 部署计划
- **开发环境**: 在开发环境完成所有功能开发
- **测试环境**: 在测试环境进行完整功能测试
- **生产环境**: 测试通过后部署到生产环境

## 📚 参考资料

### 技术文档
- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [SQLAlchemy官方文档](https://docs.sqlalchemy.org/)
- [React官方文档](https://reactjs.org/docs/)
- [TypeScript官方文档](https://www.typescriptlang.org/docs/)

### 项目规范
- [BizLinkSpeedy IDM项目规范](../requirement/00.overview.md)
- [FastAPI异常处理规范](../requirement/fastapi-exception-handling.md)
- [前端技术规范](../requirement/frontend-tech-rule.md)

### 相关需求
- [库存管理需求](../requirement/05.inventory-management.md)
- [采购流程管理需求](../requirement/purchase-management.md)
- [用户权限管理需求](../requirement/user-management.md)
