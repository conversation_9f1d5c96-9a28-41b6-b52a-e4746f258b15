# 采购申请模块实施计划

## 项目概述

### 基本信息
- **模块名称**: 采购申请模块 (Purchase Request Module)
- **项目阶段**: 实施阶段
- **优先级**: 高
- **预计工期**: 4-6周
- **负责人**: AI Agent

### 业务目标
- 实现标准的三级审批流程：部门经理复核 → 物品管理员主管审批 → 公司主管最终审批
- 支持购物车模式的采购申请
- 提供完整的数据快照和审计追踪

## 现状分析

### 已存在的代码
#### 后端模型 (Backend Models)
- **文件位置**: `backend/app/models/purchase.py`
- **现有模型**: 
  - `PurchaseRequest`: 基础采购申请模型
  - `PurchaseRequestItem`: 采购申请物品明细
- **状态字段**: 使用旧的状态定义 (`draft`, `submitted`, `approved`, `rejected`, `completed`)

#### 前端页面 (Frontend Pages)
- **文件位置**: `frontend/src/apps/admin/pages/PurchaseRequests.tsx`
- **现有功能**: 基础的采购申请列表展示和状态管理
- **状态配置**: 使用旧的状态映射

### 需要重构的部分
1. **数据模型**: 完全重构以符合新的需求设计
2. **状态管理**: 更新为新的三级审批流程
3. **业务逻辑**: 实现购物车、快照等核心功能
4. **前端界面**: 重新设计用户界面和交互流程

## 实施计划

### Phase 1: 数据模型重构 (Week 1)

#### 1.1 数据库表结构更新
**任务**: 根据需求文档创建新的数据库表结构
**文件**: `backend/app/models/purchase.py`

**需要创建的表**:
```python
# 核心表结构
- purchase_carts (购物车表)
- purchase_cart_items (购物车明细表)
- purchase_requests (采购申请表)
- purchase_request_items (采购申请明细表)
- request_flow_history (申请流转历史表)

- purchase_orders (采购订单表)
- purchase_order_items (采购订单明细表)
- price_snapshots (价格快照表)
- request_flow_history (申请流转历史表)
```

**关键字段更新**:
- 状态字段: 更新为新的状态流转
- 主键字段: 统一使用 `id` 命名
- 时间字段: `updated_at` 使用 `GENERATED ALWAYS AS (CURRENT_TIMESTAMP) STORED`
- Snapshot字段: 添加 `item_snapshot`, `approval_snapshot` 等

#### 1.2 数据库迁移脚本
**任务**: 创建数据库迁移脚本
**文件**: `backend/scripts/migrations/06_purchase_request_module.sql`

**迁移内容**:
```sql
-- 创建新表
CREATE TABLE purchase_carts (...);
CREATE TABLE purchase_cart_items (...);
-- 更新现有表
ALTER TABLE purchase_requests ADD COLUMN ...;
-- 数据迁移
INSERT INTO new_table SELECT ... FROM old_table;
```

### Phase 2: 核心业务逻辑实现 (Week 2-3)

#### 2.1 购物车管理服务
**文件**: `backend/app/services/purchase_cart_service.py`

**核心功能**:
```python
class PurchaseCartService:
    def create_cart(self, department_id: int, created_by: int) -> PurchaseCart
    def add_item_to_cart(self, cart_id: int, item_data: dict) -> PurchaseCartItem
    def update_cart_item(self, cart_item_id: int, updates: dict) -> PurchaseCartItem
    def remove_cart_item(self, cart_item_id: int) -> bool
    def submit_cart_as_request(self, cart_id: int) -> PurchaseRequest
    def get_department_cart(self, department_id: int) -> PurchaseCart
```

#### 2.2 采购申请服务
**文件**: `backend/app/services/purchase_request_service.py`

**核心功能**:
```python
class PurchaseRequestService:
    def create_request_from_cart(self, cart_id: int, submitter_id: int) -> PurchaseRequest
    def submit_request(self, request_id: int) -> PurchaseRequest
    def withdraw_request(self, request_id: int, user_id: int) -> PurchaseRequest
    def get_request_by_status(self, status: str, filters: dict) -> List[PurchaseRequest]
    def update_request_status(self, request_id: int, new_status: str) -> PurchaseRequest
```

#### 2.3 审批流程服务
**文件**: `backend/app/services/approval_service.py`

**核心功能**:
```python
class ApprovalService:
    def submit_for_review(self, request_id: int) -> RequestFlowHistory
    def review_request(self, request_id: int, approver_id: int, action: str, comments: str) -> RequestFlowHistory
    def principle_approval(self, request_id: int, approver_id: int, action: str, comments: str) -> RequestFlowHistory
    def final_approval(self, request_id: int, approver_id: int, action: str, comments: str) -> RequestFlowHistory
    def reject_request(self, request_id: int, approver_id: int, comments: str) -> RequestFlowHistory
    def get_approval_history(self, request_id: int) -> List[RequestFlowHistory]
```



#### 2.5 快照管理服务
**文件**: `backend/app/services/snapshot_service.py`

**核心功能**:
```python
class SnapshotService:
    def create_item_snapshot(self, request_item_id: int, trigger: str) -> dict
    def create_approval_snapshot(self, request_id: int, approval_level: str) -> dict
    def create_price_snapshot(self, request_id: int, item_id: int, supplier_id: int, price: float) -> PriceSnapshot
    def get_snapshot_history(self, entity_type: str, entity_id: int) -> List[dict]
```

### Phase 3: API接口实现 (Week 3-4)

#### 3.1 购物车API
**文件**: `backend/app/apps/admin/api/purchase_cart.py`

**接口设计**:
```python
@router.post("/purchase-carts", response_model=PurchaseCart)
async def create_cart(cart_data: CreateCartRequest)

@router.get("/purchase-carts/{cart_id}", response_model=PurchaseCart)
async def get_cart(cart_id: int)

@router.post("/purchase-carts/{cart_id}/items", response_model=PurchaseCartItem)
async def add_cart_item(cart_id: int, item_data: AddCartItemRequest)

@router.put("/purchase-carts/{cart_id}/items/{item_id}", response_model=PurchaseCartItem)
async def update_cart_item(cart_id: int, item_id: int, updates: UpdateCartItemRequest)

@router.delete("/purchase-carts/{cart_id}/items/{item_id}")
async def remove_cart_item(cart_id: int, item_id: int)

@router.post("/purchase-carts/{cart_id}/submit", response_model=PurchaseRequest)
async def submit_cart_as_request(cart_id: int, submit_data: SubmitCartRequest)
```

#### 3.2 采购申请API
**文件**: `backend/app/apps/admin/api/purchase_request.py`

**接口设计**:
```python
@router.get("/purchase/requests", response_model=List[PurchaseRequest])
async def get_purchase_requests(filters: RequestFilters)

@router.get("/purchase/requests/{request_id}", response_model=PurchaseRequest)
async def get_purchase_request(request_id: int)

@router.post("/purchase/requests", response_model=PurchaseRequest)
async def create_purchase_request(request_data: CreateRequestRequest)

@router.put("/purchase/requests/{request_id}/status", response_model=PurchaseRequest)
async def update_request_status(request_id: int, status_update: StatusUpdateRequest)

@router.post("/purchase/requests/{request_id}/withdraw", response_model=PurchaseRequest)
async def withdraw_request(request_id: int, withdraw_data: WithdrawRequest)

@router.get("/purchase/requests/{request_id}/approval-history", response_model=List[RequestFlowHistory])
async def get_approval_history(request_id: int)
```

#### 3.3 审批流程API
**文件**: `backend/app/apps/admin/api/approval.py`

**接口设计**:
```python
@router.post("/approval/{request_id}/review", response_model=RequestFlowHistory)
async def submit_for_review(request_id: int, review_data: ReviewRequest)

@router.post("/approval/{request_id}/principle-approval", response_model=RequestFlowHistory)
async def principle_approval(request_id: int, approval_data: ApprovalRequest)

@router.post("/approval/{request_id}/final-approval", response_model=RequestFlowHistory)
async def final_approval(request_id: int, approval_data: ApprovalRequest)

@router.post("/approval/{request_id}/reject", response_model=RequestFlowHistory)
async def reject_request(request_id: int, reject_data: RejectRequest)
```



### Phase 4: 前端界面重构 (Week 4-5)

#### 4.1 购物车管理界面
**文件**: `frontend/src/apps/admin/pages/PurchaseCart.tsx`

**功能特性**:
- 部门购物车展示
- 物品添加/编辑/删除
- 数量调整和备注管理
- 购物车提交为申请
- 实时价格计算

#### 4.2 采购申请管理界面
**文件**: `frontend/src/apps/admin/pages/PurchaseRequests.tsx` (重构)

**功能特性**:
- 申请列表和状态管理
- 申请详情查看
- 状态流转展示
- 撤回和重新提交
- 流转历史查看

#### 4.3 审批工作台界面
**文件**: `frontend/src/apps/admin/pages/ApprovalWorkbench.tsx`

**功能特性**:
- 待审批申请列表
- 审批操作界面
- 审批意见录入
- 批量审批支持
- 审批统计报表



### Phase 5: 集成测试和优化 (Week 5-6)

#### 5.1 单元测试
**文件**: `backend/tests/test_purchase_services.py`

**测试覆盖**:
- 购物车服务测试
- 采购申请服务测试
- 审批流程测试
- 快照服务测试


#### 5.2 集成测试
**文件**: `backend/tests/test_purchase_api.py`

**测试覆盖**:
- API接口测试
- 业务流程测试
- 权限控制测试
- 数据一致性测试

#### 5.3 前端测试
**文件**: `frontend/src/apps/admin/__tests__/`

**测试覆盖**:
- 组件渲染测试
- 用户交互测试
- 状态管理测试
- API调用测试

#### 5.4 性能优化
**任务**:
- 数据库查询优化
- 缓存策略实现
- 前端性能优化
- 批量操作优化

## 技术架构

### 后端架构
```
backend/app/
├── models/
│   ├── purchase.py          # 采购相关数据模型
│   └── __init__.py
├── services/
│   ├── purchase_cart_service.py      # 购物车服务
│   ├── purchase_request_service.py   # 采购申请服务
│   ├── approval_service.py           # 审批服务

│   └── snapshot_service.py           # 快照服务
├── apps/admin/api/
│   ├── purchase_cart.py     # 购物车API
│   ├── purchase_request.py  # 采购申请API
│   ├── approval.py          # 审批API

└── schemas/
    ├── purchase_cart.py     # 购物车数据模式
    ├── purchase_request.py  # 采购申请数据模式
    ├── approval.py          # 审批数据模式
    
```

### 前端架构
```
frontend/src/apps/admin/
├── pages/
│   ├── PurchaseCart.tsx             # 购物车管理
│   ├── PurchaseRequests.tsx         # 采购申请管理
│   ├── ApprovalWorkbench.tsx        # 审批工作台

├── components/
│   ├── PurchaseCartItem.tsx         # 购物车物品组件
│   ├── PurchaseRequestForm.tsx      # 采购申请表单
│   ├── ApprovalFlow.tsx             # 审批流程组件
│   └── BatchSummary.tsx             # 批量汇总组件
├── services/
│   ├── purchaseCartService.ts       # 购物车服务
│   ├── purchaseRequestService.ts    # 采购申请服务
│   ├── approvalService.ts           # 审批服务

└── hooks/
    ├── usePurchaseCart.ts           # 购物车Hook
    ├── usePurchaseRequest.ts        # 采购申请Hook
    └── useApproval.ts               # 审批Hook
```

## 数据流转

### 状态流转图
```
draft → pending_submission → under_review → under_principle_approval → under_final_approval → approved
  ↓           ↓                ↓                    ↓                      ↓
cart     可编辑修改        部门经理复核         主管审批              公司主管审批
```

### 业务流程
1. **购物车阶段**: 用户添加物品到购物车，可多次编辑
2. **申请提交**: 购物车内容转换为采购申请，状态变为 `pending_submission`
3. **审批流程**: 按三级审批流程依次进行
4. **状态管理**: 支持撤回、拒绝、重新提交等操作
5. **快照保护**: 关键节点创建数据快照


## 风险控制

### 技术风险
- **数据迁移风险**: 现有数据需要完整迁移到新结构
- **状态一致性**: 复杂的状态流转可能导致数据不一致
- **性能风险**: 大量快照数据可能影响查询性能

### 缓解措施
- 制定详细的数据迁移计划和回滚方案
- 实现完整的状态机验证和事务控制
- 设计合理的快照清理策略和索引优化

### 业务风险
- **审批流程变更**: 用户需要适应新的审批流程
- **数据完整性**: 快照机制可能增加系统复杂度

### 缓解措施
- 提供详细的用户培训和操作指南
- 实现完整的审计日志和异常处理机制

## 验收标准

### 功能验收
- [ ] 购物车功能完整可用
- [ ] 三级审批流程正常运行
- [ ] 状态流转符合设计要求
- [ ] 快照机制正常工作


### 性能验收
- [ ] 页面响应时间 < 2秒
- [ ] 数据库查询性能满足要求
- [ ] 并发用户支持 > 50人

### 质量验收
- [ ] 代码覆盖率 > 80%
- [ ] 单元测试通过率 100%
- [ ] 集成测试通过率 100%
- [ ] 无严重Bug遗留

## 后续规划

### 短期优化 (1-2个月)
- 用户界面优化和用户体验提升
- 性能监控和优化
- 用户反馈收集和处理

### 中期扩展 (3-6个月)
- 移动端支持
- 高级报表和分析功能
- 与其他系统的深度集成

### 长期规划 (6个月以上)
- 智能化审批推荐
- 采购成本分析和优化
- 供应商绩效管理

---

**文档版本**: v1.0  
**创建时间**: 2024-01-15  
**最后更新**: 2024-01-15  
**负责人**: AI Agent  
**审核状态**: 待审核
