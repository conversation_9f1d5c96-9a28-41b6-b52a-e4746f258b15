# Supplier Management (供应商管理) 改造进度

## 📋 改造概述

基于需求文档 `05.supplier-management.md`，对现有supplier模块进行全面改造，实现完整的多供应商支持、价格管理、优先级配置等功能。

## 🎯 改造目标

1. **数据库结构改造**: 按照需求文档更新数据模型
2. **API接口改造**: 实现完整的供应商管理API
3. **前端界面改造**: 实现供应商管理界面
4. **功能验证**: 每个步骤完成后可打开页面验证

## 📊 改造步骤

### 第一阶段：数据库结构改造 (预计2天)

#### 步骤1.1：更新Supplier模型 (0.5天)
- [ ] 修改 `backend/app/models/supplier.py`
  - [ ] 更新Supplier表字段结构
  - [ ] 移除不必要的字段（如business_license, tax_number等）
  - [ ] 统一使用 `status` 字段替代 `is_active`
  - [ ] 更新字段注释和约束

**验证方式**: 运行数据库迁移，检查表结构是否正确

#### 步骤1.2：更新ItemSupplier模型 (0.5天)
- [ ] 修改 `backend/app/models/supplier.py` 中的ItemSupplier类
  - [ ] 添加 `priority` 字段（INTEGER类型，0=preferred, 1+=alternative）
  - [ ] 添加 `spq` 和 `moq` 字段
  - [ ] 将 `is_active` 改为 `status` 字段
  - [ ] 移除不必要的字段（如supplier_item_code, supplier_item_name等）
  - [ ] 更新关系定义

**验证方式**: 运行数据库迁移，检查表结构是否正确

#### 步骤1.3：更新SupplierPrice模型 (0.5天)
- [ ] 修改 `backend/app/models/supplier.py` 中的SupplierPrice类
  - [ ] 移除 `spq` 和 `moq` 字段（已移到ItemSupplier）
  - [ ] 将 `is_active` 改为 `status` 字段
  - [ ] 简化价格结构，移除复杂的JSON配置
  - [ ] 添加 `remarks` 字段用于价格备注
  - [ ] 更新字段注释和约束

**验证方式**: 运行数据库迁移，检查表结构是否正确

#### 步骤1.4：创建数据库迁移脚本 (0.5天)
- [ ] 创建数据库迁移文件
- [ ] 处理现有数据的迁移
- [ ] 设置默认值和约束
- [ ] 测试迁移脚本

**验证方式**: 执行迁移脚本，检查数据是否正确迁移

### 第二阶段：API接口改造 (预计3天)

#### 步骤2.1：更新Supplier Schemas (0.5天)
- [ ] 修改 `backend/app/schemas/supplier.py`
  - [ ] 更新SupplierCreate, SupplierUpdate等schema
  - [ ] 添加新的字段验证规则
  - [ ] 移除不需要的字段
  - [ ] 更新响应模型

**验证方式**: 运行单元测试，检查schema验证是否正确

#### 步骤2.2：更新ItemSupplier Schemas (0.5天)
- [ ] 修改 `backend/app/schemas/supplier.py`
  - [ ] 更新ItemSupplierCreate, ItemSupplierUpdate等schema
  - [ ] 添加priority, spq, moq字段
  - [ ] 更新状态字段为status
  - [ ] 添加字段验证规则

**验证方式**: 运行单元测试，检查schema验证是否正确

#### 步骤2.3：更新SupplierPrice Schemas (0.5天)
- [ ] 修改 `backend/app/schemas/supplier.py`
  - [ ] 更新SupplierPriceCreate, SupplierPriceUpdate等schema
  - [ ] 移除spq, moq字段
  - [ ] 更新状态字段为status
  - [ ] 添加remarks字段
  - [ ] 简化价格结构

**验证方式**: 运行单元测试，检查schema验证是否正确

#### 步骤2.4：改造供应商管理API (1天)
- [ ] 修改 `backend/app/api/suppliers.py`
  - [ ] 更新供应商CRUD接口
  - [ ] 添加排序功能（名称、编码、状态、评级、交易金额、购买数量）
  - [ ] 更新搜索和过滤逻辑
  - [ ] 添加供应商编码自动生成功能
  - [ ] 更新错误处理和验证

**验证方式**: 使用API测试工具测试接口功能

#### 步骤2.5：改造物品供应商关系API (0.5天)
- [ ] 修改 `backend/app/api/suppliers.py`
  - [ ] 更新物品供应商关系CRUD接口
  - [ ] 添加优先级管理功能
  - [ ] 添加SPQ、MOQ管理功能
  - [ ] 实现供应商切换逻辑
  - [ ] 更新状态管理

**验证方式**: 使用API测试工具测试接口功能

#### 步骤2.6：改造价格管理API (0.5天)
- [ ] 修改 `backend/app/api/suppliers.py`
  - [ ] 更新价格CRUD接口
  - [ ] 实现价格历史记录（通过不删除记录）
  - [ ] 添加价格备注功能
  - [ ] 实现价格有效期管理
  - [ ] 更新价格计算逻辑

**验证方式**: 使用API测试工具测试接口功能

### 第三阶段：前端界面改造 (预计4天)

#### 步骤3.1：更新供应商列表页面 (1天)
- [ ] 修改 `frontend/src/pages/SupplierManagement.tsx`
  - [ ] 更新表格列定义，添加新字段
  - [ ] 实现排序功能（名称、编码、状态、评级、交易金额、购买数量）
  - [ ] 更新搜索和过滤功能
  - [ ] 更新供应商状态显示
  - [ ] 优化表格布局和样式

**验证方式**: 打开供应商管理页面，检查列表显示和功能

#### 步骤3.2：更新供应商详情页面 (1天)
- [ ] 修改 `frontend/src/pages/SupplierManagement.tsx`
  - [ ] 更新供应商详情弹窗
  - [ ] 添加物品供应商关系显示
  - [ ] 添加价格信息显示
  - [ ] 更新统计信息显示
  - [ ] 优化详情页面布局

**验证方式**: 点击供应商详情，检查信息显示是否正确

#### 步骤3.3：更新供应商编辑页面 (1天)
- [ ] 修改 `frontend/src/pages/SupplierManagement.tsx`
  - [ ] 更新供应商新增/编辑表单
  - [ ] 添加新字段的输入控件
  - [ ] 更新表单验证规则
  - [ ] 优化表单布局
  - [ ] 添加字段说明和提示

**验证方式**: 测试新增和编辑供应商功能

#### 步骤3.4：添加物品供应商关系管理 (1天)
- [ ] 创建物品供应商关系管理组件
  - [ ] 实现物品供应商关系列表
  - [ ] 实现优先级设置功能
  - [ ] 实现SPQ、MOQ设置功能
  - [ ] 实现供应商切换功能
  - [ ] 添加状态管理功能

**验证方式**: 测试物品供应商关系管理功能

### 第四阶段：价格管理功能 (预计2天)

#### 步骤4.1：创建价格管理页面 (1天)
- [ ] 创建 `frontend/src/pages/SupplierPriceManagement.tsx`
  - [ ] 实现价格列表显示
  - [ ] 实现价格新增/编辑功能
  - [ ] 实现价格历史记录显示
  - [ ] 实现价格有效期管理
  - [ ] 添加价格备注功能

**验证方式**: 打开价格管理页面，测试价格管理功能

#### 步骤4.2：实现价格计算器 (1天)
- [ ] 创建价格计算器组件
  - [ ] 实现多供应商比价功能
  - [ ] 实现最优价格推荐
  - [ ] 实现价格分析报告
  - [ ] 添加价格趋势显示

**验证方式**: 测试价格计算器功能

### 第五阶段：集成测试和优化 (预计2天)

#### 步骤5.1：端到端测试 (1天)
- [ ] 测试完整的供应商管理流程
  - [ ] 供应商创建、编辑、删除
  - [ ] 物品供应商关系管理
  - [ ] 价格管理功能
  - [ ] 优先级切换功能
  - [ ] 数据验证和错误处理

**验证方式**: 完整测试所有功能，确保流程正确

#### 步骤5.2：性能优化和bug修复 (1天)
- [ ] 优化数据库查询性能
- [ ] 优化前端页面加载速度
- [ ] 修复发现的bug
- [ ] 完善错误处理
- [ ] 优化用户体验

**验证方式**: 性能测试和用户体验测试

## 🔍 验证检查点

### 每个步骤完成后的验证方式：

1. **数据库结构验证**
   - 运行数据库迁移
   - 检查表结构是否正确
   - 验证字段约束和索引

2. **API接口验证**
   - 使用Postman或类似工具测试API
   - 验证请求和响应格式
   - 测试错误处理

3. **前端界面验证**
   - 打开对应页面
   - 测试所有交互功能
   - 验证数据显示正确性

4. **功能集成验证**
   - 测试完整业务流程
   - 验证数据一致性
   - 检查权限控制

## 📈 进度跟踪

| 阶段 | 步骤 | 状态 | 完成时间 | 备注 |
|------|------|------|----------|------|
| 第一阶段 | 1.1 更新Supplier模型 | ⏳ 待开始 | - | - |
| 第一阶段 | 1.2 更新ItemSupplier模型 | ⏳ 待开始 | - | - |
| 第一阶段 | 1.3 更新SupplierPrice模型 | ⏳ 待开始 | - | - |
| 第一阶段 | 1.4 创建数据库迁移脚本 | ⏳ 待开始 | - | - |
| 第二阶段 | 2.1 更新Supplier Schemas | ⏳ 待开始 | - | - |
| 第二阶段 | 2.2 更新ItemSupplier Schemas | ⏳ 待开始 | - | - |
| 第二阶段 | 2.3 更新SupplierPrice Schemas | ⏳ 待开始 | - | - |
| 第二阶段 | 2.4 改造供应商管理API | ⏳ 待开始 | - | - |
| 第二阶段 | 2.5 改造物品供应商关系API | ⏳ 待开始 | - | - |
| 第二阶段 | 2.6 改造价格管理API | ⏳ 待开始 | - | - |
| 第三阶段 | 3.1 更新供应商列表页面 | ⏳ 待开始 | - | - |
| 第三阶段 | 3.2 更新供应商详情页面 | ⏳ 待开始 | - | - |
| 第三阶段 | 3.3 更新供应商编辑页面 | ⏳ 待开始 | - | - |
| 第三阶段 | 3.4 添加物品供应商关系管理 | ⏳ 待开始 | - | - |
| 第四阶段 | 4.1 创建价格管理页面 | ⏳ 待开始 | - | - |
| 第四阶段 | 4.2 实现价格计算器 | ⏳ 待开始 | - | - |
| 第五阶段 | 5.1 端到端测试 | ⏳ 待开始 | - | - |
| 第五阶段 | 5.2 性能优化和bug修复 | ⏳ 待开始 | - | - |

## 🚀 开始实施

建议按照以下顺序开始实施：

1. **立即开始**: 第一阶段数据库结构改造
2. **并行进行**: 第二阶段API接口改造
3. **逐步验证**: 每个步骤完成后立即验证
4. **持续集成**: 确保代码质量和功能正确性

---

*本文档将随着改造进度持续更新* 