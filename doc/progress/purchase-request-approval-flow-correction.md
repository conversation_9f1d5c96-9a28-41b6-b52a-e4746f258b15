# 采购申请审批流程描述修正记录

## 修正概述

根据需求文档 `02.user-permission-management.md` 中的三级审批流程定义，对前端代码中的审批流程描述进行了统一修正，确保与需求文档保持一致。

## 需求文档中的三级审批流程

根据需求文档，采购申请采用**三级审批流程**：

1. **第一级：部门经理复核** (`purchase.review`)
   - 角色：部门经理
   - 职责：对采购申请进行初步审核，检查申请合理性

2. **第二级：物品管理员原则审批** (`purchase.principle_approve`)
   - 角色：物品管理员
   - 职责：对申请进行业务合理性评估，确认采购必要性

3. **第三级：公司主管审批** (`purchase.approve`)
   - 角色：公司主管
   - 职责：最终业务决策，审批通过后触发合并采购流程

## 修正内容

### 1. 申请进度组件 (RequestStatusSteps.tsx)

**修正前：**
```typescript
const steps: StepItem[] = [
  { title: '部门经理复核', status: 'wait', level: 'review' },
  { title: '采购主管审批', status: 'wait', level: 'principle_approval' },
  { title: 'Boss最终审批', status: 'wait', level: 'final_approval' }
];
```

**修正后：**
```typescript
const steps: StepItem[] = [
  { title: '部门经理复核', status: 'wait', level: 'review' },
  { title: '物品管理员原则审批', status: 'wait', level: 'principle_approval' },
  { title: '公司主管审批', status: 'wait', level: 'final_approval' }
];
```

### 2. 状态配置 (PurchaseRequestDetail.tsx)

**修正前：**
```typescript
const statusConfig = {
  under_review: { color: 'orange', text: '待审批', icon: <CheckCircleOutlined /> },
  under_principle_approval: { color: 'orange', text: '主管审批', icon: <CheckCircleOutlined /> },
  under_final_approval: { color: 'orange', text: '最终审批', icon: <CheckCircleOutlined /> },
};
```

**修正后：**
```typescript
const statusConfig = {
  under_review: { color: 'orange', text: '部门经理复核', icon: <CheckCircleOutlined /> },
  under_principle_approval: { color: 'orange', text: '物品管理员原则审批', icon: <CheckCircleOutlined /> },
  under_final_approval: { color: 'orange', text: '公司主管审批', icon: <CheckCircleOutlined /> },
};
```

### 3. 审批层级文本 (PurchaseRequestDetail.tsx)

**修正前：**
```typescript
const getApprovalLevelText = (level: string) => {
  switch (level) {
    case 'principle_approval':
      return '主管审批';
    case 'final_approval':
      return '最终审批';
    default:
      return level;
  }
};
```

**修正后：**
```typescript
const getApprovalLevelText = (level: string) => {
  switch (level) {
    case 'review':
      return '部门经理复核';
    case 'principle_approval':
      return '物品管理员原则审批';
    case 'final_approval':
      return '公司主管审批';
    default:
      return level;
  }
};
```

### 4. 状态文本 (PurchaseRequestDetail.tsx)

**修正前：**
```typescript
const statusConfig = {
  under_review: '待审批',
  under_principle_approval: '主管审批',
  under_final_approval: '最终审批',
};
```

**修正后：**
```typescript
const statusConfig = {
  under_review: '部门经理复核',
  under_principle_approval: '物品管理员原则审批',
  under_final_approval: '公司主管审批',
};
```

### 5. 审批按钮文本 (PurchaseRequestDetail.tsx)

**修正前：**
```typescript
{request.status === 'under_principle_approval' && (
  <Button type="primary" onClick={() => showApprovalModal('principle_approval')}>
    主管审批
  </Button>
)}

{request.status === 'under_final_approval' && (
  <Button type="primary" onClick={() => showApprovalModal('final_approval')}>
    最终审批
  </Button>
)}
```

**修正后：**
```typescript
{request.status === 'under_principle_approval' && (
  <Button type="primary" onClick={() => showApprovalModal('principle_approval')}>
    物品管理员原则审批
  </Button>
)}

{request.status === 'under_final_approval' && (
  <Button type="primary" onClick={() => showApprovalModal('final_approval')}>
    公司主管审批
  </Button>
)}
```

### 6. 审批弹窗状态名称 (ApprovalForm.tsx)

**修正前：**
```typescript
const statusNames = {
  'under_review': '待审批',
  'under_principle_approval': '待审批',
  'under_final_approval': '待审批'
};
```

**修正后：**
```typescript
const statusNames = {
  'under_review': '部门经理复核',
  'under_principle_approval': '物品管理员原则审批',
  'under_final_approval': '公司主管审批'
};
```

### 7. 流转动作描述 (PurchaseRequestDetail.tsx)

**修正前：**
```typescript
case 'submit':
  return '申请已提交';
case 'approve':
  return '申请已批准';
case 'reject':
  return '申请已拒绝';
case 'withdraw':
  return '申请已撤销';
```

**修正后：**
```typescript
case 'submit':
  return '申请已提交，等待部门经理复核';
case 'approve':
  return '申请已批准，进入下一级审批';
case 'reject':
  return '申请已拒绝，流程结束';
case 'withdraw':
  return '申请已撤销，回到待提交状态';
case 'return':
  return '申请被退回，需要重新修改';
```

## 修正效果

### 修正前的问题
- 审批流程描述不统一，使用了"主管审批"、"最终审批"等模糊描述
- 与需求文档中的三级审批流程定义不一致
- 用户可能对审批流程产生误解

### 修正后的效果
- 审批流程描述与需求文档完全一致
- 明确了每个审批级别的具体角色和职责
- 提升了用户体验，用户能清楚了解当前审批状态
- 保持了代码的一致性和可维护性

## 涉及的文件

1. `frontend/src/apps/admin/components/purchase/RequestStatusSteps.tsx` - 申请进度组件
2. `frontend/src/apps/admin/pages/PurchaseRequestDetail.tsx` - 申请详情页面
3. `frontend/src/apps/admin/components/purchase/ApprovalForm.tsx` - 审批弹窗组件

## 验证方法

1. 检查申请进度显示是否正确显示三级审批流程
2. 验证流转历史中的审批级别描述是否准确
3. 确认审批弹窗中的状态名称是否与需求一致
4. 测试不同审批状态下的显示效果

## 总结

通过本次修正，采购申请的审批流程描述现在与需求文档完全一致，确保了：
- 业务逻辑的准确性
- 用户界面的专业性
- 代码维护的一致性
- 系统功能的完整性

修正完成后，用户能够清楚地了解采购申请的三级审批流程，每个审批级别的具体角色和职责，提升了系统的可用性和专业性。
