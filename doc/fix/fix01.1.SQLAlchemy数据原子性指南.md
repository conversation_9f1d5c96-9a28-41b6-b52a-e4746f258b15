# SQLAlchemy 原子化操作指南

重要: 要先设置 synchronize_session=False 并且没有使用对应特性

## 概述

本文档介绍如何使用 SQLAlchemy 实现数据库操作，分为两大类：
1. **单表原子化操作**：适用于单张表的数值更新，可以完全原子化
2. **多表行锁操作**：适用于涉及多张表的操作，使用行锁确保一致性

## 1. 单表原子化操作

### 1.1 增/减固定值操作

#### 库存数量 +1 操作

```python
from sqlalchemy import update, func
from sqlalchemy.orm import Session

def increment_inventory_stock(inventory_id: int, increment: int = 1):
    """原子化增加库存数量"""
    stmt = update(Inventory).where(
        Inventory.id == inventory_id
    ).values(
        stock_quantity=Inventory.stock_quantity + increment,
        last_updated=func.now()
    )
    
    session.execute(stmt)
    session.commit()
```

#### 库存数量 -1 操作

```python
def decrement_inventory_stock(inventory_id: int, decrement: int = 1):
    """原子化减少库存数量"""
    stmt = update(Inventory).where(
        Inventory.id == inventory_id
    ).values(
        stock_quantity=Inventory.stock_quantity - decrement,
        last_updated=func.now()
    )
    
    session.execute(stmt)
    session.commit()
```

#### 使用 query.update() 方法

```python
def update_inventory_stock(inventory_id: int, change_amount: int):
    """原子化更新库存数量（正数增加，负数减少）"""
    session.query(Inventory).filter(
        Inventory.id == inventory_id
    ).update({
        "stock_quantity": Inventory.stock_quantity + change_amount,
        "last_updated": func.now()
    })
    session.commit()
```

### 1.2 增/减其他表的值操作

#### 基于其他表数据更新库存

```python
def update_inventory_from_order(order_id: int):
    """基于订单数据原子化更新库存"""
    
    # 使用子查询获取订单总数量
    order_quantity = select(func.sum(OrderItem.quantity)).where(
        OrderItem.order_id == order_id
    ).scalar_subquery()
    
    # 原子化更新库存
    stmt = update(Inventory).where(
        Inventory.id == order_id
    ).values(
        stock_quantity=Inventory.stock_quantity - order_quantity,
        last_updated=func.now()
    )
    
    session.execute(stmt)
    session.commit()
```

#### 基于其他表数据计算并更新

```python
def update_average_cost_from_purchases(inventory_id: int):
    """基于采购记录原子化更新平均成本"""
    
    # 计算新的平均成本
    avg_cost_subquery = select(
        func.sum(Purchase.quantity * Purchase.unit_price) / 
        func.sum(Purchase.quantity)
    ).where(
        Purchase.inventory_id == inventory_id,
        Purchase.status == 'completed'
    ).scalar_subquery()
    
    # 原子化更新平均成本
    stmt = update(Inventory).where(
        Inventory.id == inventory_id
    ).values(
        average_cost=avg_cost_subquery,
        last_updated=func.now()
    )
    
    session.execute(stmt)
    session.commit()
```

### 1.3 条件原子化更新

#### 库存充足时才减少

```python
def decrease_inventory_if_sufficient(inventory_id: int, decrease_amount: int):
    """只有当库存充足时才原子化减少库存"""
    result = session.query(Inventory).filter(
        Inventory.id == inventory_id,
        Inventory.stock_quantity >= decrease_amount
    ).update({
        "stock_quantity": Inventory.stock_quantity - decrease_amount,
        "last_updated": func.now()
    })
    
    if result == 0:
        raise ValueError("库存不足或记录不存在")
    
    session.commit()
```

#### 乐观锁版本号更新

```python
def update_with_optimistic_lock(inventory_id: int, new_quantity: int, version: int):
    """使用乐观锁的原子化更新"""
    result = session.query(Inventory).filter(
        Inventory.id == inventory_id,
        Inventory.version == version
    ).update({
        "stock_quantity": new_quantity,
        "version": Inventory.version + 1,
        "last_updated": func.now()
    })
    
    if result == 0:
        raise ValueError("数据已被其他用户修改，请刷新后重试")
    
    session.commit()
```

## 2. 多表行锁操作

### 2.1 行锁基础概念

当操作涉及多张表时，使用行锁确保数据一致性：

```python
from sqlalchemy.orm import Session

def lock_inventory_row_for_update(session: Session, inventory_id: int):
    """锁定库存行，防止其他事务同时修改"""
    # 使用 SELECT ... FOR UPDATE 锁定行
    inventory = session.query(Inventory).filter(
        Inventory.id == inventory_id
    ).with_for_update().first()
    
    if not inventory:
        raise ValueError("库存记录不存在")
    
    return inventory
```

### 2.2 多表操作的行锁策略

#### 2.2.1 库存变更 + 日志记录

```python
def update_inventory_with_log(
    session: Session, 
    inventory_id: int, 
    change_amount: int, 
    reason: str
):
    """更新库存并记录日志 - 使用行锁"""
    
    try:
        # 1. 锁定库存行
        inventory = session.query(Inventory).filter(
            Inventory.id == inventory_id
        ).with_for_update().first()
        
        if not inventory:
            raise ValueError("库存记录不存在")
        
        # 2. 验证库存是否充足（减少时）
        if change_amount < 0 and inventory.stock_quantity + change_amount < 0:
            raise ValueError("库存不足")
        
        # 3. 更新库存
        inventory.stock_quantity += change_amount
        inventory.last_updated = func.now()
        
        # 4. 插入日志记录
        log_record = InventoryLog(
            inventory_id=inventory_id,
            change_amount=change_amount,
            reason=reason,
            created_at=func.now()
        )
        session.add(log_record)
        
        # 5. 提交事务，释放锁
        session.commit()
        
    except Exception as e:
        session.rollback()
        raise e
```

#### 2.2.2 库存转移操作

```python
def transfer_inventory_between_warehouses(
    session: Session,
    from_inventory_id: int,
    to_inventory_id: int,
    transfer_amount: int
):
    """仓库间库存转移 - 使用行锁"""
    
    try:
        # 关键：按照固定顺序获取锁，避免死锁
        inventory_ids = sorted([from_inventory_id, to_inventory_id])
        
        # 1. 按照排序后的ID顺序锁定库存记录
        inventories = {}
        for inv_id in inventory_ids:
            inventory = session.query(Inventory).filter(
                Inventory.id == inv_id
            ).with_for_update().first()
            
            if not inventory:
                raise ValueError(f"库存记录 {inv_id} 不存在")
            
            inventories[inv_id] = inventory
        
        # 2. 验证源库存是否充足
        from_inventory = inventories[from_inventory_id]
        if from_inventory.stock_quantity < transfer_amount:
            raise ValueError("源库存不足")
        
        # 3. 执行转移操作
        from_inventory.stock_quantity -= transfer_amount
        to_inventory = inventories[to_inventory_id]
        to_inventory.stock_quantity += transfer_amount
        
        # 4. 更新最后修改时间
        from_inventory.last_updated = func.now()
        to_inventory.last_updated = func.now()
        
        # 5. 记录转移日志
        transfer_log = InventoryTransferLog(
            from_inventory_id=from_inventory_id,
            to_inventory_id=to_inventory_id,
            transfer_amount=transfer_amount,
            transfer_date=func.now()
        )
        session.add(transfer_log)
        
        # 6. 提交事务，释放所有锁
        session.commit()
        
    except Exception as e:
        session.rollback()
        raise e
```

#### 2.2.3 订单履约操作

```python
def process_order_fulfillment(session: Session, order_id: int):
    """订单履约 - 使用行锁确保一致性"""
    
    try:
        # 1. 查询订单详情
        order = session.query(Order).filter(Order.id == order_id).first()
        if not order:
            raise ValueError("订单不存在")
        
        # 2. 锁定并更新所有相关产品的库存
        for item in order.items:
            # 锁定库存行
            inventory = session.query(Inventory).filter(
                Inventory.product_id == item.product_id
            ).with_for_update().first()
            
            if not inventory:
                raise ValueError(f"产品 {item.product_id} 库存记录不存在")
            
            if inventory.stock_quantity < item.quantity:
                raise ValueError(f"产品 {item.product_id} 库存不足")
            
            # 减少库存
            inventory.stock_quantity -= item.quantity
            inventory.last_updated = func.now()
            
            # 记录库存变动
            inventory_log = InventoryLog(
                inventory_id=inventory.id,
                change_amount=-item.quantity,
                reason=f"订单 {order_id} 履约",
                created_at=func.now()
            )
            session.add(inventory_log)
        
        # 3. 创建发货单
        shipment = Shipment(
            order_id=order_id,
            status='created',
            created_at=func.now()
        )
        session.add(shipment)
        
        # 4. 更新订单状态
        order.status = "fulfilled"
        order.fulfilled_at = func.now()
        
        # 5. 提交事务，释放所有锁
        session.commit()
        
    except Exception as e:
        session.rollback()
        raise e
```

### 2.3 行锁超时和重试机制

```python
import time
from sqlalchemy.exc import OperationalError

def retry_with_lock_timeout(operation_func, max_retries: int = 3):
    """带重试机制的行锁操作"""
    
    for attempt in range(max_retries):
        try:
            return operation_func()
        except OperationalError as e:
            if "lock timeout" in str(e).lower() or "deadlock" in str(e).lower():
                if attempt < max_retries - 1:
                    # 等待一段时间后重试
                    time.sleep(0.1 * (2 ** attempt))  # 指数退避
                    continue
                else:
                    raise Exception("操作超时，请稍后重试")
            else:
                raise e
        except Exception as e:
            raise e

# 使用示例
def safe_inventory_transfer_with_retry(
    session: Session,
    from_inventory_id: int,
    to_inventory_id: int,
    transfer_amount: int
):
    """带重试机制的安全库存转移"""
    
    def transfer_operation():
        return transfer_inventory_between_warehouses(
            session, from_inventory_id, to_inventory_id, transfer_amount
        )
    
    return retry_with_lock_timeout(transfer_operation)
```

## 3. 事务管理

### 3.1 上下文管理器（推荐）

```python
from contextlib import contextmanager

@contextmanager
def transaction_scope(session: Session):
    """事务上下文管理器"""
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise

# 使用示例
def atomic_inventory_operation(inventory_id: int, change_amount: int, reason: str):
    """使用上下文管理器的原子化操作"""
    with transaction_scope(session):
        # 更新库存
        session.query(Inventory).filter(
            Inventory.id == inventory_id
        ).update({
            "stock_quantity": Inventory.stock_quantity + change_amount,
            "last_updated": func.now()
        })
        
        # 记录日志
        inventory_log = InventoryLog(
            inventory_id=inventory_id,
            change_amount=change_amount,
            reason=reason,
            created_at=func.now()
        )
        session.add(inventory_log)
```

## 4. 最佳实践

### 4.1 选择策略

1. **单表操作**：优先使用原子化操作，性能更好
2. **多表操作**：使用行锁 + 事务，确保数据一致性
3. **混合场景**：单表部分用原子化，多表部分用行锁

### 4.2 行锁使用原则

1. **锁的顺序**：始终按照固定顺序获取锁（如按ID排序），避免死锁
2. **锁的粒度**：只锁定必要的行，避免锁定整个表
3. **锁的时长**：尽快释放锁，避免长事务
4. **超时处理**：设置合理的锁超时时间，并提供重试机制

### 4.3 常见陷阱

1. **忘记提交**：确保在事务结束时调用 `commit()`
2. **异常处理不当**：正确处理异常并回滚事务
3. **长事务**：避免在事务中执行耗时操作
4. **死锁**：没有按照固定顺序获取锁
5. **锁的粒度过大**：锁定不必要的行或表，影响并发性能

## 5. 迁移现有代码的步骤

### 5.1 识别需要改造的代码

1. **单表操作**：查找所有先查询再更新的库存操作
2. **多表操作**：识别涉及多表操作但未使用事务的代码

### 5.2 逐步改造

1. **第一阶段**：改造单表原子化操作
2. **第二阶段**：为多表操作添加行锁和事务管理
3. **第三阶段**：优化复杂业务逻辑
4. **第四阶段**：添加错误处理和重试机制

### 5.3 测试验证

1. 编写单元测试验证原子性
2. 进行并发测试验证线程安全
3. **行锁测试**：测试死锁场景和锁超时处理
4. **压力测试**：高并发场景下的性能测试

通过遵循本指南，你可以根据操作类型选择合适的策略：
- **单表操作**使用原子化，提高性能
- **多表操作**使用行锁，确保一致性
