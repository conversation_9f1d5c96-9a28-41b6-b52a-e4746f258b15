# SQLAlchemy 原子化操作改造清单

## 概述

本文档列出了当前项目中需要进行原子化操作改造的具体位置，通过改造这些操作可以提高系统并发性能，保证数据一致性。

## 1. 库存管理模块改造清单

### 1.1 手工入库操作
- **文件位置**: `Backend (Python)/app/apps/admin/api/inventory.py`
- **行号**: 583-654
- **当前问题**: 先查询库存记录，再更新数量，存在并发风险
- **优先级**: 🔴 高
- **影响范围**: 库存数量准确性、并发安全

### 1.2 库存调整操作
- **文件位置**: `Backend (Python)/app/apps/admin/api/inventory.py`
- **行号**: 658-695
- **当前问题**: 同样存在先查询再更新的并发风险
- **优先级**: 🔴 高
- **影响范围**: 库存数量准确性、并发安全

## 2. Kiosk 端物品领取模块改造清单

### 2.1 物品领取操作
- **文件位置**: `Backend (Python)/app/api/kiosk.py`
- **行号**: 370-470
- **当前问题**: 先验证库存充足，再扣减库存，在验证和扣减之间，其他操作可能已经改变了库存
- **优先级**: 🔴 高
- **影响范围**: 库存数量准确性、并发安全、业务逻辑正确性

### 2.2 撤销领取操作
- **文件位置**: `Backend (Python)/app/api/kiosk.py`
- **行号**: 570-648
- **当前问题**: 同样存在先查询再更新的竞态条件
- **优先级**: 🟡 中
- **影响范围**: 库存数量准确性、并发安全
