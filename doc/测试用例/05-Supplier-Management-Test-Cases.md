# Supplier Management Module Test Cases

## Test Overview
This test case covers the core functionality of the admin-side supplier management module, including supplier information management, supplier evaluation, price management, etc.

## Test Case Table

| Test ID | Test Title | Test Type | Priority | Prerequisites | Test Steps | Expected Results | Test Results | Notes |
|---------|------------|-----------|----------|---------------|------------|------------------|--------------|-------|
| TC-052 | Verify Supplier Creation Function | Functional Test | High | 1. User is logged in with supplier.create permissions<br>2. Enter supplier management page | 1. Click "Add Supplier" button<br>2. Fill in basic supplier information<br>3. Fill in contact information<br>4. Save supplier | 1. Supplier creation successful<br>2. Display success message<br>3. Supplier list updated | □ Pass<br>□ Fail | |
| TC-053 | Verify Supplier Information Edit Function | Functional Test | High | 1. User is logged in with supplier.update permissions<br>2. Editable supplier exists<br>3. Enter supplier management page | 1. Select target supplier<br>2. Click edit button<br>3. Modify supplier information<br>4. Save changes | 1. Supplier information updated successfully<br>2. Display success message<br>3. Supplier list shows updated information | □ Pass<br>□ Fail | |
| TC-054 | Verify Supplier Information Viewing Function | Functional Test | Medium | 1. User is logged in with supplier.read permissions<br>2. Supplier data exists<br>3. Enter supplier management page | 1. View supplier list<br>2. View supplier details<br>3. View supplier history records | 1. Supplier information displayed correctly<br>2. Contact information complete<br>3. History records complete | □ Pass<br>□ Fail | |
| TC-055 | Verify Supplier Deletion Function | Functional Test | Medium | 1. User is logged in with supplier.delete permissions<br>2. Deletable supplier exists<br>3. Enter supplier management page | 1. Select target supplier<br>2. Click delete button<br>3. Confirm deletion operation | 1. Supplier deletion successful<br>2. Display success message<br>3. Supplier list updated | □ Pass<br>□ Fail | |
| TC-056 | Verify Supplier Price Management Function | Functional Test | High | 1. User is logged in with supplier.price_manage permissions<br>2. Supplier and items exist<br>3. Enter supplier management page | 1. Select target supplier<br>2. Enter price management<br>3. Set item prices<br>4. Save price configuration | 1. Price configuration successful<br>2. Price information correctly saved<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-057 | Verify Supplier Evaluation Function | Functional Test | Medium | 1. User is logged in with supplier.evaluate permissions<br>2. Supplier exists<br>3. Enter supplier management page | 1. Select target supplier<br>2. Enter evaluation page<br>3. Fill in evaluation indicators<br>4. Submit evaluation results | 1. Evaluation submission successful<br>2. Evaluation records saved<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-058 | Verify Supplier Query and Filter Function | Functional Test | Medium | 1. User is logged in<br>2. Multiple suppliers exist<br>3. Enter supplier management page | 1. Use name search<br>2. Use region filter<br>3. Use status filter<br>4. View filter results | 1. Filter conditions correctly applied<br>2. Search results accurate<br>3. Filter result count correct | □ Pass<br>□ Fail | |
| TC-059 | Verify Supplier Status Management Function | Functional Test | Medium | 1. User is logged in with supplier.update permissions<br>2. Target supplier exists<br>3. Enter supplier management page | 1. Select target supplier<br>2. Modify supplier status<br>3. Save changes | 1. Status modification successful<br>2. Supplier status takes effect immediately<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-060 | Verify Supplier Contact Management Function | Functional Test | Medium | 1. User is logged in with supplier.update permissions<br>2. Target supplier exists<br>3. Enter supplier management page | 1. Select target supplier<br>2. Enter contact management<br>3. Add/edit contacts<br>4. Save contact information | 1. Contact information saved successfully<br>2. Contact list updated<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-061 | Verify Supplier Qualification Management Function | Functional Test | Medium | 1. User is logged in with supplier.update permissions<br>2. Target supplier exists<br>3. Enter supplier management page | 1. Select target supplier<br>2. Enter qualification management<br>3. Upload qualification files<br>4. Set validity period<br>5. Save qualification information | 1. Qualification information saved successfully<br>2. File upload normal<br>3. Validity period set correctly | □ Pass<br>□ Fail | |
| TC-062 | Verify Supplier Duplicate Creation Check | Exception Test | Medium | 1. User is logged in with supplier.create permissions<br>2. Supplier with same name exists<br>3. Enter supplier creation page | 1. Fill in same name as existing supplier<br>2. Fill in other information<br>3. Click save | 1. Display duplicate supplier message<br>2. Prevent duplicate creation<br>3. Prompt user to check existing suppliers | □ Pass<br>□ Fail | |
| TC-063 | Verify Supplier Permission Control | Exception Test | Medium | 1. User is logged in but with insufficient permissions<br>2. Try to execute supplier operations | 1. View supplier information<br>2. Try to edit supplier<br>3. Try to delete supplier | 1. Display insufficient permission message<br>2. Cannot execute related operations<br>3. System security protection effective | □ Pass<br>□ Fail | |
| TC-064 | Verify Complete Supplier Process | End-to-End Test | High | 1. User has complete permissions<br>2. Enter supplier management page | 1. Create new supplier<br>2. Configure supplier prices<br>3. Perform supplier evaluation<br>4. View final results | 1. Entire process executes normally<br>2. Data saved correctly<br>3. Functions complete and usable | □ Pass<br>□ Fail | |

## Test Data Requirements
- Test user accounts: item_admin permission users
- Test suppliers: At least 5 suppliers with different statuses
- Test items: At least 10 items from different categories
- Test prices: Item price configurations for different suppliers

## Test Environment Requirements
- System running normally
- Database connection normal
- Supplier data initialized
- File upload function normal
