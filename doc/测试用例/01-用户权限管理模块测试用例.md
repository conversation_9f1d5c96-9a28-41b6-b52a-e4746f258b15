# 用户权限管理模块测试用例

## 测试概述
本测试用例覆盖管理端用户权限管理模块的核心功能，包括用户管理、角色管理、权限分配等。

## 测试用例表格

| 测试编号 | 测试标题 | 测试类型 | 优先级 | 前置条件 | 测试步骤 | 预期结果 | 测试结果 | 备注 |
|---------|---------|---------|--------|----------|----------|----------|----------|------|
| TC-001 | 验证用户登录功能 | 功能点测试 | 高 | 1. 系统已启动<br>2. 存在测试用户账号<br>3. 用户未登录 | 1. 访问登录页面<br>2. 输入正确的用户名和密码<br>3. 点击登录按钮 | 1. 登录成功<br>2. 跳转到主页面<br>3. 显示用户信息 | □ 通过<br>□ 失败 | |
| TC-002 | 验证用户登录失败处理 | 异常情况测试 | 中 | 1. 系统已启动<br>2. 用户未登录 | 1. 访问登录页面<br>2. 输入错误的用户名或密码<br>3. 点击登录按钮 | 1. 显示登录失败提示<br>2. 停留在登录页面<br>3. 清空密码输入框 | □ 通过<br>□ 失败 | |
| TC-003 | 验证用户权限验证功能 | 功能点测试 | 高 | 1. 用户已登录<br>2. 用户具有特定权限 | 1. 访问需要权限的功能页面<br>2. 执行需要权限的操作 | 1. 功能正常访问<br>2. 操作正常执行<br>3. 权限控制生效 | □ 通过<br>□ 失败 | |
| TC-004 | 验证权限不足时的访问控制 | 异常情况测试 | 中 | 1. 用户已登录<br>2. 用户权限不足 | 1. 尝试访问超出权限的功能<br>2. 尝试执行超出权限的操作 | 1. 显示权限不足提示<br>2. 无法执行相关操作<br>3. 系统安全防护生效 | □ 通过<br>□ 失败 | |
| TC-005 | 验证用户角色分配功能 | 功能点测试 | 高 | 1. 管理员已登录<br>2. 存在可分配的角色<br>3. 存在目标用户 | 1. 进入用户管理页面<br>2. 选择目标用户<br>3. 分配新角色<br>4. 保存角色分配 | 1. 角色分配成功<br>2. 用户权限立即生效<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-006 | 验证用户角色撤销功能 | 功能点测试 | 高 | 1. 管理员已登录<br>2. 用户已分配角色<br>3. 进入用户管理页面 | 1. 选择目标用户<br>2. 撤销现有角色<br>3. 保存更改 | 1. 角色撤销成功<br>2. 用户权限立即失效<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-007 | 验证用户状态管理功能 | 功能点测试 | 中 | 1. 管理员已登录<br>2. 存在目标用户<br>3. 进入用户管理页面 | 1. 选择目标用户<br>2. 修改用户状态（启用/禁用）<br>3. 保存更改 | 1. 状态修改成功<br>2. 用户状态立即生效<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-008 | 验证用户密码重置功能 | 功能点测试 | 中 | 1. 管理员已登录<br>2. 存在目标用户<br>3. 进入用户管理页面 | 1. 选择目标用户<br>2. 执行密码重置操作<br>3. 确认重置 | 1. 密码重置成功<br>2. 生成临时密码<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-009 | 验证角色权限配置功能 | 功能点测试 | 高 | 1. 管理员已登录<br>2. 存在目标角色<br>3. 进入角色管理页面 | 1. 选择目标角色<br>2. 配置角色权限<br>3. 保存权限配置 | 1. 权限配置成功<br>2. 角色权限立即生效<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-010 | 验证用户登出功能 | 功能点测试 | 中 | 1. 用户已登录<br>2. 在主页面 | 1. 点击用户头像或菜单<br>2. 选择登出选项<br>3. 确认登出 | 1. 登出成功<br>2. 跳转到登录页面<br>3. 清除用户会话信息 | □ 通过<br>□ 失败 | |

## 测试数据要求
- 测试用户账号：admin/admin123（超级管理员）
- 测试角色：company_director, item_admin, dept_manager, dept_item_admin
- 测试权限：各种功能模块的权限点

## 测试环境要求
- 系统正常运行
- 数据库连接正常
- 用户权限数据已初始化
