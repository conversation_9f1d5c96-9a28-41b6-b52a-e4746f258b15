# Purchase Management Module Test Cases

## Test Overview
This test case covers the core functionality of the admin-side purchase management module, including purchase request creation, shopping cart management, purchase approval process, etc.

## Test Case Table

| Test ID | Test Title | Test Type | Priority | Prerequisites | Test Steps | Expected Results | Test Results | Notes |
|---------|------------|-----------|----------|---------------|------------|------------------|--------------|-------|
| TC-023 | Verify Shopping Cart Add Item Function | Functional Test | High | 1. User is logged in with cart.add_item permissions<br>2. Item information exists<br>3. Enter shopping cart page | 1. Click "Add Item" button<br>2. Select item and quantity<br>3. Fill in requirement description<br>4. Add to cart | 1. Item successfully added to cart<br>2. Cart quantity updated<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-024 | Verify Shopping Cart Edit Item Function | Functional Test | Medium | 1. User is logged in with cart.update_item permissions<br>2. Items exist in cart<br>3. Enter shopping cart page | 1. Select item in cart<br>2. Click edit button<br>3. Modify quantity and description<br>4. Save changes | 1. Item information updated successfully<br>2. Cart displays updated information<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-025 | Verify Shopping Cart Remove Item Function | Functional Test | Medium | 1. User is logged in with cart.remove_item permissions<br>2. Items exist in cart<br>3. Enter shopping cart page | 1. Select item in cart<br>2. Click delete button<br>3. Confirm deletion operation | 1. Item successfully removed from cart<br>2. Cart quantity updated<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-026 | Verify Shopping Cart Submit Request Function | Functional Test | High | 1. User is logged in with cart.submit permissions<br>2. Items exist in cart<br>3. Enter shopping cart page | 1. Check cart items<br>2. Click "Submit Request" button<br>3. Confirm submission information<br>4. Complete submission | 1. Purchase request created successfully<br>2. Cart cleared<br>3. Request enters approval process | □ Pass<br>□ Fail | |
| TC-027 | Verify Purchase Request Creation Function | Functional Test | High | 1. User is logged in with purchase.request permissions<br>2. Item information exists<br>3. Enter purchase request page | 1. Click "New Request" button<br>2. Select items and quantity<br>3. Fill in request reason<br>4. Submit request | 1. Request created successfully<br>2. Status is "Pending Approval"<br>3. Request number generated | □ Pass<br>□ Fail | |
| TC-028 | Verify Purchase Request Edit Function | Functional Test | Medium | 1. User is logged in with purchase.update permissions<br>2. Editable request exists<br>3. Enter purchase request page | 1. Select target request<br>2. Click edit button<br>3. Modify request information<br>4. Save changes | 1. Request information updated successfully<br>2. Display success message<br>3. Request status unchanged | □ Pass<br>□ Fail | |
| TC-029 | Verify Purchase Request Deletion Function | Functional Test | Medium | 1. User is logged in with purchase.delete permissions<br>2. Deletable request exists<br>3. Enter purchase request page | 1. Select target request<br>2. Click delete button<br>3. Confirm deletion operation | 1. Request deletion successful<br>2. Display success message<br>3. Request list updated | □ Pass<br>□ Fail | |
| TC-030 | Verify Purchase Request Withdrawal Function | Functional Test | Medium | 1. User is logged in with purchase.withdraw permissions<br>2. Withdrawable request exists<br>3. Enter purchase request page | 1. Select target request<br>2. Click withdraw button<br>3. Confirm withdrawal operation | 1. Request withdrawal successful<br>2. Status updated to "Withdrawn"<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-031 | Verify Department Manager Review Function | Functional Test | High | 1. User is logged in with purchase.review permissions<br>2. Request pending review exists<br>3. Enter purchase request page | 1. View request details<br>2. Perform review operation<br>3. Fill in review comments<br>4. Complete review | 1. Review process executes normally<br>2. Request status correctly updated<br>3. Review records completely saved | □ Pass<br>□ Fail | |
| TC-032 | Verify Item Administrator Principle Approval Function | Functional Test | High | 1. User is logged in with purchase.principle_approve permissions<br>2. Request pending principle approval exists<br>3. Enter purchase request page | 1. View request details<br>2. Perform principle approval<br>3. Fill in approval comments<br>4. Complete approval | 1. Principle approval process executes normally<br>2. Request status correctly updated<br>3. Approval records completely saved | □ Pass<br>□ Fail | |
| TC-033 | Verify Company Director Final Approval Function | Functional Test | High | 1. User is logged in with purchase.approve permissions<br>2. Request pending final approval exists<br>3. Enter purchase request page | 1. View request details<br>2. Perform final approval<br>3. Fill in approval comments<br>4. Complete approval | 1. Final approval process executes normally<br>2. Request status correctly updated<br>3. Approval records completely saved | □ Pass<br>□ Fail | |
| TC-034 | Verify Purchase Request Rejection Function | Functional Test | Medium | 1. User is logged in with purchase.reject permissions<br>2. Request pending approval exists<br>3. Enter purchase request page | 1. View request details<br>2. Perform rejection operation<br>3. Fill in rejection reason<br>4. Complete rejection | 1. Rejection process executes normally<br>2. Request status updated to "Rejected"<br>3. Rejection records completely saved | □ Pass<br>□ Fail | |
| TC-035 | Verify Purchase Request Query and Filter Function | Functional Test | Medium | 1. User is logged in<br>2. Multiple requests exist<br>3. Enter purchase request page | 1. Use status filter<br>2. Use time range filter<br>3. Use requester filter<br>4. View filter results | 1. Filter conditions correctly applied<br>2. Search results accurate<br>3. Filter result count correct | □ Pass<br>□ Fail | |
| TC-036 | Verify Purchase Request Statistics Card Function | Functional Test | Medium | 1. User is logged in<br>2. Requests with different statuses exist<br>3. Enter purchase request page | 1. View statistics cards<br>2. Click different statistics cards<br>3. Verify list switching | 1. Statistics count accurate<br>2. Click switching normal<br>3. List displays corresponding status | □ Pass<br>□ Fail | |
| TC-037 | Verify Purchase Request Approval Permission Control | Exception Test | Medium | 1. User is logged in but with insufficient permissions<br>2. Try to approve request | 1. View request details<br>2. Try to perform approval operation | 1. Display insufficient permission message<br>2. Cannot execute approval operation<br>3. System security protection effective | □ Pass<br>□ Fail | |
| TC-038 | Verify Purchase Request Status Flow | End-to-End Test | High | 1. Complete purchase request exists<br>2. Approval personnel logged in with corresponding permissions<br>3. Enter purchase request page | 1. Department manager performs review<br>2. Item administrator performs principle approval<br>3. Company director performs final approval | 1. Status flow normal<br>2. All stages records complete<br>3. Final status correct | □ Pass<br>□ Fail | |

## Test Data Requirements
- Test user accounts: dept_manager, item_admin, company_director permission users
- Test items: At least 5 items from different categories
- Test requests: Purchase requests with different statuses
- Test shopping cart: Shopping cart with multiple items

## Test Environment Requirements
- System running normally
- Database connection normal
- Purchase request data initialized
- Approval process configuration complete
