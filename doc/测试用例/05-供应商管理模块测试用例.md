# 供应商管理模块测试用例

## 测试概述
本测试用例覆盖管理端供应商管理模块的核心功能，包括供应商信息管理、供应商评估、价格管理等。

## 测试用例表格

| 测试编号 | 测试标题 | 测试类型 | 优先级 | 前置条件 | 测试步骤 | 预期结果 | 测试结果 | 备注 |
|---------|---------|---------|--------|----------|----------|----------|----------|------|
| TC-052 | 验证供应商创建功能 | 功能点测试 | 高 | 1. 用户已登录且具有supplier.create权限<br>2. 进入供应商管理页面 | 1. 点击"新增供应商"按钮<br>2. 填写供应商基本信息<br>3. 填写联系信息<br>4. 保存供应商 | 1. 供应商创建成功<br>2. 显示成功提示信息<br>3. 供应商列表更新 | □ 通过<br>□ 失败 | |
| TC-053 | 验证供应商信息编辑功能 | 功能点测试 | 高 | 1. 用户已登录且具有supplier.update权限<br>2. 存在可编辑的供应商<br>3. 进入供应商管理页面 | 1. 选择目标供应商<br>2. 点击编辑按钮<br>3. 修改供应商信息<br>4. 保存更改 | 1. 供应商信息更新成功<br>2. 显示成功提示信息<br>3. 供应商列表显示更新后的信息 | □ 通过<br>□ 失败 | |
| TC-054 | 验证供应商信息查看功能 | 功能点测试 | 中 | 1. 用户已登录且具有supplier.read权限<br>2. 存在供应商数据<br>3. 进入供应商管理页面 | 1. 查看供应商列表<br>2. 查看供应商详情<br>3. 查看供应商历史记录 | 1. 供应商信息显示正确<br>2. 联系信息完整<br>3. 历史记录完整 | □ 通过<br>□ 失败 | |
| TC-055 | 验证供应商删除功能 | 功能点测试 | 中 | 1. 用户已登录且具有supplier.delete权限<br>2. 存在可删除的供应商<br>3. 进入供应商管理页面 | 1. 选择目标供应商<br>2. 点击删除按钮<br>3. 确认删除操作 | 1. 供应商删除成功<br>2. 显示成功提示信息<br>3. 供应商列表更新 | □ 通过<br>□ 失败 | |
| TC-056 | 验证供应商价格管理功能 | 功能点测试 | 高 | 1. 用户已登录且具有supplier.price_manage权限<br>2. 存在供应商和物品<br>3. 进入供应商管理页面 | 1. 选择目标供应商<br>2. 进入价格管理<br>3. 设置物品价格<br>4. 保存价格配置 | 1. 价格配置成功<br>2. 价格信息正确保存<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-057 | 验证供应商评估功能 | 功能点测试 | 中 | 1. 用户已登录且具有supplier.evaluate权限<br>2. 存在供应商<br>3. 进入供应商管理页面 | 1. 选择目标供应商<br>2. 进入评估页面<br>3. 填写评估指标<br>4. 提交评估结果 | 1. 评估提交成功<br>2. 评估记录保存<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-058 | 验证供应商查询筛选功能 | 功能点测试 | 中 | 1. 用户已登录<br>2. 存在多个供应商<br>3. 进入供应商管理页面 | 1. 使用名称搜索<br>2. 使用地区筛选<br>3. 使用状态筛选<br>4. 查看筛选结果 | 1. 筛选条件正确应用<br>2. 搜索结果准确<br>3. 筛选结果数量正确 | □ 通过<br>□ 失败 | |
| TC-059 | 验证供应商状态管理功能 | 功能点测试 | 中 | 1. 用户已登录且具有supplier.update权限<br>2. 存在目标供应商<br>3. 进入供应商管理页面 | 1. 选择目标供应商<br>2. 修改供应商状态<br>3. 保存更改 | 1. 状态修改成功<br>2. 供应商状态立即生效<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-060 | 验证供应商联系人管理功能 | 功能点测试 | 中 | 1. 用户已登录且具有supplier.update权限<br>2. 存在目标供应商<br>3. 进入供应商管理页面 | 1. 选择目标供应商<br>2. 进入联系人管理<br>3. 添加/编辑联系人<br>4. 保存联系人信息 | 1. 联系人信息保存成功<br>2. 联系人列表更新<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-061 | 验证供应商资质管理功能 | 功能点测试 | 中 | 1. 用户已登录且具有supplier.update权限<br>2. 存在目标供应商<br>3. 进入供应商管理页面 | 1. 选择目标供应商<br>2. 进入资质管理<br>3. 上传资质文件<br>4. 设置有效期<br>5. 保存资质信息 | 1. 资质信息保存成功<br>2. 文件上传正常<br>3. 有效期设置正确 | □ 通过<br>□ 失败 | |
| TC-062 | 验证供应商重复创建检查 | 异常情况测试 | 中 | 1. 用户已登录且具有supplier.create权限<br>2. 存在同名供应商<br>3. 进入供应商创建页面 | 1. 填写与现有供应商相同的名称<br>2. 填写其他信息<br>3. 点击保存 | 1. 显示重复供应商提示<br>2. 阻止重复创建<br>3. 提示用户检查现有供应商 | □ 通过<br>□ 失败 | |
| TC-063 | 验证供应商权限控制 | 异常情况测试 | 中 | 1. 用户已登录但权限不足<br>2. 尝试执行供应商操作 | 1. 查看供应商信息<br>2. 尝试编辑供应商<br>3. 尝试删除供应商 | 1. 显示权限不足提示<br>2. 无法执行相关操作<br>3. 系统安全防护生效 | □ 通过<br>□ 失败 | |
| TC-064 | 验证供应商完整流程 | 端到端测试 | 高 | 1. 用户具有完整权限<br>2. 进入供应商管理页面 | 1. 创建新供应商<br>2. 配置供应商价格<br>3. 进行供应商评估<br>4. 查看最终结果 | 1. 整个流程正常执行<br>2. 数据保存正确<br>3. 功能完整可用 | □ 通过<br>□ 失败 | |

## 测试数据要求
- 测试用户账号：item_admin权限用户
- 测试供应商：至少包含5个不同状态的供应商
- 测试物品：至少包含10个不同分类的物品
- 测试价格：包含不同供应商的物品价格配置

## 测试环境要求
- 系统正常运行
- 数据库连接正常
- 供应商数据已初始化
- 文件上传功能正常
