# 物品管理模块测试用例

## 测试概述
本测试用例覆盖管理端物品管理模块的核心功能，包括物品分类管理、物品信息管理、物品编码管理等。

## 测试用例表格

| 测试编号 | 测试标题 | 测试类型 | 优先级 | 前置条件 | 测试步骤 | 预期结果 | 测试结果 | 备注 |
|---------|---------|---------|--------|----------|----------|----------|----------|------|
| TC-011 | 验证物品分类创建功能 | 功能点测试 | 高 | 1. 用户已登录且具有item_admin权限<br>2. 进入物品分类管理页面 | 1. 点击"新增分类"按钮<br>2. 填写分类名称和描述<br>3. 选择上级分类<br>4. 点击保存 | 1. 分类创建成功<br>2. 显示成功提示信息<br>3. 分类列表更新 | □ 通过<br>□ 失败 | |
| TC-012 | 验证物品分类编辑功能 | 功能点测试 | 中 | 1. 用户已登录且具有item_admin权限<br>2. 存在可编辑的分类<br>3. 进入物品分类管理页面 | 1. 选择目标分类<br>2. 点击编辑按钮<br>3. 修改分类信息<br>4. 保存更改 | 1. 分类信息更新成功<br>2. 显示成功提示信息<br>3. 分类列表显示更新后的信息 | □ 通过<br>□ 失败 | |
| TC-013 | 验证物品分类删除功能 | 功能点测试 | 中 | 1. 用户已登录且具有item_admin权限<br>2. 存在可删除的分类<br>3. 进入物品分类管理页面 | 1. 选择目标分类<br>2. 点击删除按钮<br>3. 确认删除操作 | 1. 分类删除成功<br>2. 显示成功提示信息<br>3. 分类列表更新 | □ 通过<br>□ 失败 | |
| TC-014 | 验证物品创建功能 | 功能点测试 | 高 | 1. 用户已登录且具有item_admin权限<br>2. 物品分类已配置<br>3. 进入物品管理页面 | 1. 点击"新增物品"按钮<br>2. 填写物品基本信息<br>3. 选择物品分类<br>4. 上传物品图片<br>5. 点击保存 | 1. 物品创建成功<br>2. 自动生成唯一编码<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-015 | 验证物品编码自动生成功能 | 功能点测试 | 高 | 1. 用户已登录且具有item_admin权限<br>2. 物品分类已配置编码规则<br>3. 进入物品创建页面 | 1. 选择物品分类<br>2. 填写物品名称<br>3. 系统自动生成编码 | 1. 编码格式正确<br>2. 编码唯一性验证通过<br>3. 编码不可编辑 | □ 通过<br>□ 失败 | |
| TC-016 | 验证物品信息编辑功能 | 功能点测试 | 高 | 1. 用户已登录且具有item_admin权限<br>2. 存在可编辑的物品<br>3. 进入物品管理页面 | 1. 选择目标物品<br>2. 点击编辑按钮<br>3. 修改物品信息<br>4. 保存更改 | 1. 物品信息更新成功<br>2. 编码保持不变<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-017 | 验证物品状态管理功能 | 功能点测试 | 中 | 1. 用户已登录且具有item_admin权限<br>2. 存在目标物品<br>3. 进入物品管理页面 | 1. 选择目标物品<br>2. 修改物品状态（启用/禁用）<br>3. 保存更改 | 1. 状态修改成功<br>2. 物品状态立即生效<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-018 | 验证物品查询筛选功能 | 功能点测试 | 中 | 1. 用户已登录<br>2. 存在多个物品<br>3. 进入物品管理页面 | 1. 使用分类筛选<br>2. 使用名称搜索<br>3. 使用状态筛选<br>4. 查看筛选结果 | 1. 筛选条件正确应用<br>2. 搜索结果准确<br>3. 筛选结果数量正确 | □ 通过<br>□ 失败 | |
| TC-019 | 验证物品属性配置功能 | 功能点测试 | 中 | 1. 用户已登录且具有item_admin权限<br>2. 物品分类已配置<br>3. 进入物品属性配置页面 | 1. 选择物品分类<br>2. 配置属性名称和类型<br>3. 设置属性选项<br>4. 保存配置 | 1. 属性配置成功<br>2. 配置信息正确保存<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-020 | 验证物品删除功能 | 功能点测试 | 中 | 1. 用户已登录且具有item_admin权限<br>2. 存在可删除的物品<br>3. 进入物品管理页面 | 1. 选择目标物品<br>2. 点击删除按钮<br>3. 确认删除操作 | 1. 物品删除成功<br>2. 显示成功提示信息<br>3. 物品列表更新 | □ 通过<br>□ 失败 | |
| TC-021 | 验证物品重复创建检查 | 异常情况测试 | 中 | 1. 用户已登录且具有item_admin权限<br>2. 存在同名物品<br>3. 进入物品创建页面 | 1. 填写与现有物品相同的名称<br>2. 选择相同的分类<br>3. 点击保存 | 1. 显示重复物品提示<br>2. 阻止重复创建<br>3. 提示用户检查现有物品 | □ 通过<br>□ 失败 | |
| TC-022 | 验证物品分类层级约束 | 异常情况测试 | 中 | 1. 用户已登录且具有item_admin权限<br>2. 进入物品分类管理页面 | 1. 尝试创建重复的二级分类名称<br>2. 在同一一级分类下创建同名二级分类 | 1. 显示分类名称重复提示<br>2. 阻止重复分类创建<br>3. 提示用户使用不同名称 | □ 通过<br>□ 失败 | |

## 测试数据要求
- 测试用户账号：item_admin权限用户
- 测试物品分类：至少包含2个一级分类和4个二级分类
- 测试物品：至少包含10个不同分类的物品
- 测试图片：支持上传的图片文件

## 测试环境要求
- 系统正常运行
- 数据库连接正常
- 物品分类数据已初始化
- 文件上传功能正常
