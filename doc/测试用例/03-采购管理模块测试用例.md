# 采购管理模块测试用例

## 测试概述
本测试用例覆盖管理端采购管理模块的核心功能，包括采购申请创建、购物车管理、采购审批流程等。

## 测试用例表格

| 测试编号 | 测试标题 | 测试类型 | 优先级 | 前置条件 | 测试步骤 | 预期结果 | 测试结果 | 备注 |
|---------|---------|---------|--------|----------|----------|----------|----------|------|
| TC-023 | 验证购物车添加物品功能 | 功能点测试 | 高 | 1. 用户已登录且具有cart.add_item权限<br>2. 物品信息已存在<br>3. 进入购物车页面 | 1. 点击"添加物品"按钮<br>2. 选择物品和数量<br>3. 填写需求说明<br>4. 添加到购物车 | 1. 物品成功添加到购物车<br>2. 购物车数量更新<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-024 | 验证购物车编辑物品功能 | 功能点测试 | 中 | 1. 用户已登录且具有cart.update_item权限<br>2. 购物车中已存在物品<br>3. 进入购物车页面 | 1. 选择购物车中的物品<br>2. 点击编辑按钮<br>3. 修改数量和说明<br>4. 保存更改 | 1. 物品信息更新成功<br>2. 购物车显示更新后的信息<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-025 | 验证购物车删除物品功能 | 功能点测试 | 中 | 1. 用户已登录且具有cart.remove_item权限<br>2. 购物车中已存在物品<br>3. 进入购物车页面 | 1. 选择购物车中的物品<br>2. 点击删除按钮<br>3. 确认删除操作 | 1. 物品成功从购物车删除<br>2. 购物车数量更新<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-026 | 验证购物车提交申请功能 | 功能点测试 | 高 | 1. 用户已登录且具有cart.submit权限<br>2. 购物车中已存在物品<br>3. 进入购物车页面 | 1. 检查购物车物品<br>2. 点击"提交申请"按钮<br>3. 确认提交信息<br>4. 完成提交 | 1. 采购申请创建成功<br>2. 购物车清空<br>3. 申请进入审批流程 | □ 通过<br>□ 失败 | |
| TC-027 | 验证采购申请创建功能 | 功能点测试 | 高 | 1. 用户已登录且具有purchase.request权限<br>2. 物品信息已存在<br>3. 进入采购申请页面 | 1. 点击"新建申请"按钮<br>2. 选择物品和数量<br>3. 填写申请理由<br>4. 提交申请 | 1. 申请创建成功<br>2. 状态为"待审批"<br>3. 生成申请编号 | □ 通过<br>□ 失败 | |
| TC-028 | 验证采购申请编辑功能 | 功能点测试 | 中 | 1. 用户已登录且具有purchase.update权限<br>2. 存在可编辑的申请<br>3. 进入采购申请页面 | 1. 选择目标申请<br>2. 点击编辑按钮<br>3. 修改申请信息<br>4. 保存更改 | 1. 申请信息更新成功<br>2. 显示成功提示信息<br>3. 申请状态保持不变 | □ 通过<br>□ 失败 | |
| TC-029 | 验证采购申请删除功能 | 功能点测试 | 中 | 1. 用户已登录且具有purchase.delete权限<br>2. 存在可删除的申请<br>3. 进入采购申请页面 | 1. 选择目标申请<br>2. 点击删除按钮<br>3. 确认删除操作 | 1. 申请删除成功<br>2. 显示成功提示信息<br>3. 申请列表更新 | □ 通过<br>□ 失败 | |
| TC-030 | 验证采购申请撤回功能 | 功能点测试 | 中 | 1. 用户已登录且具有purchase.withdraw权限<br>2. 存在可撤回的申请<br>3. 进入采购申请页面 | 1. 选择目标申请<br>2. 点击撤回按钮<br>3. 确认撤回操作 | 1. 申请撤回成功<br>2. 状态更新为"已撤回"<br>3. 显示成功提示信息 | □ 通过<br>□ 失败 | |
| TC-031 | 验证部门经理复核功能 | 功能点测试 | 高 | 1. 用户已登录且具有purchase.review权限<br>2. 存在待复核的申请<br>3. 进入采购申请页面 | 1. 查看申请详情<br>2. 进行复核操作<br>3. 填写复核意见<br>4. 完成复核 | 1. 复核流程正常执行<br>2. 申请状态正确更新<br>3. 复核记录完整保存 | □ 通过<br>□ 失败 | |
| TC-032 | 验证物品管理员原则审批功能 | 功能点测试 | 高 | 1. 用户已登录且具有purchase.principle_approve权限<br>2. 存在待原则审批的申请<br>3. 进入采购申请页面 | 1. 查看申请详情<br>2. 进行原则审批<br>3. 填写审批意见<br>4. 完成审批 | 1. 原则审批流程正常执行<br>2. 申请状态正确更新<br>3. 审批记录完整保存 | □ 通过<br>□ 失败 | |
| TC-033 | 验证公司主管最终审批功能 | 功能点测试 | 高 | 1. 用户已登录且具有purchase.approve权限<br>2. 存在待最终审批的申请<br>3. 进入采购申请页面 | 1. 查看申请详情<br>2. 进行最终审批<br>3. 填写审批意见<br>4. 完成审批 | 1. 最终审批流程正常执行<br>2. 申请状态正确更新<br>3. 审批记录完整保存 | □ 通过<br>□ 失败 | |
| TC-034 | 验证采购申请拒绝功能 | 功能点测试 | 中 | 1. 用户已登录且具有purchase.reject权限<br>2. 存在待审批的申请<br>3. 进入采购申请页面 | 1. 查看申请详情<br>2. 进行拒绝操作<br>3. 填写拒绝理由<br>4. 完成拒绝 | 1. 拒绝流程正常执行<br>2. 申请状态更新为"已拒绝"<br>3. 拒绝记录完整保存 | □ 通过<br>□ 失败 | |
| TC-035 | 验证采购申请查询筛选功能 | 功能点测试 | 中 | 1. 用户已登录<br>2. 存在多个申请<br>3. 进入采购申请页面 | 1. 使用状态筛选<br>2. 使用时间范围筛选<br>3. 使用申请人筛选<br>4. 查看筛选结果 | 1. 筛选条件正确应用<br>2. 搜索结果准确<br>3. 筛选结果数量正确 | □ 通过<br>□ 失败 | |
| TC-036 | 验证采购申请统计卡片功能 | 功能点测试 | 中 | 1. 用户已登录<br>2. 存在不同状态的申请<br>3. 进入采购申请页面 | 1. 查看统计卡片<br>2. 点击不同统计卡片<br>3. 验证列表切换 | 1. 统计数量准确<br>2. 点击切换正常<br>3. 列表显示对应状态 | □ 通过<br>□ 失败 | |
| TC-037 | 验证采购申请审批权限控制 | 异常情况测试 | 中 | 1. 用户已登录但权限不足<br>2. 尝试审批申请 | 1. 查看申请详情<br>2. 尝试进行审批操作 | 1. 显示权限不足提示<br>2. 无法执行审批操作<br>3. 系统安全防护生效 | □ 通过<br>□ 失败 | |
| TC-038 | 验证采购申请状态流转 | 端到端测试 | 高 | 1. 存在完整的采购申请<br>2. 各审批人员已登录<br>3. 进入采购申请页面 | 1. 部门经理进行复核<br>2. 物品管理员进行原则审批<br>3. 公司主管进行最终审批 | 1. 状态流转正常<br>2. 各环节记录完整<br>3. 最终状态正确 | □ 通过<br>□ 失败 | |

## 测试数据要求
- 测试用户账号：dept_manager, item_admin, company_director权限用户
- 测试物品：至少包含5个不同分类的物品
- 测试申请：包含不同状态的采购申请
- 测试购物车：包含多个物品的购物车

## 测试环境要求
- 系统正常运行
- 数据库连接正常
- 采购申请数据已初始化
- 审批流程配置完整
