# End-to-End Process Test Cases

## Test Overview
This test case covers the complete business processes of the admin-side system, from user login to completion of various business operations.

## Test Case Table

| Test ID | Test Title | Test Type | Priority | Prerequisites | Test Steps | Expected Results | Test Results | Notes |
|---------|------------|-----------|----------|---------------|------------|------------------|--------------|-------|
| TC-093 | Complete Purchase Approval Process Test | End-to-End Test | High | 1. System running normally<br>2. All role users configured<br>3. Complete item and supplier data | 1. dept_item_admin logs into system<br>2. Add items to shopping cart<br>3. Submit purchase request<br>4. dept_manager performs review<br>5. item_admin performs principle approval<br>6. company_director performs final approval<br>7. Verify request status and records | 1. Entire process executes normally<br>2. Permission control correct at all stages<br>3. Status flow normal<br>4. Records completely saved | □ Pass<br>□ Fail | |
| TC-094 | Complete Item Management Process Test | End-to-End Test | High | 1. System running normally<br>2. item_admin user configured<br>3. Complete basic data | 1. item_admin logs into system<br>2. Create item categories<br>3. Create new items<br>4. Configure item properties<br>5. Upload item images<br>6. Verify item information<br>7. Edit item information<br>8. Manage item status | 1. Entire process executes normally<br>2. Item codes auto-generated<br>3. Image upload normal<br>4. Information management complete | □ Pass<br>□ Fail | |
| TC-095 | Complete Inventory Management Process Test | End-to-End Test | High | 1. System running normally<br>2. warehouse_keeper user configured<br>3. Complete inventory data | 1. warehouse_keeper logs into system<br>2. View inventory information<br>3. Execute inventory adjustment<br>4. Execute inventory transfer<br>5. Execute inventory counting<br>6. View operation history<br>7. Generate inventory reports | 1. Entire process executes normally<br>2. Inventory data accurate<br>3. Operation records complete<br>4. Report generation correct | □ Pass<br>□ Fail | |
| TC-096 | Complete Supplier Management Process Test | End-to-End Test | High | 1. System running normally<br>2. item_admin user configured<br>3. Complete basic data | 1. item_admin logs into system<br>2. Create new supplier<br>3. Configure supplier prices<br>4. Perform supplier evaluation<br>5. Manage supplier qualifications<br>6. View supplier reports<br>7. Verify supplier information | 1. Entire process executes normally<br>2. Supplier information complete<br>3. Price configuration correct<br>4. Evaluation records saved | □ Pass<br>□ Fail | |
| TC-097 | Complete User Permission Management Process Test | End-to-End Test | High | 1. System running normally<br>2. system_admin user configured<br>3. Complete basic data | 1. system_admin logs into system<br>2. Create new department<br>3. Create new role<br>4. Configure role permissions<br>5. Create new user<br>6. Assign user role<br>7. Verify permissions effective<br>8. Test permission control | 1. Entire process executes normally<br>2. Permission configuration correct<br>3. Permission control effective<br>4. User management complete | □ Pass<br>□ Fail | |
| TC-098 | Complete Report System Process Test | End-to-End Test | Medium | 1. System running normally<br>2. All role users configured<br>3. Complete business data | 1. Different role users log in<br>2. Access reports with corresponding permissions<br>3. Set report parameters<br>4. Generate various reports<br>5. Export report files<br>6. Verify report data<br>7. Test report permission control | 1. Entire process executes normally<br>2. Report generation correct<br>3. Permission control effective<br>4. Export function normal | □ Pass<br>□ Fail | |
| TC-099 | System Performance Stress Test | Performance Test | Medium | 1. System running normally<br>2. Sufficient test data<br>3. Stable test environment | 1. Simulate multi-user concurrent login<br>2. Execute large data queries<br>3. Execute large data operations<br>4. Monitor system performance<br>5. Analyze performance bottlenecks<br>6. Verify system stability | 1. System response time normal<br>2. Concurrent processing capacity meets requirements<br>3. System runs stably<br>4. No performance anomalies | □ Pass<br>□ Fail | |
| TC-100 | System Security Test | Security Test | High | 1. System running normally<br>2. Complete security configuration<br>3. Isolated test environment | 1. Test unauthorized access<br>2. Test permission bypass<br>3. Test SQL injection protection<br>4. Test XSS protection<br>5. Test CSRF protection<br>6. Verify security log recording | 1. Security protection effective<br>2. Unauthorized access blocked<br>3. Security logs completely recorded<br>4. System secure and reliable | □ Pass<br>□ Fail | |

## Test Data Requirements
- Test user accounts: Complete configuration for all role users
- Test data: Complete business data (items, inventory, purchases, suppliers, etc.)
- Test environment: Stable test environment supporting concurrent testing
- Test tools: Performance monitoring tools, security testing tools

## Test Environment Requirements
- System running normally
- Database connection normal
- All function modules available
- Complete test data
- Stable network environment
