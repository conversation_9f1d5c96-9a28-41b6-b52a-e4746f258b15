# Item Management Module Test Cases

## Test Overview
This test case covers the core functionality of the admin-side item management module, including item category management, item information management, item code management, etc.

## Test Case Table

| Test ID | Test Title | Test Type | Priority | Prerequisites | Test Steps | Expected Results | Test Results | Notes |
|---------|------------|-----------|----------|---------------|------------|------------------|--------------|-------|
| TC-011 | Verify Item Category Creation Function | Functional Test | High | 1. User is logged in with item_admin permissions<br>2. Enter item category management page | 1. Click "Add Category" button<br>2. Fill in category name and description<br>3. Select parent category<br>4. Click save | 1. Category creation successful<br>2. Display success message<br>3. Category list updated | □ Pass<br>□ Fail | |
| TC-012 | Verify Item Category Edit Function | Functional Test | Medium | 1. User is logged in with item_admin permissions<br>2. Editable category exists<br>3. Enter item category management page | 1. Select target category<br>2. Click edit button<br>3. Modify category information<br>4. Save changes | 1. Category information updated successfully<br>2. Display success message<br>3. Category list shows updated information | □ Pass<br>□ Fail | |
| TC-013 | Verify Item Category Deletion Function | Functional Test | Medium | 1. User is logged in with item_admin permissions<br>2. Deletable category exists<br>3. Enter item category management page | 1. Select target category<br>2. Click delete button<br>3. Confirm deletion operation | 1. Category deletion successful<br>2. Display success message<br>3. Category list updated | □ Pass<br>□ Fail | |
| TC-014 | Verify Item Creation Function | Functional Test | High | 1. User is logged in with item_admin permissions<br>2. Item categories configured<br>3. Enter item management page | 1. Click "Add Item" button<br>2. Fill in basic item information<br>3. Select item category<br>4. Upload item image<br>5. Click save | 1. Item creation successful<br>2. Unique code automatically generated<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-015 | Verify Item Code Auto-Generation Function | Functional Test | High | 1. User is logged in with item_admin permissions<br>2. Item category has configured code rules<br>3. Enter item creation page | 1. Select item category<br>2. Fill in item name<br>3. System auto-generates code | 1. Code format correct<br>2. Code uniqueness validation passed<br>3. Code not editable | □ Pass<br>□ Fail | |
| TC-016 | Verify Item Information Edit Function | Functional Test | High | 1. User is logged in with item_admin permissions<br>2. Editable item exists<br>3. Enter item management page | 1. Select target item<br>2. Click edit button<br>3. Modify item information<br>4. Save changes | 1. Item information updated successfully<br>2. Code remains unchanged<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-017 | Verify Item Status Management Function | Functional Test | Medium | 1. User is logged in with item_admin permissions<br>2. Target item exists<br>3. Enter item management page | 1. Select target item<br>2. Modify item status (Enable/Disable)<br>3. Save changes | 1. Status modification successful<br>2. Item status takes effect immediately<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-018 | Verify Item Query and Filter Function | Functional Test | Medium | 1. User is logged in<br>2. Multiple items exist<br>3. Enter item management page | 1. Use category filter<br>2. Use name search<br>3. Use status filter<br>4. View filter results | 1. Filter conditions correctly applied<br>2. Search results accurate<br>3. Filter result count correct | □ Pass<br>□ Fail | |
| TC-019 | Verify Item Property Configuration Function | Functional Test | Medium | 1. User is logged in with item_admin permissions<br>2. Item categories configured<br>3. Enter item property configuration page | 1. Select item category<br>2. Configure property name and type<br>3. Set property options<br>4. Save configuration | 1. Property configuration successful<br>2. Configuration information correctly saved<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-020 | Verify Item Deletion Function | Functional Test | Medium | 1. User is logged in with item_admin permissions<br>2. Deletable item exists<br>3. Enter item management page | 1. Select target item<br>2. Click delete button<br>3. Confirm deletion operation | 1. Item deletion successful<br>2. Display success message<br>3. Item list updated | □ Pass<br>□ Fail | |
| TC-021 | Verify Item Duplicate Creation Check | Exception Test | Medium | 1. User is logged in with item_admin permissions<br>2. Item with same name exists<br>3. Enter item creation page | 1. Fill in same name as existing item<br>2. Select same category<br>3. Click save | 1. Display duplicate item message<br>2. Prevent duplicate creation<br>3. Prompt user to check existing items | □ Pass<br>□ Fail | |
| TC-022 | Verify Item Category Hierarchy Constraints | Exception Test | Medium | 1. User is logged in with item_admin permissions<br>2. Enter item category management page | 1. Try to create duplicate secondary category name<br>2. Create same name secondary category under same primary category | 1. Display category name duplicate message<br>2. Prevent duplicate category creation<br>3. Prompt user to use different name | □ Pass<br>□ Fail | |

## Test Data Requirements
- Test user accounts: item_admin permission users
- Test item categories: At least 2 primary categories and 4 secondary categories
- Test items: At least 10 items from different categories
- Test images: Supported image files for upload

## Test Environment Requirements
- System running normally
- Database connection normal
- Item category data initialized
- File upload function normal
