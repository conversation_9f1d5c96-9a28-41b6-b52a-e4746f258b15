# User Permission Management Module Test Cases

## Test Overview
This test case covers the core functionality of the admin-side user permission management module, including user management, role management, permission assignment, etc.

## Test Case Table

| Test ID | Test Title | Test Type | Priority | Prerequisites | Test Steps | Expected Results | Test Results | Notes |
|---------|------------|-----------|----------|---------------|------------|------------------|--------------|-------|
| TC-001 | Verify User Login Function | Functional Test | High | 1. System is running<br>2. Test user account exists<br>3. User is not logged in | 1. Access login page<br>2. Enter correct username and password<br>3. Click login button | 1. Login successful<br>2. Redirect to main page<br>3. Display user information | □ Pass<br>□ Fail | |
| TC-002 | Verify User Login Failure Handling | Exception Test | Medium | 1. System is running<br>2. User is not logged in | 1. Access login page<br>2. Enter incorrect username or password<br>3. Click login button | 1. Display login failure message<br>2. Stay on login page<br>3. Clear password input field | □ Pass<br>□ Fail | |
| TC-003 | Verify User Permission Validation | Functional Test | High | 1. User is logged in<br>2. User has specific permissions | 1. Access function page requiring permissions<br>2. Execute operations requiring permissions | 1. Function accessible normally<br>2. Operations execute normally<br>3. Permission control effective | □ Pass<br>□ Fail | |
| TC-004 | Verify Access Control for Insufficient Permissions | Exception Test | Medium | 1. User is logged in<br>2. User has insufficient permissions | 1. Try to access functions beyond permissions<br>2. Try to execute operations beyond permissions | 1. Display insufficient permission message<br>2. Cannot execute related operations<br>3. System security protection effective | □ Pass<br>□ Fail | |
| TC-005 | Verify User Role Assignment Function | Functional Test | High | 1. Administrator is logged in<br>2. Assignable roles exist<br>3. Target user exists | 1. Enter user management page<br>2. Select target user<br>3. Assign new role<br>4. Save role assignment | 1. Role assignment successful<br>2. User permissions take effect immediately<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-006 | Verify User Role Revocation Function | Functional Test | High | 1. Administrator is logged in<br>2. User has assigned role<br>3. Enter user management page | 1. Select target user<br>2. Revoke existing role<br>3. Save changes | 1. Role revocation successful<br>2. User permissions invalidated immediately<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-007 | Verify User Status Management Function | Functional Test | Medium | 1. Administrator is logged in<br>2. Target user exists<br>3. Enter user management page | 1. Select target user<br>2. Modify user status (Enable/Disable)<br>3. Save changes | 1. Status modification successful<br>2. User status takes effect immediately<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-008 | Verify User Password Reset Function | Functional Test | Medium | 1. Administrator is logged in<br>2. Target user exists<br>3. Enter user management page | 1. Select target user<br>2. Execute password reset operation<br>3. Confirm reset | 1. Password reset successful<br>2. Temporary password generated<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-009 | Verify Role Permission Configuration Function | Functional Test | High | 1. Administrator is logged in<br>2. Target role exists<br>3. Enter role management page | 1. Select target role<br>2. Configure role permissions<br>3. Save permission configuration | 1. Permission configuration successful<br>2. Role permissions take effect immediately<br>3. Display success message | □ Pass<br>□ Fail | |
| TC-010 | Verify User Logout Function | Functional Test | Medium | 1. User is logged in<br>2. On main page | 1. Click user avatar or menu<br>2. Select logout option<br>3. Confirm logout | 1. Logout successful<br>2. Redirect to login page<br>3. Clear user session information | □ Pass<br>□ Fail | |

## Test Data Requirements
- Test user accounts: admin/admin123 (super administrator)
- Test roles: company_director, item_admin, dept_manager, dept_item_admin
- Test permissions: Various function module permission points

## Test Environment Requirements
- System running normally
- Database connection normal
- User permission data initialized
