# 物品管理模块需求文档

## 1. 需求概述

### 业务背景
物品管理模块是企业资源管理系统的核心模块，负责管理企业内部的所有物品信息，包括物品的基本信息、分类体系、编码规则、供应商关系等。

### 核心目标
- 建立统一的物品分类体系和编码规则
- 确保物品信息的准确性和唯一性
- 支持物品的全生命周期管理
- 提供灵活的物品查询和筛选功能

### 系统定位
作为ERP系统的基础数据模块，为采购、库存、财务等其他模块提供准确的物品主数据。

## 2. 功能需求

### 2.1 物品分类管理
- **一级分类管理**：定义物品的顶级分类，设置编码前缀和格式
- **二级分类管理**：在一级分类下创建具体的业务分类
- **分类层级约束**：同一一级分类下，二级分类名称必须唯一

### 2.2 物品信息管理
- **物品创建**：支持创建新物品，自动生成唯一编码
- **物品编辑**：支持修改物品信息（编码不可修改）
- **物品查询**：支持多条件查询和筛选
- **物品状态管理**：支持启用/禁用、可购买/不可购买状态

### 2.3 物品编码管理
- **自动编码生成**：基于分类配置自动生成唯一编码
- **编码格式**：[分类前缀][序号]，如 HA0001, BG0001
- **编码唯一性**：系统内全局唯一，不可重复

### 2.4 物品属性管理
- **固定属性**：品牌、规格材质、尺寸规格
- **动态配置**：支持按分类配置属性的输入方式（文本/选择）
- **属性值约束**：选择类型支持预定义选项

## 3. 业务对象设计

```mermaid
erDiagram
    ItemPrimaryCategory ||--o{ ItemCategory : "包含"
    ItemCategory ||--o{ Item : "分类"
    ItemCategory ||--o{ ItemPropertyConfig : "配置"
    Item ||--o{ ItemSupplier : "供应"
    Item ||--o{ ItemChangeHistory : "历史"
    
    ItemPrimaryCategory {
        int id PK
        string name UK "分类名称"
        string code_prefix "编码前缀"
        string code_format "编码格式"
        int current_sequence "当前序号"
        boolean is_active "是否启用"
        datetime created_at
        datetime updated_at
    }
    
    ItemCategory {
        int id PK
        string name "分类名称"
        int primary_category_id FK
        text description "描述"
        boolean is_active "是否启用"
        datetime created_at
        datetime updated_at
    }
    
    Item {
        int id PK
        string name "物品名称"
        string code UK "物品编码"
        int category_id FK
        text description "描述"
        string image_url "图片URL"
        string unit "计量单位"
        int qty_per_up "包装数量"
        string brand "品牌"
        string spec_material "规格材质"
        string size_dimension "尺寸规格"
        boolean is_purchasable "可购买"
        boolean is_active "是否启用"
        
        datetime created_at
        datetime updated_at
    }
    
    ItemPropertyConfig {
        int id PK
        int category_id FK
        string attribute_name "属性名称"
        string input_type "输入类型"
        text options "选项配置"
        datetime created_at
        datetime updated_at
    }
```

## 4. 数据库表设计

### 4.1 物品一级分类表 (item_primary_categories)
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY | 主键 |
| name | VARCHAR(100) | NOT NULL, UNIQUE | 分类名称 |
| code_prefix | VARCHAR(10) | NOT NULL | 编码前缀 |
| code_format | VARCHAR(20) | DEFAULT '0000' | 编码格式 |
| current_sequence | INTEGER | DEFAULT 1 | 当前序号 |
| is_active | BOOLEAN | DEFAULT TRUE | 是否启用 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | ON UPDATE NOW() | 更新时间 |

### 4.2 物品二级分类表 (item_categories)
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY | 主键 |
| name | VARCHAR(100) | NOT NULL | 分类名称 |
| primary_category_id | INTEGER | NOT NULL | 一级分类ID |
| description | TEXT | | 描述 |
| is_active | BOOLEAN | DEFAULT TRUE | 是否启用 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | ON UPDATE NOW() | 更新时间 |

**索引**：
- `idx_item_categories_primary_name` (primary_category_id, name) UNIQUE

### 4.3 物品表 (items)
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY | 主键 |
| name | VARCHAR(200) | NOT NULL | 物品名称 |
| code | VARCHAR(50) | UNIQUE | 物品编码 |
| category_id | INTEGER | NOT NULL | 分类ID |
| description | TEXT | | 描述 |
| image_url | VARCHAR(500) | | 图片URL |
| unit | VARCHAR(20) | DEFAULT '个' | 计量单位 |
| qty_per_up | INTEGER | DEFAULT 1 | 包装数量 |
| brand | VARCHAR(200) | | 品牌 |
| spec_material | VARCHAR(500) | | 规格材质 |
| size_dimension | VARCHAR(200) | | 尺寸规格 |
| is_purchasable | BOOLEAN | DEFAULT TRUE | 可购买 |
| is_active | BOOLEAN | DEFAULT TRUE | 是否启用 |
| total_stock | INTEGER | DEFAULT 0 | 总库存 |

| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | ON UPDATE NOW() | 更新时间 |

**索引**：
- `idx_items_category_name_unique` (category_id, name) UNIQUE
- `idx_items_code` (code) UNIQUE
- `idx_items_name` (name)

### 4.4 物品属性配置表 (item_property_configs)
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY | 主键 |
| category_id | INTEGER | NOT NULL | 分类ID |
| attribute_name | VARCHAR(50) | NOT NULL | 属性名称 |
| input_type | VARCHAR(20) | NOT NULL | 输入类型 |
| options | TEXT | | 选项配置JSON |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | ON UPDATE NOW() | 更新时间 |

**索引**：
- `idx_item_property_configs_unique` (category_id, attribute_name) UNIQUE

## 5. 业务规则

### 5.1 唯一性约束
- **物品编码全局唯一**：整个系统内物品编码不能重复
- **同分类下物品名称唯一**：在同一二级分类下，物品名称必须唯一
- **同一级分类下二级分类名称唯一**：在同一一级分类下，二级分类名称必须唯一
- **分类属性配置唯一**：同一分类下，属性名称配置唯一

### 5.2 编码生成规则
- 物品编码由系统自动生成，用户不可手动指定
- 编码格式：[一级分类前缀][4位序号]，如 HA0001
- 序号从1开始，每创建一个物品自动递增
- 编码一旦生成不可修改

### 5.3 数据完整性规则
- 物品必须归属于某个二级分类
- 分类被物品引用时不可删除
- 删除一级分类时，其下的二级分类和物品都将被级联处理

### 5.4 状态管理规则
- 禁用的分类下不能创建新物品
- 禁用的物品不能进行采购等业务操作
- 状态变更需要记录操作日志

### 5.5 权限和安全要求
- 物品信息的创建和修改需要相应权限
- 关键操作需要记录操作日志
- 敏感信息需要适当的访问控制

### 5.6 异常处理规则
- 名称重复时返回明确的错误信息
- 编码生成失败时提供详细的错误说明
- 数据约束违反时提供用户友好的提示信息
