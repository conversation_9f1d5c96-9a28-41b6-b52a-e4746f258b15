# 采购申请价格快照逻辑完全移除说明

## 概述

本次更新完全移除了采购申请系统中的价格快照逻辑和预估价格字段，申请阶段完全使用实时价格计算，最终审批后才锁定价格到采购申请相关表中。

## 主要变更

### 1. 后端模型变更

#### PurchaseRequestItem 模型
- **移除字段**：
  - `estimated_unit_price`: 预估单价（已移除）
  - `total_price`: 预估总价（已移除）
  - `item_snapshot`: 物品快照数据（已移除）

- **保留字段**：
  - `final_unit_price`: 最终单价（审批后锁定）
  - `final_total_price`: 最终总价（审批后锁定）
  - `final_supplier_id`: 最终供应商ID
  - `price_locked_at`: 价格锁定时间

#### PurchaseRequest 模型
- 移除了 `estimated_total` 字段
- 保留了 `final_total` 字段
- 完全移除了与 `PriceSnapshot` 的关系

### 2. 服务层变更

#### PurchaseRequestService
- **移除方法**：
  - `_get_item_price()`: 预估价格获取方法
  - `get_item_real_time_price()`: 实时价格获取方法
  - `lock_final_prices()`: 价格锁定方法

- **修改方法**：
  - `create_request_from_cart_items()`: 不再计算预估总金额
  - `_fill_missing_item_info()`: 不再填充预估价格信息

### 3. API接口变更

#### 移除接口
- `GET /admin/purchase/requests/items/{item_id}/real-time-price`: 实时价格获取
- `POST /admin/purchase/requests/items/real-time-prices/batch`: 批量实时价格获取
- `POST /admin/purchase/requests/{request_id}/lock-prices`: 价格锁定

### 4. 前端变更

#### EditRequestModal 组件
- 移除了预估价格列
- 移除了实时价格显示功能
- 移除了价格刷新按钮
- 简化了表格结构

#### purchaseRequestService
- 移除了实时价格获取接口
- 移除了价格锁定接口
- 简化了 PurchaseRequestItem 接口

## 业务逻辑变更

### 申请阶段
1. **无价格存储**: 申请阶段完全不存储任何价格信息
2. **实时计算**: 每次查看申请时，前端通过其他接口获取实时价格
3. **价格展示**: 价格信息通过其他模块（如供应商管理）提供

### 审批阶段
1. **无价格锁定**: 审批过程中不涉及价格锁定
2. **最终价格**: 最终审批通过后，通过其他机制记录最终价格

### 价格管理
1. **完全分离**: 价格管理与采购申请完全分离
2. **外部依赖**: 价格信息依赖供应商管理模块
3. **实时查询**: 需要价格时实时查询，不缓存

## 数据库变更

### 字段移除
- `purchase_request_items.estimated_unit_price` - 预估单价
- `purchase_request_items.total_price` - 预估总价
- `purchase_request_items.item_snapshot` - 物品快照
- `purchase_requests.estimated_total` - 预估总金额

### 表移除
- `price_snapshots` - 价格快照表（完全移除）

### 索引调整
- 移除了预估价格相关的索引
- 保留了最终价格相关的索引

## 迁移说明

### 执行迁移
```bash
cd backend
poetry run python scripts/migrations/add_final_price_fields.py
```

### 迁移内容
1. 为 `purchase_request_items` 表添加最终价格字段
2. 移除预估价格字段（SQLite限制，字段保留但不使用）
3. 移除价格快照表
4. 创建相关索引

### 注意事项
- 由于SQLite限制，预估价格字段无法完全移除，但已不再使用
- 建议在后续版本中重建表结构以完全清理这些字段
- 价格快照表已完全移除

## 兼容性说明

### 向后兼容
- 现有的预估价格字段在数据库中保留，但不再使用
- 新增字段为可选字段，不影响现有功能
- 价格快照表完全移除

### 数据迁移
- 预估价格数据将保留但不再使用
- 新增字段初始值为 NULL
- 系统不再依赖价格快照机制

## 测试建议

### 功能测试
1. 测试采购申请创建（无价格计算）
2. 测试申请编辑和状态流转
3. 测试审批流程（无价格锁定）
4. 测试购物车功能

### 性能测试
1. 申请创建和编辑的性能
2. 大量申请时的系统性能

### 异常测试
1. 无价格信息时的处理
2. 价格相关功能缺失时的错误处理

## 后续优化建议

1. **价格集成**: 考虑如何更好地集成供应商价格管理
2. **表结构重建**: 在后续版本中重建表结构，完全移除预估价格字段
3. **价格展示**: 在前端其他位置提供价格信息展示
4. **功能补充**: 根据业务需求补充必要的价格相关功能

## 总结

本次更新完全移除了采购申请系统中的价格快照逻辑和预估价格字段，实现了以下目标：

1. **简化系统**: 移除了复杂的价格快照机制
2. **实时价格**: 申请阶段完全使用实时价格计算
3. **职责分离**: 价格管理与采购申请职责分离
4. **性能提升**: 减少了不必要的数据存储和计算

系统现在更加简洁，采购申请专注于申请流程管理，价格信息通过其他模块提供，实现了更好的模块化和职责分离。
