# 申请单时间修改功能

## 功能概述

这是一个专为测试阶段设计的功能，允许超级管理员（super_admin角色）修改采购申请单的时间字段，方便创造出历史上的申请单用于测试和数据分析。

## 访问方式

**URL路径**: `/admin/purchase/requests/{id}/time-manipulation`

**访问要求**: 
- 必须登录系统
- 用户角色必须是 `super_admin`
- 需要手动输入URL，没有按钮链接

## 功能特性

### 可修改的时间字段
- `created_at` - 创建时间
- `updated_at` - 更新时间  
- `submitted_at` - 提交时间

### 快捷调整按钮
每个时间字段都提供以下快捷调整选项：
- **-1 Day**: 往前推1天
- **-1 Week**: 往前推1周
- **-1 Month**: 往前推1个月
- **-1 Year**: 往前推1年

### 时间验证规则
- ❌ 不能修改为未来时间
- ✅ 可以修改为非常久远的历史时间
- ✅ 支持精确到秒的时间修改

## 使用说明

### 1. 访问页面
在浏览器地址栏输入：
```
http://localhost:3000/admin/purchase/requests/{申请单ID}/time-manipulation
```

### 2. 修改时间
- 直接在时间输入框中输入新的时间
- 或使用快捷按钮快速调整时间
- 页面会实时显示修改前后的时间对比

### 3. 保存修改
- 点击"保存修改"按钮提交更改
- 系统会验证时间格式和规则
- 修改成功后显示确认消息

## 注意事项

### ⚠️ 重要提醒
- 此功能仅用于测试阶段
- 修改后的时间将影响相关的统计报表和历史数据分析
- 不会影响申请单的审批流程状态
- 操作不会被记录到操作日志中

### 🔒 权限控制
- 只有 `super_admin` 角色可以访问
- 前端和后端都有权限验证
- 非授权用户会被重定向到无权限页面

### 📊 数据影响
- 报表数据基于实时计算，修改时间后报表会立即反映新时间
- 历史数据分析会基于新的时间进行
- 不会影响其他业务逻辑

## 技术实现

### 后端API
- **端点**: `PUT /api/admin/purchase/requests/{id}/time-manipulation`
- **权限**: 仅限super_admin角色
- **验证**: 时间格式验证、未来时间检查
- **更新**: 直接更新数据库时间字段

### 前端页面
- **路由**: 使用 `SuperAdminRoute` 组件进行权限控制
- **组件**: 独立的 `TimeManipulation` 页面组件
- **状态管理**: 使用React Hooks管理表单状态
- **时间处理**: 支持datetime-local输入和快捷调整

### 数据库操作
- 使用SQLAlchemy ORM更新时间字段
- 支持时区感知的DateTime字段
- 事务安全的数据更新

## 测试建议

### 功能测试
1. **权限测试**: 使用非super_admin角色访问，确认被拒绝
2. **时间验证**: 尝试设置未来时间，确认被拒绝
3. **快捷按钮**: 测试各种快捷调整功能
4. **数据持久化**: 修改后刷新页面，确认数据已保存

### 边界测试
1. **极早时间**: 设置非常早的历史时间
2. **时间格式**: 测试各种时间输入格式
3. **并发操作**: 同时修改多个时间字段

### 集成测试
1. **报表影响**: 修改时间后查看相关报表
2. **历史分析**: 确认历史数据分析基于新时间
3. **工作流状态**: 确认审批流程状态不受影响

## 故障排除

### 常见问题
1. **权限被拒绝**: 确认用户角色为super_admin
2. **时间格式错误**: 使用标准的datetime-local格式
3. **API调用失败**: 检查网络连接和服务器状态

### 调试信息
- 浏览器控制台会显示详细的错误信息
- 后端日志会记录API调用和错误详情
- 前端会显示用户友好的错误提示

## 未来扩展

### 可能的功能增强
- 批量时间修改
- 时间修改历史记录
- 更灵活的时间调整选项
- 时间修改影响分析报告

### 安全增强
- 操作审计日志
- 时间修改审批流程
- 更细粒度的权限控制
