# SPQ和MOQ验证功能实现

## 功能概述

为供应商物品列表和物品配置界面添加了SPQ（标准包装数量）和MOQ（最小订购数量）的验证功能，确保：

1. **整数限制**：SPQ和MOQ只能输入整数
2. **正整数验证**：SPQ和MOQ必须大于0
3. **业务规则验证**：MOQ必须是SPQ的整数倍

## 实现内容

### 前端实现

#### 1. 表单验证规则更新

在 `frontend/src/components/ItemConfiguration.tsx` 中为SPQ和MOQ字段添加了验证规则：

```typescript
// SPQ字段验证
rules={[
  { required: true, message: '请输入SPQ' },
  { type: 'integer', message: 'SPQ必须是整数' },
  { min: 1, message: 'SPQ必须大于0' }
]}

// MOQ字段验证
rules={[
  { required: true, message: '请输入MOQ' },
  { type: 'integer', message: 'MOQ必须是整数' },
  { min: 1, message: 'MOQ必须大于0' },
  ({ getFieldValue }) => ({
    validator(_, value) {
      const spq = getFieldValue('spq');
      if (!value || !spq) {
        return Promise.resolve();
      }
      if (value % spq !== 0) {
        return Promise.reject(new Error(`MOQ必须是SPQ的整数倍，当前SPQ: ${spq}`));
      }
      return Promise.resolve();
    },
  }),
]}
```

#### 2. 输入控件优化

- 添加了 `step={1}` 和 `precision={0}` 确保只能输入整数
- 设置了 `min={1}` 确保输入值大于0

### 后端实现

#### 1. Schema验证更新

在 `backend/app/schemas/supplier.py` 中更新了验证规则：

```python
# ItemSupplierBase
spq: int = Field(1, gt=0, description="标准包装数量")
moq: int = Field(1, gt=0, description="最小订购数量")

@field_validator('moq')
@classmethod
def validate_moq_is_spq_multiple(cls, v, info):
    """验证MOQ必须是SPQ的整数倍"""
    if 'spq' in info.data and v is not None and info.data['spq'] is not None:
        if v % info.data['spq'] != 0:
            raise ValueError(f"MOQ必须是SPQ的整数倍，当前SPQ: {info.data['spq']}, MOQ: {v}")
    return v
```

#### 2. API验证逻辑

在 `backend/app/api/suppliers.py` 中为创建和更新API添加了验证逻辑：

**创建物品供应商关联API**：
```python
# 验证SPQ必须是正整数
if not isinstance(item_supplier.spq, int) or item_supplier.spq <= 0:
    raise HTTPException(status_code=400, detail="SPQ必须是正整数")

# 验证MOQ必须是正整数
if not isinstance(item_supplier.moq, int) or item_supplier.moq <= 0:
    raise HTTPException(status_code=400, detail="MOQ必须是正整数")

# 验证MOQ必须是SPQ的整数倍
if item_supplier.moq % item_supplier.spq != 0:
    raise HTTPException(
        status_code=400, 
        detail=f"MOQ必须是SPQ的整数倍，当前SPQ: {item_supplier.spq}, MOQ: {item_supplier.moq}"
    )
```

**更新物品供应商关联API**：
```python
# 验证SPQ和MOQ
update_data = item_supplier_update.model_dump(exclude_unset=True)
new_spq = update_data.get('spq', item_supplier.spq)
new_moq = update_data.get('moq', item_supplier.moq)

# 验证SPQ必须是正整数
if new_spq is not None and (not isinstance(new_spq, int) or new_spq <= 0):
    raise HTTPException(status_code=400, detail="SPQ必须是正整数")

# 验证MOQ必须是正整数
if new_moq is not None and (not isinstance(new_moq, int) or new_moq <= 0):
    raise HTTPException(status_code=400, detail="MOQ必须是正整数")

# 验证MOQ必须是SPQ的整数倍
if new_spq is not None and new_moq is not None and new_moq % new_spq != 0:
    raise HTTPException(
        status_code=400, 
        detail=f"MOQ必须是SPQ的整数倍，当前SPQ: {new_spq}, MOQ: {new_moq}"
    )
```

## 测试验证

创建了完整的测试套件 `backend/tests/test_spq_moq_validation.py`，包含以下测试用例：

### 创建物品供应商关联测试
1. **有效数据测试**：验证正常SPQ和MOQ值可以成功创建
2. **无效SPQ测试**：验证SPQ为0或负数时被拒绝
3. **无效MOQ测试**：验证MOQ为负数时被拒绝
4. **MOQ非SPQ整数倍测试**：验证MOQ不是SPQ整数倍时被拒绝

### 更新物品供应商关联测试
1. **有效更新测试**：验证正常SPQ和MOQ值可以成功更新
2. **无效SPQ更新测试**：验证更新SPQ为无效值时被拒绝
3. **无效MOQ更新测试**：验证更新MOQ为无效值时被拒绝

### 前端验证集成测试
1. **小数输入测试**：验证小数输入被拒绝
2. **非数字字符串测试**：验证非数字字符串输入被拒绝

## 验证结果

所有测试用例均通过，验证功能正常工作：

```
tests/test_spq_moq_validation.py::TestSPQMOQValidation::test_create_item_supplier_with_valid_spq_moq PASSED
tests/test_spq_moq_validation.py::TestSPQMOQValidation::test_create_item_supplier_with_invalid_spq PASSED
tests/test_spq_moq_validation.py::TestSPQMOQValidation::test_create_item_supplier_with_invalid_moq PASSED
tests/test_spq_moq_validation.py::TestSPQMOQValidation::test_create_item_supplier_with_moq_not_spq_multiple PASSED
tests/test_spq_moq_validation.py::TestSPQMOQValidation::test_update_item_supplier_with_valid_spq_moq PASSED
tests/test_spq_moq_validation.py::TestSPQMOQValidation::test_update_item_supplier_with_invalid_spq PASSED
tests/test_spq_moq_validation.py::TestSPQMOQValidation::test_update_item_supplier_with_moq_not_spq_multiple PASSED
tests/test_spq_moq_validation.py::TestSPQMOQValidation::test_frontend_validation_integration PASSED
```

## 使用说明

### 前端使用
在供应商物品配置界面中：
1. SPQ字段只能输入正整数
2. MOQ字段只能输入正整数
3. 当输入MOQ时，系统会自动验证MOQ是否为SPQ的整数倍
4. 如果验证失败，会显示相应的错误提示

### 后端API
- 创建物品供应商关联：`POST /api/admin/suppliers/{supplier_id}/items`
- 更新物品供应商关联：`PUT /api/admin/item-suppliers/{item_supplier_id}`

两个API都会进行相同的验证：
- SPQ和MOQ必须是正整数
- MOQ必须是SPQ的整数倍

## 业务价值

1. **数据完整性**：确保SPQ和MOQ数据的正确性和一致性
2. **用户体验**：前端实时验证，及时提示用户输入错误
3. **业务逻辑**：MOQ必须是SPQ的整数倍，符合采购业务的实际需求
4. **系统稳定性**：后端双重验证，防止无效数据进入系统 