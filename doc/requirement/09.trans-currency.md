# 多货币汇率管理需求文档

## 1. 需求概述

### 1.1 业务背景
当前系统中供应商对应物品的价格字段默认以美元计价，但实际情况是不同供应商会提供不同货币的价格。为了准确记录供应商的原始报价并支持采购决策，系统需要支持多货币价格管理。同时，为了便于日常查看和决策，系统需要提供汇率转换功能，将不同货币的价格统一转换为美元显示。

### 1.2 核心目标
- **多货币价格支持**: 支持供应商以不同货币报价，准确记录原始价格信息
- **汇率管理**: 建立汇率管理体系，支持月度汇率录入和有效期管理
- **价格转换**: 提供实时汇率转换功能，将多货币价格转换为美元显示
- **采购决策支持**: 在采购申请过程中锁定汇率，确保价格一致性
- **历史记录**: 保存汇率历史记录和修改日志，支持审计追踪

### 1.3 系统定位
多货币汇率管理模块作为系统的基础功能模块，为供应商价格管理、采购申请、价格趋势分析等模块提供汇率转换支持。该模块确保系统能够准确处理多货币价格，同时为业务决策提供统一的美元价格参考。

## 2. 功能需求

### 2.1 汇率管理

#### 2.1.1 汇率录入管理
**业务场景**: 物品管理员需要每月录入主要货币与美元的汇率，确保系统能够进行价格转换。

**功能描述**:
- 汇率录入：支持录入主要货币（CNY、EUR、JPY等）与美元的汇率
- 有效期管理：汇率按月份生效，覆盖整个月份（如1月1日到1月31日）
- 汇率修改：支持修改汇率，记录修改原因和修改历史
- 汇率查询：提供汇率历史查询功能

**输入输出定义**:
- **输入**: 货币代码、汇率值、生效月份、修改原因等
- **输出**: 汇率记录、修改日志、汇率历史

#### 2.1.2 汇率有效性检查
**业务场景**: 系统需要检查汇率的有效性，确保采购申请能够正常进行。

**功能描述**:
- 月度汇率检查：检查当前月份是否有有效汇率
- 审批阻止机制：当月份汇率缺失时，阻止principle approval进行
- 历史汇率回退：使用最近的有效汇率进行价格计算
- 汇率缺失处理：完全无汇率时，采购申请无法提交

**输入输出定义**:
- **输入**: 当前日期、汇率数据、采购申请状态
- **输出**: 汇率有效性状态、审批阻止信息、价格计算结果

### 2.2 多货币价格管理

#### 2.2.1 供应商价格货币支持
**业务场景**: 供应商以不同货币报价，系统需要准确记录原始价格和货币信息。

**功能描述**:
- 货币字段扩展：在供应商价格表中增加货币字段
- 价格录入：支持录入不同货币的价格信息
- 价格显示：在供应商价格管理界面显示原始货币价格
- 价格转换：提供实时汇率转换功能

**输入输出定义**:
- **输入**: 物品ID、供应商ID、价格、货币、数量区间等
- **输出**: 多货币价格记录、转换后的美元价格

#### 2.2.2 价格转换计算
**业务场景**: 系统需要将不同货币的价格转换为美元，便于统一比较和决策。

**功能描述**:
- 实时转换：根据当前有效汇率实时转换价格
- 精度控制：大于1美元显示2位小数，小于1美元显示4位小数
- 汇率回退：当前月汇率缺失时使用最近的有效汇率
- 转换显示：
  - 采购申请单: 显示原币价格、汇率和转换后的美元价格
  - 其它地方仅显示美元

**输入输出定义**:
- **输入**: 原币价格、货币类型、汇率、显示精度要求
- **输出**: 转换后的美元价格、汇率信息、显示格式

### 2.3 采购申请汇率锁定

#### 2.3.1 申请提交汇率记录
**业务场景**: 采购申请提交时需要记录当前汇率，确保申请人看到的价格与审批时一致。

**功能描述**:
- 汇率记录：申请提交时记录当前有效汇率
- 价格计算：使用记录汇率计算预估美元价格
- 汇率显示：在申请界面显示汇率信息
- 价格一致性：确保申请阶段和审批阶段价格一致

**输入输出定义**:
- **输入**: 采购申请、当前汇率、物品价格信息
- **输出**: 汇率记录、预估美元价格、申请信息

#### 2.3.2 审批阶段汇率锁定
**业务场景**: 采购申请在不同审批阶段需要锁定汇率，确保价格计算的一致性。

**功能描述**:
- 汇率锁定：principle approval后记录汇率，锁定最终价格
- 审批阻止：当前月汇率缺失时阻止principle approval
- 价格展示：显示锁定的汇率和转换后的美元价格
- 历史记录：记录各阶段的汇率信息

**输入输出定义**:
- **输入**: 审批状态、汇率数据、价格信息
- **输出**: 锁定汇率、最终价格、审批记录

### 2.4 汇率管理界面

#### 2.4.1 汇率录入界面
**业务场景**: 物品管理员需要便捷地录入和管理汇率信息。

**功能描述**:
- 汇率录入：支持手动录入汇率数据
- 月份选择：提供月份选择器，支持选择生效月份
- 当前汇率显示：显示当前最新汇率及时间
- 汇率修改：支持修改汇率，记录修改原因

**输入输出定义**:
- **输入**: 货币代码、汇率值、生效月份、修改原因
- **输出**: 汇率记录、修改日志、操作结果

#### 2.4.2 汇率查询界面
**业务场景**: 用户需要查询汇率历史记录和变化趋势。

**功能描述**:
- 汇率历史：提供汇率历史查询功能
- 变化图表：展示前180天的汇率变化趋势
- 汇率列表：按月份分组显示汇率记录
- 修改日志：显示汇率修改历史记录

**输入输出定义**:
- **输入**: 查询条件、时间范围、货币类型
- **输出**: 汇率历史、变化图表、修改日志

## 3. 业务对象设计

### 3.1 ER图

```mermaid
erDiagram
    exchange_rates {
        int id PK
        string currency_code UK
        decimal rate
        date effective_month
        string status
        datetime created_at
        datetime updated_at
        int created_by
        int updated_by
    }
    
    exchange_rate_logs {
        int id PK
        int exchange_rate_id FK
        decimal old_rate
        decimal new_rate
        string change_reason
        int changed_by
        datetime changed_at
    }
    
    supplier_prices {
        int id PK
        int item_supplier_id FK
        decimal unit_price
        string currency_code
        int min_quantity
        int max_quantity
        datetime valid_from
        datetime valid_to
        string status
        text remarks
        datetime created_at
        datetime updated_at
        int created_by
        int updated_by
    }
    
    purchase_requests {
        int id PK
        string request_number UK
        string status
        datetime submitted_at
        datetime principle_approved_at
        datetime final_approved_at
        int submitted_by
        int principle_approved_by
        int final_approved_by
        datetime created_at
        datetime updated_at
    }
    
    purchase_request_exchange_rates {
        int id PK
        int purchase_request_id FK
        string stage
        string currency_code
        decimal rate
        datetime recorded_at
        int recorded_by
    }
    
    exchange_rates ||--o{ exchange_rate_logs : "has_logs"
    supplier_prices ||--o{ purchase_request_exchange_rates : "used_in_requests"
    purchase_requests ||--o{ purchase_request_exchange_rates : "has_rates"
```

### 3.2 业务对象说明

#### 3.2.1 ExchangeRate (汇率)
- **描述**: 货币与美元的汇率记录，按月生效
- **主要属性**: 货币代码、汇率值、生效月份、状态、创建时间
- **业务规则**: 每个货币每月只能有一条有效汇率记录

#### 3.2.2 ExchangeRateLog (汇率修改日志)
- **描述**: 记录汇率修改的历史信息
- **主要属性**: 汇率ID、修改前后值、修改原因、修改人、修改时间
- **业务规则**: 每次汇率修改都要记录日志

#### 3.2.3 SupplierPrice (供应商价格)
- **描述**: 供应商针对特定物品的价格信息，支持多货币
- **主要属性**: 物品供应商关系ID、单价、货币代码、数量区间、运费、有效期
- **业务规则**: 价格有有效期，支持多货币，不删除历史记录

#### 3.2.4 PurchaseRequest (采购申请)
- **描述**: 采购申请主表，记录申请状态和审批信息
- **主要属性**: 申请编号、状态、提交时间、审批时间、审批人等
- **业务规则**: 支持四级审批流程，状态流转有严格限制

#### 3.2.5 PurchaseRequestExchangeRate (采购申请汇率记录)
- **描述**: 记录采购申请各阶段的汇率信息
- **主要属性**: 申请ID、阶段、货币代码、汇率、记录时间、记录人
- **业务规则**: 申请提交和principle approval时记录汇率

### 3.3 实体关系描述

- **ExchangeRate ↔ ExchangeRateLog**: 一对多关系，一个汇率可以有多次修改记录
- **SupplierPrice**: 支持多货币，通过currency_code字段标识货币类型
- **PurchaseRequest ↔ PurchaseRequestExchangeRate**: 一对多关系，一个申请可以有多个阶段的汇率记录
- **SupplierPrice ↔ PurchaseRequestExchangeRate**: 通过货币代码关联，支持汇率转换计算

## 4. 数据库表设计

### 4.1 exchange_rates (汇率表)

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY, AUTOINCREMENT | 主键ID |
| currency_code | VARCHAR(10) | NOT NULL, UNIQUE | 货币代码 (CNY, EUR, JPY等) |
| rate | DECIMAL(10,6) | NOT NULL, GT 0 | 汇率值 (1 USD = rate 外币) |
| effective_month | DATE | NOT NULL | 生效月份 (格式: YYYY-MM-01) |
| status | VARCHAR(20) | DEFAULT 'active' | 状态 (active/inactive) |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |
| created_by | INTEGER | NULL | 创建人ID |
| updated_by | INTEGER | NULL | 更新人ID |

**索引**:
- `idx_exchange_rates_currency_code` ON `currency_code`
- `idx_exchange_rates_effective_month` ON `effective_month`
- `idx_exchange_rates_status` ON `status`
- `idx_exchange_rates_created_at` ON `created_at`

**约束**:
- `currency_code` 不能为空，且唯一
- `rate` 必须大于0
- `effective_month` 格式为 YYYY-MM-01

### 4.2 exchange_rate_logs (汇率修改日志表)

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY, AUTOINCREMENT | 主键ID |
| exchange_rate_id | INTEGER | NOT NULL | 汇率记录ID |
| old_rate | DECIMAL(10,6) | NOT NULL | 修改前的汇率值 |
| new_rate | DECIMAL(10,6) | NOT NULL | 修改后的汇率值 |
| change_reason | TEXT | NOT NULL | 修改原因 |
| changed_by | INTEGER | NOT NULL | 修改人ID |
| changed_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 修改时间 |

**索引**:
- `idx_exchange_rate_logs_exchange_rate_id` ON `exchange_rate_id`
- `idx_exchange_rate_logs_changed_at` ON `changed_at`
- `idx_exchange_rate_logs_changed_by` ON `changed_by`

**约束**:
- `exchange_rate_id` 不能为空
- `change_reason` 不能为空
- `changed_by` 不能为空

### 4.3 supplier_prices (供应商价格表) - 修改

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY, AUTOINCREMENT | 主键ID |
| item_supplier_id | INTEGER | NOT NULL | 物品供应商关系ID |
| unit_price | DECIMAL(10,4) | NOT NULL | 单价 |
| currency_code | VARCHAR(10) | NOT NULL | 货币代码 (USD, CNY, EUR等) |
| min_quantity | INTEGER | DEFAULT 1 | 最小数量 |
| max_quantity | INTEGER | NULL | 最大数量 (NULL表示无上限) |
| valid_from | DATETIME | NOT NULL | 生效日期 |
| valid_to | DATETIME | NULL | 失效日期 (NULL表示永久有效) |
| status | VARCHAR(20) | DEFAULT 'active' | 状态 (active/inactive) |
| remarks | TEXT | NULL | 价格备注说明 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |
| created_by | INTEGER | NULL | 创建人ID |
| updated_by | INTEGER | NULL | 更新人ID |

**索引**:
- `idx_supplier_prices_item_supplier_id` ON `item_supplier_id`
- `idx_supplier_prices_currency_code` ON `currency_code`
- `idx_supplier_prices_valid_from` ON `valid_from`
- `idx_supplier_prices_valid_to` ON `valid_to`
- `idx_supplier_prices_status` ON `status`
- `idx_supplier_prices_created_at` ON `created_at`

**约束**:
- `currency_code` 不能为空
- `unit_price` 必须大于0

### 4.4 purchase_request_exchange_rates (采购申请汇率记录表)

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY, AUTOINCREMENT | 主键ID |
| purchase_request_id | INTEGER | NOT NULL | 采购申请ID |
| stage | VARCHAR(20) | NOT NULL | 阶段 (submitted, principle_approved) |
| currency_code | VARCHAR(10) | NOT NULL | 货币代码 |
| rate | DECIMAL(10,6) | NOT NULL | 汇率值 |
| recorded_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 记录时间 |
| recorded_by | INTEGER | NOT NULL | 记录人ID |

**索引**:
- `idx_purchase_request_exchange_rates_request_id` ON `purchase_request_id`
- `idx_purchase_request_exchange_rates_stage` ON `stage`
- `idx_purchase_request_exchange_rates_currency_code` ON `currency_code`
- `idx_purchase_request_exchange_rates_recorded_at` ON `recorded_at`

**约束**:
- `purchase_request_id` 不能为空
- `stage` 只能是 'submitted' 或 'principle_approved'
- `currency_code` 不能为空
- `rate` 必须大于0

## 5. 业务规则

### 5.1 业务约束和限制

#### 5.1.1 汇率管理约束
- **月度唯一性**: 每个货币每月只能有一条有效汇率记录
- **汇率有效期**: 汇率按月份生效，覆盖整个月份（如1月1日到1月31日）
- **汇率精度**: 汇率值保留6位小数，确保转换精度
- **货币支持**: 支持主要货币（USD、CNY、EUR、JPY等），USD作为基准货币

#### 5.1.2 价格转换约束
- **显示精度**: 大于1美元显示2位小数，小于1美元显示4位小数
- **汇率回退**: 当前月汇率缺失时，使用最近的有效汇率进行计算
- **转换公式**: 美元价格 = 原币价格 ÷ 汇率
- **价格一致性**: 申请阶段和审批阶段使用相同的汇率进行计算

#### 5.1.3 采购申请约束
- **汇率锁定**: 申请提交和principle approval时必须记录汇率
- **审批阻止**: 当前月汇率缺失时，阻止principle approval进行
- **价格显示**: 界面上显示原币价格、汇率和转换后的美元价格
- **汇率记录**: 记录各阶段的汇率信息，支持审计追踪

### 5.2 异常处理规则

#### 5.2.1 汇率缺失处理
- **完全无汇率**: 界面上价格显示为 "-(无汇率)"，采购申请无法提交
- **当前月汇率缺失**: 使用最近的有效汇率进行计算，但principle approval时阻止
- **汇率过期**: 使用过期汇率进行计算，界面上不显示特殊标记

#### 5.2.2 价格转换异常
- **汇率无效**: 当汇率值异常时，显示错误信息，阻止价格计算
- **货币不支持**: 当货币代码不在支持范围内时，显示错误信息
- **精度溢出**: 当计算结果超出精度范围时，进行适当的舍入处理

#### 5.2.3 审批流程异常
- **汇率缺失**: 当前月汇率缺失时，显示明确的阻止原因
- **汇率不一致**: 当汇率发生变化时，显示警告信息但不阻止审批
- **历史汇率缺失**: 完全无历史汇率时，阻止采购申请提交

### 5.3 权限和安全要求

#### 5.3.1 汇率管理权限
- **录入权限**: item_admin角色可以录入和修改汇率
- **查询权限**: item_admin可以查询汇率信息
- **修改权限**: 只有item_admin可以修改汇率
- **删除权限**: 汇率记录不允许删除，只能修改状态

#### 5.3.2 数据安全要求
- **汇率修改日志**: 每次汇率修改都要记录详细的修改日志
- **审批记录**: 采购申请的汇率记录不能修改，确保审计追踪
- **数据完整性**: 汇率数据必须完整，不允许空值或无效值
- **访问控制**: 汇率管理功能只对授权用户开放

#### 5.3.3 审计追踪要求
- **修改历史**: 记录汇率的完整修改历史，包括修改人、时间、原因
- **操作日志**: 记录所有汇率相关的操作，支持问题排查
