# BizLinkSpeedy IDM 系统需求概述

## 📋 项目简介

BizLinkSpeedy IDM（工厂物品管理系统）是一个专为工厂环境设计的现代化物品耗材管理系统，旨在提升物品管理效率、优化采购流程，并提供详细的数据分析功能。

## 🎯 核心功能

### 物品管理
- **物品信息管理**: 管理物品基本信息、规格参数、编码等
- **二级分类管理**: 一级分类表示用途，二级分类根据特性分类，属性挂载到二级分类
- **库存管理**: 实时库存监控、低库存预警、出入库记录
- **不可购买状态**: 物品可设置不可购买状态，用于物品已不可购买但还有库存可领用

### 供应商管理
- **供应商信息**: 供应商基本资料、联系方式、评级
- **价格管理**: 阶梯价格设置、价格有效期、历史价格追踪
- **多供应商支持**: 一个Item支持多个供应商（一个Prefer，多个Alternative排序）
- **供应商停用**: 支持停用，停用Prefer后选用Alternative供应商
- **RPA同步**: 使用RPA定时从Agil系统抓取同步至系统
- **价格计算器**: 多供应商比价功能

### 采购流程
- **需求申请**: 部门物品管理员根据消耗情况提交采购申请
- **购物车模式**: 批量添加采购物品，统一提交申请
- **四级审批流程**: 部门物品管理员提交 → 部门经理复核 → 物品管理员主管审批 → 公司主管最终审批
- **采购周期锁定**: 每月25日至次月5日为审核采购期，此期间不可提交申请
- **到货管理**: 到货通知、分配领取、入库操作
- **采购申请二维码**: 生成二维码，可在领取物品处扫码入库
- **历史购买统计**: 采购申请显示历史购买情况统计（物品、数量、价格）

### 领取系统
- **Pad端操作**: 专为移动设备优化的领取界面
- **二维码扫描**: 扫描物品架二维码和工卡二维码
- **实时验证**: 库存检查、权限验证、数据同步
- **Scan In功能**: 于总仓库处做Scan In，部门领取物品后库管登记Scan In物品及数量
- **Scan In独立性**: Scan In与申请功能独立，不与申请订单关联
 - **单位规则**: 入库默认采购单位, 调整/领用默认库存单位, 前端支持单位切换与换算

### 数据分析
- **使用统计**: 物品消耗量、部门使用情况、趋势分析
- **报表系统**: 库存报表、采购报表、成本分析
- **预警系统**: 库存预警、异常使用提醒
- **部门采购统计**: 显示各部门过去10个月采购金额，并提供预估

### 用户管理 & 角色管理与权限管理

#### 用户管理
- **用户信息管理**: 用户基本信息、联系方式、工号、组织关系
- **账号管理**: 用户状态控制、密码管理、多种登录方式
- **自助服务**: 用户自助修改信息、密码重置、头像上传
- **批量管理**: 用户批量操作
- **显示名/备注名**: 支持设置显示名/备注名，用于标识当前使用人
- **用户不跟人走**: 用户跟部门与角色走

#### 角色权限管理
- **八种预设角色**: 参考pingcode项目中的记录，总结出八个用户角色（包含公司主管审批角色）
- **自定义角色**: 支持创建自定义角色，灵活组合权限
- **细粒度权限**: 涵盖用户管理、物品管理、库存管理、采购管理等全模块权限点

#### 数据权限管理
- **部门数据隔离**: 以部门为单位隔离库存、购物车、采购申请、领取记录、统计数据
- **分层数据访问**: 上级部门可访问下级部门数据，支持跨部门授权
- **动态权限过滤**: API和前端数据根据用户权限动态过滤
- **安全审计**: 完整的权限变更记录和操作审计日志

## 👥 用户角色

| 角色 | 权限 | 主要功能 |
|------|------|----------|
| **超级管理员** | 全部权限 | 系统最高权限，拥有所有功能权限 |
| **系统管理员** | 用户、角色、系统配置管理权限 | 用户管理、角色管理、系统配置、数据维护 |
| **公司主管** | 采购申请最终审批权限 | 采购申请最终审批（第三级）、全局报表查看 |
| **物品管理员** | 物品、供应商、采购审批管理权限 | 物品信息管理、供应商管理、采购审批（第二级主管） |
| **部门经理** | 部门内采购申请审批权限 | 采购申请审批（第一级）、部门报表查看 |
| **部门物品管理员** | 部门库存、采购申请管理权限 | 库存管理、采购申请提交、物品分发 |
| **采购员** | 采购申请提交、供应商管理权限 | 采购申请提交、供应商管理、采购执行 |
| **库管员** | 库存操作、Scan In功能权限 | 库存操作、Scan In功能、物品入库 |
| **普通员工** | 物品领取、查看权限 | 通过Pad端领取物品、查看库存 |

## 📊 系统规模

### 当前规模
- **物品数量**: 200+ 件
- **用户数量**: 50+ 人
- **日均领取**: 100+ 次
- **供应商**: 50+ 家

### 预期规模（远期）
- **物品数量**: 1000+ 件
- **日均领取**: 1000+ 次
- **供应商**: 1000+ 家
- **采购频率**: 每月一次

## 🏗️ 技术架构

### 后端技术栈
- **框架**: FastAPI (Python 3.12+)
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **ORM**: SQLAlchemy 2.0
- **认证**: JWT Token
- **数据验证**: Pydantic
- **依赖管理**: Poetry
- **运行环境管理**: Poetry

### 前端技术栈
- **框架**: React 18 + TypeScript
- **UI库**: Ant Design
- **状态管理**: React Context + Hooks
- **HTTP客户端**: Axios
- **构建工具**: Create React App

### 移动端
- **平台**: Web端，专为Pad优化
- **响应式设计**: 适配不同屏幕尺寸
- **离线策略**: 不支持离线使用

### 部署环境
- **网络环境**: 公司/工厂内网 + WiFi
- **操作系统**: Linux (生产) / Windows + WSL (开发)
- **容器化**: Docker (可选)
- **代理服务器**: Nginx (生产)

## 🔧 非功能性需求

### 性能要求
- **响应时间**: API响应 < 200ms，页面加载 < 2s
- **并发用户**: 支持100+并发用户
- **数据处理**: 支持实时数据处理

### 可用性要求
- **系统可用性**: 99.9%（工作时间）
- **故障恢复**: < 5分钟
- **数据备份**: 每日自动备份

### 安全要求
- **用户认证**: JWT Token + 密码加密
- **权限控制**: 基于角色的访问控制
- **数据安全**: 敏感数据加密存储
- **网络安全**: 内网隔离，HTTPS加密
- **密码复杂度**: 因为是内网使用，不需要限制密码复杂度

### 易用性要求
- **界面友好**: 现代化UI设计，符合用户习惯
- **操作简便**: 关键操作≤3步完成
- **移动优化**: Pad端界面针对触屏操作优化
- **商品卡片展示**: 物品使用商品卡片形式展示

### 其它要求: 
- **语言**: 要支持英, 中文用户的使用需要, 同时展示英 / 中文, 按英文(中文)展示, 例如: Apply(申请)
- **菜单重命名**: 重命名菜单项，不需要用那么多"管理"
- **数据迁移**: 要考虑数据结构发生变更导致的数据迁移，具体Item-Model的ID要固化下来
- **用户日志**: 要记录用户日志
- **审核导出**: 为K.J提供近似于审核的功能，审核后可导出JSON数据用于导入Portal走审核流程

## 🌐 系统边界

### 包含范围
- ✅ 物品信息管理
- ✅ 库存管理
- ✅ 采购申请流程
- ✅ 移动端领取
- ✅ 数据报表分析
- ✅ Scan In功能
- ✅ 购物车功能
- ✅ 二级分类管理
- ✅ 多供应商管理
- ✅ 价格计算器
- ✅ 采购申请二维码
- ✅ 部门采购统计

### 不包含范围
- ❌ 财务系统集成
- ❌ 第三方ERP集成
- ❌ 离线模式支持
- ❌ 移动APP开发

## 📋 列表排序功能

### 供应商列表排序
- 名称、编码、状态、评级、交易金额、购买数量

### 库存管理列表排序
- 名称、编码、分类、数量、总使用量、当月使用量、单价、总价值、当月使用价值

### 物品管理列表排序
- 名称、编码、分类、价格

## 🔍 数据完整性要求

### 物品和分类大小写处理
- 物品、分类不区分大小写，即：已经存在Glove，则不可再添加glove

### 入库排序
- 采购过的物品要放到前面

### 价格有效期提醒
- 价格接近有效期要提醒维护人员进行维护

## 📱 技术调研需求

### Web摄像头二维码扫描
- 需要调研Web调用摄像头扫描二维码功能

# 原始需求记录

## 20250813

关于MOQ: 
  * MOQ不影响阶梯价格的数量设置. 仅影响下单时采购数量
  * 部门下单时不受MOQ影响, 但由采购经理审核时会拿总数与MOQ核对, 再与各部门商量实际采购数, 以对应上MOQ
如何应对批准后的价格变动: 
  * 由Buyer来保证批准后到提交Portal之前的价格稳定性
  * 于采购申请的价格处显示当前采购数量对应价格未来的趋势
KJ审核时才Lock住价格
设计Dashboard与来提醒Buyer该维护价格了
各处显示供应商标题处也要显示供应商Code
公司约有15个部门要参与管理
下周内要到账号, 部门, 角色清单, 物品图片
配色修改成Bizlink蓝
物品列表要体现出曾买过的商品
I18n要列入日程
SupplierItem列表
  * 供应商文本要优化
  * 设置优先级功能提到物品详情中
新发Blanket Order逻辑
  * MOQ基数比较大, 需要长期供应的物品, 比如Gas
  * 一次签订大数量协议, 但分批小量供应. 比如签订1000瓶气, 每天要用20瓶, 现场存放得下40瓶. 用完20瓶后再补20瓶直到1000瓶气供应完再签订新合约
使用Dashboard来提醒该补充, 具体是提醒供货还是需要采购由Buyer在Portal中管理, 发现需要补货则直接提供供应商补货, 如果需要采购则提醒部门物品管理员添加购物车
发现新情况: 采购数量通常与供应数量不一样. 会多会少. 申请数量与入库数量不强关联
建议页面上显示版本号水印, 便于定位问题


## 20250730

需求

- 增加数据限制: 
  - 最小库存是SPQ的整数倍
  - MOQ也是是SPQ的整数倍
- 物品属性只有三种: Brand, Spec/Material, Size/Dimension
- Size要做成选项, 要可编辑添加

澄清: 

- 在此系统里, Unit非常重要
- Qty per UP是每Unit内的数量, 是整数
- 库存单位与采购单位不一样
  - 比如一罐耳塞unit是罐, SPQ是6, MOQ是6, Qty per UP是300, 一罐里有300副, 采购是按罐来采购, 库存是按副来管理
  - 再比如气体, 单位是升, SPQ是50, Qty per UP是1, 采购是按50升采购, 库存也是按50升来管理, 领用一次领50升
- Qty per UP如果是1, 则一个单位一个单位领用, 如果是50则代表一个单位里有50份, 可以一份一份领取

- 双单位与换算:
  - 字段: purchase_unit, inventory_unit, qty_per_up(int>=1)
  - 库存以 inventory_unit 计量; 供应商报价与采购以 purchase_unit 计价
  - 入库默认 purchase_unit; 调整/领用默认 inventory_unit; UI 支持单位切换并做换算

- 逐件领用规则: 当 qty_per_up > 1 时允许以库存单位逐件领取; 否则仅整采购单位发放

| 物品 | Unit | SPQ  | MOQ  | Qty per UP | 采购单位 | 库存单位 | 最小领取量 |
| ---- | ---- | ---- | ---- | ---------- | -------- | -------- | ---------- |
| 螺丝 | 粒   | 500  | 1000 | 1          | 粒       | 粒       | 1粒        |
| 耳塞 | 罐   | 6    | 6    | 300        | 罐       | 颗       | 1颗        |
| 气体 | 升   | 50   | 300  | 1          | 升       | 升       | 50升       |
| 手套 | 盒   | 1    | 6    | 50         | 盒       | 双       | 1双        |

优化: 

- 物品信息中展示Prefered供应商的MOQ, SPQ, QTY, 价格

业务规则: 

- 采购申请数量不限制, 当总采购数量达不到MOQ时, 由采购员也各部门协调
- 最小库存各部门自行设定, 最大库存, 最小库存不影响采购
- 特殊特采商品走特殊渠道(其它系统, 新建临时物品)
- 采购申请在流程中, 如果供应商MOQ, SPQ, Price有变更, 或者Prefered供应商有变更, 则打回重新提交
- 物品编码规则3 + 7: 
  - 3位大类码: CLE, ESD, HDW, OFU, PAC, PPE, WEC
  - 7位数字序列码
- 物品编码自动生成, 不靠手填

## 20250725

供应商列表要支持排序: 名称, 编码, 状态, 评级, 交易金额, 购买数量
库存管理列表要支持排序: 名称, 编码, 分类, 数量, 总使用量, 当月使用量, 单价, 总价值, 当月使用价值
物品管理要支持排序: 名称, 编码, 分类, 价格
需要一个购物车功能, department_item_admin可以在日常工作过程中将物品添加到购物车中, 然后每月集中提交采购申请, 交由部门经理复核等步骤...
采购申请中预估单价 -> 单价
采购申请要显示历史购买情况统计, 物品, 数量, 价格
物品要使用商品卡片形式展示
分类管理, 供应商管理, 放到系统管理中
重命名菜单项, 不需要用那么多"管理"
要显示各部门过去10个月采购金额, 并提供预估
入库, 采购过的物品要放到前面. 
因为是内网使用, 不需要限制密码复杂度
采购申请要生成二维码, 可在领取物品处扫码入库
分类管理要二级分类, 属性挂到二级上, 一级仅作指示大概用途

## 20250718

User Manage

总结出八个用户角色（包含公司主管审批角色）, 参考pingcode项目中的记录
用户不跟人走, 跟部门与角色走
账号要可设置显示名/备注名, 用于标识当前使用人

Item Manage

使用二级分类, 一级属用途, 用途下再根据特性进行二级分类.
物品属性挂在二级分类下
物品, 分类不区分大小写, 即: 已经存在Glove, 则不可再添加glove

采购申请的审核

流程: Submit -> Review -> Principle Approval -> Approval
任意一级审核不通过, 则返回至购物车

Scan In功能

于总仓库处做Scan In. 部门领取物品后, 库管登记Scan In物品及数量.
Scan In与申请功能独立, 不与申请订单关联. 哪个部门申请多少, 到货多少, 分配多少由SAP系统负责

Scan out Pad设备

Pad设备属于部门, Pad上的领用页面自动登录部门账号.
领用过程仅登录工牌号, 不作部门校验

注: 物品二维码是否包含部门信息? 若二维码包含部门信息, 则任意设备都可以在任何地点做Scan out


## 20250717
物品管理系统今天收集到的诉求:
物品领用:
支持领用设备与管理端登记领用. 领用设备供个人自行领用登记; 管理端领用供库管人员负责领用登记
领用设备需求接入公司内网, 无SIM卡, 用Wifi. 可选Android APP或者WEB端
每个物品需要编码, 细化到物品-规格
注:
•    管理端领用扫码可使用扫码枪扫码输入

物品管理
物品要维护品类, 品类有对应的属性, 明天物品管理员提供到类目及属性
物品要维护不可购买状态, 用于物品已不可购买, 但还有库存可领用

库存管理:
库存管理要维护最小, 最大库存量. 少于最小库存就可能不够用, 超过最大库存就有可能放不下

物品价格管理:
要支持分段价格, 价格要有有效期
价格接近有效期要提醒维护人员进行维护
需要维护物品的供应商, 供应商有自己的价格, 有供应商信息
一个Item要支持有多个供应商, 一个Prefer, 多个Alternative(排序)
供应商要支持停用, 停用了Prefer供应商后, 选用Alternative1~…供应商
供应商要使用RPA定时从Agil系统抓取同步至系统
Price管理, Price有效期时间段. 如果某时间点有多个价格生效, 则取最新一个价格
可提供价格计算器, 多供应商比价?
 
注:
•    价格的Modified Time要小心处理, 要认真考虑每一个会更新Modified Time的地方
 
物品采购申请:
系统要有锁申请功能, 在每月25日至次月5日为审核采购期, 此期间不可提交申请. 一个月采中采购一次

总体:
要英(中)文显示
要考虑数据结构发生变更导致的数据迁移, 具体Item-Model的ID要固化下来
要记录用户日志
要为K.J提供近似于审核的功能, 审核后可导出JSON数据用于导入Portal走审核流程
 
需要调研Web调用摄像头扫描二维码功能
