# 用户管理 & 权限管理需求文档

## 📋 需求概述

### 业务背景
BizLinkSpeedy IDM系统作为工厂物品管理系统，需要完善的用户管理和权限控制机制，确保不同角色的用户能够安全、高效地使用系统功能。系统采用基于角色的访问控制（RBAC）模式，支持细粒度的权限管理和数据隔离。

### 核心目标
- **安全性**: 确保系统访问安全，防止未授权访问和数据泄露
- **灵活性**: 支持灵活的角色定义和权限分配，适应不同业务场景
- **可扩展性**: 支持未来业务扩展和权限体系调整
- **易用性**: 提供直观的用户管理和权限配置界面
- **审计性**: 完整的操作日志记录，支持安全审计

## 🎯 功能需求

### 1. 用户管理功能

#### 1.1 用户信息管理
- **用户基本信息**: 用户名、邮箱、姓名、工号、职位、联系电话
- **组织关系**: 单部门层级
- **账号状态**: 激活/禁用、锁定/解锁、密码重置
- **扩展信息**: 头像、备注
- **显示名/备注名**: 支持设置显示名/备注名，用于标识当前使用人

#### 1.2 用户生命周期管理
- **用户创建**: 支持单个创建
- **用户更新**: 基本信息修改、状态变更
- **用户状态管理**: 启用/禁用机制，不提供删除功能，确保数据完整性

#### 1.3 密码管理
- **密码策略**: 由于使用于内网, 仅限制密码长度(6位以上), 不作内容复杂度要求
- **密码重置**: 管理员重置

#### 1.4 批量操作
- **批量操作**: 批量启用/禁用、批量分配角色
- **状态筛选**: 各管理页面默认筛选有效状态，提升用户体验

### 2. 角色权限管理功能

#### 2.1 预设角色体系
参考pingcode项目中的记录，总结出八个用户角色：
- **超级管理员**: 系统最高权限，拥有所有功能权限
- **系统管理员**: 用户、角色、系统配置管理权限
- **公司主管**: 采购申请最终审批权限，拥有最高业务决策权
- **物品管理员**: 物品、供应商、采购审批管理权限（主管级审批）
- **部门经理**: 部门内采购申请审批权限（第一级审批）
- **部门物品管理员**: 部门库存、采购申请管理权限
- **采购员**: 采购申请提交、供应商管理权限
- **库管员**: 库存操作、Scan In功能
- **普通员工**: 物品领取、查看权限

#### 2.2 自定义角色
- **角色创建**: 支持创建自定义角色
- **权限组合**: 灵活组合系统权限点

#### 2.3 权限管理
- **细粒度权限**: 涵盖用户、物品、库存、采购等全模块权限点
- **权限分配**: 为角色分配权限、为用户直接授权
- **权限验证**: 实时权限检查和动态权限过滤
- **权限矩阵**: 角色权限矩阵视图

### 3. 数据权限管理

#### 3.1 部门数据隔离
- **部门边界**: 以部门为单位隔离数据访问
- **数据过滤**: API和前端数据根据用户权限动态过滤

#### 3.2 数据权限控制
- **库存数据**: 部门库存数据隔离
- **采购数据**: 采购申请和订单数据隔离
- **报表数据**: 报表数据按部门权限过滤
- **用户数据**: 用户信息按管理权限过滤

### 4. 安全审计功能

#### 4.1 操作日志
- **权限变更**: 角色分配、权限授予/回收记录
- **用户操作**: 用户创建、修改、删除操作记录
- **系统操作**: 登录、登出、密码修改记录
- **数据操作**: 重要数据的增删改操作记录

#### 4.2 审计查询
- **日志查询**: 支持多维度日志查询
- **异常监控**: 异常操作监控和告警
- **合规报告**: 生成合规性审计报告

## 🏗️ 业务对象设计

### 业务对象关系图

```mermaid
erDiagram
    User {
        int id PK
        string username UK
        string email UK
        string hashed_password
        string full_name
        string display_name
        string phone
        string employee_id UK
        string position
        string avatar
        boolean is_active
        boolean is_superuser
        string account_status
        string password_status
        datetime last_login_at
        datetime password_changed_at
        int login_attempts
        datetime locked_until
        int department_id FK
        int role_id FK
        datetime created_at
        datetime updated_at
    }

    Role {
        int id PK
        string code UK
        string name
        string description
        boolean is_system
        boolean is_active
        datetime created_at
        datetime updated_at
        int created_by FK
    }

    Permission {
        int id PK
        string code UK
        string name
        string description
        string module
        boolean is_active
        boolean is_system
        datetime created_at
        datetime updated_at
        int created_by FK
    }

    Department {
        int id PK
        string name
        string code UK
        string description
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    AuditLog {
        int id PK
        int user_id FK
        string action
        string resource_type
        int resource_id
        string old_value
        string new_value
        string ip_address
        string user_agent
        datetime created_at
    }

    RolePermission {
        int role_id PK,FK
        int permission_id PK,FK
        datetime created_at
        int created_by FK
    }

    User ||--o{ Department : "belongs_to"
    User ||--o{ Role : "has_role"
    Role ||--o{ Permission : "has_permissions"
    User ||--o{ AuditLog : "performs_actions"
    Department ||--o{ User : "contains_users"
    Role ||--o{ RolePermission : "role_permissions"
    Permission ||--o{ RolePermission : "permission_roles"
```

### 业务对象说明

#### 1. 用户对象 (User)
- **核心属性**: 用户基本信息、账号状态、组织关系
- **关键关系**: 与部门、角色的多对一关系
- **特殊功能**: 显示名/备注名、密码状态管理

#### 2. 角色对象 (Role)
- **核心属性**: 角色代码、名称、描述、系统标识
- **关键关系**: 与权限的多对多关系
- **特殊功能**: 系统角色保护、自定义角色支持

#### 3. 权限对象 (Permission)
- **核心属性**: 权限代码、名称、描述、所属模块
- **关键关系**: 与角色的多对多关系
- **特殊功能**: 模块化权限管理、系统权限保护

#### 4. 部门对象 (Department)
- **核心属性**: 部门名称、代码、描述、状态
- **关键关系**: 与用户的一对多关系
- **特殊功能**: 部门层级管理、数据隔离边界

#### 5. 审计日志对象 (AuditLog)
- **核心属性**: 操作类型、资源信息、变更记录
- **关键关系**: 与用户的多对一关系
- **特殊功能**: 完整操作追踪、安全审计支持

## 🗄️ 数据库表设计

### 1. 用户表 (users)

```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    display_name VARCHAR(100),  -- 显示名/备注名
    
    -- 扩展个人信息
    phone VARCHAR(20),
    employee_id VARCHAR(50) UNIQUE,
    position VARCHAR(100),
    avatar VARCHAR(255),
    
    -- 状态管理
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    account_status VARCHAR(20) DEFAULT 'active',
    password_status VARCHAR(20) DEFAULT 'normal',
    
    -- 登录相关
    last_login_at DATETIME,
    password_changed_at DATETIME,
    login_attempts INTEGER DEFAULT 0,
    locked_until DATETIME,
    
    -- 组织关系
    department_id INTEGER REFERENCES departments(id),
    role_id INTEGER REFERENCES roles(id),
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_employee_id ON users(employee_id);
CREATE INDEX idx_users_department_id ON users(department_id);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_display_name ON users(display_name);
```

### 2. 角色表 (roles)

```sql
CREATE TABLE roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- 角色属性
    is_system BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- 索引
CREATE INDEX idx_roles_code ON roles(code);
CREATE INDEX idx_roles_is_active ON roles(is_active);
```

### 3. 权限表 (permissions)

```sql
CREATE TABLE permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    module VARCHAR(50) NOT NULL,
    
    -- 状态
    is_active BOOLEAN DEFAULT TRUE,
    is_system BOOLEAN DEFAULT FALSE,
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- 索引
CREATE INDEX idx_permissions_code ON permissions(code);
CREATE INDEX idx_permissions_module ON permissions(module);
CREATE INDEX idx_permissions_is_active ON permissions(is_active);
```

### 4. 角色权限关联表 (role_permissions)

```sql
CREATE TABLE role_permissions (
    role_id INTEGER REFERENCES roles(id),
    permission_id INTEGER REFERENCES permissions(id),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    PRIMARY KEY (role_id, permission_id)
);

-- 索引
CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON role_permissions(permission_id);
```

### 5. 部门表 (departments)

```sql
CREATE TABLE departments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    
    -- 状态
    is_active BOOLEAN DEFAULT TRUE,
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_departments_code ON departments(code);
CREATE INDEX idx_departments_is_active ON departments(is_active);
```

### 6. 审计日志表 (audit_logs)

```sql
CREATE TABLE audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50),
    resource_id INTEGER,
    old_value TEXT,
    new_value TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_resource_type ON audit_logs(resource_type);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
```

## 🔐 权限体系设计

### 1. 权限模块划分

| 模块 | 权限点 | 描述 |
|------|--------|------|
| **用户管理** | user.create, user.read, user.update, user.delete, user.reset_password, user.manage_status | 用户账号管理相关权限 |
| **角色管理** | role.create, role.read, role.update, role.delete, role.assign, role.revoke, permission.read, permission.assign | 角色和权限管理相关权限 |
| **物品管理** | item.create, item.read, item.update, item.delete, item.category_manage | 物品信息管理相关权限 |
| **库存管理** | inventory.read, inventory.update, inventory.transfer, inventory.count, inventory.adjust, inventory.alert, inventory.scan_in | 库存管理相关权限（包含Scan In功能） |
| **采购管理** | purchase.request, purchase.read, purchase.update, purchase.delete, purchase.review, purchase.principle_approve, purchase.approve, purchase.reject, purchase.execute, purchase.receive, purchase.withdraw, purchase.cart_manage, purchase.execution_manage | 采购流程管理相关权限（包含三级审批流程、购物车管理、采购执行管理） |
| **供应商管理** | supplier.create, supplier.read, supplier.update, supplier.delete, supplier.price_manage, supplier.evaluate | 供应商管理相关权限 |
| **报表分析** | report.usage, report.inventory, report.purchase, report.cost, report.admin, report.department | 报表分析相关权限（包含部门采购统计） |
| **部门管理** | department.create, department.read, department.update, department.delete, department.manage_users | 部门管理相关权限 |
| **移动端领取** | mobile.pickup, mobile.scan, mobile.offline_sync | 移动端领取系统相关权限（Pad端操作） |
| **购物车管理** | cart.add_item, cart.update_item, cart.remove_item, cart.view, cart.submit | 购物车管理相关权限（SPQ计数、批量添加、部门购物车） |
| **系统管理** | system.config, system.log, system.backup, system.restore, system.audit | 系统管理相关权限 |

**权限模块总计**: 12个核心模块，涵盖采购申请全流程管理（三级审批流程）

### 1.0 审批流程概述

采购申请采用**三级审批流程**，确保采购决策的合理性和规范性：

1. **第一级：部门经理复核** (`purchase.review`)
   - 角色：部门经理
   - 职责：对采购申请进行初步审核，检查申请合理性
   - 权限：`purchase.review`

2. **第二级：物品管理员原则审批** (`purchase.principle_approve`)
   - 角色：物品管理员
   - 职责：对申请进行业务合理性评估，确认采购必要性
   - 权限：`purchase.principle_approve`

3. **第三级：公司主管审批** (`purchase.approve`)
   - 角色：公司主管
   - 职责：最终业务决策，审批通过后触发合并采购流程
   - 权限：`purchase.approve`

### 1.1 公司主管角色权限详解

公司主管作为采购申请流程的最终决策者（第三级审批），拥有以下核心权限：

#### 采购审批权限
- **`purchase.approve`**: 第三级审批权限
  - 审批通过后自动触发合并采购流程
  - 锁定最终价格和供应商选择
  - 拥有最高业务决策权

#### 数据查看权限
- **`inventory.read`**: 全局库存查看权限
  - 可查看所有部门的库存状况
  - 支持跨部门库存分析
- **`report.*`**: 全部报表分析权限
  - 全局采购成本分析
  - 各部门采购统计对比
  - 供应商绩效评估报告


#### 权限特点
- **独立性**: 不参与日常运营管理，专注于重要决策审批
- **全局视野**: 可查看全局数据，掌握整体采购状况
- **决策权威**: 审批结果不可被下级角色推翻

### 1.2 其他关键角色权限特点

#### 物品管理员（原则审批）
- **`purchase.principle_approve`**: 第二级原则审批权限
  - 业务合理性评估，确认采购必要性
  - 可查看所有物品和供应商信息
  - 拥有物品分类和属性管理权限

#### 部门经理（复核）
- **`purchase.review`**: 第一级复核权限
  - 部门内采购申请初步审核
  - 检查申请合理性和部门预算
  - 可查看本部门及下级部门数据

#### 部门物品管理员
- **`purchase.request`**: 采购申请提交权限
  - 基于消耗情况提交采购申请
  - 管理本部门库存和物品分发
  
- **`cart.*`**: 部门购物车管理权限
  - 基于SPQ计数方式管理购物车
  - 支持批量添加、修改、删除物品
  - 部门购物车唯一性约束

- **`purchase.execution_manage`**: 采购申请执行权限
  - 批量执行已批准的采购申请
  - 管理采购执行批次和记录
  - 生成采购汇总数据和导出采购单

### 1.3 权限继承与组合规则

#### 权限继承原则
- **角色权限**: 用户继承所分配角色的所有权限
- **多角色支持**: 用户可拥有多个角色，权限为并集关系
- **系统角色保护**: 预设系统角色不可删除，权限不可修改

#### 权限组合逻辑
- **审批流程**: 三级审批权限按级别严格分离，不可越级
  - 第一级：部门经理复核（`purchase.review`）
  - 第二级：物品管理员原则审批（`purchase.principle_approve`）
  - 第三级：公司主管审批（`purchase.approve`）
- **采购执行**: 采购执行权限独立于审批流程，确保执行与审批分离
  - 执行权限：`purchase.execution_manage`（仅限物品管理员）
  - 执行范围：只能执行已批准的采购申请
  - 数据隔离：基于部门权限执行采购申请
- **数据访问**: 基于部门层级的权限继承，上级可访问下级数据
- **功能权限**: 操作权限与数据权限分离，确保安全性

#### 特殊权限说明
- **`purchase.withdraw`**: 撤回权限仅限申请人本人
- **`inventory.scan_in`**: Scan In功能仅限库管员角色
- **`mobile.pickup`**: 移动端领取权限基于部门数据隔离
- **`cart.submit`**: 购物车提交权限仅限部门物品管理员
- **`purchase.execution_manage`**: 采购执行权限仅限物品管理员，基于部门权限执行采购申请
- **`batch.status_control`**: 批量状态控制仅限物品管理员角色
- **SPQ计数权限**: 购物车SPQ管理基于部门数据隔离
- **审批权限分离**: 三级审批权限严格分离，不可越级操作

### 2. 三级审批流程权限详细说明

#### 2.1 审批权限点定义
| 权限点 | 权限名称 | 审批级别 | 适用角色 | 业务说明 |
|--------|----------|----------|----------|----------|
| `purchase.review` | 第一级复核 | 部门经理复核 | 部门经理 | 对采购申请进行初步审核，检查申请合理性 |
| `purchase.principle_approve` | 第二级原则审批 | 物品管理员原则审批 | 物品管理员 | 对申请进行业务合理性评估，确认采购必要性 |
| `purchase.approve` | 第三级审批 | 公司主管审批 | 公司主管 | 最终业务决策，审批通过后触发合并采购流程 |
| `purchase.reject` | 拒绝申请 | 各级审批 | 所有审批角色 | 在任一审批环节拒绝申请，状态变为"已拒绝" |
| `purchase.withdraw` | 撤回申请 | 申请状态 | 申请人 | 在审批过程中撤回申请，状态变为"已撤回" |

#### 2.4 审批流程特点
- **严格分级**: 三级审批必须按顺序进行，不可越级
- **权限分离**: 每个审批级别对应不同的角色和权限点
- **状态控制**: 审批状态与操作权限严格绑定
- **流程完整**: 支持申请、审批、拒绝、撤回的完整生命周期

#### 2.2 购物车管理权限点定义
| 权限点 | 权限名称 | 适用角色 | 业务说明 |
|--------|----------|----------|----------|
| `cart.add_item` | 添加物品到购物车 | 部门物品管理员 | 基于SPQ计数方式添加物品到部门购物车 |
| `cart.update_item` | 更新购物车项目 | 部门物品管理员 | 修改购物车中物品的SPQ个数和说明 |
| `cart.remove_item` | 移除购物车项目 | 部门物品管理员 | 从购物车中删除不需要的物品 |
| `cart.view` | 查看购物车 | 部门物品管理员、部门经理 | 查看部门购物车内容和摘要 |
| `cart.submit` | 提交购物车申请 | 部门物品管理员 | 将购物车内容转换为采购申请 |

#### 2.2 预设角色权限矩阵

| 角色 | 用户管理 | 角色管理 | 物品管理 | 库存管理 | 采购管理 | 供应商管理 | 报表分析 | 部门管理 | 移动端领取 | 购物车管理 | 系统管理 |
|------|----------|----------|----------|----------|----------|------------|----------|----------|------------|------------|----------|
| **超级管理员** | ✅ 全部 | ✅ 全部 | ✅ 全部 | ✅ 全部 | ✅ 全部 | ✅ 全部 | ✅ 全部 | ✅ 全部 | ✅ 全部 | ✅ 全部 | ✅ 全部 |
| **系统管理员** | ✅ 全部 | ✅ 全部 | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ 全部 | ❌ | ❌ | ✅ 部分 |
| **公司主管** | ❌ | ❌ | ❌ | ✅ 查看 | ✅ 审批（第三级） | ❌ | ✅ 全部 | ❌ | ❌ | ❌ | ❌ |
| **物品管理员** | ❌ | ❌ | ✅ 全部 | ✅ 部分 | ✅ 原则审批（第二级） | ✅ 全部 | ❌ | ❌ | ❌ | ❌ | ❌ |
| **部门经理** | ❌ | ❌ | ❌ | ✅ 查看 | ✅ 复核（第一级） | ❌ | ✅ 部分 | ❌ | ❌ | ❌ | ❌ |
| **部门物品管理员** | ❌ | ❌ | ❌ | ✅ 部分 | ✅ 申请 | ❌ | ❌ | ❌ | ❌ | ✅ 部门购物车 | ❌ |
| **采购员** | ❌ | ❌ | ❌ | ❌ | ✅ 申请 | ✅ 部分 | ❌ | ❌ | ❌ | ❌ | ❌ |
| **库管员** | ❌ | ❌ | ❌ | ✅ 操作 | ❌ | ❌ | ❌ | ❌ | ✅ 操作 | ❌ | ❌ |
| **普通员工** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ 领取 | ❌ | ❌ |

### 3. 数据权限规则

#### 3.1 部门数据隔离规则
- **库存数据**: 用户只能访问本部门及下级部门的库存数据
- **采购数据**: 用户只能查看本部门及下级部门的采购申请和订单
- **用户数据**: 系统管理员可查看所有用户，部门管理员只能查看本部门用户
- **报表数据**: 报表数据按用户部门权限进行过滤

#### 3.2 权限规则
- **部门继承**: 上级部门可访问下级部门的数据
- **权限合并**: 用户拥有所有分配角色的权限并集

## 🔧 技术实现要点

### 1. 认证授权机制
- **JWT Token**: 基于JWT的无状态认证
- **密码加密**: 使用bcrypt进行密码哈希
- **权限验证**: 基于装饰器的权限验证机制
- **数据过滤**: 动态SQL查询条件过滤

### 2. 性能优化
- **权限缓存**: 用户权限信息缓存机制
- **数据库索引**: 关键字段索引优化
- **分页查询**: 大数据量分页处理
- **批量操作**: 支持批量状态管理

### 3. 安全措施
- **SQL注入防护**: 参数化查询
- **XSS防护**: 输入输出过滤
- **CSRF防护**: 跨站请求伪造防护
- **日志审计**: 完整的操作日志记录

### 4. 扩展性设计
- **权限扩展**: 支持新增权限点
- **角色扩展**: 支持自定义角色
- **模块扩展**: 支持新增功能模块
- **数据扩展**: 支持自定义字段

## 📋 业务规则

### 1. 用户管理规则
- **用户创建**: 必须指定部门和角色
- **用户更新**: 支持显示名/备注名设置
- **用户状态**: 支持启用/禁用，不提供删除功能
- **密码策略**: 内网环境不限制密码复杂度

### 2. 角色管理规则
- **系统角色**: 系统预设角色不可删除
- **自定义角色**: 支持创建自定义角色
- **权限分配**: 角色可分配多个权限
- **角色继承**: 用户可拥有多个角色

### 3. 数据权限规则
- **部门隔离**: 用户只能访问本部门及下级部门数据
- **权限验证**: 所有操作都需要权限验证
- **数据过滤**: API返回数据根据用户权限过滤
- **审计记录**: 所有重要操作都记录审计日志

### 4. 安全审计规则
- **操作记录**: 记录所有用户操作
- **权限变更**: 记录所有权限相关变更
- **异常监控**: 监控异常操作行为
- **合规报告**: 生成合规性审计报告

---

*本文档将随着需求文档的完善持续更新*

 