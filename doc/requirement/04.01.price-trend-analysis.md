# 价格走势分析功能需求文档

## 1. 需求概述

### 1.1 业务背景
在供应商价格管理模块中，用户需要能够直观地查看和分析价格的历史变化趋势，以便：
- 了解价格波动情况
- 预测未来价格走势
- 制定采购策略
- 优化供应商选择

### 1.2 核心目标
- 在价格管理界面添加"显示价格走势"按钮
- 展示前后180天的价格走势图表
- 根据阶梯价格配置显示多条折线
- 提供直观的价格变化可视化分析

### 1.3 系统定位
- 作为价格管理模块的辅助分析功能
- 为采购决策提供数据支持
- 增强系统的数据分析能力

## 2. 功能需求

### 2.1 功能模块划分

#### 2.1.1 价格走势按钮
- **位置**: 在PriceManagement组件中添加价格按钮旁
- **样式**: 与现有按钮保持一致的设计风格
- **权限**: 需要查看价格权限

#### 2.1.2 价格走势图表
- **图表类型**: 折线图
- **时间范围**: 前后180天（当前日期前后各90天）
- **数据源**: 基于当前配置的阶梯价格计算
- **显示内容**: 每个阶梯价格显示为一条独立的折线

#### 2.1.3 图表交互功能
- **悬停**: 鼠标悬停显示详细价格信息及生效价格的摘要
- **图例**: 显示各阶梯价格的图例说明

### 2.2 详细业务场景描述

#### 2.2.1 价格走势查看场景
1. 用户在价格管理界面点击"显示价格走势"按钮
2. 系统弹出模态框显示价格走势图表
3. 图表显示前后180天的价格变化趋势
4. 每个阶梯价格（min_quantity到max_quantity范围）显示为一条折线
5. 用户可以通过图例切换显示/隐藏特定阶梯价格
6. 鼠标悬停在图表上显示具体日期和价格信息及生效价格的摘要

#### 2.2.2 数据计算场景
1. 系统获取当前item_supplier_id的所有有效价格配置
2. 根据每个价格配置的valid_from和valid_to确定生效时间范围
3. 在180天时间范围内，按日期计算每个阶梯的价格
4. 考虑价格的有效期，过期价格不显示
5. 对于没有max_quantity的价格，使用一个合理的上限值（如999999）
6. 同一日期有多个生效的价格规则，选择更新时间最新的那个

### 2.3 输入输出定义

#### 2.3.1 输入参数
```typescript
interface PriceTrendRequest {
  item_supplier_id: number;  // 物品供应商关联ID
  start_date?: string;       // 开始日期（可选，默认当前日期前90天）
  end_date?: string;         // 结束日期（可选，默认当前日期后90天）
}
```

#### 2.3.2 输出数据
```typescript
interface PriceTrendResponse {
  item_supplier_id: number;
  item_name: string;
  supplier_name: string;
  price_trends: PriceTrendData[];
  date_range: {
    start_date: string;
    end_date: string;
  };
}

interface PriceTrendData {
  tier_id: number;           // 阶梯ID（基于min_quantity）
  min_quantity: number;      // 最小数量
  max_quantity?: number;     // 最大数量
  tier_name: string;         // 阶梯名称（如"1-100", "101-500"等）
  price_points: PricePoint[];
}

interface PricePoint {
  date: string;              // 日期
  unit_price: number;        // 单价
  total_price: number;       // 总价（单价+运费）
  is_valid: boolean;         // 是否有效
}
```

## 3. 业务对象设计

### 3.1 Mermaid ER图

```mermaid
erDiagram
    ItemSupplier ||--o{ SupplierPrice : has
    
    ItemSupplier {
        int id PK
        int item_id FK
        int supplier_id FK
        int priority
        string status
        int delivery_days
        int quality_rating
        int spq
        int moq
        datetime created_at
        datetime updated_at
    }
    
    SupplierPrice {
        int id PK
        int item_supplier_id FK
        decimal unit_price
        int min_quantity
        int max_quantity
        date valid_from
        date valid_to
        string status
        string remarks
        datetime created_at
        datetime updated_at
    }
```

### 3.2 业务对象说明

#### 3.2.1 PriceTrendData（价格趋势数据）
- **用途**: 前端展示的价格趋势数据结构
- **特点**: 按阶梯组织价格点数据，通过程序计算生成
- **关系**: 基于SupplierPrice数据计算得出

#### 3.2.2 PricePoint（价格点）
- **用途**: 单个时间点的价格信息
- **特点**: 包含日期、单价、运费、总价等信息
- **关系**: 属于PriceTrendData的一部分

### 3.3 实体关系描述
- ItemSupplier与SupplierPrice是一对多关系
- PriceTrendData通过程序计算从SupplierPrice数据生成
- 价格趋势数据不持久化存储，每次请求时实时计算

## 4. 业务规则

### 4.1 业务约束和限制

#### 4.1.1 时间范围限制
- 默认显示前后180天（当前日期前后各90天）
- 最大时间范围不超过365天
- 最小时间范围不少于30天

#### 4.1.2 价格计算规则
- 只计算状态为'active'的价格配置
- 价格有效期内的数据才显示
- 过期价格在图表中不显示或显示为灰色
- 运费默认为0，如果配置了运费则计入总价
- 同一日期及数量下有多条规则生效，则选择更新时间最近的

#### 4.1.3 阶梯价格规则
- 每个阶梯价格显示为一条独立的折线
- 阶梯名称格式：`${min_quantity}-${max_quantity || '∞'}`
- 没有max_quantity的阶梯显示为`${min_quantity}+`

### 4.2 异常处理规则

#### 4.2.1 数据异常处理
- 如果没有价格数据，显示"暂无价格数据"提示
- 如果价格数据不完整，在缺失时间段显示虚线或空白
- 如果计算过程中出现错误，显示错误信息

#### 4.2.2 网络异常处理
- 网络请求失败时显示重试按钮
- 数据加载过程中显示加载动画
- 超时处理：30秒超时后显示超时提示

### 4.3 权限和安全要求

#### 4.3.1 权限控制
- 需要`SUPPLIER.READ`权限才能查看价格走势
- 价格数据按用户权限过滤
- 敏感价格信息需要额外权限验证

#### 4.3.2 数据安全
- 价格数据在传输过程中加密
- 敏感价格信息不在日志中记录
- 用户操作记录审计日志

## 5. 技术实现方案

### 5.1 前端实现

#### 5.1.1 组件结构
```
PriceManagement/
├── PriceManagement.tsx (主组件)
├── PriceTrendModal.tsx (价格走势模态框)
├── PriceTrendChart.tsx (价格走势图表)
└── PriceTrendService.ts (价格走势服务)
```

#### 5.1.2 技术栈选择
- **图表库**: Recharts（已在package.json中）
- **UI组件**: Ant Design（现有）
- **状态管理**: React Hooks
- **数据请求**: Axios（现有）

#### 5.1.3 组件设计
- 使用Modal组件显示价格走势
- 使用LineChart组件显示折线图
- 使用ResponsiveContainer确保图表响应式
- 使用Tooltip显示详细价格信息及生效价格摘要

### 5.2 后端实现

#### 5.2.1 API设计
```python
# 价格走势API
@router.get("/api/admin/item-suppliers/{item_supplier_id}/price-trend")
async def get_price_trend(
    item_supplier_id: int,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    current_user: User = Depends(get_current_user)
) -> PriceTrendResponse:
    """
    获取价格走势数据
    """
    pass
```

#### 5.2.2 数据计算逻辑
1. 获取指定item_supplier_id的所有有效价格配置
2. 根据时间范围生成日期序列（前后180天）
3. 对每个日期和阶梯计算价格：
   - 筛选该日期生效的价格配置
   - 按阶梯（min_quantity）分组
   - 同一阶梯多个规则时选择更新时间最新的
4. 考虑价格有效期和状态，过期价格标记为无效
5. 格式化数据并返回价格趋势数据

#### 5.2.3 计算算法
```python
def calculate_price_trend(item_supplier_id: int, start_date: date, end_date: date):
    """
    计算价格趋势数据
    """
    # 1. 获取所有有效价格配置
    prices = get_active_prices(item_supplier_id)
    
    # 2. 生成日期序列
    date_range = generate_date_range(start_date, end_date)
    
    # 3. 按阶梯分组价格配置
    tier_prices = group_prices_by_tier(prices)
    
    # 4. 计算每个日期每个阶梯的价格
    price_trends = []
    for tier_id, tier_config in tier_prices.items():
        price_points = []
        for current_date in date_range:
            # 获取该日期生效的价格规则
            active_prices = get_active_prices_for_date(tier_config, current_date)
            if active_prices:
                # 选择更新时间最新的
                latest_price = max(active_prices, key=lambda x: x.updated_at)
                price_point = calculate_price_point(latest_price, current_date)
            else:
                price_point = create_invalid_price_point(current_date)
            price_points.append(price_point)
        
        price_trends.append(PriceTrendData(
            tier_id=tier_id,
            min_quantity=tier_config['min_quantity'],
            max_quantity=tier_config['max_quantity'],
            tier_name=generate_tier_name(tier_config),
            price_points=price_points
        ))
    
    return price_trends
```

### 5.3 性能优化

#### 5.3.1 前端优化
- 使用React.memo优化组件渲染
- 使用useMemo缓存计算结果
- 使用useCallback优化事件处理
- 图表数据分页加载

#### 5.3.2 后端优化
- 数据库查询优化，使用合适的索引
- 异步计算价格趋势数据
- 数据压缩传输

## 6. 测试方案

### 6.1 单元测试
- 价格计算逻辑测试
- 数据格式化测试
- 组件渲染测试

### 6.2 集成测试
- API接口测试
- 前后端集成测试
- 权限控制测试

### 6.3 用户验收测试
- 功能完整性测试
- 用户体验测试
- 性能测试

## 7. 部署和运维

### 7.1 部署要求
- 前端构建优化
- 后端服务配置

### 7.2 监控和日志
- 价格走势查询日志
- 性能监控指标
- 错误告警机制

## 8. 风险评估

### 8.1 技术风险
- 大量数据计算可能影响性能
- 图表渲染在低端设备上可能卡顿
- 数据准确性依赖价格配置的完整性

### 8.2 业务风险
- 价格数据敏感，需要严格控制权限
- 历史数据可能不完整，影响分析准确性
- 用户对图表功能的使用频率不确定

### 8.3 缓解措施
- 实施数据缓存和分页加载
- 加强权限控制和数据安全
- 提供数据质量检查和修复工具
