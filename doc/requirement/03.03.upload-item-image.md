# 补充需求: 上传图片

## 1. 需求概述

### 业务背景
当前系统在编辑物品时只能手动输入图片URL，缺乏完整的图片上传功能。用户需要能够直接上传物品图片，提升用户体验和操作便利性。

### 核心目标
- 在物品编辑界面集成图片上传功能
- 支持图片压缩和格式转换
- 实现图片的本地存储和管理
- 优化图片加载和显示性能

### 系统定位
作为物品管理模块的功能增强，提升物品信息管理的完整性和用户体验。

## 2. 功能需求

### 2.1 图片上传功能
**模块**: 物品管理 - 编辑物品

**详细业务场景**:
- 用户在编辑物品时，通过图片上传组件管理物品图片
- 支持拖拽上传和点击选择文件两种方式
- 支持常见图片格式：JPEG、PNG、GIF、WebP
- 文件大小限制：单文件最大500KB
- 图片尺寸限制：最大分辨率2048x2048像素
- 简化操作：有图片时直接点击更换，无需先删除

**输入输出定义**:
- 输入：图片文件（支持拖拽和点击选择）
- 输出：上传成功后的图片相对URL，自动填充到图片URL字段

### 2.2 图片处理功能
**模块**: 前端图片处理

**详细业务场景**:
- 前端接收原始图片文件
- 自动检测图片尺寸，超过限制时进行压缩
- 图片格式统一转换为JPEG格式（质量85%）

### 2.3 后端保存图片
**模块**: 后端图片处理

**详细业务场景**:
- 检测图片尺寸，超过限制时拒绝

- 保存处理后的图片到服务器

**输入输出定义**:
- 输入：前端上传的图片文件（通过API上传）
- 输出：处理后的图片文件URL

### 2.4 图片存储管理
**模块**: 后端文件存储管理

**详细业务场景**:
- 图片保存到服务器目录：`uploads/images/items`
- 图片命名规则：`{item-code}-image-{YYYYMMDDHHMMSS}.jpg`
- 通过API接口提供图片访问和删除功能

**输入输出定义**:
- 输入：处理后的图片文件
- 输出：图片文件存储路径和API访问URL

## 3. 业务对象设计

### 3.1 图片上传组件
```mermaid
classDiagram
    class ImageUploader {
        +value: string
        +onChange: function
        +uploading: boolean
        +handleFileUpload(file: File)
        +handlePreview()
        +handleDelete()
        +handleReplace()
    }
    
    class ImageUploadAPI {
        +uploadImage(file: File): Promise<UploadResult>
        +deleteImage(filename: string): Promise<void>
        +getImageUrl(filename: string): string
    }
    
    class BackendImageProcessor {
        
        +validateImage(file: UploadFile): boolean
    }
    
    class BackendImageStorage {
        +saveImage(file: UploadFile, subDir: string): Promise<StorageResult>
        +deleteImage(filePath: string): Promise<boolean>
        +getImageInfo(filePath: string): ImageInfo
    }
    
    ImageUploader --> ImageUploadAPI
    ImageUploadAPI --> BackendImageProcessor
    BackendImageProcessor --> BackendImageStorage
```

### 3.2 业务对象说明
- **ImageUploader**: 前端图片上传组件，负责用户交互和文件选择
- **ImageUploadAPI**: 前端API接口，负责与后端通信
- **BackendImageProcessor**: 后端图片处理服务，负责压缩和格式转换
- **BackendImageStorage**: 后端图片存储管理，负责文件保存和URL生成

## 4. 数据库表设计

### 4.1 物品表字段变更
**表名**: `items`

**新增字段**:
```sql

```



## 5. 业务规则

### 5.1 图片上传规则
- **文件格式限制**: 仅支持JPEG、PNG、GIF、WebP格式
- **文件大小限制**: 单文件最大500KB
- **图片尺寸限制**: 最大分辨率2048x2048像素
- **上传频率限制**: 同一物品5分钟内最多上传3次

### 5.2 图片处理规则
- **压缩规则**: 图片尺寸超过2048x2048像素时自动压缩
- **格式转换**: 统一转换为JPEG格式，质量设置为85%

- **文件命名**: 使用物品编码和精确时间戳，格式为`{item-code}-image-{YYYYMMDDHHMMSS}.jpg`

### 5.3 存储管理规则
- **目录结构**: 图片文件保存在`uploads/images/items/`目录下
- **文件命名**: `{item-code}-image-{YYYYMMDDHHMMSS}.jpg`
- **文件管理**: 后端FastAPI服务负责文件读写和管理
- **访问权限**: 图片文件通过后端API接口访问

### 5.4 用户交互规则
- **无图片状态**: 显示上传区域，支持点击上传
- **有图片状态**: 显示300x300像素的原图，点击图片直接上传替换
- **更换图片**: 点击图片区域或右上角上传按钮直接选择新图片替换
- **简化操作**: 移除删除按钮和预览功能，直接点击图片区域即可上传替换
- **简单更新**: 上传新图片时直接更新URL，不删除旧图片

### 5.5 异常处理规则
- **文件格式错误**: 显示错误提示，要求重新选择文件
- **文件大小超限**: 提示文件过大，建议压缩后重试
- **上传失败**: 显示网络错误，提供重试选项
- **处理失败**: 回滚操作，恢复原始状态

### 5.6 权限和安全要求
- **上传权限**: 需要物品编辑权限
- **文件安全**: 仅允许图片文件上传，禁止可执行文件，所有图片都会进行压缩处理（如果不是JPEG则转换为JPEG，然后再进行压缩）
- **路径安全**: 使用日期作为文件名，避免路径遍历攻击
- **访问控制**: 图片文件通过API接口访问，不暴露服务器路径

## 6. 技术实现要点

### 6.1 前端实现
- 使用React + TypeScript开发
- 集成Ant Design的Image和Upload组件
- 实现拖拽上传和进度显示
- 通过API接口上传图片到后端
- 简化交互：有图片时直接点击更换，无需先删除

### 6.2 图片处理
- 使用后端PIL库进行图片处理
- 支持图片压缩和格式转换
- 处理过程在服务器端完成，确保处理质量

### 6.3 文件存储（后端API服务）
- 后端FastAPI服务负责文件读写操作
- 图片文件保存到`uploads/images/items/`目录
- 提供API接口访问图片文件
- 支持图片获取、删除和信息查询

### 6.4 性能优化
- 图片压缩减少文件大小

- 使用懒加载优化图片显示性能
- 简化操作流程，提升用户体验
