# Item Property 补充需求: 配置Property选项

## 📋 需求概述

### 业务背景
在工厂物品管理系统中，物品的 Brand（品牌）、Spec/Material（规格/材质）、Size/Dimension（尺寸/规格）等属性通常是手填的，但在实际业务中，某些物品类型的这些属性值是有固定范围的。例如：
- 手套类物品的尺寸只能是 S、M、L 三种规格
- 安全帽的品牌可能限定为几个特定品牌
- 某些耗材的材质只能是特定的几种选择

如果允许随意填写，会导致数据不一致、难以统计和管理的问题。

### 核心目标
- **数据标准化**: 通过选项配置确保物品属性值的标准化和一致性
- **灵活配置**: 支持不同物品分类的差异化属性配置需求
- **用户体验**: 为管理员提供直观的选项选择界面，提升数据录入效率
- **数据质量**: 减少手填错误，提高数据质量和可追溯性

### 系统定位
该功能是对现有物品管理模块的增强，在保持现有功能的基础上，为物品属性管理提供更精细的控制能力，是物品分类管理的重要补充功能。

## 🎯 功能需求

### 1. 属性配置管理功能

#### 1.1 属性输入类型配置
- **配置位置**: 在二级分类管理界面中配置 Brand、Spec/Material、Size/Dimension 的输入类型
- **输入类型**: 
  - **选项类型**: 限制为预定义的选项值，编辑时显示下拉选择框
  - **文字类型**: 允许自由输入，编辑时显示文本框
- **配置权限**: 权限随着二级分类的编辑权限走, 即有管理分类权限的角色就可以配置

#### 1.2 选项值管理
- **选项配置**: 当选择"选项"类型时，需要配置可选的选项值列表
- **选项格式**: 支持文本格式的选项值，如 "S"、"M"、"L" 或 "品牌A"、"品牌B"
- **选项排序**: 支持选项值的排序，影响下拉框中的显示顺序
- **选项验证**: 确保选项值不为空且在同一分类下唯一

#### 1.3 配置继承机制
- **分类级别配置**: 属性配置在二级分类级别生效
- **物品继承**: 该分类下的所有物品自动继承分类的属性配置
- **配置变更影响**: 修改分类属性配置后，该分类下的物品编辑界面会相应更新

### 2. 物品编辑界面增强

#### 2.1 动态控件显示
- **选项控件**: 当属性配置为"选项"类型时，显示下拉选择框
- **文本控件**: 当属性配置为"文字"类型时，显示文本输入框
- **实时切换**: 根据分类的属性配置动态显示对应的输入控件

#### 2.2 数据验证
- **选项验证**: 选项类型的属性值只作前端验证, 后台不进行管控
- **必填验证**: 无必填项配置及限制
- **格式验证**: 不必对属性值作格式校验

#### 2.3 用户体验优化
- **默认值**: 不必设置默认值
- **占位符**: 为文本输入框提供合适的占位符提示

### 3. 数据迁移和兼容性

#### 3.1 现有数据处理
- **默认配置**: 现有分类默认所有属性为"文字"类型
- **数据保留**: 现有物品的属性值保持不变
- **渐进迁移**: 支持管理员逐步将分类配置为选项类型

#### 3.2 向后兼容
- **API兼容**: 保持现有API接口的兼容性
- **数据格式**: 保持现有数据存储格式的兼容性
- **功能扩展**: 在现有功能基础上进行扩展，不影响现有功能

## 🏗️ 业务对象设计

### ER图

```mermaid
erDiagram
    ItemCategory ||--o{ Item : "contains"
    ItemCategory ||--o{ ItemPropertyConfig : "has_configs"

    ItemCategory {
        integer id PK
        string name
        string description
        integer primary_category_id FK
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    ItemPropertyConfig {
        integer id PK
        integer category_id FK
        string attribute_name "brand|spec_material|size_dimension"
        string input_type "select|text"
        json options "选项值数组，仅input_type为select时使用"
        datetime created_at
        datetime updated_at
    }

    Item {
        integer id PK
        string name
        string code
        integer category_id FK
        string brand
        string spec_material
        string size_dimension
        boolean is_active
        datetime created_at
        datetime updated_at
    }
```

### 业务对象说明

#### ItemCategory（物品分类）
- **功能**: 物品分类管理，支持一级和二级分类
- **配置关联**: 通过 `ItemPropertyConfig` 表关联属性配置
- **继承机制**: 二级分类的属性配置会应用到该分类下的所有物品

#### ItemPropertyConfig（物品属性配置）
- **功能**: 存储每个分类的属性配置信息
- **配置范围**: 支持 brand、spec_material、size_dimension 三个属性
- **输入类型**: select（选项）或 text（文本）
- **选项管理**: 当类型为 select 时，通过 options 字段存储选项值数组
- **排序控制**: 通过 sort_order 字段控制配置的显示顺序

#### Item（物品）
- **功能**: 物品信息管理
- **属性继承**: 根据所属分类的属性配置动态显示输入控件
- **数据验证**: 根据分类配置进行数据验证

### 实体关系描述

1. **ItemCategory 与 ItemPropertyConfig**: 一对多关系
   - 一个分类可以有多个属性配置（brand、spec_material、size_dimension）
   - 每个属性配置属于特定的分类

2. **ItemCategory 与 Item**: 一对多关系
   - 一个分类可以包含多个物品
   - 每个物品属于特定的分类

3. **ItemPropertyConfig 与 Item**: 间接关系
   - 物品通过所属分类继承属性配置
   - 配置变更会影响该分类下所有物品的编辑界面

## 🗄️ 数据库表设计

### 1. 物品属性配置表 (item_property_configs)

```sql
CREATE TABLE item_property_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    category_id INTEGER NOT NULL,
    attribute_name VARCHAR(50) NOT NULL,  -- brand, spec_material, size_dimension
    input_type VARCHAR(20) NOT NULL,  -- select, text
    options TEXT,  -- JSON格式存储选项值数组，仅select类型使用
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_item_property_configs_category_id ON item_property_configs(category_id);
CREATE INDEX idx_item_property_configs_attribute_name ON item_property_configs(attribute_name);
-- 复合唯一索引：同一分类下属性名称唯一
CREATE UNIQUE INDEX idx_item_property_configs_unique ON item_property_configs(category_id, attribute_name);
```

### 2. 现有物品表保持不变

```sql
-- 物品表中的现有字段保持不变，用于向后兼容
-- brand, spec_material, size_dimension 字段继续使用
-- 配置表仅用于控制前端输入方式和验证规则
```

### 字段说明

#### item_property_configs 表字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | INTEGER | 是 | 主键，自增 |
| category_id | INTEGER | 是 | 关联的分类ID |
| attribute_name | VARCHAR(50) | 是 | 属性名称：brand/spec_material/size_dimension |
| input_type | VARCHAR(20) | 是 | 输入类型：select/text |
| is_required | BOOLEAN | 否 | 是否必填，默认FALSE |
| default_value | TEXT | 否 | 默认值 |
| options | TEXT | 否 | JSON格式的选项值数组，仅select类型使用 |
| sort_order | INTEGER | 否 | 排序顺序，默认0 |
| is_active | BOOLEAN | 否 | 是否激活，默认TRUE |
| created_at | DATETIME | 否 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |

## 🔧 业务规则

### 1. 数据约束规则

#### 1.1 属性配置约束
- **分类级别配置**: 属性配置只能在二级分类级别设置，一级分类不能配置
- **属性名称唯一性**: 同一分类下属性名称必须唯一（brand、spec_material、size_dimension）
- **输入类型限制**: 输入类型只能是 "select" 或 "text"
- **选项值唯一性**: 同一配置下的选项值必须唯一，不区分大小写

#### 1.2 数据验证规则
- **选项值验证**: 当属性配置为"选项"类型时，物品的属性值必须在预定义选项中
- **必填验证**: 根据属性配置的必填设置进行验证
- **格式验证**: 文本类型的属性值长度限制在200字符以内

#### 1.3 配置变更规则
- **数据迁移**: 从"文字"类型改为"选项"类型时，保留现有数据
- **向后兼容**: 从"选项"类型改为"文字"类型时，保留现有数据

### 2. 权限和安全规则

#### 2.1 操作权限
- **配置权限**: 只有系统管理员可以修改分类的属性配置
- **查看权限**: 只有系统管理员可以查看分类的属性配置信息
- **数据权限**: 根据用户的物品管理权限控制属性值的查看和编辑

### 3. 异常处理规则

#### 3.1 配置异常处理
- **无效配置**: 检测到无效的属性配置时，使用默认配置
- **选项缺失**: 当选项配置为空时，自动切换为文本类型

### 4. 性能优化规则

#### 4.1 查询优化
- **索引优化**: 为常用查询字段建立合适的索引
- **批量查询**: 优化批量获取配置信息的查询性能

#### 4.2 存储优化
- **JSON压缩**: 对JSON配置数据进行压缩存储


## 实施步骤

1. 更新Model定义
2. 运行a_, b_, c_开头的脚本重建数据库及初始化数据
3. 更新前后端代码
4. 验证功能
5. 写集成测试来测试后端接口
