# 物品管理 & 分类管理需求文档

## 📋 需求概述

### 业务背景
BizLinkSpeedy IDM系统作为工厂物品管理系统，物品管理是系统的核心业务模块。系统需要建立完善的物品信息管理体系，包括物品分类、物品信息、规格属性等，为采购、库存、领取等业务流程提供基础数据支撑。系统采用二级分类体系，支持灵活的属性配置，满足工厂多样化物品管理需求。

### 核心目标
- **标准化管理**: 建立统一的物品编码和分类体系，规范物品管理流程
- **灵活配置**: 支持二级分类和动态属性配置，适应不同物品类型需求
- **高效检索**: 提供强大的搜索和筛选功能，快速定位目标物品
- **数据完整性**: 确保物品信息的准确性和一致性，支持历史追溯
- **易用性**: 提供直观的物品管理界面，支持商品卡片形式展示

### 系统定位
物品管理模块是BizLinkSpeedy IDM系统的核心基础模块，为采购管理、库存管理、供应商管理等业务模块提供基础数据支撑。通过建立标准化的物品管理体系，提升整个系统的数据质量和业务效率。

## 🎯 功能需求

### 1. 物品分类管理功能

#### 1.1 二级分类体系
- **一级分类**: 表示物品用途，如"劳保用品"、"办公用品"、"生产耗材"等
- **二级分类**: 根据物品特性进行分类，如"手套"、"口罩"、"安全帽"等
- **分类层级**: 支持父子分类关系，属性配置挂载到二级分类
- **分类编码**: 自动生成分类编码，支持手动修改

#### 1.2 分类属性配置
- **动态属性**: 支持为二级分类配置自定义属性
- **属性类型**: 支持文本、数字、选择、日期等多种属性类型
- **属性验证**: 支持属性值的格式验证和必填验证
- **属性继承**: 同一分类下的物品自动继承分类属性配置

#### 1.3 分类管理操作
- **分类创建**: 支持创建一级和二级分类
- **分类编辑**: 修改分类名称、描述、属性配置
- **分类删除**: 删除空分类，有物品的分类不可删除
- **分类查询**: 支持分类名称搜索和层级展示

### 2. 物品信息管理功能

#### 2.1 物品基本信息
- **物品名称**: 物品的标准名称，支持中英文
- **物品图片**: 物品的图片，如无图片用默认占位图片代替
- **物品编码**: 唯一标识码，支持自动生成和手动设置
- **物品描述**: 详细描述信息，支持富文本
- **物品单位**: UOM(计量单位)、MOQ(最小购买数量)、SPQ(标准包装数量)、QTY(单位包装数量)，支持固体、液体、气体等多种物品类型
- **不可购买状态**: 物品可设置不可购买状态，用于物品已不可购买但还有库存可领用

#### 2.2 物品规格属性
- **动态规格**: 根据分类属性配置动态生成规格字段
- **规格值管理**: 支持文本、数字、选择等多种规格值类型
- **规格验证**: 根据属性配置进行规格值验证
- **规格展示**: 以结构化方式展示物品规格信息

#### 2.3 物品状态管理
- **物品状态**: 启用/禁用状态控制
- **库存状态**: 实时库存数量显示
- **价格状态**: 平均价格和最新价格显示
- **供应商状态**: 关联供应商信息显示
- **图片状态**: 图片上传状态和显示状态管理

### 3. 物品搜索和筛选功能

#### 3.1 全文搜索
- **搜索范围**: 物品名称、编码、描述、规格信息
- **搜索算法**: 全文搜索，支持模糊匹配
- **搜索结果**: 按相关度排序
- **搜索历史**: 记录用户搜索历史，支持快速重搜

#### 3.2 高级筛选
- **分类筛选**: 按一级或二级分类筛选
- **状态筛选**: 按物品状态、库存状态筛选
- **价格筛选**: 按价格范围筛选
- **规格筛选**: 按规格属性值筛选
- **组合筛选**: 支持多条件组合筛选

#### 3.3 排序功能
- **默认排序**: 按名称、编码、分类、价格排序
- **自定义排序**: 支持用户自定义排序规则
- **排序记忆**: 记住用户的排序偏好(前端保存)

### 4. 物品展示功能

#### 4.1 商品卡片展示
- **卡片布局**: 采用商品卡片形式展示物品信息，类似淘宝/1688
- **关键信息**: 显示物品图片、名称、编码、分类、价格等关键信息
- **图片支持**: 支持物品图片展示，无图片时显示默认占位图
- **快速操作**: 卡片上提供快速编辑、查看详情等操作

#### 4.2 列表展示
- **表格布局**: 支持表格形式展示物品列表
- **列配置**: 可配置显示的列和列宽
- **批量操作**: 支持批量选择、批量编辑、批量删除
- **分页显示**: 支持大数据量的分页显示

#### 4.3 详情展示
- **完整信息**: 显示物品的所有详细信息
- **关联信息**: 显示关联的分类、供应商、库存等信息
- **历史记录**: 显示物品的创建、修改历史
- **操作记录**: 显示物品相关的操作记录

### 5. (暂不实现)数据导入导出功能

#### 5.1 数据导入
- **Excel导入**: 支持Excel格式的物品数据导入
- **批量创建**: 支持批量创建物品和分类
- **数据验证**: 导入时进行数据格式和业务规则验证
- **错误处理**: 提供详细的导入错误信息和处理建议

#### 5.2 数据导出
- **格式支持**: 支持Excel、CSV等格式导出
- **筛选导出**: 支持按筛选条件导出数据
- **字段选择**: 支持选择导出的字段
- **批量导出**: 支持大量数据的批量导出

## 🏗️ 业务对象设计

### 业务对象关系图

```mermaid
erDiagram
    ItemPrimaryCategory {
        int id PK
        string name UK
        string description
        string code_prefix
        string code_format
        int current_sequence
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    ItemCategory {
        int id PK
        string name UK
        string description
        int primary_category_id FK
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    ItemAttribute {
        int id PK
        string name
        string code UK
        string description
        string data_type
        boolean is_required
        boolean is_unique
        string default_value
        string validation_rules
        int category_id FK
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    Item {
        int id PK
        string name
        string code UK
        string description
        int category_id FK
        string image_url
        string unit
        decimal qty
        boolean is_purchasable
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    ItemSpecification {
        int id PK
        int item_id FK
        int attribute_id FK
        string value
        datetime created_at
        datetime updated_at
    }

    ItemChangeHistory {
        int id PK
        int item_id FK
        int user_id FK
        string action
        string field_name
        string old_value
        string new_value
        datetime created_at
    }

    ItemPrimaryCategory ||--o{ ItemCategory : "contains"
    ItemCategory ||--o{ ItemAttribute : "has_attributes"
    ItemCategory ||--o{ Item : "contains"
    Item ||--o{ ItemSpecification : "has_specifications"
    ItemAttribute ||--o{ ItemSpecification : "used_in"
    Item ||--o{ ItemChangeHistory : "has_changes"
    Item ||--o{ ItemSupplier : "has_suppliers"
    Item ||--o{ Inventory : "has_inventory"
    Item ||--o{ UsageRecord : "has_usage"
```

### 业务对象说明

#### 1. 一级分类对象 (ItemPrimaryCategory)
- **核心属性**: 分类名称、描述、编码配置、状态
- **关键关系**: 与二级分类的一对多关系
- **特殊功能**: 一级分类管理，表示物品用途，负责编码生成

#### 2. 物品分类对象 (ItemCategory)
- **核心属性**: 分类名称、描述、所属一级分类、状态
- **关键关系**: 与一级分类的多对一关系，与物品的一对多关系
- **特殊功能**: 物品分类管理，表示物品特性

#### 3. 物品属性对象 (ItemAttribute)
- **核心属性**: 属性名称、代码、数据类型、验证规则
- **关键关系**: 与二级分类的多对一关系，与物品规格的多对多关系
- **特殊功能**: 属性配置管理，支持多种数据类型和验证规则

#### 4. 物品对象 (Item)
- **核心属性**: 物品基本信息、图片、单位配置、所属二级分类、库存信息
- **关键关系**: 与二级分类的多对一关系，与规格的多对多关系
- **特殊功能**: 编码生成、规格管理、状态控制、图片管理

#### 5. 物品规格对象 (ItemSpecification)
- **核心属性**: 物品ID、属性ID、属性值
- **关键关系**: 与物品的多对一关系，与属性的多对一关系
- **特殊功能**: 动态规格值存储，支持复杂属性配置

-- 物品编码序列对象已移除，编码生成功能集成到一级分类对象中

#### 7. 物品变更历史对象 (ItemChangeHistory)
- **核心属性**: 变更记录、操作人、变更内容
- **关键关系**: 与物品、用户的多对一关系
- **特殊功能**: 变更追踪、审计支持

## 🗄️ 数据库表设计

### 1. 一级分类表 (item_primary_categories)

```sql
CREATE TABLE item_primary_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- 编码配置
    code_prefix VARCHAR(10) NOT NULL,  -- 编码前缀，如 "LB", "BG", "SC"
    code_format VARCHAR(20) DEFAULT '0000',  -- 编码格式，如 "0000"
    current_sequence INTEGER DEFAULT 1,  -- 当前序号
    
    -- 状态
    is_active BOOLEAN DEFAULT TRUE,
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_item_primary_categories_name ON item_primary_categories(name);
CREATE INDEX idx_item_primary_categories_is_active ON item_primary_categories(is_active);
CREATE INDEX idx_item_primary_categories_code_prefix ON item_primary_categories(code_prefix);
```

### 2. 物品分类表 (item_categories)

```sql
CREATE TABLE item_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- 所属一级分类
    primary_category_id INTEGER NOT NULL,
    
    -- 状态
    is_active BOOLEAN DEFAULT TRUE,
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_item_categories_name ON item_categories(name);
CREATE INDEX idx_item_categories_primary_id ON item_categories(primary_category_id);
CREATE INDEX idx_item_categories_is_active ON item_categories(is_active);
-- 复合唯一索引：同一一级分类下分类名称唯一
CREATE UNIQUE INDEX idx_item_categories_unique ON item_categories(primary_category_id, name);
```

### 3. 物品属性表 (item_attributes)

```sql
CREATE TABLE item_attributes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    
    -- 属性配置
    data_type VARCHAR(20) NOT NULL,  -- text, number, select, date, boolean
    is_required BOOLEAN DEFAULT FALSE,
    is_unique BOOLEAN DEFAULT FALSE,
    default_value TEXT,
    validation_rules TEXT,  -- JSON格式存储验证规则
    
    -- 所属分类
    category_id INTEGER NOT NULL,
    
    -- 状态
    is_active BOOLEAN DEFAULT TRUE,
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_item_attributes_code ON item_attributes(code);
CREATE INDEX idx_item_attributes_category_id ON item_attributes(category_id);
CREATE INDEX idx_item_attributes_is_active ON item_attributes(is_active);
```

### 4. 物品表 (items)

```sql
CREATE TABLE items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(200) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    
    -- 所属分类
    category_id INTEGER NOT NULL,
    
    -- 基本信息
    image_url VARCHAR(500),  -- 物品图片URL
    unit VARCHAR(20) DEFAULT '个',  -- UOM(计量单位)
    qty DECIMAL(10, 3) DEFAULT 1,  -- QTY(单位包装数量)，支持小数
    is_purchasable BOOLEAN DEFAULT TRUE,  -- 是否可购买
    is_active BOOLEAN DEFAULT TRUE,
            
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_items_name ON items(name);
CREATE INDEX idx_items_code ON items(code);
CREATE INDEX idx_items_category_id ON items(category_id);
CREATE INDEX idx_items_is_active ON items(is_active);
CREATE INDEX idx_items_is_purchasable ON items(is_purchasable);
CREATE INDEX idx_items_total_stock ON items(total_stock);
CREATE INDEX idx_items_image_url ON items(image_url);
-- 复合唯一索引：同一分类下物品名称唯一
CREATE UNIQUE INDEX idx_items_unique_name ON items(category_id, name);
```

### 5. 物品规格表 (item_specifications)

```sql
CREATE TABLE item_specifications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id INTEGER NOT NULL,
    attribute_id INTEGER NOT NULL,
    value TEXT,
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_item_specifications_item_id ON item_specifications(item_id);
CREATE INDEX idx_item_specifications_attribute_id ON item_specifications(attribute_id);
-- 复合唯一索引：同一物品的同一属性只能有一个值
CREATE UNIQUE INDEX idx_item_specifications_unique ON item_specifications(item_id, attribute_id);
```

-- 物品编码序列表已移除，编码生成功能集成到一级分类表中

### 7. 物品变更历史表 (item_change_history)

```sql
CREATE TABLE item_change_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    
    -- 变更信息
    action VARCHAR(50) NOT NULL,  -- create, update, delete
    field_name VARCHAR(100),
    old_value TEXT,
    new_value TEXT,
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_item_change_history_item_id ON item_change_history(item_id);
CREATE INDEX idx_item_change_history_user_id ON item_change_history(user_id);
CREATE INDEX idx_item_change_history_action ON item_change_history(action);
CREATE INDEX idx_item_change_history_created_at ON item_change_history(created_at);
```


## 🔧 业务规则

### 1. 数据约束规则

#### 1.1 分类数据约束
- **一级分类名称唯一性**: 一级分类名称在系统中必须唯一，大小写不敏感
- **一级分类编码前缀唯一性**: 一级分类编码前缀必须唯一，不能重复，大小写不敏感
- **分类名称唯一性**: 同一一级分类下分类名称必须唯一，大小写不敏感
- **分类删除限制**: 有物品的分类不可删除，有分类的一级分类不可删除
- **属性配置限制**: 只有分类可以配置属性，一级分类不能配置属性

#### 1.2 物品数据约束
- **物品编码唯一性**: 物品编码必须全局唯一，不能重复
- **物品名称唯一性**: 同一分类下物品名称不能重复（不区分大小写）
- **物品分类必填**: 物品必须关联到有效的分类（应用层验证）
- **物品规格验证**: 物品规格值必须符合所属分类的属性配置要求

#### 1.3 数据格式约束
- **名称长度限制**: 物品名称长度限制在200字符以内
- **编码格式限制**: 物品编码长度限制在50字符以内
- **描述长度限制**: 物品描述支持富文本，无长度限制
- **图片URL限制**: 物品图片URL长度限制在500字符以内
- **单位配置限制**: MOQ、SPQ、QTY必须为正数，支持小数（如气体、液体等）
- **规格数据格式**: 规格信息存储在规格表中，支持结构化查询

### 2. 业务逻辑约束

#### 2.1 分类业务约束
- **分类层级逻辑**: 分类必须属于某个一级分类
- **分类状态逻辑**: 禁用一级分类时，其下所有分类和物品自动禁用
- **分类属性继承**: 物品自动继承所属分类的属性配置
- **分类操作顺序**: 必须先创建一级分类，再创建分类

#### 2.2 物品业务约束
- **物品状态逻辑**: 物品状态与库存状态、价格状态保持一致
- **物品编码逻辑**: 已使用的编码不建议修改，避免影响关联数据
- **物品编码生成**: 物品编码由所属分类的一级分类自动生成，使用固定的IDM前缀
- **编码并发安全**: 编码生成使用数据库行锁防止重复，确保编码唯一性
- **物品图片逻辑**: 物品图片可选，无图片时显示默认占位图
- **物品单位逻辑**: MOQ、SPQ、QTY必须满足业务合理性（MOQ ≤ SPQ），支持小数（如气体、液体等）
- **物品规格逻辑**: 根据分类的属性配置决定哪些规格为必填项
- **物品删除逻辑**: 有库存或使用记录的物品不可删除

#### 2.3 搜索业务约束
- **搜索范围限制**: 只能搜索已启用的物品和分类
- **搜索结果限制**: 搜索结果按相关度排序，最多返回1000条
- **筛选条件限制**: 多条件筛选时，条件间为AND关系
- **排序规则限制**: 排序字段必须在预定义的字段范围内
- **规格搜索限制**: 规格搜索基于物品规格表，支持属性值精确匹配

### 3. 权限和安全约束

#### 3.1 操作权限约束
- **查看权限**: 所有用户都可以查看物品信息
- **创建权限**: 只有物品管理员可以创建物品和分类
- **编辑权限**: 只有物品管理员可以编辑物品和分类信息
- **删除权限**: 只有物品管理员可以删除物品和空分类

#### 3.2 数据安全约束
- **数据访问控制**: 用户只能访问有权限的数据
- **操作审计要求**: 所有重要操作必须记录审计日志
- **数据备份要求**: 重要数据必须定期备份(由数据库运维程序完成, 不由本系统完成)
- **数据恢复机制**: 必须支持误删数据的恢复操作

### 4. 性能和质量约束

#### 4.1 性能约束
- **响应时间限制**: 搜索操作响应时间不超过2秒
- **并发处理限制**: 支持100+并发用户同时操作
- **编码生成性能**: 编码生成操作响应时间不超过200毫秒（使用数据库锁）
- **数据量限制**: 单个分类下物品数量不超过10000个
- **查询复杂度限制**: 避免全表扫描和大结果集查询

#### 4.2 数据质量约束
- **数据完整性**: 关键字段不能为空，关联数据必须存在（应用层验证）
- **数据一致性**: 相关数据状态必须保持一致
- **数据准确性**: 物品信息必须准确反映实际情况
- **数据时效性**: 库存、价格等动态数据必须实时更新
- **引用完整性**: 在应用层保证外键引用的完整性

### 5. 系统集成约束

#### 5.1 数据迁移约束
- **向后兼容性**: 数据结构变更必须保持向后兼容
- **数据迁移完整性**: 数据迁移过程中不能丢失数据
- **迁移回滚机制**: 数据迁移失败时必须支持回滚
- **迁移验证要求**: 数据迁移后必须进行完整性验证


