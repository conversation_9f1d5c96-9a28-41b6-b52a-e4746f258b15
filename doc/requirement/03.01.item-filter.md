# Item Filter 补充需求

## 1. 需求概述

### 业务背景
当前Item列表页面已有左侧分类筛选和基础的搜索功能，但用户在同一个一级分类下切换二级分类时操作不够便捷。参考淘宝等电商平台的商品列表筛选体验，需要在Item列表上方增加一个平级分类快速切换组件，当用户选择二级分类后显示同级的其他分类供快速切换，同时提供属性筛选功能。

### 核心目标
- **提升筛选体验**: 在保留左侧分类筛选的基础上，增加平级分类快速切换功能
- **增强视觉引导**: 通过平级分类切换和属性筛选，帮助用户快速定位目标物品
- **优化信息展示**: 动态显示相关筛选选项，避免信息冗余
- **保持界面简洁**: 确保筛选组件不会占用过多页面空间

### 系统定位
这是一个前端用户体验优化功能，需要后端提供相应的数据接口支持，但不涉及数据库结构变更。通过平级分类快速切换和属性筛选，提升用户在Item列表页面的筛选体验。

## 2. 功能需求

### 2.1 筛选组件布局

#### 2.1.1 平级分类快速切换
- **位置**: 在Item列表页面的顶部，位于搜索框和操作按钮之上
- **触发条件**: 当用户在左侧分类筛选器中选择了一个分类后显示
- **样式**: 采用卡片式设计，背景色为浅灰色，边框为1px实线
- **高度**: 自适应高度，根据分类数量调整
- **响应式**: 在不同屏幕尺寸下保持良好的显示效果

#### 2.1.2 组件结构
```
┌─────────────────────────────────────────────────────────┐
│ 快速切换: [手套] [口罩] [安全帽] [防护服] [全部]      │
│ 属性筛选: Brand: [3M] [霍尼韦尔] [全部]              │
│          Spec: [N95] [KN95] [全部]                   │
│          Size: [M] [L] [XL] [全部]                   │
└─────────────────────────────────────────────────────────┘
```

### 2.2 平级分类快速切换功能

#### 2.2.1 显示逻辑
- **触发条件**: 当用户在左侧分类筛选器中选择了一个二级分类后显示
- **显示内容**: 显示与当前选中分类同级的其他二级分类
- **当前分类**: 当前选中的分类用主色调高亮显示
- **全部选项**: 提供"全部"选项，点击后清除分类筛选

#### 2.2.2 交互行为
- **点击切换**: 点击平级分类立即切换到该分类，更新物品列表
- **分类高亮**: 当前选中的分类用主色调高亮显示
- **分类计数**: 每个分类后显示该分类下的物品数量（可选）
- **快速导航**: 提供在同一一级分类下快速切换二级分类的能力

### 2.3 属性筛选功能

#### 2.3.1 属性显示条件
- **未选择分类**: 不显示任何属性筛选选项
- **选择一级分类**: 显示该一级分类下所有物品的属性值
- **选择二级分类**: 显示该二级分类下所有物品的属性值
- **显示时机**: 与平级分类快速切换组件同时显示

#### 2.3.2 属性筛选类型
- **Brand（品牌）**: 显示该分类下所有物品的品牌
- **Spec/Material（规格/材质）**: 显示该分类下所有物品的规格或材质
- **Size/Dimension（尺寸/规格）**: 显示该分类下所有物品的尺寸或规格

#### 2.3.3 属性值处理
- **去重显示**: 同一属性下的相同值只显示一次
- **空值处理**: 不显示空值或null值的选项
- **动态更新**: 当分类变化时，属性选项实时更新
- **多选支持**: 支持在同一属性下选择多个值

### 2.4 筛选状态管理

#### 2.4.1 筛选状态显示
- **当前筛选**: 在筛选组件上方显示当前应用的筛选条件
- **筛选标签**: 每个筛选条件显示为一个可删除的标签
- **清除全部**: 提供"清除全部筛选"按钮

#### 2.4.2 筛选结果反馈
- **结果数量**: 显示当前筛选条件下的物品数量
- **无结果提示**: 当筛选结果为空时，显示友好的提示信息
- **加载状态**: 筛选过程中显示加载动画

### 2.5 交互体验优化

#### 2.5.1 响应式设计
- **移动端适配**: 在小屏幕设备上采用折叠式设计
- **触摸友好**: 确保在触摸设备上有足够的点击区域
- **滚动优化**: 筛选组件固定，物品列表可独立滚动

#### 2.5.2 性能优化
- **防抖处理**: 用户快速切换筛选条件时，避免频繁请求
- **缓存机制**: 缓存已加载的分类和属性数据
- **懒加载**: 属性选项按需加载，避免一次性加载过多数据

## 3. 业务对象设计

### 3.1 前端数据模型

```mermaid
classDiagram
    class FilterState {
        +selectedPrimaryCategory: number?
        +selectedCategory: number?
        +selectedBrands: string[]
        +selectedSpecs: string[]
        +selectedSizes: string[]
        +showQuickSwitch: boolean
    }
    
    class CategoryData {
        +id: number
        +name: string
        +primary_category_id: number
        +item_count: number
    }
    
    class PeerCategoryData {
        +id: number
        +name: string
        +item_count: number
        +is_selected: boolean
    }
    
    class AttributeData {
        +brand: string[]
        +spec_material: string[]
        +size_dimension: string[]
    }
    
    FilterState --> CategoryData
    FilterState --> PeerCategoryData
    FilterState --> AttributeData
```

### 3.2 组件状态管理

```mermaid
stateDiagram-v2
    [*] --> Initial
    Initial --> CategorySelected: 选择分类
    CategorySelected --> QuickSwitchVisible: 选择二级分类
    QuickSwitchVisible --> AttributesLoaded: 加载属性
    AttributesLoaded --> FilterApplied: 应用筛选
    FilterApplied --> QuickSwitchVisible: 切换平级分类
    FilterApplied --> CategorySelected: 切换一级分类
    FilterApplied --> Initial: 清除筛选
    FilterApplied --> AttributesLoaded: 修改属性筛选
```

## 4. 界面设计规范

### 4.1 视觉设计
- **主色调**: 使用系统主色调（蓝色系）
- **选中状态**: 主色调背景 + 白色文字
- **未选中状态**: 白色背景 + 深色文字 + 边框
- **禁用状态**: 浅灰色背景 + 浅色文字

### 4.2 布局规范
- **间距**: 筛选项之间间距8px，行间距12px
- **内边距**: 筛选组件内边距16px
- **圆角**: 筛选按钮圆角4px
- **阴影**: 轻微阴影效果，提升层次感

### 4.3 交互反馈
- **悬停效果**: 鼠标悬停时显示浅色背景
- **点击反馈**: 点击时有轻微缩放效果
- **加载状态**: 数据加载时显示骨架屏或加载动画

## 5. 业务规则

### 5.1 筛选逻辑规则
- **分类筛选**: 选择分类后，只显示该分类下的物品
- **平级切换**: 在同一一级分类下快速切换二级分类
- **属性筛选**: 属性筛选在分类筛选基础上进行
- **多属性筛选**: 同一属性下的多个值采用OR逻辑，不同属性间采用AND逻辑
- **筛选优先级**: 分类筛选 > 属性筛选 > 搜索文本

### 5.2 数据更新规则
- **实时更新**: 筛选条件变化时立即更新物品列表
- **状态同步**: 筛选状态与URL参数同步，支持浏览器前进后退
- **数据缓存**: 已加载的分类和属性数据缓存5分钟

### 5.3 异常处理规则
- **网络异常**: 网络请求失败时显示错误提示，保持当前筛选状态
- **数据异常**: 数据格式错误时显示默认状态
- **空数据处理**: 分类或属性为空时显示"暂无数据"提示

### 5.4 用户体验规则
- **操作反馈**: 每次筛选操作都有明确的视觉反馈
- **状态保持**: 页面刷新后保持用户的筛选状态
- **渐进增强**: 在基础功能基础上提供高级筛选选项

## 6. 后端接口需求

### 6.1 平级分类接口
- **接口路径**: `GET /api/admin/items/categories/{category_id}/peers`
- **功能描述**: 获取指定分类的同级分类列表
- **请求参数**: 
  - `category_id`: 当前选中的分类ID
- **响应数据**:
  ```json
  {
    "data": [
      {
        "id": 1,
        "name": "手套",
        "item_count": 15,
        "is_selected": false
      },
      {
        "id": 2,
        "name": "口罩", 
        "item_count": 8,
        "is_selected": true
      }
    ]
  }
  ```

### 6.2 分类属性接口
- **接口路径**: `GET /api/admin/items/categories/{category_id}/attributes`
- **功能描述**: 获取指定分类下所有物品的属性值
- **请求参数**:
  - `category_id`: 分类ID（支持一级分类和二级分类）
- **响应数据**:
  ```json
  {
    "data": {
      "brand": ["3M", "霍尼韦尔", "杜邦"],
      "spec_material": ["N95", "KN95", "医用"],
      "size_dimension": ["M", "L", "XL"]
    }
  }
  ```

### 6.3 接口说明
- **数据库结构**: 不涉及数据库表结构变更
- **权限控制**: 使用现有的物品读取权限
- **缓存策略**: 建议对分类和属性数据进行缓存
- **错误处理**: 返回标准的错误响应格式

## 7. 不包含的板块

### 7.1 复杂功能
- 不包含筛选条件的保存和分享功能
- 不包含筛选历史的记录功能
- 不包含自定义筛选条件的创建功能

### 7.2 其他模块
- 不涉及物品详情页面的修改
- 不涉及采购流程的筛选功能
- 不涉及报表和统计功能的筛选

### 6.2 复杂功能
- 不包含筛选条件的保存和分享功能
- 不包含筛选历史的记录功能
- 不包含自定义筛选条件的创建功能

### 6.3 其他模块
- 不涉及物品详情页面的修改
- 不涉及采购流程的筛选功能
- 不涉及报表和统计功能的筛选
