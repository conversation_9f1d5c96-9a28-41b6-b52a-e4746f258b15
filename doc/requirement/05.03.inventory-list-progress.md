# 库存列表进度条展示逻辑

## 展示形式

* 第一行显示库存量和状态标签
* 第二行显示分段进度条
* 第三行显示库存范围

## 进度条逻辑

### 进度条上限计算
* **有最大库存限制**：以 `max(最大库存, 当前库存)` 为上限
* **无最大库存限制**：以当前库存为上限（100%）

### 进度条分段颜色规则
* **缺货状态**：库存为0时，进度条宽度为0%，背景为红色
* **低库存状态**：0到最小库存区间，显示橙色
* **正常状态**：最小库存到最大库存区间，显示绿色
* **超储状态**：超过最大库存区间，显示蓝色

### 进度条显示逻辑
* **缺货时**：进度条宽度为0%，显示红色背景
* **有库存时**：根据库存水平分段显示不同颜色
* **无最大库存限制时**：进度条显示为100%满

## 具体示例

### 有最大库存限制的情况
* 库存范围：0~10，当前库存：5
  * 进度条上限：10
  * 进度条显示：0~5为橙色（低库存），5~10为空白
  * 进度百分比：50%

* 库存范围：10~20，当前库存：15
  * 进度条上限：20
  * 进度条显示：0~10为橙色（低库存），10~15为绿色（正常），15~20为空白
  * 进度百分比：75%

* 库存范围：10~20，当前库存：25
  * 进度条上限：25
  * 进度条显示：0~10为橙色（低库存），10~20为绿色（正常），20~25为蓝色（超储）
  * 进度百分比：100%

### 无最大库存限制的情况
* 库存范围：0~-，当前库存：8
  * 进度条上限：8
  * 进度条显示：0~8为绿色（正常），100%满
  * 进度百分比：100%

* 库存范围：0~-，当前库存：0
  * 进度条上限：0
  * 进度条显示：0%宽度，红色背景（缺货）
  * 进度百分比：0%

## 状态判断规则
* **缺货**：有DepartmentInventory记录，且库存为0
* **低库存**：库存大于0但小于等于最小库存
* **正常**：库存在合理范围内
* **超储**：库存超过最大库存