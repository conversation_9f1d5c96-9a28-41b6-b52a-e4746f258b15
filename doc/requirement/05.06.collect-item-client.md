# 领取物品客户端

## 1. 需求概述

### 业务背景
在工厂环境中，员工需要定期领取工作所需的物品和耗材。传统的纸质登记方式效率低下，容易出现错误，且难以追踪物品使用情况。需要一个现代化的移动端领取系统来提升物品领取效率，实现数字化管理。

### 核心目标
- 提供便捷的物品领取操作界面，支持二维码扫描和手动输入
- 实现物品领取的自动化记录和库存实时更新
- 建立完整的物品使用追溯体系
- 优化部门物品管理流程，提升工作效率

### 系统定位
该客户端是 BizLinkSpeedy IDM 系统的移动端领取模块，专门为 Android PAD 设备优化，提供基于 Web 的物品领取服务。客户端与管理系统独立，拥有独立的登录入口和功能页面。

## 2. 功能需求

### 2.1 用户认证模块

#### 2.1.1 部门仓库账号登录
- **功能描述**: 客户端使用部门仓库专用账号登录，以识别物品领取来源部门
- **业务场景**: 部门仓库管理员使用 `dept_warehouse_<部门简写>` 账号登录客户端
- **输入**: 部门仓库账号、密码
- **输出**: 登录状态、部门信息、权限信息
- **特殊要求**: 
  - 登录状态永久有效，无需重复登录
  - 登录界面使用最简单的登录功能，不需要找回密码等功能
  - 独立于管理系统的登录入口
  - 账号命名规则：`dept_warehouse_<部门简写>`，如 `dept_warehouse_ENG`（工程部）

#### 2.1.2 权限验证
- **功能描述**: 验证用户是否有权限进行物品领取操作
- **业务场景**: 每次物品领取前进行权限检查
- **输入**: 用户身份、操作类型
- **输出**: 权限验证结果
- **权限要求**:
  - 用户必须具有 `dept_warehouse` 角色
  - 仅能操作本部门数据（领取物品、查看领取记录）
  - 支持物品领取和记录查看权限

### 2.2 物品识别模块

#### 2.2.1 二维码扫描
- **功能描述**: 通过扫描物品二维码获取物品编码
- **业务场景**: 员工在物品架前扫描物品二维码
- **输入**: 物品二维码（简化版格式，仅包含物品编码如 IDM001）
- **输出**: 物品编码、物品基本信息
- **技术要求**: 
  - 支持 Web 摄像头调用
  - 支持简化版二维码格式（避免换行符处理问题）
  - 扫描响应时间 < 2 秒
  - 扫码枪兼容性：支持扫码枪作为键盘输入

#### 2.2.2 手动输入
- **功能描述**: 支持手动输入物品编码
- **业务场景**: 二维码损坏或扫描失败时的备选方案
- **输入**: 物品编码
- **输出**: 物品信息验证结果

### 2.3 员工身份验证模块

#### 2.3.1 工卡扫描
- **功能描述**: 扫描员工卡获取工号
- **业务场景**: 验证领取物品的员工身份
- **输入**: 员工工卡二维码（简化版格式，仅包含员工工号如 EMP001）
- **输出**: 员工工号、姓名、部门信息

#### 2.3.2 工号输入
- **功能描述**: 支持手动输入员工工号
- **业务场景**: 工卡扫描失败或工卡丢失时的备选方案
- **输入**: 员工工号
- **输出**: 员工信息验证结果

### 2.4 物品领取模块

#### 2.4.1 数量输入
- **功能描述**: 输入要领取的物品数量
- **业务场景**: 员工确认领取数量
- **输入**: 领取数量
- **输出**: 数量验证结果
- **业务规则**: 
  - 数量为浮点数
  - 数量不能超过可用库存
  - 支持单位换算显示（采购单位 vs 库存单位）

#### 2.4.2 库存验证
- **功能描述**: 验证部门库存是否足够
- **业务场景**: 领取前检查库存可用性
- **输入**: 物品编码、领取数量、部门信息
- **输出**: 库存状态、可用数量
- **业务规则**: 
  - 实时库存检查
  - 低库存预警提示
  - 库存不足时阻止领取

#### 2.4.3 领取确认
- **功能描述**: 确认物品领取操作
- **业务场景**: 员工确认领取信息无误后提交
- **输入**: 物品信息、员工信息、领取数量
- **输出**: 领取成功确认、领取记录

### 2.5 数据记录模块

#### 2.5.1 领取记录
- **功能描述**: 记录物品领取的详细信息
- **业务场景**: 每次领取操作完成后自动记录
- **记录内容**: 
  - 物品编码、名称、规格
  - 领取数量、单位（支持小数）
  - 员工工号、姓名、部门
  - 领取时间、操作人员
  - 库存变化前后状态
- **双重记录**: 
  - 记录到 `ITEM_USAGE_RECORD` 表（使用行为记录）
  - 同时记录到 `InventoryChangeRecord` 表（库存变更记录）
  - 两表通过 `inventory_change_id` 字段关联

#### 2.5.2 库存更新
- **功能描述**: 实时更新部门库存数量
- **业务场景**: 领取完成后立即更新库存
- **更新内容**: 
  - 库存数量扣减
  - 最后使用时间更新
  - 使用量统计更新
- **变更记录**: 同时记录到 `InventoryChangeRecord` 表，记录类型为 `pickup_out`

### 2.6 历史查询模块

#### 2.6.1 领取历史
- **功能描述**: 显示该部门近期领取历史
- **业务场景**: 库管员查看部门物品使用情况
- **显示内容**: 
  - 领取时间、物品信息
  - 领取人员、数量
  - 操作状态
- **查询条件**: 
  - 时间范围筛选
  - 物品分类筛选
  - 员工筛选

#### 2.6.2 统计信息
- **功能描述**: 显示部门物品使用统计
- **业务场景**: 了解物品消耗趋势
- **统计内容**: 
  - 物品使用频率
  - 部门消耗排名
  - 库存周转率

### 2.7 界面适配模块

#### 2.7.1 手机/平板优化
- **功能描述**: 专为 Android PAD 设备优化的界面
- **优化内容**: 
  - 大尺寸触屏操作
  - 清晰的视觉层次
  - 便捷的手势操作
  - 单页面即完成操作
- **适配内容**: 
  - 仅竖屏操作
  - 响应式布局
  - 触摸友好的按钮尺寸
  - 移动端导航优化

## 3. 业务对象设计

### 3.1 业务对象关系图

```mermaid
erDiagram
    DEPARTMENT ||--o{ INVENTORY : manages
    DEPARTMENT ||--o{ ITEM_USAGE_RECORD : generates
    DEPARTMENT ||--o{ USER : has
    
    USER ||--o{ ITEM_USAGE_RECORD : operates
    USER {
        string user_id PK
        string username
        string department_id FK
        string role_id FK
        string status
    }
    
    ITEM_USAGE_RECORD {
        string record_id PK
        string item_id FK
        string department_id FK
        string employee_id FK
        int quantity
        string unit
        datetime usage_time
        string operator_id FK
        string notes
    }
    
    DEPARTMENT {
        string department_id PK
        string department_name
        string parent_department_id FK
        string status
    }
    
    INVENTORY {
        string inventory_id PK
        string item_id FK
        string department_id FK
        int current_quantity
        int min_quantity
        int max_quantity
        string unit
        datetime last_updated
    }
    
    ITEM {
        string item_id PK
        string item_code
        string item_name
        string category_id FK
        string purchase_unit
        string inventory_unit
        int qty_per_up
        string status
    }
    
    EMPLOYEE {
        string employee_id PK
        string employee_code
        string employee_name
        string department_id FK
        string status
    }
    
    ROLE {
        string role_id PK
        string role_name
        string description
        string status
    }
```

### 3.2 业务对象说明

#### 3.2.1 USER (用户)
- **描述**: 部门仓库用户信息，使用现有用户体系
- **主要属性**: 用户ID、用户名、所属部门、角色ID、状态
- **业务规则**: 
  - 账号命名规则：`dept_warehouse_<部门简写>`
  - 每个部门可以有多个仓库用户
  - 用户只能操作本部门数据

#### 3.2.2 ROLE (角色)
- **描述**: 用户角色定义，新增 `dept_warehouse` 角色
- **主要属性**: 角色ID、角色名称、描述、状态
- **业务规则**: 
  - `dept_warehouse` 角色具有物品领取和记录查看权限
  - 权限范围限制在本部门数据

#### 3.2.3 ITEM_USAGE_RECORD (物品使用记录)
- **描述**: 物品领取使用的详细记录
- **主要属性**: 记录ID、物品ID、部门ID、员工ID、数量、单位、使用时间、操作员ID、库存变更记录ID、备注
- **业务规则**: 
  - 每次领取都会生成记录，支持完整的追溯链
  - 与库存变更记录表关联，实现双重记录机制

#### 3.2.4 INVENTORY_CHANGE_RECORD (库存变更记录)
- **描述**: 库存数量变化的详细记录
- **主要属性**: 记录ID、部门ID、物品ID、变更前后数量、变更类型、操作员ID、变更日期
- **业务规则**: 
  - 物品领取时记录类型为 `pickup_out`
  - 与使用记录表关联，支持双向追溯

### 3.3 实体关系描述

- **部门与用户**: 一个部门可以有多个仓库用户，用户只能属于一个部门
- **用户与角色**: 用户通过角色获得权限，`dept_warehouse` 角色具有特定权限
- **部门与库存**: 一个部门管理多个物品的库存，库存按部门隔离
- **部门与使用记录**: 一个部门产生多个使用记录，记录按部门隔离
- **用户与使用记录**: 用户操作产生使用记录，记录包含操作员信息
- **物品与库存**: 一个物品在多个部门有库存，库存按部门管理
- **使用记录与变更记录**: 使用记录通过 `inventory_change_id` 关联库存变更记录，实现双重记录机制

## 4. 数据库表设计

### 4.1 新增角色配置

#### 4.1.1 在现有角色表中新增 `dept_warehouse` 角色

| 字段名 | 值 | 说明 |
|--------|----|------|
| role_name | 'dept_warehouse' | 部门仓库角色名称 |
| description | '部门仓库管理员，具有物品领取和记录查看权限' | 角色描述 |
| status | 'ACTIVE' | 角色状态 |

#### 4.1.2 角色权限配置

| 权限模块 | 权限点 | 权限描述 | 是否授权 |
|----------|--------|----------|----------|
| 物品领取 | item_pickup | 物品领取操作 | ✅ |
| 记录查看 | record_view | 查看本部门领取记录 | ✅ |
| 库存查询 | inventory_query | 查询本部门库存 | ✅ |
| 员工验证 | employee_verify | 验证员工身份 | ✅ |

### 4.2 新增用户账号

#### 4.2.1 用户账号命名规则
- **格式**: `dept_warehouse_<部门简写>`
- **示例**: 
  - `dept_warehouse_ENG` (工程部)
  - `dept_warehouse_PRD` (生产部)
  - `dept_warehouse_QC` (质检部)
  - `dept_warehouse_HR` (人事部)

#### 4.2.2 用户配置要求
- **角色分配**: 统一分配 `dept_warehouse` 角色
- **部门绑定**: 用户只能访问所属部门数据
- **权限范围**: 仅限物品领取和记录查看
- **账号状态**: 默认启用状态

### 4.3 ITEM_USAGE_RECORD (物品使用记录表)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| record_id | VARCHAR | 50 | NOT NULL | - | 记录唯一标识，主键 |
| item_id | VARCHAR | 50 | NOT NULL | - | 物品ID |
| department_id | VARCHAR | 50 | NOT NULL | - | 部门ID |
| employee_id | VARCHAR | 50 | NOT NULL | - | 员工ID |
| quantity | DECIMAL(15,4) | - | NOT NULL | - | 领取数量（支持小数） |
| unit | VARCHAR | 20 | NOT NULL | - | 领取单位 |
| usage_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 使用时间 |
| operator_id | VARCHAR | 50 | NOT NULL | - | 操作员ID（部门仓库用户） |
| inventory_change_id | VARCHAR | 50 | NULL | - | 关联的库存变更记录ID |
| notes | TEXT | - | NULL | - | 备注信息 |
| created_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |

**索引设计**:
- 主键索引: `record_id`
- 普通索引: `item_id`, `department_id`, `employee_id`, `usage_time`, `operator_id`
- 外键索引: `inventory_change_id`

**约束说明**:
- 记录ID唯一性约束
- 数量必须大于0
- 相关ID存在性验证（代码层面）
- 操作员ID必须具有 `dept_warehouse` 角色
- 库存变更记录ID关联到 `InventoryChangeRecord` 表

### 4.4 相关表引用

#### 4.4.1 DEPARTMENT (部门表)
- 引用字段: `department_id`
- 关联关系: 部门与用户、库存、使用记录的一对多关系

#### 4.4.2 ITEM (物品表)
- 引用字段: `item_id`
- 关联关系: 物品与库存、使用记录的一对多关系

#### 4.4.3 INVENTORY (库存表)
- 引用字段: `inventory_id`
- 关联关系: 库存与部门、物品的多对一关系

#### 4.4.4 EMPLOYEE (员工表)
- 引用字段: `employee_id`
- 关联关系: 员工与使用记录的一对多关系

#### 4.4.5 USER (用户表)
- 引用字段: `user_id`
- 关联关系: 用户与使用记录的一对多关系，用户通过角色获得权限

#### 4.4.6 ROLE (角色表)
- 引用字段: `role_id`
- 关联关系: 角色与用户的多对一关系，定义用户权限范围

#### 4.4.7 INVENTORY_CHANGE_RECORD (库存变更记录表)
- 引用字段: `id`
- 关联关系: 物品领用时自动创建变更记录，记录类型为 `pickup_out`
- **同步记录**: 每次物品领取操作必须同时记录到此表
- **数据关联**: 通过 `inventory_change_id` 字段与使用记录表建立关联关系

## 5. 业务规则

### 5.1 业务约束和限制

#### 5.1.1 用户权限约束
- **角色要求**: 用户必须具有 `dept_warehouse` 角色才能进行物品领取操作
- **部门绑定**: 用户只能操作所属部门的数据，不能跨部门操作
- **权限范围**: 仅限物品领取、记录查看、库存查询、员工验证等基本功能

#### 5.1.2 库存管理约束
- **库存验证**: 领取数量不能超过当前可用库存数量
- **单位一致性**: 领取单位必须与库存单位一致，支持单位换算显示
- **实时更新**: 领取操作完成后必须立即更新库存数量
- **双重记录**: 必须同时记录到两个表：
  - `ITEM_USAGE_RECORD`: 使用行为记录
  - `InventoryChangeRecord`: 库存变更记录（类型为 `pickup_out`）
- **数量精度**: 支持小数数量，精度为4位小数
- **事务一致性**: 库存更新和双重记录必须在同一事务中完成

#### 5.1.3 权限控制约束
- **部门隔离**: 用户只能访问所属部门的库存和使用记录
- **操作权限**: 只有具有 `dept_warehouse` 角色的用户才能进行物品领取操作
- **数据可见性**: 用户只能查看和操作所属部门的数据
- **权限继承**: 通过角色继承获得物品领取相关权限

### 5.2 异常处理规则

#### 5.2.1 网络异常处理
- **连接失败**: 网络连接失败时显示错误提示，支持重试操作
- **超时处理**: 操作超时时自动重试，超过重试次数后提示用户
- **离线模式**: 不支持离线操作，网络恢复后需要重新验证

#### 5.2.2 数据异常处理
- **库存不足**: 库存不足时阻止领取，显示当前可用库存
- **物品不存在**: 扫描无效二维码时提示物品不存在
- **员工验证失败**: 员工信息验证失败时要求重新扫描或输入

#### 5.2.3 用户权限异常处理
- **权限不足**: 用户权限不足时提示联系管理员分配相应角色
- **部门不匹配**: 尝试操作其他部门数据时提示权限错误
- **角色失效**: 用户角色被禁用时提示联系管理员

### 5.3 权限和安全要求

#### 5.3.1 认证安全
- **登录验证**: 必须使用有效的部门仓库账号登录
- **角色验证**: 验证用户是否具有 `dept_warehouse` 角色
- **部门绑定**: 用户与部门账号绑定，防止跨部门使用

#### 5.3.2 数据安全
- **数据隔离**: 严格按部门隔离数据，防止跨部门数据泄露
- **操作审计**: 记录所有物品领取操作的详细日志
- **敏感信息保护**: 员工个人信息加密存储

#### 5.3.3 操作安全
- **权限验证**: 每次操作前验证用户角色和部门权限
- **数据完整性**: 确保数据操作的原子性和一致性
- **异常监控**: 监控异常操作行为和权限违规，及时告警

### 5.4 性能和质量要求

#### 5.4.1 响应时间要求
- **页面加载**: 页面加载时间 < 3 秒
- **扫码响应**: 二维码扫描响应时间 < 2 秒
- **数据提交**: 领取操作提交响应时间 < 1 秒

#### 5.4.2 可用性要求
- **系统可用性**: 工作时间可用性 > 99%
- **故障恢复**: 单点故障恢复时间 < 5 分钟
- **数据备份**: 每日自动备份，支持数据恢复

#### 5.4.3 兼容性要求
- **浏览器兼容**: 支持 Chrome、Firefox、Safari 等主流浏览器
- **设备兼容**: 支持 Android 5.0+ 系统
- **屏幕适配**: 支持不同分辨率的平板和手机设备

## 6. 技术实现要点

### 6.1 Web摄像头集成
- **技术方案**: 使用 WebRTC getUserMedia API 调用设备摄像头
- **兼容性**: 支持 HTTPS 环境下的摄像头访问
- **用户体验**: 提供摄像头权限管理界面

### 6.2 二维码识别
- **技术方案**: 集成 jsQR 或 ZXing 等二维码识别库
- **识别精度**: 支持多种二维码格式，识别准确率 > 95%
- **性能优化**: 实时视频流处理，优化识别性能

### 6.3 响应式设计
- **布局适配**: 使用 CSS Grid 和 Flexbox 实现响应式布局
- **触摸优化**: 针对触摸操作优化按钮尺寸和间距
- **视觉反馈**: 提供清晰的操作反馈和状态提示

### 6.4 用户权限管理
- **角色权限**: 通过 `dept_warehouse` 角色统一管理权限
- **部门隔离**: 基于用户部门实现数据访问控制
- **权限缓存**: 缓存用户权限信息，减少权限验证开销

### 6.5 数据同步机制
- **双重记录机制**: 
  - 物品领取时同时记录到两个表
  - `ITEM_USAGE_RECORD`: 记录使用行为（谁、什么时候、用了什么、用了多少）
  - `InventoryChangeRecord`: 记录库存变更（库存从多少变为多少）
- **数据关联**: 两表通过 `inventory_change_id` 字段建立关联关系
- **事务保证**: 使用数据库事务确保三个操作的一致性：
  1. 库存数量更新
  2. 使用记录创建
  3. 变更记录创建
- **数据完整性**: 通过双重记录实现完整的业务追溯链

### 6.6 数据查询优化
- **关联查询**: 支持通过关联字段进行双向数据查询
- **索引优化**: 为关联字段建立合适的索引，提升查询性能
- **缓存策略**: 对常用查询结果进行缓存，减少数据库访问

## 7. 部署和运维

### 7.1 部署要求
- **网络环境**: 公司内网环境，支持 WiFi 连接
- **服务器要求**: 与主系统部署在同一网络环境

---

*本文档定义了领取物品客户端的完整需求，包括功能需求、技术实现、业务规则等各个方面，为系统开发和部署提供详细指导。*
