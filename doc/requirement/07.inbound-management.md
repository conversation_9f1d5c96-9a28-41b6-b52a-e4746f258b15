# Inbound入库管理模块需求文档

## 1. 需求概述

### 业务背景
在工厂物品管理系统中，物品采购完成后需要进行入库操作，将采购的物品正式纳入部门库存管理。传统的入库流程需要手动录入物品信息，效率低下且容易出错。通过扫码入库功能，可以快速识别采购申请单中的物品信息，提高入库效率和准确性。

### 核心目标
- 提供高效的扫码入库功能，支持仓库管理员快速完成物品入库操作
- 通过扫描采购申请单二维码，快速定位部门、物品和数量信息
- 支持灵活的入库数量调整，满足实际入库需求
- 完整记录库存变更历史，支持审计追溯

### 系统定位
Inbound应用是系统的独立客户端应用之一，专门为仓库管理员提供入库操作界面。该应用与采购申请模块松耦合，采购申请单仅作为快速定位参考，不强制关联入库状态。

## 2. 功能需求

### 2.1 用户认证与权限

#### 2.1.1 用户角色要求
- **主要用户**: warehouse_keeper（仓库管理员）
- **权限要求**: 
  - `item.read`: 读取物品信息
  - `inventory.read`: 读取库存信息
  - `inventory.update`: 更新库存数量
  - `purchase.read`: 读取采购申请信息

#### 2.1.2 应用访问控制
- 仅限warehouse_keeper角色用户访问
- 支持给所有部门进行入库操作，无部门权限限制
- 基于JWT Token的身份验证

### 2.2 扫码查询功能

#### 2.2.1 二维码扫描
- **扫描对象**: 采购申请单（PurchaseRequest）上的二维码
- **扫描方式**: 支持扫码枪输入或手动输入二维码内容
- **二维码内容**: 包含采购申请单ID或唯一标识符

#### 2.2.2 申请单信息查询
- **查询内容**: 
  - 申请部门信息
  - 申请物品列表（名称、编码、申请数量、采购单位）
  - 申请状态和提交时间
- **数据来源**: 采购申请单（PurchaseRequest）和申请明细（PurchaseRequestItem）
- **验证要求**: 检查物品是否在系统中已定义

### 2.3 入库操作功能

#### 2.3.1 物品列表展示
- **显示格式**: 表格形式展示所有申请物品
- **显示字段**: 
  - 物品名称和编码
  - 申请数量（采购单位）
  - 采购单位
  - 库存单位
  - 可编辑的入库数量字段
  - 操作按钮（移除物品）

#### 2.3.2 数量调整功能
- **数量修改**: 支持修改每个物品的入库数量
- **数量限制**: 入库数量可以超过申请数量，无强制限制
- **单位换算**: 入库使用采购单位，系统自动转换为库存单位存储
- **数量验证**: 入库数量必须为正数

#### 2.3.3 物品管理
- **移除物品**: 支持移除不需要入库的物品
- **批量操作**: 一次性确认所有物品的入库数量
- **操作确认**: 入库前显示最终确认界面

### 2.4 入库执行功能

#### 2.4.1 入库操作
- **执行方式**: 一次性入库所有确认的物品
- **库存更新**: 更新各部门的库存数量
- **变更记录**: 创建库存变更记录（InventoryChangeRecord）
- **事务处理**: 确保所有物品入库成功或全部回滚

#### 2.4.2 结果反馈
- **成功提示**: 显示入库成功的物品和数量
- **错误处理**: 显示入库失败的物品和错误原因
- **结果汇总**: 显示本次入库的完整结果汇总

### 2.5 数据记录功能

#### 2.5.1 库存变更记录
- **记录内容**: 
  - 变更前后的库存数量
  - 变更类型（manual_in）
  - 操作员信息
  - 变更时间
  - 关联的采购申请单信息
- **记录格式**: 符合InventoryChangeRecord表结构

#### 2.5.2 操作日志
- **日志内容**: 记录入库操作的完整过程
- **审计支持**: 支持后续的审计和追溯查询

## 3. 业务对象设计

### 3.1 业务对象关系图

```mermaid
erDiagram
    User {
        int id PK
        string username
        string full_name
        int department_id FK
        int role_id FK
        boolean is_active
    }
    
    PurchaseRequest {
        int id PK
        string request_no
        int department_id FK
        int submitter_id FK
        string status
        datetime submitted_at
        string qr_code
    }
    
    PurchaseRequestItem {
        int id PK
        int request_id FK
        int item_id FK
        int requested_quantity
        string unit
        decimal estimated_price
    }
    
    Item {
        int id PK
        string name
        string code
        string purchase_unit
        string inventory_unit
        int category_id FK
    }
    
    Department {
        int id PK
        string name
        string code
    }
    
    DepartmentInventory {
        int id PK
        int department_id FK
        int item_id FK
        decimal current_quantity
        string unit
        datetime last_updated
    }
    
    InventoryChangeRecord {
        int id PK
        int department_id FK
        int item_id FK
        decimal before_quantity
        decimal after_quantity
        decimal change_quantity
        string change_type
        int operator_id FK
        datetime change_date
        int purchase_request_id FK
    }
    
    User ||--o{ PurchaseRequest : "submits"
    Department ||--o{ PurchaseRequest : "owns"
    PurchaseRequest ||--o{ PurchaseRequestItem : "contains"
    PurchaseRequestItem ||--o{ Item : "references"
    Department ||--o{ DepartmentInventory : "has"
    Item ||--o{ DepartmentInventory : "stored_in"
    User ||--o{ InventoryChangeRecord : "operates"
    Department ||--o{ InventoryChangeRecord : "affected"
    Item ||--o{ InventoryChangeRecord : "changed"
    PurchaseRequest ||--o{ InventoryChangeRecord : "triggers"
```

### 3.2 业务对象说明

#### 3.2.1 核心业务对象
- **PurchaseRequest**: 采购申请单，包含申请部门、提交人、申请状态等信息
- **PurchaseRequestItem**: 采购申请明细，包含具体物品、申请数量、单位等信息
- **Item**: 物品信息，包含名称、编码、采购单位、库存单位等
- **Department**: 部门信息，入库的目标部门
- **DepartmentInventory**: 部门库存，记录各部门的物品库存数量
- **InventoryChangeRecord**: 库存变更记录，记录所有入库操作的详细信息

#### 3.2.2 业务关系描述
- 一个采购申请单属于一个部门，包含多个申请物品
- 每个申请物品对应一个具体的物品
- 入库操作会更新目标部门的库存数量
- 每次入库操作都会创建库存变更记录
- 库存变更记录关联采购申请单，便于追溯

## 4. 数据库表设计

### 4.1 主要表结构

#### 4.1.1 purchase_requests（采购申请单表）
```sql
CREATE TABLE purchase_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_no VARCHAR(50) UNIQUE NOT NULL,
    department_id INTEGER NOT NULL,
    submitter_id INTEGER NOT NULL,
    status VARCHAR(30) DEFAULT 'pending_submission',
    final_total DECIMAL(15,2),
    notes TEXT,
    qr_code VARCHAR(500),
    submitted_at DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_purchase_requests_no (request_no),
    INDEX idx_purchase_requests_dept (department_id),
    INDEX idx_purchase_requests_submitter (submitter_id),
    INDEX idx_purchase_requests_status (status),
    INDEX idx_purchase_requests_date (submitted_at)
);
```

#### 4.1.2 purchase_request_items（采购申请明细表）
```sql
CREATE TABLE purchase_request_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_id INTEGER NOT NULL,
    item_id INTEGER NOT NULL,
    item_code VARCHAR(20) NOT NULL,
    item_name VARCHAR(200) NOT NULL,
    requested_quantity INTEGER NOT NULL,
    unit VARCHAR(20) NOT NULL,
    estimated_price DECIMAL(10,4),
    recommended_supplier_id INTEGER,
    remarks TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_purchase_request_items_request (request_id),
    INDEX idx_purchase_request_items_item (item_id),
    INDEX idx_purchase_request_items_status (status)
);
```

#### 4.1.3 items（物品表）
```sql
CREATE TABLE items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(200) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    category_id INTEGER,
    purchase_unit VARCHAR(20) NOT NULL,
    inventory_unit VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_items_code (code),
    INDEX idx_items_category (category_id),
    INDEX idx_items_active (is_active)
);
```

#### 4.1.4 department_inventories（部门库存表）
```sql
CREATE TABLE department_inventories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    department_id INTEGER NOT NULL,
    item_id INTEGER NOT NULL,
    current_quantity DECIMAL(15,4) NOT NULL DEFAULT 0,
    min_quantity DECIMAL(15,4) DEFAULT 0,
    max_quantity DECIMAL(15,4),
    storage_location VARCHAR(100),
    rack_number VARCHAR(50),
    last_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(department_id, item_id),
    INDEX idx_department_inventories_department_id (department_id),
    INDEX idx_department_inventories_item_id (item_id),
    INDEX idx_department_inventories_last_updated (last_updated)
);
```

#### 4.1.5 inventory_change_records（库存变更记录表）
```sql
CREATE TABLE inventory_change_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    department_id INTEGER NOT NULL,
    item_id INTEGER NOT NULL,
    before_quantity DECIMAL(15,4) NOT NULL,
    after_quantity DECIMAL(15,4) NOT NULL,
    change_quantity DECIMAL(15,4) NOT NULL,
    change_type VARCHAR(20) NOT NULL,
    change_reason VARCHAR(100),
    operator_id INTEGER NOT NULL,
    change_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    purchase_request_id INTEGER,
    remarks TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_inventory_change_records_department_id (department_id),
    INDEX idx_inventory_change_records_item_id (item_id),
    INDEX idx_inventory_change_records_change_type (change_type),
    INDEX idx_inventory_change_records_operator_id (operator_id),
    INDEX idx_inventory_change_records_change_date (change_date),
    INDEX idx_inventory_change_records_purchase_request (purchase_request_id)
);
```

### 4.2 索引和约束

#### 4.2.1 主要索引
- 采购申请单号唯一索引
- 采购申请单部门索引
- 采购申请单状态索引
- 物品编码唯一索引
- 部门库存复合唯一索引
- 库存变更记录复合索引

#### 4.2.2 约束说明
- 采购申请单号必须唯一
- 物品编码必须唯一
- 部门库存按部门+物品唯一
- 入库数量必须为正数
- 库存数量不能为负数

### 4.3 字段说明

#### 4.3.1 关键字段说明
- **qr_code**: 采购申请单二维码，用于快速识别
- **purchase_unit**: 采购单位，入库时使用的单位
- **inventory_unit**: 库存单位，系统存储时使用的单位
- **change_type**: 变更类型，入库操作固定为"manual_in"
- **purchase_request_id**: 关联的采购申请单ID，用于追溯

#### 4.3.2 数量字段精度
- 库存数量：支持小数点后4位精度
- 变更数量：支持小数点后4位精度
- 申请数量：整数，无小数精度要求

## 5. 业务规则

### 5.1 入库业务规则

#### 5.1.1 数量规则
- **入库数量**: 必须为正数，支持小数
- **数量限制**: 可以超过申请数量，无强制限制
- **单位换算**: 入库使用采购单位，系统自动转换为库存单位
- **精度要求**: 支持小数点后4位精度

#### 5.1.2 物品规则
- **物品验证**: 入库前必须验证物品是否在系统中存在
- **物品状态**: 只允许入库状态为激活的物品
- **分类支持**: 支持所有物品分类的入库操作

#### 5.1.3 部门规则
- **部门权限**: 仓库管理员可以给任何部门入库
- **部门验证**: 入库前必须验证目标部门是否存在且有效
- **库存初始化**: 如果部门没有该物品的库存记录，自动创建

### 5.2 操作流程规则

#### 5.2.1 扫码流程
- **二维码格式**: 必须符合系统定义的二维码格式
- **申请单状态**: 不限制申请单状态，任何状态的申请单都可以扫码
- **数据完整性**: 扫码后必须能完整获取申请单信息

#### 5.2.2 确认流程
- **数量确认**: 每个物品的入库数量必须明确确认
- **物品确认**: 可以移除不需要入库的物品
- **批量确认**: 所有物品确认完成后才能执行入库

#### 5.2.3 入库流程
- **事务处理**: 入库操作必须在一个事务中完成
- **库存更新**: 先更新库存数量，再创建变更记录
- **错误处理**: 任何步骤失败都必须回滚整个操作

### 5.3 数据完整性规则

#### 5.3.1 库存一致性
- **数量计算**: 变更后数量 = 变更前数量 + 变更数量
- **历史追溯**: 每次变更都必须记录变更前后的数量
- **数据同步**: 库存数量和变更记录必须保持同步

#### 5.3.2 关联关系
- **申请单关联**: 库存变更记录必须关联采购申请单
- **操作员记录**: 必须记录执行入库操作的用户
- **时间记录**: 必须记录入库操作的具体时间

### 5.4 异常处理规则

#### 5.4.1 业务异常
- **物品不存在**: 提示错误，不允许入库
- **部门不存在**: 提示错误，不允许入库
- **数量无效**: 提示错误，要求重新输入

#### 5.4.2 系统异常
- **网络异常**: 提示网络错误，建议重试
- **数据库异常**: 记录错误日志，提示系统错误
- **权限异常**: 提示权限不足，拒绝操作

### 5.5 权限和安全要求

#### 5.5.1 用户权限
- **角色限制**: 仅限warehouse_keeper角色用户访问
- **功能权限**: 需要inventory.update权限才能执行入库
- **数据权限**: 可以访问所有部门的数据

#### 5.5.2 操作安全
- **身份验证**: 每次操作都必须验证用户身份
- **操作审计**: 所有入库操作都必须记录操作日志
- **数据保护**: 敏感数据必须加密存储和传输

#### 5.5.3 系统安全
- **接口安全**: API接口必须进行权限验证
- **数据验证**: 所有输入数据必须进行格式和业务验证
- **错误处理**: 系统错误不能暴露敏感信息

## 6. 技术实现要点

### 6.1 前端技术要点
- **响应式设计**: 支持PC浏览器操作
- **状态管理**: 使用URL保存页面状态
- **表单验证**: 实时验证用户输入
- **错误处理**: 友好的错误提示和引导

### 6.2 后端技术要点
- **API设计**: RESTful API设计，支持批量操作
- **事务管理**: 确保入库操作的原子性
- **异常处理**: 统一的异常处理和错误响应
- **性能优化**: 支持高并发的入库操作

### 6.3 数据库技术要点
- **索引优化**: 针对查询场景优化索引设计
- **事务隔离**: 确保并发操作的数据一致性
- **数据备份**: 支持数据备份和恢复
- **性能监控**: 监控数据库性能指标

## 7. 验收标准

### 7.1 功能验收
- 扫码功能正常，能正确识别采购申请单
- 入库操作成功，库存数量正确更新
- 变更记录完整，支持审计追溯
- 错误处理完善，用户操作友好

### 7.2 性能验收
- 扫码响应时间 < 1秒
- 入库操作响应时间 < 3秒
- 支持并发用户数 > 10
- 系统可用性 > 99.9%

### 7.3 安全验收
- 用户身份验证有效
- 权限控制正确
- 操作日志完整
- 数据安全保护到位
