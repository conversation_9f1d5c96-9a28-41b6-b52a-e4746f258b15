# 采购申请模块需求文档

## 1. 需求概述

### 1.1 业务背景
公司各部门在日常运营中需要采购各种物品，包括办公用品、生产耗材、设备配件等。为了提高采购效率，规范采购流程，需要建立统一的采购申请管理系统。

### 1.2 核心目标
- 简化采购申请流程，提高采购效率
- 建立四级审批机制，确保采购决策的合理性
- 提供完整的采购记录和审计追踪
- 实现申请阶段实时价格计算，最终审批后锁定价格
- 采用SPQ（标准包装数量）计数方式，提高采购标准化

### 1.3 系统定位
采购申请模块是公司采购管理系统的核心组件，负责从需求收集到申请审批的完整流程管理。

## 2. 功能需求

### 2.1 采购申请管理

#### 2.1.1 申请创建
- **购物车发起**: 采购申请只能由购物车发起，不能直接创建
- **申请信息**: 包含业务说明、优先级、期望交期等基本信息
- **申请明细**: 支持添加、删除、修改物品SPQ个数
- **SPQ管理**: 数量分为SPQ（标准包装数量）和SPQ个数两个字段
- **实时价格**: 申请阶段实时获取最优先供应商价格，不存储预估价格

#### 2.1.2 申请状态管理
- **草稿**: 申请创建后的初始状态，可以编辑修改和删除
- **待提交**: 申请创建但未提交的状态，可以编辑修改和删除
- **待审批**: 申请提交后的审批状态，不可编辑，不可删除，但可撤回
- **主管审批中**: 进入主管审批环节，不可编辑，不可删除，但可撤回
- **最终审批中**: 进入最终审批环节，不可编辑，不可删除，但可撤回
- **已批准**: 申请通过所有审批环节，不可编辑，不可删除
- **已拒绝**: 申请在任一审批环节被拒绝，可以编辑修改和删除
- **已撤回**: 申请被撤回的状态，可以编辑修改和删除

#### 2.1.3 审批流程
- **四级审批**: 部门物品管理员提交 → 部门经理复核 → 物品管理员主管审批 → 公司主管最终审批
- **审批操作**: 每个审批环节只有"通过"和"不通过"两个选项
- **状态转换**: 审批通过后进入下一级，审批不通过后状态变为"已拒绝"
- **流转历史**: 记录每个审批环节的操作人、操作时间、审批意见等
- **价格锁定**: 最终审批通过后自动锁定最终价格

#### 2.1.4 申请修改
- **编辑权限**: 只有在"草稿"、"待提交"和"已拒绝"状态下可以编辑
- **删除权限**: 只有在"草稿"、"待提交"、"已拒绝"和"已撤回"状态下可以删除
- **撤回权限**: 只有在"待审批"、"主管审批中"、"最终审批中"状态下可以撤回
- **修改内容**: 可以修改申请信息、添加/删除物品、调整SPQ个数等
- **状态恢复**: 已拒绝的申请修改后自动变为"待提交"状态

#### 2.1.5 状态流转图
```mermaid
stateDiagram-v2
    [*] --> 草稿
    草稿 --> 待提交 : 创建申请
    待提交 --> 待审批 : 提交申请
    待提交 --> 草稿 : 保存草稿
    待审批 --> 主管审批中 : 部门经理通过
    待审批 --> 已拒绝 : 部门经理拒绝
    主管审批中 --> 最终审批中 : 主管通过
    主管审批中 --> 已拒绝 : 主管拒绝
    最终审批中 --> 已批准 : 公司主管通过
    最终审批中 --> 已拒绝 : 公司主管拒绝
    待审批 --> 待提交 : 撤回申请
    主管审批中 --> 待提交 : 撤回申请
    最终审批中 --> 待提交 : 撤回申请
    已拒绝 --> 待提交 : 修改申请
    已撤回 --> 待提交 : 重新提交
    草稿 --> [*] : 删除申请
    待提交 --> [*] : 删除申请
    已拒绝 --> [*] : 删除申请
    已撤回 --> [*] : 删除申请
```

### 2.2 价格管理

#### 2.2.1 实时价格计算
- **申请阶段**: 每次查看申请时实时获取最优先供应商的当前有效价格
- **价格匹配**: 基于申请数量（SPQ × SPQ个数），匹配相应的阶梯价格区间
- **供应商优先级**: 按供应商优先级排序，priority = 0 为最高优先级
- **有效期检查**: 只获取当前有效的价格，检查 valid_from 和 valid_to 时间范围

#### 2.2.2 最终价格锁定
- **锁定时机**: 最终审批通过时自动锁定最终价格
- **存储位置**: 在申请明细表中直接存储最终价格信息
- **价格信息**: 包含最终单价、总价、供应商ID、锁定时间等

#### 2.2.3 价格计算规则
- **数量区间匹配**: 优先匹配申请数量（SPQ × SPQ个数）所在的价格区间
- **阶梯价格**: 支持不同数量区间的阶梯价格设置
- **运费计算**: 包含运费在内的总价计算
- **数量警告**: 当数量超出价格区间时显示警告信息

### 2.3 SPQ计数管理

#### 2.3.1 SPQ定义
- **标准包装数量**: 每个物品的标准包装数量，如1piece、0.5L等
- **SPQ个数**: 需要多少个标准包装，为整数值
- **显示格式**: 显示为"SPQ个数 × SPQ"格式，如"10 × 0.5L"

#### 2.3.2 SPQ获取逻辑
- **添加购物车时**: 从最优先供应商获取该物品的SPQ信息
- **价格计算**: 基于SPQ × SPQ个数计算总数量进行价格匹配
- **审批流程**: 整个审批流程都使用SPQ计数方式

#### 2.3.3 SPQ标准化
- **统一计数**: 所有采购相关流程都使用SPQ计数方式
- **减少混淆**: 避免不同单位之间的换算问题
- **提高效率**: 标准化计数方式提高采购效率

### 2.4 四级审批流程
- **部门物品管理员提交**: 部门物品管理员根据消耗情况提交采购申请
- **部门经理复核**: 第一级审批，部门经理对申请进行初步审核
- **物品管理员主管审批**: 第二级审批，主管对申请进行业务合理性评估
- **公司主管最终审批**: 第三级审批，最终决策者进行最终审批
- **流转历史**: 完整记录每级审批的意见和结果



### 2.6 数据保护
- **物品信息**: 记录申请时的物品基本信息
- **流转历史**: 记录审批时的申请状态和意见
- **价格历史**: 通过最终价格字段记录审批后的价格信息
- **SPQ信息**: 记录申请时的SPQ和SPQ个数信息

## 3. 业务对象设计

### 3.1 ER图
```mermaid
erDiagram
    PurchaseCartItem ||--o{ PurchaseRequestItem : "creates"
    PurchaseRequest ||--o{ PurchaseRequestItem : "contains"
    PurchaseRequest ||--o{ RequestFlowHistory : "has"
    PurchaseOrder ||--o{ PurchaseOrderItem : "contains"
    
    PurchaseCartItem {
        int id PK
        int department_id
        int item_id
        decimal spq_quantity
        int spq_count
        string spq_unit
        text notes
        int created_by
        datetime created_at
        datetime updated_at
    }
    
    PurchaseRequest {
        int id PK
        string request_no
        string request_uuid
        int department_id
        int submitter_id
        string status
        decimal final_total
        text notes
        string qr_code
        datetime submitted_at
        datetime created_at
        datetime updated_at
    }
    
    PurchaseRequestItem {
        int id PK
        int request_id FK
        int item_id
        string item_code
        string item_name
        decimal spq_quantity
        int spq_count
        string spq_unit
        decimal final_unit_price
        decimal final_total_price
        int final_supplier_id
        datetime price_locked_at
        text notes
        datetime created_at
        datetime updated_at
    }
    
    RequestFlowHistory {
        int id PK
        int request_id FK
        string action
        string from_status
        string to_status
        int operator_id
        string operator_name
        string approval_level
        text comments
        datetime created_at
    }
    

    
    PurchaseOrder {
        int id PK
        int batch_id FK
        string order_no
        string order_uuid
        int request_id FK
        string status
        decimal total_amount
        datetime order_date
        datetime created_at
        datetime updated_at
    }
    
    PurchaseOrderItem {
        int id PK
        int order_id FK
        int request_item_id FK
        int item_id
        decimal spq_quantity
        int spq_count
        string spq_unit
        decimal unit_price
        decimal total_price
        datetime created_at
        datetime updated_at
    }
```

### 3.2 业务对象说明

#### 3.2.1 购物车项目 (PurchaseCartItem)
- **简化设计**: 直接代表各部门的购物车，无需单独的购物车表
- **唯一约束**: 每个部门对同一物品只能有一个购物车项目
- **SPQ管理**: 包含SPQ数量、SPQ个数和SPQ单位三个字段
- **自动管理**: 系统自动管理购物车项目的生命周期

#### 3.2.2 采购申请 (PurchaseRequest)
- **来源**: 直接从购物车项目创建，无需通过购物车表
- **状态流转**: 支持完整的状态生命周期管理
- **流转关联**: 与流转历史建立关联关系
- **价格管理**: 不存储预估价格，最终审批后锁定最终总金额

#### 3.2.3 采购申请明细 (PurchaseRequestItem)
- **SPQ计数**: 使用SPQ数量、SPQ个数和SPQ单位三个字段管理数量
- **实时价格**: 申请阶段不存储预估价格，每次查看时实时计算
- **最终价格**: 最终审批通过后锁定最终价格信息
- **价格信息**: 包含最终单价、总价、供应商ID、锁定时间等
- **物品信息**: 记录物品的基本信息，不包含价格快照

#### 3.2.4 申请流转历史 (RequestFlowHistory)
- **完整记录**: 记录申请从创建到最终结果的所有操作历史
- **操作类型**: 支持提交、审批、拒绝、撤销、编辑等各种操作类型
- **状态追踪**: 记录每次操作前后的状态变更
- **操作人信息**: 记录每次操作的操作人ID和姓名
- **审批级别**: 对于审批操作，记录相应的审批级别
- **操作说明**: 记录操作说明或审批意见
- **时间戳**: 记录每次操作的确切时间

### 3.3 实体关系描述
- **购物车项目 → 采购申请**: 一个或多个购物车项目可以创建一个采购申请
- **采购申请 → 申请明细**: 一个申请包含多个申请明细
- **采购申请 → 流转历史**: 一个申请有完整的流转历史记录，包括提交、审批、拒绝、撤销等所有操作


## 4. 数据库表设计

### 4.1 购物车项目表 (purchase_cart_items)
```sql
CREATE TABLE purchase_cart_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    department_id INTEGER NOT NULL COMMENT '部门ID',
    item_id INTEGER NOT NULL COMMENT '物品ID',
    spq_quantity DECIMAL(10,3) NOT NULL COMMENT 'SPQ数量（标准包装数量）',
    spq_count INTEGER NOT NULL COMMENT 'SPQ个数（需要多少个标准包装）',
    spq_unit VARCHAR(20) NOT NULL COMMENT 'SPQ单位',
    requirement_notes TEXT COMMENT '需求说明',
    expected_delivery DATE COMMENT '期望交期',
    created_by INTEGER NOT NULL COMMENT '添加人ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_cart_items_department (department_id),
    INDEX idx_cart_items_item_id (item_id),
    UNIQUE INDEX idx_cart_items_unique (department_id, item_id),
    INDEX idx_cart_items_created_by (created_by)
);
```

### 4.2 采购申请表 (purchase_requests)
```sql
CREATE TABLE purchase_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_no VARCHAR(50) UNIQUE NOT NULL COMMENT '申请单号',
    request_uuid VARCHAR(36) UNIQUE NOT NULL COMMENT '申请唯一标识',
    department_id INTEGER NOT NULL COMMENT '申请部门ID',
    submitter_id INTEGER NOT NULL COMMENT '提交人ID',
    status VARCHAR(30) DEFAULT 'pending_submission' COMMENT '申请状态',
    final_total DECIMAL(15,2) COMMENT '最终总金额',
    business_justification TEXT COMMENT '业务说明',
    priority VARCHAR(20) DEFAULT 'normal' COMMENT '优先级',
    expected_delivery DATE COMMENT '期望交期',
    qr_code VARCHAR(500) COMMENT '申请二维码',
    submitted_at DATETIME COMMENT '提交时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_purchase_requests_no (request_no),
    INDEX idx_purchase_requests_dept (department_id),
    INDEX idx_purchase_requests_submitter (submitter_id),
    INDEX idx_purchase_requests_status (status),
    INDEX idx_purchase_requests_date (submitted_at)
);
```

### 4.3 采购申请明细表 (purchase_request_items)
```sql
CREATE TABLE purchase_request_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_id INTEGER NOT NULL COMMENT '申请ID',
    item_id INTEGER NOT NULL COMMENT '物品ID',
    item_code VARCHAR(20) NOT NULL COMMENT '物品编码',
    item_name VARCHAR(200) NOT NULL COMMENT '物品名称',
    spq_quantity DECIMAL(10,3) NOT NULL COMMENT 'SPQ数量（标准包装数量）',
    spq_count INTEGER NOT NULL COMMENT 'SPQ个数（需要多少个标准包装）',
    spq_unit VARCHAR(20) NOT NULL COMMENT 'SPQ单位',
    final_unit_price DECIMAL(12,4) COMMENT '最终单价',
    final_total_price DECIMAL(15,2) COMMENT '最终总价',
    final_supplier_id INTEGER COMMENT '最终供应商ID',
    price_locked_at DATETIME COMMENT '价格锁定时间',
    requirement_notes TEXT COMMENT '需求说明',
    expected_delivery DATE COMMENT '期望交期',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_request_items_request_id (request_id),
    INDEX idx_request_items_item_id (item_id),
    INDEX idx_request_items_code (item_code)
);
```

### 4.4 申请流转历史表 (request_flow_history)
```sql
CREATE TABLE request_flow_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_id INTEGER NOT NULL COMMENT '申请ID',
    action VARCHAR(50) NOT NULL COMMENT '操作类型: submit, resubmit, approve, reject, return, withdraw, edit',
    from_status VARCHAR(30) COMMENT '操作前状态',
    to_status VARCHAR(30) NOT NULL COMMENT '操作后状态',
    operator_id INTEGER NOT NULL COMMENT '操作人ID',
    operator_name VARCHAR(100) NOT NULL COMMENT '操作人姓名',
    approval_level VARCHAR(30) COMMENT '审批级别: review, principle_approval, final_approval (仅在审批操作时有效)',
    comments TEXT COMMENT '操作说明或审批意见',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    
    INDEX idx_flow_history_request_id (request_id),
    INDEX idx_flow_history_operator (operator_id),
    INDEX idx_flow_history_action (action),
    INDEX idx_flow_history_created_at (created_at)
);
```



### 4.6 采购订单表 (purchase_orders)
```sql
CREATE TABLE purchase_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_no VARCHAR(50) UNIQUE NOT NULL COMMENT '订单号',
    order_uuid VARCHAR(36) UNIQUE NOT NULL COMMENT '订单唯一标识',
    request_id INTEGER NOT NULL COMMENT '申请ID',
    status VARCHAR(30) DEFAULT 'pending' COMMENT '订单状态',
    total_amount DECIMAL(15,2) COMMENT '订单总金额',
    order_date DATETIME COMMENT '订单日期',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_purchase_orders_request (request_id),
    INDEX idx_purchase_orders_status (status)
);
```

### 4.7 采购订单明细表 (purchase_order_items)
```sql
CREATE TABLE purchase_order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id INTEGER NOT NULL COMMENT '订单ID',
    request_item_id INTEGER NOT NULL COMMENT '申请明细ID',
    item_id INTEGER NOT NULL COMMENT '物品ID',
    spq_quantity DECIMAL(10,3) NOT NULL COMMENT 'SPQ数量（标准包装数量）',
    spq_count INTEGER NOT NULL COMMENT 'SPQ个数（需要多少个标准包装）',
    spq_unit VARCHAR(20) NOT NULL COMMENT 'SPQ单位',
    unit_price DECIMAL(12,4) NOT NULL COMMENT '单价',
    total_price DECIMAL(15,2) NOT NULL COMMENT '总价',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_purchase_order_items_order (order_id),
    INDEX idx_purchase_order_items_request_item (request_item_id),
    INDEX idx_purchase_order_items_item (item_id)
);
```

## 5. 业务规则

### 5.1 购物车管理规则
- **部门唯一性**: 每个部门只有一个有效的购物车（通过购物车项目表实现）
- **物品唯一性**: 同一部门对同一物品只能有一个购物车项目
- **自动清理**: 提交申请后自动清空相关的购物车项目

### 5.2 采购申请规则
- **状态流转**: 申请状态必须按照预定义的流程进行流转
- **权限控制**: 只有申请人可以撤回自己的申请


#### 5.2.1 删除权限规则
- **可删除状态**: 草稿、待提交、已拒绝、已撤回状态的申请可以删除
- **不可删除状态**: 待审批、主管审批中、最终审批中、已批准状态的申请不能删除
- **删除原因**: 已进入审批流程的申请需要保护数据完整性和审批流程的完整性
- **权限控制**: 只有申请人可以删除自己的申请

### 5.3 流转历史规则
- **四级审批**: 必须按照部门物品管理员提交 → 部门经理复核 → 物品管理员主管审批 → 公司主管最终审批的顺序进行
- **审批权限**: 不同级别的审批需要相应的权限
- **流转记录**: 每次操作都必须记录详细信息，包括操作类型、状态变更、操作人、时间等
- **完整追踪**: 支持申请的全生命周期追踪，包括提交、审批、拒绝、撤销、重新提交等所有操作
- **状态一致性**: 流转历史中的状态变更必须与申请当前状态保持一致



### 5.5 数据完整性规则
- **流转历史**: 所有状态变更操作都记录在流转历史表中
- **审计追踪**: 所有操作都有完整的操作记录，包括操作人、时间、状态变更等
- **数据一致性**: 确保申请状态与流转历史记录的一致性
- **操作不可逆**: 流转历史记录一旦创建不可修改，确保审计完整性

## 6. 非功能性需求

### 6.1 性能要求
- **响应时间**: 页面加载时间不超过3秒
- **并发处理**: 支持100个用户同时操作
- **数据处理**: 支持10000条记录的快速查询

### 6.2 安全要求
- **权限控制**: 基于角色的访问控制
- **数据加密**: 敏感数据加密存储
- **操作审计**: 完整的操作日志记录

### 6.3 可用性要求
- **系统可用性**: 99.5%的系统可用性
- **数据备份**: 每日自动数据备份
- **故障恢复**: 故障恢复时间不超过4小时

### 6.4 扩展性要求
- **模块化设计**: 支持功能模块的独立扩展
- **接口标准化**: 提供标准化的API接口
- **配置化**: 支持业务规则的配置化调整

## 7. 接口设计

### 7.1 购物车管理接口
- `GET /api/admin/purchase/cart/department/{department_id}/items` - 获取部门购物车物品
- `POST /api/admin/purchase/cart/department/{department_id}/items` - 添加物品到购物车
- `PUT /api/admin/purchase/cart/items/{cart_item_id}` - 更新购物车项目
- `DELETE /api/admin/purchase/cart/items/{cart_item_id}` - 移除购物车项目
- `DELETE /api/admin/purchase/cart/department/{department_id}/clear` - 清空部门购物车
- `GET /api/admin/purchase/cart/department/{department_id}/summary` - 获取购物车摘要

### 7.2 采购申请接口
- `GET /api/admin/purchase/requests` - 获取申请列表
- `POST /api/admin/purchase/requests` - 创建采购申请
- `GET /api/admin/purchase/requests/{request_id}` - 获取申请详情
- `PUT /api/admin/purchase/requests/{request_id}` - 更新申请
- `DELETE /api/admin/purchase/requests/{request_id}` - 删除申请（仅限特定状态）
- `POST /api/admin/purchase/requests/{request_id}/submit` - 提交申请
- `POST /api/admin/purchase/requests/{request_id}/withdraw` - 撤回申请
- `GET /api/admin/purchase/requests/{request_id}/items` - 获取申请明细
- `GET /api/admin/purchase/requests/department/{department_id}` - 获取部门申请列表
- `PUT /api/admin/purchase/requests/{request_id}/status` - 更新申请状态
- `GET /api/admin/purchase/requests/summary` - 获取申请统计
- `GET /api/admin/purchase/requests/{request_id}/final-total` - 获取申请最终总金额

### 7.3 审批流程接口
- `GET /api/admin/purchase/approval/pending` - 获取待审批列表
- `POST /api/admin/purchase/approval/{request_id}/review` - 部门经理复核
- `POST /api/admin/purchase/approval/{request_id}/principle-approval` - 部门经理复核
- `POST /api/admin/purchase/approval/{request_id}/final-approval` - 物品管理员主管审批
- `POST /api/admin/purchase/approval/{request_id}/reject` - 拒绝申请
- `POST /api/admin/purchase/approval/{request_id}/return` - 退回申请
- `GET /api/admin/purchase/approval/{request_id}/history` - 获取流转历史
- `GET /api/admin/purchase/approval/summary` - 获取审批统计
- `GET /api/admin/purchase/approval/workflow/status` - 获取工作流状态说明



## 8. 用户界面要求

### 8.1 购物车界面
- **简化设计**: 直接展示购物车中的物品，无需复杂的购物车管理
- **SPQ显示**: 显示SPQ数量、SPQ个数和SPQ单位，格式为"SPQ个数 × SPQ"
- **快速操作**: 支持添加、编辑、删除物品的快速操作
- **实时统计**: 显示物品数量、总金额等统计信息
- **一键提交**: 支持将购物车物品直接提交为采购申请

### 8.2 采购申请界面
- **申请列表**: 支持多条件筛选和排序
- **申请详情**: 显示完整的申请信息和审批状态
- **SPQ信息**: 清晰显示每个物品的SPQ信息
- **状态管理**: 清晰显示申请状态和下一步操作
- **审批流程**: 可视化展示审批流程和进度

### 8.3 申请管理界面
- **状态显示**: 清晰显示申请状态和审批进度
- **操作按钮**: 根据申请状态动态显示相应的操作按钮
- **快速操作**: 支持添加、编辑、删除物品的快速操作

### 8.4 操作按钮显示规则
- **编辑按钮**: 只在草稿、待提交、已拒绝状态下显示
- **删除按钮**: 只在草稿、待提交、已拒绝、已撤回状态下显示
- **撤回按钮**: 只在待审批、主管审批中、最终审批中状态下显示
- **审批按钮**: 只在待审批、主管审批中、最终审批中状态下显示
- **查看详情**: 所有状态都显示
- **状态说明**: 每个操作按钮都有相应的权限说明和状态限制提示

### 8.5 审批工作台
- **待办列表**: 显示待审批的申请
- **审批操作**: 支持审批、拒绝、退回等操作
- **流转历史**: 查看完整的申请流转历史
- **批量操作**: 支持批量审批操作

### 8.6 流转历史显示
- **时间排序**: 按操作时间从早到晚排序显示
- **操作类型**: 清晰标识不同类型的操作（提交、审批、拒绝、撤销等）
- **状态变更**: 显示每次操作前后的状态变更
- **操作人信息**: 显示每次操作的操作人姓名
- **操作说明**: 显示操作说明或审批意见
- **颜色标识**: 使用不同颜色区分不同类型的操作



## 9. 测试要求

### 9.1 功能测试
- **购物车功能**: 测试添加、编辑、删除、清空等操作
- **SPQ管理**: 测试SPQ数量、SPQ个数和SPQ单位的正确性
- **申请流程**: 测试完整的申请创建和状态流转
- **审批流程**: 测试四级审批的完整流程
- **流转历史**: 测试所有操作的流转历史记录

- **删除权限**: 测试不同状态下申请删除权限的正确性
  - 测试草稿、待提交、已拒绝、已撤回状态可以删除
  - 测试待审批、主管审批中、最终审批中、已批准状态不能删除
  - 测试删除权限控制（只有申请人可以删除）

### 9.2 性能测试
- **并发测试**: 测试多用户同时操作的性能
- **数据量测试**: 测试大量数据的处理性能
- **响应时间测试**: 测试各种操作的响应时间

### 9.3 安全测试
- **权限测试**: 测试不同角色的权限控制
- **数据安全测试**: 测试数据的安全性和完整性
- **接口安全测试**: 测试API接口的安全性

## 10. 部署要求

### 10.1 环境要求
- **操作系统**: Linux/Windows Server
- **数据库**: SQLite/PostgreSQL/MySQL
- **应用服务器**: 支持Python 3.8+
- **内存要求**: 最少4GB内存
- **存储要求**: 最少50GB可用空间

### 10.2 部署方式
- **容器化部署**: 支持Docker容器化部署
- **传统部署**: 支持传统的服务器部署
- **云平台部署**: 支持主流云平台的部署

### 10.3 监控要求
- **系统监控**: 监控系统资源使用情况
- **应用监控**: 监控应用性能和错误
- **业务监控**: 监控关键业务指标
- **告警机制**: 建立完善的告警机制

## 11. 维护要求

### 11.1 日常维护
- **数据备份**: 定期进行数据备份
- **日志清理**: 定期清理系统日志
- **性能优化**: 定期进行性能优化
- **安全更新**: 及时更新安全补丁

### 11.2 故障处理
- **故障响应**: 建立故障响应机制
- **故障诊断**: 提供故障诊断工具
- **故障恢复**: 制定故障恢复流程
- **故障总结**: 进行故障分析和总结

### 11.3 版本管理
- **版本控制**: 使用版本控制系统管理代码
- **发布管理**: 建立规范的发布流程
- **回滚机制**: 支持版本回滚操作
- **变更记录**: 记录所有变更内容

## 12. 总结

本需求文档定义了采购申请模块的完整功能需求和技术要求。通过采用SPQ计数方式、简化购物车设计、优化审批流程、完善流转历史管理，系统将为公司提供高效、规范的采购管理解决方案。

**关键设计原则**:
1. **SPQ标准化**: 使用SPQ数量、SPQ个数和SPQ单位三个字段管理数量，提高采购标准化
2. **简化购物车**: 一个部门一个购物车，直接通过购物车项目表管理
3. **流程优化**: 四级审批流程，确保采购决策的合理性
4. **流转历史**: 完整的申请生命周期追踪，支持所有操作的审计记录
5. **用户友好**: 简洁的界面设计和便捷的操作体验
6. **系统稳定**: 高可用性和可扩展性的系统架构