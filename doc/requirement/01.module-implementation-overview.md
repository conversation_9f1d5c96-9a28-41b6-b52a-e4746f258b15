# BizLinkSpeedy IDM 模块实现概览

## 📊 实现顺序与优先级

| 优先级 | 模块名称 | 实现顺序 | 依赖关系 | 状态 |
|--------|----------|----------|----------|------|
| P0 | 用户管理 & 权限管理 | 1 | 无 | 📝 待创建文档 |
| P0 | 物品管理 & 分类管理 | 2 | 用户权限管理 | 📝 待创建文档 |
| P1 | 供应商管理 | 3 | 用户权限管理, 物品管理 | 📝 待创建文档 |
| P1 | 采购流程管理 | 5 | 用户权限管理, 物品管理, 供应商管理, 库存管理 | 📝 待创建文档 |
| P2 | 移动端领取系统 | 6 | 用户权限管理, 物品管理, 库存管理 | 📝 待创建文档 |
| P1 | 库存管理 | 4 | 用户权限管理, 物品管理 | 📝 待创建文档 |
| P2 | 报表分析系统 | 7 | 用户权限管理, 物品管理, 库存管理, 采购管理 | 📝 待创建文档 |


## 🏗️ 架构依赖关系图

```mermaid
flowchart TD
    A["👤 用户权限管理<br/>(User & Permission)"] 
    
    B["📦 物品管理<br/>(Item Management)"]
    C["🏢 供应商管理<br/>(Supplier Management)"]
    
    
    E["📋 库存管理<br/>(Inventory Management)"]
    
    F["🛒 采购流程管理<br/>(Purchase Management)"]
    
    G["📱 移动端领取系统<br/>(Mobile Pickup)"]
    H["📊 报表分析系统<br/>(Report & Analytics)"]

    
    %% 依赖关系
    A --> B
    A --> C

    
    B --> E
    C --> E
    
    E --> F
    
    F --> G
    F --> H
    
    %% 分层标识
    classDef infraLayer fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef coreLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef operationLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef processLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef appLayer fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    
    class A infraLayer
    class B,C,D coreLayer
    class E operationLayer
    class F processLayer
    class G,H appLayer
```

### 架构分层说明

- **🔵 基础设施层 (Infrastructure Layer)**: 用户权限管理 - 为所有模块提供认证授权基础
- **🟣 核心业务层 (Core Business Layer)**: 物品管理、供应商管理、通知系统 - 核心业务实体管理
- **🟢 运营管理层 (Operation Layer)**: 库存管理 - 连接业务实体与流程的运营层
- **🟠 业务流程层 (Process Layer)**: 采购流程管理 - 复杂业务流程编排
- **🔴 应用服务层 (Application Layer)**: 移动端、报表 - 面向用户的应用功能

## 📋 各模块核心功能矩阵

### 1. 用户管理 & 权限管理 (P0)
- **核心功能**: 认证授权、角色管理、数据权限、审计日志
- **数据库表**: users, roles, permissions, role_permissions, audit_logs
- **关键API**: `/api/admin/auth/*`, `/api/admin/users/*`, `/api/admin/roles/*`
- **技术要点**: JWT Token、bcrypt密码加密、部门数据隔离
- **新增功能**: 
  - 八种预设角色（参考pingcode项目，包含公司主管审批角色）
  - 用户显示名/备注名功能
  - 用户不跟人走，跟部门与角色走
  - 密码复杂度：内网环境不限制密码复杂度
  - 四级审批流程角色权限：部门经理（第一级）、物品管理员主管（第二级）、公司主管（第三级最终审批）
  - 公司主管角色设计：独立的最高业务决策角色，拥有采购申请最终审批权和全局报表查看权

### 2. 物品管理 & 分类管理 (P0)
- **核心功能**: 二级分类体系、属性配置、编码生成、全文搜索
- **数据库表**: item_categories, items, item_code_sequences, item_change_history
- **关键API**: `/api/admin/categories/*`, `/api/admin/items/*`
- **技术要点**: FTS5全文搜索、JSON属性存储、编码规则
- **新增功能**:
  - 二级分类：一级表示用途，二级根据特性分类
  - 属性挂载到二级分类
  - 物品和分类大小写不敏感处理
  - 物品不可购买状态
  - 双单位支持: `purchase_unit`(采购单位), `inventory_unit`(库存单位), `qty_per_up`(每采购单位包含的库存单位数量, int>=1)
  - 表单校验: 当单位不同需配置 `qty_per_up > 1`; 默认 `qty_per_up >= 1`
  - 商品卡片形式展示
  - 列表排序：名称、编码、分类、价格

### 3. 供应商管理 (P1)
- **核心功能**: 供应商信息、价格管理、优先级设置、RPA同步
- **数据库表**: suppliers, item_suppliers, supplier_prices, sync_tasks
- **关键API**: `/api/admin/suppliers/*`, `/api/admin/suppliers/sync/*`
- **技术要点**: 阶梯价格、价格有效期、Agil系统集成
- **新增功能**:
  - 多供应商支持：一个Item支持多个供应商（一个Prefer，多个Alternative排序）
  - 供应商停用功能
  - RPA定时从Agil系统抓取同步
  - 价格计算器：多供应商比价
  - 价格有效期提醒
  - 列表排序：名称、编码、状态、评级、交易金额、购买数量

### 4. 库存管理 (P1) - 待创建文档
- **核心功能**: 部门库存、出入库、库存预警、盘点管理
- **预计数据库表**: department_inventories, inventory_transactions, inventory_alerts
- **关键业务**: 实时库存更新、低库存预警、批量操作
- **新增功能**:
  - 库存最大阈值管理
  - 入库排序：采购过的物品要放到前面
  - 列表排序：名称、编码、分类、数量、总使用量、当月使用量、单价、总价值、当月使用价值
  - 单位换算显示: 列表/详情显示库存单位数量与按采购单位换算后的数量
  - 操作单位规则: 入库默认采购单位; 调整默认库存单位; UI 支持单位切换

### 5. 采购流程管理 (P1) - 已完成文档
- **核心功能**: 购物车模式、四级审批流程、采购申请执行、合并订单采购、数据快照保护
- **数据库表**: purchase_requests, purchase_request_items, approval_history, purchase_execution_batches, purchase_execution_items, purchase_orders
- **关键API**: `/api/admin/purchase/*`, `/api/admin/approval/*`, `/api/admin/purchase/execution/*`
- **技术要点**: 数据快照机制、状态机管理、历史数据保护、批量执行管理
- **关键业务**: 
  - 四级审批：部门物品管理员提交 → 部门经理复核（第一级） → 物品管理员主管审批（第二级） → 公司主管最终审批（第三级）
  - 审批权限：部门经理（第一级）、物品管理员主管（第二级）、公司主管（第三级最终审批）
  - 公司主管审批特性：最高业务决策权，审批通过后自动触发合并采购流程
  - 采购锁定：每月25日-次月5日为审核期，不可提交新申请
  - 合并采购：审批通过后统一选择供应商和计算价格
  - 采购执行：物品管理员批量执行已批准的采购申请，生成执行批次和采购汇总数据
  - K.J审核功能：审核后导出JSON数据
  - 历史保护：完整的数据快照机制保护历史记录完整性
- **新增功能**:
  - 购物车功能：department_item_admin日常工作过程中添加物品到购物车
  - 采购申请二维码生成
  - 历史购买统计：显示历史购买情况（物品、数量、价格）
  - 预估单价改为单价
  - 批量执行采购申请：支持批量选择已批准的申请单，生成执行批次
  - 采购执行记录管理：完整的执行批次管理和详情查看
  - 采购汇总数据导出：支持多种格式的采购单导出

### 6. 移动端领取系统 (P2) - 已完成文档
- **核心功能**: 二维码扫描、Pad端界面、离线同步、权限验证、数据快照保护
- **数据库表**: item_usage_records, pad_devices, device_sessions
- **关键API**: `/api/admin/mobile/*`, `/api/admin/usage/*`
- **技术要点**: 领取记录快照、历史数据保护、设备绑定管理
- **关键业务**: 物品架扫码、工卡验证、实时库存更新、完整领取历史追溯
- **新增功能**:
  - Scan In功能：于总仓库处做Scan In，部门领取物品后库管登记Scan In物品及数量
  - Scan In独立性：与申请功能独立，不与申请订单关联
  - Pad设备属于部门，Pad上的领用页面自动登录部门账号
  - 领用过程仅登录工牌号，不作部门校验
  - 物品二维码设计：是否包含部门信息？
  - 逐件领用: 当 `qty_per_up > 1` 时可按库存单位逐件领取; 否则按采购单位整包发放

### 7. 报表分析系统 (P2) - 待创建文档
- **核心功能**: 使用统计、成本分析、趋势预测、自定义报表
- **关键业务**: 多维度数据分析、图表展示
- **新增功能**:
  - 部门采购统计：显示各部门过去10个月采购金额，并提供预估



## 🔗 模块间数据流

### 典型业务流程数据流向

#### 采购申请流程

```mermaid
sequenceDiagram
    participant DM as 👤 部门物品管理员<br/>(Department Item Manager)
    participant P as 🔐 权限系统
    participant I as 📦 物品管理
    participant Inv as 📋 库存管理
    participant Pr as 🛒 采购系统
    participant DepM as 👔 部门经理<br/>(Department Manager - 第一级审批)
    participant ItemM as 📋 物品管理员主管<br/>(Item Manager Principle - 第二级审批)
    participant Boss as 👑 公司主管<br/>(Company Supervisor - 第三级审批)
    participant S as 🏢 供应商管理
    participant N as 🔔 通知系统

    Note over DM,N: 采购申请流程 - 四级审批 (每月一次)
    
    DM->>P: 1. 部门物品管理员登录验证
    P-->>DM: 返回权限信息
    
    DM->>I: 2. 选择需要采购的物品
    I-->>DM: 返回物品信息 (无价格)
    
    DM->>Inv: 3. 检查当前库存状态
    Inv-->>DM: 返回库存数据
    
    Note over DM: 基于消耗情况填写采购数量
    
    DM->>Pr: 4. 提交采购申请 (购物车模式)
    Pr->>N: 发送复核通知
    N-->>DepM: 通知部门经理复核
    
    Note over DepM: 部门经理复核阶段（第一级审批）
    DepM->>Pr: 5. 部门经理复核申请
    
    alt 复核通过
        Pr->>N: 发送主管审批通知
        N-->>ItemM: 通知物品管理员主管
        
        Note over ItemM: 物品管理员主管审批（第二级审批）
        ItemM->>Pr: 6. 物品管理员主管审批
        
        alt 主管审批通过
            Pr->>N: 发送公司主管审批通知
            N-->>Boss: 通知公司主管审批
            
            Note over Boss: 公司主管最终审批（第三级审批）
            Boss->>Pr: 7. 公司主管最终审批
            
            alt 公司主管审批通过
                Note over Pr,S: 合并订单采购阶段（所有审批通过后）
                Pr->>S: 8. 查询供应商和价格
                S-->>Pr: 返回供应商价格信息
                
                Pr->>S: 9. 选择最优供应商
                Pr->>Pr: 10. 计算总价格并生成采购订单
                
                Pr->>N: 11. 发送采购执行通知
                N-->>DM: 通知采购结果
            else 公司主管审批不通过
                Pr->>N: 返回购物车重新申请
                N-->>DM: 通知重新提交
            end
        else 主管审批不通过
            Pr->>N: 返回购物车重新申请
            N-->>DM: 通知重新提交
        end
    else 复核不通过
        Pr->>N: 返回购物车重新申请
        N-->>DM: 通知重新提交
    end
```

#### 物品领取流程

```mermaid
sequenceDiagram
    participant Pad as 📱 Pad设备
    participant QR as 📷 扫码系统
    participant P as 🔐 权限系统
    participant I as 📦 物品管理
    participant Inv as 📋 库存管理
    participant U as 👤 用户验证
    participant L as 📝 领取记录
    participant N as 🔔 通知系统
    participant R as 📊 统计系统

    Note over Pad,R: 物品领取流程
    
    Pad->>P: 1. Pad设备自动登录部门账号
    P-->>Pad: 返回部门权限
    
    Pad->>QR: 2. 扫描物品架二维码
    QR->>I: 识别物品信息
    I-->>QR: 返回物品详情
    QR-->>Pad: 显示物品信息
    
    Pad->>QR: 3. 扫描员工工卡二维码
    QR->>U: 验证员工身份
    U-->>QR: 返回员工信息
    QR-->>Pad: 显示员工信息
    
    Pad->>Inv: 4. 检查库存可用性
    Inv-->>Pad: 返回库存状态
    
    Note over Pad: 用户确认领取数量
    
    Pad->>P: 5. 验证领取权限
    P-->>Pad: 权限验证结果
    
    Pad->>L: 6. 记录领取信息
    L->>Inv: 7. 更新库存数量
    
    Inv->>R: 8. 更新使用统计
    Inv->>N: 9. 检查低库存预警
    
    alt 库存低于阈值
        N->>N: 10. 发送补货提醒
    end
    
    Pad-->>QR: 11. 显示领取成功
```

#### Scan In流程

```mermaid
sequenceDiagram
    participant WH as 🏭 总仓库
    participant SI as 📥 Scan In系统
    participant I as 📦 物品管理
    participant Inv as 📋 库存管理
    participant N as 🔔 通知系统

    Note over WH,N: Scan In流程
    
    WH->>SI: 1. 库管登录Scan In系统
    SI-->>WH: 返回系统权限
    
    WH->>SI: 2. 扫描物品二维码
    SI->>I: 识别物品信息
    I-->>SI: 返回物品详情
    SI-->>WH: 显示物品信息
    
    WH->>SI: 3. 输入入库数量
    SI->>Inv: 4. 更新库存数量
    Inv-->>SI: 确认库存更新
    
    SI->>N: 5. 发送入库通知
    N-->>WH: 通知入库完成
    
    SI-->>WH: 6. 显示入库成功
```

#### 采购周期管理

```mermaid
gantt
    title 采购周期管理 (每月循环)
    dateFormat  DD
    axisFormat %d日
    
    section 正常申请期
    可提交采购申请    :done, normal1, 01, 24
    
    section 审核采购期  
    锁定申请提交      :crit, lock, 25, 05
    审批处理         :active, review, 25, 05
    
    section 正常申请期
    可提交采购申请    :normal2, 06, 24
```

**采购周期说明**:
- **正常申请期 (1-24日)**: 部门物品管理员可以正常提交采购申请
- **审核采购期 (25日-次月5日)**: 系统锁定新申请提交，专注处理已提交申请的审批和合并采购
- **月度循环**: 每月重复此周期，确保采购的有序管理

## 📐 数据库设计原则

### 核心表设计规范
- **统一时间戳**: 所有表包含 `created_at`, `updated_at`
- **用户追踪**: 重要表包含 `created_by`, `updated_by`
- **软删除**: 关键业务数据支持软删除机制
- **JSON存储**: 灵活属性使用JSON字段存储
- **索引优化**: 查询频繁字段建立合适索引
- **无外键约束**: 不使用数据库外键约束，在代码层面保证数据关系和完整性
- **历史数据保护**: 通过数据快照、版本控制等方式保护历史记录的完整性

### 关联关系设计
- **代码层完整性**: 在应用层通过ORM和业务逻辑保证数据完整性
- **性能优化**: 避免外键约束带来的性能开销和锁竞争
- **兼容性保证**: 提高不同数据库系统间的兼容性
- **关联表**: 多对多关系使用中间表，通过ID字段或者业务编号(如订单号)关联
- **数据隔离**: 基于部门的数据权限隔离

## 🔧 技术选型总结

### 后端技术栈
- **框架**: FastAPI (高性能异步框架)
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **ORM**: SQLAlchemy 2.0 (现代化ORM)
- **认证**: JWT Token + bcrypt
- **搜索**: FTS5 全文搜索
- **任务队列**: Celery (RPA同步任务)

### 前端技术栈
- **框架**: React 18 + TypeScript
- **UI组件**: Ant Design (企业级UI库)
- **状态管理**: Context + Hooks
- **网络请求**: Axios
- **图表**: Chart.js / ECharts

### 移动端技术
- **平台**: Web端响应式设计
- **优化**: 专为平板设备优化
- **交互**: 触屏友好的操作界面
- **扫码**: Web Camera API

## 🚀 开发里程碑

### 第一阶段 (基础设施) - 预计2周
- [ ] 用户权限管理模块
- [ ] 物品分类管理模块
- [ ] 基础UI框架搭建
- [ ] 数据库初始化脚本

### 第二阶段 (核心业务) - 预计3周
- [ ] 供应商管理模块
- [ ] 库存管理模块
- [ ] 基础API接口开发
- [ ] 前端核心页面

### 第三阶段 (业务流程) - 预计3周
- [ ] 采购流程管理模块
- [ ] 移动端领取系统
- [ ] 完整业务流程测试
- [ ] 用户培训材料

### 第四阶段 (分析优化) - 预计2周
- [ ] 报表分析系统
- [ ] 通知系统
- [ ] 性能优化
- [ ] 部署上线

## 📝 开发注意事项

### 数据一致性
- 跨表数据更新时保证ACID特性
- 重要业务操作使用数据库事务
- **代码层完整性**: 在ORM和Service层实现数据关系验证
- **级联操作**: 通过代码逻辑实现级联删除和更新
- 定期数据一致性检查和修复

### 数据演变处理策略
- **数据快照**: 历史记录保存关联数据的快照，避免主数据变更影响历史完整性
- **版本控制**: 重要实体支持版本管理，保留历史版本供追溯
- **事件溯源**: 关键业务流程记录完整的事件链，支持状态重建
- **冗余存储**: 关键字段在业务记录中适度冗余，减少关联查询依赖
- **数据迁移**: 提供数据结构变更时的自动迁移和兼容处理

### 性能优化
- 数据库查询优化和索引设计
- 前端组件懒加载和虚拟滚动
- API响应缓存和数据预加载

### 安全考虑
- 所有API接口权限验证
- 敏感数据加密存储
- SQL注入和XSS攻击防护
- 操作审计和异常监控

### 国际化
- 所有界面文本支持中英文双语显示，如"User Management(用户管理)"
- 数据库字段存储多语言值
- 日期时间格式本地化
- 数字和货币格式处理

### 部署运维
- Docker容器化部署
- 数据库备份恢复策略
- 日志收集和监控告警
- 版本升级和回滚机制

### 代码层数据完整性保证
- **删除级联**: 通过Service层方法处理关联数据的删除逻辑
- **存在性验证**: 在创建关联关系前验证引用实体的存在性
- **业务规则验证**: 在数据操作前验证业务规则和约束条件
- **事务保证**: 使用数据库事务确保复杂操作的原子性
- **状态一致性**: 通过代码逻辑维护数据状态的一致性


---

*本文档将随着需求文档的完善持续更新* 