# 物品管理系统二维码标准

## 概述

本文档定义了物品管理系统中所有业务对象的二维码生成和解析标准。系统采用简化版二维码格式，直接使用业务对象的编码作为二维码内容，避免复杂的协议格式和换行符处理问题。

## 二维码格式标准

### 1. 基本格式

所有二维码内容直接使用业务对象的编码，不再包含协议前缀、版本信息等复杂结构。

### 2. 编码规则

- **物品编码**: 以 `IDM` 开头，如 `IDM001`、`IDM002`
- **部门编码**: 支持任意格式，可手工随意设置
  - 字母编码：`IT`、`HR`、`PUR`、`WH`、`ME`、`SI`、`AD`、`QA`
  - 数字编码：`001`、`002`、`003`
  - 任意组合：`DEPT001`、`DEPT002`、`IT-001`、`HR_001`等
- **员工工号**: 以 `EMP` 开头，如 `EMP001`、`EMP002`
- **供应商编码**: 以 `SUP` 开头，如 `SUP001`、`SUP002`
- **采购申请号**: 以 `PR` 开头，如 `PR001`、`PR002`

### 3. 类型识别

系统通过编码前缀自动识别二维码类型：
- `IDM` → 物品类型
- `DEPT`、`IT`、`HR`、`PUR`、`WH`、`ME`、`SI`、`AD`、`QA`、纯数字 → 部门类型
- `EMP` → 员工类型
- `SUP` → 供应商类型
- `PR` → 采购申请类型

## 二维码生成

### 1. 物品二维码

```typescript
// 生成物品二维码
const qrContent = QRCodeService.generateItemQRCode(item);
// 结果: "IDM001"
```

### 2. 部门二维码

```typescript
// 生成部门二维码
const qrContent = QRCodeService.generateDepartmentQRCode(department);
// 结果: "DEPT001"
```

### 3. 员工二维码

```typescript
// 生成员工二维码
const qrContent = QRCodeService.generateEmployeeQRCode(user);
// 结果: "EMP001"
```

### 4. 供应商二维码

```typescript
// 生成供应商二维码
const qrContent = QRCodeService.generateSupplierQRCode(supplier);
// 结果: "SUP001"
```

## 二维码解析

### 1. 自动类型识别

```typescript
// 解析二维码内容
const qrData = QRCodeService.parseQRCode("IDM001");
// 结果: { type: "i", code: "IDM001", ... }
```

### 2. 类型验证

```typescript
// 验证是否为物品类型
const isItem = QRCodeService.detectQRCodeType("IDM001") === QRCodeType.ITEM;
// 结果: true
```

## 优势

### 1. 扫码枪兼容性

- 避免换行符被当作特殊字符处理
- 扫码枪作为键盘输入时内容完整
- 支持各种扫码设备

### 2. 系统兼容性

- 保持现有的权限验证逻辑
- 保持现有的错误处理机制
- 向后兼容现有业务逻辑

### 3. 维护性

- 代码逻辑简化
- 减少解析错误
- 便于调试和维护

## 迁移说明

### 1. 版本兼容

- 新版本：使用简化版二维码格式
- 旧版本：仍支持原有复杂格式（通过编码前缀判断）

### 2. 数据更新

- 现有二维码内容保持不变
- 新生成的二维码使用简化格式
- 系统自动识别新旧格式

## 注意事项

### 1. 编码唯一性

- 确保各类编码在系统内唯一
- 避免编码冲突导致识别错误

### 2. 编码长度

- 建议编码长度控制在合理范围内
- 避免过长编码影响扫码效果

### 3. 特殊字符

- 编码中避免使用特殊字符
- 确保扫码枪能正确识别
