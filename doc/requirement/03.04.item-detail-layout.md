# 物品详情/编辑页面布局设计

## 1. 需求概述

### 业务背景
当前物品详情页面布局较为混乱，信息分类不够清晰，用户体验有待提升。需要重新组织页面布局，按照业务逻辑将信息分为不同板块，提升信息的可读性和操作便利性。

### 核心目标
- 重新组织物品详情页面布局，按业务逻辑分类信息
- 优化编辑模式下的表单布局和交互体验
- 提升信息的可读性和操作便利性
- 保持详情模式和编辑模式的一致性

### 系统定位
作为物品管理模块的用户界面优化，提升物品信息查看和编辑的用户体验。

## 2. 功能需求

### 2.1 页面布局结构
**模块**: 物品详情页面布局

**详细业务场景**:
- 页面采用卡片式布局，每个板块独立成卡片
- 详情模式：只读显示，支持编辑按钮切换
- 编辑模式：表单编辑，支持保存和取消操作
- 响应式设计：支持不同屏幕尺寸

**输入输出定义**:
- 输入：物品ID（路由参数）
- 输出：物品详情页面，支持查看和编辑功能

### 2.2 基本信息板块
**模块**: 基本信息展示和编辑

**详细业务场景**:
- 图片区域：显示物品图片，支持上传和更换
- 基础信息：名称、编号、分类、单位
- 描述信息：物品详细描述（可选）

**字段定义**:
- 图片：物品图片URL，支持上传组件
- 名称：物品名称（必填）
- 编号：物品编码（必填）
- 分类：所属分类（必填，显示一级/二级分类）
- 单位：计量单位（必填）
- 描述：物品描述（可选）

### 2.3 属性板块
**模块**: 物品属性信息

**详细业务场景**:
- 品牌信息：物品品牌
- 规格材质：物品规格和材质信息
- 尺寸规格：物品尺寸和规格信息

**字段定义**:
- 品牌：物品品牌（可选）
- 规格/材质：物品规格和材质（可选）
- 尺寸/规格：物品尺寸和规格（可选）

### 2.4 状态板块
**模块**: 物品状态管理

**详细业务场景**:
- 启用状态：控制物品是否可用
- 可购买状态：控制物品是否可以购买

**字段定义**:
- 启用状态：布尔值，控制物品是否启用
- 可购买状态：布尔值，控制物品是否可以购买

### 2.5 采购属性板块
**模块**: 采购相关信息

**详细业务场景**:
- 价格信息：物品平均价格
- 采购数量：最小购买数量、标准包装数量、单位包装数量

**字段定义**:
- 平均价格：物品平均价格（只读）
- 最小购买数量（MOQ）：最小购买数量（必填）
- 标准包装数量（SPQ）：标准包装数量（必填）
- 单位包装数量（QtyPurUP）：单位包装数量（必填）

### 2.6 库存信息板块
**模块**: 库存状态和数量

**详细业务场景**:
- 库存数量：当前总库存
- 库存状态：库存状态标签（充足/正常/不足/缺货）

**字段定义**:
- 总库存：当前库存数量（只读）
- 库存状态：根据库存数量显示状态标签

## 3. 业务对象设计

### 3.1 页面布局组件
```mermaid
classDiagram
    class ItemDetailPage {
        +item: Item
        +isEditing: boolean
        +handleEdit()
        +handleSave()
        +handleCancel()
    }
    
    class BasicInfoCard {
        +item: Item
        +isEditing: boolean
        +onChange: function
    }
    
    class AttributeCard {
        +item: Item
        +isEditing: boolean
        +onChange: function
    }
    
    class StatusCard {
        +item: Item
        +isEditing: boolean
        +onChange: function
    }
    
    class PurchaseCard {
        +item: Item
        +isEditing: boolean
        +onChange: function
    }
    
    class InventoryCard {
        +item: Item
        +isEditing: boolean
        +onChange: function
    }
    
    ItemDetailPage --> BasicInfoCard
    ItemDetailPage --> AttributeCard
    ItemDetailPage --> StatusCard
    ItemDetailPage --> PurchaseCard
    ItemDetailPage --> InventoryCard
```

### 3.2 业务对象说明
- **ItemDetailPage**: 主页面组件，负责整体布局和状态管理
- **BasicInfoCard**: 基本信息卡片，包含图片、名称、编号等基础信息
- **AttributeCard**: 属性信息卡片，包含品牌、规格、尺寸等属性
- **StatusCard**: 状态信息卡片，包含启用状态和可购买状态
- **PurchaseCard**: 采购信息卡片，包含价格和采购数量相关信息
- **InventoryCard**: 库存信息卡片，包含库存数量和状态信息

## 4. 页面布局设计

### 4.1 详情模式布局
```
┌─────────────────────────────────────────────────────────────┐
│ 返回按钮                    [编辑] [删除]                  │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────┐ │
│ │                 │ │ 物品名称                            │ │
│ │    物品图片     │ │ 编码: XXX-001                       │ │
│ │                 │ │ [启用] [可购买] [库存状态]          │ │
│ └─────────────────┘ └─────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 基本信息                                                │ │
│ │ 名称: XXX    编码: XXX-001    分类: 一级/二级          │ │
│ │ 单位: 个     描述: 物品详细描述...                     │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 属性信息                                                │ │
│ │ 品牌: [品牌标签]    规格/材质: [规格标签]              │ │
│ │ 尺寸/规格: [尺寸标签]                                  │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 状态信息                                                │ │
│ │ 启用状态: 启用    可购买状态: 可购买                    │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 采购属性                                                │ │
│ │ 平均价格: $100.00    最小购买数量: 10                  │ │
│ │ 标准包装数量: 100    单位包装数量: 1                   │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 库存信息                                                │ │
│ │ 总库存: 500    库存状态: 充足                           │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 编辑模式布局
```
┌─────────────────────────────────────────────────────────────┐
│ 返回按钮                    [保存] [取消]                  │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────┐ │
│ │                 │ │ 物品名称: [输入框]                  │ │
│ │    图片上传     │ │ 物品编码: [输入框]                  │ │
│ │    组件区域     │ │ 所属分类: [下拉选择]                │ │
│ └─────────────────┘ │ 计量单位: [输入框]                  │ │
│                     │ 物品描述: [文本域]                  │ │
│                     └─────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 属性信息                                                │ │
│ │ 品牌: [输入框]    规格/材质: [输入框]                  │ │
│ │ 尺寸/规格: [输入框]                                    │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 状态信息                                                │ │
│ │ 启用状态: [开关]    可购买状态: [开关]                  │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 采购属性                                                │ │
│ │ 最小购买数量: [数字输入]    标准包装数量: [数字输入]    │ │
│ │ 单位包装数量: [数字输入]                                │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 5. 业务规则

### 5.1 显示规则
- **详情模式**: 所有信息以只读方式显示，支持标签和状态显示
- **编辑模式**: 可编辑字段显示为表单控件，只读字段保持显示
- **响应式布局**: 支持桌面端和移动端适配

### 5.2 编辑规则
- **必填字段**: 名称、编码、分类、单位、MOQ、SPQ、QtyPurUP
- **可选字段**: 描述、品牌、规格/材质、尺寸/规格
- **开关字段**: 启用状态、可购买状态
- **只读字段**: 平均价格、总库存（由系统计算）

### 5.3 验证规则
- **名称**: 必填，长度1-100字符
- **编码**: 必填，长度1-50字符，唯一性验证
- **分类**: 必填，从有效分类中选择
- **单位**: 必填，长度1-20字符
- **数量字段**: 必填，大于0的数值
- **预警值**: 必填，大于等于0的整数

### 5.4 交互规则
- **编辑切换**: 点击编辑按钮进入编辑模式
- **保存操作**: 验证必填字段，提交表单数据
- **取消操作**: 放弃当前编辑，返回详情模式
- **图片上传**: 支持拖拽和点击上传，实时预览

### 5.5 状态显示规则
- **库存状态**: 根据库存数量和预警值显示不同颜色标签
  - 充足：绿色（库存 > 预警值 * 2）
  - 正常：蓝色（预警值 < 库存 ≤ 预警值 * 2）
  - 不足：橙色（0 < 库存 ≤ 预警值）
  - 缺货：红色（库存 = 0）
- **启用状态**: 绿色标签表示启用，红色标签表示禁用
- **可购买状态**: 蓝色标签表示可购买，橙色标签表示不可购买

### 5.6 权限和安全要求
- **查看权限**: 需要物品查看权限
- **编辑权限**: 需要物品编辑权限
- **删除权限**: 需要物品删除权限
- **数据安全**: 表单数据验证和XSS防护

## 6. 技术实现要点

### 6.1 前端实现
- 使用React + TypeScript开发
- 采用Ant Design组件库
- 实现响应式布局设计
- 支持表单验证和错误提示

### 6.2 组件设计
- 每个板块独立为卡片组件
- 支持详情和编辑两种模式
- 统一的样式和交互规范
- 可复用的表单控件组件

### 6.3 状态管理
- 使用React Hooks管理组件状态
- 表单数据与后端API同步
- 支持编辑状态的回滚操作
- 实时验证和错误提示

### 6.4 性能优化
- 图片懒加载和压缩
- 表单验证防抖处理
- 组件按需渲染
- 数据缓存和预加载 