# 采购申请执行功能需求文档

## 1. 需求概述

### 1.1 业务背景
采购申请通过四级审批流程后，物品管理员需要将已批准的采购申请转换为可执行的采购订单。为了提高采购执行效率，需要支持批量操作，将多个已批准的采购申请一次性标记为已执行状态，并生成采购汇总数据供采购员录入RPA系统。

### 1.2 核心目标
- 提升采购执行效率，支持批量处理已批准的采购申请
- 建立完整的采购执行记录，实现采购全流程可追溯
- 生成标准化的采购汇总数据，便于采购员录入RPA系统
- 优化采购状态管理，清晰区分申请、执行、完成等不同阶段

### 1.3 系统定位
采购申请执行功能是采购申请模块的重要组成部分，负责将审批通过的采购申请转换为可执行的采购订单，为后续的采购执行和供应商管理提供数据支撑。

## 2. 功能需求

### 2.1 批量执行采购申请

#### 2.1.1 申请单筛选
- **筛选条件**: 支持按部门、提交时间、物品类型等条件筛选已批准的采购申请
- **状态过滤**: 只显示状态为"已批准"的采购申请单
- **批量选择**: 支持全选、部分选择、单个选择等多种选择方式
- **选择验证**: 验证选中申请单的状态和权限，确保只能选择已批准的申请

#### 2.1.2 批量执行操作
- **执行按钮**: 在采购申请列表页面提供"批量执行"按钮
- **权限控制**: 只有物品管理员可以执行批量操作
- **执行确认**: 显示执行确认对话框，包含选中申请单的汇总信息
- **批次生成**: 每次批量执行生成一个唯一的执行批次编号

#### 2.1.3 状态更新
- **状态变更**: 将选中的采购申请单状态从"已批准"更新为"已执行"
- **执行记录**: 记录执行时间、执行人、批次编号等执行信息
- **流转历史**: 在申请流转历史中添加执行操作记录
- **价格锁定**: 执行时自动获取申请明细中已锁定的最终价格，确保价格一致性

### 2.2 执行记录管理

#### 2.2.1 执行批次列表
- **批次信息**: 显示执行批次编号、执行时间、执行人、申请单数量、总金额
- **状态标识**: 清晰标识每个批次的执行状态
- **筛选功能**: 支持按执行时间、执行人、批次编号等条件筛选
- **排序功能**: 支持按执行时间、总金额等字段排序

#### 2.2.2 批次详情查看
- **批次概览**: 显示批次的基本信息、执行统计、采购汇总
- **申请单列表**: 展示该批次包含的所有采购申请单详情
- **汇总数据**: 按供应商、物品类型等维度汇总采购数据
- **操作记录**: 显示批次的完整操作历史

### 2.3 采购汇总数据

#### 2.3.1 数据汇总规则
- **按供应商汇总**: 同一供应商的物品合并显示，计算总数量和总金额
- **按物品分类**: 按物品的一级分类和二级分类进行分组
- **数量统计**: 汇总SPQ数量和SPQ个数，计算总采购数量
- **金额计算**: 基于最终锁定价格计算总采购金额
- **价格来源**: 所有价格数据必须来自最终审批后锁定的价格，确保汇总数据的准确性

#### 2.3.2 汇总数据展示
- **供应商维度**: 显示每个供应商的采购物品清单和总金额
- **分类维度**: 显示各物品分类的采购数量和金额分布
- **时间维度**: 显示采购申请的提交时间和期望交期
- **状态维度**: 显示各申请单的执行状态和进度

### 2.4 采购单导出

#### 2.4.1 导出格式
- **Excel格式**: 支持.xlsx格式导出，便于数据分析和处理
- **PDF格式**: 支持PDF格式导出，便于打印和存档
- **CSV格式**: 支持CSV格式导出，便于系统间数据交换

#### 2.4.2 导出内容
- **批次信息**: 执行批次编号、执行时间、执行人
- **申请汇总**: 申请单数量、总金额、期望交期
- **物品明细**: 物品编码、名称、规格、数量、单价、总价
- **供应商信息**: 供应商名称、联系方式

#### 2.4.3 导出功能
- **单个批次导出**: 支持导出单个执行批次的采购汇总单
- **批量导出**: 支持按条件批量导出多个批次的采购单
- **模板定制**: 支持自定义导出模板，满足不同采购员的需求

## 3. 业务对象设计

### 3.1 ER图
```mermaid
erDiagram
    PurchaseRequest ||--o{ PurchaseExecutionBatch : "executes"
    PurchaseExecutionBatch ||--o{ PurchaseExecutionItem : "contains"
    PurchaseRequestItem ||--o{ PurchaseExecutionItem : "references"
    
    PurchaseRequest {
        int id PK
        string request_no
        string status
        int department_id
        int submitter_id
        decimal final_total
        datetime submitted_at
        datetime created_at
        datetime updated_at
    }
    
    PurchaseExecutionBatch {
        int id PK
        string batch_no
        string batch_name
        int executor_id
        string executor_name
        datetime executed_at
        int request_count
        decimal total_amount
        string status
        text notes
        datetime created_at
        datetime updated_at
    }
    
    PurchaseExecutionItem {
        int id PK
        int batch_id FK
        int request_id FK
        int request_item_id FK
        int item_id
        string item_code
        string item_name
        decimal spq_quantity
        int spq_count
        string spq_unit
        decimal unit_price
        decimal total_price
        int supplier_id
        string supplier_name
        datetime created_at
    }
```

### 3.2 业务对象说明

#### 采购执行批次（PurchaseExecutionBatch）
- **批次编号**: 唯一标识每个执行批次
- **执行信息**: 记录执行时间、执行人、执行状态
- **统计信息**: 包含申请单数量、总金额等汇总数据
- **备注信息**: 记录执行过程中的特殊说明和要求

#### 采购执行明细（PurchaseExecutionItem）
- **批次关联**: 关联到具体的执行批次
- **申请关联**: 关联到具体的采购申请和申请明细
- **物品信息**: 包含物品的基本信息、数量、价格
- **供应商信息**: 记录最终确定的供应商和价格信息
- **价格锁定**: 价格信息来自最终审批后锁定的价格，确保执行时价格一致性

## 4. 数据库表设计

### 4.1 采购执行批次表（purchase_execution_batches）
```sql
CREATE TABLE purchase_execution_batches (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    batch_no VARCHAR(50) UNIQUE NOT NULL COMMENT '执行批次编号',
    batch_name VARCHAR(100) NOT NULL COMMENT '批次名称',
    executor_id INTEGER NOT NULL COMMENT '执行人ID',
    executor_name VARCHAR(100) NOT NULL COMMENT '执行人姓名',
    executed_at DATETIME NOT NULL COMMENT '执行时间',
    request_count INTEGER NOT NULL COMMENT '申请单数量',
    total_amount DECIMAL(15,2) NOT NULL COMMENT '总金额',
    status VARCHAR(20) DEFAULT 'active' COMMENT '批次状态',
    notes TEXT COMMENT '备注说明',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_execution_batches_batch_no (batch_no),
    INDEX idx_execution_batches_executor (executor_id),
    INDEX idx_execution_batches_executed_at (executed_at),
    INDEX idx_execution_batches_status (status)
);
```

### 4.2 采购执行明细表（purchase_execution_items）
```sql
CREATE TABLE purchase_execution_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    batch_id INTEGER NOT NULL COMMENT '执行批次ID',
    request_id INTEGER NOT NULL COMMENT '采购申请ID',
    request_item_id INTEGER NOT NULL COMMENT '申请明细ID',
    item_id INTEGER NOT NULL COMMENT '物品ID',
    item_code VARCHAR(20) NOT NULL COMMENT '物品编码',
    item_name VARCHAR(200) NOT NULL COMMENT '物品名称',
    spq_quantity DECIMAL(10,3) NOT NULL COMMENT 'SPQ数量',
    spq_count INTEGER NOT NULL COMMENT 'SPQ个数',
    spq_unit VARCHAR(20) NOT NULL COMMENT 'SPQ单位',
    unit_price DECIMAL(12,4) NOT NULL COMMENT '最终锁定单价（来自审批后的价格）',
    total_price DECIMAL(15,2) NOT NULL COMMENT '最终锁定总价（来自审批后的价格）',
    supplier_id INTEGER NOT NULL COMMENT '最终确定的供应商ID（来自审批后的选择）',
    supplier_name VARCHAR(200) NOT NULL COMMENT '最终确定的供应商名称（来自审批后的选择）',
    price_locked_at DATETIME COMMENT '价格锁定时间（记录最终审批通过时间）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_execution_items_batch (batch_id),
    INDEX idx_execution_items_request (request_id),
    INDEX idx_execution_items_item (item_id),
    INDEX idx_execution_items_supplier (supplier_id)
);
```

### 4.3 采购申请状态更新
```sql
-- 更新采购申请状态为已执行
UPDATE purchase_requests 
SET status = 'executed', updated_at = CURRENT_TIMESTAMP 
WHERE id IN (SELECT request_id FROM purchase_execution_items WHERE batch_id = ?);

-- 在流转历史中添加执行记录
INSERT INTO request_flow_history (
    request_id, action, from_status, to_status, 
    operator_id, operator_name, comments, created_at
) VALUES (?, 'execute', 'approved', 'executed', ?, ?, ?, CURRENT_TIMESTAMP);
```

## 5. 业务规则

### 5.1 执行权限规则
- **执行权限**: 只有物品管理员可以执行采购申请
- **状态限制**: 只能执行状态为"已批准"的采购申请
- **数据权限**: 物品管理员能执行所有的采购申请

### 5.2 批量执行规则
- **批次唯一性**: 每个执行批次必须有唯一的批次编号
- **申请单状态**: 批量执行后，所有选中的申请单状态必须同步更新
- **数据完整性**: 执行过程中必须保证申请单数据的完整性和一致性
- **操作记录**: 每次批量执行必须记录完整的操作日志和状态变更

### 5.3 汇总数据规则
- **供应商分组**: 按最终确定的供应商进行分组汇总
- **数量计算**: 基于SPQ数量和SPQ个数计算总采购数量
- **金额计算**: 基于最终锁定的价格计算总采购金额
- **分类统计**: 按物品分类进行统计，便于采购分析和决策
- **价格一致性**: 执行时的价格必须与最终审批锁定的价格保持一致，确保采购成本可控

### 5.4 导出规则
- **数据准确性**: 导出的数据必须与系统中的数据保持一致
- **格式标准化**: 导出格式必须符合采购员录入RPA系统的要求
- **权限控制**: 只有有权限的用户才能导出采购汇总数据
- **审计追踪**: 所有导出操作必须记录操作人和操作时间

### 5.5 价格锁定规则
- **价格来源**: 执行明细中的价格必须来自最终审批后锁定的价格，不得使用实时查询的价格
- **价格验证**: 执行前必须验证申请明细中的最终价格是否已锁定
- **价格一致性**: 确保执行时的价格与审批时的价格完全一致
- **历史追溯**: 记录价格锁定时间，便于后续审计和追溯

### 5.6 异常处理规则
- **执行失败**: 批量执行过程中如果部分申请单执行失败，必须回滚整个批次
- **数据不一致**: 发现申请单数据不一致时，必须停止执行并记录错误信息
- **价格未锁定**: 发现申请单价格未锁定时，必须停止执行并提示用户
- **权限不足**: 用户权限不足时，必须明确提示并阻止操作
- **系统异常**: 系统异常时必须保存操作状态，支持后续恢复操作

