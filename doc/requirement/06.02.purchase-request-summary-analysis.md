# 采购申请汇总分析功能需求文档

## 1. 需求概述

### 1.1 业务背景
在采购申请管理过程中，采购员和老板需要从宏观角度了解当前批次采购申请的整体情况，包括物品数量汇总、价格趋势分析、成本控制等关键信息。现有的采购详情页面主要关注单个申请的流程管理和审批操作，缺乏对多个申请进行汇总分析的能力，无法为批量采购决策提供有效的数据支撑。

### 1.2 核心目标
- 提升采购决策效率，通过汇总视图快速了解采购规模
- 降低采购成本，通过价格趋势分析优化采购时机
- 改善供应商谈判效果，基于汇总数量获得更好的价格优惠
- 增强成本控制能力，实时掌握采购预算使用情况

### 1.3 系统定位
采购申请汇总分析功能是采购申请模块的决策支持组件，为采购员、老板和财务人员提供批量采购的数据分析和决策依据，是连接采购申请管理和采购执行的重要桥梁。

## 2. 功能需求

### 2.1 申请汇总概览

#### 2.1.1 批次申请统计
- **功能点**: 显示当前批次采购申请的整体统计信息
- **业务场景**: 用户选择多个采购申请后，系统自动计算并展示汇总统计
- **前置条件**: 存在状态为"已批准"或"待审批"的采购申请
- **后置条件**: 用户获得批次申请的总体概览信息
- **输入**: 采购申请ID列表、统计时间范围
- **输出**: 申请总数、涉及部门数、总金额预估、申请状态分布

#### 2.1.2 物品汇总分析
- **功能点**: 按物品维度汇总采购数量和金额
- **业务场景**: 系统将多个申请中相同物品的数量和金额进行合并计算
- **前置条件**: 选中的申请中包含物品信息
- **后置条件**: 用户了解各物品的采购总量和总价值
- **输入**: 采购申请明细数据
- **输出**: 物品汇总表（物品名称、编码、分类、总SPQ数量、总SPQ个数、总金额）

#### 2.1.3 部门采购分布
- **功能点**: 展示各部门的采购申请分布情况
- **业务场景**: 用户查看各部门的采购需求和金额分布
- **前置条件**: 申请数据包含部门信息
- **后置条件**: 用户了解各部门的采购需求分布
- **输入**: 采购申请部门数据
- **输出**: 部门采购统计（部门名称、申请数量、总金额、占比）

### 2.2 价格趋势分析

#### 2.2.1 批量价格趋势
- **功能点**: 展示选中物品的价格变化趋势
- **业务场景**: 用户查看物品在指定时间范围内的价格波动情况
- **前置条件**: 物品存在历史价格数据
- **后置条件**: 用户了解价格变化趋势，做出采购时机判断
- **输入**: 物品ID列表、时间范围、供应商信息
- **输出**: 价格趋势图表、价格波动统计、趋势分析建议

#### 2.2.3 成本趋势预测
- **功能点**: 基于历史数据预测未来采购成本趋势
- **业务场景**: 用户根据历史趋势预测未来采购成本变化
- **前置条件**: 存在足够的历史价格数据
- **后置条件**: 用户获得成本预测信息，优化采购计划
- **输入**: 历史价格数据、时间序列
- **输出**: 成本趋势预测、置信区间、预测准确性指标

### 2.3 供应商分析

#### 2.3.1 供应商分布统计
- **功能点**: 统计各供应商在选中申请中的分布情况
- **业务场景**: 用户了解当前批次采购的供应商分布
- **前置条件**: 申请数据包含供应商信息
- **后置条件**: 用户了解供应商集中度，评估供应风险
- **输入**: 采购申请供应商数据
- **输出**: 供应商分布统计、供应集中度分析

## 5. 业务规则

### 5.1 业务约束

#### 5.1.1 汇总范围约束
- **申请状态限制**: 只能汇总状态为"已批准"、"待审批"、"主管审批中"、"最终审批中"的申请
- **时间范围限制**: 汇总分析的时间范围不能超过365天
- **申请数量限制**: 单次汇总的申请数量不能超过1000个
- **部门数量限制**: 单次汇总涉及的部门数量不能超过50个

#### 5.1.2 数据完整性约束
- **物品信息完整性**: 汇总的物品必须包含完整的编码、名称、分类信息
- **价格数据有效性**: 价格数据必须为正数，且不能超过系统设定的最大值
- **数量数据合理性**: SPQ数量和SPQ个数必须为正数，且符合业务逻辑
- **金额计算准确性**: 总金额必须等于各物品金额之和，误差不能超过0.01元

#### 5.1.3 分析结果约束
- **百分比计算精度**: 百分比计算结果保留两位小数

### 5.2 异常处理规则

#### 5.2.1 数据异常处理
- **缺失数据处理**: 当物品缺少价格数据时，在汇总中标记为"价格待确认"
- **数据不一致处理**: 当同一物品在不同申请中信息不一致时，以物品当前最优先供应商当前价格为准

#### 5.2.2 分析异常处理
- **分析失败处理**: 当汇总分析失败时，系统记录失败原因并通知用户

#### 5.2.3 用户操作异常处理
- **权限不足处理**: 当用户权限不足时，系统提示权限要求并引导申请权限
- **参数错误处理**: 当用户输入参数错误时，系统提供参数说明和正确示例

### 5.3 权限和安全要求

#### 5.3.1 功能权限控制
- **查看权限**: 用户必须具有"采购申请查看"权限才能访问汇总分析功能

#### 5.3.2 数据访问控制
- **部门数据隔离**: 与采购申请单一致, 普通用户可查看本部门的申请单并汇总. 物品管理员, 公司主管, 超级管理员可以查看所有采购申请并汇总
- **敏感信息保护**: 供应商价格等敏感信息只对具有相应权限的用户显示

#### 5.3.3 系统安全要求
- **接口访问控制**: 所有汇总分析相关的API接口都必须进行身份验证和权限验证

### 5.4 性能和质量要求

#### 5.4.1 响应时间要求
- **汇总分析响应时间**: 普通汇总分析（100个申请以内）响应时间不超过5秒
- **大数据量处理**: 大数据量汇总分析（1000个申请）响应时间不超过30秒
- **图表渲染时间**: 价格趋势图表渲染时间不超过3秒
- **数据导出时间**: 汇总数据导出时间不超过10秒

#### 5.4.2 数据准确性要求
- **金额计算精度**: 金额计算精度必须达到分（0.01元）级别
- **数量统计准确性**: 数量统计必须100%准确，不能有遗漏或重复
- **趋势分析准确性**: 价格趋势分析的准确率必须达到80%以上
- **供应商评分准确性**: 供应商评分必须基于客观数据，不能人为干预
