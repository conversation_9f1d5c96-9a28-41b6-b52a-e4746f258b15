# Supplier Management (供应商管理) 需求文档

## 1. 需求概述

### 业务背景
供应商管理是BizLinkSpeedy IDM系统的核心模块之一，负责管理所有物品的供应商信息、价格体系、评级机制和采购关系。通过建立完善的供应商管理体系，确保采购流程的规范化和成本控制的有效性。

### 核心目标
- **供应商信息管理**: 建立完整的供应商档案，包括基本信息、联系方式、评级等
- **价格体系管理**: 支持阶梯价格、价格有效期、历史价格追踪
- **多供应商支持**: 一个物品支持多个供应商，建立优先级机制
- (暂不实现)**RPA数据同步**: 通过RPA定时从Agil系统抓取同步供应商数据
- (暂不实现)**价格计算器**: 提供多供应商比价功能，辅助采购决策

### 系统定位
供应商管理模块作为物品管理的上游模块，为采购流程提供供应商和价格数据支撑，同时为库存管理提供供应商选择依据。该模块与物品管理、采购流程管理、库存管理等模块紧密关联。

## 2. 功能需求

### 2.1 供应商信息管理

#### 2.1.1 供应商基础信息管理
**业务场景**: 物品管理员需要维护供应商的基本信息，包括公司信息、联系方式、评级等。

**功能描述**:
- 供应商基本信息录入和编辑
- 供应商状态管理（启用/停用）
- 供应商评级管理
- 供应商编码自动生成

**输入输出定义**:
- **输入**: 供应商名称、公司地址、联系人、电话、邮箱、评级等
- **输出**: 供应商档案、状态变更记录、评级历史

#### 2.1.2 供应商列表管理
**业务场景**: 系统需要提供供应商列表展示，支持排序和筛选功能。

**功能描述**:
- 供应商列表展示
- 支持按名称、编码、状态、评级、交易金额、购买数量排序
- 供应商状态筛选
- 供应商搜索功能

**输入输出定义**:
- **输入**: 排序条件、筛选条件、搜索关键词
- **输出**: 供应商列表、排序结果、筛选结果

### 2.2 价格管理

#### 2.2.1 阶梯价格管理
**业务场景**: 供应商针对不同采购数量提供不同的价格，需要建立阶梯价格体系。

**功能描述**:
- 阶梯价格设置（数量区间对应价格）
- 价格有效期管理
- 价格历史记录追踪（通过不删除历史记录实现）
- 价格变更通知

**输入输出定义**:
- **输入**: 数量区间、对应价格、有效期、供应商ID、备注说明
- **输出**: 阶梯价格表、价格历史记录、有效期提醒

#### 2.2.2 价格有效期管理
**业务场景**: 供应商价格有有效期限制，需要及时提醒维护人员进行价格更新。

**功能描述**:
- 价格有效期设置
- 价格到期提醒
- 价格自动失效处理
- 价格维护记录

**输入输出定义**:
- **输入**: 价格有效期、提醒时间、供应商ID
- **输出**: 价格有效期状态、提醒通知、维护记录

### 2.3 多供应商支持

#### 2.3.1 供应商优先级管理
**业务场景**: 一个物品可能有多个供应商，需要建立优先级机制，确保采购时优先选择合适的供应商。

**功能描述**:
- 供应商优先级设置（Prefer/Alternative）
- 供应商排序管理
- 供应商切换机制
- 供应商停用处理
- SPQ、MOQ配置管理

**输入输出定义**:
- **输入**: 物品ID、供应商列表、优先级设置、SPQ、MOQ
- **输出**: 供应商优先级列表、切换记录、停用记录

#### 2.3.2 供应商停用管理
**业务场景**: 当Preferred供应商停用后，系统需要自动切换到Alternative供应商。

**功能描述**:
- 供应商停用操作
- 自动供应商切换
- 停用原因记录
- 切换通知

**输入输出定义**:
- **输入**: 供应商ID、停用原因、切换目标
- **输出**: 停用记录、切换记录、通知消息

### 2.4 (暂不实现)RPA数据同步

#### 2.4.1 Agil系统数据同步
**业务场景**: 通过RPA定时从Agil系统抓取供应商数据，保持系统数据的一致性。

**功能描述**:
- 定时数据同步任务
- 数据变更检测
- 同步状态监控
- 同步错误处理

**输入输出定义**:
- **输入**: Agil系统数据、同步配置
- **输出**: 同步结果、变更记录、错误日志

### (暂不实现)2.5 价格计算器

#### 2.5.1 多供应商比价
**业务场景**: 采购时需要比较多个供应商的价格，选择最优的供应商。

**功能描述**:
- 多供应商价格比较
- 最优价格推荐
- 价格分析报告
- 采购建议生成

**输入输出定义**:
- **输入**: 物品ID、采购数量、供应商列表
- **输出**: 价格比较表、最优供应商、采购建议

## 3. 业务对象设计

### 3.1 ER图

```mermaid
erDiagram
    suppliers {
        int id PK
        string code UK
        string name
        string company_address
        string contact_person
        string phone
        string email
        int rating
        string status
        datetime created_at
        datetime updated_at
        int created_by
        int updated_by
    }
    
    items {
        int id PK
        string code UK
        string name
        int category_id FK
        string description
        boolean is_purchasable
        datetime created_at
        datetime updated_at
    }
    
    item_suppliers {
        int id PK
        int item_id FK
        int supplier_id FK
        int priority
        int sort_order
        string status
        int delivery_days
        int quality_rating
        int spq
        int moq
        datetime created_at
        datetime updated_at
    }
    
    supplier_prices {
        int id PK
        int item_supplier_id FK
        decimal unit_price
        int min_quantity
        int max_quantity
        date valid_from
        date valid_to
        string status
        text remarks
        datetime created_at
        datetime updated_at
        int created_by
        int updated_by
    }
    
    sync_tasks {
        int id PK
        string task_type
        string status
        datetime last_sync_time
        string sync_result
        text error_message
        datetime created_at
        datetime updated_at
    }
    
    %% price_history表已移除，价格历史通过supplier_prices表的created_at时间戳计算
    
    suppliers ||--o{ item_suppliers : "supplies"
    items ||--o{ item_suppliers : "supplied_by"
    item_suppliers ||--o{ supplier_prices : "has_prices"
```

### 3.2 业务对象说明

#### 3.2.1 Supplier (供应商)
- **描述**: 供应商实体，包含供应商的基本信息、评级、状态等
- **主要属性**: 编码、名称、地址、联系人、评级、状态
- **业务规则**: 编码唯一，状态只能是启用/停用

#### 3.2.2 ItemSupplier (物品供应商关系)
- **描述**: 物品与供应商的多对多关系，包含优先级、排序、SPQ、MOQ等信息
- **主要属性**: 物品ID、供应商ID、优先级（数字）、排序、状态、SPQ、MOQ
- **业务规则**: 一个物品只能有一个Preferred供应商（priority = 0），可以有多个Alternative供应商（priority >= 1）

#### 3.2.3 SupplierPrice (供应商价格)
- **描述**: 供应商针对特定物品的价格信息，支持阶梯价格
- **主要属性**: 物品供应商关系ID、单价、数量区间、运费、有效期、备注说明
- **业务规则**: 价格有有效期，过期自动失效；价格变更通过发布新记录实现，不删除历史记录

#### 3.2.4 SyncTask (同步任务)
- **描述**: RPA同步任务记录，用于监控数据同步状态
- **主要属性**: 任务类型、状态、同步时间、结果、错误信息
- **业务规则**: 定时执行，记录同步历史和错误信息

#### 3.2.5 PriceHistory (价格历史) - 已移除
- **描述**: 价格历史通过 `supplier_prices` 表的记录来实现
- **主要属性**: 通过 `supplier_prices` 表的 `created_at` 时间戳计算历史
- **业务规则**: 不删除历史记录，通过发布新价格记录来实现价格变更

### 3.3 实体关系描述

- **Supplier ↔ ItemSupplier**: 一对多关系，一个供应商可以供应多个物品
- **Item ↔ ItemSupplier**: 一对多关系，一个物品可以有多个供应商
- **ItemSupplier ↔ SupplierPrice**: 一对多关系，一个物品供应商关系可以有多个价格记录
- **SupplierPrice**: 价格记录不删除，通过 `created_at` 时间戳计算历史
- **SyncTask**: 独立实体，用于管理数据同步任务

## 4. 数据库表设计

### 4.1 suppliers (供应商表)

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY, AUTOINCREMENT | 主键ID |
| code | VARCHAR(50) | UNIQUE, NOT NULL | 供应商编码 |
| name | VARCHAR(200) | NOT NULL | 供应商名称 |
| company_address | TEXT | NULL | 公司地址 |
| contact_person | VARCHAR(100) | NULL | 联系人 |
| phone | VARCHAR(50) | NULL | 联系电话 |
| email | VARCHAR(200) | NULL | 邮箱地址 |
| rating | INTEGER | DEFAULT 0 | 供应商评级 (1-5) |
| status | VARCHAR(20) | DEFAULT 'active' | 状态 (active/inactive) |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |
| created_by | INTEGER | NULL | 创建人ID |
| updated_by | INTEGER | NULL | 更新人ID |

**索引**:
- `idx_suppliers_code` ON `code`
- `idx_suppliers_name` ON `name`
- `idx_suppliers_status` ON `status`
- `idx_suppliers_rating` ON `rating`

### 4.2 item_suppliers (物品供应商关系表)

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY, AUTOINCREMENT | 主键ID |
| item_id | INTEGER | NOT NULL | 物品ID |
| supplier_id | INTEGER | NOT NULL | 供应商ID |
| priority | INTEGER | DEFAULT 1 | 优先级 (0=preferred, 1+=alternative) |
| sort_order | INTEGER | DEFAULT 0 | 排序顺序 |
| status | VARCHAR(20) | DEFAULT 'active' | 状态 (active/inactive) |
| delivery_days | INTEGER | DEFAULT 7 | 交货天数 |
| quality_rating | INTEGER | DEFAULT 3 | 质量评级 (1-5) |
| spq | INTEGER | DEFAULT 1 | 标准包装数量 |
| moq | INTEGER | DEFAULT 1 | 最小订购数量 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

**索引**:
- `idx_item_suppliers_item_id` ON `item_id`
- `idx_item_suppliers_supplier_id` ON `supplier_id`
- `idx_item_suppliers_priority` ON `priority`
- `idx_item_suppliers_status` ON `status`
- `UNIQUE(item_id, supplier_id)`

### 4.3 supplier_prices (供应商价格表)

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY, AUTOINCREMENT | 主键ID |
| item_supplier_id | INTEGER | NOT NULL | 物品供应商关系ID |
| unit_price | DECIMAL(10,4) | NOT NULL | 单价 |
| min_quantity | INTEGER | DEFAULT 1 | 最小数量 |
| max_quantity | INTEGER | NULL | 最大数量 (NULL表示无上限) |
| valid_from | DATE | NOT NULL | 生效日期 |
| valid_to | DATE | NULL | 失效日期 (NULL表示永久有效) |
| status | VARCHAR(20) | DEFAULT 'active' | 状态 (active/inactive) |
| remarks | TEXT | NULL | 价格备注说明 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |
| created_by | INTEGER | NULL | 创建人ID |
| updated_by | INTEGER | NULL | 更新人ID |

**索引**:
- `idx_supplier_prices_item_supplier_id` ON `item_supplier_id`
- `idx_supplier_prices_valid_from` ON `valid_from`
- `idx_supplier_prices_valid_to` ON `valid_to`
- `idx_supplier_prices_status` ON `status`
- `idx_supplier_prices_created_at` ON `created_at`

### 4.4 (暂不实现)sync_tasks (同步任务表)

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY, AUTOINCREMENT | 主键ID |
| task_type | VARCHAR(50) | NOT NULL | 任务类型 |
| status | VARCHAR(20) | DEFAULT 'pending' | 状态 (pending/running/completed/failed) |
| last_sync_time | DATETIME | NULL | 最后同步时间 |
| sync_result | TEXT | NULL | 同步结果 |
| error_message | TEXT | NULL | 错误信息 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

**索引**:
- `idx_sync_tasks_type` ON `task_type`
- `idx_sync_tasks_status` ON `status`
- `idx_sync_tasks_last_sync` ON `last_sync_time`

### 4.5 price_history (价格历史表) - 已移除

**说明**: 价格历史通过 `supplier_prices` 表的记录来实现，不删除历史记录，通过 `created_at` 时间戳来计算价格历史。价格变更通过发布新价格记录来实现，同一时间存在多个价格时以 `created_at` 最近的为准。

## 5. 业务规则

### 5.1 供应商管理规则

#### 5.1.1 供应商编码规则
- 供应商编码自动生成，格式为：`SUP` + 6位数字序列
- 编码唯一，不允许重复
- 编码生成后不可修改

#### 5.1.2 供应商状态管理
- 供应商状态只能是 `active` 或 `inactive`
- 停用供应商时，需要选择替代供应商
- 停用原因必须记录
- **系统统一**: 所有表的active状态统一使用 `status` 字段，类型为 `VARCHAR(20)`，值为 `active/inactive`

#### 5.1.3 供应商评级规则
- 评级范围：1-5星
- 评级基于交易历史、质量反馈、交货及时性等因素
- 评级变更需要记录原因

### 5.2 价格管理规则

#### 5.2.1 阶梯价格规则
- 价格按数量区间设置，区间不能重叠
- 数量区间必须连续，不能有空白
- 同一物品供应商关系在同一时间只能有一个有效价格

#### 5.2.2 价格有效期规则
- 价格必须有生效日期
- 价格必须有失效日期, 最长不得超过三年(365 * 3天)
- 价格过期后自动失效，需要设置新价格

#### 5.2.3 价格变更规则
- 价格变更通过发布新价格记录实现，不删除历史记录
- 发布新价格时必须填写备注说明
- 同一时间存在多个价格时，以 `created_at` 时间最近的为准
- 变更人必须记录

### 5.3 多供应商支持规则

#### 5.3.1 优先级规则
- 一个物品只能有一个Preferred供应商（priority = 0）
- 可以有多个Alternative供应商（priority >= 1）
- Alternative供应商按priority数值排序，数值越小优先级越高
- SPQ、MOQ配置在物品供应商关系层面，不细化到价格层面

#### 5.3.2 供应商切换规则
- Preferred供应商（priority = 0）停用时，自动切换到priority最小的Alternative供应商
- 切换时需要记录切换原因和时间
- 切换后需要通知相关人员

#### 5.3.3 供应商停用规则
- 停用供应商时必须选择替代供应商
- 停用原因必须记录
- 停用后相关价格自动失效

### (暂不实现)5.4 数据同步规则

#### 5.4.1 RPA同步规则
- 定时执行，默认每天凌晨2点
- 同步失败时记录错误信息
- 同步成功时更新最后同步时间

#### 5.4.2 数据冲突处理
- 本地数据优先原则
- 冲突时记录冲突信息
- 提供手动解决冲突的界面

### (暂不实现)5.5 价格计算器规则

#### 5.5.1 比价规则
- 只比较有效价格
- 考虑运费、交货时间、质量评级
- 提供综合评分排序

#### 5.5.2 最优供应商选择
- 综合考虑价格、质量、交货时间
- 提供采购建议
- 记录选择原因

### 5.6 异常处理规则

#### 5.6.1 数据异常处理
- 价格数据异常时自动标记
- 供应商信息不完整时提醒完善
- 同步失败时发送告警

#### 5.6.2 业务异常处理
- 供应商停用时的替代方案
- 价格过期时的处理流程
- 数据冲突时的解决机制

### 5.7 权限和安全要求

#### 5.7.1 权限控制
- 供应商信息管理：物品管理员及以上权限
- 价格管理：物品管理员及以上权限
- 同步任务管理：系统管理员权限
- 价格历史查看：物品管理员及以上权限

#### 5.7.2 数据安全
- 操作日志完整记录
- 数据备份和恢复机制

#### 5.7.3 审计要求
- 所有价格变更记录
- 供应商状态变更记录
- 同步任务执行记录
- 用户操作日志记录

## 7. 接口设计

### 7.1 供应商管理接口

#### 7.1.1 供应商列表
```
GET /api/admin/suppliers
参数: page, size, sort, filter, search
返回: 供应商列表、总数、分页信息
```

#### 7.1.2 供应商详情
```
GET /api/admin/suppliers/{id}
返回: 供应商详细信息
```

#### 7.1.3 创建供应商
```
POST /api/admin/suppliers
参数: 供应商信息
返回: 创建的供应商信息
```

#### 7.1.4 更新供应商
```
PUT /api/admin/suppliers/{id}
参数: 更新的供应商信息
返回: 更新后的供应商信息
```

#### 7.1.5 停用供应商
```
POST /api/admin/suppliers/{id}/deactivate
参数: 停用原因、替代供应商
返回: 停用结果
```

### 7.2 价格管理接口

#### 7.2.1 价格列表
```
GET /api/admin/suppliers/{supplier_id}/prices
参数: item_id, status
返回: 价格列表
```

#### 7.2.2 设置价格
```
POST /api/admin/suppliers/{supplier_id}/prices
参数: 价格信息
返回: 创建的价格信息
```

#### 7.2.3 更新价格
```
PUT /api/admin/suppliers/{supplier_id}/prices/{price_id}
参数: 更新的价格信息
返回: 更新后的价格信息
```

#### 7.2.4 价格历史
```
GET /api/admin/suppliers/{supplier_id}/prices/{price_id}/history
返回: 价格变更历史
```

(暂不实现)### 7.3 价格计算器接口

#### 7.3.1 多供应商比价
```
POST /api/admin/price-calculator/compare
参数: item_id, quantity
返回: 价格比较结果、最优供应商
```

#### 7.3.2 价格分析
```
GET /api/admin/price-calculator/analysis
参数: item_id, date_range
返回: 价格趋势分析
```

(暂不实现)### 7.4 同步任务接口

#### 7.4.1 同步状态
```
GET /api/admin/sync/status
返回: 同步任务状态
```

#### 7.4.2 手动同步
```
POST /api/admin/sync/manual
参数: task_type
返回: 同步结果
```

#### 7.4.3 同步历史
```
GET /api/admin/sync/history
参数: task_type, date_range
返回: 同步历史记录
```

## 8. 数据迁移策略

不考虑现有数据迁移, 直接运行脚本a_, b_, c_重建数据库与数据. 

## 9. 测试策略

### 9.1 单元测试
- 供应商CRUD操作测试
- 价格计算逻辑测试
- 数据验证规则测试

### 9.2 集成测试
- 供应商与物品关联测试
- 价格与采购流程集成测试
- 同步任务执行测试

### 9.3 性能测试
- 大量供应商数据加载测试
- 价格计算性能测试
- 并发操作测试

### 9.4 用户验收测试
- 供应商管理流程测试
- 价格设置流程测试
- 比价功能测试

## 10. 部署和运维

### 10.1 部署要求
- 数据库初始化脚本
- 数据迁移脚本
- 配置文件模板

### 10.2 监控要求
- 同步任务执行监控
- 价格过期提醒监控
- 系统性能监控

### 10.3 备份策略
- 供应商数据定期备份
- 价格历史数据备份
- 同步任务日志备份

---

*本文档将随着系统开发和需求变更持续更新* 