# 消息中心与通知功能需求文档

## 📋 需求概述

### 业务背景
BizLinkSpeedy IDM系统需要建立统一的消息中心与通知功能，当发生系统业务事件时向特定用户发送系统通知，同时向其邮箱发送通知邮件。通过及时的通知机制，确保用户能够及时了解系统状态变化和需要处理的事项，提升系统的响应性和用户体验。

### 核心目标
- **实时通知**: 业务事件发生时立即创建系统通知
- **邮件通知**: 通过定时任务扫描通知表，根据状态发送邮件
- **用户管理**: 支持用户自定义通知偏好设置
- **权限控制**: 基于用户角色的通知查看和管理权限
- **状态跟踪**: 完整的通知状态和邮件发送状态跟踪

### 系统定位
消息中心与通知功能是BizLinkSpeedy IDM系统的基础设施模块，为库存管理、审批流程等业务模块提供通知服务支持。通过统一的通知机制，确保业务流程的顺畅进行和用户及时响应。

## 🎯 功能需求

### 1. 通知类型管理

#### 1.1 库存告警通知
- **低库存预警**: 库存低于最小库存阈值时触发
- **缺货预警**: 库存为零时触发紧急预警
- **超储预警**: 库存超过最大库存时提醒注意
- **触发条件**: 库存变更时检查阈值，定时检查库存状态
- **接收对象**: 部门物品管理员

#### 1.2 审批流程通知
- **申请提交通知**: 采购申请提交时通知相关人员
- **审批状态通知**: 审批状态变更时通知申请人和审批人
- **审批完成通知**: 审批通过/拒绝时通知相关人员
- **触发条件**: 审批流程状态变更时
- **接收对象**: 申请人和审批流程中的相关人员

### 2. 通知创建和发送

#### 2.1 通知创建
- **业务集成**: 在业务代码中直接调用通知服务
- **通知内容**: 包含标题、内容、类型、接收人等基本信息
- **业务数据**: 关联相关的业务数据（如库存数量、审批链接等）
- **操作指引**: 提供相关业务操作链接

#### 2.2 邮件发送
- **定时任务**: 每5分钟扫描一次通知表
- **发送状态**: 待发送邮件、已发送邮件、发送失败
- **失败处理**: 记录失败原因，不进行重试
- **手动触发**: 管理员可手动触发邮件发送，方便调试测试

### 3. 通知状态管理

#### 3.1 通知状态
- **阅读状态**: 未读/已读
- **邮件状态**: 待发送邮件/已发送邮件/发送失败
- **永久保留**: 通知历史永久保留，不进行清理

#### 3.2 状态更新
- **阅读更新**: 用户查看通知时自动标记为已读
- **批量操作**: 支持批量标记已读/未读
- **状态同步**: 通知状态与邮件发送状态保持同步

### 4. 用户通知设置

#### 4.1 个人设置
- **邮件开关**: 用户可选择是否接收邮件通知
- **设置存储**: 通知设置集成到用户表中
- **权限控制**: 基于用户角色的通知查看权限

#### 4.2 权限管理
- **普通用户**: 只能查看自己收到的通知
- **超级管理员**: 可在专门的通知管理页面查看所有通知
- **管理权限**: 超级管理员可查看通知状态和邮件发送状态

## 🏗️ 业务对象设计

### 业务对象关系图

```mermaid
erDiagram
    User ||--o{ Notification : receives
    User ||--o{ NotificationEmail : receives
    Notification ||--o{ NotificationEmail : has
    
    User {
        int id PK
        string username
        string email
        boolean email_notifications_enabled
        datetime created_at
        datetime updated_at
    }
    
    Notification {
        int id PK
        int user_id FK
        string title
        string content
        string notification_type
        string status
        json business_data
        string action_url
        datetime created_at
        datetime read_at
    }
    
    NotificationEmail {
        int id PK
        int notification_id FK
        int user_id FK
        string email_status
        string failure_reason
        datetime sent_at
        datetime created_at
    }
    
    SystemConfig {
        int id PK
        string config_key
        string config_value
        string description
        datetime updated_at
    }
```

### 业务对象说明

#### Notification (通知表)
- **作用**: 存储系统通知信息
- **关键属性**: 标题、内容、类型、状态、业务数据、操作链接
- **状态管理**: 未读/已读状态，邮件发送状态
- **业务关联**: 通过business_data字段存储相关业务数据

#### NotificationEmail (邮件发送记录表)
- **作用**: 记录邮件发送状态和结果
- **发送状态**: 待发送、已发送、发送失败
- **失败记录**: 记录发送失败的具体原因
- **关联关系**: 与通知表一对一关联

#### User (用户表扩展)
- **扩展字段**: email_notifications_enabled (是否接收邮件通知)
- **权限控制**: 基于用户角色的通知查看权限
- **个人设置**: 用户可自定义通知偏好

#### SystemConfig (系统配置表)
- **SMTP配置**: 邮件服务器配置信息
- **通知开关**: 系统级通知功能开关
- **配置管理**: 支持环境变量配置

## 🗄️ 数据库表设计

### 1. notifications (通知表)

```sql
CREATE TABLE notifications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    notification_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'unread',
    business_data JSON,
    action_url VARCHAR(500),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    read_at DATETIME,
    
    INDEX idx_notifications_user_id (user_id),
    INDEX idx_notifications_type (notification_type),
    INDEX idx_notifications_status (status),
    INDEX idx_notifications_created_at (created_at)
);
```

**字段说明**:
- `user_id`: 接收用户ID，关联users表
- `title`: 通知标题
- `content`: 通知内容
- `notification_type`: 通知类型，inventory_alert(库存告警)、approval_flow(审批流程)
- `status`: 通知状态，unread(未读)、read(已读)
- `business_data`: 业务相关数据，JSON格式存储
- `action_url`: 相关业务操作链接
- `created_at`: 创建时间
- `read_at`: 阅读时间

### 2. notification_emails (邮件发送记录表)

```sql
CREATE TABLE notification_emails (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    notification_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    email_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    failure_reason TEXT,
    sent_at DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_notification_emails_notification_id (notification_id),
    INDEX idx_notification_emails_user_id (user_id),
    INDEX idx_notification_emails_status (email_status),
    INDEX idx_notification_emails_created_at (created_at)
);
```

**字段说明**:
- `notification_id`: 关联的通知ID
- `user_id`: 接收用户ID
- `email_status`: 邮件发送状态，pending(待发送)、sent(已发送)、failed(发送失败)
- `failure_reason`: 发送失败原因
- `sent_at`: 发送成功时间
- `created_at`: 创建时间

### 3. 用户表扩展

```sql
-- 在users表中添加字段
ALTER TABLE users ADD COLUMN email_notifications_enabled BOOLEAN DEFAULT TRUE;
```

**字段说明**:
- `email_notifications_enabled`: 是否接收邮件通知，默认开启

### 4. system_configs (系统配置表)

```sql
CREATE TABLE system_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    description VARCHAR(200),
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_system_configs_key (config_key)
);
```

**配置项说明**:
- `smtp_host`: SMTP服务器地址
- `smtp_port`: SMTP服务器端口
- `smtp_username`: SMTP用户名
- `smtp_password`: SMTP密码
- `smtp_use_tls`: 是否使用TLS加密
- `notification_enabled`: 系统通知功能开关
- `email_send_interval`: 邮件发送间隔（分钟）

## 📋 业务规则

### 1. 通知创建规则

#### 1.1 触发规则
- **库存告警**: 库存变更时检查阈值，低于最小库存或超过最大库存时触发
- **审批流程**: 审批状态变更时立即触发通知
- **业务集成**: 在业务代码中直接调用通知服务，确保及时性

#### 1.2 内容规则
- **标题规范**: 简洁明了的通知标题，便于用户快速理解
- **内容完整**: 包含必要的业务信息和操作指引
- **操作链接**: 提供直接跳转到相关业务页面的链接
- **英文内容**: 通知内容使用英文，支持国际化

### 2. 邮件发送规则

#### 2.1 发送规则
- **定时扫描**: 每5分钟扫描一次待发送邮件
- **状态检查**: 只发送状态为pending的邮件
- **失败处理**: 发送失败时记录原因，不进行重试
- **手动触发**: 管理员可手动触发邮件发送，便于调试

#### 2.2 模板规则
- **类型模板**: 按通知类型设计不同的邮件模板
- **内容包含**: 通知标题、内容、相关业务数据、操作指引URL
- **英文邮件**: 邮件内容使用英文
- **格式规范**: 支持HTML格式，确保邮件美观

### 3. 权限控制规则

#### 3.1 查看权限
- **普通用户**: 只能查看自己收到的通知
- **超级管理员**: 可查看所有通知和邮件发送状态
- **数据隔离**: 基于用户ID的数据权限隔离

#### 3.2 操作权限
- **批量操作**: 普通用户可批量标记已读/未读，批量删除
- **管理操作**: 超级管理员可查看发送日志、手动触发邮件
- **设置权限**: 用户可修改自己的邮件通知开关

### 4. 状态管理规则

#### 4.1 通知状态
- **状态流转**: 未读 → 已读（用户查看时自动更新）
- **永久保留**: 通知历史永久保留，不进行清理
- **状态同步**: 通知状态与邮件发送状态保持同步

#### 4.2 邮件状态
- **状态流转**: 待发送 → 已发送/发送失败
- **失败记录**: 详细记录发送失败的具体原因
- **状态更新**: 邮件发送完成后更新状态和时间

## 🎨 界面设计

### 1. 通知列表页面

#### 1.1 布局设计
- **左侧筛选区**: 通知类型筛选、状态筛选、时间筛选
- **右侧列表区**: 通知列表，支持分页显示
- **顶部操作区**: 批量操作按钮（标记已读、删除等）
- **搜索功能**: 按通知标题搜索

#### 1.2 功能特性
- **筛选功能**: 按类型、状态、时间进行筛选
- **搜索功能**: 支持按标题关键词搜索
- **批量操作**: 支持批量标记已读/未读、批量删除
- **分页显示**: 支持分页浏览，每页显示数量可配置

### 2. 通知详情页面

#### 2.1 布局设计
- **通知内容区**: 显示完整的通知标题和内容
- **业务操作区**: 提供相关业务操作链接
- **状态信息区**: 显示通知状态和创建时间

#### 2.2 功能特性
- **内容展示**: 完整显示通知内容，支持富文本格式
- **操作链接**: 直接跳转到相关业务页面
- **状态更新**: 查看时自动标记为已读

### 3. 系统配置页面

#### 3.1 SMTP配置
- **服务器配置**: SMTP主机、端口、用户名、密码
- **加密设置**: TLS/SSL加密选项
- **测试功能**: 发送测试邮件验证配置
- **保存功能**: 配置保存和更新

#### 3.2 通知开关
- **功能开关**: 系统级通知功能开关
- **邮件开关**: 邮件发送功能开关
- **配置保存**: 开关状态保存和更新

### 4. 通知管理页面（超级管理员）

#### 4.1 通知管理
- **全部通知**: 查看所有用户的通知列表
- **状态统计**: 各类型通知的发送数量统计
- **邮件状态**: 邮件发送状态和失败原因查看

#### 4.2 邮件管理
- **发送状态**: 邮件发送成功率统计图表
- **手动触发**: 手动触发邮件发送功能
- **发送日志**: 详细的邮件发送日志查看

## 🔧 技术实现

### 1. 架构设计

#### 1.1 服务架构
- **通知服务**: 统一的通知创建和管理服务
- **邮件服务**: 邮件发送和状态管理服务
- **定时任务**: 邮件发送的定时扫描任务
- **配置管理**: 系统配置的动态管理

#### 1.2 数据流
```
业务事件 → 通知服务 → 通知表 → 定时任务 → 邮件服务 → 邮件发送
```

### 2. 核心服务

#### 2.1 通知服务 (NotificationService)
- **创建通知**: 根据业务事件创建系统通知
- **状态管理**: 管理通知的阅读状态
- **查询服务**: 提供通知查询和筛选功能

#### 2.2 邮件服务 (EmailService)
- **邮件发送**: 发送邮件并更新状态
- **模板渲染**: 根据通知类型渲染邮件模板
- **状态跟踪**: 跟踪邮件发送状态和结果

#### 2.3 定时任务 (EmailScheduler)
- **任务调度**: 每5分钟执行一次邮件发送
- **状态扫描**: 扫描待发送的邮件
- **批量处理**: 批量处理邮件发送任务

### 3. 集成方式

#### 3.1 业务集成
- **直接调用**: 在业务代码中直接调用通知服务
- **事件驱动**: 通过事件机制触发通知创建
- **数据关联**: 通知与业务数据的关联存储

#### 3.2 配置管理
- **环境变量**: 支持环境变量配置SMTP信息
- **动态配置**: 配置变更不需要重启服务
- **配置验证**: 配置保存时进行有效性验证

### 4. 国际化支持

#### 4.1 界面国际化
- **多语言支持**: 管理界面支持i18n国际化
- **语言切换**: 支持动态语言切换
- **语言包管理**: 统一管理多语言资源

#### 4.2 内容国际化
- **通知内容**: 系统通知内容使用英文
- **邮件内容**: 邮件模板使用英文
- **用户界面**: 管理界面支持多语言

## 📊 数据统计

### 1. 统计指标

#### 1.1 通知统计
- **发送数量**: 各类型通知的发送数量统计
- **阅读率**: 通知的阅读率统计
- **响应时间**: 通知从发送到阅读的时间统计

#### 1.2 邮件统计
- **发送成功率**: 邮件发送成功率统计
- **失败原因**: 邮件发送失败的原因分析
- **发送趋势**: 邮件发送数量的时间趋势

### 2. 统计展示

#### 2.1 图表类型
- **饼图**: 通知类型分布、邮件状态分布
- **柱状图**: 各类型通知发送数量
- **趋势图**: 邮件发送数量时间趋势

#### 2.2 数据导出
- **统计报表**: 支持统计数据的导出
- **历史数据**: 支持历史数据的查询和导出
- **实时更新**: 统计数据实时更新

## 🚀 部署和运维

### 1. 部署要求

#### 1.1 环境配置
- **环境变量**: 支持环境变量配置SMTP信息
- **配置热更新**: 配置变更不需要重启服务
- **单实例部署**: 不考虑多实例部署场景

#### 1.2 依赖要求
- **数据库**: 支持SQLite/PostgreSQL/MySQL
- **邮件服务**: 支持SMTP邮件发送
- **定时任务**: 支持定时任务调度

### 2. 运维管理

#### 2.1 监控管理
- **状态监控**: 通知和邮件发送状态监控
- **日志查看**: 详细的运行日志查看
- **错误处理**: 异常情况的记录和处理

#### 2.2 维护操作
- **配置管理**: 系统配置的动态管理
- **测试功能**: 邮件发送测试功能
- **手动触发**: 管理员手动触发邮件发送

## 📝 测试需求

### 1. 功能测试

#### 1.1 通知功能测试
- **通知创建**: 测试各种业务场景下的通知创建
- **状态管理**: 测试通知状态的正确更新
- **权限控制**: 测试不同用户的权限控制

#### 1.2 邮件功能测试
- **邮件发送**: 测试邮件发送功能
- **模板渲染**: 测试邮件模板的正确渲染
- **状态跟踪**: 测试邮件发送状态的跟踪

### 2. 集成测试

#### 2.1 业务集成测试
- **库存告警**: 测试库存告警通知的完整流程
- **审批流程**: 测试审批流程通知的完整流程
- **数据一致性**: 测试通知数据与业务数据的一致性

#### 2.2 系统集成测试
- **定时任务**: 测试定时任务的正确执行
- **配置管理**: 测试配置的动态更新
- **错误处理**: 测试异常情况的处理

