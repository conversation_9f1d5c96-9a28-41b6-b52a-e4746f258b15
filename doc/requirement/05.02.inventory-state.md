# 库存列表状态逻辑设计文档

## 1. 概述

本文档描述了库存管理模块中库存列表的状态逻辑，包括状态判断规则、显示逻辑、统计口径等核心设计。

## 2. 状态判断核心逻辑

### 2.1 状态类型

库存状态分为四种类型：
- **正常**：库存在合理范围内（绿色标签）
- **低库存**：库存大于0但小于等于最小库存（橙色标签）
- **缺货**：库存为0（红色标签）
- **超储**：库存超过最大库存（蓝色标签）

### 2.2 状态判断规则

所有状态判断都通过后端的统一函数 `_determine_status` 进行，确保统计和显示的一致性。当最小库存为0时，即使当前库存为0也不会被判断为缺货状态。

## 3. 库存记录分类

### 3.1 有库存记录

存在有效的 `DepartmentInventory` 记录且 `is_active = true` 时，显示完整的库存信息，包括当前库存、最小/最大库存、状态、价值、最后更新时间等。

### 3.2 无库存记录

不存在库存记录或记录被禁用时，相关字段显示 "-"，状态显示 "无库存" 标签。

### 3.3 已禁用库存

存在库存记录但 `is_active = false` 时，状态优先显示 "已禁用" 标签（红色），覆盖其他状态判断。

## 4. 统计口径一致性

Overview API 的统计和列表中的状态显示使用完全相同的状态判断逻辑，确保数据一致性。统计按库存数量进行，而非物品数量。

## 5. 特殊状态处理

- 最小库存为0的物品允许零库存，不会被判断为缺货
- 无最大库存限制的物品不会出现超储状态
- 通过 `is_active` 字段控制库存记录是否参与状态判断

## 6. 数据一致性保证

- 统一的状态判断函数避免数据不一致
- 状态在入库/出库、设置变更后自动更新
- 状态变化会触发相应的库存预警

## 7. 用户界面设计

有库存记录显示实际状态，无库存记录显示 "-" 或 "无库存"，已禁用优先显示 "已禁用" 状态。

## 8. 总结

当前设计确保统计口径和显示口径完全一致，状态判断规则清晰明确，支持库存记录的激活/禁用管理，具有良好的扩展性和可靠性。
