#!/usr/bin/env python3
"""
Migration script to remove shipping_cost column from supplier_prices table
Run this script to update the database schema after shipping cost removal
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import engine
from sqlalchemy import text

def remove_shipping_cost_column():
    """Remove shipping_cost column from supplier_prices table"""
    
    try:
        with engine.connect() as connection:
            # Start transaction
            trans = connection.begin()
            
            try:
                # Check if column exists first
                result = connection.execute(text("""
                    SELECT COUNT(*) as count
                    FROM information_schema.columns
                    WHERE table_name = 'supplier_prices' 
                    AND column_name = 'shipping_cost'
                """))
                
                column_exists = result.fetchone()[0] > 0
                
                if column_exists:
                    print("Removing shipping_cost column from supplier_prices table...")
                    
                    # Remove the shipping_cost column
                    connection.execute(text("""
                        ALTER TABLE supplier_prices DROP COLUMN shipping_cost
                    """))
                    
                    print("✅ Successfully removed shipping_cost column")
                else:
                    print("ℹ️ shipping_cost column does not exist, skipping removal")
                
                # Commit transaction
                trans.commit()
                print("✅ Migration completed successfully")
                
            except Exception as e:
                # Rollback transaction on error
                trans.rollback()
                raise e
                
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        raise e

if __name__ == "__main__":
    print("🚀 Starting migration to remove shipping_cost column...")
    
    try:
        remove_shipping_cost_column()
        print("🎉 Migration completed successfully!")
    except Exception as e:
        print(f"💥 Migration failed: {str(e)}")
        sys.exit(1)