#!/usr/bin/env python3
"""
测试API启动脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.main import app
from app.apps.admin.routes import router as admin_router

def test_api_startup():
    """测试API启动"""
    print("🚀 测试API启动...")
    
    try:
        # 检查主应用
        print("✅ 主应用创建成功")
        
        # 检查admin路由
        print("✅ Admin路由加载成功")
        
        # 检查路由数量
        routes = [route for route in admin_router.routes]
        print(f"✅ Admin路由数量: {len(routes)}")
        
        # 检查通知相关路由
        notification_routes = [route for route in routes if "notification" in str(route.path)]
        print(f"✅ 通知相关路由数量: {len(notification_routes)}")
        
        print("\n🎉 API启动测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ API启动测试失败: {str(e)}")
        return False


if __name__ == "__main__":
    success = test_api_startup()
    sys.exit(0 if success else 1)
