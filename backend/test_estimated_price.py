#!/usr/bin/env python3
"""
测试预估价格是否正确返回
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_db
from app.services.purchase_request_service import PurchaseRequestService

def test_estimated_price():
    """测试预估价格是否正确返回"""
    
    # 获取数据库会话
    db = next(get_db())
    
    try:
        # 创建服务实例
        service = PurchaseRequestService(db)
        
        # 获取第一个采购申请
        requests = service.get_all_requests()
        
        if not requests:
            print("没有找到采购申请")
            return
        
        request = requests[0]
        print(f"测试采购申请: {request.request_no}")
        print(f"状态: {request.status}")
        print(f"物品数量: {len(request.items) if request.items else 0}")
        
        if request.items:
            print("\n物品详情:")
            for i, item in enumerate(request.items):
                print(f"  {i+1}. {item.item_name}")
                print(f"     编码: {item.item_code}")
                print(f"     图片URL: {getattr(item, 'item_image_url', '字段不存在')}")
                print(f"     SPQ数量: {item.spq_quantity}")
                print(f"     SPQ个数: {item.spq_count}")
                print(f"     单位: {item.spq_unit}")
                print(f"     预估单价: {getattr(item, 'estimated_unit_price', '字段不存在')}")
                print(f"     预估总价: {getattr(item, 'estimated_total_price', '字段不存在')}")
                print()
        else:
            print("没有物品明细")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_estimated_price()
