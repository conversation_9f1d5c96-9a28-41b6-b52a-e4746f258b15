[tool.poetry]
name = "bizlinkspeedy-idm-backend"
version = "1.0.0"
description = "BizLinkSpeedy IDM Backend API"
authors = ["Xyz9Selu <<EMAIL>>"]
package-mode = false

[tool.poetry.dependencies]
python = "^3.12"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
sqlalchemy = "^2.0.23"
pydantic = "^2.5.0"
pydantic-settings = "^2.1.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
python-multipart = "^0.0.6"
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
bcrypt = "4.0.1"
python-dotenv = "^1.0.0"
email-validator = "^2.1.0"
aiofiles = "^23.2.1"
Pillow = "^10.1.0"
openpyxl = "^3.1.5"
pandas = "^2.3.1"
psycopg2-binary = "^2.9.10"
pymysql = "^1.1.1"
requests = "^2.32.4"
aiosmtplib = "^4.0.2"
apscheduler = "^3.11.0"


[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-cov = "^4.1.0"
pytest-html = "^4.1.1"
httpx = "^0.25.2"
faker = "^20.1.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
