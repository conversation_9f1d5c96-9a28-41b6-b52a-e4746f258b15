#!/usr/bin/env python3
"""
测试运行脚本
展示如何使用重构后的测试结构
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_tests(test_type=None, pattern=None, verbose=False):
    """运行测试"""
    cmd = ["poetry", "run", "pytest"]
    
    if verbose:
        cmd.append("-v")
    
    if test_type:
        cmd.extend(["-m", test_type])
    elif pattern:
        cmd.extend(["-k", pattern])
    else:
        cmd.append("tests/")
    
    print(f"运行命令: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=Path(__file__).parent)
    return result.returncode


def main():
    parser = argparse.ArgumentParser(description="运行测试")
    parser.add_argument(
        "--type", 
        choices=["auth", "user", "department", "permission", "integration"],
        help="运行特定类型的测试"
    )
    parser.add_argument(
        "--pattern", 
        help="运行匹配模式的测试"
    )
    parser.add_argument(
        "-v", "--verbose", 
        action="store_true",
        help="详细输出"
    )
    
    args = parser.parse_args()
    
    if args.type and args.pattern:
        print("错误: 不能同时指定 --type 和 --pattern")
        sys.exit(1)
    
    exit_code = run_tests(args.type, args.pattern, args.verbose)
    sys.exit(exit_code)


if __name__ == "__main__":
    main() 