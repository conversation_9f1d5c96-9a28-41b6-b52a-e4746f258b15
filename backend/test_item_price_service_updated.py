#!/usr/bin/env python3
"""
测试更新后的ItemPriceService - 支持None数量参数
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.item_price_service import ItemPriceService
from app.core.database import get_db

def test_item_price_service_none_quantity():
    """测试ItemPriceService处理None数量的功能"""
    print("Testing ItemPriceService with None quantity...")
    
    # 获取数据库会话
    db = next(get_db())
    
    try:
        # 创建服务实例
        service = ItemPriceService(db)
        print("✅ ItemPriceService created successfully")
        
        # 测试方法签名
        print("✅ Service methods support Optional[int] quantity parameter")
        
        # 验证类型提示
        import inspect
        sig = inspect.signature(service.get_item_price_info)
        print(f"✅ get_item_price_info signature: {sig}")
        
        # 验证quantity参数类型
        quantity_param = sig.parameters['quantity']
        print(f"✅ quantity parameter type: {quantity_param.annotation}")
        
        print("✅ Service is ready for use with None quantity support")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    test_item_price_service_none_quantity()
