"""
通知集成服务单元测试
测试库存告警、库存变更和审批流程的通知创建功能
"""

import pytest
from decimal import Decimal
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.services.******************************** import NotificationIntegrationService, NotificationType
from app.models.inventory import DepartmentInventory, InventoryChangeRecord, InventoryChangeType
from app.models.user import User, Department
from app.models.item import Item
from app.models.notification import Notification
from tests.base import BaseTestCase


class TestNotificationIntegrationService(BaseTestCase):
    """通知集成服务测试类"""
    
    def setup_test_data(self):
        """设置测试数据"""
        # 创建测试部门
        self.department = self.create_test_department(
            name="测试部门",
            code="TEST"
        )
        
        # 创建测试物品
        self.item = self.create_test_item(
            name="测试物品",
            code="TEST001",
            purchase_unit="个"
        )
        
        # 创建部门管理员用户
        self.admin_user = self.create_test_user(
            username="admin",
            full_name="部门管理员",
            department_id=self.department.id,
            is_superuser=True
        )
        
        # 创建普通用户
        self.regular_user = self.create_test_user(
            username="user",
            full_name="普通用户",
            department_id=self.department.id,
            is_superuser=False
        )
        
        # 创建库存记录
        self.inventory = DepartmentInventory(
            department_id=self.department.id,
            item_id=self.item.id,
            current_quantity=Decimal("10.0"),
            min_quantity=Decimal("5.0"),
            max_quantity=Decimal("20.0"),
            is_active=True
        )
        self.db.add(self.inventory)
        self.db.commit()
        self.db.refresh(self.inventory)
    
    def create_test_item(self, **kwargs):
        """创建测试物品"""
        from app.models.item import Item, ItemCategory
        
        # 创建测试分类
        category = ItemCategory(
            name="测试分类",
            is_active=True
        )
        self.db.add(category)
        self.db.commit()
        self.db.refresh(category)
        
        item = Item(
            name=kwargs.get("name", "测试物品"),
            code=kwargs.get("code", "TEST001"),
            purchase_unit=kwargs.get("purchase_unit", "个"),
            description=kwargs.get("description", "测试用物品"),
            category_id=category.id,
            is_active=True
        )
        
        self.db.add(item)
        self.db.commit()
        self.db.refresh(item)
        return item
    
    def test_create_inventory_alert_notification_low_stock(self):
        """测试创建低库存预警通知"""
        service = NotificationIntegrationService(self.db)
        
        # 创建低库存预警通知
        notification = service.create_inventory_alert_notification(
            department_id=self.department.id,
            item_id=self.item.id,
            alert_type="low_stock",
            current_quantity=Decimal("3.0"),
            threshold_value=Decimal("5.0")
        )
        
        assert notification is not None
        assert notification.title == "库存预警 - 测试物品"
        assert "库存不足" in notification.content
        assert notification.notification_type == NotificationType.INVENTORY_ALERT
        assert notification.user_id == self.admin_user.id
        assert notification.status == "unread"
    
    def test_create_inventory_alert_notification_out_of_stock(self):
        """测试创建缺货通知"""
        service = NotificationIntegrationService(self.db)
        
        # 创建缺货通知
        notification = service.create_inventory_alert_notification(
            department_id=self.department.id,
            item_id=self.item.id,
            alert_type="out_of_stock",
            current_quantity=Decimal("0.0")
        )
        
        assert notification is not None
        assert notification.title == "缺货通知 - 测试物品"
        assert "已缺货" in notification.content
        assert notification.notification_type == NotificationType.INVENTORY_ALERT
        assert notification.user_id == self.admin_user.id
        assert notification.status == "unread"
    
    def test_create_inventory_alert_notification_overstock(self):
        """测试创建超储提醒通知"""
        service = NotificationIntegrationService(self.db)
        
        # 创建超储提醒通知
        notification = service.create_inventory_alert_notification(
            department_id=self.department.id,
            item_id=self.item.id,
            alert_type="overstock",
            current_quantity=Decimal("25.0"),
            threshold_value=Decimal("20.0")
        )
        
        assert notification is not None
        assert notification.title == "超储提醒 - 测试物品"
        assert "库存超储" in notification.content
        assert notification.notification_type == NotificationType.INVENTORY_ALERT
        assert notification.user_id == self.admin_user.id
        assert notification.status == "unread"
    
    def test_create_inventory_alert_notification_unknown_type(self):
        """测试创建未知预警类型的通知"""
        service = NotificationIntegrationService(self.db)
        
        # 创建未知预警类型的通知
        notification = service.create_inventory_alert_notification(
            department_id=self.department.id,
            item_id=self.item.id,
            alert_type="unknown_type",
            current_quantity=Decimal("10.0")
        )
        
        assert notification is None
    
    def test_create_inventory_alert_notification_no_admin_users(self):
        """测试没有管理员用户时不创建通知"""
        # 删除管理员用户
        self.db.delete(self.admin_user)
        self.db.commit()
        
        service = NotificationIntegrationService(self.db)
        
        # 尝试创建通知
        notification = service.create_inventory_alert_notification(
            department_id=self.department.id,
            item_id=self.item.id,
            alert_type="low_stock",
            current_quantity=Decimal("3.0"),
            threshold_value=Decimal("5.0")
        )
        
        assert notification is None
    
    def test_create_inventory_change_notification_manual_in(self):
        """测试创建手工入库通知"""
        service = NotificationIntegrationService(self.db)
        
        # 创建库存变更记录
        change_record = InventoryChangeRecord(
            department_id=self.department.id,
            item_id=self.item.id,
            before_quantity=Decimal("10.0"),
            after_quantity=Decimal("15.0"),
            change_quantity=Decimal("5.0"),
            change_type=InventoryChangeType.MANUAL_IN,
            change_reason="手工入库",
            operator_id=self.regular_user.id
        )
        self.db.add(change_record)
        self.db.commit()
        self.db.refresh(change_record)
        
        # 创建库存变更通知
        notification = service.create_inventory_change_notification(
            change_record=change_record,
            operator_id=self.regular_user.id
        )
        
        assert notification is not None
        assert notification.title == "入库通知 - 测试物品"
        assert "入库" in notification.content
        assert notification.notification_type == NotificationType.INVENTORY_ALERT
        assert notification.user_id == self.admin_user.id
        assert notification.status == "unread"
    
    def test_create_inventory_change_notification_pickup_out(self):
        """测试创建出库通知"""
        service = NotificationIntegrationService(self.db)
        
        # 创建库存变更记录
        change_record = InventoryChangeRecord(
            department_id=self.department.id,
            item_id=self.item.id,
            before_quantity=Decimal("10.0"),
            after_quantity=Decimal("8.0"),
            change_quantity=Decimal("-2.0"),
            change_type=InventoryChangeType.PICKUP_OUT,
            change_reason="工人领取",
            operator_id=self.regular_user.id
        )
        self.db.add(change_record)
        self.db.commit()
        self.db.refresh(change_record)
        
        # 创建库存变更通知
        notification = service.create_inventory_change_notification(
            change_record=change_record,
            operator_id=self.regular_user.id
        )
        
        assert notification is not None
        assert notification.title == "出库通知 - 测试物品"
        assert "出库" in notification.content
        assert notification.notification_type == NotificationType.INVENTORY_ALERT
        assert notification.user_id == self.admin_user.id
        assert notification.status == "unread"
    
    def test_create_inventory_change_notification_adjust(self):
        """测试创建库存调整通知"""
        service = NotificationIntegrationService(self.db)
        
        # 创建库存变更记录
        change_record = InventoryChangeRecord(
            department_id=self.department.id,
            item_id=self.item.id,
            before_quantity=Decimal("10.0"),
            after_quantity=Decimal("12.0"),
            change_quantity=Decimal("2.0"),
            change_type=InventoryChangeType.ADJUST,
            change_reason="库存盘点调整",
            operator_id=self.regular_user.id
        )
        self.db.add(change_record)
        self.db.commit()
        self.db.refresh(change_record)
        
        # 创建库存变更通知
        notification = service.create_inventory_change_notification(
            change_record=change_record,
            operator_id=self.regular_user.id
        )
        
        assert notification is not None
        assert notification.title == "库存调整通知 - 测试物品"
        assert "库存调整" in notification.content
        assert notification.notification_type == NotificationType.INVENTORY_ALERT
        assert notification.user_id == self.admin_user.id
        assert notification.status == "unread"
    
    def test_create_inventory_change_notification_skip_operator(self):
        """测试跳过操作员自己的通知"""
        service = NotificationIntegrationService(self.db)
        
        # 创建库存变更记录（操作员是管理员）
        change_record = InventoryChangeRecord(
            department_id=self.department.id,
            item_id=self.item.id,
            before_quantity=Decimal("10.0"),
            after_quantity=Decimal("15.0"),
            change_quantity=Decimal("5.0"),
            change_type=InventoryChangeType.MANUAL_IN,
            change_reason="手工入库",
            operator_id=self.admin_user.id
        )
        self.db.add(change_record)
        self.db.commit()
        self.db.refresh(change_record)
        
        # 创建库存变更通知
        notification = service.create_inventory_change_notification(
            change_record=change_record,
            operator_id=self.admin_user.id
        )
        
        # 由于操作员是管理员，且是唯一的管理员，所以不会创建通知
        assert notification is None
    
    def test_create_approval_notification_purchase_request(self):
        """测试创建采购申请提交通知"""
        service = NotificationIntegrationService(self.db)
        
        # 创建采购申请提交通知
        notification = service.create_approval_notification(
            user_id=self.regular_user.id,
            approval_type="purchase_request",
            item_name="测试物品",
            request_id=123,
            status="已提交"
        )
        
        assert notification is not None
        assert notification.title == "采购申请提交 - 测试物品"
        assert "采购申请已提交" in notification.content
        assert notification.notification_type == NotificationType.APPROVAL_FLOW
        assert notification.user_id == self.regular_user.id
        assert notification.status == "unread"
    
    def test_create_approval_notification_approval_status(self):
        """测试创建审批状态更新通知"""
        service = NotificationIntegrationService(self.db)
        
        # 创建审批状态更新通知
        notification = service.create_approval_notification(
            user_id=self.regular_user.id,
            approval_type="approval_status",
            item_name="测试物品",
            request_id=123,
            status="under_review"
        )
        
        assert notification is not None
        assert notification.title == "审批状态更新 - 测试物品"
        assert "审批状态已更新" in notification.content
        assert notification.notification_type == NotificationType.APPROVAL_FLOW
        assert notification.user_id == self.regular_user.id
        assert notification.status == "unread"
    
    def test_create_approval_notification_approval_complete(self):
        """测试创建审批完成通知"""
        service = NotificationIntegrationService(self.db)
        
        # 创建审批完成通知
        notification = service.create_approval_notification(
            user_id=self.regular_user.id,
            approval_type="approval_complete",
            item_name="测试物品",
            request_id=123,
            status="审批通过"
        )
        
        assert notification is not None
        assert notification.title == "审批完成 - 测试物品"
        assert "审批已完成" in notification.content
        assert notification.notification_type == NotificationType.APPROVAL_FLOW
        assert notification.user_id == self.regular_user.id
        assert notification.status == "unread"
    
    def test_create_approval_notification_unknown_type(self):
        """测试创建未知审批类型的通知"""
        service = NotificationIntegrationService(self.db)
        
        # 创建未知审批类型的通知
        notification = service.create_approval_notification(
            user_id=self.regular_user.id,
            approval_type="unknown_type",
            item_name="测试物品",
            request_id=123,
            status="未知状态"
        )
        
        assert notification is None
    
    def test_check_and_create_inventory_alerts_low_stock(self):
        """测试检查并创建低库存预警"""
        service = NotificationIntegrationService(self.db)
        
        # 检查低库存预警
        notifications = service.check_and_create_inventory_alerts(
            department_id=self.department.id,
            item_id=self.item.id,
            current_quantity=Decimal("3.0")
        )
        
        assert len(notifications) == 1
        notification = notifications[0]
        assert notification.title == "库存预警 - 测试物品"
        assert "库存不足" in notification.content
    
    def test_check_and_create_inventory_alerts_out_of_stock(self):
        """测试检查并创建缺货预警"""
        service = NotificationIntegrationService(self.db)
        
        # 检查缺货预警
        notifications = service.check_and_create_inventory_alerts(
            department_id=self.department.id,
            item_id=self.item.id,
            current_quantity=Decimal("0.0")
        )
        
        assert len(notifications) == 1
        notification = notifications[0]
        assert notification.title == "缺货通知 - 测试物品"
        assert "已缺货" in notification.content
    
    def test_check_and_create_inventory_alerts_overstock(self):
        """测试检查并创建超储预警"""
        service = NotificationIntegrationService(self.db)
        
        # 检查超储预警
        notifications = service.check_and_create_inventory_alerts(
            department_id=self.department.id,
            item_id=self.item.id,
            current_quantity=Decimal("25.0")
        )
        
        assert len(notifications) == 1
        notification = notifications[0]
        assert notification.title == "超储提醒 - 测试物品"
        assert "库存超储" in notification.content
    
    def test_check_and_create_inventory_alerts_normal_stock(self):
        """测试正常库存时不创建预警"""
        service = NotificationIntegrationService(self.db)
        
        # 检查正常库存
        notifications = service.check_and_create_inventory_alerts(
            department_id=self.department.id,
            item_id=self.item.id,
            current_quantity=Decimal("10.0")
        )
        
        assert len(notifications) == 0
    
    def test_check_and_create_inventory_alerts_no_inventory_record(self):
        """测试没有库存记录时不创建预警"""
        service = NotificationIntegrationService(self.db)
        
        # 删除库存记录
        self.db.delete(self.inventory)
        self.db.commit()
        
        # 检查预警
        notifications = service.check_and_create_inventory_alerts(
            department_id=self.department.id,
            item_id=self.item.id,
            current_quantity=Decimal("3.0")
        )
        
        assert len(notifications) == 0
    
    @patch('app.services.********************************.logger')
    def test_error_handling_in_notification_creation(self, mock_logger):
        """测试通知创建失败时的错误处理"""
        service = NotificationIntegrationService(self.db)
        
        # 模拟通知服务失败
        with patch.object(service.notification_service, 'create_notification', side_effect=Exception("测试错误")):
            # 尝试创建通知
            notification = service.create_inventory_alert_notification(
                department_id=self.department.id,
                item_id=self.item.id,
                alert_type="low_stock",
                current_quantity=Decimal("3.0"),
                threshold_value=Decimal("5.0")
            )
            
            # 应该返回None，不影响主流程
            assert notification is None
            # 应该记录警告日志
            mock_logger.error.assert_called_once()
    
    def test_notification_priority_setting(self):
        """测试通知优先级设置"""
        service = NotificationIntegrationService(self.db)
        
        # 创建高优先级通知（缺货）
        notification = service.create_inventory_alert_notification(
            department_id=self.department.id,
            item_id=self.item.id,
            alert_type="out_of_stock",
            current_quantity=Decimal("0.0")
        )
        
        # 检查通知数据中是否包含优先级信息
        # 注意：这里我们检查的是通知内容，因为Notification模型中没有priority字段
        assert notification is not None
        assert "请立即补充库存" in notification.content  # 缺货通知应该包含紧急提示
    
    def test_multiple_admin_users_notifications(self):
        """测试为多个管理员用户创建通知"""
        # 创建另一个管理员用户
        admin_user2 = self.create_test_user(
            username="admin2",
            full_name="部门管理员2",
            department_id=self.department.id,
            is_superuser=True
        )
        
        service = NotificationIntegrationService(self.db)
        
        # 创建库存预警通知
        notification = service.create_inventory_alert_notification(
            department_id=self.department.id,
            item_id=self.item.id,
            alert_type="low_stock",
            current_quantity=Decimal("3.0"),
            threshold_value=Decimal("5.0")
        )
        
        # 应该为第一个管理员创建通知
        assert notification is not None
        assert notification.user_id in [self.admin_user.id, admin_user2.id]
        
        # 检查是否创建了多个通知
        all_notifications = self.db.query(Notification).filter(
            Notification.related_module == "inventory",
            Notification.related_id == self.item.id
        ).all()
        
        # 应该为每个管理员创建通知
        assert len(all_notifications) >= 1
