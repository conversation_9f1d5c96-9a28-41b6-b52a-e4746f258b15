"""
采购申请API测试
"""

import pytest
from tests.base import IntegrationTestCase
from app.models.purchase import PurchaseRequest, PurchaseRequestItem
from app.models.user import User, Department
from app.models.item import Item, ItemCategory, ItemPrimaryCategory


class TestPurchaseRequestAPI(IntegrationTestCase):
    """采购申请API测试"""

    def setup_test_data(self):
        """设置测试数据"""
        super().setup_test_data()
        
        # 创建物品分类
        self.primary_category = self.create_test_primary_category()
        self.category = self.create_test_category(self.primary_category.id)
        
        # 创建测试物品
        self.test_item = self.create_test_item(self.category.id)

    def create_test_primary_category(self):
        """创建测试一级分类"""
        primary_category = ItemPrimaryCategory(
            name="测试一级分类",
            description="用于测试的一级分类",
            code_prefix="TEST",
            code_format="0000",
            current_sequence=1
        )
        self.db.add(primary_category)
        self.db.commit()
        self.db.refresh(primary_category)
        return primary_category

    def create_test_category(self, primary_category_id: int):
        """创建测试二级分类"""
        category = ItemCategory(
            name="测试二级分类",
            description="用于测试的二级分类",
            primary_category_id=primary_category_id
        )
        self.db.add(category)
        self.db.commit()
        self.db.refresh(category)
        return category

    def create_test_item(self, category_id: int):
        """创建测试物品"""
        item = Item(
            code="TEST_ITEM",
            name="测试物品",
            category_id=category_id,
            description="用于测试的物品",
            purchase_unit="piece"
        )
        self.db.add(item)
        self.db.commit()
        self.db.refresh(item)
        return item

    def test_submit_purchase_request(self):
        """测试提交采购申请"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 1. 创建测试部门
        dept_data = {
            "name": "测试部门",
            "code": "TEST_DEPT",
            "description": "用于测试的部门"
        }
        
        dept_response = self.client.post("/api/admin/departments/", json=dept_data, headers=headers)
        assert dept_response.status_code == 201
        dept_id = dept_response.json()["id"]
        
        # 2. 创建测试用户
        user_data = {
            "username": "test_user",
            "email": "<EMAIL>",
            "password": "test123",
            "full_name": "测试用户",
            "department_id": dept_id,
            "role_id": self.roles[0].id,
            "is_superuser": False,
            "is_active": True
        }
        
        user_response = self.client.post("/api/admin/users/", json=user_data, headers=headers)
        assert user_response.status_code == 201
        user_id = user_response.json()["id"]
        
        # 3. 创建采购申请
        request_data = {
            "department_id": dept_id,
            "submitter_id": user_id,
            "notes": "测试采购申请",
            "items": [
                {
                    "item_id": self.test_item.id,
                    "quantity": 100,
                    "unit_price": 10.50,
                    "notes": "测试物品采购"
                }
            ]
        }
        
        create_response = self.client.post("/api/admin/purchase-requests/", json=request_data, headers=headers)
        assert create_response.status_code == 201
        request_id = create_response.json()["id"]
        
        # 4. 提交采购申请
        submit_response = self.client.post(f"/api/admin/purchase-requests/{request_id}/submit", headers=headers)
        assert submit_response.status_code == 200
        
        # 验证状态已更新为 under_review
        submit_data = submit_response.json()
        assert submit_data["status"] == "under_review"
        
        # 5. 验证流转历史已创建
        get_response = self.client.get(f"/api/admin/purchase-requests/{request_id}", headers=headers)
        assert get_response.status_code == 200
        request_data = get_response.json()
        assert len(request_data["flow_history"]) > 0
        
        # 验证最新的流转记录
        latest_flow = request_data["flow_history"][-1]
        assert latest_flow["action"] == "submit"
        assert latest_flow["to_status"] == "under_review"
        assert latest_flow["comments"] == "申请人提交申请"

    def test_submit_nonexistent_request(self):
        """测试提交不存在的采购申请"""
        headers = self.get_auth_headers("admin", "admin123")
        
        submit_response = self.client.post("/api/admin/purchase-requests/99999/submit", headers=headers)
        assert submit_response.status_code == 404
