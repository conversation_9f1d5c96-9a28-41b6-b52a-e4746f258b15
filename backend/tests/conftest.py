"""
pytest配置文件
"""

import pytest
import os
import sys
import warnings
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# 过滤passlib的crypt弃用警告
warnings.filterwarnings("ignore", message=".*'crypt' is deprecated.*", category=DeprecationWarning, module="passlib.*")

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 设置测试环境变量
os.environ["TESTING"] = "true"
os.environ["DATABASE_URL"] = "sqlite:///:memory:"

from app.core.database import get_db, Base
from app.main import app


@pytest.fixture(scope="session")
def test_settings():
    """测试设置"""
    return {
        "database_url": "sqlite:///:memory:",
        "secret_key": "test-secret-key",
        "algorithm": "HS256",
        "access_token_expire_minutes": 30,
    }


# 创建内存数据库
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """覆盖数据库依赖"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="function")
def db_session():
    """数据库会话fixture"""
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session):
    """测试客户端fixture"""
    from fastapi.testclient import TestClient
    return TestClient(app)


# 测试标记
pytest_plugins = []


def pytest_configure(config):
    """pytest配置"""
    # 注册自定义标记
    config.addinivalue_line(
        "markers", "integration: 标记为集成测试"
    )
    config.addinivalue_line(
        "markers", "auth: 标记为认证相关测试"
    )
    config.addinivalue_line(
        "markers", "user: 标记为用户管理测试"
    )
    config.addinivalue_line(
        "markers", "department: 标记为部门管理测试"
    )
    config.addinivalue_line(
        "markers", "permission: 标记为权限管理测试"
    )
    config.addinivalue_line(
        "markers", "slow: 标记为慢速测试"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试项"""
    for item in items:
        # 为测试类添加标记
        if "TestAuthentication" in str(item.cls):
            item.add_marker(pytest.mark.auth)
        elif "TestUserManagement" in str(item.cls):
            item.add_marker(pytest.mark.user)
        elif "TestDepartmentManagement" in str(item.cls):
            item.add_marker(pytest.mark.department)
        elif "TestPermissionManagement" in str(item.cls):
            item.add_marker(pytest.mark.permission)
        elif "TestIntegration" in str(item.cls):
            item.add_marker(pytest.mark.integration)
        
        # 所有测试都是集成测试
        item.add_marker(pytest.mark.integration) 