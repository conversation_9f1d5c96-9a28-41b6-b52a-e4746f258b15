import pytest
from datetime import date, datetime
from decimal import Decimal
from sqlalchemy.orm import Session
from app.models.purchase import PurchaseRequest, PurchaseRequestItem
from app.models.supplier import ItemSupplier, SupplierPrice
from app.models.item import Item
from app.models.exchange_rate import ExchangeRate
from app.services.purchase_request_service import PurchaseRequestService
from app.services.exchange_rate_service import ExchangeRateService
from app.core.exceptions import BusinessException


class TestPurchaseRequestExchangeRate:
    """测试采购申请汇率检查功能"""

    def test_check_exchange_rate_validity_with_valid_rate(self, db_session: Session):
        """测试有有效汇率时的检查"""
        # 创建测试数据
        item = Item(
            name="测试物品",
            code="TEST001",
            category_id=1,
            purchase_unit="个",
            inventory_unit="个",
            qty_per_up=1
        )
        db_session.add(item)
        db_session.flush()

        item_supplier = ItemSupplier(
            item_id=item.id,
            supplier_id=1,
            priority=0,
            status="active"
        )
        db_session.add(item_supplier)
        db_session.flush()

        # 创建有效汇率（使用当前月份）
        current_month = date.today().replace(day=1)
        exchange_rate = ExchangeRate(
            currency_code="CNY",
            rate=Decimal("7.2"),
            effective_month=current_month,
            status="active",
            created_by=1
        )
        db_session.add(exchange_rate)
        db_session.flush()

        # 创建供应商价格
        supplier_price = SupplierPrice(
            item_supplier_id=item_supplier.id,
            unit_price=Decimal("10.00"),
            currency_code="CNY",
            min_quantity=1,
            valid_from=datetime.now(),
            created_by=1
        )
        db_session.add(supplier_price)
        db_session.flush()

        # 测试汇率有效性检查
        exchange_rate_service = ExchangeRateService(db_session)
        validity = exchange_rate_service.check_exchange_rate_validity("CNY")
        
        assert validity["is_valid"] is True
        assert validity["has_current_month_rate"] is True
        assert validity["current_month_rate"] == Decimal("7.2")

    def test_check_exchange_rate_validity_without_current_month_rate(self, db_session: Session):
        """测试当前月无汇率但使用历史汇率的情况"""
        # 创建测试数据
        item = Item(
            name="测试物品",
            code="TEST002",
            category_id=1,
            purchase_unit="个",
            inventory_unit="个",
            qty_per_up=1
        )
        db_session.add(item)
        db_session.flush()

        item_supplier = ItemSupplier(
            item_id=item.id,
            supplier_id=1,
            priority=0,
            status="active"
        )
        db_session.add(item_supplier)
        db_session.flush()

        # 创建历史汇率（非当前月）
        current_month = date.today().replace(day=1)
        if current_month.month == 1:
            previous_month = current_month.replace(year=current_month.year-1, month=12)
        else:
            previous_month = current_month.replace(month=current_month.month-1)
        
        exchange_rate = ExchangeRate(
            currency_code="EUR",
            rate=Decimal("0.85"),
            effective_month=previous_month,
            status="active",
            created_by=1
        )
        db_session.add(exchange_rate)
        db_session.flush()

        # 创建供应商价格
        supplier_price = SupplierPrice(
            item_supplier_id=item_supplier.id,
            unit_price=Decimal("15.00"),
            currency_code="EUR",
            min_quantity=1,
            valid_from=datetime.now(),
            created_by=1
        )
        db_session.add(supplier_price)
        db_session.flush()

        # 测试汇率有效性检查
        exchange_rate_service = ExchangeRateService(db_session)
        validity = exchange_rate_service.check_exchange_rate_validity("EUR")
        
        assert validity["is_valid"] is True
        assert validity["has_current_month_rate"] is False
        assert validity["fallback_rate"] == Decimal("0.85")

    def test_check_exchange_rate_validity_without_any_rate(self, db_session: Session):
        """测试完全没有汇率的情况"""
        # 创建测试数据
        item = Item(
            name="测试物品",
            code="TEST003",
            category_id=1,
            purchase_unit="个",
            inventory_unit="个",
            qty_per_up=1
        )
        db_session.add(item)
        db_session.flush()

        item_supplier = ItemSupplier(
            item_id=item.id,
            supplier_id=1,
            priority=0,
            status="active"
        )
        db_session.add(item_supplier)
        db_session.flush()

        # 创建供应商价格（JPY，但没有汇率）
        supplier_price = SupplierPrice(
            item_supplier_id=item_supplier.id,
            unit_price=Decimal("1000.00"),
            currency_code="JPY",
            min_quantity=1,
            valid_from=datetime.now(),
            created_by=1
        )
        db_session.add(supplier_price)
        db_session.flush()

        # 测试汇率有效性检查
        exchange_rate_service = ExchangeRateService(db_session)
        validity = exchange_rate_service.check_exchange_rate_validity("JPY")
        
        assert validity["is_valid"] is False
        assert validity["has_current_month_rate"] is False
        assert validity["fallback_rate"] is None

    def test_validate_purchase_request_exchange_rates(self, db_session: Session):
        """测试采购申请汇率验证"""
        # 创建测试数据
        item1 = Item(
            name="测试物品1",
            code="TEST001",
            category_id=1,
            purchase_unit="个",
            inventory_unit="个",
            qty_per_up=1
        )
        item2 = Item(
            name="测试物品2",
            code="TEST002",
            category_id=1,
            purchase_unit="个",
            inventory_unit="个",
            qty_per_up=1
        )
        db_session.add_all([item1, item2])
        db_session.flush()

        # 创建供应商关系
        item_supplier1 = ItemSupplier(
            item_id=item1.id,
            supplier_id=1,
            priority=0,
            status="active"
        )
        item_supplier2 = ItemSupplier(
            item_id=item2.id,
            supplier_id=1,
            priority=0,
            status="active"
        )
        db_session.add_all([item_supplier1, item_supplier2])
        db_session.flush()

        # 创建汇率（使用当前月份）
        current_month = date.today().replace(day=1)
        cny_rate = ExchangeRate(
            currency_code="CNY",
            rate=Decimal("7.2"),
            effective_month=current_month,
            status="active",
            created_by=1
        )
        db_session.add(cny_rate)
        db_session.flush()

        # 创建供应商价格
        price1 = SupplierPrice(
            item_supplier_id=item_supplier1.id,
            unit_price=Decimal("10.00"),
            currency_code="CNY",
            min_quantity=1,
            valid_from=datetime.now(),
            created_by=1
        )
        price2 = SupplierPrice(
            item_supplier_id=item_supplier2.id,
            unit_price=Decimal("5.00"),
            currency_code="USD",
            min_quantity=1,
            valid_from=datetime.now(),
            created_by=1
        )
        db_session.add_all([price1, price2])
        db_session.flush()

        # 测试汇率验证
        exchange_rate_service = ExchangeRateService(db_session)
        validation_results = exchange_rate_service.validate_purchase_request_exchange_rates([
            {"currency_code": "CNY"},
            {"currency_code": "USD"}
        ])
        
        assert validation_results["overall_valid"] is True
        assert len(validation_results["items_validation"]) == 1  # 只有CNY需要验证
        assert validation_results["items_validation"][0]["currency_code"] == "CNY"
        assert validation_results["items_validation"][0]["is_valid"] is True

    def test_purchase_request_exchange_rate_check_in_service(self, db_session: Session):
        """测试采购申请服务中的汇率检查"""
        # 创建测试数据
        item = Item(
            name="测试物品",
            code="TEST001",
            category_id=1,
            purchase_unit="个",
            inventory_unit="个",
            qty_per_up=1
        )
        db_session.add(item)
        db_session.flush()

        item_supplier = ItemSupplier(
            item_id=item.id,
            supplier_id=1,
            priority=0,
            status="active"
        )
        db_session.add(item_supplier)
        db_session.flush()

        # 创建供应商价格（JPY，没有汇率）
        supplier_price = SupplierPrice(
            item_supplier_id=item_supplier.id,
            unit_price=Decimal("1000.00"),
            currency_code="JPY",
            min_quantity=1,
            valid_from=datetime.now(),
            created_by=1
        )
        db_session.add(supplier_price)
        db_session.flush()

        # 创建采购申请
        request = PurchaseRequest(
            request_no="PR202401010001",
            department_id=1,
            submitter_id=1,
            status="pending_submission"
        )
        db_session.add(request)
        db_session.flush()

        # 创建申请明细
        request_item = PurchaseRequestItem(
            request_id=request.id,
            item_id=item.id,
            item_code=item.code,
            item_name=item.name,
            spq_quantity=Decimal("1"),
            spq_count=1,
            spq_unit="个"
        )
        db_session.add(request_item)
        db_session.flush()

        # 测试汇率检查（应该抛出异常）
        service = PurchaseRequestService(db_session)
        with pytest.raises(BusinessException) as exc_info:
            service._check_exchange_rate_validity(request.id)
        
        assert "JPY" in str(exc_info.value)
        assert "没有有效汇率" in str(exc_info.value)

    def test_purchase_request_exchange_rate_check_with_valid_rates(self, db_session: Session):
        """测试有有效汇率时的采购申请汇率检查"""
        # 创建测试数据
        item = Item(
            name="测试物品",
            code="TEST001",
            category_id=1,
            purchase_unit="个",
            inventory_unit="个",
            qty_per_up=1
        )
        db_session.add(item)
        db_session.flush()

        item_supplier = ItemSupplier(
            item_id=item.id,
            supplier_id=1,
            priority=0,
            status="active"
        )
        db_session.add(item_supplier)
        db_session.flush()

        # 创建有效汇率（使用当前月份）
        current_month = date.today().replace(day=1)
        exchange_rate = ExchangeRate(
            currency_code="CNY",
            rate=Decimal("7.2"),
            effective_month=current_month,
            status="active",
            created_by=1
        )
        db_session.add(exchange_rate)
        db_session.flush()

        # 创建供应商价格
        supplier_price = SupplierPrice(
            item_supplier_id=item_supplier.id,
            unit_price=Decimal("10.00"),
            currency_code="CNY",
            min_quantity=1,
            valid_from=datetime.now(),
            created_by=1
        )
        db_session.add(supplier_price)
        db_session.flush()

        # 创建采购申请
        request = PurchaseRequest(
            request_no="PR202401010002",
            department_id=1,
            submitter_id=1,
            status="pending_submission"
        )
        db_session.add(request)
        db_session.flush()

        # 创建申请明细
        request_item = PurchaseRequestItem(
            request_id=request.id,
            item_id=item.id,
            item_code=item.code,
            item_name=item.name,
            spq_quantity=Decimal("1"),
            spq_count=1,
            spq_unit="个"
        )
        db_session.add(request_item)
        db_session.flush()

        # 测试汇率检查（应该通过）
        service = PurchaseRequestService(db_session)
        service._check_exchange_rate_validity(request.id)  # 不应该抛出异常
