"""
图片上传功能集成测试
"""

import pytest
import io
from PIL import Image
from fastapi.testclient import TestClient
from app.main import app
from app.services.file_upload_service import file_upload_service
from tests.base import AuthTestCase

client = TestClient(app)

def create_test_image(width=800, height=600, format='JPEG'):
    """创建测试图片"""
    img = Image.new('RGB', (width, height), color='red')
    img_bytes = io.BytesIO()
    img.save(img_bytes, format=format)
    img_bytes.seek(0)
    return img_bytes

class TestImageUpload(AuthTestCase):
    """图片上传功能测试"""
    
    def test_upload_valid_image(self):
        """测试上传有效图片"""
        # 创建测试图片
        test_image = create_test_image(800, 600, 'JPEG')
        
        # 获取认证头
        headers = self.get_auth_headers("admin", "admin123")
        
        # 上传图片
        files = {"file": ("test-image.jpg", test_image.getvalue(), "image/jpeg")}
        data = {"sub_dir": "items"}
        
        response = self.client.post("/api/admin/upload/images", files=files, data=data, headers=headers)
        
        # 验证响应
        assert response.status_code == 200
        result = response.json()
        assert result["success"] == True
        assert "data" in result
        assert "url" in result["data"]
        
        # 保存上传的文件信息用于后续测试
        self.uploaded_file_info = result["data"]
    
    def test_upload_large_file(self):
        """测试上传大文件"""
        # 创建超过500KB的文件
        large_data = b'x' * (600 * 1024)  # 600KB
        
        headers = self.get_auth_headers("admin", "admin123")
        files = {"file": ("large-image.jpg", large_data, "image/jpeg")}
        data = {"sub_dir": "items"}
        
        response = self.client.post("/api/admin/upload/images", files=files, data=data, headers=headers)
        
        # 应该返回400错误
        assert response.status_code == 400
        error_detail = response.json()["detail"]
        assert "文件大小超过限制" in error_detail
    
    def test_upload_invalid_format(self):
        """测试上传无效格式"""
        # 创建文本文件
        text_data = b"This is not an image"
        
        headers = self.get_auth_headers("admin", "admin123")
        files = {"file": ("test.txt", text_data, "text/plain")}
        data = {"sub_dir": "items"}
        
        response = self.client.post("/api/admin/upload/images", files=files, data=data, headers=headers)
        
        # 应该返回400错误
        assert response.status_code == 400
        error_detail = response.json()["detail"]
        assert "不支持的图片格式" in error_detail

class TestImageRetrieval(AuthTestCase):
    """图片获取功能测试"""
    
    def test_get_image_success(self):
        """测试成功获取图片"""
        # 先上传一个图片
        test_image = create_test_image(100, 100, 'JPEG')
        
        headers = self.get_auth_headers("admin", "admin123")
        files = {"file": ("test-image.jpg", test_image.getvalue(), "image/jpeg")}
        data = {"sub_dir": "items"}
        
        # 上传图片
        upload_response = self.client.post("/api/admin/upload/images", files=files, data=data, headers=headers)
        assert upload_response.status_code == 200
        
        upload_result = upload_response.json()
        filename = upload_result["data"]["url"].split("/")[-1]
        
        # 测试获取图片
        response = self.client.get(f"/api/admin/upload/images/items/{filename}")
        
        # 验证响应
        assert response.status_code == 200
        # 验证响应头
        assert response.headers["content-type"] == "image/jpeg"
        assert len(response.content) > 0
    
    def test_get_image_not_found(self):
        """测试获取不存在的图片"""
        # 测试获取不存在的图片
        response = self.client.get("/api/admin/upload/images/items/nonexistent-image.jpg")
        
        # 应该返回404错误
        assert response.status_code == 404
        error_detail = response.json()["detail"]
        assert "图片不存在" in error_detail
    

    
    def test_get_image_info_success(self):
        """测试成功获取图片信息"""
        # 先上传一个图片
        test_image = create_test_image(100, 100, 'JPEG')
        
        headers = self.get_auth_headers("admin", "admin123")
        files = {"file": ("test-image.jpg", test_image.getvalue(), "image/jpeg")}
        data = {"sub_dir": "items"}
        
        # 上传图片
        upload_response = self.client.post("/api/admin/upload/images", files=files, data=data, headers=headers)
        assert upload_response.status_code == 200
        
        upload_result = upload_response.json()
        filename = upload_result["data"]["url"].split("/")[-1]
        
        # 测试获取图片信息
        response = self.client.get(f"/api/admin/upload/images/info/items/{filename}", headers=headers)
        
        # 验证响应
        assert response.status_code == 200
        result = response.json()
        assert result["success"] == True
        assert "data" in result
        # 验证图片信息字段
        image_info = result["data"]
        assert "width" in image_info
        assert "height" in image_info
        assert "format" in image_info
        assert "size" in image_info
    
    def test_get_image_info_not_found(self):
        """测试获取不存在的图片信息"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 测试获取不存在的图片信息
        response = self.client.get("/api/admin/upload/images/info/items/nonexistent-image.jpg", headers=headers)
        
        # 应该返回404错误
        assert response.status_code == 404
        error_detail = response.json()["detail"]
        assert "图片不存在" in error_detail

class TestImageDeletion(AuthTestCase):
    """图片删除功能测试"""
    
    def test_delete_image_success(self):
        """测试成功删除图片"""
        # 先上传一个图片
        test_image = create_test_image(100, 100, 'JPEG')
        
        headers = self.get_auth_headers("admin", "admin123")
        files = {"file": ("test-image.jpg", test_image.getvalue(), "image/jpeg")}
        data = {"sub_dir": "items"}
        
        # 上传图片
        upload_response = self.client.post("/api/admin/upload/images", files=files, data=data, headers=headers)
        assert upload_response.status_code == 200
        
        upload_result = upload_response.json()
        filename = upload_result["data"]["url"].split("/")[-1]
        
        # 测试删除图片
        response = self.client.delete(f"/api/admin/upload/images/items/{filename}", headers=headers)
        
        # 验证响应
        assert response.status_code == 200
        result = response.json()
        assert result["success"] == True
        assert "图片删除成功" in result["message"]
    
    def test_delete_image_not_found(self):
        """测试删除不存在的图片"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 测试删除不存在的图片
        response = self.client.delete("/api/admin/upload/images/items/nonexistent-image.jpg", headers=headers)
        
        # 应该返回404错误
        assert response.status_code == 404
        error_detail = response.json()["detail"]
        assert "图片不存在" in error_detail

class TestFileUploadService:
    """文件上传服务测试"""
    
    def test_validate_image_file_valid(self):
        """测试验证有效图片文件"""
        # 创建模拟文件对象
        class MockFile:
            def __init__(self):
                self.filename = "test.jpg"
                self.size = 1000  # 1KB
        
        mock_file = MockFile()
        
        # 应该不抛出异常
        try:
            file_upload_service._validate_image_file(mock_file)
            assert True
        except Exception as e:
            assert False, f"不应该抛出异常: {e}"
    
    def test_validate_image_file_large(self):
        """测试验证大文件"""
        # 创建模拟大文件对象
        class MockLargeFile:
            def __init__(self):
                self.filename = "large.jpg"
                self.size = 600 * 1024  # 600KB
        
        mock_file = MockLargeFile()
        
        # 应该抛出异常
        with pytest.raises(Exception) as exc_info:
            file_upload_service._validate_image_file(mock_file)
        
        # 检查异常信息
        error = exc_info.value
        if hasattr(error, 'detail'):
            error_str = error.detail
        else:
            error_str = str(error)
        
        print(f"异常信息: {error_str}")
        assert "文件大小超过限制" in error_str
    
    def test_validate_image_file_invalid_format(self):
        """测试验证无效格式"""
        # 创建模拟无效格式文件对象
        class MockInvalidFile:
            def __init__(self):
                self.filename = "test.txt"
                self.size = 1000
        
        mock_file = MockInvalidFile()
        
        # 应该抛出异常
        with pytest.raises(Exception) as exc_info:
            file_upload_service._validate_image_file(mock_file)
        
        # 检查异常信息
        error = exc_info.value
        if hasattr(error, 'detail'):
            error_str = error.detail
        else:
            error_str = str(error)
        
        print(f"异常信息: {error_str}")
        assert "不支持的图片格式" in error_str

class TestImageUploadWithAuth(AuthTestCase):
    """带认证的图片上传测试"""
    
    def test_upload_with_valid_auth(self):
        """测试带有效认证的上传"""
        # 创建测试图片
        test_image = create_test_image(100, 100, 'JPEG')
        
        # 获取认证头
        headers = self.get_auth_headers("admin", "admin123")
        files = {"file": ("test-image.jpg", test_image.getvalue(), "image/jpeg")}
        data = {"sub_dir": "items"}
        
        response = self.client.post("/api/admin/upload/images", files=files, data=data, headers=headers)
        
        # 验证上传成功
        assert response.status_code == 200
        result = response.json()
        assert result["success"] == True
        assert "data" in result
        assert "url" in result["data"]
        
        # 获取上传的图片信息
        image_url = result["data"]["url"]
        filename = image_url.split("/")[-1]
        
        # 测试获取图片
        get_response = self.client.get(f"/api/admin/upload/images/items/{filename}")
        assert get_response.status_code == 200
        

        
        # 测试获取图片信息
        info_response = self.client.get(f"/api/admin/upload/images/info/items/{filename}", headers=headers)
        assert info_response.status_code == 200
        
        # 测试删除图片
        delete_response = self.client.delete(f"/api/admin/upload/images/items/{filename}", headers=headers)
        assert delete_response.status_code == 200
        
        print(f"✅ 完整的上传-获取-删除流程测试通过")
        print(f"   上传的图片: {filename}")
        print(f"   图片URL: {image_url}")

if __name__ == "__main__":
    pytest.main([__file__]) 