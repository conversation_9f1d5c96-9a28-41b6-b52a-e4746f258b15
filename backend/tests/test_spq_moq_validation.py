import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from decimal import Decimal

from app.main import app
from app.core.database import get_db
from app.models.supplier import Supplier, ItemSupplier, SupplierPrice
from app.models.item import Item
from app.models.user import User
from tests.base import BaseTestCase, UserManagementTestCase


class TestSPQMOQValidation(UserManagementTestCase):
    """测试SPQ和MOQ验证功能"""
    
    def create_test_data(self, client: TestClient):
        """创建测试数据：供应商、分类、物品"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试供应商
        supplier_data = {
            "name_cn": "测试供应商",
            "name_en": "Test Supplier",
            "code": "SUP001",
            "status": "active"
        }
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 先创建一级分类
        primary_category_data = {
            "name": "测试一级分类",
            "code_prefix": "TEST",
            "description": "测试用一级分类"
        }
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 创建二级分类
        category_data = {
            "name": "测试二级分类",
            "primary_category_id": primary_category_id,
            "description": "测试用二级分类"
        }
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        # 创建测试物品
        item_data = {
            "name": "测试物品",
            "code": "ITEM001",
            "unit": "个",
            "qty_per_up": 1,
            "is_purchasable": True,
            "is_active": True,
    
            "category_id": category_id
        }
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        return admin_headers, supplier_id, item_id
    
    def test_create_item_supplier_with_valid_spq_moq(self, client: TestClient):
        """测试创建物品供应商关联时使用有效的SPQ和MOQ"""
        admin_headers, supplier_id, item_id = self.create_test_data(client)
        
        # 创建物品供应商关联
        response = client.post(
            f"/api/admin/suppliers/{supplier_id}/items",
            json={
                "item_id": item_id,
                "spq": 100,
                "moq": 200,  # MOQ是SPQ的2倍
                "delivery_days": 7,
                "quality_rating": 3
            },
            headers=admin_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["spq"] == 100
        assert data["moq"] == 200
    
    def test_create_item_supplier_with_invalid_spq(self, client: TestClient):
        """测试创建物品供应商关联时使用无效的SPQ（非正整数）"""
        admin_headers, supplier_id, item_id = self.create_test_data(client)
        
        response = client.post(
            f"/api/admin/suppliers/{supplier_id}/items",
            json={
                "item_id": item_id,
                "spq": 0,  # 无效：必须大于0
                "moq": 100,
                "delivery_days": 7,
                "quality_rating": 3
            },
            headers=admin_headers
        )
        assert response.status_code == 422  # Pydantic验证错误
        assert "greater than 0" in response.json()["detail"][0]["msg"]
    
    def test_create_item_supplier_with_invalid_moq(self, client: TestClient):
        """测试创建物品供应商关联时使用无效的MOQ（非正整数）"""
        admin_headers, supplier_id, item_id = self.create_test_data(client)
        
        response = client.post(
            f"/api/admin/suppliers/{supplier_id}/items",
            json={
                "item_id": item_id,
                "spq": 100,
                "moq": -1,  # 无效：必须大于0
                "delivery_days": 7,
                "quality_rating": 3
            },
            headers=admin_headers
        )
        assert response.status_code == 422  # Pydantic验证错误
        assert "greater than 0" in response.json()["detail"][0]["msg"]
    
    def test_create_item_supplier_with_moq_not_spq_multiple(self, client: TestClient):
        """测试创建物品供应商关联时MOQ不是SPQ的整数倍"""
        admin_headers, supplier_id, item_id = self.create_test_data(client)
        
        response = client.post(
            f"/api/admin/suppliers/{supplier_id}/items",
            json={
                "item_id": item_id,
                "spq": 100,
                "moq": 150,  # 无效：不是SPQ的整数倍
                "delivery_days": 7,
                "quality_rating": 3
            },
            headers=admin_headers
        )
        assert response.status_code == 422  # Pydantic验证错误
        assert "MOQ必须是SPQ的整数倍" in response.json()["detail"][0]["msg"]
    
    def test_update_item_supplier_with_valid_spq_moq(self, client: TestClient):
        """测试更新物品供应商关联时使用有效的SPQ和MOQ"""
        admin_headers, supplier_id, item_id = self.create_test_data(client)
        
        # 创建物品供应商关联
        create_response = client.post(
            f"/api/admin/suppliers/{supplier_id}/items",
            json={
                "item_id": item_id,
                "spq": 100,
                "moq": 100,
                "delivery_days": 7,
                "quality_rating": 3
            },
            headers=admin_headers
        )
        item_supplier_id = create_response.json()["id"]
        
        # 更新SPQ和MOQ
        response = client.put(
            f"/api/admin/item-suppliers/{item_supplier_id}",
            json={
                "spq": 50,
                "moq": 100  # MOQ是SPQ的2倍
            },
            headers=admin_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["spq"] == 50
        assert data["moq"] == 100
    
    def test_update_item_supplier_with_invalid_spq(self, client: TestClient):
        """测试更新物品供应商关联时使用无效的SPQ"""
        admin_headers, supplier_id, item_id = self.create_test_data(client)
        
        # 创建物品供应商关联
        create_response = client.post(
            f"/api/admin/suppliers/{supplier_id}/items",
            json={
                "item_id": item_id,
                "spq": 100,
                "moq": 100,
                "delivery_days": 7,
                "quality_rating": 3
            },
            headers=admin_headers
        )
        item_supplier_id = create_response.json()["id"]
        
        # 更新为无效的SPQ
        response = client.put(
            f"/api/admin/item-suppliers/{item_supplier_id}",
            json={
                "spq": 0  # 无效：必须大于0
            },
            headers=admin_headers
        )
        assert response.status_code == 422  # Pydantic验证错误
        assert "greater than 0" in response.json()["detail"][0]["msg"]
    
    def test_update_item_supplier_with_moq_not_spq_multiple(self, client: TestClient):
        """测试更新物品供应商关联时MOQ不是SPQ的整数倍"""
        admin_headers, supplier_id, item_id = self.create_test_data(client)
        
        # 创建物品供应商关联
        create_response = client.post(
            f"/api/admin/suppliers/{supplier_id}/items",
            json={
                "item_id": item_id,
                "spq": 100,
                "moq": 100,
                "delivery_days": 7,
                "quality_rating": 3
            },
            headers=admin_headers
        )
        item_supplier_id = create_response.json()["id"]
        
        # 更新为无效的MOQ
        response = client.put(
            f"/api/admin/item-suppliers/{item_supplier_id}",
            json={
                "spq": 50,
                "moq": 75  # 无效：不是SPQ的整数倍
            },
            headers=admin_headers
        )
        assert response.status_code == 422  # Pydantic验证错误
        assert "MOQ必须是SPQ的整数倍" in response.json()["detail"][0]["msg"]
    
    def test_frontend_validation_integration(self, client: TestClient):
        """测试前端验证与后端验证的集成"""
        admin_headers, supplier_id, item_id = self.create_test_data(client)
        
        # 测试小数输入（应该被前端阻止）
        response = client.post(
            f"/api/admin/suppliers/{supplier_id}/items",
            json={
                "item_id": item_id,
                "spq": 100.5,  # 小数，应该被拒绝
                "moq": 200,
                "delivery_days": 7,
                "quality_rating": 3
            },
            headers=admin_headers
        )
        # 由于Pydantic的验证，这应该被拒绝
        assert response.status_code == 422  # Validation error
        
        # 测试非数字字符串输入（应该被拒绝）
        response = client.post(
            f"/api/admin/suppliers/{supplier_id}/items",
            json={
                "item_id": item_id,
                "spq": "abc",  # 非数字字符串，应该被拒绝
                "moq": 200,
                "delivery_days": 7,
                "quality_rating": 3
            },
            headers=admin_headers
        )
        assert response.status_code == 422  # Validation error
    
    def test_valid_moq_values(self, client: TestClient):
        """测试有效的MOQ值（包括2）"""
        admin_headers, supplier_id, item_id = self.create_test_data(client)
        
        # 测试MOQ=2的情况
        response = client.post(
            f"/api/admin/suppliers/{supplier_id}/items",
            json={
                "item_id": item_id,
                "spq": 1,
                "moq": 2,  # 2是有效的，应该被接受
                "delivery_days": 7,
                "quality_rating": 3
            },
            headers=admin_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["spq"] == 1
        assert data["moq"] == 2
        
        # 创建第二个供应商来测试MOQ=2, SPQ=2的情况
        supplier_data_2 = {
            "name_cn": "测试供应商2",
            "name_en": "Test Supplier 2",
            "code": "SUP002",
            "status": "active"
        }
        supplier_response_2 = client.post("/api/admin/suppliers", json=supplier_data_2, headers=admin_headers)
        supplier_id_2 = supplier_response_2.json()["id"]
        
        # 测试MOQ=2, SPQ=2的情况
        response = client.post(
            f"/api/admin/suppliers/{supplier_id_2}/items",
            json={
                "item_id": item_id,
                "spq": 2,
                "moq": 2,  # 2是2的整数倍，应该被接受
                "delivery_days": 7,
                "quality_rating": 3
            },
            headers=admin_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["spq"] == 2
        assert data["moq"] == 2 