# 测试文档

## 测试结构

本项目使用重构后的测试结构，采用基类模式来管理不同的测试场景，并使用Faker生成测试数据。

### 文件结构

```
tests/
├── base.py                    # 测试基类定义
├── conftest.py               # pytest配置
├── test_auth.py              # 认证功能测试
├── test_user_management.py   # 用户管理测试
├── test_permissions.py       # 权限管理测试
├── test_department_management.py # 部门管理测试
├── test_integration.py       # 集成测试
└── README.md                 # 测试文档
```

### 测试基类

#### BaseTestCase
基础测试类，提供通用的测试功能：
- 自动设置测试环境
- 使用Faker生成测试数据
- 提供数据创建方法
- 提供认证和断言方法

#### AuthTestCase
认证测试基类，继承自BaseTestCase：
- 设置基础用户（管理员和普通用户）
- 用于认证相关的测试

#### UserManagementTestCase
用户管理测试基类，继承自BaseTestCase：
- 设置部门、角色和管理员用户
- 用于用户管理相关的测试

#### PermissionTestCase
权限测试基类，继承自BaseTestCase：
- 设置完整的权限、角色和用户体系
- 用于权限管理相关的测试

#### IntegrationTestCase
集成测试基类，继承自BaseTestCase：
- 设置完整的测试数据集
- 包含多个部门、角色、权限和用户
- 用于集成测试和复杂业务流程测试

### 使用Faker生成测试数据

项目使用Faker库生成真实的测试数据：

```python
from tests.base import AuthTestCase

class TestMyFeature(AuthTestCase):
    def test_something(self):
        # 使用faker生成数据
        user_data = {
            "username": self.fake.user_name(),
            "email": self.fake.email(),
            "full_name": self.fake.name(),
            "password": "Test123456"
        }
        
        # 或者使用基类提供的方法
        test_user = self.create_test_user(
            username="testuser",
            email="<EMAIL>",
            full_name="测试用户"
        )
```

### 运行测试

#### 运行所有测试
```bash
poetry run pytest
```

#### 运行特定类型的测试
```bash
# 运行认证测试
poetry run pytest -m auth

# 运行用户管理测试
poetry run pytest -m user

# 运行权限测试
poetry run pytest -m permission

# 运行部门管理测试
poetry run pytest -m department

# 运行集成测试
poetry run pytest -m integration
```

#### 运行特定测试文件
```bash
# 运行认证测试
poetry run pytest tests/test_auth.py

# 运行用户管理测试
poetry run pytest tests/test_user_management.py

# 运行权限测试
poetry run pytest tests/test_permissions.py

# 运行部门管理测试
poetry run pytest tests/test_department_management.py

# 运行集成测试
poetry run pytest tests/test_integration.py
```

#### 运行特定测试方法
```bash
# 运行特定的测试方法
poetry run pytest tests/test_auth.py::TestAuthentication::test_login_success
```

### 测试数据生成

#### 用户数据
```python
# 生成用户数据
user_data = self.create_user_data(
    username="custom_user",
    email="<EMAIL>",
    full_name="自定义用户"
)

# 创建测试用户
user = self.create_test_user(**user_data)
```

#### 部门数据
```python
# 生成部门数据
dept_data = self.create_department_data(
    name="技术部",
    code="TECH",
    description="技术研发部门"
)

# 创建测试部门
department = self.create_test_department(**dept_data)
```

#### 权限数据
```python
# 生成权限数据
perm_data = self.create_permission_data(
    code="user.create",
    name="创建用户",
    description="创建新用户的权限"
)

# 创建测试权限
permission = self.create_test_permission(**perm_data)
```

#### 角色数据
```python
# 生成角色数据
role_data = self.create_role_data(
    name="管理员",
    code="ADMIN",
    description="系统管理员"
)

# 创建测试角色
role = self.create_test_role(**role_data)
```

### 认证和权限测试

#### 获取认证头
```python
# 使用默认管理员账户
headers = self.get_auth_headers()

# 使用指定账户
headers = self.get_auth_headers("user", "user123")
```

#### 权限检查
```python
# 检查用户是否有权限访问特定端点
self.assert_permission_check(
    headers=headers,
    endpoint="/api/admin/users/",
    method="GET",
    expected_status=200
)
```

### 测试最佳实践

1. **使用合适的基类**：根据测试需求选择合适的基类
2. **使用Faker生成数据**：避免硬编码测试数据
3. **测试数据隔离**：每个测试方法都有独立的测试数据
4. **清晰的测试命名**：使用描述性的测试方法名
5. **适当的断言**：验证关键的业务逻辑
6. **错误场景测试**：测试异常情况和边界条件

### 测试标记

项目使用pytest标记来分类测试：

- `@pytest.mark.auth` - 认证相关测试
- `@pytest.mark.user` - 用户管理测试
- `@pytest.mark.department` - 部门管理测试
- `@pytest.mark.permission` - 权限管理测试
- `@pytest.mark.integration` - 集成测试
- `@pytest.mark.slow` - 慢速测试

### 性能测试

集成测试中包含性能测试，验证系统在大数据集下的表现：

```python
def test_performance_with_large_dataset(self):
    """测试大数据集性能"""
    # 创建大量测试数据
    for i in range(50):
        self.create_test_user(...)
    
    # 测试查询性能
    start_time = time.time()
    response = self.client.get("/api/admin/users/?page=1&size=100")
    end_time = time.time()
    
    assert end_time - start_time < 2.0  # 查询应该在2秒内完成
```

### 并发测试

测试系统在并发操作下的稳定性：

```python
def test_concurrent_operations(self):
    """测试并发操作"""
    import threading
    
    def create_user(user_id):
        # 创建用户的逻辑
        pass
    
    # 启动多个线程
    threads = []
    for i in range(10):
        thread = threading.Thread(target=lambda i=i: create_user(i))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
```

### 故障排除

#### 常见问题

1. **数据库连接问题**：确保测试使用内存数据库
2. **权限问题**：检查测试用户的权限设置
3. **数据冲突**：确保测试数据不重复
4. **异步问题**：注意并发测试的时序问题

#### 调试技巧

1. **使用pytest -s**：显示print输出
2. **使用pytest -v**：显示详细输出
3. **使用pytest --tb=short**：简化错误回溯
4. **使用pytest -x**：遇到第一个失败就停止

### 持续集成

测试配置支持持续集成环境：

```yaml
# .github/workflows/test.yml 示例
- name: Run tests
  run: |
    cd backend
    poetry install
    poetry run pytest --cov=app --cov-report=xml
```

### 测试覆盖率

生成测试覆盖率报告：

```bash
# 安装覆盖率工具
poetry add pytest-cov --group dev

# 运行测试并生成覆盖率报告
poetry run pytest --cov=app --cov-report=html
```

覆盖率报告将生成在 `htmlcov/` 目录中。 