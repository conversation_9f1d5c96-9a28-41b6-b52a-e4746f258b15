import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime
from decimal import Decimal

from app.models.purchase import (
    PurchaseRequest, PurchaseRequestItem, PurchaseExecutionBatch, 
    PurchaseExecutionItem, RequestFlowHistory
)
from app.models.user import User
from app.models.supplier import Supplier


class TestPurchaseExecution:
    """采购执行功能集成测试"""
    
    def setup_test_data(self, db_session: Session, test_user: User):
        """创建测试数据"""
        # 创建供应商
        supplier = Supplier(
            name="测试供应商",
            contact_person="张三",
            phone="13800138000",
            email="<EMAIL>",
            priority=1,
            status="active"
        )
        db_session.add(supplier)
        db_session.flush()
        
        # 创建已批准的采购申请
        request = PurchaseRequest(
            request_no="REQ202401001",
            department_id=test_user.department_id,  # 使用用户的部门ID
            submitter_id=test_user.id,
            status="approved",
            final_total=Decimal("500.00"),
            notes="测试采购申请",
            submitted_at=datetime.now()
        )
        db_session.add(request)
        db_session.flush()
        
        # 创建申请明细
        item = PurchaseRequestItem(
            request_id=request.id,
            item_id=test_user.id,  # 使用动态ID而不是硬编码的1
            item_code="TEST001",
            item_name="测试物品",
            spq_quantity=Decimal("1.0"),
            spq_count=10,
            spq_unit="个",
            final_unit_price=Decimal("50.00"),
            final_total_price=Decimal("500.00"),
            final_supplier_id=supplier.id,
            price_locked_at=datetime.now()
        )
        db_session.add(item)
        
        # 创建流转历史
        history = RequestFlowHistory(
            request_id=request.id,
            action="approve",
            from_status="under_final_approval",
            to_status="approved",
            operator_id=test_user.id,
            operator_name=test_user.username,
            approval_level="final_approval",
            comments="最终审批通过"
        )
        db_session.add(history)
        
        db_session.commit()
        return request, item, supplier
    
    def test_get_approved_requests(self, client: TestClient, db_session: Session, test_user: User, auth_headers):
        """测试获取已批准申请列表"""
        # 创建测试数据
        request, item, supplier = self.setup_test_data(db, test_user)
        
        # 调用API
        response = client.get(
            "/api/admin/purchase/execution/approved-requests",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "requests" in data
        assert len(data["requests"]) >= 1
        
        # 验证返回的申请数据
        found_request = None
        for req in data["requests"]:
            if req["id"] == request.id:
                found_request = req
                break
        
        assert found_request is not None
        assert found_request["request_no"] == "REQ202401001"
        assert found_request["status"] == "approved"
        assert len(found_request["items"]) == 1
    
    def test_preview_batch_execution(self, client: TestClient, db: Session, test_user: User, auth_headers):
        """测试预览批量执行"""
        # 创建测试数据
        request, item, supplier = self.setup_test_data(db, test_user)
        
        # 调用预览API
        response = client.post(
            "/api/admin/purchase/execution/preview",
            json={
                "request_ids": [request.id],
                "batch_name": "测试批次",
                "notes": "测试批量执行",
                "confirm": False
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证预览数据
        assert data["request_count"] == 1
        assert float(data["total_amount"]) == 500.00
        assert "estimated_batch_no" in data
        assert len(data["supplier_summary"]) >= 1
        assert len(data["warnings"]) == 0  # 没有警告
    
    def test_execute_batch(self, client: TestClient, db: Session, test_user: User, auth_headers):
        """测试批量执行"""
        # 创建测试数据
        request, item, supplier = self.setup_test_data(db, test_user)
        
        # 执行批量操作
        response = client.post(
            "/api/admin/purchase/execution/execute",
            json={
                "request_ids": [request.id],
                "batch_name": "测试执行批次",
                "notes": "集成测试批量执行",
                "confirm": True
            },
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        
        # 验证执行结果
        assert "id" in data
        assert data["batch_name"] == "测试执行批次"
        assert data["request_count"] == 1
        assert float(data["total_amount"]) == 500.00
        assert data["executor_name"] == test_user.username
        
        # 验证数据库状态
        db.refresh(request)
        assert request.status == "executed"
        
        # 验证执行批次记录
        execution_batch = db.query(PurchaseExecutionBatch).filter(
            PurchaseExecutionBatch.id == data["id"]
        ).first()
        assert execution_batch is not None
        assert execution_batch.batch_name == "测试执行批次"
        
        # 验证执行明细
        execution_items = db.query(PurchaseExecutionItem).filter(
            PurchaseExecutionItem.batch_id == execution_batch.id
        ).all()
        assert len(execution_items) == 1
        assert execution_items[0].item_name == "测试物品"
        assert execution_items[0].supplier_id == supplier.id
    
    def test_get_execution_batches(self, client: TestClient, db: Session, test_user: User, auth_headers):
        """测试获取执行批次列表"""
        # 先执行一个批次
        request, item, supplier = self.setup_test_data(db, test_user)
        
        execute_response = client.post(
            "/api/admin/purchase/execution/execute",
            json={
                "request_ids": [request.id],
                "batch_name": "测试批次列表",
                "notes": "测试",
                "confirm": True
            },
            headers=auth_headers
        )
        assert execute_response.status_code == 201
        
        # 获取批次列表
        response = client.get(
            "/api/admin/purchase/execution/batches",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "batches" in data
        assert len(data["batches"]) >= 1
        
        # 验证批次数据
        batch = data["batches"][0]
        assert "batch_no" in batch
        assert "batch_name" in batch
        assert "executor_name" in batch
        assert "total_amount" in batch
    
    def test_get_execution_batch_detail(self, client: TestClient, db: Session, test_user: User, auth_headers):
        """测试获取执行批次详情"""
        # 先执行一个批次
        request, item, supplier = self.setup_test_data(db, test_user)
        
        execute_response = client.post(
            "/api/admin/purchase/execution/execute",
            json={
                "request_ids": [request.id],
                "batch_name": "测试批次详情",
                "notes": "测试",
                "confirm": True
            },
            headers=auth_headers
        )
        batch_data = execute_response.json()
        batch_id = batch_data["id"]
        
        # 获取批次详情
        response = client.get(
            f"/api/admin/purchase/execution/batches/{batch_id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证详情数据
        assert data["id"] == batch_id
        assert data["batch_name"] == "测试批次详情"
        assert data["request_count"] == 1
        assert float(data["total_amount"]) == 500.00
    
    def test_execution_with_invalid_request(self, client: TestClient, db: Session, test_user: User, auth_headers):
        """测试执行无效申请（状态不正确）"""
        # 创建一个未批准的申请
        request = PurchaseRequest(
            request_no="REQ202401002",
            department_id=test_user.department_id,  # 使用用户的部门ID
            submitter_id=test_user.id,
            status="under_review",  # 非已批准状态
            final_total=Decimal("300.00"),
            notes="未批准的申请"
        )
        db.add(request)
        db.commit()
        
        # 尝试执行
        response = client.post(
            "/api/admin/purchase/execution/execute",
            json={
                "request_ids": [request.id],
                "batch_name": "无效测试批次",
                "confirm": True
            },
            headers=auth_headers
        )
        
        # 应该返回错误
        assert response.status_code == 400 or response.status_code == 422
    
    def test_execution_statistics(self, client: TestClient, db: Session, test_user: User, auth_headers):
        """测试执行统计数据"""
        # 先执行一个批次
        request, item, supplier = self.setup_test_data(db, test_user)
        
        execute_response = client.post(
            "/api/admin/purchase/execution/execute",
            json={
                "request_ids": [request.id],
                "batch_name": "统计测试批次",
                "confirm": True
            },
            headers=auth_headers
        )
        assert execute_response.status_code == 201
        
        # 获取统计数据
        response = client.get(
            "/api/admin/purchase/execution/statistics?days=30",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证统计数据结构
        assert "total_batches" in data
        assert "total_requests" in data
        assert "total_amount" in data
        assert "executor_statistics" in data
        assert data["total_batches"] >= 1
