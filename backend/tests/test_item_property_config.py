"""
物品属性配置测试模块
测试物品属性配置的创建、更新、删除等功能
"""

import pytest
import json
from typing import Dict, List
from tests.base import BaseTestCase
from app.models.item import ItemCategory, ItemPropertyConfig, ItemPrimaryCategory


class TestItemPropertyConfig(BaseTestCase):
    """物品属性配置测试类"""
    
    def setup_test_data(self):
        """设置测试数据"""
        # 创建测试用户
        self.admin_user = self.create_test_user(
            username="admin",
            email="<EMAIL>",
            password="admin123",
            full_name="系统管理员",
            is_superuser=True,
            is_active=True
        )
        
        # 创建测试部门
        self.test_department = self.create_test_department(
            name="测试部门",
            code="TEST"
        )
        
        # 创建测试角色
        self.test_role = self.create_test_role(
            name="物品管理员",
            code="ITEM_ADMIN"
        )
        
        # 创建测试一级分类
        self.primary_category = ItemPrimaryCategory(
            name="测试一级分类",
            description="用于测试的一级分类",
            code_prefix="TEST",
            code_format="0000",
            current_sequence=1,
            is_active=True
        )
        self.db.add(self.primary_category)
        self.db.commit()
        self.db.refresh(self.primary_category)
        
        # 创建测试二级分类
        self.category = ItemCategory(
            name="测试分类",
            description="用于测试的分类",
            primary_category_id=self.primary_category.id,
            is_active=True
        )
        self.db.add(self.category)
        self.db.commit()
        self.db.refresh(self.category)
    
    def get_auth_headers(self, username: str = "admin", password: str = "admin123") -> Dict[str, str]:
        """获取认证头"""
        response = self.client.post("/api/admin/auth/login", json={
            "username": username,
            "password": password
        })
        
        if response.status_code == 200:
            data = response.json()
            return {"Authorization": f"Bearer {data['access_token']}"}
        return {}
    
    def create_property_config_data(self, **kwargs) -> Dict:
        """创建属性配置数据"""
        return {
            "category_id": kwargs.get("category_id", self.category.id),
            "attribute_name": kwargs.get("attribute_name", "brand"),
            "input_type": kwargs.get("input_type", "select"),
            "options": kwargs.get("options", json.dumps(["选项A", "选项B", "选项C"]))
        }
    
    def test_create_property_config_success(self):
        """测试成功创建属性配置"""
        headers = self.get_auth_headers()
        
        config_data = self.create_property_config_data(
            attribute_name="brand",
            input_type="select",
            options=json.dumps(["品牌A", "品牌B", "品牌C"])
        )
        
        response = self.client.post(
            f"/api/admin/items/categories/{self.category.id}/property-configs",
            json=config_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["attribute_name"] == "brand"
        assert data["input_type"] == "select"
        assert data["category_id"] == self.category.id
        
        # 验证数据库中的数据
        db_config = self.db.query(ItemPropertyConfig).filter(
            ItemPropertyConfig.id == data["id"]
        ).first()
        assert db_config is not None
        assert db_config.attribute_name == "brand"
        assert db_config.input_type == "select"
    
    def test_create_property_config_text_type(self):
        """测试创建文本类型的属性配置"""
        headers = self.get_auth_headers()
        
        config_data = self.create_property_config_data(
            attribute_name="spec_material",
            input_type="text",
            options=None
        )
        
        response = self.client.post(
            f"/api/admin/items/categories/{self.category.id}/property-configs",
            json=config_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["input_type"] == "text"
        assert data["options"] is None
    
    def test_create_property_config_duplicate_attribute(self):
        """测试创建重复属性的配置"""
        headers = self.get_auth_headers()
        
        # 先创建一个配置
        config_data = self.create_property_config_data(
            attribute_name="brand",
            input_type="select",
            options=json.dumps(["品牌A", "品牌B"])
        )
        
        response1 = self.client.post(
            f"/api/admin/items/categories/{self.category.id}/property-configs",
            json=config_data,
            headers=headers
        )
        assert response1.status_code == 200
        
        # 尝试创建相同属性的配置
        response2 = self.client.post(
            f"/api/admin/items/categories/{self.category.id}/property-configs",
            json=config_data,
            headers=headers
        )
        assert response2.status_code == 400
        assert "该属性已存在配置" in response2.json()["detail"]
    
    def test_create_property_config_invalid_attribute(self):
        """测试创建无效属性的配置"""
        headers = self.get_auth_headers()
        
        config_data = self.create_property_config_data(
            attribute_name="invalid_attribute",
            input_type="select",
            options=json.dumps(["选项A", "选项B"])
        )
        
        response = self.client.post(
            f"/api/admin/items/categories/{self.category.id}/property-configs",
            json=config_data,
            headers=headers
        )
        
        assert response.status_code == 400
        assert "无效的属性名称" in response.json()["detail"]
    
    def test_create_property_config_invalid_input_type(self):
        """测试创建无效输入类型的配置"""
        headers = self.get_auth_headers()
        
        config_data = self.create_property_config_data(
            attribute_name="brand",
            input_type="invalid_type",
            options=json.dumps(["选项A", "选项B"])
        )
        
        response = self.client.post(
            f"/api/admin/items/categories/{self.category.id}/property-configs",
            json=config_data,
            headers=headers
        )
        
        assert response.status_code == 400
        assert "无效的输入类型" in response.json()["detail"]
    
    def test_create_property_config_invalid_options_format(self):
        """测试创建无效选项格式的配置"""
        headers = self.get_auth_headers()
        
        config_data = self.create_property_config_data(
            attribute_name="brand",
            input_type="select",
            options="invalid_json"
        )
        
        response = self.client.post(
            f"/api/admin/items/categories/{self.category.id}/property-configs",
            json=config_data,
            headers=headers
        )
        
        assert response.status_code == 400
        assert "选项格式错误" in response.json()["detail"]
    
    def test_list_property_configs(self):
        """测试获取属性配置列表"""
        headers = self.get_auth_headers()
        
        # 创建多个配置
        configs_data = [
            self.create_property_config_data(
                attribute_name="brand",
                input_type="select",
                options=json.dumps(["品牌A", "品牌B"])
            ),
            self.create_property_config_data(
                attribute_name="spec_material",
                input_type="text",
                options=None
            )
        ]
        
        for config_data in configs_data:
            response = self.client.post(
                f"/api/admin/items/categories/{self.category.id}/property-configs",
                json=config_data,
                headers=headers
            )
            assert response.status_code == 200
        
        # 获取配置列表
        response = self.client.get(
            f"/api/admin/items/categories/{self.category.id}/property-configs",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        
        # 验证配置按属性名称排序
        attribute_names = [config["attribute_name"] for config in data]
        assert attribute_names == ["brand", "spec_material"]
    
    def test_get_property_config_by_attribute(self):
        """测试根据属性名称获取配置"""
        headers = self.get_auth_headers()
        
        # 创建配置
        config_data = self.create_property_config_data(
            attribute_name="brand",
            input_type="select",
            options=json.dumps(["品牌A", "品牌B", "品牌C"])
        )
        
        response = self.client.post(
            f"/api/admin/items/categories/{self.category.id}/property-configs",
            json=config_data,
            headers=headers
        )
        assert response.status_code == 200
        
        # 获取配置
        response = self.client.get(
            f"/api/admin/items/categories/{self.category.id}/property-configs/attribute/brand",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["input_type"] == "select"
        assert data["options"] == ["品牌A", "品牌B", "品牌C"]
    
    def test_get_property_config_by_attribute_not_found(self):
        """测试获取不存在的属性配置"""
        headers = self.get_auth_headers()
        
        response = self.client.get(
            f"/api/admin/items/categories/{self.category.id}/property-configs/attribute/brand",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["input_type"] == "text"
        assert data["options"] is None
    
    def test_update_property_config(self):
        """测试更新属性配置"""
        headers = self.get_auth_headers()
        
        # 创建配置
        config_data = self.create_property_config_data(
            attribute_name="brand",
            input_type="select",
            options=json.dumps(["品牌A", "品牌B"])
        )
        
        response = self.client.post(
            f"/api/admin/items/categories/{self.category.id}/property-configs",
            json=config_data,
            headers=headers
        )
        assert response.status_code == 200
        config_id = response.json()["id"]
        
        # 更新配置
        update_data = {
            "input_type": "select",
            "options": json.dumps(["品牌A", "品牌B", "品牌C", "品牌D"])
        }
        
        response = self.client.put(
            f"/api/admin/items/categories/{self.category.id}/property-configs/{config_id}",
            json=update_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["options"] == json.dumps(["品牌A", "品牌B", "品牌C", "品牌D"])
    
    def test_delete_property_config(self):
        """测试删除属性配置"""
        headers = self.get_auth_headers()
        
        # 创建配置
        config_data = self.create_property_config_data(
            attribute_name="brand",
            input_type="select",
            options=json.dumps(["品牌A", "品牌B"])
        )
        
        response = self.client.post(
            f"/api/admin/items/categories/{self.category.id}/property-configs",
            json=config_data,
            headers=headers
        )
        assert response.status_code == 200
        config_id = response.json()["id"]
        
        # 删除配置
        response = self.client.delete(
            f"/api/admin/items/categories/{self.category.id}/property-configs/{config_id}",
            headers=headers
        )
        
        assert response.status_code == 200
        assert "属性配置删除成功" in response.json()["message"]
        
        # 验证配置已被删除
        response = self.client.get(
            f"/api/admin/items/categories/{self.category.id}/property-configs",
            headers=headers
        )
        assert response.status_code == 200
        assert len(response.json()) == 0 