"""
采购申请服务测试
"""

import pytest
from tests.base import BaseTestCase
from app.services.purchase_request_service import PurchaseRequestService
from app.models.purchase import PurchaseRequest, PurchaseRequestItem, RequestFlowHistory
from app.models.user import User, Department
from app.models.item import Item, ItemCategory, ItemPrimaryCategory
from app.core.exceptions import ResourceNotFoundException


class TestPurchaseRequestService(BaseTestCase):
    """采购申请服务测试"""

    def setup_test_data(self):
        """设置测试数据"""
        # 创建部门
        self.department = self.create_test_department()
        
        # 创建用户
        self.user = self.create_test_user(department_id=self.department.id)
        
        # 创建物品分类
        self.primary_category = self.create_test_primary_category()
        self.category = self.create_test_category(self.primary_category.id)
        
        # 创建物品
        self.item = self.create_test_item(self.category.id)
        
        # 创建采购申请
        self.purchase_request = self.create_test_purchase_request()

    def create_test_primary_category(self):
        """创建测试一级分类"""
        primary_category = ItemPrimaryCategory(
            name="测试一级分类",
            description="用于测试的一级分类",
            code_prefix="TEST",
            code_format="0000",
            current_sequence=1
        )
        self.db.add(primary_category)
        self.db.commit()
        self.db.refresh(primary_category)
        return primary_category

    def create_test_category(self, primary_category_id: int):
        """创建测试二级分类"""
        category = ItemCategory(
            name="测试二级分类",
            description="用于测试的二级分类",
            primary_category_id=primary_category_id
        )
        self.db.add(category)
        self.db.commit()
        self.db.refresh(category)
        return category

    def create_test_item(self, category_id: int):
        """创建测试物品"""
        item = Item(
            code="TEST_ITEM",
            name="测试物品",
            category_id=category_id,
            description="用于测试的物品",
            purchase_unit="piece"
        )
        self.db.add(item)
        self.db.commit()
        self.db.refresh(item)
        return item

    def create_test_purchase_request(self):
        """创建测试采购申请"""
        request = PurchaseRequest(
            request_no="TEST001",
            request_uuid="test-uuid-001",
            department_id=self.department.id,
            submitter_id=self.user.id,
            status="pending_submission",
            notes="测试采购申请"
        )
        self.db.add(request)
        self.db.commit()
        self.db.refresh(request)
        return request

    def test_update_request_status_success(self):
        """测试成功更新申请状态"""
        service = PurchaseRequestService(self.db)
        
        # 更新状态
        updated_request = service.update_request_status(
            request_id=self.purchase_request.id,
            new_status="under_review",
            operator_id=self.user.id,
            operator_name=self.user.full_name,
            action="submit",
            comments="申请人提交申请"
        )
        
        # 验证状态已更新
        assert updated_request.status == "under_review"
        assert updated_request.id == self.purchase_request.id
        
        # 验证流转历史已创建
        flow_history = self.db.query(RequestFlowHistory).filter(
            RequestFlowHistory.request_id == self.purchase_request.id
        ).all()
        
        assert len(flow_history) == 1
        latest_flow = flow_history[0]
        assert latest_flow.action == "submit"
        assert latest_flow.from_status == "pending_submission"
        assert latest_flow.to_status == "under_review"
        assert latest_flow.comments == "申请人提交申请"

    def test_update_request_status_nonexistent(self):
        """测试更新不存在的申请状态"""
        service = PurchaseRequestService(self.db)
        
        with pytest.raises(ResourceNotFoundException):
            service.update_request_status(
                request_id=99999,
                new_status="under_review",
                operator_id=self.user.id,
                operator_name=self.user.full_name
            )

    def test_update_request_status_multiple_updates(self):
        """测试多次更新申请状态"""
        service = PurchaseRequestService(self.db)
        
        # 第一次更新
        service.update_request_status(
            request_id=self.purchase_request.id,
            new_status="under_review",
            operator_id=self.user.id,
            operator_name=self.user.full_name,
            action="submit",
            comments="申请人提交申请"
        )
        
        # 第二次更新
        service.update_request_status(
            request_id=self.purchase_request.id,
            new_status="under_principle_approval",
            operator_id=self.user.id,
            operator_name=self.user.full_name,
            action="approve",
            comments="审批通过"
        )
        
        # 验证最终状态
        final_request = self.db.query(PurchaseRequest).filter(
            PurchaseRequest.id == self.purchase_request.id
        ).first()
        
        assert final_request.status == "under_principle_approval"
        
        # 验证流转历史
        flow_history = self.db.query(RequestFlowHistory).filter(
            RequestFlowHistory.request_id == self.purchase_request.id
        ).order_by(RequestFlowHistory.created_at).all()
        
        assert len(flow_history) == 2
        
        # 验证第一次流转
        first_flow = flow_history[0]
        assert first_flow.action == "submit"
        assert first_flow.to_status == "under_review"
        
        # 验证第二次流转
        second_flow = flow_history[1]
        assert second_flow.action == "approve"
        assert second_flow.to_status == "under_principle_approval"

    def test_clear_locked_currency_fields_on_withdraw(self):
        """测试撤销申请时清除锁定货币字段"""
        service = PurchaseRequestService(self.db)
        
        # 首先为申请添加一个物品
        request_item = PurchaseRequestItem(
            request_id=self.purchase_request.id,
            item_id=self.item.id,
            item_code=self.item.code,
            item_name=self.item.name,
            spq_quantity=1,
            spq_count=1,
            spq_unit="piece",
            notes="测试物品"
        )
        self.db.add(request_item)
        self.db.commit()
        self.db.refresh(request_item)
        
        # 首先提交申请以锁定汇率
        service.update_request_status(
            request_id=self.purchase_request.id,
            new_status="under_review",
            operator_id=self.user.id,
            operator_name=self.user.full_name,
            action="submit",
            comments="申请人提交申请"
        )
        
        # 验证汇率已锁定
        request_items = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.request_id == self.purchase_request.id
        ).all()
        
        for item in request_items:
            assert item.locked_currency_code is not None
            assert item.locked_exchange_rate is not None
            assert item.locked_exchange_rate_stage == "submitted"
        
        # 撤销申请回到待提交状态
        service.withdraw_to_pending(
            request_id=self.purchase_request.id,
            user_id=self.user.id
        )
        
        # 验证汇率锁定已清除
        self.db.refresh(request_items[0])  # Refresh to get updated data
        for item in request_items:
            assert item.locked_currency_code is None
            assert item.locked_exchange_rate is None
            assert item.locked_exchange_rate_month is None
            assert item.locked_exchange_rate_stage is None
            assert item.locked_exchange_rate_at is None

    def test_clear_locked_currency_fields_on_edit_rejected_request(self):
        """测试编辑拒绝申请时清除锁定货币字段"""
        service = PurchaseRequestService(self.db)
        
        # 首先为申请添加一个物品
        request_item = PurchaseRequestItem(
            request_id=self.purchase_request.id,
            item_id=self.item.id,
            item_code=self.item.code,
            item_name=self.item.name,
            spq_quantity=1,
            spq_count=1,
            spq_unit="piece",
            notes="测试物品"
        )
        self.db.add(request_item)
        self.db.commit()
        self.db.refresh(request_item)
        
        # 首先提交申请以锁定汇率
        service.update_request_status(
            request_id=self.purchase_request.id,
            new_status="under_review",
            operator_id=self.user.id,
            operator_name=self.user.full_name,
            action="submit",
            comments="申请人提交申请"
        )
        
        # 拒绝申请
        service.update_request_status(
            request_id=self.purchase_request.id,
            new_status="rejected",
            operator_id=self.user.id,
            operator_name=self.user.full_name,
            action="reject",
            comments="申请被拒绝"
        )
        
        # 验证汇率已锁定
        request_items = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.request_id == self.purchase_request.id
        ).all()
        
        for item in request_items:
            assert item.locked_currency_code is not None
            assert item.locked_exchange_rate is not None
            assert item.locked_exchange_rate_stage == "submitted"
        
        # 编辑拒绝的申请（这会自动将状态改为pending_submission）
        service.update_request(
            request_id=self.purchase_request.id,
            update_data={"notes": "更新后的备注"}
        )
        
        # 验证汇率锁定已清除
        self.db.refresh(request_items[0])  # Refresh to get updated data
        for item in request_items:
            assert item.locked_currency_code is None
            assert item.locked_exchange_rate is None
            assert item.locked_exchange_rate_month is None
            assert item.locked_exchange_rate_stage is None
            assert item.locked_exchange_rate_at is None
