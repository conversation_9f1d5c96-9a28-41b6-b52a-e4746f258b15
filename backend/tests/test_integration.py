"""
集成测试
使用IntegrationTestCase基类，测试完整的业务流程
"""

import pytest
from tests.base import IntegrationTestCase


class TestIntegration(IntegrationTestCase):
    """集成测试"""

    def test_complete_user_workflow(self):
        """测试完整的用户工作流程"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 1. 创建新部门
        dept_data = {
            "name": "测试部门",
            "code": "TEST_DEPT",
            "description": "用于集成测试的部门"
        }
        
        dept_response = self.client.post("/api/admin/departments/", json=dept_data, headers=headers)
        assert dept_response.status_code == 201
        new_dept_id = dept_response.json()["id"]
        
        # 2. 创建新角色
        role_data = {
            "name": "测试角色",
            "code": "TEST_ROLE",
            "description": "用于集成测试的角色",
            "permission_ids": [self.permissions[0].id, self.permissions[1].id]
        }
        
        role_response = self.client.post("/api/admin/roles/", json=role_data, headers=headers)
        assert role_response.status_code == 201
        new_role_id = role_response.json()["id"]
        
        # 3. 创建新用户
        user_data = {
            "username": "integration_user",
            "email": "<EMAIL>",
            "password": "integration123",
            "full_name": "集成测试用户",
            "department_id": new_dept_id,
            "role_id": new_role_id,
            "is_superuser": False,
            "is_active": True
        }
        
        user_response = self.client.post("/api/admin/users/", json=user_data, headers=headers)
        assert user_response.status_code == 201
        new_user_id = user_response.json()["id"]
        
        # 4. 验证用户创建成功
        get_user_response = self.client.get(f"/api/admin/users/{new_user_id}", headers=headers)
        assert get_user_response.status_code == 200
        user_info = get_user_response.json()
        assert user_info["username"] == "integration_user"
        assert user_info["department_id"] == new_dept_id
        assert user_info["role_id"] == new_role_id
        
        # 5. 新用户登录
        login_response = self.client.post("/api/admin/auth/login", json={
            "username": "integration_user",
            "password": "integration123"
        })
        assert login_response.status_code == 200
        user_token = login_response.json()["access_token"]
        user_headers = {"Authorization": f"Bearer {user_token}"}
        
        # 6. 验证用户权限
        # 用户应该能访问有权限的端点
        self.assert_permission_check(user_headers, "/api/admin/users/", "GET", 200)
        # 用户不应该能访问没有权限的端点
        self.assert_permission_check(user_headers, "/api/admin/users/", "POST", 403)
        
        # 7. 更新用户信息
        update_data = {
            "full_name": "更新后的集成测试用户",
            "email": "<EMAIL>"
        }
        
        update_response = self.client.put(f"/api/admin/users/{new_user_id}", json=update_data, headers=headers)
        assert update_response.status_code == 200
        updated_user = update_response.json()
        assert updated_user["full_name"] == "更新后的集成测试用户"
        
        # 8. 验证删除用户功能已移除
        delete_response = self.client.delete(f"/api/admin/users/{new_user_id}", headers=headers)
        assert delete_response.status_code == 405  # Method Not Allowed
        
        # 9. 验证用户仍然存在（删除功能已移除）
        verify_delete_response = self.client.get(f"/api/admin/users/{new_user_id}", headers=headers)
        assert verify_delete_response.status_code == 200

    def test_data_filtering_and_search(self):
        """测试数据过滤和搜索功能"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 测试用户搜索和过滤
        response = self.client.get("/api/admin/users/?search=testuser&is_active=true", headers=headers)
        assert response.status_code == 200
        data = response.json()
        
        # 验证搜索结果
        for user in data["items"]:
            assert "testuser" in user["username"] or "testuser" in user["full_name"]
            assert user["is_active"] is True
        
        # 测试部门搜索
        response = self.client.get("/api/admin/departments/?search=部门", headers=headers)
        assert response.status_code == 200
        data = response.json()
        
        # 验证部门搜索结果
        for dept in data["items"]:
            assert "部门" in dept["name"] or "部门" in dept["description"]
        
        # 测试权限搜索
        response = self.client.get("/api/admin/permissions/?search=user", headers=headers)
        assert response.status_code == 200
        data = response.json()
        
        # 验证权限搜索结果
        for perm in data["items"]:
            assert "user" in perm["code"] or "user" in perm["name"]

    def test_pagination_consistency(self):
        """测试分页一致性"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 测试用户分页
        page1_response = self.client.get("/api/admin/users/?page=1&size=5", headers=headers)
        assert page1_response.status_code == 200
        page1_data = page1_response.json()
        
        page2_response = self.client.get("/api/admin/users/?page=2&size=5", headers=headers)
        assert page2_response.status_code == 200
        page2_data = page2_response.json()
        
        # 验证分页数据不重复
        page1_ids = {user["id"] for user in page1_data["items"]}
        page2_ids = {user["id"] for user in page2_data["items"]}
        assert not page1_ids.intersection(page2_ids)
        
        # 测试部门分页
        dept_page1 = self.client.get("/api/admin/departments/?page=1&size=3", headers=headers)
        assert dept_page1.status_code == 200
        dept_page1_data = dept_page1.json()
        
        dept_page2 = self.client.get("/api/admin/departments/?page=2&size=3", headers=headers)
        assert dept_page2.status_code == 200
        dept_page2_data = dept_page2.json()
        
        # 验证部门分页数据不重复
        dept_page1_ids = {dept["id"] for dept in dept_page1_data["items"]}
        dept_page2_ids = {dept["id"] for dept in dept_page2_data["items"]}
        assert not dept_page1_ids.intersection(dept_page2_ids)

    def test_role_permission_integration(self):
        """测试角色权限集成"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建自定义权限
        custom_permissions = []
        for i in range(3):
            perm_data = {
                "code": f"custom.permission.{i}",
                "name": f"自定义权限{i}",
                "description": f"自定义权限{i}的描述",
                "module": "custom"
            }
            perm_response = self.client.post("/api/admin/permissions/", json=perm_data, headers=headers)
            assert perm_response.status_code == 201
            custom_permissions.append(perm_response.json())
        
        # 创建自定义角色
        role_data = {
            "name": "自定义集成角色",
            "code": "CUSTOM_INTEGRATION_ROLE",
            "description": "用于集成测试的自定义角色",
            "permission_ids": [perm["id"] for perm in custom_permissions]
        }
        
        role_response = self.client.post("/api/admin/roles/", json=role_data, headers=headers)
        assert role_response.status_code == 201
        custom_role = role_response.json()
        
        # 验证角色权限分配
        assert len(custom_role["permissions"]) == 3
        permission_codes = {perm["code"] for perm in custom_role["permissions"]}
        expected_codes = {perm["code"] for perm in custom_permissions}
        assert permission_codes == expected_codes
        
        # 创建使用该角色的用户
        user_data = {
            "username": "custom_role_user",
            "email": "<EMAIL>",
            "password": "custom123",
            "full_name": "自定义角色用户",
            "role_id": custom_role["id"],
            "is_active": True
        }
        
        user_response = self.client.post("/api/admin/users/", json=user_data, headers=headers)
        assert user_response.status_code == 201
        custom_user = user_response.json()
        
        # 用户登录
        login_response = self.client.post("/api/admin/auth/login", json={
            "username": "custom_role_user",
            "password": "custom123"
        })
        assert login_response.status_code == 200
        user_token = login_response.json()["access_token"]
        user_headers = {"Authorization": f"Bearer {user_token}"}
        
        # 验证用户权限（这里需要根据实际的权限检查逻辑调整）
        # 用户应该只能访问其角色拥有的权限

    def test_department_user_relationship(self):
        """测试部门用户关系"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建新部门
        dept_data = {
            "name": "关系测试部门",
            "code": "RELATION_TEST",
            "description": "用于测试部门用户关系的部门"
        }
        
        dept_response = self.client.post("/api/admin/departments/", json=dept_data, headers=headers)
        assert dept_response.status_code == 201
        test_dept = dept_response.json()
        
        # 创建多个用户分配到该部门
        users = []
        for i in range(5):
            user_data = {
                "username": f"relation_user{i}",
                "email": f"relation_user{i}@example.com",
                "password": "relation123",
                "full_name": f"关系测试用户{i}",
                "department_id": test_dept["id"],
                "is_active": True
            }
            
            user_response = self.client.post("/api/admin/users/", json=user_data, headers=headers)
            assert user_response.status_code == 201
            users.append(user_response.json())
        
        # 获取部门用户列表
        dept_users_response = self.client.get(f"/api/admin/departments/{test_dept['id']}/users", headers=headers)
        if dept_users_response.status_code == 200:
            dept_users = dept_users_response.json()
            assert len(dept_users["items"]) >= 5
            
            # 验证所有用户都属于该部门
            user_ids = {user["id"] for user in dept_users["items"]}
            expected_ids = {user["id"] for user in users}
            assert expected_ids.issubset(user_ids)
        
        # 更新用户部门
        if users:
            update_data = {"department_id": self.departments[0].id}
            update_response = self.client.put(f"/api/admin/users/{users[0]['id']}", json=update_data, headers=headers)
            assert update_response.status_code == 200
            
            # 验证用户部门已更新
            updated_user = update_response.json()
            assert updated_user["department_id"] == self.departments[0].id

    def test_error_handling(self):
        """测试错误处理"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 测试访问不存在的资源
        response = self.client.get("/api/admin/users/99999", headers=headers)
        assert response.status_code == 404
        
        response = self.client.get("/api/admin/departments/99999", headers=headers)
        assert response.status_code == 404
        
        response = self.client.get("/api/admin/roles/99999", headers=headers)
        assert response.status_code == 404
        
        # 测试无效的请求数据
        invalid_user_data = {
            "username": "",  # 空用户名
            "email": "invalid-email",  # 无效邮箱
            "password": "123"  # 密码太短
        }
        
        response = self.client.post("/api/admin/users/", json=invalid_user_data, headers=headers)
        assert response.status_code == 422
        
        # 测试重复数据
        duplicate_dept_data = {
            "name": "重复部门",
            "code": self.departments[0].code,  # 使用已存在的代码
            "description": "重复的部门"
        }
        
        response = self.client.post("/api/admin/departments/", json=duplicate_dept_data, headers=headers)
        assert response.status_code in [400, 409]  # 取决于API实现

    def test_performance_with_large_dataset(self):
        """测试大数据集性能"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建大量测试数据
        for i in range(50):
            self.create_test_user(
                username=f"perf_user{i}",
                email=f"perf_user{i}@example.com",
                full_name=f"性能测试用户{i}"
            )
        
        for i in range(20):
            self.create_test_department(
                name=f"性能测试部门{i}",
                code=f"PERF{i:03d}",
                description=f"性能测试部门{i}的描述"
            )
        
        # 测试查询性能
        import time
        
        start_time = time.time()
        response = self.client.get("/api/admin/users/?page=1&size=100", headers=headers)
        end_time = time.time()
        
        assert response.status_code == 200
        assert end_time - start_time < 2.0  # 查询应该在2秒内完成
        
        # 测试搜索性能
        start_time = time.time()
        response = self.client.get("/api/admin/users/?search=perf", headers=headers)
        end_time = time.time()
        
        assert response.status_code == 200
        assert end_time - start_time < 1.0  # 搜索应该在1秒内完成

    def test_concurrent_operations(self):
        """测试并发操作"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建多个用户并发
        import threading
        import time
        
        def create_user(user_id):
            user_data = {
                "username": f"concurrent_user{user_id}",
                "email": f"concurrent_user{user_id}@example.com",
                "password": "concurrent123",
                "full_name": f"并发用户{user_id}",
                "is_active": True
            }
            
            response = self.client.post("/api/admin/users/", json=user_data, headers=headers)
            return response.status_code == 201
        
        # 启动多个线程创建用户
        threads = []
        results = []
        
        for i in range(10):
            thread = threading.Thread(target=lambda i=i: results.append(create_user(i)))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证所有用户都创建成功
        assert all(results)
        
        # 验证用户数量
        response = self.client.get("/api/admin/users/", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) >= 10  # 至少应该有10个新用户 