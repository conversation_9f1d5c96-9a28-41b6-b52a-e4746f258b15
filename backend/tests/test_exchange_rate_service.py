import pytest
from decimal import Decimal
from datetime import date, datetime
from sqlalchemy.orm import Session

from app.services.exchange_rate_service import ExchangeRateService
from app.schemas.exchange_rate import ExchangeRateCreate, ExchangeRateUpdate, PurchaseRequestExchangeRateCreate
from app.core.exceptions import BusinessException, ResourceNotFoundException
from tests.base import BaseTestCase


class TestExchangeRateService(BaseTestCase):
    """汇率管理服务测试类"""
    
    def setup_test_data(self):
        """设置测试数据"""
        # 创建测试部门
        self.department = self.create_test_department()
        
        # 创建测试用户
        self.user = self.create_test_user(department_id=self.department.id)
        
        # 创建汇率服务实例
        self.service = ExchangeRateService(self.db)
    
    def test_create_exchange_rate(self):
        """测试创建汇率记录"""
        # 准备测试数据
        exchange_rate_data = ExchangeRateCreate(
            currency_code="CNY",
            rate=Decimal("7.200000"),
            effective_month=date(2024, 1, 1),
            status="active"
        )
        
        # 执行创建
        exchange_rate = self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 验证结果
        assert exchange_rate.id is not None
        assert exchange_rate.currency_code == "CNY"
        assert exchange_rate.rate == Decimal("7.200000")
        assert exchange_rate.effective_month == date(2024, 1, 1)
        assert exchange_rate.status == "active"
        assert exchange_rate.created_by == self.user.id
    
    def test_create_exchange_rate_duplicate_month(self):
        """测试创建重复月份的汇率记录"""
        # 先创建一个汇率记录
        exchange_rate_data = ExchangeRateCreate(
            currency_code="CNY",
            rate=Decimal("7.200000"),
            effective_month=date(2024, 1, 1),
            status="active"
        )
        self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 尝试创建同一货币同一月份的汇率记录
        duplicate_data = ExchangeRateCreate(
            currency_code="CNY",
            rate=Decimal("7.300000"),
            effective_month=date(2024, 1, 1),
            status="active"
        )
        
        # 应该抛出业务异常
        with pytest.raises(BusinessException) as exc_info:
            self.service.create_exchange_rate(duplicate_data, self.user.id)
        
        assert "已存在有效汇率" in str(exc_info.value)
    
    def test_update_exchange_rate(self):
        """测试更新汇率记录"""
        # 先创建一个汇率记录
        exchange_rate_data = ExchangeRateCreate(
            currency_code="CNY",
            rate=Decimal("7.200000"),
            effective_month=date(2024, 1, 1),
            status="active"
        )
        exchange_rate = self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 更新汇率
        update_data = ExchangeRateUpdate(
            rate=Decimal("7.300000"),
            change_reason="汇率调整"
        )
        
        updated_rate = self.service.update_exchange_rate(exchange_rate.id, update_data, self.user.id)
        
        # 验证结果
        assert updated_rate.rate == Decimal("7.300000")
        assert updated_rate.updated_by == self.user.id
        
        # 验证修改日志
        logs = self.db.query(self.service.db.query().from_statement(
            "SELECT * FROM exchange_rate_logs WHERE exchange_rate_id = :rate_id"
        ).params(rate_id=exchange_rate.id).statement).all()
        
        assert len(logs) == 1
        assert logs[0].old_rate == Decimal("7.200000")
        assert logs[0].new_rate == Decimal("7.300000")
        assert logs[0].change_reason == "汇率调整"
        assert logs[0].changed_by == self.user.id
    
    def test_get_exchange_rate(self):
        """测试获取汇率记录"""
        # 先创建一个汇率记录
        exchange_rate_data = ExchangeRateCreate(
            currency_code="CNY",
            rate=Decimal("7.200000"),
            effective_month=date(2024, 1, 1),
            status="active"
        )
        created_rate = self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 获取汇率记录
        retrieved_rate = self.service.get_exchange_rate(created_rate.id)
        
        # 验证结果
        assert retrieved_rate.id == created_rate.id
        assert retrieved_rate.currency_code == "CNY"
        assert retrieved_rate.rate == Decimal("7.200000")
    
    def test_get_exchange_rate_not_found(self):
        """测试获取不存在的汇率记录"""
        with pytest.raises(ResourceNotFoundException) as exc_info:
            self.service.get_exchange_rate(999)
        
        assert "汇率记录 999 不存在" in str(exc_info.value)
    
    def test_get_exchange_rates(self):
        """测试查询汇率记录列表"""
        # 创建多个汇率记录
        rates_data = [
            ("CNY", "7.200000", date(2024, 1, 1)),
            ("EUR", "0.920000", date(2024, 1, 1)),
            ("JPY", "148.500000", date(2024, 1, 1)),
        ]
        
        for currency_code, rate, effective_month in rates_data:
            exchange_rate_data = ExchangeRateCreate(
                currency_code=currency_code,
                rate=Decimal(rate),
                effective_month=effective_month,
                status="active"
            )
            self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 查询汇率记录列表
        result = self.service.get_exchange_rates(
            ExchangeRateQuery(page=1, size=10)
        )
        
        # 验证结果
        assert result["total"] == 3
        assert len(result["data"]) == 3
        assert result["page"] == 1
        assert result["size"] == 10
    
    def test_get_exchange_rate_by_currency_month(self):
        """测试根据货币代码和生效月份获取汇率"""
        # 先创建一个汇率记录
        exchange_rate_data = ExchangeRateCreate(
            currency_code="CNY",
            rate=Decimal("7.200000"),
            effective_month=date(2024, 1, 1),
            status="active"
        )
        self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 获取汇率
        rate = self.service.get_exchange_rate_by_currency_month("CNY", date(2024, 1, 1))
        
        # 验证结果
        assert rate is not None
        assert rate.currency_code == "CNY"
        assert rate.effective_month == date(2024, 1, 1)
        assert rate.rate == Decimal("7.200000")
    
    def test_get_latest_exchange_rate(self):
        """测试获取最新有效汇率"""
        # 创建多个月份的汇率记录
        rates_data = [
            ("CNY", "7.200000", date(2024, 1, 1)),
            ("CNY", "7.300000", date(2024, 2, 1)),
            ("CNY", "7.400000", date(2024, 3, 1)),
        ]
        
        for currency_code, rate, effective_month in rates_data:
            exchange_rate_data = ExchangeRateCreate(
                currency_code=currency_code,
                rate=Decimal(rate),
                effective_month=effective_month,
                status="active"
            )
            self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 获取最新汇率
        latest_rate = self.service.get_latest_exchange_rate("CNY", date(2024, 3, 15))
        
        # 验证结果（应该返回3月份的汇率）
        assert latest_rate is not None
        assert latest_rate.effective_month == date(2024, 3, 1)
        assert latest_rate.rate == Decimal("7.400000")
    
    def test_convert_currency_to_usd(self):
        """测试货币转换为美元"""
        # 先创建一个汇率记录
        exchange_rate_data = ExchangeRateCreate(
            currency_code="CNY",
            rate=Decimal("7.200000"),
            effective_month=date(2024, 1, 1),
            status="active"
        )
        self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 转换100人民币为美元
        usd_amount = self.service.convert_currency_to_usd(Decimal("100"), "CNY", date(2024, 1, 15))
        
        # 验证结果：100 CNY ÷ 7.2 = 13.89 USD
        expected_usd = Decimal("100") / Decimal("7.200000")
        assert usd_amount == expected_usd
    
    def test_convert_currency_to_usd_no_rate(self):
        """测试货币转换为美元（无汇率）"""
        # 尝试转换没有汇率的货币
        usd_amount = self.service.convert_currency_to_usd(Decimal("100"), "XXX", date(2024, 1, 15))
        
        # 应该返回None
        assert usd_amount is None
    
    def test_convert_usd_to_currency(self):
        """测试美元转换为指定货币"""
        # 先创建一个汇率记录
        exchange_rate_data = ExchangeRateCreate(
            currency_code="CNY",
            rate=Decimal("7.200000"),
            effective_month=date(2024, 1, 1),
            status="active"
        )
        self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 转换10美元为人民币
        cny_amount = self.service.convert_usd_to_currency(Decimal("10"), "CNY", date(2024, 1, 15))
        
        # 验证结果：10 USD × 7.2 = 72 CNY
        expected_cny = Decimal("10") * Decimal("7.200000")
        assert cny_amount == expected_cny
    
    def test_format_price_display(self):
        """测试价格显示格式化"""
        # 大于1美元，显示2位小数
        formatted_price = self.service.format_price_display(Decimal("123.456"))
        assert formatted_price == "$123.46"
        
        # 小于1美元，显示4位小数
        formatted_price = self.service.format_price_display(Decimal("0.123456"))
        assert formatted_price == "$0.1235"
    
    def test_is_exchange_rate_valid(self):
        """测试汇率有效性检查"""
        # 先创建一个汇率记录
        exchange_rate_data = ExchangeRateCreate(
            currency_code="CNY",
            rate=Decimal("7.200000"),
            effective_month=date(2024, 1, 1),
            status="active"
        )
        self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 检查有效性
        is_valid = self.service.is_exchange_rate_valid("CNY", date(2024, 1, 15))
        assert is_valid is True
        
        # 检查没有汇率的货币
        is_valid = self.service.is_exchange_rate_valid("XXX", date(2024, 1, 15))
        assert is_valid is False
    
    def test_create_purchase_request_exchange_rate(self):
        """测试创建采购申请汇率记录"""
        # 准备测试数据
        data = PurchaseRequestExchangeRateCreate(
            purchase_request_id=1,
            stage="submitted",
            currency_code="CNY",
            rate=Decimal("7.200000"),
            recorded_by=self.user.id
        )
        
        # 执行创建
        exchange_rate_record = self.service.create_purchase_request_exchange_rate(data)
        
        # 验证结果
        assert exchange_rate_record.id is not None
        assert exchange_rate_record.purchase_request_id == 1
        assert exchange_rate_record.stage == "submitted"
        assert exchange_rate_record.currency_code == "CNY"
        assert exchange_rate_record.rate == Decimal("7.200000")
        assert exchange_rate_record.recorded_by == self.user.id
    
    def test_get_purchase_request_exchange_rates(self):
        """测试获取采购申请汇率记录"""
        # 创建多个汇率记录
        data_list = [
            PurchaseRequestExchangeRateCreate(
                purchase_request_id=1,
                stage="submitted",
                currency_code="CNY",
                rate=Decimal("7.200000"),
                recorded_by=self.user.id
            ),
            PurchaseRequestExchangeRateCreate(
                purchase_request_id=1,
                stage="principle_approved",
                currency_code="CNY",
                rate=Decimal("7.300000"),
                recorded_by=self.user.id
            ),
        ]
        
        for data in data_list:
            self.service.create_purchase_request_exchange_rate(data)
        
        # 获取汇率记录
        records = self.service.get_purchase_request_exchange_rates(1)
        
        # 验证结果
        assert len(records) == 2
        assert records[0].stage == "submitted"
        assert records[1].stage == "principle_approved"
    
    def test_get_exchange_rate_by_stage(self):
        """测试根据阶段获取汇率记录"""
        # 创建汇率记录
        data = PurchaseRequestExchangeRateCreate(
            purchase_request_id=1,
            stage="submitted",
            currency_code="CNY",
            rate=Decimal("7.200000"),
            recorded_by=self.user.id
        )
        self.service.create_purchase_request_exchange_rate(data)
        
        # 获取指定阶段的汇率记录
        record = self.service.get_exchange_rate_by_stage(1, "submitted")
        
        # 验证结果
        assert record is not None
        assert record.stage == "submitted"
        assert record.currency_code == "CNY"
        assert record.rate == Decimal("7.200000")
