"""
权限管理功能测试
使用PermissionTestCase基类，简化测试代码
"""

import pytest
from tests.base import PermissionTestCase


class TestPermissionManagement(PermissionTestCase):
    """权限管理功能测试"""

    def test_get_permissions_list(self):
        """测试获取权限列表"""
        headers = self.get_auth_headers("admin", "admin123")
        
        response = self.client.get("/api/admin/permissions/", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert len(data["items"]) >= len(self.permissions)

    def test_get_permission_by_id(self):
        """测试根据ID获取权限"""
        headers = self.get_auth_headers("admin", "admin123")
        
        test_permission = self.permissions[0]
        response = self.client.get(f"/api/admin/permissions/{test_permission.id}", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_permission.id
        assert data["code"] == test_permission.code

    def test_create_permission(self):
        """测试创建权限"""
        headers = self.get_auth_headers("admin", "admin123")
        
        permission_data = {
            "code": "test.create",
            "name": "测试创建权限",
            "description": "用于测试的创建权限",
            "module": "test"
        }
        
        response = self.client.post("/api/admin/permissions/", json=permission_data, headers=headers)
        
        assert response.status_code == 201
        data = response.json()
        assert data["code"] == "test.create"
        assert data["name"] == "测试创建权限"

    def test_update_permission(self):
        """测试更新权限"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建测试权限
        test_permission = self.create_test_permission(
            code="update.test",
            name="更新测试权限",
            description="用于更新测试的权限"
        )
        
        update_data = {
            "name": "更新后的权限",
            "description": "更新后的描述"
        }
        
        response = self.client.put(f"/api/admin/permissions/{test_permission.id}", json=update_data, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "更新后的权限"
        assert data["description"] == "更新后的描述"

    def test_delete_permission(self):
        """测试删除权限"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建测试权限
        test_permission = self.create_test_permission(
            code="delete.test",
            name="删除测试权限"
        )
        
        response = self.client.delete(f"/api/admin/permissions/{test_permission.id}", headers=headers)
        
        assert response.status_code == 204
        
        # 验证权限已被删除
        get_response = self.client.get(f"/api/admin/permissions/{test_permission.id}", headers=headers)
        assert get_response.status_code == 404

    def test_get_roles_list(self):
        """测试获取角色列表"""
        headers = self.get_auth_headers("admin", "admin123")
        
        response = self.client.get("/api/admin/roles/", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert len(data["items"]) >= 2  # 至少有两个角色

    def test_create_role(self):
        """测试创建角色"""
        headers = self.get_auth_headers("admin", "admin123")
        
        role_data = {
            "name": "测试角色",
            "code": "TEST_ROLE",
            "description": "用于测试的角色",
            "permission_ids": [self.permissions[0].id, self.permissions[1].id]
        }
        
        response = self.client.post("/api/admin/roles/", json=role_data, headers=headers)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "测试角色"
        assert data["code"] == "TEST_ROLE"
        assert len(data["permissions"]) == 2

    def test_update_role(self):
        """测试更新角色"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建测试角色
        test_role = self.create_test_role(
            name="更新测试角色",
            code="UPDATE_TEST",
            description="用于更新测试的角色"
        )
        
        update_data = {
            "name": "更新后的角色",
            "description": "更新后的角色描述",
            "permission_ids": [self.permissions[2].id, self.permissions[3].id]
        }
        
        response = self.client.put(f"/api/admin/roles/{test_role.id}", json=update_data, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "更新后的角色"
        assert data["description"] == "更新后的角色描述"
        assert len(data["permissions"]) == 2

    def test_delete_role(self):
        """测试删除角色"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建测试角色
        test_role = self.create_test_role(
            name="删除测试角色",
            code="DELETE_TEST"
        )
        
        response = self.client.delete(f"/api/admin/roles/{test_role.id}", headers=headers)
        
        assert response.status_code == 204
        
        # 验证角色已被删除
        get_response = self.client.get(f"/api/admin/roles/{test_role.id}", headers=headers)
        assert get_response.status_code == 404

    def test_permission_enforcement(self):
        """测试权限强制执行"""
        # 测试管理员用户（有所有权限）
        admin_headers = self.get_auth_headers("admin", "admin123")
        
        # 管理员应该能访问所有端点
        self.assert_permission_check(admin_headers, "/api/admin/users/", "GET", 200)
        self.assert_permission_check(admin_headers, "/api/admin/users/", "POST", 201)
        self.assert_permission_check(admin_headers, "/api/admin/permissions/", "GET", 200)
        self.assert_permission_check(admin_headers, "/api/admin/roles/", "GET", 200)
        
        # 测试普通用户（只有有限权限）
        user_headers = self.get_auth_headers("user", "user123")
        
        # 普通用户只能读取用户和部门信息
        self.assert_permission_check(user_headers, "/api/admin/users/", "GET", 200)
        self.assert_permission_check(user_headers, "/api/admin/users/", "POST", 403)  # 无创建权限
        self.assert_permission_check(user_headers, "/api/admin/permissions/", "GET", 403)  # 无权限访问
        self.assert_permission_check(user_headers, "/api/admin/roles/", "GET", 403)  # 无权限访问

    def test_role_permission_assignment(self):
        """测试角色权限分配"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建新角色
        role_data = {
            "name": "自定义角色",
            "code": "CUSTOM_ROLE",
            "description": "自定义权限角色"
        }
        
        response = self.client.post("/api/admin/roles/", json=role_data, headers=headers)
        assert response.status_code == 201
        role_id = response.json()["id"]
        
        # 为角色分配权限
        permission_ids = [self.permissions[0].id, self.permissions[1].id, self.permissions[2].id]
        update_data = {"permission_ids": permission_ids}
        
        response = self.client.put(f"/api/admin/roles/{role_id}", json=update_data, headers=headers)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data["permissions"]) == 3
        
        # 验证分配的权限
        permission_codes = [perm["code"] for perm in data["permissions"]]
        expected_codes = [self.permissions[0].code, self.permissions[1].code, self.permissions[2].code]
        assert set(permission_codes) == set(expected_codes)

    def test_permission_validation(self):
        """测试权限数据验证"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 测试缺少必填字段
        invalid_data = {
            "name": "测试权限"
            # 缺少code
        }
        
        response = self.client.post("/api/admin/permissions/", json=invalid_data, headers=headers)
        assert response.status_code == 422
        
        # 测试重复权限代码
        duplicate_data = {
            "code": self.permissions[0].code,  # 使用已存在的权限代码
            "name": "重复权限",
            "description": "重复的权限"
        }
        
        response = self.client.post("/api/admin/permissions/", json=duplicate_data, headers=headers)
        assert response.status_code == 400  # 或409，取决于API实现

    def test_role_validation(self):
        """测试角色数据验证"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 测试缺少必填字段
        invalid_data = {
            "description": "测试角色"
            # 缺少name和code
        }
        
        response = self.client.post("/api/admin/roles/", json=invalid_data, headers=headers)
        assert response.status_code == 422
        
        # 测试重复角色代码
        duplicate_data = {
            "name": "重复角色",
            "code": self.admin_role.code,  # 使用已存在的角色代码
            "description": "重复的角色"
        }
        
        response = self.client.post("/api/admin/roles/", json=duplicate_data, headers=headers)
        assert response.status_code == 400  # 或409，取决于API实现

    def test_permission_search(self):
        """测试权限搜索功能"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 搜索特定模块的权限
        response = self.client.get("/api/admin/permissions/?module=test", headers=headers)
        assert response.status_code == 200
        data = response.json()
        
        # 验证所有返回的权限都属于test模块
        for permission in data["items"]:
            assert permission["module"] == "test"
        
        # 搜索特定代码的权限
        search_code = "user"
        response = self.client.get(f"/api/admin/permissions/?search={search_code}", headers=headers)
        assert response.status_code == 200
        data = response.json()
        
        # 验证搜索结果包含搜索关键词
        for permission in data["items"]:
            assert search_code in permission["code"] or search_code in permission["name"]

    def test_role_search(self):
        """测试角色搜索功能"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 搜索角色
        search_term = "管理员"
        response = self.client.get(f"/api/admin/roles/?search={search_term}", headers=headers)
        assert response.status_code == 200
        data = response.json()
        
        # 验证搜索结果包含搜索关键词
        for role in data["items"]:
            assert search_term in role["name"] or search_term in role["description"] 