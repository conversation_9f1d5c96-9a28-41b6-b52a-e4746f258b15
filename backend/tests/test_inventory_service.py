"""
库存服务测试
测试原子操作和数据一致性
"""

import pytest
from decimal import Decimal
import threading
import time
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine

from tests.base import BaseTestCase
from app.services.inventory_service import InventoryService
from app.models.inventory import DepartmentInventory, InventoryChangeRecord
from app.models.item import Item
from app.models.user import Department, User


class TestInventoryService(BaseTestCase):
    """库存服务测试类"""
    
    def setup_test_data(self):
        """设置测试数据"""
        # 创建部门
        self.dept1 = Department(name="生产部", code="PROD")
        self.dept2 = Department(name="仓库部", code="WARE")
        self.db.add(self.dept1)
        self.db.add(self.dept2)
        
        # 创建用户
        self.user1 = User(username="user1", email="<EMAIL>", hashed_password="hash1")
        self.user2 = User(username="user2", email="<EMAIL>", hashed_password="hash2")
        self.db.add(self.user1)
        self.db.add(self.user2)
        
        # 创建物品
        for i in range(1, 31):
            item = Item(
                name=f"测试物品{i}",
                code=f"ITEM{i:03d}",
                category_id=1,
                brand="测试品牌",
                is_active=True
            )
            self.db.add(item)
        
        self.db.flush()  # 获取自动生成的ID
        self.db.commit()
    
    def test_change_stock_increment(self):
        """测试增加库存"""
        # 先创建库存记录
        inventory = DepartmentInventory(
            department_id=self.dept1.id,
            item_id=1,  # 第一个物品的ID
            current_quantity=Decimal("5.0"),
            min_quantity=Decimal("1.0"),
            is_active=True
        )
        self.db.add(inventory)
        self.db.commit()
        
        # 执行入库操作
        result = InventoryService.change_stock(
            db=self.db,
            department_id=self.dept1.id,
            item_id=1,  # 第一个物品的ID
            change_amount=Decimal("10.5"),
            operator_id=self.user1.id,
            reason="测试入库"
        )
        
        assert result == True
        
        # 验证库存记录
        self.db.refresh(inventory)
        assert inventory.current_quantity == Decimal("15.5")
        
        # 验证变更记录
        change_record = self.db.query(InventoryChangeRecord).filter(
            InventoryChangeRecord.department_id == self.dept1.id,
            InventoryChangeRecord.item_id == 1
        ).first()
        
        assert change_record is not None
        assert change_record.before_quantity == Decimal("5.0")
        assert change_record.after_quantity == Decimal("15.5")
        assert change_record.change_quantity == Decimal("10.5")
        assert change_record.operator_id == self.user1.id
    
    def test_change_stock_decrement(self):
        """测试减少库存"""
        # 先创建一个库存记录
        inventory = DepartmentInventory(
            department_id=self.dept1.id,
            item_id=2,  # 第二个物品的ID
            current_quantity=Decimal("20.0"),
            min_quantity=Decimal("5.0"),
            is_active=True
        )
        self.db.add(inventory)
        self.db.commit()
        
        # 执行出库操作
        result = InventoryService.change_stock(
            db=self.db,
            department_id=self.dept1.id,
            item_id=2,  # 第二个物品的ID
            change_amount=Decimal("-8.0"),
            operator_id=self.user1.id,
            reason="测试出库"
        )
        
        assert result == True
        
        # 验证库存数量已更新
        self.db.refresh(inventory)
        assert inventory.current_quantity == Decimal("12.0")
    
    def test_change_stock_record_not_exists(self):
        """测试库存记录不存在的情况"""
        # 尝试对不存在的库存记录进行操作
        with pytest.raises(ValueError, match="库存记录不存在"):
            InventoryService.change_stock(
                db=self.db,
                department_id=self.dept1.id,
                item_id=999,
                change_amount=Decimal("10.0"),
                operator_id=self.user1.id
            )
    
    def test_adjust_stock_record_not_exists(self):
        """测试调整库存 - 记录不存在"""
        # 尝试调整不存在的库存记录
        with pytest.raises(ValueError, match="库存记录不存在"):
            InventoryService.adjust_stock(
                db=self.db,
                department_id=self.dept2.id,
                item_id=999,
                new_quantity=Decimal("15.0"),
                operator_id=self.user2.id
            )
    
    def test_adjust_stock_atomic_existing_record(self):
        """测试原子化调整库存 - 已存在记录"""
        # 先创建一个库存记录
        inventory = DepartmentInventory(
            department_id=self.dept1.id,
            item_id=6,
            current_quantity=Decimal("10.0"),
            min_quantity=Decimal("2.0"),
            is_active=True
        )
        self.db.add(inventory)
        self.db.commit()
        
        # 执行库存调整
        result = InventoryService.adjust_stock(
            db=self.db,
            department_id=self.dept1.id,
            item_id=6,
            new_quantity=Decimal("25.0"),
            operator_id=self.user1.id,
            reason="盘点调整"
        )
        
        assert result == True
        
        # 验证库存数量已更新
        self.db.refresh(inventory)
        assert inventory.current_quantity == Decimal("25.0")
    
    def test_get_inventory_info(self):
        """测试获取库存信息"""
        # 创建库存记录
        inventory = DepartmentInventory(
            department_id=self.dept1.id,
            item_id=11,
            current_quantity=Decimal("25.5"),
            min_quantity=Decimal("5.0"),
            max_quantity=Decimal("100.0"),
            storage_location="A区-01架",
            rack_number="A01",
            is_active=True
        )
        self.db.add(inventory)
        self.db.commit()
        
        # 获取库存信息
        info = InventoryService.get_inventory_info(self.db, self.dept1.id, 11)
        
        assert info is not None
        assert info['current_quantity'] == 25.5
        assert info['min_quantity'] == 5.0
        assert info['max_quantity'] == 100.0
        assert info['storage_location'] == "A区-01架"
        assert info['rack_number'] == "A01"
    
    def test_get_department_inventories(self):
        """测试获取部门所有库存"""
        # 创建多个库存记录
        for i in range(3):
            inventory = DepartmentInventory(
                department_id=self.dept1.id,
                item_id=20 + i,
                current_quantity=Decimal(f"{10 + i}.0"),
                min_quantity=Decimal("2.0"),
                is_active=True
            )
            self.db.add(inventory)
        self.db.commit()
        
        # 获取部门库存列表
        inventories = InventoryService.get_department_inventories(self.db, self.dept1.id)
        
        assert len(inventories) == 3
        assert inventories[0]['current_quantity'] == 10.0
        assert inventories[1]['current_quantity'] == 11.0
        assert inventories[2]['current_quantity'] == 12.0
    
    def test_concurrent_increment_operations(self):
        """测试并发增加库存操作 - 简化版本，不使用真正的并发"""
        # 创建库存记录
        inventory = DepartmentInventory(
            department_id=self.dept1.id,
            item_id=30,
            current_quantity=Decimal("0.0"),
            min_quantity=Decimal("0.0"),
            is_active=True
        )
        self.db.add(inventory)
        self.db.commit()
        
        # 模拟多次操作（不使用真正的并发，因为SQLite内存数据库的限制）
        for i in range(5):
            result = InventoryService.change_stock(
                db=self.db,
                department_id=self.dept1.id,
                item_id=30,
                change_amount=Decimal("1.0"),
                operator_id=self.user1.id,
                reason=f"模拟并发测试 {i+1}"
            )
            assert result == True
        
        # 验证最终库存数量
        self.db.refresh(inventory)
        assert inventory.current_quantity == Decimal("5.0")
    

    
    def test_inventory_insufficient_stock(self):
        """测试库存不足的情况"""
        # 创建库存记录
        inventory = DepartmentInventory(
            department_id=self.dept1.id,
            item_id=25,
            current_quantity=Decimal("5.0"),
            min_quantity=Decimal("1.0"),
            is_active=True
        )
        self.db.add(inventory)
        self.db.commit()
        
        # 尝试扣减超过库存数量的物品
        with pytest.raises(ValueError, match="库存不足"):
            InventoryService.change_stock(
                db=self.db,
                department_id=self.dept1.id,
                item_id=25,
                change_amount=Decimal("-10.0"),  # 超过现有库存5.0
                operator_id=self.user1.id,
                reason="测试库存不足"
            )
