import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from decimal import Decimal

from app.main import app
from app.core.database import get_db
from app.models.supplier import Supplier, ItemSupplier, SupplierPrice
from app.models.item import Item
from app.models.user import User
from tests.base import BaseTestCase, UserManagementTestCase


class TestSupplierManagement(UserManagementTestCase):
    """供应商管理测试"""

    def test_create_supplier_success(self, client: TestClient):
        """测试成功创建供应商"""
        admin_headers = self.get_auth_headers()
        
        supplier_data = {
            "name_cn": "测试供应商",
            "company_address": "北京市朝阳区",
            "contact_person": "张三",
            "phone": "13800138000",
            "email": "<EMAIL>",
            "rating": 4
        }
        
        response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["name_cn"] == supplier_data["name_cn"]
        assert data["code"].startswith("SUP")
        assert data["rating"] == supplier_data["rating"]
        assert data["status"] == "active"

    def test_create_supplier_duplicate_name(self, client: TestClient):
        """测试创建重复名称的供应商"""
        supplier_data = {
            "name_cn": "重复供应商",
            "company_address": "上海市浦东新区",
            "contact_person": "李四",
            "phone": "13900139000",
            "email": "<EMAIL>",
            "rating": 3
        }
        
        admin_headers = self.get_auth_headers()
        
        # 第一次创建
        response1 = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        assert response1.status_code == 200
        
        # 第二次创建相同名称
        response2 = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        assert response2.status_code == 400

    def test_get_suppliers_list(self, client: TestClient):
        """测试获取供应商列表"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试供应商
        supplier_data = {
            "name": "列表测试供应商",
            "company_address": "广州市天河区",
            "contact_person": "王五",
            "phone": "13700137000",
            "email": "<EMAIL>",
            "rating": 5
        }
        client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        
        response = client.get("/api/admin/suppliers", headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert len(data["items"]) > 0

    def test_get_supplier_by_id(self, client: TestClient):
        """测试根据ID获取供应商"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商
        supplier_data = {
            "name": "详情测试供应商",
            "company_address": "深圳市南山区",
            "contact_person": "赵六",
            "phone": "13600136000",
            "email": "<EMAIL>",
            "rating": 4
        }
        create_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = create_response.json()["id"]
        
        # 获取供应商详情
        response = client.get(f"/api/admin/suppliers/{supplier_id}", headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == supplier_id
        assert data["name"] == supplier_data["name"]

    def test_update_supplier(self, client: TestClient):
        """测试更新供应商"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商
        supplier_data = {
            "name": "更新测试供应商",
            "company_address": "杭州市西湖区",
            "contact_person": "钱七",
            "phone": "13500135000",
            "email": "<EMAIL>",
            "rating": 3
        }
        create_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = create_response.json()["id"]
        
        # 更新供应商
        update_data = {
            "name": "已更新供应商",
            "rating": 5,
            "status": "inactive"
        }
        response = client.put(f"/api/admin/suppliers/{supplier_id}", json=update_data, headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["rating"] == update_data["rating"]
        assert data["status"] == update_data["status"]

    def test_delete_supplier(self, client: TestClient):
        """测试删除供应商"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商
        supplier_data = {
            "name": "删除测试供应商",
            "company_address": "成都市高新区",
            "contact_person": "孙八",
            "phone": "13400134000",
            "email": "<EMAIL>",
            "rating": 2
        }
        create_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = create_response.json()["id"]
        
        # 删除供应商
        response = client.delete(f"/api/admin/suppliers/{supplier_id}", headers=admin_headers)
        assert response.status_code == 204
        
        # 验证已删除
        get_response = client.get(f"/api/admin/suppliers/{supplier_id}", headers=admin_headers)
        assert get_response.status_code == 404

    def test_supplier_search(self, client: TestClient):
        """测试供应商搜索"""
        admin_headers = self.get_auth_headers()
        
        # 创建多个供应商
        suppliers = [
            {"name": "搜索测试供应商A", "contact_person": "搜索A"},
            {"name": "搜索测试供应商B", "contact_person": "搜索B"},
            {"name": "其他供应商", "contact_person": "其他"}
        ]
        
        for supplier_data in suppliers:
            client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        
        # 搜索包含"搜索"的供应商
        response = client.get("/api/admin/suppliers?search=搜索", headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data["items"]) >= 2

    def test_supplier_filtering(self, client: TestClient):
        """测试供应商过滤"""
        admin_headers = self.get_auth_headers()
        
        # 创建不同评级的供应商
        suppliers = [
            {"name": "高评级供应商", "rating": 5},
            {"name": "中评级供应商", "rating": 3},
            {"name": "低评级供应商", "rating": 1}
        ]
        
        for supplier_data in suppliers:
            client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        
        # 过滤评级>=3的供应商
        response = client.get("/api/admin/suppliers?rating_min=3", headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        for supplier in data["items"]:
            assert supplier["rating"] >= 3

    def test_supplier_pagination(self, client: TestClient):
        """测试供应商分页"""
        admin_headers = self.get_auth_headers()
        
        # 创建多个供应商
        for i in range(25):
            supplier_data = {
                "name": f"分页测试供应商{i}",
                "contact_person": f"联系人{i}",
                "rating": 3
            }
            client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        
        # 测试第一页
        response1 = client.get("/api/admin/suppliers?page=1&size=10", headers=admin_headers)
        assert response1.status_code == 200
        data1 = response1.json()
        assert len(data1["items"]) == 10
        assert data1["page"] == 1
        
        # 测试第二页
        response2 = client.get("/api/admin/suppliers?page=2&size=10", headers=admin_headers)
        assert response2.status_code == 200
        data2 = response2.json()
        assert len(data2["items"]) == 10
        assert data2["page"] == 2


class TestItemSupplierManagement(UserManagementTestCase):
    """物品供应商关联管理测试"""

    def test_add_supplier_item(self, client: TestClient):
        """测试添加供应商物品关联"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商和物品
        supplier_data = {"name": "物品关联测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        item_data = {"name": "测试物品", "code": "TEST001", "category_id": 1}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        # 添加物品供应商关联
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1,
            "delivery_days": 7,
            "quality_rating": 4,
            "spq": 100,
            "moq": 10
        }
        
        response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                             json=item_supplier_data, headers=admin_headers)
        assert response.status_code == 201
        
        data = response.json()
        assert data["item_id"] == item_id
        assert data["supplier_id"] == supplier_id
        assert data["priority"] == 1

    def test_get_supplier_items(self, client: TestClient):
        """测试获取供应商的物品列表"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商和物品
        supplier_data = {"name": "物品列表测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        item_data = {"name": "测试物品", "code": "TEST002", "category_id": 1}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        # 添加关联
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                   json=item_supplier_data, headers=admin_headers)
        
        # 获取供应商的物品列表
        response = client.get(f"/api/admin/suppliers/{supplier_id}/items", headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) > 0
        assert data[0]["item_id"] == item_id

    def test_update_item_supplier(self, client: TestClient):
        """测试更新物品供应商关联"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商和物品
        supplier_data = {"name": "更新关联测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        item_data = {"name": "测试物品", "code": "TEST003", "category_id": 1}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        # 添加关联
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1,
            "delivery_days": 7
        }
        create_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                    json=item_supplier_data, headers=admin_headers)
        item_supplier_id = create_response.json()["id"]
        
        # 更新关联
        update_data = {
            "priority": 0,
            "delivery_days": 14,
            "quality_rating": 5
        }
        response = client.put(f"/api/admin/item-suppliers/{item_supplier_id}", 
                            json=update_data, headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["priority"] == 0
        assert data["delivery_days"] == 14
        assert data["quality_rating"] == 5

    def test_delete_item_supplier(self, client: TestClient):
        """测试删除物品供应商关联"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商和物品
        supplier_data = {"name": "删除关联测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        item_data = {"name": "测试物品", "code": "TEST004", "category_id": 1}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        # 添加关联
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        create_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                    json=item_supplier_data, headers=admin_headers)
        item_supplier_id = create_response.json()["id"]
        
        # 删除关联
        response = client.delete(f"/api/admin/item-suppliers/{item_supplier_id}", headers=admin_headers)
        assert response.status_code == 204


class TestSupplierPriceManagement(UserManagementTestCase):
    """供应商价格管理测试"""

    def test_create_supplier_price(self, client: TestClient):
        """测试创建供应商价格"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商、物品和关联
        supplier_data = {"name_cn": "价格测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 创建一级分类
        primary_category_data = {"name": "价格测试一级分类", "code_prefix": "PRICE2"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 创建二级分类
        category_data = {"name": "价格测试分类", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        # 创建物品
        item_data = {"name": "价格测试物品", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        item_supplier_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                           json=item_supplier_data, headers=admin_headers)
        item_supplier_id = item_supplier_response.json()["id"]
        
        # 创建价格
        price_data = {
            "item_supplier_id": item_supplier_id,
            "unit_price": "10.50",
            "min_quantity": 1,
            "max_quantity": 100,
            "valid_from": datetime.now().isoformat(),
            "valid_to": (datetime.now() + timedelta(days=365)).isoformat(),
            "remarks": "测试价格"
        }
        
        response = client.post("/api/admin/supplier-prices", json=price_data, headers=admin_headers)
        assert response.status_code == 201
        
        data = response.json()
        assert data["unit_price"] == "10.50"
        assert data["min_quantity"] == 1
        assert data["max_quantity"] == 100

    def test_get_supplier_prices(self, client: TestClient):
        """测试获取供应商价格列表"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试数据
        supplier_data = {"name_cn": "价格列表测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 创建一级分类
        primary_category_data = {"name": "价格列表测试一级分类", "code_prefix": "PRICE1"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 创建二级分类
        category_data = {"name": "价格列表测试分类", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        # 创建物品
        item_data = {"name": "价格列表测试物品", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        item_supplier_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                           json=item_supplier_data, headers=admin_headers)
        item_supplier_id = item_supplier_response.json()["id"]
        
        # 创建价格
        price_data = {
            "item_supplier_id": item_supplier_id,
            "unit_price": "15.00",
            "min_quantity": 1,
            "valid_from": datetime.now().isoformat()
        }
        client.post("/api/admin/supplier-prices", json=price_data, headers=admin_headers)
        
        # 获取价格列表
        response = client.get(f"/api/admin/supplier-prices?supplier_id={supplier_id}", headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "items" in data
        assert len(data["items"]) > 0
        
        # 验证按更新时间倒序排序
        items = data["items"]
        if len(items) > 1:
            # 检查是否按更新时间倒序排序
            for i in range(len(items) - 1):
                current_updated_at = items[i]["updated_at"]
                next_updated_at = items[i + 1]["updated_at"]
                # 由于是倒序，当前项的更新时间应该大于等于下一项的更新时间
                assert current_updated_at >= next_updated_at, f"排序错误: {current_updated_at} < {next_updated_at}"

    def test_update_supplier_price(self, client: TestClient):
        """测试更新供应商价格"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试数据
        supplier_data = {"name_cn": "价格更新测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 创建一级分类
        primary_category_data = {"name": "价格更新测试一级分类", "code_prefix": "PRICE3"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 创建二级分类
        category_data = {"name": "价格更新测试分类", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        # 创建物品
        item_data = {"name": "价格更新测试物品", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        item_supplier_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                           json=item_supplier_data, headers=admin_headers)
        item_supplier_id = item_supplier_response.json()["id"]
        
        # 创建价格
        price_data = {
            "item_supplier_id": item_supplier_id,
            "unit_price": "20.00",
            "min_quantity": 1,
            "valid_from": datetime.now().isoformat()
        }
        create_response = client.post("/api/admin/supplier-prices", json=price_data, headers=admin_headers)
        price_id = create_response.json()["id"]
        
        # 更新价格
        update_data = {
            "unit_price": "25.00",
            "remarks": "更新后的价格"
        }
        response = client.put(f"/api/admin/supplier-prices/{price_id}", 
                            json=update_data, headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["unit_price"] == "25.00"
        assert data["remarks"] == "更新后的价格"

    def test_delete_supplier_price(self, client: TestClient):
        """测试删除供应商价格"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试数据
        supplier_data = {"name_cn": "价格删除测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 创建一级分类
        primary_category_data = {"name": "价格删除测试一级分类", "code_prefix": "PRICE4"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 创建二级分类
        category_data = {"name": "价格删除测试分类", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        # 创建物品
        item_data = {"name": "价格删除测试物品", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        item_supplier_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                           json=item_supplier_data, headers=admin_headers)
        item_supplier_id = item_supplier_response.json()["id"]
        
        # 创建价格
        price_data = {
            "item_supplier_id": item_supplier_id,
            "unit_price": "30.00",
            "min_quantity": 1,
            "valid_from": datetime.now().isoformat()
        }
        create_response = client.post("/api/admin/supplier-prices", json=price_data, headers=admin_headers)
        price_id = create_response.json()["id"]
        
        # 删除价格
        response = client.delete(f"/api/admin/supplier-prices/{price_id}", headers=admin_headers)
        assert response.status_code == 204

    def test_calculate_supplier_price(self, client: TestClient):
        """测试计算供应商价格"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试数据
        supplier_data = {"name_cn": "价格计算测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 创建一级分类
        primary_category_data = {"name": "价格计算测试一级分类", "code_prefix": "PRICE5"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 创建二级分类
        category_data = {"name": "价格计算测试分类", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        # 创建物品
        item_data = {"name": "价格计算测试物品", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        item_supplier_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                           json=item_supplier_data, headers=admin_headers)
        item_supplier_id = item_supplier_response.json()["id"]
        
        # 创建价格
        price_data = {
            "item_supplier_id": item_supplier_id,
            "unit_price": "12.50",
            "min_quantity": 1,
            "max_quantity": 100,
            "valid_from": datetime.now().isoformat()
        }
        client.post("/api/admin/supplier-prices", json=price_data, headers=admin_headers)
        
        # 计算价格
        response = client.get(f"/api/admin/suppliers/{supplier_id}/items/{item_id}/price?quantity=50", 
                            headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "unit_price" in data
        assert "total_price" in data

    def test_supplier_price_sorting(self, client: TestClient):
        """测试供应商价格列表按更新时间倒序排序"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试数据
        supplier_data = {"name_cn": "价格排序测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 创建一级分类
        primary_category_data = {"name": "价格排序测试一级分类", "code_prefix": "SORT1"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 创建二级分类
        category_data = {"name": "价格排序测试分类", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        # 创建物品
        item_data = {"name": "价格排序测试物品", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        # 创建物品供应商关联
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        item_supplier_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                           json=item_supplier_data, headers=admin_headers)
        item_supplier_id = item_supplier_response.json()["id"]
        
        # 创建多个价格记录，确保有不同的更新时间
        price_records = []
        for i in range(3):
            price_data = {
                "item_supplier_id": item_supplier_id,
                "unit_price": f"{10 + i * 5}.00",
                "min_quantity": 1,
                "valid_from": datetime.now().isoformat()
            }
            response = client.post("/api/admin/supplier-prices", json=price_data, headers=admin_headers)
            price_records.append(response.json())
        
        # 获取价格列表
        response = client.get(f"/api/admin/supplier-prices?supplier_id={supplier_id}", headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "items" in data
        assert len(data["items"]) >= 3
        
        # 验证按更新时间倒序排序
        items = data["items"]
        print(f"获取到 {len(items)} 条价格记录")
        
        # 检查是否按更新时间倒序排序
        for i in range(len(items) - 1):
            current_updated_at = items[i]["updated_at"]
            next_updated_at = items[i + 1]["updated_at"]
            print(f"第{i+1}条: {current_updated_at}")
            print(f"第{i+2}条: {next_updated_at}")
            # 由于是倒序，当前项的更新时间应该大于等于下一项的更新时间
            assert current_updated_at >= next_updated_at, f"排序错误: {current_updated_at} < {next_updated_at}"
        
        print("价格列表按更新时间倒序排序验证通过")


class TestSupplierPermissions(UserManagementTestCase):
    """供应商权限测试"""

    def test_supplier_permissions(self, client: TestClient):
        """测试供应商相关权限"""
        # 测试创建供应商权限
        self.assert_permission_check(admin_headers, "/api/admin/suppliers", "POST", 201)
        
        # 测试查看供应商权限
        self.assert_permission_check(admin_headers, "/api/admin/suppliers", "GET", 200)
        
        # 测试更新供应商权限
        supplier_data = {"name": "权限测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        self.assert_permission_check(admin_headers, f"/api/admin/suppliers/{supplier_id}", "PUT", 200)
        
        # 测试删除供应商权限
        self.assert_permission_check(admin_headers, f"/api/admin/suppliers/{supplier_id}", "DELETE", 204)

    def test_supplier_price_permissions(self, client: TestClient):
        """测试供应商价格权限"""
        # 创建普通用户
        user_data = {
            "username": "price_user",
            "email": "<EMAIL>",
            "password": "testpass123",
            "full_name": "价格用户"
        }
        admin_headers = self.get_auth_headers()
        user_response = client.post("/api/admin/users", json=user_data, headers=admin_headers)
        user_id = user_response.json()["id"]
        
        # 给用户分配价格管理权限
        role_data = {
            "name": "价格管理角色",
            "description": "价格管理权限",
            "permissions": ["supplier.price_manage"]
        }
        role_response = client.post("/api/admin/roles", json=role_data, headers=admin_headers)
        role_id = role_response.json()["id"]
        
        client.post(f"/api/admin/users/{user_id}/roles", json={"role_id": role_id}, headers=admin_headers)
        
        # 测试价格管理权限
        user_headers = self.get_auth_headers("price_user", "testpass123")
        response = client.get("/api/admin/supplier-prices", headers=user_headers)
        assert response.status_code == 200


class TestPriceTrendAnalysis(UserManagementTestCase):
    """价格走势分析测试"""

    def test_get_price_trend_success(self, client: TestClient):
        """测试成功获取价格走势数据"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试数据
        # 1. 创建供应商
        supplier_data = {
            "name_cn": "价格走势测试供应商",
            "code": "SUP000001",
            "company_address": "测试地址",
            "contact_person": "测试联系人",
            "phone": "13800138000",
            "email": "<EMAIL>",
            "rating": 4,
            "status": "active"
        }
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        assert supplier_response.status_code == 200, f"供应商创建失败: {supplier_response.text}"
        supplier_id = supplier_response.json()["id"]
        
        # 2. 创建一级分类
        primary_category_data = {
            "name": "测试一级分类",
            "description": "测试一级分类描述",
            "code_prefix": "TEST",
            "code_format": "0000",
            "current_sequence": 1,
            "is_active": True
        }
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        assert primary_category_response.status_code == 200, f"一级分类创建失败: {primary_category_response.text}"
        primary_category_id = primary_category_response.json()["id"]
        
        # 3. 创建二级分类
        category_data = {
            "name": "测试二级分类",
            "description": "测试二级分类描述",
            "primary_category_id": primary_category_id,
            "is_active": True
        }
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        assert category_response.status_code == 200, f"二级分类创建失败: {category_response.text}"
        category_id = category_response.json()["id"]
        
        # 4. 创建物品
        item_data = {
            "name": "价格走势测试物品",
            "code": "ITEM000001",
            "description": "测试物品描述",
            "category_id": category_id,
            "unit": "个",
            "spec_material": "测试材质",
            "size_dimension": "10x10x10",
            "brand": "测试品牌"
        }
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        assert item_response.status_code == 200, f"物品创建失败: {item_response.text}"
        item_id = item_response.json()["id"]
        
        # 5. 创建物品供应商关联
        item_supplier_data = {
            "item_id": item_id,
            "priority": 1,
            "status": "active",
            "delivery_days": 7,
            "quality_rating": 4,
            "spq": 1,
            "moq": 1
        }
        item_supplier_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", json=item_supplier_data, headers=admin_headers)
        assert item_supplier_response.status_code == 200, f"物品供应商关联创建失败: {item_supplier_response.text}"
        item_supplier_id = item_supplier_response.json()["id"]
        
        # 6. 创建价格记录
        from datetime import datetime, timedelta
        today = datetime.now().date()
        
        price_data = {
            "item_supplier_id": item_supplier_id,
            "unit_price": "10.50",
            "min_quantity": 1,
            "max_quantity": 100,
            "valid_from": today.isoformat(),
            "valid_to": (today + timedelta(days=30)).isoformat(),
            "status": "active",
            "remarks": "测试价格"
        }
        price_response = client.post("/api/admin/supplier-prices", json=price_data, headers=admin_headers)
        assert price_response.status_code == 200, f"价格创建失败: {price_response.text}"
        
        # 7. 测试价格走势API
        response = client.get(f"/api/admin/item-suppliers/{item_supplier_id}/price-trend", headers=admin_headers)
        assert response.status_code == 200, f"价格走势API失败: {response.text}"
        
        data = response.json()
        assert data["item_supplier_id"] == item_supplier_id
        assert data["item_name"] == "价格走势测试物品"
        assert data["supplier_name"] == "价格走势测试供应商"
        assert "price_trends" in data
        assert "date_range" in data
        assert len(data["price_trends"]) > 0
        
        # 验证价格趋势数据
        price_trend = data["price_trends"][0]
        assert price_trend["tier_id"] == 1
        assert price_trend["min_quantity"] == 1
        assert price_trend["max_quantity"] == 100
        assert price_trend["tier_name"] == "1-100"
        assert len(price_trend["price_points"]) > 0

    def test_get_price_trend_no_data(self, client: TestClient):
        """测试没有价格数据时的价格走势"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试数据（不创建价格记录）
        supplier_data = {
            "name_cn": "无价格测试供应商",
            "code": "SUP000002",
            "company_address": "测试地址",
            "contact_person": "测试联系人",
            "phone": "13800138001",
            "email": "<EMAIL>",
            "rating": 4,
            "status": "active"
        }
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        assert supplier_response.status_code == 200
        supplier_id = supplier_response.json()["id"]
        
        # 创建分类
        primary_category_data = {
            "name": "无价格测试一级分类",
            "description": "测试一级分类描述",
            "code_prefix": "NOPRICE",
            "code_format": "0000",
            "current_sequence": 1,
            "is_active": True
        }
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        assert primary_category_response.status_code == 200
        primary_category_id = primary_category_response.json()["id"]
        
        category_data = {
            "name": "无价格测试二级分类",
            "description": "测试二级分类描述",
            "primary_category_id": primary_category_id,
            "is_active": True
        }
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        assert category_response.status_code == 200
        category_id = category_response.json()["id"]
        
        item_data = {
            "name": "无价格测试物品",
            "code": "ITEM000002",
            "description": "测试物品描述",
            "category_id": category_id,
            "unit": "个",
            "spec_material": "测试材质",
            "size_dimension": "10x10x10",
            "brand": "测试品牌"
        }
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        assert item_response.status_code == 200
        item_id = item_response.json()["id"]
        
        item_supplier_data = {
            "item_id": item_id,
            "priority": 1,
            "status": "active",
            "delivery_days": 7,
            "quality_rating": 4,
            "spq": 1,
            "moq": 1
        }
        item_supplier_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", json=item_supplier_data, headers=admin_headers)
        assert item_supplier_response.status_code == 200
        item_supplier_id = item_supplier_response.json()["id"]
        
        # 测试价格走势API（应该返回404）
        response = client.get(f"/api/admin/item-suppliers/{item_supplier_id}/price-trend", headers=admin_headers)
        assert response.status_code == 404

    def test_get_price_trend_invalid_date_range(self, client: TestClient):
        """测试无效日期范围的价格走势"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试数据
        supplier_data = {
            "name_cn": "日期测试供应商",
            "code": "SUP000003",
            "company_address": "测试地址",
            "contact_person": "测试联系人",
            "phone": "13800138002",
            "email": "<EMAIL>",
            "rating": 4,
            "status": "active"
        }
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        assert supplier_response.status_code == 200
        supplier_id = supplier_response.json()["id"]
        
        # 创建分类
        primary_category_data = {
            "name": "日期测试一级分类",
            "description": "测试一级分类描述",
            "code_prefix": "DATE",
            "code_format": "0000",
            "current_sequence": 1,
            "is_active": True
        }
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        assert primary_category_response.status_code == 200
        primary_category_id = primary_category_response.json()["id"]
        
        category_data = {
            "name": "日期测试二级分类",
            "description": "测试二级分类描述",
            "primary_category_id": primary_category_id,
            "is_active": True
        }
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        assert category_response.status_code == 200
        category_id = category_response.json()["id"]
        
        item_data = {
            "name": "日期测试物品",
            "code": "ITEM000003",
            "description": "测试物品描述",
            "category_id": category_id,
            "unit": "个",
            "spec_material": "测试材质",
            "size_dimension": "10x10x10",
            "brand": "测试品牌"
        }
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        assert item_response.status_code == 200
        item_id = item_response.json()["id"]
        
        item_supplier_data = {
            "item_id": item_id,
            "priority": 1,
            "status": "active",
            "delivery_days": 7,
            "quality_rating": 4,
            "spq": 1,
            "moq": 1
        }
        item_supplier_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", json=item_supplier_data, headers=admin_headers)
        assert item_supplier_response.status_code == 200
        item_supplier_id = item_supplier_response.json()["id"]
        
        # 测试无效日期格式
        response = client.get(
            f"/api/admin/item-suppliers/{item_supplier_id}/price-trend?start_date=invalid-date",
            headers=admin_headers
        )
        assert response.status_code == 400
        
        # 测试日期范围超过365天
        from datetime import datetime, timedelta
        start_date = datetime.now().date() - timedelta(days=200)
        end_date = datetime.now().date() + timedelta(days=200)
        
        response = client.get(
            f"/api/admin/item-suppliers/{item_supplier_id}/price-trend?start_date={start_date}&end_date={end_date}",
            headers=admin_headers
        )
        assert response.status_code == 400

    def test_get_price_trend_not_found(self, client: TestClient):
        """测试不存在的物品供应商关联"""
        admin_headers = self.get_auth_headers()
        
        # 测试不存在的item_supplier_id
        response = client.get("/api/admin/item-suppliers/99999/price-trend", headers=admin_headers)
        assert response.status_code == 404 