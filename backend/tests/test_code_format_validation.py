"""
编码格式验证测试
测试新的编码格式标准是否符合要求
"""

import pytest
from app.services.item_code_service import ItemCodeService
from app.services.employee_code_service import EmployeeCodeService
from app.services.department_code_service import DepartmentCodeService
from app.services.qr_code_service import QRCodeService


class TestCodeFormatValidation:
    """编码格式验证测试"""
    
    def test_item_code_format_validation(self):
        """测试物品编码格式验证"""
        # 有效的物品编码
        assert ItemCodeService.validate_code_format("IDM001", None) == True
        assert ItemCodeService.validate_code_format("IDM12345", None) == True
        assert ItemCodeService.validate_code_format("IDM001-001", None) == True
        assert ItemCodeService.validate_code_format("IDM001-T1234", None) == True
        
        # 无效的物品编码
        assert ItemCodeService.validate_code_format("", None) == False
        assert ItemCodeService.validate_code_format("ITM001", None) == False  # 旧格式
        assert ItemCodeService.validate_code_format("M001", None) == False   # 旧格式
        assert ItemCodeService.validate_code_format("IDM", None) == False    # 缺少数字部分
        assert ItemCodeService.validate_code_format("IDMABC", None) == False # 非数字部分
    
    def test_employee_code_format_validation(self):
        """测试员工编码格式验证"""
        # 有效的员工编码
        assert EmployeeCodeService.validate_employee_code("EMP001") == True
        assert EmployeeCodeService.validate_employee_code("EMP002") == True
        assert EmployeeCodeService.validate_employee_code("EMP123456") == True
        
        # 无效的员工编码
        assert EmployeeCodeService.validate_employee_code("") == False
        assert EmployeeCodeService.validate_employee_code("EMP") == False
        assert EmployeeCodeService.validate_employee_code("EMPABC") == False
        assert EmployeeCodeService.validate_employee_code("001") == False
    
    def test_qr_code_type_detection(self):
        """测试二维码类型自动识别"""
        # 物品类型识别
        assert QRCodeService.detect_qr_code_type("IDM001") == "i"
        assert QRCodeService.detect_qr_code_type("IDM12345") == "i"
        
        # 员工类型识别
        assert QRCodeService.detect_qr_code_type("EMP001") == "c"
        assert QRCodeService.detect_qr_code_type("EMP123456") == "c"
        
        # 部门类型识别
        assert QRCodeService.detect_qr_code_type("DEPT001") == "d"
        assert QRCodeService.detect_qr_code_type("DEPT002") == "d"
        assert QRCodeService.detect_qr_code_type("IT") == "d"
        assert QRCodeService.detect_qr_code_type("HR") == "d"
        assert QRCodeService.detect_qr_code_type("PUR") == "d"
        assert QRCodeService.detect_qr_code_type("WH") == "d"
        assert QRCodeService.detect_qr_code_type("ME") == "d"
        assert QRCodeService.detect_qr_code_type("SI") == "d"
        assert QRCodeService.detect_qr_code_type("AD") == "d"
        assert QRCodeService.detect_qr_code_type("QA") == "d"
        
        # 供应商类型识别
        assert QRCodeService.detect_qr_code_type("SUP001") == "s"
        assert QRCodeService.detect_qr_code_type("SUP123456") == "s"
        
        # 采购申请类型识别
        assert QRCodeService.detect_qr_code_type("PR001") == "pr"
        assert QRCodeService.detect_qr_code_type("PR20241201123456001") == "pr"
        
        # 无效编码识别
        assert QRCodeService.detect_qr_code_type("") == None
        assert QRCodeService.detect_qr_code_type("ABC123") == None
        assert QRCodeService.detect_qr_code_type("123456") == None
    
    def test_qr_code_parsing(self):
        """测试二维码内容解析"""
        # 物品二维码解析
        item_data = QRCodeService.parse_qr_code("IDM001")
        assert item_data["type"] == "i"
        assert item_data["code"] == "IDM001"
        assert item_data["version"] == "2.0"
        
        # 员工二维码解析
        employee_data = QRCodeService.parse_qr_code("EMP001")
        assert employee_data["type"] == "c"
        assert employee_data["code"] == "EMP001"
        assert employee_data["version"] == "2.0"
        
        # 部门二维码解析
        dept_data = QRCodeService.parse_qr_code("DEPT001")
        assert dept_data["type"] == "d"
        assert dept_data["code"] == "DEPT001"
        assert dept_data["version"] == "2.0"
        
        # 测试其他部门编码格式
        dept_data2 = QRCodeService.parse_qr_code("IT")
        assert dept_data2["type"] == "d"
        assert dept_data2["code"] == "IT"
        assert dept_data2["version"] == "2.0"
        
        # 供应商二维码解析
        supplier_data = QRCodeService.parse_qr_code("SUP001")
        assert supplier_data["type"] == "s"
        assert supplier_data["code"] == "SUP001"
        assert supplier_data["version"] == "2.0"
        
        # 采购申请二维码解析
        request_data = QRCodeService.parse_qr_code("PR20241201123456001")
        assert request_data["type"] == "pr"
        assert request_data["code"] == "PR20241201123456001"
        assert request_data["version"] == "2.0"
    
    def test_invalid_qr_code_parsing(self):
        """测试无效二维码内容解析"""
        # 空内容
        with pytest.raises(ValueError, match="二维码内容为空"):
            QRCodeService.parse_qr_code("")
        
        # 无法识别的格式
        with pytest.raises(ValueError, match="无法识别的编码格式"):
            QRCodeService.parse_qr_code("ABC123")
        
        # 空字符串
        with pytest.raises(ValueError, match="二维码内容为空"):
            QRCodeService.parse_qr_code("   ")
