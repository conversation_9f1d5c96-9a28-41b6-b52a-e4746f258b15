"""
测试基类模块
提供不同测试场景的基类，使用faker生成测试数据
"""

import pytest
from typing import Dict, List, Optional
from faker import Faker
from sqlalchemy.orm import Session
from fastapi.testclient import TestClient

from app.models.user import User
from app.models.permission import Permission, Role
from app.models.user import Department
from app.services.auth import get_password_hash

# 创建faker实例，使用中文
fake = Faker(['zh_CN'])


class BaseTestCase:
    """基础测试类"""
    
    @pytest.fixture(autouse=True)
    def setup(self, db_session: Session, client: TestClient):
        """自动设置测试环境"""
        self.db = db_session
        self.client = client
        self.fake = fake
        self.setup_test_data()
    
    def setup_test_data(self):
        """设置测试数据，子类可以重写"""
        pass
    
    def create_user_data(self, **kwargs) -> Dict:
        """创建用户数据"""
        return {
            "username": kwargs.get("username", fake.user_name()),
            "email": kwargs.get("email", fake.email()),
            "password": kwargs.get("password", "Test123456"),
            "full_name": kwargs.get("full_name", fake.name()),
            "department_id": kwargs.get("department_id"),
            "role_id": kwargs.get("role_id"),
            "is_superuser": kwargs.get("is_superuser", False),
            "is_active": kwargs.get("is_active", True)
        }
    
    def create_department_data(self, **kwargs) -> Dict:
        """创建部门数据"""
        return {
            "name": kwargs.get("name", fake.company()),
            "code": kwargs.get("code", fake.pystr(min_chars=3, max_chars=6).upper()),
            "description": kwargs.get("description", fake.text(max_nb_chars=100))
        }
    
    def create_permission_data(self, **kwargs) -> Dict:
        """创建权限数据"""
        return {
            "code": kwargs.get("code", f"{fake.word()}.{fake.word()}"),
            "name": kwargs.get("name", fake.word()),
            "description": kwargs.get("description", fake.text(max_nb_chars=100)),
            "module": kwargs.get("module", fake.word())
        }
    
    def create_role_data(self, **kwargs) -> Dict:
        """创建角色数据"""
        return {
            "name": kwargs.get("name", fake.job()),
            "code": kwargs.get("code", f"ROLE_{fake.pystr(min_chars=3, max_chars=6).upper()}"),
            "description": kwargs.get("description", fake.text(max_nb_chars=100))
        }
    
    def create_test_user(self, **kwargs) -> User:
        """创建测试用户"""
        user_data = self.create_user_data(**kwargs)
        
        user = User(
            username=user_data["username"],
            email=user_data["email"],
            hashed_password=get_password_hash(user_data["password"]),
            full_name=user_data["full_name"],
            department_id=user_data["department_id"],
            role_id=user_data["role_id"],
            is_superuser=user_data["is_superuser"],
            is_active=user_data["is_active"]
        )
        
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        return user
    
    def create_test_department(self, **kwargs) -> Department:
        """创建测试部门"""
        dept_data = self.create_department_data(**kwargs)
        
        department = Department(
            name=dept_data["name"],
            code=dept_data["code"],
            description=dept_data["description"]
        )
        
        self.db.add(department)
        self.db.commit()
        self.db.refresh(department)
        return department
    
    def create_test_permission(self, **kwargs) -> Permission:
        """创建测试权限"""
        perm_data = self.create_permission_data(**kwargs)
        
        permission = Permission(
            code=perm_data["code"],
            name=perm_data["name"],
            description=perm_data["description"],
            module=perm_data["module"]
        )
        
        self.db.add(permission)
        self.db.commit()
        self.db.refresh(permission)
        return permission
    
    def create_test_role(self, **kwargs) -> Role:
        """创建测试角色"""
        role_data = self.create_role_data(**kwargs)
        
        role = Role(
            name=role_data["name"],
            code=role_data["code"],
            description=role_data["description"]
        )
        
        # 分配权限
        permissions = kwargs.get("permissions", [])
        if permissions:
            for perm_code in permissions:
                perm = self.db.query(Permission).filter(Permission.code == perm_code).first()
                if perm:
                    role.permissions.append(perm)
        
        self.db.add(role)
        self.db.commit()
        self.db.refresh(role)
        return role
    
    def get_auth_headers(self, username: str = "admin", password: str = "admin123") -> Dict[str, str]:
        """获取认证头"""
        response = self.client.post("/api/admin/auth/login", json={
            "username": username,
            "password": password
        })
        
        if response.status_code == 200:
            token = response.json()["access_token"]
            return {"Authorization": f"Bearer {token}"}
        else:
            raise Exception(f"登录失败: {response.status_code}")
    
    def assert_user_data(self, user_data: Dict, expected_data: Dict):
        """断言用户数据"""
        for key, value in expected_data.items():
            if key != "password":  # 密码不应该在响应中
                assert user_data.get(key) == value, f"用户数据不匹配: {key}"
    
    def assert_permission_check(self, headers: Dict[str, str], endpoint: str, method: str = "GET", expected_status: int = 200, body: dict = None):
        """断言权限检查"""
        if method.upper() == "GET":
            response = self.client.get(endpoint, headers=headers)
        elif method.upper() == "POST":
            response = self.client.post(endpoint, headers=headers, json=body or {})
        elif method.upper() == "PUT":
            response = self.client.put(endpoint, headers=headers, json=body or {})
        elif method.upper() == "DELETE":
            response = self.client.delete(endpoint, headers=headers)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
        assert response.status_code == expected_status, f"权限检查失败: {endpoint} 返回 {response.status_code}"


class AuthTestCase(BaseTestCase):
    """认证测试基类"""
    
    def setup_test_data(self):
        """设置认证测试数据"""
        # 创建基础用户
        self.admin_user = self.create_test_user(
            username="admin",
            email="<EMAIL>",
            password="admin123",
            full_name="系统管理员",
            is_superuser=True,
            is_active=True
        )
        
        self.regular_user = self.create_test_user(
            username="user",
            email="<EMAIL>",
            password="user123",
            full_name="普通用户",
            is_superuser=False,
            is_active=True
        )


class UserManagementTestCase(BaseTestCase):
    """用户管理测试基类"""
    
    def setup_test_data(self):
        """设置用户管理测试数据"""
        # 创建部门
        self.tech_dept = self.create_test_department(
            name="技术部",
            code="TECH",
            description="技术研发部门"
        )
        
        self.hr_dept = self.create_test_department(
            name="人事部", 
            code="HR",
            description="人力资源部门"
        )
        
        # 创建角色
        self.admin_role = self.create_test_role(
            name="管理员",
            code="ADMIN",
            description="系统管理员"
        )
        
        self.user_role = self.create_test_role(
            name="普通用户",
            code="USER", 
            description="普通用户"
        )
        
        # 创建管理员用户
        self.admin_user = self.create_test_user(
            username="admin",
            email="<EMAIL>",
            password="admin123",
            full_name="系统管理员",
            department_id=self.tech_dept.id,
            role_id=self.admin_role.id,
            is_superuser=True,
            is_active=True
        )


class PermissionTestCase(BaseTestCase):
    """权限测试基类"""
    
    def setup_test_data(self):
        """设置权限测试数据"""
        # 创建权限
        self.permissions = []
        permission_codes = [
            "user.read", "user.create", "user.update", "user.delete",
            "department.read", "department.create", "department.update", "department.delete",
            "role.read", "role.create", "role.update", "role.delete"
        ]
        
        for code in permission_codes:
            perm = self.create_test_permission(
                code=code,
                name=f"权限_{code}",
                description=f"{code}的描述",
                module="test"
            )
            self.permissions.append(perm)
        
        # 创建角色
        self.admin_role = self.create_test_role(
            name="管理员",
            code="ADMIN",
            description="系统管理员",
            permissions=permission_codes
        )
        
        self.user_role = self.create_test_role(
            name="普通用户",
            code="USER",
            description="普通用户", 
            permissions=["user.read", "department.read"]
        )
        
        # 创建部门
        self.tech_dept = self.create_test_department(
            name="技术部",
            code="TECH",
            description="技术研发部门"
        )
        
        # 创建用户
        self.admin_user = self.create_test_user(
            username="admin",
            email="<EMAIL>",
            password="admin123",
            full_name="系统管理员",
            department_id=self.tech_dept.id,
            role_id=self.admin_role.id,
            is_superuser=True,
            is_active=True
        )
        
        self.regular_user = self.create_test_user(
            username="user",
            email="<EMAIL>", 
            password="user123",
            full_name="普通用户",
            department_id=self.tech_dept.id,
            role_id=self.user_role.id,
            is_superuser=False,
            is_active=True
        )


class IntegrationTestCase(BaseTestCase):
    """集成测试基类"""
    
    def setup_test_data(self):
        """设置集成测试数据"""
        # 创建完整的测试数据集
        # 部门
        self.departments = []
        for i in range(3):
            dept = self.create_test_department(
                name=f"{fake.company()}部门",
                code=f"DEPT{i+1:03d}",
                description=fake.text(max_nb_chars=100)
            )
            self.departments.append(dept)
        
        # 权限
        self.permissions = []
        permission_codes = [
            "user.read", "user.create", "user.update", "user.delete",
            "department.read", "department.create", "department.update", "department.delete", 
            "role.read", "role.create", "role.update", "role.delete",
            "permission.read", "permission.create", "permission.update", "permission.delete"
        ]
        
        for code in permission_codes:
            perm = self.create_test_permission(
                code=code,
                name=f"权限_{code}",
                description=f"{code}的描述",
                module="test"
            )
            self.permissions.append(perm)
        
        # 角色
        self.admin_role = self.create_test_role(
            name="系统管理员",
            code="ADMIN",
            description="拥有所有权限的系统管理员",
            permissions=permission_codes
        )
        
        self.manager_role = self.create_test_role(
            name="部门经理",
            code="MANAGER",
            description="部门经理角色",
            permissions=["user.read", "user.create", "department.read", "department.update"]
        )
        
        self.user_role = self.create_test_role(
            name="普通用户",
            code="USER",
            description="普通用户角色",
            permissions=["user.read", "department.read"]
        )
        
        # 用户
        self.admin_user = self.create_test_user(
            username="admin",
            email="<EMAIL>",
            password="admin123",
            full_name="系统管理员",
            department_id=self.departments[0].id,
            role_id=self.admin_role.id,
            is_superuser=True,
            is_active=True
        )
        
        self.manager_user = self.create_test_user(
            username="manager",
            email="<EMAIL>",
            password="manager123",
            full_name="部门经理",
            department_id=self.departments[1].id,
            role_id=self.manager_role.id,
            is_superuser=False,
            is_active=True
        )
        
        self.regular_user = self.create_test_user(
            username="user",
            email="<EMAIL>",
            password="user123", 
            full_name="普通用户",
            department_id=self.departments[2].id,
            role_id=self.user_role.id,
            is_superuser=False,
            is_active=True
        )
        
        # 创建一些额外的测试用户
        self.test_users = []
        for i in range(5):
            user = self.create_test_user(
                username=f"testuser{i+1}",
                email=f"testuser{i+1}@example.com",
                password="test123",
                full_name=fake.name(),
                department_id=self.departments[i % len(self.departments)].id,
                role_id=self.user_role.id,
                is_superuser=False,
                is_active=True
            )
            self.test_users.append(user) 