import pytest
from decimal import Decimal
from datetime import date
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.core.database import get_db
from app.services.exchange_rate_service import ExchangeRateService
from app.schemas.exchange_rate import ExchangeRateCreate, ExchangeRateUpdate
from tests.base import BaseTestCase


class TestExchangeRateAPI(BaseTestCase):
    """汇率管理API测试类"""
    
    def setup_test_data(self):
        """设置测试数据"""
        # 创建测试部门
        self.department = self.create_test_department()
        
        # 创建测试用户
        self.user = self.create_test_user(department_id=self.department.id)
        
        # 创建汇率服务实例
        self.service = ExchangeRateService(self.db)
        
        # 创建测试客户端
        self.client = TestClient(app)
        
        # 模拟用户登录
        self.login_user()
    
    def login_user(self):
        """模拟用户登录"""
        # 这里应该实现实际的登录逻辑
        # 为了测试，我们直接设置用户信息
        pass
    
    def test_create_exchange_rate_api(self):
        """测试创建汇率API"""
        # 准备测试数据
        exchange_rate_data = {
            "currency_code": "CNY",
            "rate": 7.200000,
            "effective_month": "2024-01-01",
            "status": "active"
        }
        
        # 发送POST请求
        response = self.client.post(
            "/admin/exchange-rates",
            json=exchange_rate_data,
            headers={"Authorization": f"Bearer test_token_{self.user.id}"}
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["currency_code"] == "CNY"
        assert data["rate"] == 7.200000
        assert data["effective_month"] == "2024-01-01"
        assert data["status"] == "active"
    
    def test_get_exchange_rates_api(self):
        """测试获取汇率列表API"""
        # 先创建一些测试数据
        test_rates = [
            ("CNY", 7.200000, "2024-01-01"),
            ("EUR", 0.920000, "2024-01-01"),
            ("JPY", 148.500000, "2024-01-01"),
        ]
        
        for currency_code, rate, effective_month in test_rates:
            exchange_rate_data = ExchangeRateCreate(
                currency_code=currency_code,
                rate=Decimal(str(rate)),
                effective_month=date.fromisoformat(effective_month),
                status="active"
            )
            self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 发送GET请求
        response = self.client.get(
            "/admin/exchange-rates",
            headers={"Authorization": f"Bearer test_token_{self.user.id}"}
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 3
        assert len(data["data"]) == 3
    
    def test_get_exchange_rate_detail_api(self):
        """测试获取汇率详情API"""
        # 先创建一个汇率记录
        exchange_rate_data = ExchangeRateCreate(
            currency_code="CNY",
            rate=Decimal("7.200000"),
            effective_month=date(2024, 1, 1),
            status="active"
        )
        created_rate = self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 发送GET请求
        response = self.client.get(
            f"/admin/exchange-rates/{created_rate.id}",
            headers={"Authorization": f"Bearer test_token_{self.user.id}"}
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_rate.id
        assert data["currency_code"] == "CNY"
        assert data["rate"] == 7.200000
    
    def test_update_exchange_rate_api(self):
        """测试更新汇率API"""
        # 先创建一个汇率记录
        exchange_rate_data = ExchangeRateCreate(
            currency_code="CNY",
            rate=Decimal("7.200000"),
            effective_month=date(2024, 1, 1),
            status="active"
        )
        created_rate = self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 准备更新数据
        update_data = {
            "rate": 7.300000,
            "change_reason": "汇率调整"
        }
        
        # 发送PUT请求
        response = self.client.put(
            f"/admin/exchange-rates/{created_rate.id}",
            json=update_data,
            headers={"Authorization": f"Bearer test_token_{self.user.id}"}
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["rate"] == 7.300000
    
    def test_get_exchange_rate_history_api(self):
        """测试获取汇率历史API"""
        # 先创建一些测试数据
        test_rates = [
            ("CNY", 7.200000, "2024-01-01"),
            ("CNY", 7.300000, "2024-02-01"),
            ("CNY", 7.400000, "2024-03-01"),
        ]
        
        for currency_code, rate, effective_month in test_rates:
            exchange_rate_data = ExchangeRateCreate(
                currency_code=currency_code,
                rate=Decimal(str(rate)),
                effective_month=date.fromisoformat(effective_month),
                status="active"
            )
            self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 发送GET请求
        response = self.client.get(
            "/admin/exchange-rates/history?currency_code=CNY",
            headers={"Authorization": f"Bearer test_token_{self.user.id}"}
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 3
    
    def test_get_exchange_rate_summary_api(self):
        """测试获取汇率汇总API"""
        # 先创建一些测试数据
        test_rates = [
            ("CNY", 7.200000, "2024-01-01"),
            ("EUR", 0.920000, "2024-01-01"),
        ]
        
        for currency_code, rate, effective_month in test_rates:
            exchange_rate_data = ExchangeRateCreate(
                currency_code=currency_code,
                rate=Decimal(str(rate)),
                effective_month=date.fromisoformat(effective_month),
                status="active"
            )
            self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 发送GET请求
        response = self.client.get(
            "/admin/exchange-rates/summary",
            headers={"Authorization": f"Bearer test_token_{self.user.id}"}
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
    
    def test_get_supported_currencies_api(self):
        """测试获取支持货币列表API"""
        # 发送GET请求
        response = self.client.get("/admin/exchange-rates/currencies")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert len(data) > 0
        
        # 验证USD是基础货币
        usd_currency = next((c for c in data if c["code"] == "USD"), None)
        assert usd_currency is not None
        assert usd_currency["is_base"] is True
    
    def test_convert_currency_api(self):
        """测试货币转换API"""
        # 先创建一个汇率记录
        exchange_rate_data = ExchangeRateCreate(
            currency_code="CNY",
            rate=Decimal("7.200000"),
            effective_month=date(2024, 1, 1),
            status="active"
        )
        self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 发送GET请求
        response = self.client.get(
            "/admin/exchange-rates/convert/CNY?amount=100",
            headers={"Authorization": f"Bearer test_token_{self.user.id}"}
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["original_amount"] == 100
        assert data["original_currency"] == "CNY"
        assert data["usd_amount"] > 0
    
    def test_validate_exchange_rate_api(self):
        """测试汇率有效性验证API"""
        # 先创建一个汇率记录
        exchange_rate_data = ExchangeRateCreate(
            currency_code="CNY",
            rate=Decimal("7.200000"),
            effective_month=date(2024, 1, 1),
            status="active"
        )
        self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 发送GET请求
        response = self.client.get(
            "/admin/exchange-rates/validate/CNY",
            headers={"Authorization": f"Bearer test_token_{self.user.id}"}
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["currency_code"] == "CNY"
        assert data["is_valid"] is True
        assert data["current_rate"] == 7.200000
    
    def test_create_exchange_rate_duplicate_month(self):
        """测试创建重复月份汇率的错误处理"""
        # 先创建一个汇率记录
        exchange_rate_data = ExchangeRateCreate(
            currency_code="CNY",
            rate=Decimal("7.200000"),
            effective_month=date(2024, 1, 1),
            status="active"
        )
        self.service.create_exchange_rate(exchange_rate_data, self.user.id)
        
        # 尝试创建同一货币同一月份的汇率记录
        duplicate_data = {
            "currency_code": "CNY",
            "rate": 7.300000,
            "effective_month": "2024-01-01",
            "status": "active"
        }
        
        # 发送POST请求
        response = self.client.post(
            "/admin/exchange-rates",
            json=duplicate_data,
            headers={"Authorization": f"Bearer test_token_{self.user.id}"}
        )
        
        # 应该返回错误
        assert response.status_code == 400
    
    def test_get_exchange_rate_not_found(self):
        """测试获取不存在汇率的错误处理"""
        # 发送GET请求
        response = self.client.get(
            "/admin/exchange-rates/999",
            headers={"Authorization": f"Bearer test_token_{self.user.id}"}
        )
        
        # 应该返回404
        assert response.status_code == 404
    
    def test_update_exchange_rate_not_found(self):
        """测试更新不存在汇率的错误处理"""
        # 准备更新数据
        update_data = {
            "rate": 7.300000,
            "change_reason": "汇率调整"
        }
        
        # 发送PUT请求
        response = self.client.put(
            "/admin/exchange-rates/999",
            json=update_data,
            headers={"Authorization": f"Bearer test_token_{self.user.id}"}
        )
        
        # 应该返回404
        assert response.status_code == 404


class TestExchangeRateUtils(BaseTestCase):
    """汇率工具函数测试类"""
    
    def setup_test_data(self):
        """设置测试数据"""
        from app.utils.exchange_rate_utils import (
            convert_price_to_usd,
            convert_usd_to_currency,
            format_price_display,
            validate_exchange_rate_data
        )
        
        self.convert_price_to_usd = convert_price_to_usd
        self.convert_usd_to_currency = convert_usd_to_currency
        self.format_price_display = format_price_display
        self.validate_exchange_rate_data = validate_exchange_rate_data
    
    def test_convert_price_to_usd(self):
        """测试价格转换为美元"""
        from decimal import Decimal
        
        # 测试CNY转换
        usd_price = self.convert_price_to_usd(Decimal("100"), "CNY", Decimal("7.200000"))
        expected_usd = Decimal("100") / Decimal("7.200000")
        assert usd_price == expected_usd.quantize(Decimal('0.01'))
        
        # 测试USD（应该直接返回）
        usd_price = self.convert_price_to_usd(Decimal("100"), "USD", Decimal("1.000000"))
        assert usd_price == Decimal("100")
    
    def test_convert_usd_to_currency(self):
        """测试美元转换为指定货币"""
        from decimal import Decimal
        
        # 测试转换为CNY
        cny_price = self.convert_usd_to_currency(Decimal("10"), "CNY", Decimal("7.200000"))
        expected_cny = Decimal("10") * Decimal("7.200000")
        assert cny_price == expected_cny.quantize(Decimal('0.01'))
        
        # 测试转换为USD（应该直接返回）
        usd_price = self.convert_usd_to_currency(Decimal("100"), "USD", Decimal("1.000000"))
        assert usd_price == Decimal("100")
    
    def test_format_price_display(self):
        """测试价格显示格式化"""
        from decimal import Decimal
        
        # 测试大于1的价格
        formatted = self.format_price_display(Decimal("123.456"), "USD")
        assert formatted == "$123.46"
        
        # 测试小于1的价格
        formatted = self.format_price_display(Decimal("0.123456"), "USD")
        assert formatted == "$0.1235"
        
        # 测试其他货币
        formatted = self.format_price_display(Decimal("100"), "CNY")
        assert formatted == "¥100.00"
    
    def test_validate_exchange_rate_data(self):
        """测试汇率数据验证"""
        from datetime import date
        
        # 测试有效数据
        is_valid, error_msg = self.validate_exchange_rate_data("CNY", Decimal("7.200000"), date(2024, 1, 1))
        assert is_valid is True
        assert error_msg == ""
        
        # 测试无效货币代码
        is_valid, error_msg = self.validate_exchange_rate_data("XXX", Decimal("7.200000"), date(2024, 1, 1))
        assert is_valid is False
        assert "不支持的货币代码" in error_msg
        
        # 测试无效汇率值
        is_valid, error_msg = self.validate_exchange_rate_data("CNY", Decimal("-1"), date(2024, 1, 1))
        assert is_valid is False
        assert "汇率必须大于0" in error_msg
        
        # 测试无效月份格式
        is_valid, error_msg = self.validate_exchange_rate_data("CNY", Decimal("7.200000"), date(2024, 1, 15))
        assert is_valid is False
        assert "应该是月初" in error_msg
