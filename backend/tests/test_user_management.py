"""
用户管理功能测试
使用UserManagementTestCase基类，简化测试代码
"""

import pytest
from tests.base import UserManagementTestCase


class TestUserManagement(UserManagementTestCase):
    """用户管理功能测试"""

    def test_create_user(self):
        """测试创建用户"""
        headers = self.get_auth_headers("admin", "admin123")
        
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "NewUser123",
            "full_name": "新用户",
            "department_id": self.tech_dept.id,
            "role_id": self.user_role.id,
            "is_superuser": False,
            "is_active": True
        }
        
        response = self.client.post("/api/admin/users/", json=user_data, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["username"] == "newuser"
        assert data["email"] == "<EMAIL>"
        assert data["full_name"] == "新用户"
        assert data["is_superuser"] is False
        assert data["is_active"] is True

    def test_get_users_list(self):
        """测试获取用户列表"""
        headers = self.get_auth_headers("admin", "admin123")
        
        response = self.client.get("/api/admin/users/", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert len(data["items"]) >= 1  # 至少有一个管理员用户

    def test_get_user_by_id(self):
        """测试根据ID获取用户"""
        headers = self.get_auth_headers("admin", "admin123")
        
        response = self.client.get(f"/api/admin/users/{self.admin_user.id}", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == self.admin_user.id
        assert data["username"] == "admin"

    def test_update_user(self):
        """测试更新用户"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建测试用户
        test_user = self.create_test_user(
            username="updateuser",
            email="<EMAIL>",
            password="updateuser123",
            full_name="更新用户",
            department_id=self.tech_dept.id,
            role_id=self.user_role.id
        )
        
        update_data = {
            "full_name": "更新后的用户",
            "email": "<EMAIL>",
            "department_id": self.hr_dept.id,
            "is_active": False
        }
        
        response = self.client.put(f"/api/admin/users/{test_user.id}", json=update_data, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["full_name"] == "更新后的用户"
        assert data["email"] == "<EMAIL>"
        assert data["is_active"] is False

    def test_delete_user_removed(self):
        """测试删除用户功能已移除"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建测试用户
        test_user = self.create_test_user(
            username="deleteuser",
            email="<EMAIL>",
            password="deleteuser123",
            full_name="删除用户",
            department_id=self.tech_dept.id,
            role_id=self.user_role.id
        )
        
        # 验证删除接口已不存在
        response = self.client.delete(f"/api/admin/users/{test_user.id}", headers=headers)
        assert response.status_code == 405  # Method Not Allowed
        
        # 验证用户仍然存在
        get_response = self.client.get(f"/api/admin/users/{test_user.id}", headers=headers)
        assert get_response.status_code == 200

    def test_create_user_validation(self):
        """测试创建用户时的数据验证"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 测试缺少必填字段
        invalid_data = {
            "email": "<EMAIL>"
            # 缺少username
        }
        
        response = self.client.post("/api/admin/users/", json=invalid_data, headers=headers)
        assert response.status_code == 422
        
        # 测试无效邮箱格式
        invalid_email_data = {
            "username": "testuser",
            "email": "invalid-email",
            "password": "test123",
            "full_name": "测试用户"
        }
        
        response = self.client.post("/api/admin/users/", json=invalid_email_data, headers=headers)
        assert response.status_code == 422

    def test_duplicate_username(self):
        """测试重复用户名"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建第一个用户
        user_data1 = {
            "username": "duplicateuser",
            "email": "<EMAIL>",
            "password": "test123",
            "full_name": "重复用户1",
            "department_id": self.tech_dept.id,
            "role_id": self.user_role.id
        }
        
        response1 = self.client.post("/api/admin/users/", json=user_data1, headers=headers)
        assert response1.status_code == 200
        
        # 尝试创建相同用户名的用户
        user_data2 = {
            "username": "duplicateuser",
            "email": "<EMAIL>",
            "password": "test123",
            "full_name": "重复用户2",
            "department_id": self.tech_dept.id,
            "role_id": self.user_role.id
        }
        
        response2 = self.client.post("/api/admin/users/", json=user_data2, headers=headers)
        assert response2.status_code == 400  # 或409，取决于API实现

    def test_user_search(self):
        """测试用户搜索功能"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建一些测试用户
        self.create_test_user(username="searchuser1", full_name="搜索用户1")
        self.create_test_user(username="searchuser2", full_name="搜索用户2")
        self.create_test_user(username="otheruser", full_name="其他用户")
        
        # 搜索包含"search"的用户
        response = self.client.get("/api/admin/users/?search=search", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) >= 2
        
        # 验证搜索结果包含预期的用户
        usernames = [user["username"] for user in data["items"]]
        assert "searchuser1" in usernames
        assert "searchuser2" in usernames

    def test_user_pagination(self):
        """测试用户分页功能"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建多个测试用户
        for i in range(15):
            self.create_test_user(
                username=f"pageuser{i}",
                email=f"pageuser{i}@example.com",
                full_name=f"分页用户{i}"
            )
        
        # 测试第一页
        response = self.client.get("/api/admin/users/?page=1&size=10", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) <= 10
        assert data["page"] == 1
        
        # 测试第二页
        response = self.client.get("/api/admin/users/?page=2&size=10", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert data["page"] == 2

    def test_user_filtering(self):
        """测试用户过滤功能"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建不同状态的用户
        active_user = self.create_test_user(
            username="activeuser",
            is_active=True
        )
        
        inactive_user = self.create_test_user(
            username="inactiveuser",
            is_active=False
        )
        
        # 过滤活跃用户
        response = self.client.get("/api/admin/users/?is_active=true", headers=headers)
        assert response.status_code == 200
        data = response.json()
        
        # 验证所有返回的用户都是活跃的
        for user in data["items"]:
            assert user["is_active"] is True
        
        # 过滤非活跃用户
        response = self.client.get("/api/admin/users/?is_active=false", headers=headers)
        assert response.status_code == 200
        data = response.json()
        
        # 验证所有返回的用户都是非活跃的
        for user in data["items"]:
            assert user["is_active"] is False 