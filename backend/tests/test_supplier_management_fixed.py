import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from decimal import Decimal

from app.main import app
from app.core.database import get_db
from app.models.supplier import Supplier, ItemSupplier, SupplierPrice
from app.models.item import Item
from app.models.user import User
from tests.base import BaseTestCase, UserManagementTestCase


class TestSupplierManagement(UserManagementTestCase):
    """供应商管理测试"""

    def test_create_supplier_success(self, client: TestClient):
        """测试成功创建供应商"""
        admin_headers = self.get_auth_headers()
        
        supplier_data = {
            "name": "测试供应商",
            "company_address": "北京市朝阳区",
            "contact_person": "张三",
            "phone": "13800138000",
            "email": "<EMAIL>",
            "rating": 4
        }
        
        response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == supplier_data["name"]
        assert data["code"].startswith("SUP")
        assert data["rating"] == supplier_data["rating"]
        assert data["status"] == "active"

    def test_create_supplier_duplicate_name(self, client: TestClient):
        """测试创建重复名称的供应商"""
        admin_headers = self.get_auth_headers()
        
        supplier_data = {
            "name": "重复供应商",
            "company_address": "上海市浦东新区",
            "contact_person": "李四",
            "phone": "13900139000",
            "email": "<EMAIL>",
            "rating": 3
        }
        
        # 第一次创建
        response1 = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        assert response1.status_code == 200
        
        # 第二次创建相同名称
        response2 = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        assert response2.status_code == 400

    def test_get_suppliers_list(self, client: TestClient):
        """测试获取供应商列表"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试供应商
        supplier_data = {
            "name": "列表测试供应商",
            "company_address": "广州市天河区",
            "contact_person": "王五",
            "phone": "13700137000",
            "email": "<EMAIL>",
            "rating": 5
        }
        client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        
        response = client.get("/api/admin/suppliers", headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert len(data["items"]) > 0

    def test_get_supplier_by_id(self, client: TestClient):
        """测试根据ID获取供应商"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商
        supplier_data = {
            "name": "详情测试供应商",
            "company_address": "深圳市南山区",
            "contact_person": "赵六",
            "phone": "13600136000",
            "email": "<EMAIL>",
            "rating": 4
        }
        create_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = create_response.json()["id"]
        
        # 获取供应商详情
        response = client.get(f"/api/admin/suppliers/{supplier_id}", headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == supplier_id
        assert data["name"] == supplier_data["name"]

    def test_update_supplier(self, client: TestClient):
        """测试更新供应商"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商
        supplier_data = {
            "name": "更新测试供应商",
            "company_address": "杭州市西湖区",
            "contact_person": "钱七",
            "phone": "13500135000",
            "email": "<EMAIL>",
            "rating": 3
        }
        create_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = create_response.json()["id"]
        
        # 更新供应商
        update_data = {
            "name": "已更新供应商",
            "rating": 5,
            "status": "inactive"
        }
        response = client.put(f"/api/admin/suppliers/{supplier_id}", json=update_data, headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["rating"] == update_data["rating"]
        assert data["status"] == update_data["status"]

    def test_delete_supplier(self, client: TestClient):
        """测试删除供应商"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商
        supplier_data = {
            "name": "删除测试供应商",
            "company_address": "成都市高新区",
            "contact_person": "孙八",
            "phone": "13400134000",
            "email": "<EMAIL>",
            "rating": 2
        }
        create_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = create_response.json()["id"]
        
        # 删除供应商
        response = client.delete(f"/api/admin/suppliers/{supplier_id}", headers=admin_headers)
        assert response.status_code == 200
        
        # 验证已删除
        get_response = client.get(f"/api/admin/suppliers/{supplier_id}", headers=admin_headers)
        assert get_response.status_code == 404

    def test_supplier_search(self, client: TestClient):
        """测试供应商搜索"""
        admin_headers = self.get_auth_headers()
        
        # 创建多个供应商
        suppliers = [
            {"name": "搜索测试供应商A", "contact_person": "搜索A"},
            {"name": "搜索测试供应商B", "contact_person": "搜索B"},
            {"name": "其他供应商", "contact_person": "其他"}
        ]
        
        for supplier_data in suppliers:
            client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        
        # 搜索包含"搜索"的供应商
        response = client.get("/api/admin/suppliers?search=搜索", headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data["items"]) >= 2

    def test_supplier_filtering(self, client: TestClient):
        """测试供应商过滤"""
        admin_headers = self.get_auth_headers()
        
        # 创建不同评级的供应商
        suppliers = [
            {"name": "高评级供应商", "rating": 5},
            {"name": "中评级供应商", "rating": 3},
            {"name": "低评级供应商", "rating": 1}
        ]
        
        for supplier_data in suppliers:
            client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        
        # 过滤评级>=3的供应商
        response = client.get("/api/admin/suppliers?rating_min=3", headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        for supplier in data["items"]:
            assert supplier["rating"] >= 3

    def test_supplier_pagination(self, client: TestClient):
        """测试供应商分页"""
        admin_headers = self.get_auth_headers()
        
        # 创建多个供应商
        for i in range(25):
            supplier_data = {
                "name": f"分页测试供应商{i}",
                "contact_person": f"联系人{i}",
                "rating": 3
            }
            client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        
        # 测试第一页
        response1 = client.get("/api/admin/suppliers?page=1&size=10", headers=admin_headers)
        assert response1.status_code == 200
        data1 = response1.json()
        assert len(data1["items"]) == 10
        assert data1["page"] == 1
        
        # 测试第二页
        response2 = client.get("/api/admin/suppliers?page=2&size=10", headers=admin_headers)
        assert response2.status_code == 200
        data2 = response2.json()
        assert len(data2["items"]) == 10
        assert data2["page"] == 2


class TestItemSupplierManagement(UserManagementTestCase):
    """物品供应商关联管理测试"""

    def test_add_supplier_item(self, client: TestClient):
        """测试添加供应商物品关联"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商和物品
        supplier_data = {"name": "物品关联测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 先创建一级分类
        primary_category_data = {"name": "测试一级分类", "code_prefix": "TEST"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        assert primary_category_response.status_code == 200
        primary_category_id = primary_category_response.json()["id"]
        
        # 再创建分类
        category_data = {"name": "测试分类", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        item_data = {"name": "测试物品", "code": "TEST001", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        # 添加物品供应商关联
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1,
            "delivery_days": 7,
            "quality_rating": 4,
            "spq": 100,
            "moq": 10
        }
        
        response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                             json=item_supplier_data, headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["item_id"] == item_id
        assert data["supplier_id"] == supplier_id
        assert data["priority"] == 1

    def test_get_supplier_items(self, client: TestClient):
        """测试获取供应商的物品列表"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商和物品
        supplier_data = {"name": "物品列表测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 先创建一级分类
        primary_category_data = {"name": "测试一级分类2", "code_prefix": "TEST2"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 再创建分类
        category_data = {"name": "测试分类2", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        item_data = {"name": "测试物品", "code": "TEST002", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        # 添加关联
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                   json=item_supplier_data, headers=admin_headers)
        
        # 获取供应商的物品列表
        response = client.get(f"/api/admin/suppliers/{supplier_id}/items", headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) > 0
        assert data[0]["item_id"] == item_id

    def test_update_item_supplier(self, client: TestClient):
        """测试更新物品供应商关联"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商和物品
        supplier_data = {"name": "更新关联测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 先创建一级分类
        primary_category_data = {"name": "测试一级分类3", "code_prefix": "TEST3"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 再创建分类
        category_data = {"name": "测试分类3", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        item_data = {"name": "测试物品", "code": "TEST003", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        # 添加关联
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1,
            "delivery_days": 7
        }
        create_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                    json=item_supplier_data, headers=admin_headers)
        item_supplier_id = create_response.json()["id"]
        
        # 更新关联
        update_data = {
            "priority": 0,
            "delivery_days": 14,
            "quality_rating": 5
        }
        response = client.put(f"/api/admin/item-suppliers/{item_supplier_id}", 
                            json=update_data, headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["priority"] == 0
        assert data["delivery_days"] == 14
        assert data["quality_rating"] == 5

    def test_delete_item_supplier(self, client: TestClient):
        """测试删除物品供应商关联"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商和物品
        supplier_data = {"name": "删除关联测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 先创建一级分类
        primary_category_data = {"name": "测试一级分类4", "code_prefix": "TEST4"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 再创建分类
        category_data = {"name": "测试分类4", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        item_data = {"name": "测试物品", "code": "TEST004", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        # 添加关联
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        create_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                    json=item_supplier_data, headers=admin_headers)
        item_supplier_id = create_response.json()["id"]
        
        # 删除关联
        response = client.delete(f"/api/admin/item-suppliers/{item_supplier_id}", headers=admin_headers)
        assert response.status_code == 200


class TestSupplierPriceManagement(UserManagementTestCase):
    """供应商价格管理测试"""

    def test_create_supplier_price(self, client: TestClient):
        """测试创建供应商价格"""
        admin_headers = self.get_auth_headers()
        
        # 创建供应商、物品和关联
        supplier_data = {"name": "价格测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 先创建一级分类
        primary_category_data = {"name": "价格测试一级分类", "code_prefix": "PRICE"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 再创建分类
        category_data = {"name": "价格测试分类", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        item_data = {"name": "价格测试物品", "code": "PRICE001", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        item_supplier_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                           json=item_supplier_data, headers=admin_headers)
        item_supplier_id = item_supplier_response.json()["id"]
        
        # 创建价格
        price_data = {
            "item_supplier_id": item_supplier_id,
            "unit_price": "10.50",
            "min_quantity": 1,
            "max_quantity": 100,
            "valid_from": datetime.now().isoformat(),
            "valid_to": (datetime.now() + timedelta(days=365)).isoformat(),
            "remarks": "测试价格"
        }
        
        response = client.post("/api/admin/supplier-prices", json=price_data, headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["unit_price"] == "10.5000"
        assert data["min_quantity"] == 1
        assert data["max_quantity"] == 100

    def test_get_supplier_prices(self, client: TestClient):
        """测试获取供应商价格列表"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试数据
        supplier_data = {"name": "价格列表测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 先创建一级分类
        primary_category_data = {"name": "价格列表测试一级分类", "code_prefix": "PRICE2"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 再创建分类
        category_data = {"name": "价格列表测试分类", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        item_data = {"name": "价格列表测试物品", "code": "PRICE002", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        item_supplier_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                           json=item_supplier_data, headers=admin_headers)
        item_supplier_id = item_supplier_response.json()["id"]
        
        # 创建价格
        price_data = {
            "item_supplier_id": item_supplier_id,
            "unit_price": "15.00",
            "min_quantity": 1,
            "valid_from": datetime.now().isoformat()
        }
        client.post("/api/admin/supplier-prices", json=price_data, headers=admin_headers)
        
        # 获取价格列表
        response = client.get(f"/api/admin/supplier-prices?supplier_id={supplier_id}", headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "items" in data
        assert len(data["items"]) > 0

    def test_update_supplier_price(self, client: TestClient):
        """测试更新供应商价格"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试数据
        supplier_data = {"name": "价格更新测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 先创建一级分类
        primary_category_data = {"name": "价格更新测试一级分类", "code_prefix": "PRICE3"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 再创建分类
        category_data = {"name": "价格更新测试分类", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        item_data = {"name": "价格更新测试物品", "code": "PRICE003", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        item_supplier_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                           json=item_supplier_data, headers=admin_headers)
        item_supplier_id = item_supplier_response.json()["id"]
        
        # 创建价格
        price_data = {
            "item_supplier_id": item_supplier_id,
            "unit_price": "20.00",
            "min_quantity": 1,
            "valid_from": datetime.now().isoformat()
        }
        create_response = client.post("/api/admin/supplier-prices", json=price_data, headers=admin_headers)
        price_id = create_response.json()["id"]
        
        # 更新价格
        update_data = {
            "unit_price": "25.00",
            "remarks": "更新后的价格"
        }
        response = client.put(f"/api/admin/supplier-prices/{price_id}", 
                            json=update_data, headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["unit_price"] == "25.0000"
        assert data["remarks"] == "更新后的价格"

    def test_delete_supplier_price(self, client: TestClient):
        """测试删除供应商价格"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试数据
        supplier_data = {"name": "价格删除测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 先创建一级分类
        primary_category_data = {"name": "价格删除测试一级分类", "code_prefix": "PRICE4"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 再创建分类
        category_data = {"name": "价格删除测试分类", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        item_data = {"name": "价格删除测试物品", "code": "PRICE004", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        item_supplier_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                           json=item_supplier_data, headers=admin_headers)
        item_supplier_id = item_supplier_response.json()["id"]
        
        # 创建价格
        price_data = {
            "item_supplier_id": item_supplier_id,
            "unit_price": "30.00",
            "min_quantity": 1,
            "valid_from": datetime.now().isoformat()
        }
        create_response = client.post("/api/admin/supplier-prices", json=price_data, headers=admin_headers)
        price_id = create_response.json()["id"]
        
        # 删除价格
        response = client.delete(f"/api/admin/supplier-prices/{price_id}", headers=admin_headers)
        assert response.status_code == 200

    def test_calculate_supplier_price(self, client: TestClient):
        """测试计算供应商价格"""
        admin_headers = self.get_auth_headers()
        
        # 创建测试数据
        supplier_data = {"name": "价格计算测试供应商"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        supplier_id = supplier_response.json()["id"]
        
        # 先创建一级分类
        primary_category_data = {"name": "价格计算测试一级分类", "code_prefix": "PRICE5"}
        primary_category_response = client.post("/api/admin/items/primary-categories", json=primary_category_data, headers=admin_headers)
        primary_category_id = primary_category_response.json()["id"]
        
        # 再创建分类
        category_data = {"name": "价格计算测试分类", "primary_category_id": primary_category_id}
        category_response = client.post("/api/admin/items/categories", json=category_data, headers=admin_headers)
        category_id = category_response.json()["id"]
        
        item_data = {"name": "价格计算测试物品", "code": "PRICE005", "category_id": category_id}
        item_response = client.post("/api/admin/items", json=item_data, headers=admin_headers)
        item_id = item_response.json()["id"]
        
        item_supplier_data = {
            "item_id": item_id,
            "supplier_id": supplier_id,
            "priority": 1
        }
        item_supplier_response = client.post(f"/api/admin/suppliers/{supplier_id}/items", 
                                           json=item_supplier_data, headers=admin_headers)
        item_supplier_id = item_supplier_response.json()["id"]
        
        # 创建价格
        price_data = {
            "item_supplier_id": item_supplier_id,
            "unit_price": "12.50",
            "min_quantity": 1,
            "max_quantity": 100,
            "valid_from": datetime.now().isoformat()
        }
        client.post("/api/admin/supplier-prices", json=price_data, headers=admin_headers)
        
        # 计算价格
        response = client.get(f"/api/admin/suppliers/{supplier_id}/items/{item_id}/price?quantity=50", 
                            headers=admin_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "unit_price" in data
        assert "total_amount" in data


class TestSupplierPermissions(UserManagementTestCase):
    """供应商权限测试"""

    def test_supplier_permissions(self, client: TestClient):
        """测试供应商相关权限"""
        admin_headers = self.get_auth_headers()
        
        # 测试创建供应商权限
        supplier_data = {"name": "权限测试供应商"}
        self.assert_permission_check(admin_headers, "/api/admin/suppliers", "POST", 200, body=supplier_data)
        
        # 测试查看供应商权限
        self.assert_permission_check(admin_headers, "/api/admin/suppliers", "GET", 200)
        
        # 测试更新供应商权限
        supplier_data = {"name": "权限测试供应商2"}
        supplier_response = client.post("/api/admin/suppliers", json=supplier_data, headers=admin_headers)
        assert supplier_response.status_code == 200
        supplier_id = supplier_response.json()["id"]
        
        update_data = {"name": "更新权限测试供应商"}
        self.assert_permission_check(admin_headers, f"/api/admin/suppliers/{supplier_id}", "PUT", 200, body=update_data)
        
        # 测试删除供应商权限
        self.assert_permission_check(admin_headers, f"/api/admin/suppliers/{supplier_id}", "DELETE", 200)

    def test_supplier_price_permissions(self, client: TestClient):
        """测试供应商价格权限"""
        admin_headers = self.get_auth_headers()
        
        # 测试创建价格权限 - 使用不存在的ID来测试权限，而不是测试业务逻辑
        price_data = {"item_supplier_id": 999, "unit_price": "10.00", "min_quantity": 1, "valid_from": datetime.now().isoformat()}
        self.assert_permission_check(admin_headers, "/api/admin/supplier-prices", "POST", 404, body=price_data)
        
        # 测试查看价格权限
        self.assert_permission_check(admin_headers, "/api/admin/supplier-prices", "GET", 200)
        
        # 测试更新价格权限
        update_price_data = {"unit_price": "15.00"}
        self.assert_permission_check(admin_headers, "/api/admin/supplier-prices/999", "PUT", 404, body=update_price_data)
        
        # 测试删除价格权限
        self.assert_permission_check(admin_headers, "/api/admin/supplier-prices/999", "DELETE", 404) 