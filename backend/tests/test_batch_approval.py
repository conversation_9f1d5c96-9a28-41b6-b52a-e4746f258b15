import pytest
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session
from app.services.approval_service import ApprovalService
from app.models.purchase import PurchaseRequest, RequestFlowHistory
from app.schemas.approval import BatchApprovalRequest


class TestBatchApproval:
    """测试批量审批功能"""

    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return Mock(spec=Session)

    @pytest.fixture
    def approval_service(self, mock_db):
        """创建审批服务实例"""
        return ApprovalService(mock_db)

    @pytest.fixture
    def sample_requests(self):
        """创建示例申请数据"""
        return [
            PurchaseRequest(
                id=1,
                request_no="PR001",
                status="under_review",
                submitter_id=1,
                department_id=1
            ),
            PurchaseRequest(
                id=2,
                request_no="PR002",
                status="under_review",
                submitter_id=2,
                department_id=1
            ),
            PurchaseRequest(
                id=3,
                request_no="PR003",
                status="under_principle_approval",
                submitter_id=3,
                department_id=2
            )
        ]

    def test_batch_approve_requests_success(self, approval_service, mock_db, sample_requests):
        """测试批量审批成功的情况"""
        # 模拟数据库查询
        mock_db.query.return_value.filter.return_value.first.side_effect = sample_requests
        
        # 模拟审批方法
        with patch.object(approval_service, 'submit_for_review') as mock_review:
            mock_review.return_value = Mock()
            
            result = approval_service.batch_approve_requests(
                request_ids=[1, 2],
                action="approve",
                comments="批量审批测试",
                approval_level="review",
                approver_id=10,
                approver_name="测试审批人"
            )
            
            # 验证结果
            assert result['success_count'] == 2
            assert result['failure_count'] == 0
            assert result['total_count'] == 2
            assert len(result['failed_requests']) == 0
            
            # 验证调用了两次submit_for_review
            assert mock_review.call_count == 2

    def test_batch_approve_requests_partial_failure(self, approval_service, mock_db, sample_requests):
        """测试批量审批部分失败的情况"""
        # 模拟数据库查询
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            sample_requests[0],  # 第一个申请存在
            None,  # 第二个申请不存在
            sample_requests[2]   # 第三个申请存在
        ]
        
        # 模拟审批方法
        with patch.object(approval_service, 'submit_for_review') as mock_review:
            mock_review.return_value = Mock()
            
            result = approval_service.batch_approve_requests(
                request_ids=[1, 2, 3],
                action="approve",
                comments="批量审批测试",
                approval_level="review",
                approver_id=10,
                approver_name="测试审批人"
            )
            
            # 验证结果
            assert result['success_count'] == 2
            assert result['failure_count'] == 1
            assert result['total_count'] == 3
            assert len(result['failed_requests']) == 1
            assert result['failed_requests'][0]['request_id'] == 2
            assert "申请不存在" in result['failed_requests'][0]['reason']

    def test_batch_approve_requests_invalid_approval_level(self, approval_service, mock_db, sample_requests):
        """测试无效审批级别的情况"""
        # 模拟数据库查询
        mock_db.query.return_value.filter.return_value.first.return_value = sample_requests[0]
        
        result = approval_service.batch_approve_requests(
            request_ids=[1],
            action="approve",
            comments="批量审批测试",
            approval_level="invalid_level",
            approver_id=10,
            approver_name="测试审批人"
        )
        
        # 验证结果
        assert result['success_count'] == 0
        assert result['failure_count'] == 1
        assert result['total_count'] == 1
        assert len(result['failed_requests']) == 1
        assert "无效的审批级别" in result['failed_requests'][0]['reason']

    def test_batch_approve_requests_exception_handling(self, approval_service, mock_db, sample_requests):
        """测试异常处理的情况"""
        # 模拟数据库查询
        mock_db.query.return_value.filter.return_value.first.return_value = sample_requests[0]
        
        # 模拟审批方法抛出异常
        with patch.object(approval_service, 'submit_for_review') as mock_review:
            mock_review.side_effect = Exception("审批失败")
            
            result = approval_service.batch_approve_requests(
                request_ids=[1],
                action="approve",
                comments="批量审批测试",
                approval_level="review",
                approver_id=10,
                approver_name="测试审批人"
            )
            
            # 验证结果
            assert result['success_count'] == 0
            assert result['failure_count'] == 1
            assert result['total_count'] == 1
            assert len(result['failed_requests']) == 1
            assert "审批失败" in result['failed_requests'][0]['reason']

    def test_batch_approve_requests_different_approval_levels(self, approval_service, mock_db, sample_requests):
        """测试不同审批级别的申请混合的情况"""
        # 模拟数据库查询
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            sample_requests[0],  # under_review
            sample_requests[2]   # under_principle_approval
        ]
        
        # 模拟审批方法
        with patch.object(approval_service, 'submit_for_review') as mock_review:
            mock_review.return_value = Mock()
            
            result = approval_service.batch_approve_requests(
                request_ids=[1, 3],
                action="approve",
                comments="批量审批测试",
                approval_level="review",  # 使用review级别
                approver_id=10,
                approver_name="测试审批人"
            )
            
            # 验证结果
            assert result['success_count'] == 2
            assert result['failure_count'] == 0
            assert result['total_count'] == 2
            # 注意：这里虽然状态不同，但都使用review级别审批，所以会成功
            # 实际业务中应该在前端限制只能选择同一级别的申请
