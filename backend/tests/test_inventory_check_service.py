import pytest
from decimal import Decimal
from unittest.mock import Mock, patch
from app.services.inventory_check_service import InventoryCheckService
from app.models.purchase import PurchaseCartItem
from app.models.inventory import DepartmentInventory
from app.models.item import Item


class TestInventoryCheckService:
    """库存检查服务测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.mock_db = Mock()
        self.service = InventoryCheckService(self.mock_db)
    
    def test_check_cart_item_inventory_no_inventory(self):
        """测试检查购物车项目库存 - 无库存记录"""
        # 准备测试数据
        cart_item = Mock(spec=PurchaseCartItem)
        cart_item.department_id = 1
        cart_item.item_id = 1
        cart_item.spq_quantity = Decimal("10.0")
        cart_item.spq_count = 2
        
        item = Mock(spec=Item)
        item.name = "测试物品"
        item.code = "TEST001"
        
        # 模拟数据库查询
        self.mock_db.query.return_value.filter.return_value.first.side_effect = [
            None,  # 库存记录不存在
            item   # 物品信息
        ]
        
        # 执行测试
        result = self.service.check_cart_item_inventory(cart_item)
        
        # 验证结果
        assert result["is_overstock"] is False
        assert result["current_quantity"] == Decimal("0")
        assert result["max_quantity"] is None
        assert result["cart_quantity"] == Decimal("20.0")
        assert result["total_after_purchase"] == Decimal("20.0")
        assert result["item_name"] == "测试物品"
        assert result["item_code"] == "TEST001"
    
    def test_check_cart_item_inventory_with_max_quantity(self):
        """测试检查购物车项目库存 - 有最大库存限制"""
        # 准备测试数据
        cart_item = Mock(spec=PurchaseCartItem)
        cart_item.department_id = 1
        cart_item.item_id = 1
        cart_item.spq_quantity = Decimal("10.0")
        cart_item.spq_count = 3
        
        inventory = Mock(spec=DepartmentInventory)
        inventory.current_quantity = Decimal("50.0")
        inventory.max_quantity = Decimal("100.0")
        
        item = Mock(spec=Item)
        item.name = "测试物品"
        item.code = "TEST001"
        
        # 模拟数据库查询
        self.mock_db.query.return_value.filter.return_value.first.side_effect = [
            inventory,  # 库存记录
            item       # 物品信息
        ]
        
        # 执行测试
        result = self.service.check_cart_item_inventory(cart_item)
        
        # 验证结果
        assert result["is_overstock"] is False  # 50 + 30 = 80 < 100
        assert result["current_quantity"] == Decimal("50.0")
        assert result["max_quantity"] == Decimal("100.0")
        assert result["cart_quantity"] == Decimal("30.0")
        assert result["total_after_purchase"] == Decimal("80.0")
    
    def test_check_cart_item_inventory_overstock(self):
        """测试检查购物车项目库存 - 超储情况"""
        # 准备测试数据
        cart_item = Mock(spec=PurchaseCartItem)
        cart_item.department_id = 1
        cart_item.item_id = 1
        cart_item.spq_quantity = Decimal("10.0")
        cart_item.spq_count = 6  # 60个
        
        inventory = Mock(spec=DepartmentInventory)
        inventory.current_quantity = Decimal("50.0")
        inventory.max_quantity = Decimal("100.0")
        
        item = Mock(spec=Item)
        item.name = "测试物品"
        item.code = "TEST001"
        
        # 模拟数据库查询
        self.mock_db.query.return_value.filter.return_value.first.side_effect = [
            inventory,  # 库存记录
            item       # 物品信息
        ]
        
        # 执行测试
        result = self.service.check_cart_item_inventory(cart_item)
        
        # 验证结果
        assert result["is_overstock"] is True  # 50 + 60 = 110 > 100
        assert result["current_quantity"] == Decimal("50.0")
        assert result["max_quantity"] == Decimal("100.0")
        assert result["cart_quantity"] == Decimal("60.0")
        assert result["total_after_purchase"] == Decimal("110.0")
        assert "超过最大库存限制" in result["alert_message"]
    
    def test_check_department_cart_inventory(self):
        """测试检查部门购物车库存"""
        # 准备测试数据
        cart_item1 = Mock(spec=PurchaseCartItem)
        cart_item1.department_id = 1
        cart_item1.item_id = 1
        
        cart_item2 = Mock(spec=PurchaseCartItem)
        cart_item2.department_id = 1
        cart_item2.item_id = 2
        
        # 模拟数据库查询
        self.mock_db.query.return_value.filter.return_value.all.return_value = [cart_item1, cart_item2]
        
        # 模拟单个项目检查
        with patch.object(self.service, 'check_cart_item_inventory') as mock_check:
            mock_check.side_effect = [
                {"is_overstock": False, "item_name": "物品1"},
                {"is_overstock": True, "item_name": "物品2"}
            ]
            
            # 执行测试
            results = self.service.check_department_cart_inventory(1)
            
            # 验证结果
            assert len(results) == 2
            assert results[0]["item_name"] == "物品1"
            assert results[1]["item_name"] == "物品2"
    
    def test_generate_overstock_alert_file(self):
        """测试生成超储预警文件"""
        # 准备测试数据
        overstock_items = [
            {
                "is_overstock": True,
                "item_name": "测试物品1",
                "current_quantity": Decimal("50.0"),
                "max_quantity": Decimal("100.0"),
                "cart_quantity": Decimal("60.0"),
                "total_after_purchase": Decimal("110.0"),
                "alert_message": "超储警告"
            }
        ]
        
        department = Mock()
        department.name = "测试部门"
        
        # 模拟数据库查询
        self.mock_db.query.return_value.filter.return_value.first.return_value = department
        
        # 模拟文件操作
        with patch('builtins.open', create=True) as mock_open:
            mock_file = Mock()
            mock_open.return_value.__enter__.return_value = mock_file
            
            # 执行测试
            result = self.service.generate_overstock_alert_file(overstock_items, 1)
            
            # 验证结果
            assert result != ""
            assert "overstock_alert_测试部门_" in result
            mock_file.write.assert_called()
    
    def test_check_and_alert_cart_inventory(self):
        """测试检查并预警购物车库存"""
        # 准备测试数据
        inventory_results = [
            {"is_overstock": False, "item_name": "正常物品"},
            {"is_overstock": True, "item_name": "超储物品"}
        ]
        
        # 模拟检查方法
        with patch.object(self.service, 'check_department_cart_inventory') as mock_check:
            mock_check.return_value = inventory_results
            
            # 模拟生成预警文件
            with patch.object(self.service, 'generate_overstock_alert_file') as mock_generate:
                mock_generate.return_value = "/path/to/alert.json"
                
                # 执行测试
                result = self.service.check_and_alert_cart_inventory(1)
                
                # 验证结果
                assert result["department_id"] == 1
                assert result["total_items"] == 2
                assert result["overstock_count"] == 1
                assert result["alert_generated"] is True
                assert result["alert_file_path"] == "/path/to/alert.json"


if __name__ == "__main__":
    pytest.main([__file__])
