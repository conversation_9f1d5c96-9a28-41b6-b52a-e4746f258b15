"""
测试JSON序列化功能
"""

import pytest
from datetime import datetime, date
from decimal import Decimal
from app.core.json_encoder import CustomJSONEncoder, json_dumps
from app.core.utils import serialize_datetime, format_response_data


class TestJSONSerialization:
    """测试JSON序列化"""
    
    def test_datetime_serialization(self):
        """测试datetime序列化"""
        dt = datetime(2023, 1, 1, 12, 30, 45)
        result = json_dumps({"datetime": dt})
        assert "2023-01-01T12:30:45" in result
    
    def test_date_serialization(self):
        """测试date序列化"""
        d = date(2023, 1, 1)
        result = json_dumps({"date": d})
        assert "2023-01-01" in result
    
    def test_decimal_serialization(self):
        """测试Decimal序列化"""
        dec = Decimal("123.45")
        result = json_dumps({"decimal": dec})
        assert "123.45" in result
    
    def test_nested_serialization(self):
        """测试嵌套对象序列化"""
        data = {
            "datetime": datetime(2023, 1, 1, 12, 30, 45),
            "date": date(2023, 1, 1),
            "decimal": Decimal("123.45"),
            "nested": {
                "datetime": datetime(2023, 1, 1, 12, 30, 45)
            }
        }
        result = json_dumps(data)
        assert "2023-01-01T12:30:45" in result
        assert "2023-01-01" in result
        assert "123.45" in result
    
    def test_serialize_datetime_function(self):
        """测试serialize_datetime函数"""
        dt = datetime(2023, 1, 1, 12, 30, 45)
        result = serialize_datetime(dt)
        assert result == "2023-01-01T12:30:45"
    
    def test_format_response_data(self):
        """测试format_response_data函数"""
        data = {
            "datetime": datetime(2023, 1, 1, 12, 30, 45),
            "date": date(2023, 1, 1),
            "decimal": Decimal("123.45")
        }
        result = format_response_data(data)
        assert result["datetime"] == "2023-01-01T12:30:45"
        assert result["date"] == "2023-01-01"
        assert result["decimal"] == 123.45 