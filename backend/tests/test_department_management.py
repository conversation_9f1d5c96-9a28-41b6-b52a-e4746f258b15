"""
部门管理功能测试
使用UserManagementTestCase基类，简化测试代码
"""

import pytest
from tests.base import UserManagementTestCase


class TestDepartmentManagement(UserManagementTestCase):
    """部门管理功能测试"""

    def test_get_departments_list(self):
        """测试获取部门列表"""
        headers = self.get_auth_headers("admin", "admin123")
        
        response = self.client.get("/api/admin/departments/", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert len(data["items"]) >= 2  # 至少有两个部门

    def test_get_department_by_id(self):
        """测试根据ID获取部门"""
        headers = self.get_auth_headers("admin", "admin123")
        
        response = self.client.get(f"/api/admin/departments/{self.tech_dept.id}", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == self.tech_dept.id
        assert data["name"] == "技术部"
        assert data["code"] == "TECH"

    def test_create_department(self):
        """测试创建部门"""
        headers = self.get_auth_headers("admin", "admin123")
        
        department_data = {
            "name": "新部门",
            "code": "NEW_DEPT",
            "description": "新创建的部门"
        }
        
        response = self.client.post("/api/admin/departments/", json=department_data, headers=headers)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "新部门"
        assert data["code"] == "NEW_DEPT"
        assert data["description"] == "新创建的部门"

    def test_update_department(self):
        """测试更新部门"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建测试部门
        test_dept = self.create_test_department(
            name="更新测试部门",
            code="UPDATE_TEST",
            description="用于更新测试的部门"
        )
        
        update_data = {
            "name": "更新后的部门",
            "description": "更新后的部门描述"
        }
        
        response = self.client.put(f"/api/admin/departments/{test_dept.id}", json=update_data, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "更新后的部门"
        assert data["description"] == "更新后的部门描述"

    def test_delete_department(self):
        """测试删除部门"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建测试部门
        test_dept = self.create_test_department(
            name="删除测试部门",
            code="DELETE_TEST"
        )
        
        response = self.client.delete(f"/api/admin/departments/{test_dept.id}", headers=headers)
        
        assert response.status_code == 204
        
        # 验证部门已被删除
        get_response = self.client.get(f"/api/admin/departments/{test_dept.id}", headers=headers)
        assert get_response.status_code == 404

    def test_department_validation(self):
        """测试部门数据验证"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 测试缺少必填字段
        invalid_data = {
            "description": "测试部门"
            # 缺少name和code
        }
        
        response = self.client.post("/api/admin/departments/", json=invalid_data, headers=headers)
        assert response.status_code == 422
        
        # 测试重复部门代码
        duplicate_data = {
            "name": "重复部门",
            "code": self.tech_dept.code,  # 使用已存在的部门代码
            "description": "重复的部门"
        }
        
        response = self.client.post("/api/admin/departments/", json=duplicate_data, headers=headers)
        assert response.status_code == 400  # 或409，取决于API实现

    def test_department_search(self):
        """测试部门搜索功能"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建一些测试部门
        self.create_test_department(name="搜索部门1", code="SEARCH1")
        self.create_test_department(name="搜索部门2", code="SEARCH2")
        self.create_test_department(name="其他部门", code="OTHER")
        
        # 搜索包含"搜索"的部门
        response = self.client.get("/api/admin/departments/?search=搜索", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) >= 2
        
        # 验证搜索结果包含预期的部门
        department_names = [dept["name"] for dept in data["items"]]
        assert "搜索部门1" in department_names
        assert "搜索部门2" in department_names

    def test_department_pagination(self):
        """测试部门分页功能"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建多个测试部门
        for i in range(15):
            self.create_test_department(
                name=f"分页部门{i}",
                code=f"PAGE{i:03d}",
                description=f"分页测试部门{i}"
            )
        
        # 测试第一页
        response = self.client.get("/api/admin/departments/?page=1&size=10", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) <= 10
        assert data["page"] == 1
        
        # 测试第二页
        response = self.client.get("/api/admin/departments/?page=2&size=10", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert data["page"] == 2

    def test_department_users_relationship(self):
        """测试部门与用户的关系"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建一些用户分配到不同部门
        user1 = self.create_test_user(
            username="deptuser1",
            full_name="部门用户1",
            department_id=self.tech_dept.id
        )
        
        user2 = self.create_test_user(
            username="deptuser2", 
            full_name="部门用户2",
            department_id=self.hr_dept.id
        )
        
        # 获取部门信息，应该包含用户
        response = self.client.get(f"/api/admin/departments/{self.tech_dept.id}", headers=headers)
        assert response.status_code == 200
        data = response.json()
        
        # 验证部门包含用户（如果API返回用户信息）
        if "users" in data:
            user_ids = [user["id"] for user in data["users"]]
            assert user1.id in user_ids
        
        # 获取部门用户列表
        response = self.client.get(f"/api/admin/departments/{self.tech_dept.id}/users", headers=headers)
        if response.status_code == 200:
            data = response.json()
            user_ids = [user["id"] for user in data["items"]]
            assert user1.id in user_ids

    def test_department_hierarchy(self):
        """测试部门层级结构"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建父部门
        parent_dept = self.create_test_department(
            name="父部门",
            code="PARENT",
            description="父级部门"
        )
        
        # 创建子部门
        child_dept = self.create_test_department(
            name="子部门",
            code="CHILD",
            description="子级部门"
        )
        
        # 更新子部门的父部门（如果API支持）
        update_data = {"parent_id": parent_dept.id}
        response = self.client.put(f"/api/admin/departments/{child_dept.id}", json=update_data, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            assert data["parent_id"] == parent_dept.id
            
            # 获取父部门的子部门列表
            response = self.client.get(f"/api/admin/departments/{parent_dept.id}/children", headers=headers)
            if response.status_code == 200:
                data = response.json()
                child_ids = [dept["id"] for dept in data["items"]]
                assert child_dept.id in child_ids

    def test_department_statistics(self):
        """测试部门统计信息"""
        headers = self.get_auth_headers("admin", "admin123")
        
        # 创建一些用户分配到部门
        for i in range(5):
            self.create_test_user(
                username=f"statuser{i}",
                full_name=f"统计用户{i}",
                department_id=self.tech_dept.id
            )
        
        for i in range(3):
            self.create_test_user(
                username=f"hruser{i}",
                full_name=f"HR用户{i}",
                department_id=self.hr_dept.id
            )
        
        # 获取部门统计信息（如果API支持）
        response = self.client.get(f"/api/admin/departments/{self.tech_dept.id}/statistics", headers=headers)
        if response.status_code == 200:
            data = response.json()
            assert "user_count" in data
            assert data["user_count"] >= 5  # 至少5个用户
        
        # 获取所有部门统计信息
        response = self.client.get("/api/admin/departments/statistics", headers=headers)
        if response.status_code == 200:
            data = response.json()
            assert "total_departments" in data
            assert "total_users" in data

    def test_department_permissions(self):
        """测试部门权限控制"""
        # 测试管理员用户（有所有权限）
        admin_headers = self.get_auth_headers("admin", "admin123")
        
        # 管理员应该能访问所有部门操作
        self.assert_permission_check(admin_headers, "/api/admin/departments/", "GET", 200)
        self.assert_permission_check(admin_headers, "/api/admin/departments/", "POST", 201)
        self.assert_permission_check(admin_headers, f"/api/admin/departments/{self.tech_dept.id}", "PUT", 200)
        self.assert_permission_check(admin_headers, f"/api/admin/departments/{self.tech_dept.id}", "DELETE", 204)
        
        # 测试普通用户（只有读取权限）
        user_headers = self.get_auth_headers("user", "user123")
        
        # 普通用户只能读取部门信息
        self.assert_permission_check(user_headers, "/api/admin/departments/", "GET", 200)
        self.assert_permission_check(user_headers, "/api/admin/departments/", "POST", 403)  # 无创建权限
        self.assert_permission_check(user_headers, f"/api/admin/departments/{self.tech_dept.id}", "PUT", 403)  # 无更新权限
        self.assert_permission_check(user_headers, f"/api/admin/departments/{self.tech_dept.id}", "DELETE", 403)  # 无删除权限 