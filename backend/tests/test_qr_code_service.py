"""
QRCodeService 测试用例
"""

import pytest
from unittest.mock import Mock, patch
from app.services.qr_code_service import QRCodeService, QRCodeType


class TestQRCodeService:
    """QRCodeService 测试类"""

    def test_generate_item_qr_code(self):
        """测试生成物品二维码"""
        # 模拟物品对象
        mock_item = Mock()
        mock_item.code = "ITM-2025-001"
        mock_item.name = "N95防护口罩"
        mock_item.brand = "3M"
        mock_item.spec_material = "独立包装 / 医用级"
        mock_item.size_dimension = "KN95"

        qr_content = QRCodeService.generate_item_qr_code(mock_item)
        
        expected_lines = [
            "bls://idm",
            "v:1.0",
            "t:i",
            "c:ITM-2025-001",
            "n:N95防护口罩",
            "r:3M / 独立包装 / 医用级 / KN95"
        ]
        
        assert qr_content == "\n".join(expected_lines)

    def test_generate_item_qr_code_no_attributes(self):
        """测试生成物品二维码（无属性）"""
        mock_item = Mock()
        mock_item.code = "ITM-2025-002"
        mock_item.name = "普通口罩"
        mock_item.brand = None
        mock_item.spec_material = None
        mock_item.size_dimension = None

        qr_content = QRCodeService.generate_item_qr_code(mock_item)
        
        expected_lines = [
            "bls://idm",
            "v:1.0",
            "t:i",
            "c:ITM-2025-002",
            "n:普通口罩"
        ]
        
        assert qr_content == "\n".join(expected_lines)

    def test_generate_department_qr_code(self):
        """测试生成部门二维码"""
        mock_dept = Mock()
        mock_dept.code = "dev"
        mock_dept.name = "技术研发部"

        qr_content = QRCodeService.generate_department_qr_code(mock_dept)
        
        expected_lines = [
            "bls://idm",
            "v:1.0",
            "t:d",
            "c:dev",
            "r:技术研发部"
        ]
        
        assert qr_content == "\n".join(expected_lines)

    def test_generate_employee_qr_code(self):
        """测试生成员工二维码"""
        mock_user = Mock()
        mock_user.employee_id = "99001"
        mock_user.display_name = "Huang Jun"
        mock_user.full_name = "Huang Jun"
        mock_user.username = "hjun"

        qr_content = QRCodeService.generate_employee_qr_code(mock_user)
        
        expected_lines = [
            "bls://idm",
            "v:1.0",
            "t:c",
            "c:99001",
            "r:Huang Jun"
        ]
        
        assert qr_content == "\n".join(expected_lines)

    def test_generate_supplier_qr_code(self):
        """测试生成供应商二维码"""
        mock_supplier = Mock()
        mock_supplier.code = "SUP001"
        mock_supplier.name_cn = "ABC供应商"
        mock_supplier.name_en = "ABC Supplier"

        qr_content = QRCodeService.generate_supplier_qr_code(mock_supplier)
        
        expected_lines = [
            "bls://idm",
            "v:1.0",
            "t:s",
            "c:SUP001",
            "r:ABC供应商"
        ]
        
        assert qr_content == "\n".join(expected_lines)

    def test_parse_qr_code_valid(self):
        """测试解析有效二维码"""
        qr_content = """bls://idm
v:1.0
t:i
c:ITM-2025-001
n:N95防护口罩
r:独立包装 / 医用级"""

        result = QRCodeService.parse_qr_code(qr_content)
        
        assert result['type'] == 'i'
        assert result['code'] == 'ITM-2025-001'
        assert result['name'] == 'N95防护口罩'
        assert result['remark'] == '独立包装 / 医用级'
        assert result['version'] == '1.0'

    def test_parse_qr_code_invalid_prefix(self):
        """测试解析无效前缀的二维码"""
        qr_content = """invalid://idm
v:1.0
t:i
c:ITM-2025-001"""

        with pytest.raises(ValueError, match="无效的二维码格式：协议前缀不匹配"):
            QRCodeService.parse_qr_code(qr_content)

    def test_parse_qr_code_missing_required_fields(self):
        """测试解析缺少必要字段的二维码"""
        qr_content = """bls://idm
v:1.0
t:i"""

        with pytest.raises(ValueError, match="缺少必要字段：c"):
            QRCodeService.parse_qr_code(qr_content)

    def test_parse_qr_code_invalid_version(self):
        """测试解析无效版本的二维码"""
        qr_content = """bls://idm
v:2.0
t:i
c:ITM-2025-001"""

        with pytest.raises(ValueError, match="不支持的版本：2.0"):
            QRCodeService.parse_qr_code(qr_content)

    def test_parse_qr_code_invalid_type(self):
        """测试解析无效类型的二维码"""
        qr_content = """bls://idm
v:1.0
t:invalid
c:ITM-2025-001"""

        with pytest.raises(ValueError, match="不支持的类型：invalid"):
            QRCodeService.parse_qr_code(qr_content)

    def test_parse_qr_code_with_newlines_in_values(self):
        """测试解析包含换行符的二维码值"""
        qr_content = """bls://idm
v:1.0
t:i
c:ITM-2025-001
n:N95防护口罩
r:独立包装
医用级"""

        result = QRCodeService.parse_qr_code(qr_content)
        
        assert result['type'] == 'i'
        assert result['code'] == 'ITM-2025-001'
        assert result['name'] == 'N95防护口罩'
        assert result['remark'] == '独立包装'

    def test_is_valid_qr_code(self):
        """测试二维码有效性验证"""
        valid_qr = """bls://idm
v:1.0
t:i
c:ITM-2025-001
n:测试物品"""

        invalid_qr = "invalid content"

        assert QRCodeService.is_valid_qr_code(valid_qr) is True
        assert QRCodeService.is_valid_qr_code(invalid_qr) is False

    def test_get_qr_code_type(self):
        """测试获取二维码类型"""
        item_qr = """bls://idm
v:1.0
t:i
c:ITM-2025-001"""

        dept_qr = """bls://idm
v:1.0
t:d
c:dev"""

        assert QRCodeService.get_qr_code_type(item_qr) == QRCodeType.ITEM
        assert QRCodeService.get_qr_code_type(dept_qr) == QRCodeType.DEPARTMENT
        assert QRCodeService.get_qr_code_type("invalid") is None

    @patch('app.services.qr_code_service.Item')
    def test_get_item_by_qr_code_success(self, mock_item_model):
        """测试根据二维码成功查询物品"""
        # 模拟数据库会话
        mock_db = Mock()
        
        # 模拟物品对象
        mock_item = Mock()
        mock_item.code = "ITM-2025-001"
        mock_item.name = "测试物品"
        mock_item.is_active = True
        
        # 模拟查询结果
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = mock_item
        mock_db.query.return_value = mock_query
        
        qr_content = """bls://idm
v:1.0
t:i
c:ITM-2025-001
n:测试物品"""

        result = QRCodeService.get_item_by_qr_code(qr_content, mock_db)
        
        assert result is not None
        assert result.code == "ITM-2025-001"
        assert result.name == "测试物品"

    @patch('app.services.qr_code_service.User')
    def test_get_employee_by_qr_code_success(self, mock_user_model):
        """测试根据二维码成功查询员工"""
        # 模拟数据库会话
        mock_db = Mock()
        
        # 模拟员工对象
        mock_employee = Mock()
        mock_employee.employee_id = "99001"
        mock_employee.full_name = "测试员工"
        mock_employee.is_active = True
        
        # 模拟查询结果
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = mock_employee
        mock_db.query.return_value = mock_query
        
        qr_content = """bls://idm
v:1.0
t:c
c:99001
r:测试员工"""

        result = QRCodeService.get_employee_by_qr_code(qr_content, mock_db)
        
        assert result is not None
        assert result.employee_id == "99001"
        assert result.full_name == "测试员工"

    def test_get_item_by_qr_code_wrong_type(self):
        """测试根据二维码查询物品时类型不匹配"""
        mock_db = Mock()
        
        qr_content = """bls://idm
v:1.0
t:d
c:dev
r:技术部"""

        result = QRCodeService.get_item_by_qr_code(qr_content, mock_db)
        
        assert result is None

    def test_get_item_by_qr_code_invalid_format(self):
        """测试根据无效格式二维码查询物品"""
        mock_db = Mock()
        
        qr_content = "invalid qr content"

        result = QRCodeService.get_item_by_qr_code(qr_content, mock_db)
        
        assert result is None
