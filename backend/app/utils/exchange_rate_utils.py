"""
汇率管理工具函数
提供汇率计算、有效性检查、价格转换等通用功能
"""

from typing import Optional, Tuple
from decimal import Decimal, ROUND_HALF_UP
from datetime import date, datetime
from app.services.exchange_rate_service import ExchangeRateService


def convert_price_to_usd(
    amount: Decimal, 
    currency_code: str, 
    exchange_rate: Decimal
) -> Decimal:
    """
    将指定货币的价格转换为美元
    
    Args:
        amount: 原币价格
        currency_code: 货币代码
        exchange_rate: 汇率 (1 USD = exchange_rate 外币)
    
    Returns:
        转换后的美元价格
    """
    if currency_code == "USD":
        return amount
    
    if exchange_rate <= 0:
        raise ValueError("汇率必须大于0")
    
    # 转换公式：美元价格 = 原币价格 ÷ 汇率
    usd_price = amount / exchange_rate
    
    # 根据金额大小确定精度
    if usd_price >= 1:
        # 大于1美元，保留2位小数
        return usd_price.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
    else:
        # 小于1美元，保留4位小数
        return usd_price.quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)


def convert_usd_to_currency(
    usd_amount: Decimal, 
    currency_code: str, 
    exchange_rate: Decimal
) -> Decimal:
    """
    将美元价格转换为指定货币
    
    Args:
        usd_amount: 美元价格
        currency_code: 目标货币代码
        exchange_rate: 汇率 (1 USD = exchange_rate 外币)
    
    Returns:
        转换后的目标货币价格
    """
    if currency_code == "USD":
        return usd_amount
    
    if exchange_rate <= 0:
        raise ValueError("汇率必须大于0")
    
    # 转换公式：目标货币价格 = 美元价格 × 汇率
    target_price = usd_amount * exchange_rate
    
    # 根据金额大小确定精度
    if target_price >= 1:
        # 大于1，保留2位小数
        return target_price.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
    else:
        # 小于1，保留4位小数
        return target_price.quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)


def format_price_display(price: Decimal, currency_code: str = "USD") -> str:
    """
    格式化价格显示
    
    Args:
        price: 价格
        currency_code: 货币代码
    
    Returns:
        格式化后的价格字符串
    """
    currency_symbols = {
        "USD": "$",
        "CNY": "¥",
        "EUR": "€",
        "JPY": "¥",
        "GBP": "£",
        "KRW": "₩",
        "SGD": "S$",
        "HKD": "HK$",
        "MYR": "RM"
    }
    
    symbol = currency_symbols.get(currency_code, currency_code)
    
    if price >= 1:
        # 大于1，显示2位小数
        formatted_price = f"{price:.2f}"
    else:
        # 小于1，显示4位小数
        formatted_price = f"{price:.4f}"
    
    return f"{symbol}{formatted_price}"


def calculate_total_price_with_currency(
    unit_price: Decimal,
    quantity: int,
    currency_code: str,
    exchange_rate: Optional[Decimal] = None
) -> Tuple[Decimal, str, Optional[Decimal]]:
    """
    计算总价格（支持多货币）
    
    Args:
        unit_price: 单价
        quantity: 数量
        currency_code: 货币代码
        exchange_rate: 汇率（可选）
    
    Returns:
        (总价格, 格式化显示, 美元价格)
    """
    # 计算原币总价
    total_price = unit_price * quantity
    
    # 格式化显示
    formatted_price = format_price_display(total_price, currency_code)
    
    # 如果提供了汇率，计算美元价格
    usd_price = None
    if exchange_rate and currency_code != "USD":
        usd_price = convert_price_to_usd(total_price, currency_code, exchange_rate)
    
    return total_price, formatted_price, usd_price


def validate_exchange_rate_data(
    currency_code: str,
    rate: Decimal,
    effective_month: date
) -> Tuple[bool, str]:
    """
    验证汇率数据有效性
    
    Args:
        currency_code: 货币代码
        rate: 汇率值
        effective_month: 生效月份
    
    Returns:
        (是否有效, 错误信息)
    """
    # 验证货币代码
    valid_currencies = ["USD", "CNY", "EUR", "JPY", "GBP", "KRW", "SGD", "HKD", "MYR"]
    if currency_code not in valid_currencies:
        return False, f"不支持的货币代码: {currency_code}"
    
    # 验证汇率值
    if rate <= 0:
        return False, "汇率必须大于0"
    
    if rate > 10000:
        return False, "汇率值过大，请检查数据"
    
    # 验证生效月份
    if effective_month > date.today():
        return False, "生效月份不能是未来日期"
    
    # 验证月份格式（应该是月初）
    if effective_month.day != 1:
        return False, "生效月份应该是月初（1号）"
    
    return True, ""


def get_currency_info(currency_code: str) -> dict:
    """
    获取货币信息
    
    Args:
        currency_code: 货币代码
    
    Returns:
        货币信息字典
    """
    currency_info = {
        "USD": {"name": "美元", "symbol": "$", "is_base": True},
        "CNY": {"name": "人民币", "symbol": "¥", "is_base": False},
        "EUR": {"name": "欧元", "symbol": "€", "is_base": False},
        "JPY": {"name": "日元", "symbol": "¥", "is_base": False},
        "GBP": {"name": "英镑", "symbol": "£", "is_base": False},
        "KRW": {"name": "韩元", "symbol": "₩", "is_base": False},
        "SGD": {"name": "新加坡元", "symbol": "S$", "is_base": False},
        "HKD": {"name": "港币", "symbol": "HK$", "is_base": False},
        "MYR": {"name": "马来西亚林吉特", "symbol": "RM", "is_base": False},
    }
    
    return currency_info.get(currency_code, {
        "name": currency_code,
        "symbol": currency_code,
        "is_base": False
    })


def calculate_price_change_percentage(
    old_price: Decimal,
    new_price: Decimal,
    currency_code: str = "USD"
) -> Tuple[Decimal, str]:
    """
    计算价格变化百分比
    
    Args:
        old_price: 旧价格
        new_price: 新价格
        currency_code: 货币代码
    
    Returns:
        (变化百分比, 变化描述)
    """
    if old_price == 0:
        return Decimal('0'), "无变化"
    
    change_percentage = ((new_price - old_price) / old_price) * 100
    
    if change_percentage > 0:
        change_desc = f"上涨 {change_percentage:.2f}%"
    elif change_percentage < 0:
        change_desc = f"下降 {abs(change_percentage):.2f}%"
    else:
        change_desc = "无变化"
    
    return change_percentage, change_desc


def estimate_monthly_cost(
    unit_price: Decimal,
    monthly_usage: int,
    currency_code: str,
    exchange_rate: Optional[Decimal] = None
) -> dict:
    """
    估算月度成本
    
    Args:
        unit_price: 单价
        monthly_usage: 月度使用量
        currency_code: 货币代码
        exchange_rate: 汇率（可选）
    
    Returns:
        月度成本估算结果
    """
    # 计算月度成本
    monthly_cost = unit_price * monthly_usage
    
    # 格式化显示
    formatted_cost = format_price_display(monthly_cost, currency_code)
    
    # 如果提供了汇率，计算美元成本
    usd_cost = None
    if exchange_rate and currency_code != "USD":
        usd_cost = convert_price_to_usd(monthly_cost, currency_code, exchange_rate)
        usd_formatted = format_price_display(usd_cost, "USD")
    else:
        usd_formatted = None
    
    return {
        "monthly_cost": monthly_cost,
        "formatted_cost": formatted_cost,
        "usd_cost": usd_cost,
        "usd_formatted": usd_formatted,
        "currency_code": currency_code,
        "monthly_usage": monthly_usage
    }
