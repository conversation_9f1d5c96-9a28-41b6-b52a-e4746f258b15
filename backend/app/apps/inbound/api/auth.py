from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from app.core.config import settings
from app.core.database import get_db
from app.models.user import User
from app.models.permission import Role
from app.schemas.user import User<PERSON>ogin, Token, User as UserSchema
from app.services.auth import (
    authenticate_user, 
    create_access_token, 
    get_user_with_permissions,
    update_login_info,
    get_user,
    get_current_active_user
)

router = APIRouter()

@router.post("/login", response_model=Token)
async def login(user_credentials: UserLogin, db: Session = Depends(get_db), request: Request = None):
    """入库系统用户登录"""
    result = authenticate_user(db, user_credentials.username, user_credentials.password)
    
    if isinstance(result, tuple) and len(result) == 2:
        user, error_message = result
        if not user:
            # 登录失败，更新失败次数
            db_user = get_user(db, user_credentials.username)
            if db_user:
                update_login_info(db, db_user, 
                                ip_address=request.client.host if request else None, 
                                success=False)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=error_message,
                headers={"WWW-Authenticate": "Bearer"},
            )
    else:
        # 向后兼容
        user = result
        if not user:
            # 登录失败，更新失败次数
            db_user = get_user(db, user_credentials.username)
            if db_user:
                update_login_info(db, db_user, 
                                ip_address=request.client.host if request else None, 
                                success=False)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    # 检查用户角色是否为warehouse_keeper
    if user.role_id:
        role = db.query(Role).filter(Role.id == user.role_id).first()
        if not role or role.code != 'warehouse_keeper':
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有仓库管理员可以访问入库管理系统"
            )
    else:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户没有分配角色，无法访问入库管理系统"
        )
    
    # 登录成功，更新登录信息
    update_login_info(db, user, 
                      ip_address=request.client.host if request else None, 
                      success=True)
    
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    
    # 获取包含权限的用户信息
    user_data = get_user_with_permissions(db, user)
    
    # 添加角色信息到用户数据中
    if user_data and isinstance(user_data, dict):
        user_data['role'] = role.code
        user_data['role_name'] = role.name
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.access_token_expire_minutes * 60,  # 转换为秒
        "user": user_data
    }

@router.get("/verify")
async def verify_token(current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    """验证token有效性"""
    # 获取用户角色信息
    role = None
    if current_user.role_id:
        role = db.query(Role).filter(Role.id == current_user.role_id).first()
    
    return {
        "valid": True,
        "user": {
            "user_id": current_user.id,
            "username": current_user.username,
            "full_name": current_user.full_name,
            "role": role.code if role else None,
            "role_name": role.name if role else None
        }
    }
