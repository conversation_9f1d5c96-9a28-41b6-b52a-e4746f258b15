from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.core.database import get_db
from app.models.item import Item
from app.models.inventory import DepartmentInventory, InventoryChangeRecord
from app.models.purchase import PurchaseRequest, PurchaseRequestItem
from app.schemas.inventory import DepartmentInventoryResponse
from app.schemas.inbound import (
    ScanQRCodeRequest,
    ScanQRCodeResponse,
    InboundItemRequest,
    InboundRequest,
    InboundResponse
)
from app.services.auth import get_current_user
from app.services.inbound_service import InboundService

router = APIRouter()

@router.post("/scan-qr", response_model=ScanQRCodeResponse)
async def scan_qr_code(
    request: ScanQRCodeRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """扫描二维码获取采购申请单信息"""
    try:
        service = InboundService(db)
        result = service.scan_qr_code(request.qr_code, current_user.id)
        return result
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/inbound", response_model=InboundResponse)
async def execute_inbound(
    request: InboundRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """执行入库操作"""
    try:
        service = InboundService(db)
        result = service.execute_inbound(request, current_user.id)
        return result
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("", response_model=List[DepartmentInventoryResponse])
async def get_inventory_list(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user),
    item_name: Optional[str] = Query(None, description="物品名称"),
    category_id: Optional[int] = Query(None, description="分类ID"),
    supplier_id: Optional[int] = Query(None, description="供应商ID"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000)
):
    """获取库存列表 - 入库端专用"""
    query = db.query(DepartmentInventory).join(Item)
    
    if item_name:
        query = query.filter(Item.name.contains(item_name))
    if category_id:
        query = query.filter(Item.category_id == category_id)
    if supplier_id:
        query = query.filter(Item.supplier_id == supplier_id)
    
    inventory_list = query.offset(skip).limit(limit).all()
    return inventory_list

@router.get("/{item_id}", response_model=DepartmentInventoryResponse)
async def get_item_inventory(
    item_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取指定物品的库存信息 - 入库端专用"""
    inventory = db.query(DepartmentInventory).filter(DepartmentInventory.item_id == item_id).first()
    if not inventory:
        raise HTTPException(status_code=404, detail="库存记录不存在")
    return inventory
