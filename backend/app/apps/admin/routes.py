from fastapi import APIRouter
from .api import users, roles, items, suppliers, reports, inventory, upload, image_management, departments
from .api import purchase_cart, purchase_request, approval, purchase_overview, purchase_execution
from .api import auth, notifications, emails, configs, stats
from .routers import exchange_rate

router = APIRouter()

# 用户与权限管理
router.include_router(auth.router, prefix="/auth")
router.include_router(users.router, prefix="/users")
router.include_router(roles.router, prefix="/roles")

# 基础资料管理
router.include_router(items.router, prefix="/items")
router.include_router(inventory.router, prefix="/inventory")
router.include_router(suppliers.router, prefix="/suppliers")
router.include_router(departments.router, prefix="/departments")
router.include_router(exchange_rate.router, prefix="/exchange-rates")

# 报表与系统管理
router.include_router(reports.router, prefix="/reports")
router.include_router(upload.router, prefix="/upload")
router.include_router(image_management.router, prefix="/image-management")

# 采购管理
router.include_router(purchase_overview.router, prefix="/purchase/overview")
router.include_router(purchase_cart.router, prefix="/purchase/cart")
router.include_router(purchase_request.router, prefix="/purchase/requests")
router.include_router(approval.router, prefix="/purchase/approval")
router.include_router(purchase_execution.router, prefix="/purchase/execution")

# 通知中心管理
router.include_router(notifications.router, prefix="/notifications")
router.include_router(emails.router, prefix="/notification-emails")
router.include_router(configs.router, prefix="/system-configs")
router.include_router(stats.router, prefix="/stats")
