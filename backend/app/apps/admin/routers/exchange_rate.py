from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import date
from pydantic import BaseModel

from app.core.database import get_db
from app.services.auth import get_current_user
from app.models.user import User
from app.services.exchange_rate_service import ExchangeRateService
from app.schemas.exchange_rate import (
    ExchangeRateCreate,
    ExchangeRateUpdate,
    ExchangeRate,
    ExchangeRateWithLogs,
    ExchangeRateQuery,
    ExchangeRateHistoryQuery,
    ExchangeRateSummary,
    CurrencyInfo
)
from app.core.exceptions import BusinessException, ResourceNotFoundException

# 创建专门的响应模型
class ExchangeRateListResponse(BaseModel):
    data: List[ExchangeRate]
    total: int
    page: int
    size: int

router = APIRouter(prefix="", tags=["汇率管理"])


@router.post("", response_model=ExchangeRate)
async def create_exchange_rate(
    exchange_rate_data: ExchangeRateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建汇率记录"""
    service = ExchangeRateService(db)
    return service.create_exchange_rate(exchange_rate_data, current_user.id)


@router.put("/{rate_id}", response_model=ExchangeRate)
async def update_exchange_rate(
    rate_id: int,
    update_data: ExchangeRateUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新汇率记录"""
    service = ExchangeRateService(db)
    return service.update_exchange_rate(rate_id, update_data, current_user.id)


@router.get("/{rate_id}", response_model=ExchangeRate)
async def get_exchange_rate(
    rate_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取汇率记录详情"""
    service = ExchangeRateService(db)
    return service.get_exchange_rate(rate_id)


@router.get("/{rate_id}/with-logs", response_model=ExchangeRateWithLogs)
async def get_exchange_rate_with_logs(
    rate_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取汇率记录详情（包含修改日志）"""
    service = ExchangeRateService(db)
    exchange_rate = service.get_exchange_rate(rate_id)
    
    # 获取修改日志
    logs = service.db.query(service.db.query().from_statement(
        "SELECT * FROM exchange_rate_logs WHERE exchange_rate_id = :rate_id ORDER BY changed_at DESC"
    ).params(rate_id=rate_id).statement).all()
    
    # 构建响应对象
    response_data = ExchangeRateWithLogs.model_validate(exchange_rate)
    response_data.logs = logs
    
    return response_data


@router.get("", response_model=ExchangeRateListResponse)
async def get_exchange_rates(
    currency_code: Optional[str] = Query(None, description="货币代码"),
    effective_month: Optional[date] = Query(None, description="生效月份"),
    status: Optional[str] = Query(None, description="状态"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """查询汇率记录列表"""
    service = ExchangeRateService(db)
    query = ExchangeRateQuery(
        currency_code=currency_code,
        effective_month=effective_month,
        status=status,
        page=page,
        size=size
    )
    result = service.get_exchange_rates(query)
    
    # 将SQLAlchemy模型转换为Pydantic模型以确保正确序列化
    from app.schemas.exchange_rate import ExchangeRate
    serialized_data = []
    for rate in result["data"]:
        serialized_rate = ExchangeRate.model_validate(rate)
        serialized_data.append(serialized_rate)
    
    return ExchangeRateListResponse(
        data=serialized_data,
        total=result["total"],
        page=result["page"],
        size=result["size"]
    )


@router.get("/history", response_model=List[ExchangeRate])
async def get_exchange_rate_history(
    currency_code: str = Query(..., description="货币代码"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    days: int = Query(180, ge=1, le=365, description="查询天数（默认180天）"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取汇率历史记录"""
    service = ExchangeRateService(db)
    query = ExchangeRateHistoryQuery(
        currency_code=currency_code,
        start_date=start_date,
        end_date=end_date,
        days=days
    )
    rates = service.get_exchange_rate_history(query)
    
    # 将SQLAlchemy模型转换为Pydantic模型
    from app.schemas.exchange_rate import ExchangeRate
    return [ExchangeRate.model_validate(rate) for rate in rates]


@router.get("/summary", response_model=List[ExchangeRateSummary])
async def get_exchange_rate_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取汇率汇总信息"""
    service = ExchangeRateService(db)
    return service.get_exchange_rate_summary()


@router.get("/currencies", response_model=List[CurrencyInfo])
async def get_supported_currencies():
    """获取支持的货币列表"""
    currencies = [
        CurrencyInfo(code="USD", name="美元", symbol="$", is_base=True),
        CurrencyInfo(code="CNY", name="人民币", symbol="¥", is_base=False),
        CurrencyInfo(code="EUR", name="欧元", symbol="€", is_base=False),
        CurrencyInfo(code="JPY", name="日元", symbol="¥", is_base=False),
        CurrencyInfo(code="GBP", name="英镑", symbol="£", is_base=False),
        CurrencyInfo(code="KRW", name="韩元", symbol="₩", is_base=False),
        CurrencyInfo(code="SGD", name="新加坡元", symbol="S$", is_base=False),
        CurrencyInfo(code="HKD", name="港币", symbol="HK$", is_base=False),
        CurrencyInfo(code="MYR", name="马来西亚林吉特", symbol="RM", is_base=False),
    ]
    return currencies


@router.get("/convert/{currency_code}")
async def convert_currency(
    currency_code: str,
    amount: float = Query(..., gt=0, description="金额"),
    target_date: Optional[date] = Query(None, description="目标日期"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """货币转换（转换为美元）"""
    from decimal import Decimal
    
    service = ExchangeRateService(db)
    usd_amount = service.convert_currency_to_usd(Decimal(str(amount)), currency_code, target_date)
    
    if usd_amount is None:
        raise BusinessException(f"无法获取货币 {currency_code} 的有效汇率")
    
    return {
        "original_amount": amount,
        "original_currency": currency_code,
        "usd_amount": float(usd_amount),
        "usd_formatted": service.format_price_display(usd_amount),
        "conversion_date": target_date or date.today()
    }


@router.get("/validate/{currency_code}")
async def validate_exchange_rate(
    currency_code: str,
    target_date: Optional[date] = Query(None, description="目标日期"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """验证汇率有效性"""
    service = ExchangeRateService(db)
    is_valid = service.is_exchange_rate_valid(currency_code, target_date)
    
    if is_valid:
        # 获取当前有效汇率
        current_rate = service.get_latest_exchange_rate(currency_code, target_date)
        return {
            "currency_code": currency_code,
            "is_valid": True,
            "current_rate": float(current_rate.rate) if current_rate else None,
            "effective_month": current_rate.effective_month if current_rate else None
        }
    else:
        return {
            "currency_code": currency_code,
            "is_valid": False,
            "current_rate": None,
            "effective_month": None
        }
