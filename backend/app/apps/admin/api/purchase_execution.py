from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from app.core.database import get_db
from app.models.user import User
from app.services.purchase_execution_service import PurchaseExecutionService
from app.schemas.purchase_execution import (
    ExecutionBatchCreateRequest, ExecutionBatchFilters, ExecutionBatchResponse,
    ExecutionBatchListResponse, ExecutionSummaryResponse, ExecutionExportRequest,
    ApprovedRequestsListResponse, BatchExecutionConfirmRequest, BatchExecutionPreviewResponse
)
from app.services.auth import get_current_user, require_permissions
from app.core.exceptions import BusinessException
from datetime import datetime
import logging

router = APIRouter(tags=["采购申请执行"])


@router.get("/approved-requests", response_model=ApprovedRequestsListResponse)
async def get_approved_requests(
    department_id: Optional[int] = Query(None, description="部门ID"),
    submitter_id: Optional[int] = Query(None, description="提交人ID"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    current_user: User = Depends(require_permissions(["purchase.execute"])),
    db: Session = Depends(get_db)
):
    """获取已批准的采购申请列表（可执行的申请）"""
    service = PurchaseExecutionService(db)
    
    filters = {}
    if department_id:
        filters["department_id"] = department_id
    if submitter_id:
        filters["submitter_id"] = submitter_id
    if start_date and end_date:
        filters["start_date"] = start_date
        filters["end_date"] = end_date
    
    result = service.get_approved_requests(filters, page, size)
    return ApprovedRequestsListResponse(**result)


@router.post("/preview", response_model=BatchExecutionPreviewResponse)
async def preview_batch_execution(
    request_data: BatchExecutionConfirmRequest,
    current_user: User = Depends(require_permissions(["purchase.execute"])),
    db: Session = Depends(get_db)
):
    """预览批量执行"""
    service = PurchaseExecutionService(db)
    preview = service.preview_batch_execution(request_data.request_ids)
    return preview


@router.post("/execute", response_model=ExecutionBatchResponse, status_code=status.HTTP_201_CREATED)
async def execute_batch(
    request_data: BatchExecutionConfirmRequest,
    current_user: User = Depends(require_permissions(["purchase.execute"])),
    db: Session = Depends(get_db)
):
    """批量执行采购申请"""
    if not request_data.confirm:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请确认执行操作"
        )
    
    service = PurchaseExecutionService(db)
    result = service.execute_batch(
        request_ids=request_data.request_ids,
        executor_id=current_user.id,
        executor_name=current_user.full_name or current_user.username,
        batch_name=request_data.batch_name,
        notes=request_data.notes
    )
    return result


@router.get("/batches", response_model=ExecutionBatchListResponse)
async def get_execution_batches(
    executor_id: Optional[int] = Query(None, description="执行人ID"),
    status: Optional[str] = Query(None, description="批次状态"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    batch_no: Optional[str] = Query(None, description="批次编号"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取执行批次列表"""
    service = PurchaseExecutionService(db)
    
    filters = ExecutionBatchFilters(
        executor_id=executor_id,
        status=status,
        start_date=start_date,
        end_date=end_date,
        batch_no=batch_no
    )
    
    result = service.get_execution_batches(filters, page, size)
    return ExecutionBatchListResponse(**result)


@router.get("/batches/{batch_id}", response_model=ExecutionBatchResponse)
async def get_execution_batch_detail(
    batch_id: int,
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取执行批次详情"""
    service = PurchaseExecutionService(db)
    batch = service.get_execution_batch_detail(batch_id)
    if not batch:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="执行批次不存在"
        )
    return batch


@router.get("/summary", response_model=ExecutionSummaryResponse)
async def get_execution_summary(
    batch_ids: Optional[List[int]] = Query(None, description="批次ID列表"),
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取执行汇总数据"""
    service = PurchaseExecutionService(db)
    summary = service.get_execution_summary(batch_ids)
    return ExecutionSummaryResponse(**summary)


@router.post("/export")
async def export_execution_data(
    export_request: ExecutionExportRequest,
    current_user: User = Depends(require_permissions(["purchase.export"])),
    db: Session = Depends(get_db)
):
    """导出执行数据"""
    # TODO: 实现导出功能
    # 这里可以根据export_request.export_format选择不同的导出格式
    # 例如Excel、PDF、CSV等
    
    service = PurchaseExecutionService(db)
    
    # 获取批次详情
    batches = []
    for batch_id in export_request.batch_ids:
        batch = service.get_execution_batch_detail(batch_id)
        if batch:
            batches.append(batch)
    
    if not batches:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="没有找到可导出的批次"
        )
    
    # 根据格式生成文件
    export_format = export_request.export_format.lower()
    
    if export_format == "xlsx":
        # TODO: 实现Excel导出
        return {"message": "Excel导出功能待实现", "batches_count": len(batches)}
    elif export_format == "pdf":
        # TODO: 实现PDF导出
        return {"message": "PDF导出功能待实现", "batches_count": len(batches)}
    elif export_format == "csv":
        # TODO: 实现CSV导出
        return {"message": "CSV导出功能待实现", "batches_count": len(batches)}
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的导出格式"
        )


@router.get("/statistics")
async def get_execution_statistics(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取执行统计数据"""
    service = PurchaseExecutionService(db)
    
    # 计算日期范围
    end_date = datetime.now()
    start_date = end_date.replace(day=end_date.day - days)
    
    # 获取统计数据
    filters = ExecutionBatchFilters(start_date=start_date, end_date=end_date)
    batches_result = service.get_execution_batches(filters, page=1, size=1000)
    
    # 计算统计指标
    total_batches = batches_result["total"]
    total_requests = sum(batch.request_count for batch in batches_result["batches"])
    total_amount = sum(float(batch.total_amount) for batch in batches_result["batches"])
    
    # 按执行人统计
    executor_stats = {}
    for batch in batches_result["batches"]:
        executor_id = batch.executor_id
        if executor_id not in executor_stats:
            executor_stats[executor_id] = {
                "executor_id": executor_id,
                "executor_name": batch.executor_name,
                "batch_count": 0,
                "request_count": 0,
                "total_amount": 0.0
            }
        executor_stats[executor_id]["batch_count"] += 1
        executor_stats[executor_id]["request_count"] += batch.request_count
        executor_stats[executor_id]["total_amount"] += float(batch.total_amount)
    
    return {
        "period_days": days,
        "total_batches": total_batches,
        "total_requests": total_requests,
        "total_amount": total_amount,
        "average_amount_per_batch": total_amount / total_batches if total_batches > 0 else 0,
        "average_requests_per_batch": total_requests / total_batches if total_batches > 0 else 0,
        "executor_statistics": list(executor_stats.values())
    }
