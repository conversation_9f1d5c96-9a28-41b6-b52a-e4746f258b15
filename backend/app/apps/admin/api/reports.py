"""
报表仪表板相关的API路由
"""
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_, literal
from typing import List, Optional
from datetime import datetime, timedelta
from decimal import Decimal

from app.core.database import get_db
from app.models.usage import ItemUsageRecord, UsageStatus
from app.models.item import Item, ItemCategory
from app.models.user import Department
from app.models.supplier import Supplier
from app.models.purchase import PurchaseRequest
from app.models.inventory import DepartmentInventory
from app.models.user import User
from app.schemas.reports import (
    DashboardOverview, ConsumptionReport, InventoryReport, 
    ProcurementReport, DepartmentUsageReport, TopItemsReport,
    TrendAnalysis, CostAnalysis
)
from app.services.auth import get_current_user, require_roles

router = APIRouter(tags=["报表管理"])


@router.get("/dashboard/overview", response_model=DashboardOverview)
def get_dashboard_overview(
    days: int = Query(30, ge=1, description="统计天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["admin", "item_manager"]))
):
    """获取仪表板概览数据"""
    start_date = datetime.now() - timedelta(days=days)
    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    
    # 基础统计
    total_items = db.query(func.count(Item.id)).scalar() or 0
    total_departments = db.query(func.count(Department.id)).scalar() or 0
    total_suppliers = db.query(func.count(Supplier.id)).scalar() or 0
    
    # 今日领取统计
    today_usages = db.query(func.count(ItemUsageRecord.id)).filter(
        ItemUsageRecord.usage_time >= today_start
    ).scalar() or 0
    
    # 本期领取统计
    period_usages = db.query(func.count(ItemUsageRecord.id)).filter(
        ItemUsageRecord.usage_time >= start_date
    ).scalar() or 0
    
    # 待确认领取
    pending_usages = db.query(func.count(ItemUsageRecord.id)).filter(
        ItemUsageRecord.status == UsageStatus.pending
    ).scalar() or 0
    
    # 本期总费用 - ItemUsageRecord没有total_amount字段，需要计算
    total_cost = db.query(func.sum(ItemUsageRecord.quantity * 0)).filter(
        and_(
            ItemUsageRecord.usage_time >= start_date,
            ItemUsageRecord.status == UsageStatus.confirmed
        )
    ).scalar() or Decimal("0")
    
    # 活跃用户数（本期有领取记录的用户）
    active_users = db.query(func.count(func.distinct(ItemUsageRecord.employee_id))).filter(
        ItemUsageRecord.usage_time >= start_date
    ).scalar() or 0
    
    # 低库存预警（固定阈值10）
    low_stock_items = db.query(func.count(Item.id)).filter(
        Item.total_stock <= 10
    ).scalar() or 0
    
    return DashboardOverview(
        total_items=total_items,
        total_departments=total_departments,
        total_suppliers=total_suppliers,
        today_usages=today_usages,
        period_usages=period_usages,
        pending_usages=pending_usages,
        total_cost=total_cost,
        active_users=active_users,
        low_stock_items=low_stock_items,
        period_days=days
    )


@router.get("/consumption", response_model=List[ConsumptionReport])
def get_consumption_report(
    start_date: datetime = Query(..., description="开始日期"),
    end_date: datetime = Query(..., description="结束日期"),
    department_id: Optional[int] = Query(None, description="部门筛选"),
    category_id: Optional[int] = Query(None, description="分类筛选"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["admin", "item_manager", "department_manager"]))
):
    """获取消耗报表"""
    query = db.query(
        Item.id.label("item_id"),
        Item.name.label("item_name"),
        Item.code.label("item_code"),
        Item.inventory_unit.label("unit"),
        func.sum(ItemUsageRecord.quantity).label("total_quantity"),
        func.sum(ItemUsageRecord.quantity * 0).label("total_amount"),  # 计算总金额，暂时设为0
        func.count(ItemUsageRecord.id).label("usage_count"),
        func.avg(ItemUsageRecord.quantity).label("avg_quantity")
    ).join(ItemUsageRecord).filter(
        and_(
            ItemUsageRecord.usage_time >= start_date,
            ItemUsageRecord.usage_time <= end_date,
            ItemUsageRecord.status == UsageStatus.confirmed
        )
    )
    
    # 应用筛选条件
    if department_id:
        query = query.filter(ItemUsageRecord.department_id == department_id)
    if category_id:
        query = query.filter(Item.category_id == category_id)
    
    # 按物品分组并排序
    results = query.group_by(
        Item.id, Item.name, Item.code, Item.inventory_unit
    ).order_by(desc(func.sum(ItemUsageRecord.quantity))).all()
    
    return [
        ConsumptionReport(
            item_id=result.item_id,
            item_name=result.item_name,
            item_code=result.item_code,
            unit=result.unit,
            total_quantity=result.total_quantity,
            total_amount=result.total_amount or Decimal("0"),  # 如果没有total_amount，设为0
            usage_count=result.usage_count,
            avg_quantity=result.avg_quantity
        )
        for result in results
    ]


@router.get("/inventory", response_model=List[InventoryReport])
def get_inventory_report(
    department_id: Optional[int] = Query(None, description="部门筛选"),
    low_stock_only: bool = Query(False, description="仅显示低库存"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["admin", "item_manager", "department_manager"]))
):
    """获取库存报表"""
    query = db.query(
        Item.id.label("item_id"),
        Item.name.label("item_name"),
        Item.code.label("item_code"),
        Item.inventory_unit.label("unit"),
        Item.total_stock.label("current_stock"),
        literal(10).label("min_stock"),
        Department.name.label("department_name")
    ).outerjoin(DepartmentInventory).outerjoin(Department)
    
    # 应用筛选条件
    if department_id:
        query = query.filter(DepartmentInventory.department_id == department_id)
    if low_stock_only:
        query = query.filter(Item.total_stock <= 10)
    
    results = query.order_by(Item.name).all()
    
    return [
        InventoryReport(
            item_id=r.item_id,
            item_name=r.item_name,
            item_code=r.item_code,
            inventory_unit=r.unit,
            current_stock=r.current_stock,
            min_stock=r.min_stock,
            stock_status="低库存" if r.current_stock <= r.min_stock else "正常",
            department_name=r.department_name or "未分配"
        ) for r in results
    ]


@router.get("/procurement", response_model=List[ProcurementReport])
def get_procurement_report(
    start_date: datetime = Query(..., description="开始日期"),
    end_date: datetime = Query(..., description="结束日期"),
    status: Optional[str] = Query(None, description="状态筛选"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["admin", "item_manager"]))
):
    """获取采购报表"""
    query = db.query(
        PurchaseRequest.id.label("request_id"),
        PurchaseRequest.request_code.label("request_code"),
        PurchaseRequest.status.label("status"),
        PurchaseRequest.total_amount.label("total_amount"),
        PurchaseRequest.created_at.label("created_at"),
        Department.name.label("department_name"),
        User.full_name.label("requester_name")
    ).join(Department).join(User).filter(
        and_(
            PurchaseRequest.created_at >= start_date,
            PurchaseRequest.created_at <= end_date
        )
    )
    
    if status:
        query = query.filter(PurchaseRequest.status == status)
    
    results = query.order_by(desc(PurchaseRequest.created_at)).all()
    
    return [
        ProcurementReport(
            request_id=r.request_id,
            request_code=r.request_code,
            status=r.status,
            total_amount=r.total_amount,
            created_at=r.created_at,
            department_name=r.department_name,
            requester_name=r.requester_name
        ) for r in results
    ]


@router.get("/department-usage", response_model=List[DepartmentUsageReport])
def get_department_usage_report(
    start_date: datetime = Query(..., description="开始日期"),
    end_date: datetime = Query(..., description="结束日期"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["admin", "item_manager"]))
):
    """获取部门使用情况报表"""
    # 部门使用报表
    results = db.query(
        Department.id.label("department_id"),
        Department.name.label("department_name"),
        func.count(ItemUsageRecord.id).label("usage_count"),
        func.sum(ItemUsageRecord.quantity).label("total_quantity"),
        func.sum(ItemUsageRecord.quantity * 0).label("total_amount"),  # 计算总金额，暂时设为0
        func.count(func.distinct(ItemUsageRecord.employee_id)).label("active_users"),
        func.count(func.distinct(ItemUsageRecord.item_id)).label("item_types")
    ).join(ItemUsageRecord).filter(
        and_(
            ItemUsageRecord.usage_time >= start_date,
            ItemUsageRecord.usage_time <= end_date,
            ItemUsageRecord.status == UsageStatus.confirmed
        )
    ).group_by(Department.id, Department.name).order_by(
        desc(func.sum(ItemUsageRecord.quantity * 0))  # 按总金额排序，暂时设为0
    ).all()
    
    return [
        DepartmentUsageReport(
            department_id=r.department_id,
            department_name=r.department_name,
            usage_count=r.usage_count,
            total_quantity=int(r.total_quantity),
            total_amount=r.total_amount,
            active_users=r.active_users,
            item_types=r.item_types
        ) for r in results
    ]


@router.get("/top-items", response_model=TopItemsReport)
def get_top_items_report(
    limit: int = Query(10, ge=1, le=50, description="返回数量"),
    days: int = Query(30, ge=1, description="统计天数"),
    sort_by: str = Query("quantity", description="排序方式: quantity, amount, frequency"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["admin", "item_manager"]))
):
    """获取热门物品报表"""
    start_date = datetime.now() - timedelta(days=days)
    
    # 根据排序方式选择排序字段
    sort_column = {
        "quantity": func.sum(ItemUsageRecord.quantity),
        "amount": func.sum(ItemUsageRecord.total_amount),
        "frequency": func.count(ItemUsageRecord.id)
    }.get(sort_by, func.sum(ItemUsageRecord.quantity))
    
    results = db.query(
        Item.id.label("item_id"),
        Item.name.label("item_name"),
        Item.code.label("item_code"),
        Item.inventory_unit.label("unit"),
        func.sum(ItemUsageRecord.quantity).label("total_quantity"),
        func.sum(ItemUsageRecord.total_amount).label("total_amount"),
        func.count(ItemUsageRecord.id).label("usage_frequency")
    ).join(ItemUsageRecord).filter(
        and_(
            ItemUsageRecord.usage_time >= start_date,
            ItemUsageRecord.status == UsageStatus.confirmed
        )
    ).group_by(
        Item.id, Item.name, Item.code, Item.inventory_unit
    ).order_by(desc(sort_column)).limit(limit).all()
    
    items = [
        {
            "item_id": r.item_id,
            "item_name": r.item_name,
            "item_code": r.item_code,
            "inventory_unit": r.unit,
            "total_quantity": int(r.total_quantity),
            "total_amount": float(r.total_amount),
            "usage_frequency": r.usage_frequency
        } for r in results
    ]
    
    return TopItemsReport(
        period_days=days,
        sort_by=sort_by,
        total_items=len(items),
        items=items
    )


@router.get("/trend-analysis", response_model=TrendAnalysis)
def get_trend_analysis(
    days: int = Query(30, ge=7, le=365, description="分析天数"),
    granularity: str = Query("daily", description="粒度: daily, weekly, monthly"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["admin", "item_manager"]))
):
    """获取趋势分析数据"""
    start_date = datetime.now() - timedelta(days=days)
    
    # 根据粒度确定日期格式
    date_format = {
        "daily": "%Y-%m-%d",
        "weekly": "%Y-%U",  # 年-周
        "monthly": "%Y-%m"
    }.get(granularity, "%Y-%m-%d")
    
    # 使用量趋势
    usage_trend = db.query(
        func.strftime(date_format, ItemUsageRecord.usage_time).label("period"),
        func.count(ItemUsageRecord.id).label("usage_count"),
        func.sum(ItemUsageRecord.quantity).label("total_quantity"),
        func.sum(ItemUsageRecord.quantity * 0).label("total_amount")  # 计算总金额，暂时设为0
    ).filter(
        and_(
            ItemUsageRecord.usage_time >= start_date,
            ItemUsageRecord.status == UsageStatus.confirmed
        )
    ).group_by(
        func.strftime(date_format, ItemUsageRecord.usage_time)
    ).order_by("period").all()
    
    trend_data = [
        {
            "period": t.period,
            "usage_count": t.usage_count,
            "total_quantity": int(t.total_quantity),
            "total_amount": float(t.total_amount)
        } for t in usage_trend
    ]
    
    return TrendAnalysis(
        period_days=days,
        granularity=granularity,
        data_points=len(trend_data),
        trend_data=trend_data
    )


@router.get("/cost-analysis", response_model=CostAnalysis)
def get_cost_analysis(
    start_date: datetime = Query(..., description="开始日期"),
    end_date: datetime = Query(..., description="结束日期"),
    group_by: str = Query("department", description="分组方式: department, category, item"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["admin", "item_manager"]))
):
    """获取成本分析数据"""
    if group_by == "department":
        # 按部门分组
        results = db.query(
            Department.id.label("group_id"),
            Department.name.label("group_name"),
            func.sum(ItemUsageRecord.quantity * 0).label("total_cost"),  # 计算总成本，暂时设为0
            func.count(ItemUsageRecord.id).label("usage_count")
        ).join(ItemUsageRecord).filter(
            and_(
                ItemUsageRecord.usage_time >= start_date,
                ItemUsageRecord.usage_time <= end_date,
                ItemUsageRecord.status == UsageStatus.confirmed
            )
        ).group_by(Department.id, Department.name).all()
        
    elif group_by == "category":
        # 按分类分组
        results = db.query(
            ItemCategory.id.label("group_id"),
            ItemCategory.name.label("group_name"),
            func.sum(ItemUsageRecord.quantity * 0).label("total_cost"),  # 计算总成本，暂时设为0
            func.count(ItemUsageRecord.id).label("usage_count")
        ).join(Item).join(ItemUsageRecord).filter(
            and_(
                ItemUsageRecord.usage_time >= start_date,
                ItemUsageRecord.usage_time <= end_date,
                ItemUsageRecord.status == UsageStatus.confirmed
            )
        ).group_by(ItemCategory.id, ItemCategory.name).all()
        
    elif group_by == "item":
        # 按物品分组
        results = db.query(
            Item.id.label("group_id"),
            Item.name.label("group_name"),
            func.sum(ItemUsageRecord.quantity * 0).label("total_cost"),  # 计算总成本，暂时设为0
            func.count(ItemUsageRecord.id).label("usage_count")
        ).join(ItemUsageRecord).filter(
            and_(
                ItemUsageRecord.usage_time >= start_date,
                ItemUsageRecord.usage_time <= end_date,
                ItemUsageRecord.status == UsageStatus.confirmed
            )
        ).group_by(Item.id, Item.name).all()
    
    total_cost = sum(r.total_cost for r in results)
    
    cost_data = [
        {
            "group_id": r.group_id,
            "group_name": r.group_name,
            "total_cost": float(r.total_cost),
            "usage_count": r.usage_count,
            "percentage": float(r.total_cost / total_cost * 100) if total_cost > 0 else 0
        } for r in results
    ]
    
    return CostAnalysis(
        group_by=group_by,
        total_cost=float(total_cost),
        group_count=len(cost_data),
        cost_data=sorted(cost_data, key=lambda x: x["total_cost"], reverse=True)
    ) 