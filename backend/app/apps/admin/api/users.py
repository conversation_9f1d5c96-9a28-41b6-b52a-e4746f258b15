"""
用户管理API
提供完整的用户管理功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_, func
from typing import List, Optional

from datetime import datetime, timedelta, timezone

from app.core.database import get_db
from app.models.user import User
from app.models.permission import Role
from app.models.user import Department
from app.schemas.user import (
    User as UserSchema, UserCreate, UserUpdate, UserPasswordUpdate, 
    UserPasswordReset, UserProfile, UserListQuery, AccountStatus
)
from app.services.auth import (
    get_current_user, require_permissions, require_any, get_password_hash,
    log_action, check_account_status, update_login_info, verify_password
)
from app.services.data_filter import create_data_filter, check_user_management

router = APIRouter(tags=["用户管理"])

@router.get("", response_model=dict)
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="页大小"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    department_id: Optional[int] = Query(None, description="部门ID"),
    roles: Optional[List[str]] = Query(None, description="角色筛选"),
    role: Optional[str] = Query(None, description="单个角色筛选（向后兼容）"),
    account_status: Optional[AccountStatus] = Query(None, description="账号状态"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    current_user: User = Depends(require_permissions(["user.read"])),
    db: Session = Depends(get_db)
):
    """获取用户列表"""
    # 创建数据过滤器
    data_filter = create_data_filter(db, current_user)
    
    # 基础查询
    query = db.query(User).join(Department, User.department_id == Department.id, isouter=True)
    
    # 应用数据权限过滤
    accessible_user_ids = data_filter.get_accessible_user_ids()
    if accessible_user_ids is not None:
        query = query.filter(User.id.in_(accessible_user_ids))
    
    # 搜索过滤
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                User.username.ilike(search_term),
                User.full_name.ilike(search_term),
                User.display_name.ilike(search_term),
                User.email.ilike(search_term),
                User.employee_id.ilike(search_term),
                Department.name.ilike(search_term)
            )
        )
    
    # 部门过滤
    if department_id:
        query = query.filter(User.department_id == department_id)
    
    # 账号状态过滤
    if account_status:
        query = query.filter(User.account_status == account_status)
    
    # 激活状态过滤
    if is_active is not None:
        query = query.filter(User.is_active == is_active)
    
    # 角色过滤
    if roles:
        # 通过角色代码数组查询
        role_ids = db.query(Role.id).filter(Role.code.in_(roles)).all()
        role_id_list = [r.id for r in role_ids]
        query = query.filter(User.role_id.in_(role_id_list))
    elif role:
        # 通过单个角色代码查询（向后兼容）
        role_obj = db.query(Role).filter(Role.code == role).first()
        if role_obj:
            query = query.filter(User.role_id == role_obj.id)
    
    # 分页
    total = query.count()
    users = query.offset((page - 1) * page_size).limit(page_size).all()
    
    # 构建响应数据
    user_list = []
    for user in users:
        user_data = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "display_name": user.display_name,
            "phone": user.phone,
            "employee_id": user.employee_id,
            "position": user.position,
            "avatar": user.avatar,
            "is_active": user.is_active,
            "is_superuser": user.is_superuser,
            "account_status": user.account_status,
            "password_status": user.password_status,
            "department_id": user.department_id,
            "department_name": user.department.name if user.department else None,
            "role_id": user.role_id,
            "last_login_at": user.last_login_at,
            "created_at": user.created_at
        }
        user_list.append(user_data)
    
    from app.core.utils import paginate_response
    return paginate_response(user_list, total, page, page_size)

@router.get("/{user_id}", response_model=UserProfile)
async def get_user(
    user_id: int,
    current_user: User = Depends(require_permissions(["user.read"])),
    db: Session = Depends(get_db)
):
    """获取用户详情"""
    # 检查数据访问权限
    data_filter = create_data_filter(db, current_user)
    if not data_filter.can_access_user(user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问该用户"
        )
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 构建用户资料
    profile = UserProfile(
        id=user.id,
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        display_name=user.display_name,
        phone=user.phone,
        employee_id=user.employee_id,
        position=user.position,
        avatar=user.avatar,
        department_name=user.department.name if user.department else None,
        supervisor_name=user.supervisor.full_name if hasattr(user, 'supervisor') and user.supervisor else None,
        role_id=user.role_id,
        last_login_at=user.last_login_at,
        created_at=user.created_at
    )
    
    return profile

@router.post("", response_model=UserSchema)
async def create_user(
    user_data: UserCreate,
    request: Request,
    current_user: User = Depends(require_permissions(["user.create"])),
    db: Session = Depends(get_db)
):
    """创建用户"""
    # 检查用户名是否已存在
    existing_user = db.query(User).filter(User.username == user_data.username).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    existing_email = db.query(User).filter(User.email == user_data.email).first()
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )
    
    # 检查工号是否已存在
    if user_data.employee_id:
        existing_employee = db.query(User).filter(User.employee_id == user_data.employee_id).first()
        if existing_employee:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="工号已存在"
            )
    
    # 创建用户
    hashed_password = get_password_hash(user_data.password)
    user = User(
        username=user_data.username,
        email=user_data.email,
        hashed_password=hashed_password,
        full_name=user_data.full_name,
        display_name=user_data.display_name,
        phone=user_data.phone,
        employee_id=user_data.employee_id,
        position=user_data.position,
        department_id=user_data.department_id,
        role_id=user_data.role_id,
        account_status=user_data.account_status,
        is_active=True
    )
    
    db.add(user)
    db.commit()
    db.refresh(user)
    
    # 记录操作日志
    log_action(
        db, current_user.id, "user.create", "user", user.id,
        None, f"创建用户: {user.username}", request
    )
    
    # 返回200状态码表示创建成功
    from app.core.responses import CustomJSONResponse
    return CustomJSONResponse(
        status_code=200,
        content={
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "display_name": user.display_name,
            "phone": user.phone,
            "employee_id": user.employee_id,
            "position": user.position,
            "is_active": user.is_active,
            "is_superuser": user.is_superuser,
            "account_status": user.account_status,
            "password_status": user.password_status,
            "department_id": user.department_id,
            "role_id": user.role_id,
            "created_at": user.created_at
        }
    )

@router.put("/{user_id}", response_model=UserSchema)
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    request: Request,
    current_user: User = Depends(require_permissions(["user.update"])),
    db: Session = Depends(get_db)
):
    """更新用户信息"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查权限
    data_filter = create_data_filter(db, current_user)
    if not data_filter.can_access_user(user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改该用户"
        )
    
    # 记录原始值
    old_values = {
        "username": user.username,
        "email": user.email,
        "full_name": user.full_name,
        "display_name": user.display_name,
        "phone": user.phone,
        "employee_id": user.employee_id,
        "position": user.position,
        "department_id": user.department_id,
        "role_id": user.role_id,
        "is_active": user.is_active,
        "account_status": user.account_status
    }
    
    # 更新字段
    update_fields = user_data.model_dump(exclude_unset=True)
    for field, value in update_fields.items():
        setattr(user, field, value)
    
    db.commit()
    db.refresh(user)
    
    # 记录操作日志
    log_action(
        db, current_user.id, "user.update", "user", user.id,
        str(old_values), str(update_fields), request
    )
    
    return user

@router.post("/{user_id}/reset-password")
async def reset_user_password(
    user_id: int,
    password_data: UserPasswordReset,
    request: Request,
    current_user: User = Depends(require_permissions(["user.reset_password"])),
    db: Session = Depends(get_db)
):
    """重置用户密码"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查权限
    data_filter = create_data_filter(db, current_user)
    if not data_filter.can_access_user(user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限重置该用户密码"
        )
    
    # 更新密码
    hashed_password = get_password_hash(password_data.new_password)
    user.hashed_password = hashed_password
    user.password_changed_at = datetime.now()
    user.password_status = "temporary" if password_data.is_temporary else "normal"
    
    db.commit()
    
    # 记录操作日志
    log_action(
        db, current_user.id, "user.reset_password", "user", user.id,
        None, "密码已重置", request
    )
    
    return {"message": "密码重置成功"}

@router.post("/{user_id}/toggle-status")
async def toggle_user_status(
    user_id: int,
    request: Request,
    current_user: User = Depends(require_permissions(["user.manage_status"])),
    db: Session = Depends(get_db)
):
    """切换用户状态"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查权限
    data_filter = create_data_filter(db, current_user)
    if not data_filter.can_access_user(user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改该用户状态"
        )
    
    # 切换状态
    old_status = user.is_active
    user.is_active = not user.is_active
    user.account_status = "active" if user.is_active else "disabled"
    
    db.commit()
    
    # 记录操作日志
    log_action(
        db, current_user.id, "user.toggle_status", "user", user.id,
        str(old_status), str(user.is_active), request
    )
    
    return {
        "message": f"用户状态已{'启用' if user.is_active else '禁用'}",
        "is_active": user.is_active
    }

@router.post("/batch/toggle-status")
async def batch_toggle_user_status(
    user_ids: List[int],
    action: str,  # "enable" or "disable"
    request: Request,
    current_user: User = Depends(require_permissions(["user.manage_status"])),
    db: Session = Depends(get_db)
):
    """批量切换用户状态"""
    if action not in ["enable", "disable"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的操作类型"
        )
    
    # 检查数据访问权限
    data_filter = create_data_filter(db, current_user)
    accessible_user_ids = data_filter.get_accessible_user_ids()
    
    if accessible_user_ids is not None:
        # 过滤出有权限访问的用户ID
        user_ids = [uid for uid in user_ids if uid in accessible_user_ids]
    
    if not user_ids:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限操作任何用户"
        )
    
    # 批量更新
    is_active = action == "enable"
    account_status = "active" if is_active else "disabled"
    
    users = db.query(User).filter(User.id.in_(user_ids)).all()
    updated_count = 0
    
    for user in users:
        user.is_active = is_active
        user.account_status = account_status
        updated_count += 1
    
    db.commit()
    
    # 记录操作日志
    log_action(
        db, current_user.id, "user.batch_toggle_status", "user", None,
        None, f"批量{action}了{updated_count}个用户", request
    )
    
    return {
        "message": f"成功{action}了{updated_count}个用户",
        "updated_count": updated_count
    }

@router.post("/batch/assign-role")
async def batch_assign_role(
    user_ids: List[int],
    role_id: int,
    request: Request,
    current_user: User = Depends(require_permissions(["role.assign"])),
    db: Session = Depends(get_db)
):
    """批量分配角色"""
    # 检查角色是否存在
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    # 检查数据访问权限
    data_filter = create_data_filter(db, current_user)
    accessible_user_ids = data_filter.get_accessible_user_ids()
    
    if accessible_user_ids is not None:
        # 过滤出有权限访问的用户ID
        user_ids = [uid for uid in user_ids if uid in accessible_user_ids]
    
    if not user_ids:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限操作任何用户"
        )
    
    # 批量更新
    users = db.query(User).filter(User.id.in_(user_ids)).all()
    updated_count = 0
    
    for user in users:
        user.role_id = role_id
        updated_count += 1
    
    db.commit()
    
    # 记录操作日志
    log_action(
        db, current_user.id, "user.batch_assign_role", "user", None,
        None, f"批量分配角色{role.name}给{updated_count}个用户", request
    )
    
    return {
        "message": f"成功为{updated_count}个用户分配角色",
        "updated_count": updated_count,
        "role_name": role.name
    }

@router.get("/me/profile", response_model=UserProfile)
async def get_my_profile(current_user: User = Depends(get_current_user)):
    """获取当前用户个人资料"""
    return UserProfile(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        full_name=current_user.full_name,
        display_name=current_user.display_name,
        phone=current_user.phone,
        employee_id=current_user.employee_id,
        position=current_user.position,
        avatar=current_user.avatar,
        department_name=current_user.department.name if current_user.department else None,
        supervisor_name=current_user.supervisor.full_name if hasattr(current_user, 'supervisor') and current_user.supervisor else None,
        role_id=current_user.role_id,
        last_login_at=current_user.last_login_at,
        created_at=current_user.created_at
    )

@router.put("/me/profile", response_model=UserProfile)
async def update_my_profile(
    profile_data: dict,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新当前用户个人资料"""
    # 只允许更新特定字段
    allowed_fields = ["display_name", "phone", "avatar"]
    update_data = {k: v for k, v in profile_data.items() if k in allowed_fields}
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="没有可更新的字段"
        )
    
    # 更新字段
    for field, value in update_data.items():
        setattr(current_user, field, value)
    
    db.commit()
    db.refresh(current_user)
    
    # 记录操作日志
    log_action(
        db, current_user.id, "user.update_profile", "user", current_user.id,
        None, str(update_data), request
    )
    
    return UserProfile(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        full_name=current_user.full_name,
        display_name=current_user.display_name,
        phone=current_user.phone,
        employee_id=current_user.employee_id,
        position=current_user.position,
        avatar=current_user.avatar,
        department_name=current_user.department.name if current_user.department else None,
        supervisor_name=current_user.supervisor.full_name if hasattr(current_user, 'supervisor') and current_user.supervisor else None,
        role_id=current_user.role_id,
        last_login_at=current_user.last_login_at,
        created_at=current_user.created_at
    )

@router.post("/me/change-password")
async def change_my_password(
    password_data: UserPasswordUpdate,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """修改当前用户密码"""
    # 验证旧密码
    if not verify_password(password_data.old_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="旧密码错误"
        )
    
    # 更新密码
    hashed_password = get_password_hash(password_data.new_password)
    current_user.hashed_password = hashed_password
    current_user.password_changed_at = datetime.now()
    current_user.password_status = "normal"
    
    db.commit()
    
    # 记录操作日志
    log_action(
        db, current_user.id, "user.change_password", "user", current_user.id,
        None, "密码已修改", request
    )
    
    return {"message": "密码修改成功"} 