"""
角色权限管理API
提供角色和权限的完整管理功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_, func
from typing import List, Optional

from app.core.database import get_db
from app.models.user import User
from app.models.permission import Role, Permission
from app.schemas.permission import (
    Role as RoleSchema, RoleCreate, RoleUpdate,
    Permission as PermissionSchema, PermissionCreate, PermissionUpdate,
    UserRoleAssign, UserPermissions, PermissionNode,
    RolePermissionMatrix
)
from app.services.auth import require_permissions, require_any, get_current_user, log_action
from app.core.permissions import PERMISSION_DEFINITIONS, ROLE_DEFINITIONS, PERMISSION_GROUPS

router = APIRouter(tags=["角色管理"])

# ========== 权限管理 ==========

@router.get("/permissions", response_model=List[PermissionSchema])
async def get_permissions(
    module: Optional[str] = Query(None, description="模块筛选"),

    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: User = Depends(require_permissions(["permission.read"])),
    db: Session = Depends(get_db)
):
    """获取权限列表"""
    query = db.query(Permission)
    
    if module:
        query = query.filter(Permission.module == module)
    

    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Permission.name.ilike(search_term),
                Permission.code.ilike(search_term),
                Permission.description.ilike(search_term)
            )
        )
    
    permissions = query.filter(Permission.is_active == True).order_by(Permission.module, Permission.code).all()
    return permissions

@router.get("/permissions/groups", response_model=List[PermissionNode])
async def get_permission_groups(
    current_user: User = Depends(require_permissions(["permission.read"])),
    db: Session = Depends(get_db)
):
    """获取权限列表（按模块分组）"""
    permissions = db.query(Permission).filter(Permission.is_active == True).all()
    
    # 模块名称映射
    module_names = {
        "user": "用户管理",
        "role": "角色管理", 
        "item": "物品管理",
        "inventory": "库存管理",
        "purchase": "采购管理",
        "supplier": "供应商管理",
        "report": "报表分析",
        "department": "部门管理",
        "system": "系统管理"
    }
    
    # 按模块分组权限
    module_dict = {}
    for perm in permissions:
        if perm.module not in module_dict:
            module_dict[perm.module] = []
        module_dict[perm.module].append({
            "id": perm.id,
            "code": perm.code,
            "name": perm.name,
            "description": perm.description,
            "module": perm.module,
            "is_active": perm.is_active,
            "is_system": perm.is_system,
            "created_at": perm.created_at
        })
    
    # 转换为分组数组结构  
    groups = []
    for module, module_permissions in module_dict.items():
        # 为模块创建虚拟ID (使用负数避免与实际权限ID冲突)
        module_id = -(len(groups) + 1)
        groups.append({
            "id": module_id,
            "code": module,
            "name": module_names.get(module, module),
            "description": f"{module_names.get(module, module)}模块权限",
            "module": module,
            "is_active": True,
            "permissions": module_permissions
        })
    
    return groups

@router.post("/permissions", response_model=PermissionSchema)
async def create_permission(
    permission_data: PermissionCreate,
    request: Request,
    current_user: User = Depends(require_permissions(["permission.create"])),
    db: Session = Depends(get_db)
):
    """创建权限"""
    # 检查权限代码是否已存在
    existing_permission = db.query(Permission).filter(Permission.code == permission_data.code).first()
    if existing_permission:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="权限代码已存在"
        )
    
    permission = Permission(
        code=permission_data.code,
        name=permission_data.name,
        description=permission_data.description,
        module=permission_data.module,


        is_active=permission_data.is_active,
        created_by=current_user.id
    )
    
    db.add(permission)
    db.commit()
    db.refresh(permission)
    
    # 记录操作日志
    log_action(
        db, current_user.id, "permission_created", "permission", permission.id,
        ip_address=request.client.host,
        new_value=f"Created permission: {permission.code}"
    )
    
    return permission

# ========== 角色管理 ==========

@router.get("", response_model=List[RoleSchema])
async def get_roles(
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    current_user: User = Depends(require_permissions(["role.read"])),
    db: Session = Depends(get_db)
):
    """获取角色列表"""
    query = db.query(Role)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Role.name.ilike(search_term),
                Role.code.ilike(search_term),
                Role.description.ilike(search_term)
            )
        )
    
    if is_active is not None:
        query = query.filter(Role.is_active == is_active)
    
    roles = query.order_by(Role.name).all()
    return roles

@router.get("/{role_id}", response_model=RoleSchema)
async def get_role(
    role_id: int,
    current_user: User = Depends(require_permissions(["role.read"])),
    db: Session = Depends(get_db)
):
    """获取角色详情"""
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    return role

@router.post("", response_model=RoleSchema)
async def create_role(
    role_data: RoleCreate,
    request: Request,
    current_user: User = Depends(require_permissions(["role.create"])),
    db: Session = Depends(get_db)
):
    """创建角色"""
    # 检查角色代码是否已存在
    existing_role = db.query(Role).filter(Role.code == role_data.code).first()
    if existing_role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="角色代码已存在"
        )
    
    role = Role(
        code=role_data.code,
        name=role_data.name,
        description=role_data.description,


        is_active=role_data.is_active,
        created_by=current_user.id
    )
    
    db.add(role)
    db.commit()
    db.refresh(role)
    
    # 分配权限
    if role_data.permission_ids:
        permissions = db.query(Permission).filter(Permission.id.in_(role_data.permission_ids)).all()
        role.permissions.extend(permissions)
        db.commit()
    
    # 记录操作日志
    log_action(
        db, current_user.id, "role_created", "role", role.id,
        ip_address=request.client.host,
        new_value=f"Created role: {role.code}"
    )
    
    return role

@router.put("/{role_id}", response_model=RoleSchema)
async def update_role(
    role_id: int,
    role_data: RoleUpdate,
    request: Request,
    current_user: User = Depends(require_permissions(["role.update"])),
    db: Session = Depends(get_db)
):
    """更新角色"""
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    # 系统角色不允许修改
    if role.is_system:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="系统角色不允许修改"
        )
    
    # 记录原始值
    old_value = f"name: {role.name}, permissions: {len(role.permissions)}"
    
    # 更新角色信息
    update_data = role_data.dict(exclude_unset=True, exclude={'permission_ids'})
    for field, value in update_data.items():
        if hasattr(role, field):
            setattr(role, field, value)
    
    # 更新权限
    if role_data.permission_ids is not None:
        # 清除现有权限
        role.permissions.clear()
        # 添加新权限
        if role_data.permission_ids:
            permissions = db.query(Permission).filter(Permission.id.in_(role_data.permission_ids)).all()
            role.permissions.extend(permissions)
    
    db.commit()
    db.refresh(role)
    
    # 记录操作日志
    new_value = f"name: {role.name}, permissions: {len(role.permissions)}"
    log_action(
        db, current_user.id, "role_updated", "role", role.id,
        old_value=old_value, new_value=new_value,
        ip_address=request.client.host
    )
    
    return role

@router.delete("/{role_id}")
async def delete_role(
    role_id: int,
    request: Request,
    current_user: User = Depends(require_permissions(["role.delete"])),
    db: Session = Depends(get_db)
):
    """删除角色"""
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    # 系统角色不允许删除
    if role.is_system:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="系统角色不允许删除"
        )
    
    # 检查是否有用户使用该角色
    users_count = db.query(User).filter(User.role_id == role_id).count()
    
    if users_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该角色正在被用户使用，无法删除"
        )
    
    db.delete(role)
    db.commit()
    
    # 记录操作日志
    log_action(
        db, current_user.id, "role_deleted", "role", role_id,
        ip_address=request.client.host,
        new_value=f"Deleted role: {role.code}"
    )
    
    return {"message": "角色删除成功"}

# ========== 用户角色分配 ==========

@router.post("/assign")
async def assign_user_roles(
    assignment: UserRoleAssign,
    request: Request,
    current_user: User = Depends(require_permissions(["role.assign"])),
    db: Session = Depends(get_db)
):
    """为用户分配角色"""
    user = db.query(User).filter(User.id == assignment.user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查角色是否存在
    role = db.query(Role).filter(Role.id == assignment.role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="角色不存在"
        )
    
    # 记录原始角色
    old_role_id = user.role_id
    
    # 更新用户角色
    user.role_id = role.id
    
    db.commit()
    
    # 记录操作日志
    log_action(
        db, current_user.id, "role_assigned", "user", user.id,
        old_value=f"role_id: {old_role_id}",
        new_value=f"role_id: {role.id}",
        ip_address=request.client.host
    )
    
    return {"message": "角色分配成功", "role": role.name}

@router.get("/users/{user_id}/permissions", response_model=UserPermissions)
async def get_user_permissions(
    user_id: int,
    current_user: User = Depends(require_permissions(["role.read"])),
    db: Session = Depends(get_db)
):
    """获取用户的所有权限"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    from app.services.auth import get_user_permissions
    
    # 获取用户的所有权限
    user_permissions = get_user_permissions(db, user)
    
    # 获取用户角色
    user_role = None
    if user.role_id:
        user_role = db.query(Role).filter(Role.id == user.role_id).first()
    
    return UserPermissions(
        user_id=user_id,
        role=user_role,
        all_permissions=user_permissions
    )

@router.get("/matrix")
async def get_role_permission_matrix(
    current_user: User = Depends(require_permissions(["role.read"])),
    db: Session = Depends(get_db)
):
    """获取角色权限矩阵"""
    roles = db.query(Role).filter(Role.is_active == True).all()
    permissions = db.query(Permission).filter(Permission.is_active == True).all()
    
    matrix = []
    for role in roles:
        role_permissions = [perm for perm in role.permissions if perm.is_active]
        matrix.append({
            "role": role,
            "permissions": role_permissions,
            "permission_codes": [perm.code for perm in role_permissions]
        })
    
    return {
        "roles": roles,
        "permissions": permissions,
        "matrix": matrix
    }

# ========== 系统初始化 ==========

@router.post("/init-system-data")
async def init_system_permissions_and_roles(
    request: Request,
    current_user: User = Depends(require_any(roles=["admin", "super_admin"])),
    db: Session = Depends(get_db)
):
    """初始化系统权限和角色数据"""
    try:
        created_permissions = 0
        created_roles = 0
        
        # 1. 创建权限
        for perm_data in PERMISSION_DEFINITIONS:
            code, name, description, module, category = perm_data
            
            # 检查权限是否已存在
            existing_perm = db.query(Permission).filter(Permission.code == code).first()
            if not existing_perm:
                permission = Permission(
                    code=code,
                    name=name,
                    description=description,
                    module=module,
                    category=category,
                    is_active=True,
                    is_system=True,
                    created_by=current_user.id
                )
                db.add(permission)
                created_permissions += 1
        
        db.commit()
        
        # 2. 创建角色
        for role_data in ROLE_DEFINITIONS:
            code, name, description, permission_codes = role_data
            
            # 检查角色是否已存在
            existing_role = db.query(Role).filter(Role.code == code).first()
            if not existing_role:
                role = Role(
                    code=code,
                    name=name,
                    description=description,

                    is_active=True,
                    is_system=True,
                    created_by=current_user.id
                )
                db.add(role)
                db.commit()
                db.refresh(role)
                
                # 分配权限
                permissions = db.query(Permission).filter(Permission.code.in_(permission_codes)).all()
                role.permissions.extend(permissions)
                
                created_roles += 1
        
        db.commit()
        
        # 记录操作日志
        log_action(
            db, current_user.id, "system_data_initialized", "system", None,
            ip_address=request.client.host,
            new_value=f"Created {created_permissions} permissions and {created_roles} roles"
        )
        
        return {
            "message": "系统数据初始化完成",
            "created_permissions": created_permissions,
            "created_roles": created_roles
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"初始化失败: {str(e)}"
        )

@router.get("/system-info")
async def get_system_role_info(
    current_user: User = Depends(require_permissions(["role.read"]))
):
    """获取系统角色权限信息"""
    return {
        "predefined_permissions": len(PERMISSION_DEFINITIONS),
        "predefined_roles": len(ROLE_DEFINITIONS),
        "permission_groups": PERMISSION_GROUPS,
        "role_definitions": [
            {
                "code": role[0],
                "name": role[1],
                "description": role[2],
                "permission_count": len(role[3]),

            }
            for role in ROLE_DEFINITIONS
        ]
    } 