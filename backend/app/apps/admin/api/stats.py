from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.auth import get_current_user
from app.models.user import User
from app.services.notification_service import NotificationService
from app.services.email_service import EmailService
from app.schemas.notification import (
    NotificationStatsResponse, EmailStatsResponse
)

router = APIRouter()


@router.get("/notifications", response_model=NotificationStatsResponse)
async def get_notification_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取通知统计信息"""
    service = NotificationService(db)
    
    if current_user.is_superuser:
        # 超级管理员可以查看所有通知的统计
        stats = service.get_notification_stats()
    else:
        # 普通用户只能查看自己的通知统计
        stats = service.get_notification_stats(current_user.id)
    
    return stats


@router.get("/emails", response_model=EmailStatsResponse)
async def get_email_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取邮件发送统计信息（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    service = EmailService(db)
    stats = service.get_email_stats()
    
    return stats


@router.get("/dashboard")
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取仪表板统计数据"""
    notification_service = NotificationService(db)
    
    # 获取通知统计
    if current_user.is_superuser:
        notification_stats = notification_service.get_notification_stats()
    else:
        notification_stats = notification_service.get_notification_stats(current_user.id)
    
    # 构建仪表板数据
    dashboard_data = {
        "notifications": notification_stats,
        "user_info": {
            "id": current_user.id,
            "username": current_user.username,
            "full_name": current_user.full_name,
            "is_superuser": current_user.is_superuser
        }
    }
    
    # 如果是超级管理员，添加邮件统计
    if current_user.is_superuser:
        email_service = EmailService(db)
        email_stats = email_service.get_email_stats()
        dashboard_data["emails"] = email_stats
    
    return dashboard_data


@router.get("/notifications/trend")
async def get_notification_trend(
    days: int = 7,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取通知趋势统计（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    if days < 1 or days > 30:
        raise HTTPException(status_code=400, detail="天数必须在1-30之间")
    
    # 这里可以扩展为实际的趋势统计
    # 目前返回模拟数据
    from datetime import datetime, timedelta
    
    trend_data = []
    for i in range(days):
        date = datetime.utcnow().date() - timedelta(days=i)
        trend_data.append({
            "date": date.isoformat(),
            "total": 0,  # 可以扩展为实际统计
            "unread": 0,
            "read": 0
        })
    
    return {
        "days": days,
        "trend": trend_data
    }


@router.get("/emails/trend")
async def get_email_trend(
    days: int = 7,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取邮件发送趋势统计（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    if days < 1 or days > 30:
        raise HTTPException(status_code=400, detail="天数必须在1-30之间")
    
    # 这里可以扩展为实际的趋势统计
    # 目前返回模拟数据
    from datetime import datetime, timedelta
    
    trend_data = []
    for i in range(days):
        date = datetime.utcnow().date() - timedelta(days=i)
        trend_data.append({
            "date": date.isoformat(),
            "sent": 0,  # 可以扩展为实际统计
            "failed": 0,
            "pending": 0
        })
    
    return {
        "days": days,
        "trend": trend_data
    }


@router.get("/notifications/by-type")
async def get_notifications_by_type(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取按类型分类的通知统计"""
    service = NotificationService(db)
    
    if current_user.is_superuser:
        stats = service.get_notification_stats()
    else:
        stats = service.get_notification_stats(current_user.id)
    
    # 按类型统计
    type_stats = stats.get("by_type", {})
    
    return {
        "total": stats["total"],
        "by_type": type_stats,
        "types": list(type_stats.keys())
    }


@router.get("/notifications/by-status")
async def get_notifications_by_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取按状态分类的通知统计"""
    service = NotificationService(db)
    
    if current_user.is_superuser:
        stats = service.get_notification_stats()
    else:
        stats = service.get_notification_stats(current_user.id)
    
    return {
        "total": stats["total"],
        "unread": stats["unread"],
        "read": stats["read"],
        "unread_percentage": round((stats["unread"] / stats["total"] * 100) if stats["total"] > 0 else 0, 2),
        "read_percentage": round((stats["read"] / stats["total"] * 100) if stats["total"] > 0 else 0, 2)
    }
