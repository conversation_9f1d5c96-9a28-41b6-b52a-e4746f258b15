"""
图片管理API
系统管理功能：扫描上传目录、匹配资源、处理孤立文件
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
from app.core.database import get_db
from app.services.auth import get_current_user
from app.services.image_management_service import image_management_service
from app.models.user import User

router = APIRouter(tags=["图片管理"])


class DeleteImagesRequest(BaseModel):
    """删除图片请求模型"""
    filenames: List[str]
    sub_dir: str = "items"


@router.get("/directories/{sub_dir}/scan")
async def scan_directory(
    sub_dir: str,
    _db: Session = Depends(get_db),
    _current_user: User = Depends(get_current_user)
):
    """
    扫描指定目录下的所有图片文件
    
    Args:
        sub_dir: 子目录名称
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        扫描结果
    """
    try:
        result = image_management_service.scan_directory_images(sub_dir)
        return {
            "success": True,
            "data": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"扫描目录失败: {str(e)}")


@router.get("/directories/{sub_dir}/match")
async def match_resources(
    sub_dir: str,
    db: Session = Depends(get_db),
    _current_user: User = Depends(get_current_user)
):
    """
    匹配资源记录与图片文件
    
    Args:
        sub_dir: 子目录名称
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        匹配结果
    """
    try:
        if sub_dir == "items":
            result = image_management_service.match_items_with_images(db, sub_dir)
        else:
            raise HTTPException(status_code=400, detail=f"不支持的子目录: {sub_dir}")
        
        return {
            "success": True,
            "data": result
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"匹配资源失败: {str(e)}")


@router.get("/directories/{sub_dir}/statistics")
async def get_statistics(
    sub_dir: str,
    _db: Session = Depends(get_db),
    _current_user: User = Depends(get_current_user)
):
    """
    获取目录统计信息
    
    Args:
        sub_dir: 子目录名称
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        统计信息
    """
    try:
        result = image_management_service.get_directory_statistics(sub_dir)
        return {
            "success": True,
            "data": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.post("/orphaned/delete")
async def delete_orphaned_images(
    request: DeleteImagesRequest,
    _db: Session = Depends(get_db),
    _current_user: User = Depends(get_current_user)
):
    """
    删除孤立的图片文件
    
    Args:
        request: 删除请求
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        删除结果
    """
    try:
        if not request.filenames:
            raise HTTPException(status_code=400, detail="文件名列表不能为空")
        
        result = image_management_service.delete_orphaned_images(
            request.filenames, 
            request.sub_dir
        )
        
        return {
            "success": True,
            "message": f"删除完成，成功删除 {result['deleted_count']} 个文件",
            "data": result
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除孤立图片失败: {str(e)}")


@router.get("/overview")
async def get_overview(
    db: Session = Depends(get_db),
    _current_user: User = Depends(get_current_user)
):
    """
    获取图片管理概览信息
    
    Args:
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        概览信息
    """
    try:
        # 获取各个子目录的统计信息
        directories = ["items"]  # 可以扩展支持更多目录
        overview = {}
        
        for sub_dir in directories:
            stats = image_management_service.get_directory_statistics(sub_dir)
            
            # 获取匹配信息（仅对items目录）
            if sub_dir == "items" and stats["exists"]:
                match_result = image_management_service.match_items_with_images(db, sub_dir)
                stats["match_statistics"] = match_result["statistics"]
            
            overview[sub_dir] = stats
        
        return {
            "success": True,
            "data": {
                "directories": overview,
                "total_directories": len([d for d in overview.values() if d["exists"]])
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取概览信息失败: {str(e)}")
