from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.auth import get_current_user
from app.models.user import User
from app.services.notification_service import NotificationService
from app.schemas.notification import (
    NotificationListResponse, NotificationResponse, 
    NotificationFilter, BatchOperationRequest
)
from app.schemas.notification import NotificationStatsResponse

router = APIRouter()


@router.get("", response_model=NotificationListResponse)
async def get_notifications(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    notification_type: Optional[str] = Query(None, description="通知类型"),
    status: Optional[str] = Query(None, description="通知状态"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取当前用户的通知列表"""
    # 构建筛选参数
    filter_params = None
    if any([notification_type, status, start_date, end_date]):
        from datetime import datetime
        filter_params = NotificationFilter(
            notification_type=notification_type,
            status=status,
            start_date=datetime.fromisoformat(start_date) if start_date else None,
            end_date=datetime.fromisoformat(end_date) if end_date else None
        )
    
    service = NotificationService(db)
    result = service.get_notifications(
        user_id=current_user.id,
        filter_params=filter_params,
        page=page,
        size=size
    )
    
    return result


@router.get("/all", response_model=NotificationListResponse)
async def get_all_notifications(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    user_id: Optional[int] = Query(None, description="用户ID"),
    notification_type: Optional[str] = Query(None, description="通知类型"),
    status: Optional[str] = Query(None, description="通知状态"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有通知列表（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    # 构建筛选参数
    filter_params = None
    if any([user_id, notification_type, status, start_date, end_date]):
        from datetime import datetime
        filter_params = NotificationFilter(
            user_id=user_id,
            notification_type=notification_type,
            status=status,
            start_date=datetime.fromisoformat(start_date) if start_date else None,
            end_date=datetime.fromisoformat(end_date) if end_date else None
        )
    
    service = NotificationService(db)
    result = service.get_all_notifications(
        filter_params=filter_params,
        page=page,
        size=size
    )
    
    return result


# 注意：具体的路径路由必须在参数路由之前定义
# 否则 /stats 会被 /{notification_id} 匹配

@router.get("/stats", response_model=NotificationStatsResponse)
async def get_notification_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取通知统计信息"""
    service = NotificationService(db)
    
    if current_user.is_superuser:
        # 超级管理员可以查看所有通知的统计
        stats = service.get_notification_stats()
    else:
        # 普通用户只能查看自己的通知统计
        stats = service.get_notification_stats(current_user.id)
    
    return stats


@router.get("/{notification_id}", response_model=NotificationResponse)
async def get_notification(
    notification_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取通知详情"""
    service = NotificationService(db)
    notification = service.get_notification_by_id(notification_id, current_user.id)
    
    if not notification:
        raise HTTPException(status_code=404, detail="通知不存在或无权限访问")
    
    return notification


@router.put("/{notification_id}/read", response_model=NotificationResponse)
async def mark_notification_read(
    notification_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """标记通知为已读"""
    service = NotificationService(db)
    notification = service.mark_as_read(notification_id, current_user.id)
    
    if not notification:
        raise HTTPException(status_code=404, detail="通知不存在或无权限访问")
    
    return notification


@router.put("/batch-read", response_model=dict)
async def batch_mark_as_read(
    request: BatchOperationRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量标记通知为已读"""
    service = NotificationService(db)
    updated_count = service.batch_mark_as_read(request.notification_ids, current_user.id)
    
    return {
        "message": f"成功标记 {updated_count} 条通知为已读",
        "updated_count": updated_count
    }


@router.delete("/{notification_id}")
async def delete_notification(
    notification_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除通知"""
    service = NotificationService(db)
    success = service.delete_notification(notification_id, current_user.id)
    
    if not success:
        raise HTTPException(status_code=404, detail="通知不存在或无权限访问")
    
    return {"message": "通知删除成功"}


@router.delete("/batch")
async def batch_delete_notifications(
    request: BatchOperationRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量删除通知"""
    service = NotificationService(db)
    deleted_count = service.batch_delete_notifications(request.notification_ids, current_user.id)
    
    return {
        "message": f"成功删除 {deleted_count} 条通知",
        "deleted_count": deleted_count
    }





@router.get("/stats/detailed")
async def get_detailed_notification_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取详细的通知统计信息（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    service = NotificationService(db)
    
    # 获取所有通知的统计
    all_stats = service.get_notification_stats()
    
    # 获取各类型通知的详细统计
    from datetime import datetime, timedelta
    today = datetime.utcnow().date()
    yesterday = today - timedelta(days=1)
    
    # 这里可以扩展为更详细的统计信息
    detailed_stats = {
        "overview": all_stats,
        "today": {
            "total": 0,  # 可以扩展为实际统计
            "unread": 0,
            "read": 0
        },
        "yesterday": {
            "total": 0,  # 可以扩展为实际统计
            "unread": 0,
            "read": 0
        }
    }
    
    return detailed_stats
