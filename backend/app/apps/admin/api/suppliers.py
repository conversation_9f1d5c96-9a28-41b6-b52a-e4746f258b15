from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from sqlalchemy.orm import joinedload
from typing import List, Optional
from datetime import datetime
from decimal import Decimal

from app.core.database import get_db
from app.models.supplier import Supplier, ItemSupplier, SupplierPrice
from app.models.user import Department
from app.models.user import User
from app.models.item import Item
from app.schemas.supplier import (
    Supplier as SupplierSchema,
    SupplierCreate,
    SupplierUpdate,
    SupplierQuery,
    ItemSupplier as ItemSupplierSchema,
    ItemSupplierCreate,
    ItemSupplierUpdate,
    SupplierPrice as SupplierPriceSchema,
    SupplierPriceCreate,
    SupplierPriceUpdate,
    SupplierPriceQuery,
    Department as DepartmentSchema,
    DepartmentCreate,
    DepartmentUpdate,
    PriceTrendResponse,
    PriceTrendData,
    PricePoint
)
from app.services.auth import get_current_user, require_permissions
from app.schemas.user import User as UserSchema
from app.core.exceptions import BusinessException

router = APIRouter(tags=["供应商管理"])


def generate_supplier_code(db: Session) -> str:
    """生成供应商编码：SUP + 6位数字序列"""
    # 获取当前最大编码
    max_code = db.query(Supplier.code).filter(
        Supplier.code.like("SUP%")
    ).order_by(Supplier.code.desc()).first()
    
    if max_code:
        # 提取数字部分
        try:
            current_num = int(max_code[0][3:])
            next_num = current_num + 1
        except (ValueError, IndexError):
            next_num = 1
    else:
        next_num = 1
    
    return f"SUP{next_num:06d}"


# 供应商管理
@router.get("", response_model=dict)
async def list_suppliers(
    search: Optional[str] = Query(None, description="搜索关键词"),
    status: Optional[str] = Query(None, description="状态过滤"),
    rating_min: Optional[int] = Query(None, ge=0, le=5, description="最小评级"),
    sort_by: Optional[str] = Query("name", description="排序字段 (name/code/status/rating)"),
    sort_order: Optional[str] = Query("asc", description="排序方向 (asc/desc)"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.read"]))
):
    """获取供应商列表"""
    query = db.query(Supplier)
    
    # 搜索过滤
    if search:
        search_pattern = f"%{search}%"
        query = query.filter(
            or_(
                Supplier.name_cn.ilike(search_pattern),
                Supplier.name_en.ilike(search_pattern),
                Supplier.code.ilike(search_pattern),
                Supplier.contact_person.ilike(search_pattern),
                Supplier.company_address.ilike(search_pattern)
            )
        )
    
    # 状态过滤
    if status:
        query = query.filter(Supplier.status == status)
    
    # 评级过滤
    if rating_min is not None:
        query = query.filter(Supplier.rating >= rating_min)
    
    # 排序
    if sort_by == "name":
        # 优先按英文名排序，如果没有则按中文名排序
        query = query.order_by(
            func.coalesce(Supplier.name_en, Supplier.name_cn).asc() if sort_order == "asc" 
            else func.coalesce(Supplier.name_en, Supplier.name_cn).desc()
        )
    elif sort_by == "code":
        query = query.order_by(Supplier.code.asc() if sort_order == "asc" else Supplier.code.desc())
    elif sort_by == "status":
        query = query.order_by(Supplier.status.asc() if sort_order == "asc" else Supplier.status.desc())
    elif sort_by == "rating":
        query = query.order_by(Supplier.rating.asc() if sort_order == "asc" else Supplier.rating.desc())
    else:
        # 默认按名称排序：优先按英文名排序，如果没有则按中文名排序
        query = query.order_by(
            func.coalesce(Supplier.name_en, Supplier.name_cn).asc()
        )
    
    # 分页
    total = query.count()
    suppliers = query.offset((page - 1) * size).limit(size).all()
    
    # 计算统计信息
    # 1. 活跃供应商数量
    active_suppliers_count = db.query(Supplier).filter(Supplier.status == "active").count()
    
    # 2. 平均评分（只统计rating >= 1的供应商）
    rated_suppliers_query = db.query(Supplier).filter(Supplier.rating >= 1)
    rated_suppliers_count = rated_suppliers_query.count()
    if rated_suppliers_count > 0:
        avg_rating = rated_suppliers_query.with_entities(func.avg(Supplier.rating)).scalar()
        avg_rating = float(avg_rating) if avg_rating else 0.0
    else:
        avg_rating = 0.0
    
    # 3. 本月新增供应商数量
    from datetime import datetime, timezone
    now = datetime.now(timezone.utc)
    current_month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    new_this_month_count = db.query(Supplier).filter(
        Supplier.created_at >= current_month_start
    ).count()
    
    # 为每个供应商计算物品数量和价格范围
    suppliers_with_stats = []
    for supplier in suppliers:
        # 获取供应商的物品数量
        item_count = db.query(ItemSupplier).filter(
            ItemSupplier.supplier_id == supplier.id,
            ItemSupplier.status == "active"
        ).count()
        
        # 获取供应商的价格范围
        price_range = ""
        if item_count > 0:
            # 查询供应商的所有活跃价格
            prices_query = db.query(SupplierPrice).join(
                ItemSupplier, ItemSupplier.id == SupplierPrice.item_supplier_id
            ).filter(
                ItemSupplier.supplier_id == supplier.id,
                ItemSupplier.status == "active",
                SupplierPrice.status == "active"
            )
            
            prices = prices_query.with_entities(SupplierPrice.unit_price).all()
            if prices:
                unit_prices = [float(price[0]) for price in prices if price[0] is not None]
                if unit_prices:
                    min_price = min(unit_prices)
                    max_price = max(unit_prices)
                    if min_price == max_price:
                        price_range = f"${min_price:.2f}"
                    else:
                        price_range = f"${min_price:.2f} - ${max_price:.2f}"
        
        # 创建包含统计信息的供应商数据
        supplier_data = SupplierSchema.model_validate(supplier)
        supplier_dict = supplier_data.model_dump()
        supplier_dict["itemCount"] = item_count
        supplier_dict["priceRange"] = price_range
        
        suppliers_with_stats.append(supplier_dict)
    
    return {
        "items": suppliers_with_stats,
        "total": total,
        "page": page,
        "size": size,
        "pages": (total + size - 1) // size,
        "stats": {
            "active_suppliers": active_suppliers_count,
            "avg_rating": avg_rating,
            "new_this_month": new_this_month_count
        }
    }


# 供应商价格管理
@router.get("/prices", response_model=dict)
async def list_supplier_prices(
    supplier_id: Optional[int] = Query(None, description="供应商ID"),
    item_id: Optional[int] = Query(None, description="物品ID"),
    item_supplier_id: Optional[int] = Query(None, description="物品供应商关联ID"),
    status: Optional[str] = Query(None, description="状态"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.read"]))
):
    """获取供应商价格列表"""
    query = db.query(SupplierPrice)
    
    # 过滤条件
    if item_supplier_id:
        # 直接按item_supplier_id过滤
        query = query.filter(SupplierPrice.item_supplier_id == item_supplier_id)
    elif supplier_id or item_id:
        # 通过关联查询
        query = query.join(SupplierPrice.item_supplier)
        
        if supplier_id:
            query = query.filter(ItemSupplier.supplier_id == supplier_id)
        
        if item_id:
            query = query.filter(ItemSupplier.item_id == item_id)
    
    # 只有当status参数明确提供时才进行状态过滤
    if status is not None:
        query = query.filter(SupplierPrice.status == status)
    
    # 按更新时间倒序排序
    query = query.order_by(SupplierPrice.updated_at.desc())
    
    # 分页
    total = query.count()
    prices = query.offset((page - 1) * size).limit(size).all()
    
    # 转换为Pydantic模型
    price_schemas = [SupplierPriceSchema.model_validate(price) for price in prices]
    
    return {
        "items": price_schemas,
        "total": total,
        "page": page,
        "size": size,
        "pages": (total + size - 1) // size
    }


@router.post("/prices/batch")
async def get_supplier_prices_batch(
    item_supplier_ids: List[int],
    status: Optional[str] = Query(None, description="状态"),
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.read"]))
):
    """批量获取多个物品供应商关联的价格信息"""
    if not item_supplier_ids:
        return {}
    
    # 构建查询
    query = db.query(SupplierPrice).filter(
        SupplierPrice.item_supplier_id.in_(item_supplier_ids)
    )
    
    # 只有当status参数明确提供时才进行状态过滤
    if status is not None:
        query = query.filter(SupplierPrice.status == status)
    
    # 按更新时间倒序排序
    query = query.order_by(SupplierPrice.updated_at.desc())
    
    # 获取所有价格
    prices = query.all()
    
    # 按item_supplier_id分组
    price_map = {}
    for price in prices:
        if price.item_supplier_id not in price_map:
            price_map[price.item_supplier_id] = []
        price_map[price.item_supplier_id].append(SupplierPriceSchema.model_validate(price))
    
    # 构建结果，确保所有请求的item_supplier_id都有返回（即使没有价格）
    result = {}
    for item_supplier_id in item_supplier_ids:
        result[item_supplier_id] = price_map.get(item_supplier_id, [])
    
    return result


@router.post("/prices", response_model=SupplierPriceSchema)
async def create_supplier_price(
    price: SupplierPriceCreate,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.price_manage"]))
):
    """创建供应商价格"""
    # 检查物品供应商关联是否存在
    item_supplier = db.query(ItemSupplier).filter(ItemSupplier.id == price.item_supplier_id).first()
    if not item_supplier:
        raise HTTPException(status_code=404, detail="物品供应商关联不存在")
    
    # 检查价格有效期
    if price.valid_to and price.valid_from > price.valid_to:
        raise HTTPException(status_code=400, detail="截止日期不能早于起始日期")
    
    # 检查价格有效期是否超过3年
    if price.valid_to:
        from datetime import timedelta
        max_valid_to = price.valid_from + timedelta(days=365 * 3)
        if price.valid_to > max_valid_to:
            raise HTTPException(status_code=400, detail="价格有效期不能超过3年")
    
    db_price = SupplierPrice(
        **price.model_dump(),
        created_by=current_user.id
    )
    db.add(db_price)
    db.commit()
    db.refresh(db_price)
    
    return db_price


@router.put("/prices/{price_id}", response_model=SupplierPriceSchema)
async def update_supplier_price(
    price_id: int,
    price_update: SupplierPriceUpdate,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.price_manage"]))
):
    """更新供应商价格"""
    # 检查价格是否存在
    db_price = db.query(SupplierPrice).filter(SupplierPrice.id == price_id).first()
    if not db_price:
        raise HTTPException(status_code=404, detail="价格记录不存在")
    
    # 检查价格有效期
    update_data = price_update.model_dump(exclude_unset=True)
    if 'valid_from' in update_data and 'valid_to' in update_data:
        if update_data['valid_from'] > update_data['valid_to']:
            raise HTTPException(status_code=400, detail="截止日期不能早于起始日期")
    elif 'valid_from' in update_data and db_price.valid_to:
        if update_data['valid_from'] > db_price.valid_to:
            raise HTTPException(status_code=400, detail="截止日期不能早于起始日期")
    elif 'valid_to' in update_data and db_price.valid_from:
        if db_price.valid_from > update_data['valid_to']:
            raise HTTPException(status_code=400, detail="截止日期不能早于起始日期")
    
    # 检查价格有效期是否超过3年
    if 'valid_to' in update_data and update_data['valid_to']:
        from datetime import timedelta
        valid_from = update_data.get('valid_from', db_price.valid_from)
        max_valid_to = valid_from + timedelta(days=365 * 3)
        if update_data['valid_to'] > max_valid_to:
            raise HTTPException(status_code=400, detail="价格有效期不能超过3年")
    
    # 更新字段
    for field, value in update_data.items():
        setattr(db_price, field, value)
    
    db_price.updated_by = current_user.id
    db.commit()
    db.refresh(db_price)
    
    return db_price


@router.delete("/prices/{price_id}")
async def delete_supplier_price(
    price_id: int,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.price_manage"]))
):
    """删除供应商价格"""
    # 检查价格是否存在
    db_price = db.query(SupplierPrice).filter(SupplierPrice.id == price_id).first()
    if not db_price:
        raise HTTPException(status_code=404, detail="价格记录不存在")
    
    db.delete(db_price)
    db.commit()
    
    return {"message": "价格记录删除成功"}


@router.post("", response_model=SupplierSchema)
async def create_supplier(
    supplier: SupplierCreate,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.create"]))
):
    """创建供应商"""
    # 验证至少有一个名称
    if not supplier.name_cn and not supplier.name_en:
        raise HTTPException(status_code=400, detail="请至少填写供应商中文名或英文名")
    
    # 检查中文名是否重复（如果提供了中文名）
    if supplier.name_cn:
        existing_supplier = db.query(Supplier).filter(Supplier.name_cn == supplier.name_cn).first()
        if existing_supplier:
            raise HTTPException(status_code=400, detail="供应商中文名已存在")
    
    # 检查英文名是否重复（如果提供了英文名）
    if supplier.name_en:
        existing_supplier = db.query(Supplier).filter(Supplier.name_en == supplier.name_en).first()
        if existing_supplier:
            raise HTTPException(status_code=400, detail="供应商英文名已存在")
    
    # 总是自动生成编码
    supplier.code = generate_supplier_code(db)
    db_supplier = Supplier(
        **supplier.model_dump(),
        created_by=current_user.id
    )
    db.add(db_supplier)
    db.commit()
    db.refresh(db_supplier)
    return db_supplier


@router.get("/{supplier_id}", response_model=SupplierSchema)
async def get_supplier(
    supplier_id: int,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.read"]))
):
    """获取供应商详情"""
    supplier = db.query(Supplier).filter(Supplier.id == supplier_id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="供应商不存在")
    
    return supplier


@router.put("/{supplier_id}", response_model=SupplierSchema)
async def update_supplier(
    supplier_id: int,
    supplier_update: SupplierUpdate,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.update"]))
):
    """更新供应商"""
    supplier = db.query(Supplier).filter(Supplier.id == supplier_id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="供应商不存在")
    
    # 验证至少有一个名称
    update_data = supplier_update.model_dump(exclude_unset=True)
    new_name_cn = update_data.get('name_cn', supplier.name_cn)
    new_name_en = update_data.get('name_en', supplier.name_en)
    
    if not new_name_cn and not new_name_en:
        raise HTTPException(status_code=400, detail="请至少填写供应商中文名或英文名")
    
    # 检查中文名是否重复（排除当前供应商，如果提供了中文名）
    if new_name_cn and new_name_cn != supplier.name_cn:
        existing_supplier = db.query(Supplier).filter(
            Supplier.name_cn == new_name_cn,
            Supplier.id != supplier_id
        ).first()
        if existing_supplier:
            raise HTTPException(status_code=400, detail="供应商中文名已存在")
    
    # 检查英文名是否重复（排除当前供应商，如果提供了英文名）
    if new_name_en and new_name_en != supplier.name_en:
        existing_supplier = db.query(Supplier).filter(
            Supplier.name_en == new_name_en,
            Supplier.id != supplier_id
        ).first()
        if existing_supplier:
            raise HTTPException(status_code=400, detail="供应商英文名已存在")
    
    # 更新字段
    for field, value in update_data.items():
        setattr(supplier, field, value)
    
    supplier.updated_by = current_user.id
    db.commit()
    db.refresh(supplier)
    
    return supplier


@router.delete("/{supplier_id}")
async def delete_supplier(
    supplier_id: int,
    force_delete: bool = Query(False, description="是否强制删除关联物品"),
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.delete"]))
):
    """删除供应商"""
    supplier = db.query(Supplier).filter(Supplier.id == supplier_id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="供应商不存在")
    
    # 检查是否有关联的物品供应商关系
    item_suppliers = db.query(ItemSupplier).filter(ItemSupplier.supplier_id == supplier_id).all()
    if item_suppliers and not force_delete:
        # 获取关联物品的详细信息
        item_details = []
        for item_supplier in item_suppliers[:3]:  # 只取前3个用于显示
            item = db.query(Item).filter(Item.id == item_supplier.item_id).first()
            if item:
                item_details.append({
                    "id": item.id,
                    "name": item.name,
                    "code": item.code
                })
        
        # 返回关联物品信息，使用200状态码
        return {
            "has_related_items": True,
            "item_count": len(item_suppliers),
            "item_details": item_details,
            "message": f"该供应商还有{len(item_suppliers)}个关联物品，是否一起删除？"
        }
    
    # 如果强制删除或没有关联物品，执行删除
    if force_delete and item_suppliers:
        # 删除关联的价格记录
        for item_supplier in item_suppliers:
            prices = db.query(SupplierPrice).filter(SupplierPrice.item_supplier_id == item_supplier.id).all()
            for price in prices:
                db.delete(price)
        
        # 删除物品供应商关联
        for item_supplier in item_suppliers:
            db.delete(item_supplier)
    
    db.delete(supplier)
    db.commit()
    
    return {"message": "供应商删除成功"}


# 物品供应商关联管理
@router.get("/{supplier_id}/items", response_model=List[ItemSupplierSchema])
async def list_supplier_items(
    supplier_id: int,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.read"]))
):
    """获取供应商提供的物品列表"""
    supplier = db.query(Supplier).filter(Supplier.id == supplier_id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="供应商不存在")
    
    # 只获取有效的物品，并预加载物品信息
    item_suppliers = db.query(ItemSupplier).options(
        joinedload(ItemSupplier.item)
    ).filter(
        ItemSupplier.supplier_id == supplier_id,
        ItemSupplier.status == "active"
    ).order_by(ItemSupplier.priority.asc()).all()
    
    # 转换为Pydantic模型
    item_supplier_schemas = [ItemSupplierSchema.model_validate(item_supplier) for item_supplier in item_suppliers]
    
    return item_supplier_schemas


@router.get("/{supplier_id}/items-with-usd-prices")
async def list_supplier_items_with_usd_prices(
    supplier_id: int,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.read"]))
):
    """获取供应商提供的物品列表，包含USD价格计算"""
    from app.services.exchange_rate_service import ExchangeRateService
    
    supplier = db.query(Supplier).filter(Supplier.id == supplier_id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="供应商不存在")
    
    # 获取物品供应商关联，预加载物品和价格信息
    item_suppliers = db.query(ItemSupplier).options(
        joinedload(ItemSupplier.item),
        joinedload(ItemSupplier.price_records)
    ).filter(
        ItemSupplier.supplier_id == supplier_id,
        ItemSupplier.status == "active"
    ).order_by(ItemSupplier.priority.asc()).all()
    
    # 初始化汇率服务
    exchange_rate_service = ExchangeRateService(db)
    
    result = []
    for item_supplier in item_suppliers:
        # 获取当前有效价格
        current_time = datetime.now().replace(tzinfo=None)  # 使用naive datetime避免时区问题
        active_prices = []
        for price in item_supplier.price_records:
            if price.status != "active":
                continue
                
            # 处理时区问题：确保时间比较的一致性
            valid_from = price.valid_from
            valid_to = price.valid_to
            
            # 如果数据库时间是aware，转换为naive
            if valid_from and valid_from.tzinfo is not None:
                valid_from = valid_from.replace(tzinfo=None)
            if valid_to and valid_to.tzinfo is not None:
                valid_to = valid_to.replace(tzinfo=None)
            
            # 检查价格是否有效
            if (valid_from and valid_from <= current_time and 
                (valid_to is None or valid_to > current_time)):
                active_prices.append(price)
        
        # 计算USD价格
        usd_prices = []
        for price in active_prices:
            if price.currency_code == "USD":
                usd_price = float(price.unit_price)
            else:
                # 转换为USD
                usd_amount = exchange_rate_service.convert_currency_to_usd(
                    price.unit_price, 
                    price.currency_code
                )
                usd_price = float(usd_amount) if usd_amount else None
            
            if usd_price is not None:
                usd_prices.append({
                    "original_price": float(price.unit_price),
                    "original_currency": price.currency_code,
                    "usd_price": usd_price,
                    "min_quantity": price.min_quantity,
                    "max_quantity": price.max_quantity,
                    "valid_from": price.valid_from.isoformat() if price.valid_from else None,
                    "valid_to": price.valid_to.isoformat() if price.valid_to else None,
                    "remarks": price.remarks
                })
        
        # 计算价格范围
        price_range = None
        if usd_prices:
            min_usd_price = min(p["usd_price"] for p in usd_prices)
            max_usd_price = max(p["usd_price"] for p in usd_prices)
            price_range = {
                "min_usd_price": min_usd_price,
                "max_usd_price": max_usd_price,
                "has_multiple_prices": len(usd_prices) > 1
            }
        
        result.append({
            "id": item_supplier.id,
            "item_id": item_supplier.item_id,
            "supplier_id": item_supplier.supplier_id,
            "priority": item_supplier.priority,
            "status": item_supplier.status,
            "delivery_days": item_supplier.delivery_days,
            "quality_rating": item_supplier.quality_rating,
            "spq": item_supplier.spq,
            "moq": item_supplier.moq,
            "created_at": item_supplier.created_at.isoformat() if item_supplier.created_at else None,
            "updated_at": item_supplier.updated_at.isoformat() if item_supplier.updated_at else None,
            "item": {
                "id": item_supplier.item.id,
                "name": item_supplier.item.name,
                "code": item_supplier.item.code,
                "description": item_supplier.item.description,
                "image_url": item_supplier.item.image_url,
                "purchase_unit": item_supplier.item.purchase_unit,
                "inventory_unit": item_supplier.item.inventory_unit,
                "qty_per_up": item_supplier.item.qty_per_up,
                "is_active": item_supplier.item.is_active,
                "is_purchasable": item_supplier.item.is_purchasable,
                "brand": item_supplier.item.brand,
                "spec_material": item_supplier.item.spec_material,
                "size_dimension": item_supplier.item.size_dimension,
                "category": {
                    "id": item_supplier.item.category.id,
                    "name": item_supplier.item.category.name,
                    "primary_category": {
                        "id": item_supplier.item.category.primary_category.id,
                        "name": item_supplier.item.category.primary_category.name
                    } if item_supplier.item.category.primary_category else None
                } if item_supplier.item.category else None
            } if item_supplier.item else None,
            "usd_prices": usd_prices,
            "price_range": price_range
        })
    
    return result


@router.post("/{supplier_id}/items", response_model=ItemSupplierSchema)
async def add_supplier_item(
    supplier_id: int,
    item_supplier: ItemSupplierCreate,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.update"]))
):
    """为供应商添加物品"""
    # 检查供应商是否存在
    supplier = db.query(Supplier).filter(Supplier.id == supplier_id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="供应商不存在")
    
    # 检查物品是否存在
    item = db.query(Item).filter(Item.id == item_supplier.item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="物品不存在")
    
    # 检查是否已存在关联
    existing = db.query(ItemSupplier).filter(
        and_(
            ItemSupplier.supplier_id == supplier_id,
            ItemSupplier.item_id == item_supplier.item_id
        )
    ).first()
    if existing:
        raise HTTPException(status_code=400, detail="该供应商已经关联了此物品")
    
    # 如果设置为Preferred供应商，需要检查是否已有其他Preferred供应商
    if item_supplier.priority == 0:
        existing_preferred = db.query(ItemSupplier).filter(
            and_(
                ItemSupplier.item_id == item_supplier.item_id,
                ItemSupplier.priority == 0,
                ItemSupplier.status == "active"
            )
        ).first()
        if existing_preferred:
            raise HTTPException(status_code=400, detail="该物品已有Preferred供应商")
    
    # 验证SPQ和MOQ
    # 验证SPQ必须是正整数
    if not isinstance(item_supplier.spq, int) or item_supplier.spq <= 0:
        raise HTTPException(status_code=400, detail="SPQ必须是正整数")
    
    # 验证MOQ必须是正整数
    if not isinstance(item_supplier.moq, int) or item_supplier.moq <= 0:
        raise HTTPException(status_code=400, detail="MOQ必须是正整数")
    
    # 验证MOQ必须是SPQ的整数倍
    if item_supplier.moq % item_supplier.spq != 0:
        raise HTTPException(
            status_code=400, 
            detail=f"MOQ必须是SPQ的整数倍，当前SPQ: {item_supplier.spq}, MOQ: {item_supplier.moq}"
        )
    
    # 确保supplier_id正确设置
    item_supplier_data = item_supplier.model_dump()
    item_supplier_data['supplier_id'] = supplier_id
    
    db_item_supplier = ItemSupplier(**item_supplier_data)
    db.add(db_item_supplier)
    db.commit()
    db.refresh(db_item_supplier)
    
    return db_item_supplier


@router.put("/item-suppliers/{item_supplier_id}", response_model=ItemSupplierSchema)
async def update_item_supplier(
    item_supplier_id: int,
    item_supplier_update: ItemSupplierUpdate,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.update"]))
):
    """更新物品供应商关联"""
    item_supplier = db.query(ItemSupplier).filter(ItemSupplier.id == item_supplier_id).first()
    if not item_supplier:
        raise HTTPException(status_code=404, detail="物品供应商关联不存在")
    
    # 验证SPQ和MOQ
    update_data = item_supplier_update.model_dump(exclude_unset=True)
    
    # 获取要更新的SPQ和MOQ值
    new_spq = update_data.get('spq', item_supplier.spq)
    new_moq = update_data.get('moq', item_supplier.moq)
    
    # 验证SPQ必须是正整数
    if new_spq is not None and (not isinstance(new_spq, int) or new_spq <= 0):
        raise HTTPException(status_code=400, detail="SPQ必须是正整数")
    
    # 验证MOQ必须是正整数
    if new_moq is not None and (not isinstance(new_moq, int) or new_moq <= 0):
        raise HTTPException(status_code=400, detail="MOQ必须是正整数")
    
    # 验证MOQ必须是SPQ的整数倍
    if new_spq is not None and new_moq is not None and new_moq % new_spq != 0:
        raise HTTPException(
            status_code=400, 
            detail=f"MOQ必须是SPQ的整数倍，当前SPQ: {new_spq}, MOQ: {new_moq}"
        )
    
    # 检查优先级冲突并处理
    if item_supplier_update.priority is not None:
        # 检查是否已有其他供应商占用该优先级
        existing_supplier = db.query(ItemSupplier).filter(
            and_(
                ItemSupplier.item_id == item_supplier.item_id,
                ItemSupplier.priority == item_supplier_update.priority,
                ItemSupplier.status == "active",
                ItemSupplier.id != item_supplier_id
            )
        ).first()
        
        if existing_supplier:
            # 先清除冲突的优先级
            existing_supplier.priority = None
            print(f"清除供应商ID {existing_supplier.supplier_id} 的优先级 {item_supplier_update.priority}")
            # 先提交这个更改
            db.commit()
            # 重新获取对象
            db.refresh(existing_supplier)
    
    # 更新字段
    for field, value in update_data.items():
        setattr(item_supplier, field, value)
    
    db.commit()
    db.refresh(item_supplier)
    
    return item_supplier


@router.delete("/item-suppliers/{item_supplier_id}")
async def delete_item_supplier(
    item_supplier_id: int,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.update"]))
):
    """删除物品供应商关联"""
    item_supplier = db.query(ItemSupplier).filter(ItemSupplier.id == item_supplier_id).first()
    if not item_supplier:
        raise HTTPException(status_code=404, detail="物品供应商关联不存在")
    
    db.delete(item_supplier)
    db.commit()
    
    return {"message": "物品供应商关联删除成功"}



    """创建供应商价格"""
    # 检查物品供应商关联是否存在
    item_supplier = db.query(ItemSupplier).filter(ItemSupplier.id == price.item_supplier_id).first()
    if not item_supplier:
        raise HTTPException(status_code=404, detail="物品供应商关联不存在")
    
    # 检查价格有效期
    if price.valid_to and price.valid_from > price.valid_to:
        raise HTTPException(status_code=400, detail="截止日期不能早于起始日期")
    
    # 检查价格有效期是否超过3年
    if price.valid_to:
        from datetime import timedelta
        max_valid_to = price.valid_from + timedelta(days=365 * 3)
        if price.valid_to > max_valid_to:
            raise HTTPException(status_code=400, detail="价格有效期不能超过3年")
    
    db_price = SupplierPrice(
        **price.model_dump(),
        created_by=current_user.id
    )
    db.add(db_price)
    db.commit()
    db.refresh(db_price)
    
    return db_price


@router.put("/prices/{price_id}", response_model=SupplierPriceSchema)
async def update_supplier_price(
    price_id: int,
    price_update: SupplierPriceUpdate,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.price_manage"]))
):
    """更新供应商价格"""
    price = db.query(SupplierPrice).filter(SupplierPrice.id == price_id).first()
    if not price:
        raise HTTPException(status_code=404, detail="价格记录不存在")
    
    # 更新字段
    update_data = price_update.model_dump(exclude_unset=True)
    
    # 检查日期验证
    if 'valid_from' in update_data or 'valid_to' in update_data:
        new_valid_from = update_data.get('valid_from', price.valid_from)
        new_valid_to = update_data.get('valid_to', price.valid_to)
        
        if new_valid_to and new_valid_from > new_valid_to:
            raise HTTPException(status_code=400, detail="截止日期不能早于起始日期")
    
    for field, value in update_data.items():
        setattr(price, field, value)
    
    price.updated_by = current_user.id
    db.commit()
    db.refresh(price)
    
    return price


@router.delete("/prices/{price_id}")
async def delete_supplier_price(
    price_id: int,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.price_manage"]))
):
    """删除供应商价格"""
    price = db.query(SupplierPrice).filter(SupplierPrice.id == price_id).first()
    if not price:
        raise HTTPException(status_code=404, detail="价格记录不存在")
    
    db.delete(price)
    db.commit()
    
    return {"message": "价格记录删除成功"}


# 部门管理
@router.get("/departments", response_model=List[DepartmentSchema])
async def list_departments(
    search: Optional[str] = Query(None, description="搜索关键词 (按名称或编码)"),
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["department.read"]))
):
    """获取部门列表（支持按名称/编码搜索）"""
    query = db.query(Department).filter(Department.is_active == True)
    if search:
        pattern = f"%{search}%"
        query = query.filter(or_(Department.name.ilike(pattern), Department.code.ilike(pattern)))
    departments = query.order_by(Department.name.asc()).all()
    
    # 转换为Pydantic模型
    department_schemas = [DepartmentSchema.model_validate(department) for department in departments]
    
    return department_schemas


@router.post("/departments", response_model=DepartmentSchema)
async def create_department(
    department: DepartmentCreate,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["department.create"]))
):
    """创建部门"""
    # 检查编码是否重复
    existing = db.query(Department).filter(Department.code == department.code).first()
    if existing:
        raise HTTPException(status_code=400, detail="部门编码已存在")
    
    db_department = Department(**department.model_dump())
    db.add(db_department)
    db.commit()
    db.refresh(db_department)
    
    return db_department


@router.get("/departments/{department_id}", response_model=DepartmentSchema)
async def get_department(
    department_id: int,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["department.read"]))
):
    """获取部门详情"""
    department = db.query(Department).filter(Department.id == department_id).first()
    if not department:
        raise HTTPException(status_code=404, detail="部门不存在")
    
    return department


@router.put("/departments/{department_id}", response_model=DepartmentSchema)
async def update_department(
    department_id: int,
    department_update: DepartmentUpdate,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["department.update"]))
):
    """更新部门"""
    department = db.query(Department).filter(Department.id == department_id).first()
    if not department:
        raise HTTPException(status_code=404, detail="部门不存在")
    
    # 更新字段
    update_data = department_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(department, field, value)
    
    db.commit()
    db.refresh(department)
    
    return department


# 获取物品的供应商信息
@router.get("/items/{item_id}/suppliers", response_model=List[ItemSupplierSchema])
async def get_item_suppliers(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.read"]))
):
    """获取物品的供应商列表"""
    item = db.query(Item).filter(Item.id == item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="物品不存在")
    
    item_suppliers = db.query(ItemSupplier).filter(
        ItemSupplier.item_id == item_id,
        ItemSupplier.status == "active"
    ).order_by(ItemSupplier.priority.asc()).all()
    
    # 转换为Pydantic模型
    item_supplier_schemas = [ItemSupplierSchema.model_validate(item_supplier) for item_supplier in item_suppliers]
    
    return item_supplier_schemas


@router.get("/items/{item_id}/supplier-priority-info")
async def get_item_priority_info(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.read"]))
):
    """获取物品的优先级信息，用于前端下拉选择"""
    # 检查物品是否存在
    item = db.query(Item).filter(Item.id == item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="物品不存在")
    
    # 获取该物品所有供应商的优先级信息
    priority_info = db.query(ItemSupplier).filter(
        ItemSupplier.item_id == item_id,
        ItemSupplier.status == "active"
    ).all()
    
    # 构建优先级选项
    priority_options = []
    
    # 定义优先级映射
    priority_map = {
        0: "Preferred",
        1: "Alternate1", 
        2: "Alternate2",
        3: "Alternate3"
    }
    
    # 检查每个优先级是否已被占用
    for priority in [0, 1, 2, 3]:
        supplier = next((p for p in priority_info if p.priority == priority), None)
        option = {
            "value": priority,
            "label": priority_map[priority],
            "occupied": supplier is not None,
            "supplier_name": supplier.supplier.name_en or supplier.supplier.name_cn if supplier else None,
            "supplier_id": supplier.supplier_id if supplier else None
        }
        priority_options.append(option)
    
    # 添加无优先级选项
    priority_options.append({
        "value": None,
        "label": "无优先级",
        "occupied": False,
        "supplier_name": None,
        "supplier_id": None
    })
    
    return {
        "priority_options": priority_options,
        "current_suppliers": [
            {
                "id": p.id,
                "supplier_id": p.supplier_id,
                "supplier_name": p.supplier.name_en or p.supplier.name_cn,
                "priority": p.priority,
                "priority_label": priority_map.get(p.priority, "未知") if p.priority is not None else "无优先级"
            }
            for p in priority_info
        ]
    }


# 获取供应商价格计算
@router.get("/{supplier_id}/items/{item_id}/price")
async def calculate_supplier_price(
    supplier_id: int,
    item_id: int,
    quantity: int = Query(..., gt=0, description="数量"),
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.read"]))
):
    """计算供应商价格"""
    from app.services.item_price_service import ItemPriceService
    
    try:
        # 使用ItemPriceService计算价格
        item_price_service = ItemPriceService(db)
        price_info = item_price_service.get_item_price_info(
            item_id=item_id,
            quantity=quantity,
            supplier_id=supplier_id
        )
        
        return {
            "supplier_id": supplier_id,
            "item_id": item_id,
            "quantity": quantity,
            "unit_price": price_info["unit_price"]["amount"],
            "unit_price_usd": price_info["unit_price"]["usd_amount"],
            "total_amount": price_info["total_price"]["amount"],
            "total_amount_usd": price_info["total_price"]["usd_amount"],
            "currency": price_info["unit_price"]["currency"],
            "exchange_rate": price_info["exchange_rate"],
            "valid_from": price_info["price_record"]["valid_from"],
            "valid_to": price_info["price_record"]["valid_to"],
            "remarks": price_info["price_record"]["remarks"]
        }
        
    except BusinessException as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算价格失败: {str(e)}")


# 价格走势分析API
@router.get("/item-suppliers/{item_supplier_id}/price-trend", response_model=PriceTrendResponse)
async def get_price_trend(
    item_supplier_id: int,
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.read"]))
):
    """获取价格走势数据"""
    from datetime import date, timedelta
    import calendar
    from app.services.exchange_rate_service import ExchangeRateService
    
    # 初始化汇率服务
    exchange_rate_service = ExchangeRateService(db)
    
    # 获取物品供应商关联信息
    item_supplier = db.query(ItemSupplier).filter(
        ItemSupplier.id == item_supplier_id
    ).options(
        joinedload(ItemSupplier.supplier),
        joinedload(ItemSupplier.item)
    ).first()
    
    if not item_supplier:
        raise HTTPException(status_code=404, detail="未找到物品供应商关联")
    
    # 设置默认日期范围（前后180天）
    today = date.today()
    if not start_date:
        start_date = (today - timedelta(days=90)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = (today + timedelta(days=90)).strftime('%Y-%m-%d')
    
    # 解析日期
    try:
        start_dt = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_dt = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式错误，请使用YYYY-MM-DD格式")
    
    # 验证日期范围
    if (end_dt - start_dt).days > 365:
        raise HTTPException(status_code=400, detail="日期范围不能超过365天")
    
    if (end_dt - start_dt).days < 30:
        raise HTTPException(status_code=400, detail="日期范围不能少于30天")
    
    # 获取所有有效的价格配置
    prices = db.query(SupplierPrice).filter(
        and_(
            SupplierPrice.item_supplier_id == item_supplier_id,
            SupplierPrice.status == "active"
        )
    ).order_by(SupplierPrice.min_quantity, SupplierPrice.updated_at.desc()).all()
    
    if not prices:
        raise HTTPException(status_code=404, detail="未找到价格数据")
    
    # 按阶梯分组价格配置
    tier_prices = {}
    for price in prices:
        # 使用 min_quantity 和 max_quantity 组合作为键，确保不同范围的分组
        if price.max_quantity:
            tier_key = f"{price.min_quantity}_{price.max_quantity}"
        else:
            tier_key = f"{price.min_quantity}_None"
        
        if tier_key not in tier_prices:
            tier_prices[tier_key] = []
        tier_prices[tier_key].append(price)
    
    # 生成日期序列
    date_range = []
    current_date = start_dt
    while current_date <= end_dt:
        date_range.append(current_date)
        current_date += timedelta(days=1)
    
    # 计算每个阶梯的价格趋势
    price_trends = []
    for tier_key, tier_configs in tier_prices.items():
        # 获取该阶梯的配置
        tier_config = tier_configs[0]  # 使用第一个配置作为基础
        min_quantity = tier_config.min_quantity
        max_quantity = tier_config.max_quantity
        
        # 生成阶梯名称
        if max_quantity:
            tier_name = f"{min_quantity}-{max_quantity}"
        else:
            tier_name = f"{min_quantity}+"
        
        # 使用 min_quantity 作为 tier_id，保持与数据库的兼容性
        tier_id = min_quantity
        
        # 计算每个日期的价格
        price_points = []
        for current_date in date_range:
            # 查找该日期生效的价格规则
            active_prices = []
            for price_config in tier_configs:
                # 检查价格是否在该日期生效
                if (price_config.valid_from.date() <= current_date and 
                    (price_config.valid_to is None or price_config.valid_to.date() >= current_date)):
                    active_prices.append(price_config)
            
            if active_prices:
                # 选择更新时间最新的价格
                latest_price = max(active_prices, key=lambda x: x.updated_at)
                unit_price = float(latest_price.unit_price)
                total_price = unit_price
                is_valid = True
                remarks = latest_price.remarks
                currency_code = latest_price.currency_code
                
                # 计算USD价格
                usd_unit_price = None
                usd_total_price = None
                exchange_rate = None
                
                if currency_code == "USD":
                    usd_unit_price = unit_price
                    usd_total_price = total_price
                    exchange_rate = 1.0
                else:
                    try:
                        # 转换为USD
                        usd_amount = exchange_rate_service.convert_currency_to_usd(
                            latest_price.unit_price, 
                            currency_code
                        )
                        if usd_amount:
                            usd_unit_price = float(usd_amount)
                            usd_total_price = float(usd_amount)
                            # 获取汇率
                            exchange_rate_obj = exchange_rate_service.get_latest_exchange_rate(currency_code)
                            if exchange_rate_obj and hasattr(exchange_rate_obj, 'rate'):
                                exchange_rate = float(exchange_rate_obj.rate)
                            else:
                                exchange_rate = None
                    except Exception as e:
                        # 如果转换失败，记录日志但不中断
                        print(f"Failed to convert {currency_code} to USD for date {current_date}: {str(e)}")
                        print(f"  - Unit price: {latest_price.unit_price}")
                        print(f"  - Currency: {currency_code}")
                        usd_unit_price = None
                        usd_total_price = None
                        exchange_rate = None
            else:
                # 没有生效的价格，标记为无效
                unit_price = 0
                total_price = 0
                is_valid = False
                remarks = None
                currency_code = "USD"
                usd_unit_price = None
                usd_total_price = None
                exchange_rate = None
            
            price_points.append(PricePoint(
                date=current_date.strftime('%Y-%m-%d'),
                unit_price=unit_price,
                total_price=total_price,
                is_valid=is_valid,
                remarks=remarks,
                currency_code=currency_code,
                usd_unit_price=usd_unit_price,
                usd_total_price=usd_total_price,
                exchange_rate=exchange_rate
            ))
        
        price_trends.append(PriceTrendData(
            tier_id=tier_id,
            min_quantity=min_quantity,
            max_quantity=max_quantity,
            tier_name=tier_name,
            price_points=price_points
        ))
    
    # 获取物品和供应商名称
    item_name = item_supplier.item.name if item_supplier.item else "未知物品"
    supplier_name = (item_supplier.supplier.name_en or 
                    item_supplier.supplier.name_cn or 
                    "未知供应商") if item_supplier.supplier else "未知供应商"
    
    return PriceTrendResponse(
        item_supplier_id=item_supplier_id,
        item_name=item_name,
        supplier_name=supplier_name,
        price_trends=price_trends,
        date_range={
            "start_date": start_date,
            "end_date": end_date
        }
    )


# 优先级管理
@router.put("/items/{item_id}/supplier-priority")
async def update_supplier_priority(
    item_id: int,
    supplier_id: int = Query(..., description="供应商ID"),
    new_priority: int = Query(..., ge=0, le=3, description="新优先级 (0-3)"),
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.update"]))
):
    """
    更新供应商优先级，自动重排其他供应商的优先级
    
    优先级重排算法：
    1. 获取该物品的所有供应商，按优先级排序
    2. 如果目标优先级已被占用，将现有供应商及其后续供应商的优先级都+1
    3. 设置当前供应商为目标优先级
    4. 重新整理优先级序列，保证连续性（0, 1, 2, 3...）
    """
    # 检查物品是否存在
    item = db.query(Item).filter(Item.id == item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="物品不存在")
    
    # 检查要更新的供应商关联是否存在
    target_item_supplier = db.query(ItemSupplier).filter(
        ItemSupplier.item_id == item_id,
        ItemSupplier.supplier_id == supplier_id,
        ItemSupplier.status == "active"
    ).first()
    
    if not target_item_supplier:
        raise HTTPException(status_code=404, detail="物品供应商关联不存在")
    
    # 如果新优先级与当前优先级相同，不需要更新
    if target_item_supplier.priority == new_priority:
        return {"message": "优先级未发生变化", "success": True}
    
    # 获取该物品的所有供应商，按优先级排序
    all_suppliers = db.query(ItemSupplier).filter(
        ItemSupplier.item_id == item_id,
        ItemSupplier.status == "active"
    ).order_by(ItemSupplier.priority.asc().nulls_last()).all()
    
    # 如果只有一个供应商，直接设置为0
    if len(all_suppliers) == 1:
        target_item_supplier.priority = 0
        db.commit()
        return {"message": "优先级更新成功", "success": True}
    
    # 执行优先级重排
    try:
        # 1. 先移除目标供应商的当前优先级（设为临时值-1）
        target_item_supplier.priority = -1
        db.flush()
        
        # 2. 获取除目标供应商外的其他供应商，按优先级排序
        other_suppliers = [s for s in all_suppliers if s.id != target_item_supplier.id]
        other_suppliers.sort(key=lambda x: x.priority if x.priority is not None else 999)
        
        # 3. 重排优先级
        for i, supplier in enumerate(other_suppliers):
            if i >= new_priority:
                supplier.priority = i + 1  # 为新供应商留出位置
            else:
                supplier.priority = i
        
        # 4. 设置目标供应商的新优先级
        target_item_supplier.priority = new_priority
        
        # 5. 确保优先级序列连续且从0开始
        all_updated_suppliers = other_suppliers + [target_item_supplier]
        all_updated_suppliers.sort(key=lambda x: x.priority)
        
        for i, supplier in enumerate(all_updated_suppliers):
            supplier.priority = i
        
        db.commit()
        
        return {
            "message": "优先级更新成功", 
            "success": True,
            "updated_priorities": [
                {
                    "supplier_id": s.supplier_id,
                    "priority": s.priority
                } for s in all_updated_suppliers
            ]
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新优先级失败: {str(e)}")


@router.get("/items/{item_id}/supplier-priorities")
async def get_item_supplier_priorities(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.read"]))
):
    """获取物品的所有供应商优先级信息，用于Item详情页面的优先级下拉选择"""
    # 检查物品是否存在
    item = db.query(Item).filter(Item.id == item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="物品不存在")
    
    # 获取该物品所有供应商的优先级信息
    suppliers = db.query(ItemSupplier).options(
        joinedload(ItemSupplier.supplier)
    ).filter(
        ItemSupplier.item_id == item_id,
        ItemSupplier.status == "active"
    ).order_by(ItemSupplier.priority.asc().nulls_last()).all()
    
    # 构建返回数据
    supplier_priorities = []
    for supplier in suppliers:
        supplier_priorities.append({
            "id": supplier.id,
            "supplier_id": supplier.supplier_id,
            "supplier_name": supplier.supplier.name_en or supplier.supplier.name_cn,
            "priority": supplier.priority,
            "priority_label": f"优先级 {supplier.priority}" if supplier.priority is not None else "无优先级"
        })
    
    # 构建可用的优先级选项
    available_priorities = []
    max_priority = len(suppliers) - 1 if suppliers else 0
    
    for i in range(max_priority + 1):
        available_priorities.append({
            "value": i,
            "label": f"优先级 {i}" + (" (Preferred)" if i == 0 else f" (Alternative{i})" if i <= 3 else ""),
            "occupied": any(s.priority == i for s in suppliers)
        })
    
    return {
        "suppliers": supplier_priorities,
        "available_priorities": available_priorities,
        "total_suppliers": len(suppliers)
    }


# 测试权限检查的API
@router.get("/test-permissions")
async def test_permissions(
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(require_permissions(["supplier.read"]))
):
    """测试权限检查的API端点"""
    return {
        "message": "权限检查通过",
        "user_id": current_user.id,
        "username": current_user.username,
        "is_superuser": current_user.is_superuser,
        "permissions": current_user.permissions if hasattr(current_user, 'permissions') else []
    }


