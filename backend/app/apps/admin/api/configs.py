from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.auth import get_current_user
from app.models.user import User
from app.services.config_service import ConfigService
from app.services.email_service import EmailService
from app.schemas.config import (
    SystemConfigResponse, SystemConfigUpdate, SMTPConfigRequest,
    SMTPConfigResponse, NotificationConfigRequest, NotificationConfigResponse,
    ConfigBulkUpdateRequest, ConfigBulkUpdateResponse
)
from app.schemas.notification import TestEmailRequest, TestEmailResponse

router = APIRouter()


@router.get("", response_model=List[SystemConfigResponse])
async def get_all_configs(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有系统配置（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    service = ConfigService(db)
    configs = service.get_all_configs()
    
    return configs


@router.get("/smtp", response_model=SMTPConfigResponse)
async def get_smtp_config(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取SMTP配置（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    service = ConfigService(db)
    smtp_config = service.get_smtp_config()
    
    return SMTPConfigResponse(**smtp_config)


@router.put("/smtp", response_model=dict)
async def update_smtp_config(
    request: SMTPConfigRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新SMTP配置（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    try:
        service = ConfigService(db)
        service.set_smtp_config(request.dict())
        
        return {"message": "SMTP配置更新成功"}
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新SMTP配置失败: {str(e)}")


@router.get("/notification", response_model=NotificationConfigResponse)
async def get_notification_config(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取通知配置（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    service = ConfigService(db)
    notification_config = service.get_notification_config()
    
    return NotificationConfigResponse(**notification_config)


@router.put("/notification", response_model=dict)
async def update_notification_config(
    request: NotificationConfigRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新通知配置（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    try:
        service = ConfigService(db)
        service.set_notification_config(request.dict())
        
        return {"message": "通知配置更新成功"}
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新通知配置失败: {str(e)}")


@router.put("/bulk", response_model=ConfigBulkUpdateResponse)
async def bulk_update_configs(
    request: ConfigBulkUpdateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量更新配置（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    try:
        service = ConfigService(db)
        service.set_configs(request.configs)
        
        return ConfigBulkUpdateResponse(
            success=True,
            message="配置批量更新成功",
            updated_count=len(request.configs)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量更新配置失败: {str(e)}")


@router.post("/test-email", response_model=TestEmailResponse)
async def test_email_config(
    request: TestEmailRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """发送测试邮件（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    try:
        email_service = EmailService(db)
        result = await email_service.test_email_connection(request.email)
        
        return TestEmailResponse(**result)
        
    except Exception as e:
        return TestEmailResponse(
            success=False,
            message=f"测试邮件发送失败: {str(e)}"
        )


@router.get("/{config_key}")
async def get_config(
    config_key: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取单个配置值（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    service = ConfigService(db)
    config_value = service.get_config(config_key)
    
    if config_value is None:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    # 直接返回配置值，不包含ID等元数据
    return {
        "config_key": config_key,
        "config_value": config_value
    }


@router.put("/{config_key}", response_model=dict)
async def update_config(
    config_key: str,
    request: SystemConfigUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新单个配置（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    try:
        service = ConfigService(db)
        service.set_config(
            config_key=config_key,
            config_value=request.config_value,
            description=request.description
        )
        
        return {"message": f"配置 {config_key} 更新成功"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")


@router.delete("/{config_key}")
async def delete_config(
    config_key: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除配置（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    try:
        service = ConfigService(db)
        success = service.delete_config(config_key)
        
        if not success:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        return {"message": f"配置 {config_key} 删除成功"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除配置失败: {str(e)}")
