"""
采购管理概览API
提供采购管理的统计数据和概览信息
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from typing import List, Optional
from datetime import datetime, timedelta

from app.core.database import get_db
from app.models.purchase import PurchaseRequest, PurchaseRequestItem, PurchaseCartItem
from app.models.user import User
from app.schemas.purchase_overview import (
    PurchaseOverviewResponse, 
    RecentActivityResponse,
    ApprovalTodoResponse
)

router = APIRouter(tags=["采购概览"])


@router.get("/stats", response_model=PurchaseOverviewResponse)
async def get_purchase_overview(
    department_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """获取采购管理概览统计数据"""
    
    # 构建基础查询条件
    base_filter = {}
    if department_id:
        base_filter["department_id"] = department_id
    
    # 1. 采购申请统计
    request_query = db.query(PurchaseRequest)
    if department_id:
        request_query = request_query.filter(PurchaseRequest.department_id == department_id)
    
    total_requests = request_query.count()
    pending_requests = request_query.filter(PurchaseRequest.status == "pending_submission").count()
    approved_requests = request_query.filter(PurchaseRequest.status == "approved").count()
    
    # 2. 采购金额统计
    # 计算总金额（由于预估价格字段已移除，暂时使用其他方式）
    # amount_query = db.query(func.sum(PurchaseRequestItem.estimated_unit_price * PurchaseRequestItem.quantity))
    # total_amount = amount_query.filter(PurchaseRequestItem.request_id == request.id).scalar() or 0
    
    # 暂时设置为0，或者通过其他方式计算
    total_amount = 0
    
    # 3. 购物车统计
    cart_query = db.query(PurchaseCartItem)
    if department_id:
        cart_query = cart_query.filter(PurchaseCartItem.department_id == department_id)
    
    total_cart_items = cart_query.count()
    active_carts = cart_query.distinct(PurchaseCartItem.department_id).count()
    

    
    return PurchaseOverviewResponse(
        total_requests=total_requests,
        pending_requests=pending_requests,
        approved_requests=approved_requests,
        total_amount=float(total_amount),
        total_cart_items=total_cart_items,
        active_carts=active_carts,

    )


@router.get("/recent-activities", response_model=List[RecentActivityResponse])
async def get_recent_activities(
    limit: int = 10,
    department_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """获取最近活动列表"""
    
    # 构建查询条件
    base_filter = {}
    if department_id:
        base_filter["department_id"] = department_id
    
    # 获取最近的采购申请活动
    recent_requests = db.query(PurchaseRequest).filter(
        PurchaseRequest.status.in_(["pending_submission", "approved", "rejected"])
    )
    
    if department_id:
        recent_requests = recent_requests.filter(PurchaseRequest.department_id == department_id)
    
    recent_requests = recent_requests.order_by(PurchaseRequest.updated_at.desc()).limit(limit).all()
    
    activities = []
    for request in recent_requests:
        # 获取提交人信息
        submitter = db.query(User).filter(User.id == request.submitter_id).first()
        submitter_name = submitter.full_name if submitter else "未知用户"
        
        # 计算时间差
        # 确保两个datetime都是timezone-aware的
        current_time = datetime.now(request.updated_at.tzinfo) if request.updated_at.tzinfo else datetime.now()
        time_diff = current_time - request.updated_at
        
        if time_diff.days > 0:
            time_text = f"{time_diff.days}天前"
        elif time_diff.seconds > 3600:
            time_text = f"{time_diff.seconds // 3600}小时前"
        else:
            time_text = f"{time_diff.seconds // 60}分钟前"
        
        # 根据状态确定活动类型
        if request.status == "pending_submission":
            activity_type = "request_created"
            title = "新采购申请已创建"
            description = f"{submitter_name}提交了采购申请"
        elif request.status == "approved":
            activity_type = "request_approved"
            title = "采购申请已批准"
            description = f"{submitter_name}的采购申请已通过审批"
        else:
            activity_type = "request_updated"
            title = "采购申请状态更新"
            description = f"{submitter_name}的采购申请状态已更新"
        
        activities.append(RecentActivityResponse(
            id=request.id,
            type=activity_type,
            title=title,
            description=description,
            time=time_text,
            user=submitter_name,
            status=request.status
        ))
    
    return activities


@router.get("/approval-todos", response_model=ApprovalTodoResponse)
async def get_approval_todos(
    department_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """获取审批待办统计"""
    
    # 构建查询条件
    base_filter = {}
    if department_id:
        base_filter["department_id"] = department_id
    
    # 统计各审批阶段的待办数量
    department_review = db.query(PurchaseRequest).filter(
        PurchaseRequest.status == "under_review"
    )
    if department_id:
        department_review = department_review.filter(PurchaseRequest.department_id == department_id)
    department_review_count = department_review.count()
    
    principle_approval = db.query(PurchaseRequest).filter(
        PurchaseRequest.status == "under_principle_approval"
    )
    if department_id:
        principle_approval = principle_approval.filter(PurchaseRequest.department_id == department_id)
    principle_approval_count = principle_approval.count()
    
    final_approval = db.query(PurchaseRequest).filter(
        PurchaseRequest.status == "under_final_approval"
    )
    if department_id:
        final_approval = final_approval.filter(PurchaseRequest.department_id == department_id)
    final_approval_count = final_approval.count()
    
    return ApprovalTodoResponse(
        department_review=department_review_count,
        principle_approval=principle_approval_count,
        final_approval=final_approval_count
    )


