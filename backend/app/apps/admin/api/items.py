from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_
from typing import Optional, List
import json
from datetime import datetime
from app.core.database import get_db
from app.models.user import User
from app.services.exchange_rate_service import ExchangeRateService
from app.services.item_price_service import ItemPriceService
from app.models.item import (
    Item, ItemCategory, ItemPrimaryCategory, ItemChangeHistory, ItemPropertyConfig
)
from app.models.supplier import ItemSupplier, SupplierPrice
from app.schemas.item import (
    ItemCreate, ItemUpdate, Item as ItemSchema, ItemListResponse,
    ItemCategoryCreate, ItemCategoryUpdate, ItemCategory as ItemCategorySchema,
    ItemPrimaryCategoryCreate, ItemPrimaryCategoryUpdate, ItemPrimaryCategory as ItemPrimaryCategorySchema,
    ItemChangeHistory as ItemChangeHistorySchema,
    ItemSearchParams, ItemCategorySearchParams,
    ItemPropertyConfigCreate, ItemPropertyConfigUpdate, ItemPropertyConfig as ItemPropertyConfigSchema
)
from app.services.auth import get_current_active_user, require_permissions
from app.services.item_code_service import ItemCodeService

router = APIRouter(tags=["物品管理"])

# ==================== 一级分类管理 ====================

@router.get("/primary-categories", response_model=List[ItemPrimaryCategorySchema])
async def list_primary_categories(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取所有一级分类"""
    categories = db.query(ItemPrimaryCategory).filter(
        ItemPrimaryCategory.is_active == True
    ).order_by(ItemPrimaryCategory.name).all()
    return categories

@router.post("/primary-categories", response_model=ItemPrimaryCategorySchema)
async def create_primary_category(
    category: ItemPrimaryCategoryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["item.category_manage"]))
):
    """创建一级分类"""
    # 检查分类名是否已存在
    existing = db.query(ItemPrimaryCategory).filter(
        ItemPrimaryCategory.name == category.name
    ).first()
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="一级分类名称已存在"
        )
    
    # 检查编码前缀是否已存在
    existing_prefix = db.query(ItemPrimaryCategory).filter(
        ItemPrimaryCategory.code_prefix == category.code_prefix
    ).first()
    if existing_prefix:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="编码前缀已存在"
        )
    
    db_category = ItemPrimaryCategory(**category.model_dump())
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    
    return db_category

@router.get("/primary-categories/{category_id}", response_model=ItemPrimaryCategorySchema)
async def get_primary_category(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取单个一级分类详情"""
    category = db.query(ItemPrimaryCategory).filter(
        ItemPrimaryCategory.id == category_id
    ).first()
    if not category:
        raise HTTPException(status_code=404, detail="一级分类不存在")
    
    return category

@router.put("/primary-categories/{category_id}", response_model=ItemPrimaryCategorySchema)
async def update_primary_category(
    category_id: int,
    category: ItemPrimaryCategoryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["item.category_manage"]))
):
    """更新一级分类"""
    db_category = db.query(ItemPrimaryCategory).filter(
        ItemPrimaryCategory.id == category_id
    ).first()
    if not db_category:
        raise HTTPException(status_code=404, detail="一级分类不存在")
    
    # 更新字段
    update_data = category.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_category, field, value)
    
    db.commit()
    db.refresh(db_category)
    
    return db_category

@router.delete("/primary-categories/{category_id}")
async def delete_primary_category(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["item.category_manage"]))
):
    """删除一级分类"""
    db_category = db.query(ItemPrimaryCategory).filter(
        ItemPrimaryCategory.id == category_id
    ).first()
    if not db_category:
        raise HTTPException(status_code=404, detail="一级分类不存在")
    
    # 检查是否有关联的二级分类
    category_count = db.query(ItemCategory).filter(
        ItemCategory.primary_category_id == category_id
    ).count()
    if category_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该一级分类下还有二级分类，无法删除"
        )
    
    db.delete(db_category)
    db.commit()
    
    return {"message": "一级分类删除成功"}

# ==================== 二级分类管理 ====================

@router.get("/categories", response_model=List[ItemCategorySchema])
async def list_categories(
    primary_category_id: Optional[int] = Query(None, description="一级分类ID"),
    is_active: Optional[bool] = Query(None, description="是否启用"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取所有二级分类"""
    query = db.query(ItemCategory)
    
    if primary_category_id is not None:
        query = query.filter(ItemCategory.primary_category_id == primary_category_id)
    
    if is_active is not None:
        query = query.filter(ItemCategory.is_active == is_active)
    
    if search:
        query = query.filter(ItemCategory.name.ilike(f"%{search}%"))
    
    categories = query.order_by(ItemCategory.name).all()
    return categories

@router.post("/categories", response_model=ItemCategorySchema)
async def create_category(
    category: ItemCategoryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["item.category_manage"]))
):
    """创建二级分类"""
    # 检查分类名是否已存在
    existing = db.query(ItemCategory).filter(
        ItemCategory.name == category.name,
        ItemCategory.primary_category_id == category.primary_category_id
    ).first()
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该一级分类下已存在同名二级分类"
        )
    
    db_category = ItemCategory(**category.model_dump())
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    
    return db_category

@router.get("/categories/{category_id}", response_model=ItemCategorySchema)
async def get_category(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取单个二级分类详情"""
    category = db.query(ItemCategory).filter(
        ItemCategory.id == category_id
    ).first()
    if not category:
        raise HTTPException(status_code=404, detail="二级分类不存在")
    
    return category

@router.put("/categories/{category_id}", response_model=ItemCategorySchema)
async def update_category(
    category_id: int,
    category: ItemCategoryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["item.category_manage"]))
):
    """更新二级分类"""
    db_category = db.query(ItemCategory).filter(
        ItemCategory.id == category_id
    ).first()
    if not db_category:
        raise HTTPException(status_code=404, detail="二级分类不存在")
    
    # 更新字段
    update_data = category.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_category, field, value)
    
    db.commit()
    db.refresh(db_category)
    
    return db_category

@router.delete("/categories/{category_id}")
async def delete_category(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["item.category_manage"]))
):
    """删除二级分类"""
    db_category = db.query(ItemCategory).filter(
        ItemCategory.id == category_id
    ).first()
    if not db_category:
        raise HTTPException(status_code=404, detail="二级分类不存在")
    
    # 检查是否有关联的物品
    item_count = db.query(Item).filter(
        Item.category_id == category_id
    ).count()
    if item_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该分类下还有物品，无法删除"
        )
    
    db.delete(db_category)
    db.commit()
    
    return {"message": "二级分类删除成功"}

# ==================== 物品管理 ====================

@router.get("", response_model=ItemListResponse)
async def list_items(
    search: Optional[str] = Query(None, description="搜索关键词"),
    category_id: Optional[int] = Query(None, description="分类ID"),
    primary_category_id: Optional[int] = Query(None, description="一级分类ID"),
    is_active: Optional[bool] = Query(None, description="是否启用"),
    is_purchasable: Optional[bool] = Query(None, description="是否可购买"),
    brand: Optional[str] = Query(None, description="品牌筛选"),
    spec_material: Optional[str] = Query(None, description="规格/材质筛选"),
    size_dimension: Optional[str] = Query(None, description="尺寸/规格筛选"),
    include_pricing: bool = Query(False, description="是否包含定价信息"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取物品列表"""
    query = db.query(Item)
    
    # 搜索条件
    if search:
        search_filter = or_(
            Item.name.ilike(f"%{search}%"),
            Item.code.ilike(f"%{search}%"),
            Item.description.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    if category_id:
        query = query.filter(Item.category_id == category_id)
    
    if primary_category_id:
        query = query.join(Item.category).filter(
            ItemCategory.primary_category_id == primary_category_id
        )
    
    if is_active is not None:
        query = query.filter(Item.is_active == is_active)
    
    if is_purchasable is not None:
        query = query.filter(Item.is_purchasable == is_purchasable)
    
    # 属性筛选
    if brand:
        # 支持多个品牌，用逗号分隔，同一级别内是OR关系
        brand_list = [b.strip() for b in brand.split(',') if b.strip()]
        if brand_list:
            brand_filters = [Item.brand.ilike(f"%{b}%") for b in brand_list]
            query = query.filter(or_(*brand_filters))
    
    if spec_material:
        # 支持多个规格/材质，用逗号分隔，同一级别内是OR关系
        spec_list = [s.strip() for s in spec_material.split(',') if s.strip()]
        if spec_list:
            spec_filters = [Item.spec_material.ilike(f"%{s}%") for s in spec_list]
            query = query.filter(or_(*spec_filters))
    
    if size_dimension:
        # 支持多个尺寸/规格，用逗号分隔，同一级别内是OR关系
        size_list = [s.strip() for s in size_dimension.split(',') if s.strip()]
        if size_list:
            size_filters = [Item.size_dimension.ilike(f"%{s}%") for s in size_list]
            query = query.filter(or_(*size_filters))
    
    # 总数
    total = query.count()
    
    # 分页并预加载分类信息
    items = query.options(
        joinedload(Item.category).joinedload(ItemCategory.primary_category)
    ).offset((page - 1) * size).limit(size).all()
    
    # 如果需要包含定价信息，使用ItemPriceService获取
    if include_pricing:
        try:
            item_price_service = ItemPriceService(db)
            items_with_pricing = []
            
            for item in items:
                try:
                    # 获取价格信息（quantity=None表示获取最小数量价格）
                    price_info = item_price_service.get_item_price_info(
                        item_id=item.id,
                        quantity=None
                    )
                    
                    # 将价格信息添加到物品对象
                    item_dict = {
                        "item": item,
                        "pricing": {
                            "unit_price_usd": price_info["unit_price"]["usd_amount"],
                            "currency": price_info["unit_price"]["currency"],
                            "original_price": price_info["unit_price"]["amount"],
                            "supplier_name": price_info["supplier_name"],
                            "supplier_names": price_info["supplier_names"],
                            "priority": price_info["priority"],
                            "exchange_rate": price_info["exchange_rate"]
                        }
                    }
                    items_with_pricing.append(item_dict)
                    
                except Exception as e:
                    # 如果获取价格失败，只返回物品信息
                    items_with_pricing.append({
                        "item": item,
                        "pricing": None,
                        "pricing_error": str(e)
                    })
            
            return {
                "items": items_with_pricing,
                "total": total,
                "page": page,
                "size": size,
                "pricing_included": True
            }
            
        except Exception as e:
            # 如果整体定价获取失败，返回原始物品列表
            return ItemListResponse(
                items=items,
                total=total,
                page=page,
                size=size
            )
    
    return ItemListResponse(
        items=items,
        total=total,
        page=page,
        size=size
    )

@router.get("/{item_id}/preferred-price")
async def get_item_preferred_price(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取物品的首选供应商价格范围（转换为USD）"""
    try:
        # 使用ItemPriceService获取价格信息（quantity=None表示获取最小数量价格）
        item_price_service = ItemPriceService(db)
        price_info = item_price_service.get_item_price_info(
            item_id=item_id,
            quantity=None  # 获取最小数量价格
        )
        
        # 构建响应
        return {
            "price_range": {
                "min_price": price_info["unit_price"]["usd_amount"],
                "max_price": price_info["unit_price"]["usd_amount"]  # 单个价格，min=max
            },
            "min_price": price_info["unit_price"]["usd_amount"],
            "max_price": price_info["unit_price"]["usd_amount"],
            "supplier": {
                "id": price_info["supplier_id"],
                "name": price_info["supplier_name"],
                "name_cn": price_info["supplier_names"]["name_cn"],
                "name_en": price_info["supplier_names"]["name_en"],
                "priority": price_info["priority"]
            },
            "original_prices": [{
                "unit_price": price_info["unit_price"]["amount"],
                "currency_code": price_info["unit_price"]["currency"],
                "min_quantity": price_info["price_record"]["min_quantity"],
                "max_quantity": price_info["price_record"]["max_quantity"],
                "usd_price": price_info["unit_price"]["usd_amount"],
                "supplier_names": price_info["supplier_names"]
            }],
            "exchange_rate": price_info["exchange_rate"],
            "tier_info": {
                "min_quantity": price_info["price_record"]["min_quantity"],
                "max_quantity": price_info["price_record"]["max_quantity"],
                "valid_from": price_info["price_record"]["valid_from"],
                "valid_to": price_info["price_record"]["valid_to"]
            }
        }
        
    except Exception as e:
        # 如果获取价格失败，返回基本信息
        return {
            "price_range": None,
            "supplier": None,
            "original_prices": [],
            "error": str(e)
        }

@router.post("/preferred-prices/batch")
async def get_items_preferred_prices_batch(
    item_ids: List[int],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """批量获取多个物品的首选供应商价格范围（转换为USD）"""
    if not item_ids:
        return {}
    
    try:
        # 使用ItemPriceService批量获取价格信息
        item_price_service = ItemPriceService(db)
        result = {}
        
        for item_id in item_ids:
            try:
                # 获取价格信息（quantity=None表示获取最小数量价格）
                price_info = item_price_service.get_item_price_info(
                    item_id=item_id,
                    quantity=None  # 获取最小数量价格
                )
                
                result[item_id] = {
                    "price_range": {
                        "min_price": price_info["unit_price"]["usd_amount"],
                        "max_price": price_info["unit_price"]["usd_amount"]  # 单个价格，min=max
                    },
                    "min_price": price_info["unit_price"]["usd_amount"],
                    "max_price": price_info["unit_price"]["usd_amount"],
                    "supplier": {
                        "id": price_info["supplier_id"],
                        "name": price_info["supplier_name"],
                        "name_cn": price_info["supplier_names"]["name_cn"],
                        "name_en": price_info["supplier_names"]["name_en"],
                        "priority": price_info["priority"]
                    },
                    "original_prices": [{
                        "unit_price": price_info["unit_price"]["amount"],
                        "currency_code": price_info["unit_price"]["currency"],
                        "min_quantity": price_info["price_record"]["min_quantity"],
                        "max_quantity": price_info["price_record"]["max_quantity"],
                        "usd_price": price_info["unit_price"]["usd_amount"],
                        "supplier_names": price_info["supplier_names"]
                    }],
                    "exchange_rate": price_info["exchange_rate"],
                    "tier_info": {
                        "min_quantity": price_info["price_record"]["min_quantity"],
                        "max_quantity": price_info["price_record"]["max_quantity"],
                        "valid_from": price_info["price_record"]["valid_from"],
                        "valid_to": price_info["price_record"]["valid_to"]
                    }
                }
                
            except Exception as e:
                # 如果某个物品获取价格失败，记录错误但继续处理其他物品
                result[item_id] = {
                    "price_range": None,
                    "supplier": None,
                    "original_prices": [],
                    "error": str(e)
                }
        
        return result
        
    except Exception as e:
        # 如果整体处理失败，返回错误信息
        return {"error": f"批量获取价格失败: {str(e)}"}

@router.get("/{item_id}", response_model=ItemSchema)
async def get_item(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取单个物品详情"""
    item = db.query(Item).filter(Item.id == item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="物品不存在")
    
    return item

@router.get("/{item_id}/detail")
async def get_item_detail(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取物品详情，包含优先级为0的供应商物品配置"""
    
    item = db.query(Item).options(
        joinedload(Item.category).joinedload(ItemCategory.primary_category)
    ).filter(Item.id == item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="物品不存在")
    
    # 获取所有活跃的供应商配置（按优先级排序）
    suppliers = db.query(ItemSupplier).options(
        joinedload(ItemSupplier.supplier)
    ).filter(
        ItemSupplier.item_id == item_id,
        ItemSupplier.status == "active"
    ).order_by(ItemSupplier.priority).all()
    
    # 获取优先级为0的供应商物品配置
    preferred_supplier = next((s for s in suppliers if s.priority == 0), None)
    
    # 处理所有供应商信息
    suppliers_info = []
    current_time = datetime.now()
    
    for supplier in suppliers:
        # 获取当前有效的价格记录
        price_records = db.query(SupplierPrice).filter(
            SupplierPrice.item_supplier_id == supplier.id,
            SupplierPrice.status == "active",
            SupplierPrice.valid_from <= current_time,
            or_(
                SupplierPrice.valid_to.is_(None),
                SupplierPrice.valid_to > current_time
            )
        ).order_by(SupplierPrice.min_quantity).all()
        
        # 构建阶梯价格信息（转换为USD）
        tiered_prices = []
        exchange_rate_service = ExchangeRateService(db)
        
        for price in price_records:
            original_price = float(price.unit_price)
            
            # 转换为USD
            if price.currency_code == "USD":
                usd_price = original_price
            else:
                converted_price = exchange_rate_service.convert_currency_to_usd(
                    price.unit_price, price.currency_code, current_time.date()
                )
                if converted_price is not None:
                    usd_price = float(converted_price)
                else:
                    # 如果无法转换，跳过这个价格
                    continue
            
            tiered_prices.append({
                "min_quantity": price.min_quantity,
                "max_quantity": price.max_quantity,
                "unit_price": usd_price,  # USD价格
                "original_price": original_price,  # 原始价格
                "currency_code": price.currency_code,  # 原始货币
                "remarks": price.remarks
            })
        
        supplier_info = {
            "id": supplier.id,
            "supplier_id": supplier.supplier_id,
            "supplier_name": supplier.supplier.name_en or supplier.supplier.name_cn,
            "priority": supplier.priority,
            "spq": supplier.spq,
            "moq": supplier.moq,
            "delivery_days": supplier.delivery_days,
            "quality_rating": supplier.quality_rating,
            "tiered_prices": tiered_prices
        }
        suppliers_info.append(supplier_info)
    
    # 获取价格范围（基于优先级为0的供应商，USD价格）
    price_range = None
    preferred_supplier = next((s for s in suppliers_info if s["priority"] == 0), None)
    if preferred_supplier and preferred_supplier["tiered_prices"]:
        prices = [p["unit_price"] for p in preferred_supplier["tiered_prices"]]
        price_range = {
            "min_price": min(prices),
            "max_price": max(prices)
        }
    
    return {
        "item": item,
        "suppliers": suppliers_info,
        "price_range": price_range
    }

@router.get("/{item_id}/pricing-details")
async def get_item_pricing_details(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取物品的详细定价信息（使用ItemPriceService）"""
    try:
        # 使用ItemPriceService获取所有供应商的价格比较
        item_price_service = ItemPriceService(db)
        pricing_comparison = item_price_service.get_supplier_price_comparison(
            item_id=item_id,
            quantity=None  # 获取最小数量价格
        )
        
        return {
            "item_id": item_id,
            "pricing_info": pricing_comparison,
            "message": "获取定价信息成功"
        }
        
    except Exception as e:
        return {
            "item_id": item_id,
            "pricing_info": None,
            "error": str(e),
            "message": "获取定价信息失败"
        }

@router.post("", response_model=ItemSchema)
async def create_item(
    item: ItemCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["item.create"]))
):
    """创建物品"""
    # 检查分类是否存在
    category = db.query(ItemCategory).filter(
        ItemCategory.id == item.category_id
    ).first()
    if not category:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="指定的分类不存在"
        )
    
    # 检查同一分类下是否已存在同名物品
    existing_item = db.query(Item).filter(
        Item.category_id == item.category_id,
        Item.name == item.name
    ).first()
    if existing_item:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"该分类下已存在名为'{item.name}'的物品"
        )
    
    # 自动生成物品编码
    try:
        generated_code = ItemCodeService.generate_item_code(
            db, 
            category.primary_category_id, 
            item.name
        )
        item.code = generated_code
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    
    # 设置默认图片URL（如果没有提供图片）
    if not item.image_url:
        item.image_url = "/static/sample-images/items/no-image-icon.png"
    
    db_item = Item(**item.model_dump())
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    
    return db_item

@router.put("/{item_id}", response_model=ItemSchema)
async def update_item(
    item_id: int,
    item: ItemUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["item.update"]))
):
    """更新物品"""
    db_item = db.query(Item).filter(Item.id == item_id).first()
    if not db_item:
        raise HTTPException(status_code=404, detail="物品不存在")
    
    # 检查物品编码是否已存在
    if item.code and item.code != db_item.code:
        existing = db.query(Item).filter(Item.code == item.code).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="物品编码已存在"
            )
    
    # 检查同一分类下是否已存在同名物品（当更新名称或分类时）
    if (item.name and item.name != db_item.name) or (item.category_id and item.category_id != db_item.category_id):
        check_category_id = item.category_id if item.category_id else db_item.category_id
        check_name = item.name if item.name else db_item.name
        
        existing_item = db.query(Item).filter(
            Item.category_id == check_category_id,
            Item.name == check_name,
            Item.id != item_id  # 排除当前物品
        ).first()
        if existing_item:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"该分类下已存在名为'{check_name}'的物品"
            )
    
    # 更新字段
    update_data = item.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_item, field, value)
    
    # 如果图片URL被清空或设置为None，设置默认图片
    if db_item.image_url is None or db_item.image_url.strip() == "":
        db_item.image_url = "/static/sample-images/items/no-image-icon.png"
    
    db.commit()
    db.refresh(db_item)
    
    return db_item

@router.delete("/{item_id}")
async def delete_item(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["item.delete"]))
):
    """删除物品"""
    db_item = db.query(Item).filter(Item.id == item_id).first()
    if not db_item:
        raise HTTPException(status_code=404, detail="物品不存在")
    
    db.delete(db_item)
    db.commit()
    
    return {"message": "物品删除成功"}

# ==================== 物品变更历史 ====================

@router.get("/{item_id}/change-history", response_model=List[ItemChangeHistorySchema])
async def get_item_change_history(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取物品变更历史"""
    history = db.query(ItemChangeHistory).filter(
        ItemChangeHistory.item_id == item_id
    ).order_by(ItemChangeHistory.created_at.desc()).all()
    
    return history

# ==================== 搜索相关 ====================

@router.get("/search/suggestions")
async def get_search_suggestions(
    search: str = Query(..., description="搜索关键词"),
    limit: int = Query(10, ge=1, le=50, description="返回数量限制"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取搜索建议"""
    from app.services.item_search_service import ItemSearchService
    
    suggestions = ItemSearchService.get_search_suggestions(
        db=db,
        search=search,
        limit=limit
    )
    
    return suggestions

@router.get("/search/statistics")
async def get_search_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取搜索统计信息"""
    from app.services.item_search_service import ItemSearchService
    
    statistics = ItemSearchService.get_search_statistics(db=db)
    
    return statistics

# ==================== 平级分类和属性筛选接口 ====================

@router.get("/categories/{category_id}/peers")
async def get_peer_categories(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取指定分类的同级分类列表"""
    # 获取当前分类信息
    current_category = db.query(ItemCategory).filter(
        ItemCategory.id == category_id,
        ItemCategory.is_active == True
    ).first()
    
    if not current_category:
        raise HTTPException(status_code=404, detail="分类不存在")
    
    # 获取同级分类（同一一级分类下的所有二级分类）
    peer_categories = db.query(ItemCategory).filter(
        ItemCategory.primary_category_id == current_category.primary_category_id,
        ItemCategory.is_active == True
    ).order_by(ItemCategory.name).all()
    
    # 统计每个分类下的物品数量
    result = []
    for category in peer_categories:
        item_count = db.query(Item).filter(
            Item.category_id == category.id,
            Item.is_active == True
        ).count()
        
        result.append({
            "id": category.id,
            "name": category.name,
            "item_count": item_count,
            "is_selected": category.id == category_id
        })
    
    return {"data": result}

@router.get("/categories/{category_id}/attributes")
async def get_category_attributes(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取指定分类下所有物品的属性值"""
    # 验证分类是否存在
    category = db.query(ItemCategory).filter(
        ItemCategory.id == category_id,
        ItemCategory.is_active == True
    ).first()
    
    if not category:
        raise HTTPException(status_code=404, detail="分类不存在")
    
    # 获取该分类下的所有物品
    items = db.query(Item).filter(
        Item.category_id == category_id,
        Item.is_active == True
    ).all()
    
    # 提取属性值并去重
    brands = set()
    spec_materials = set()
    size_dimensions = set()
    
    for item in items:
        if item.brand:
            brands.add(item.brand.strip())
        if item.spec_material:
            spec_materials.add(item.spec_material.strip())
        if item.size_dimension:
            size_dimensions.add(item.size_dimension.strip())
    
    return {
        "data": {
            "brand": sorted(list(brands)),
            "spec_material": sorted(list(spec_materials)),
            "size_dimension": sorted(list(size_dimensions))
        }
    }

# ==================== 物品属性配置管理 ====================

@router.get("/categories/{category_id}/property-configs", response_model=List[ItemPropertyConfigSchema])
async def list_property_configs(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取指定分类的属性配置列表"""
    # 验证分类是否存在
    category = db.query(ItemCategory).filter(
        ItemCategory.id == category_id,
        ItemCategory.is_active == True
    ).first()
    
    if not category:
        raise HTTPException(status_code=404, detail="分类不存在")
    
    configs = db.query(ItemPropertyConfig).filter(
        ItemPropertyConfig.category_id == category_id
    ).order_by(ItemPropertyConfig.attribute_name).all()
    
    return configs

@router.post("/categories/{category_id}/property-configs", response_model=ItemPropertyConfigSchema)
async def create_property_config(
    category_id: int,
    config: ItemPropertyConfigCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["item.category_manage"]))
):
    """创建属性配置"""
    # 验证分类是否存在
    category = db.query(ItemCategory).filter(
        ItemCategory.id == category_id,
        ItemCategory.is_active == True
    ).first()
    
    if not category:
        raise HTTPException(status_code=404, detail="分类不存在")
    
    # 验证属性名称
    valid_attributes = ["brand", "spec_material", "size_dimension"]
    if config.attribute_name not in valid_attributes:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的属性名称"
        )
    
    # 验证输入类型
    valid_input_types = ["select", "text"]
    if config.input_type not in valid_input_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的输入类型"
        )
    
    # 检查是否已存在相同属性的配置
    existing = db.query(ItemPropertyConfig).filter(
        ItemPropertyConfig.category_id == category_id,
        ItemPropertyConfig.attribute_name == config.attribute_name
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该属性已存在配置"
        )
    
    # 验证选项格式
    if config.input_type == "select" and config.options:
        try:
            options_list = json.loads(config.options)
            if not isinstance(options_list, list):
                raise ValueError("选项必须是数组格式")
            if not all(isinstance(option, str) for option in options_list):
                raise ValueError("选项值必须是字符串")
        except (json.JSONDecodeError, ValueError) as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"选项格式错误: {str(e)}"
            )
    
    db_config = ItemPropertyConfig(**config.model_dump())
    db.add(db_config)
    db.commit()
    db.refresh(db_config)
    
    return db_config

@router.get("/categories/{category_id}/property-configs/{config_id}", response_model=ItemPropertyConfigSchema)
async def get_property_config(
    category_id: int,
    config_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取单个属性配置详情"""
    config = db.query(ItemPropertyConfig).filter(
        ItemPropertyConfig.id == config_id,
        ItemPropertyConfig.category_id == category_id
    ).first()
    
    if not config:
        raise HTTPException(status_code=404, detail="属性配置不存在")
    
    return config

@router.put("/categories/{category_id}/property-configs/{config_id}", response_model=ItemPropertyConfigSchema)
async def update_property_config(
    category_id: int,
    config_id: int,
    config: ItemPropertyConfigUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["item.category_manage"]))
):
    """更新属性配置"""
    db_config = db.query(ItemPropertyConfig).filter(
        ItemPropertyConfig.id == config_id,
        ItemPropertyConfig.category_id == category_id
    ).first()
    
    if not db_config:
        raise HTTPException(status_code=404, detail="属性配置不存在")
    
    # 验证输入类型
    update_data = config.model_dump(exclude_unset=True)
    if "input_type" in update_data:
        valid_input_types = ["select", "text"]
        if update_data["input_type"] not in valid_input_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的输入类型"
            )
    
    # 验证选项格式
    if "options" in update_data and update_data["options"]:
        try:
            options_list = json.loads(update_data["options"])
            if not isinstance(options_list, list):
                raise ValueError("选项必须是数组格式")
            if not all(isinstance(option, str) for option in options_list):
                raise ValueError("选项值必须是字符串")
        except (json.JSONDecodeError, ValueError) as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"选项格式错误: {str(e)}"
            )
    
    # 更新字段
    for field, value in update_data.items():
        setattr(db_config, field, value)
    
    db.commit()
    db.refresh(db_config)
    
    return db_config

@router.delete("/categories/{category_id}/property-configs/{config_id}")
async def delete_property_config(
    category_id: int,
    config_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["item.category_manage"]))
):
    """删除属性配置"""
    db_config = db.query(ItemPropertyConfig).filter(
        ItemPropertyConfig.id == config_id,
        ItemPropertyConfig.category_id == category_id
    ).first()
    
    if not db_config:
        raise HTTPException(status_code=404, detail="属性配置不存在")
    
    db.delete(db_config)
    db.commit()
    
    return {"message": "属性配置删除成功"}

@router.get("/categories/{category_id}/property-configs/attribute/{attribute_name}")
async def get_property_config_by_attribute(
    category_id: int,
    attribute_name: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """根据属性名称获取配置"""
    # 验证属性名称
    valid_attributes = ["brand", "spec_material", "size_dimension"]
    if attribute_name not in valid_attributes:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的属性名称"
        )
    
    config = db.query(ItemPropertyConfig).filter(
        ItemPropertyConfig.category_id == category_id,
        ItemPropertyConfig.attribute_name == attribute_name
    ).first()
    
    if not config:
        # 返回默认配置
        return {
            "input_type": "text",
            "options": None
        }
    
    return {
        "input_type": config.input_type,
        "options": json.loads(config.options) if config.options else None
    } 

@router.get("/{item_id}/tier-prices")
async def get_item_tier_prices(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取物品的阶梯价格信息（用于加入购物车时显示预估价格）"""
    from app.models.supplier import ItemSupplier, SupplierPrice
    from datetime import datetime
    
    # 获取物品的首选供应商（优先级为0）
    preferred_supplier = db.query(ItemSupplier).filter(
        ItemSupplier.item_id == item_id,
        ItemSupplier.priority == 0,
        ItemSupplier.status == "active"
    ).first()
    
    if not preferred_supplier:
        return {"tier_prices": [], "supplier": None, "spq_info": None}
    
    # 获取该供应商的所有有效阶梯价格
    current_time = datetime.now()
    tier_prices = db.query(SupplierPrice).filter(
        SupplierPrice.item_supplier_id == preferred_supplier.id,
        SupplierPrice.status == "active",
        SupplierPrice.valid_from <= current_time,
        (SupplierPrice.valid_to.is_(None) | (SupplierPrice.valid_to >= current_time))
    ).order_by(SupplierPrice.min_quantity).all()
    
    # 构建阶梯价格信息
    tier_prices_data = []
    for price in tier_prices:
        tier_prices_data.append({
            "min_quantity": float(price.min_quantity) if price.min_quantity else 0,
            "max_quantity": float(price.max_quantity) if price.max_quantity else None,
            "unit_price": float(price.unit_price),
            "spq_quantity": float(preferred_supplier.spq_quantity) if hasattr(preferred_supplier, 'spq_quantity') and preferred_supplier.spq_quantity else 1.0,
            "spq_unit": preferred_supplier.spq_unit if hasattr(preferred_supplier, 'spq_unit') and preferred_supplier.spq_unit else "个"
        })
    
    return {
        "tier_prices": tier_prices_data,
        "supplier": {
            "id": preferred_supplier.supplier.id,
            "name": preferred_supplier.supplier.name_cn or preferred_supplier.supplier.name_en or "未知供应商"
        },
        "spq_info": {
            "spq_quantity": float(preferred_supplier.spq_quantity) if hasattr(preferred_supplier, 'spq_quantity') and preferred_supplier.spq_quantity else 1.0,
            "spq_unit": preferred_supplier.spq_unit if hasattr(preferred_supplier, 'spq_unit') and preferred_supplier.spq_unit else "个"
        }
    } 