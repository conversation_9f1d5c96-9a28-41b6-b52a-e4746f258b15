from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from app.core.database import get_db
from app.models.user import User
from app.services.approval_service import ApprovalService
from app.schemas.approval import (
    ReviewRequest, ApprovalRequest, RejectRequest, ReturnRequest,
    RequestFlowHistoryResponse, ApprovalSummaryResponse
)
from app.services.auth import get_current_user, require_permissions, has_permission
from app.core.exceptions import BusinessException

router = APIRouter(tags=["审批流程管理"])


@router.post("/requests/{request_id}/review", response_model=RequestFlowHistoryResponse)
async def submit_for_review(
    request_id: int,
    review_data: ReviewRequest,
    current_user: User = Depends(require_permissions(["purchase.review"])),
    db: Session = Depends(get_db)
):
    """部门经理复核"""
    service = ApprovalService(db)
    
    if review_data.approved:
        result = service.submit_for_review(request_id, current_user.id, current_user.full_name or current_user.username, review_data)
    else:
        result = service.reject_request(request_id, current_user.id, current_user.full_name or current_user.username, review_data)
    
    return result


@router.post("/requests/{request_id}/principle-approval", response_model=RequestFlowHistoryResponse)
async def principle_approval(
    request_id: int,
    approval_data: ApprovalRequest,
    current_user: User = Depends(require_permissions(["purchase.principle_approve"])),
    db: Session = Depends(get_db)
):
    """物品管理员主管审批"""
    service = ApprovalService(db)
    
    if approval_data.approved:
        result = service.principle_approval(request_id, current_user.id, current_user.full_name or current_user.username, approval_data)
    else:
        result = service.reject_request(request_id, current_user.id, current_user.full_name or current_user.username, approval_data)
    
    return result


@router.post("/requests/{request_id}/final-approval", response_model=RequestFlowHistoryResponse)
async def final_approval(
    request_id: int,
    approval_data: ApprovalRequest,
    current_user: User = Depends(require_permissions(["purchase.approve"])),
    db: Session = Depends(get_db)
):
    """Boss最终审批"""
    service = ApprovalService(db)
    
    if approval_data.approved:
        result = service.final_approval(request_id, current_user.id, current_user.full_name or current_user.username, approval_data)
    else:
        result = service.reject_request(request_id, current_user.id, current_user.full_name or current_user.username, approval_data)
    
    return result


@router.post("/requests/{request_id}/reject", response_model=RequestFlowHistoryResponse)
async def reject_request(
    request_id: int,
    reject_data: RejectRequest,
    current_user: User = Depends(require_permissions(["purchase.reject"])),
    db: Session = Depends(get_db)
):
    """拒绝申请"""
    service = ApprovalService(db)
    
    result = service.reject_request(request_id, current_user.id, current_user.full_name or current_user.username, reject_data)
    return result


@router.post("/requests/{request_id}/return", response_model=RequestFlowHistoryResponse)
async def return_request(
    request_id: int,
    return_data: ReturnRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """退回申请"""
    service = ApprovalService(db)
    
    result = service.return_request(request_id, current_user.id, current_user.full_name or current_user.username, return_data)
    return result


@router.get("/requests/{request_id}/history", response_model=List[RequestFlowHistoryResponse])
async def get_approval_history(
    request_id: int,
    db: Session = Depends(get_db)
):
    """获取审批历史"""
    service = ApprovalService(db)
    history = service.get_approval_history(request_id)
    return history


@router.get("/pending", response_model=List[dict])
async def get_pending_approvals(
    approval_level: Optional[str] = Query(None, description="审批级别"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取待审批的申请列表"""
    service = ApprovalService(db)
    
    requests = service.get_pending_approvals(current_user.id, approval_level)
    
    # 转换为响应格式
    result = []
    for request in requests:
        result.append({
            "id": request.id,
            "request_no": request.request_no,
            "department_id": request.department_id,
            "submitter_id": request.submitter_id,
            "status": request.status,
            "priority": request.priority,
            "created_at": request.created_at,
            "submitted_at": request.submitted_at
        })
    
    return result


@router.get("/summary", response_model=ApprovalSummaryResponse)
async def get_approval_summary(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取审批汇总统计"""
    service = ApprovalService(db)
    
    # 获取各状态的待审批数量
    under_review = len(service.get_pending_approvals(current_user.id, "review"))
    under_principle_approval = len(service.get_pending_approvals(current_user.id, "principle_approval"))
    under_final_approval = len(service.get_pending_approvals(current_user.id, "final_approval"))
    
    total_pending = under_review + under_principle_approval + under_final_approval
    
    return ApprovalSummaryResponse(
        total_pending=total_pending,
        under_review=under_review,
        under_principle_approval=under_principle_approval,
        under_final_approval=under_final_approval
    )


@router.get("/workflow/status")
async def get_workflow_status():
    """获取工作流状态说明"""
    return {
        "workflow_steps": [
            {
                "step": 1,
                "name": "部门经理复核",
                "status": "under_review",
                "description": "部门经理对申请进行合理性复核"
            },
            {
                "step": 2,
                "name": "物品管理员主管审批",
                "status": "under_principle_approval",
                "description": "主管审批技术和供应商选择"
            },
            {
                "step": 3,
                "name": "Boss最终审批",
                "status": "under_final_approval",
                "description": "最终审批预算和采购决策"
            }
        ],
        "status_transitions": {
            "pending_submission": ["under_review"],
            "under_review": ["under_principle_approval", "pending_submission"],
            "under_principle_approval": ["under_final_approval", "pending_submission"],
            "under_final_approval": ["approved", "pending_submission"],
            "approved": [],
            "rejected": ["pending_submission"]
        }
    }



