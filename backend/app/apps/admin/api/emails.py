from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.auth import get_current_user
from app.models.user import User
from app.services.email_service import EmailService
from app.services.scheduler_service import EmailScheduler
from app.schemas.notification import (
    NotificationEmailListResponse, EmailStatsResponse,
    SchedulerStatusResponse, SchedulerHealthResponse,
    ManualTriggerResponse
)

router = APIRouter()


@router.get("", response_model=NotificationEmailListResponse)
async def get_notification_emails(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    email_status: Optional[str] = Query(None, description="邮件状态"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取邮件发送记录列表（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    service = EmailService(db)
    
    if email_status:
        # 如果指定了状态，获取对应状态的邮件
        if email_status == "failed":
            result = service.get_failed_emails(page, size)
        else:
            # 其他状态可以扩展
            raise HTTPException(status_code=400, detail="不支持的状态筛选")
    else:
        # 获取所有邮件记录
        result = service.get_failed_emails(page, size)  # 暂时使用failed作为默认
    
    return result


@router.get("/stats", response_model=EmailStatsResponse)
async def get_email_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取邮件发送统计信息（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    service = EmailService(db)
    stats = service.get_email_stats()
    
    return stats


@router.get("/failed", response_model=NotificationEmailListResponse)
async def get_failed_emails(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取发送失败的邮件列表（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    service = EmailService(db)
    result = service.get_failed_emails(page, size)
    
    return result


@router.post("/{email_id}/retry")
async def retry_failed_email(
    email_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """重试发送失败的邮件（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    service = EmailService(db)
    success = service.retry_failed_email(email_id)
    
    if not success:
        raise HTTPException(status_code=404, detail="邮件记录不存在或状态不正确")
    
    return {"message": "邮件已重新加入发送队列"}


@router.post("/trigger", response_model=ManualTriggerResponse)
async def trigger_email_sending(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """手动触发邮件发送（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    scheduler = EmailScheduler(db)
    result = await scheduler.manual_trigger()
    
    if not result["success"]:
        raise HTTPException(status_code=400, detail=result["message"])
    
    return result


@router.get("/scheduler/status", response_model=SchedulerStatusResponse)
async def get_scheduler_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取邮件调度器状态（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    scheduler = EmailScheduler(db)
    status = scheduler.get_scheduler_status()
    
    return status


@router.get("/scheduler/health", response_model=SchedulerHealthResponse)
async def get_scheduler_health(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取邮件调度器健康状态（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    scheduler = EmailScheduler(db)
    health = scheduler.health_check()
    
    return health


@router.post("/scheduler/start")
async def start_scheduler(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """启动邮件调度器（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    try:
        scheduler = EmailScheduler(db)
        scheduler.start()
        
        return {"message": "邮件调度器启动成功"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动调度器失败: {str(e)}")


@router.post("/scheduler/stop")
async def stop_scheduler(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """停止邮件调度器（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    try:
        scheduler = EmailScheduler(db)
        scheduler.stop()
        
        return {"message": "邮件调度器停止成功"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"停止调度器失败: {str(e)}")


@router.post("/scheduler/restart")
async def restart_scheduler(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """重启邮件调度器（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    try:
        scheduler = EmailScheduler(db)
        scheduler.restart()
        
        return {"message": "邮件调度器重启成功"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重启调度器失败: {str(e)}")


@router.get("/scheduler/history")
async def get_scheduler_history(
    limit: int = Query(100, ge=1, le=1000, description="历史记录数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取调度器执行历史（超级管理员使用）"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要超级管理员权限")
    
    scheduler = EmailScheduler(db)
    history = scheduler.get_job_history(limit)
    
    return history
