from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from app.core.database import get_db
from app.models.user import User
from app.services.purchase_request_service import PurchaseRequestService
from app.services.qr_code_service import QRCodeService
from app.schemas.purchase_request import (
    CreateRequestRequest, UpdateRequestRequest, PurchaseRequestResponse,
    RequestItemResponse, RequestFilters, StatusUpdateRequest, WithdrawRequest,
    RequestSummaryResponse, SummaryAnalysisRequest, RequestSummaryAnalysisResponse
)
from app.services.auth import get_current_user, require_permissions
from app.core.exceptions import BusinessException
from datetime import datetime
from app.core.constants import (
    PurchaseRequestStatus, FlowActionType, ApprovalLevel, 
    StatusFlow, ProgressCalculator
)
import logging
from sqlalchemy import and_, or_

router = APIRouter(tags=["采购申请管理"])


@router.get("", response_model=List[PurchaseRequestResponse])
async def get_purchase_requests(
    status: Optional[str] = Query(None, description="申请状态"),
    department_id: Optional[int] = Query(None, description="部门ID"),
    submitter_id: Optional[int] = Query(None, description="提交人ID"),
    priority: Optional[str] = Query(None, description="优先级"),
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取采购申请列表"""
    service = PurchaseRequestService(db)
    filters = {}
    if status:
        filters["status"] = status
    if department_id:
        filters["department_id"] = department_id
    if submitter_id:
        filters["submitter_id"] = submitter_id
    if priority:
        filters["priority"] = priority
    
    requests = service.get_all_requests_for_api(filters)
    return requests


@router.get("/summary", response_model=RequestSummaryResponse)
async def get_requests_summary(
    department_id: Optional[int] = Query(None, description="部门ID"),
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取申请汇总统计"""
    service = PurchaseRequestService(db)
    
    # 获取各状态的申请数量
    filters = {"department_id": department_id} if department_id else {}
    
    pending_submission = len(service.get_all_requests_for_api({**filters, "status": "pending_submission"}))
    under_review = len(service.get_all_requests_for_api({**filters, "status": "under_review"}))
    under_principle_approval = len(service.get_all_requests_for_api({**filters, "status": "under_principle_approval"}))
    under_final_approval = len(service.get_all_requests_for_api({**filters, "status": "under_final_approval"}))
    approved = len(service.get_all_requests_for_api({**filters, "status": "approved"}))
    executed = len(service.get_all_requests_for_api({**filters, "status": "executed"}))
    rejected = len(service.get_all_requests_for_api({**filters, "status": "rejected"}))
    
    total_requests = pending_submission + under_review + under_principle_approval + under_final_approval + approved + executed + rejected
    
    return RequestSummaryResponse(
        total_requests=total_requests,
        pending_submission=pending_submission,
        under_review=under_review,
        under_principle_approval=under_principle_approval,
        under_final_approval=under_final_approval,
        approved=approved,
        executed=executed,
        rejected=rejected
    )


@router.get("/{request_id}/item-history")
async def get_request_item_history(
    request_id: int,
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取指定申请单对应的物品申请历史统计"""
    service = PurchaseRequestService(db)
    history_data = service.get_request_item_history(request_id)
    return history_data

@router.get("/department/{department_id}/history")
async def get_department_item_history(
    department_id: int,
    item_ids: str = Query(..., description="物品ID列表，逗号分隔"),
    start_date: str = Query(..., description="开始日期 (YYYY-MM-DD)"),
    end_date: str = Query(..., description="结束日期 (YYYY-MM-DD)"),
    request_id: Optional[int] = Query(None, description="当前申请单ID，用于包含当前申请单数据"),
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取部门物品申请历史统计，包含当前申请单数据"""
    item_id_list = [int(x.strip()) for x in item_ids.split(',') if x.strip()]
    start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
    end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
    service = PurchaseRequestService(db)
    history_data = service.get_department_item_history_with_current(
        department_id=department_id,
        item_ids=item_id_list,
        start_date=start_datetime,
        end_date=end_datetime,
        current_request_id=request_id
    )
    return history_data

@router.post("/departments/amount-history")
async def get_departments_amount_history(
    request_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["purchase.read"]))
):
    """获取多个部门的采购金额历史统计"""
    department_ids = request_data.get("department_ids", [])
    if not department_ids:
        raise HTTPException(status_code=400, detail="部门ID列表不能为空")
    
    service = PurchaseRequestService(db)
    history_data = service.get_department_amount_history(department_ids)
    
    return history_data

@router.post("/suppliers/amount-history")
async def get_suppliers_amount_history(
    request_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["purchase.read"]))
):
    """获取多个供应商的采购金额历史统计"""
    supplier_ids = request_data.get("supplier_ids", [])
    if not supplier_ids:
        raise HTTPException(status_code=400, detail="供应商ID列表不能为空")
    
    service = PurchaseRequestService(db)
    history_data = service.get_supplier_amount_history(supplier_ids)
    
    return history_data


@router.get("/department/{department_id}", response_model=List[PurchaseRequestResponse])
async def get_department_requests(
    department_id: int,
    status: Optional[str] = Query(None, description="申请状态"),
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取部门采购申请列表"""
    service = PurchaseRequestService(db)
    filters = {"department_id": department_id}
    if status:
        filters["status"] = status
    requests = service.get_all_requests(filters)
    return requests


@router.post("", response_model=PurchaseRequestResponse, status_code=status.HTTP_201_CREATED)
async def create_purchase_request(
    request_data: CreateRequestRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建采购申请"""
    service = PurchaseRequestService(db)
    request = service.create_request_from_cart_items(
        department_id=request_data.department_id,
        submitter_id=current_user.id,
        cart_item_ids=request_data.cart_item_ids,
        notes=request_data.notes
    )
    return request


@router.get("/{request_id}", response_model=PurchaseRequestResponse)
async def get_purchase_request(
    request_id: int,
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取采购申请详情"""
    service = PurchaseRequestService(db)
    request = service.get_request_by_id(request_id)
    if not request:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="采购申请不存在"
        )
    return request


@router.put("/{request_id}", response_model=PurchaseRequestResponse)
async def update_purchase_request(
    request_id: int,
    request_data: UpdateRequestRequest,
    current_user: User = Depends(require_permissions(["purchase.update"])),
    db: Session = Depends(get_db)
):
    """更新采购申请"""
    service = PurchaseRequestService(db)
    request = service.update_request(request_id, request_data.dict(exclude_unset=True))
    return request


@router.post("/{request_id}/items", response_model=RequestItemResponse)
async def add_item_to_request(
    request_id: int,
    item_data: Dict[str, Any],
    current_user: User = Depends(require_permissions(["purchase.update"])),
    db: Session = Depends(get_db)
):
    """添加物品到申请"""
    service = PurchaseRequestService(db)
    service.add_item_from_cart(request_id, item_data, current_user.id)
    return {"message": "物品添加成功"}


@router.delete("/{request_id}/items/{item_id}")
async def remove_item_from_request(
    request_id: int,
    item_id: int,
    current_user: User = Depends(require_permissions(["purchase.update"])),
    db: Session = Depends(get_db)
):
    """从申请中移除物品"""
    service = PurchaseRequestService(db)
    service.remove_item_from_request(request_id, item_id, current_user.id)
    return {"message": "物品移除成功"}


@router.put("/{request_id}/items/{item_id}")
async def update_request_item(
    request_id: int,
    item_id: int,
    item_data: Dict[str, Any],
    current_user: User = Depends(require_permissions(["purchase.update"])),
    db: Session = Depends(get_db)
):
    """更新申请明细"""
    service = PurchaseRequestService(db)
    
    if "spq_count" in item_data:
        service.update_item_quantity(request_id, item_data["item_id"], item_data["spq_count"], current_user.id)
    if "notes" in item_data:
        service.update_item_notes(request_id, item_data["item_id"], item_data["notes"], current_user.id)
    
    return {"message": "申请明细更新成功"}


@router.post("/{request_id}/submit", response_model=PurchaseRequestResponse)
async def submit_request(
    request_id: int,
    current_user: User = Depends(require_permissions(["purchase.request"])),
    db: Session = Depends(get_db)
):
    """提交采购申请"""
    service = PurchaseRequestService(db)
    request = service.update_request_status(
        request_id, 
        "under_review", 
        operator_id=current_user.id, 
        operator_name=current_user.full_name or current_user.username, 
        action="submit", 
        comments="申请人提交申请"
    )
    return request


@router.post("/{request_id}/withdraw", response_model=PurchaseRequestResponse)
async def withdraw_request(
    request_id: int,
    withdraw_data: WithdrawRequest,
    current_user: User = Depends(require_permissions(["purchase.withdraw"])),
    db: Session = Depends(get_db)
):
    """撤回采购申请"""
    service = PurchaseRequestService(db)
    request = service.withdraw_request(request_id, current_user.id)
    return request


@router.post("/{request_id}/withdraw-to-pending", response_model=PurchaseRequestResponse)
async def withdraw_to_pending(
    request_id: int,
    current_user: User = Depends(require_permissions(["purchase.withdraw"])),
    db: Session = Depends(get_db)
):
    """撤销申请回到待提交状态"""
    service = PurchaseRequestService(db)
    request = service.withdraw_to_pending(request_id, current_user.id)
    return request


@router.get("/{request_id}/items", response_model=List[RequestItemResponse])
async def get_request_items(
    request_id: int,
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取申请明细列表"""
    service = PurchaseRequestService(db)
    items = service.get_request_items(request_id)
    return items


@router.put("/{request_id}/status", response_model=PurchaseRequestResponse)
async def update_request_status(
    request_id: int,
    status_update: StatusUpdateRequest,
    current_user: User = Depends(require_permissions(["purchase.review"])),
    db: Session = Depends(get_db)
):
    """更新申请状态"""
    service = PurchaseRequestService(db)
    
    # 如果是审批操作，使用审批方法
    if hasattr(status_update, 'action') and hasattr(status_update, 'comments'):
        request = service.approve_request(
            request_id, 
            status_update.action, 
            status_update.comments
        )
    else:
        # 否则使用普通的状态更新
        request = service.update_request_status(request_id, status_update.new_status)
    
    return request


@router.delete("/{request_id}")
async def delete_purchase_request(
    request_id: int,
    return_to_cart: bool = Query(False, description="是否将申请物品返回购物车"),
    current_user: User = Depends(require_permissions(["purchase.delete"])),
    db: Session = Depends(get_db)
):
    """删除采购申请"""
    service = PurchaseRequestService(db)
    success = service.delete_request(request_id, current_user.id, return_to_cart)
    if success:
        message = "申请删除成功"
        if return_to_cart:
            message += "，物品已返回购物车"
        return {"message": message}
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="删除失败"
        )


@router.post("/{request_id}/lock-prices")
async def lock_request_prices(
    request_id: int,
    db: Session = Depends(get_db)
):
    """手动锁定采购申请的最终价格（仅用于测试或特殊情况）"""
    # 由于价格快照逻辑已移除，此端点暂时禁用
    raise HTTPException(status_code=400, detail="价格锁定功能已移除")


@router.get("/{request_id}/progress", response_model=dict)
def get_request_progress(
    request_id: int,
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取申请进度信息"""
    service = PurchaseRequestService(db)
    request = service.get_request_by_id(request_id, current_user)
    if not request:
        raise HTTPException(status_code=404, detail="申请不存在")
    
    # 计算申请进度
    progress = ProgressCalculator.calculate_progress(request.status)
    next_step = ProgressCalculator.get_next_step_description(request.status)
    
    # 获取状态显示名称
    status_display = StatusFlow.STATUS_DISPLAY_NAMES.get(request.status, request.status)
    
    return {
        "request_id": request.id,
        "request_no": request.request_no,
        "current_status": request.status,
        "status_display": status_display,
        "progress": progress,
        "next_step": next_step,
        "allowed_actions": StatusFlow.FLOW_RULES.get(request.status, {}).get("allowed_actions", []),
        "flow_description": StatusFlow.FLOW_RULES.get(request.status, {}).get("description", "")
    }

@router.get("/workflow/status-description", response_model=dict)
def get_workflow_status_description():
    """获取工作流状态说明"""
    return {
        "status_descriptions": StatusFlow.STATUS_DISPLAY_NAMES,
        "action_descriptions": StatusFlow.ACTION_DISPLAY_NAMES,
        "approval_level_descriptions": StatusFlow.APPROVAL_LEVEL_DISPLAY_NAMES,
        "flow_rules": StatusFlow.FLOW_RULES
    }


@router.post("/summary-analysis", response_model=RequestSummaryAnalysisResponse)
async def get_requests_summary_analysis(
    request_data: SummaryAnalysisRequest,
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取采购申请汇总分析
    
    Args:
        request_data: 汇总分析请求数据，包含要汇总的申请ID列表
        current_user: 当前用户
        db: 数据库会话
    
    Returns:
        RequestSummaryAnalysisResponse: 汇总分析结果
    """
    service = PurchaseRequestService(db)
    
    # 权限验证：简化版本，暂时允许所有有purchase.read权限的用户访问
    # TODO: 后续可以添加更细粒度的权限控制
    pass
    
    # 获取汇总分析结果
    analysis_result = service.get_requests_summary_analysis(request_data.request_ids, current_user)
    
    # 转换为响应模型
    return RequestSummaryAnalysisResponse(**analysis_result)


@router.post("/summary-analysis/history")
async def get_summary_analysis_history(
    request_data: dict,
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取汇总分析的历史数据
    
    Args:
        request_data: 包含要汇总的申请ID列表
        current_user: 当前用户
        db: 数据库会话
    
    Returns:
        dict: 历史数据，包括物品历史、部门历史、供应商历史
    """
    request_ids = request_data.get("request_ids", [])
    
    if not request_ids:
        raise HTTPException(status_code=400, detail="申请ID列表不能为空")
    
    service = PurchaseRequestService(db)
    
    # 获取汇总分析的历史数据
    history_data = service.get_summary_analysis_history(request_ids)
    
    return history_data


@router.get("/{request_id}/qr-code")
async def get_purchase_request_qr_code(
    request_id: int,
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """获取采购申请二维码内容
    
    Args:
        request_id: 申请ID
        current_user: 当前用户
        db: 数据库会话
    
    Returns:
        dict: 包含二维码内容的响应
    """
    service = PurchaseRequestService(db)
    request = service.get_request_by_id(request_id)
    
    if not request:
        raise HTTPException(status_code=404, detail="申请不存在")
    
    # 生成二维码内容
    qr_content = QRCodeService.generate_purchase_request_qr_code(request)
    
    return {
        "request_id": request_id,
        "qr_content": qr_content,
        "request_no": request.request_no,
        "department_id": request.department_id,
        "submitter_id": request.submitter_id,
        "status": request.status
    }


@router.get("/{request_id}/exchange-rate-validation")
async def validate_purchase_request_exchange_rates(
    request_id: int,
    current_user: User = Depends(require_permissions(["purchase.read"])),
    db: Session = Depends(get_db)
):
    """检查采购申请的汇率有效性
    
    Args:
        request_id: 申请ID
        current_user: 当前用户
        db: 数据库会话
    
    Returns:
        dict: 汇率验证结果
    """
    from app.services.exchange_rate_service import ExchangeRateService
    
    # 获取申请明细
    service = PurchaseRequestService(db)
    request = service.get_request_by_id(request_id)
    
    if not request:
        raise HTTPException(status_code=404, detail="申请不存在")
    
    # 获取申请明细
    request_items = service.get_request_items(request_id)
    
    if not request_items:
        return {
            "request_id": request_id,
            "overall_valid": True,
            "items_validation": [],
            "blocking_currencies": [],
            "warnings": [],
            "message": "申请中没有需要汇率转换的物品"
        }
    
    # 收集所有涉及的货币
    currencies = set()
    for item in request_items:
        # 获取物品的供应商价格信息
        from app.models.supplier import ItemSupplier, SupplierPrice
        from datetime import datetime
        
        # 查找物品的供应商关系
        item_suppliers = db.query(ItemSupplier).filter(
            ItemSupplier.item_id == item.item_id,
            ItemSupplier.status == "active"
        ).all()
        
        for item_supplier in item_suppliers:
            # 查找有效的价格记录
            current_time = datetime.now()
            prices = db.query(SupplierPrice).filter(
                and_(
                    SupplierPrice.item_supplier_id == item_supplier.id,
                    SupplierPrice.status == "active",
                    SupplierPrice.valid_from <= current_time,
                    or_(
                        SupplierPrice.valid_to.is_(None),
                        SupplierPrice.valid_to > current_time
                    )
                )
            ).all()
            
            for price in prices:
                if price.currency_code != "USD":
                    currencies.add(price.currency_code)
    
    # 检查每种货币的汇率有效性
    if currencies:
        exchange_rate_service = ExchangeRateService(db)
        validation_results = exchange_rate_service.validate_purchase_request_exchange_rates([
            {"currency_code": currency} for currency in currencies
        ])
        
        return {
            "request_id": request_id,
            **validation_results
        }
    
    return {
        "request_id": request_id,
        "overall_valid": True,
        "items_validation": [],
        "blocking_currencies": [],
        "warnings": [],
        "message": "申请中没有需要汇率转换的物品"
    }


@router.put("/{request_id}/time-manipulation")
async def manipulate_request_time(
    request_id: int,
    time_data: Dict[str, str],  # {"created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00", "submitted_at": "2024-01-01T00:00:00"}
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """修改申请单时间（仅限super_admin角色）"""
    # 检查是否为super_admin角色
    if not current_user.role_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要角色权限"
        )
    
    # 获取用户角色
    from app.models.permission import Role
    role = db.query(Role).filter(Role.id == current_user.role_id).first()
    if not role or role.code != "super_admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="仅限超级管理员访问"
        )
    
    service = PurchaseRequestService(db)
    
    # 验证时间格式和规则
    validated_times = {}
    current_time = datetime.now()
    
    for field, time_str in time_data.items():
        if field not in ["created_at", "updated_at", "submitted_at"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的时间字段: {field}"
            )
        
        try:
            parsed_time = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
            # 检查是否为未来时间
            if parsed_time > current_time:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"{field} 不能设置为未来时间"
                )
            validated_times[field] = parsed_time
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的时间格式: {time_str}"
            )
    
    # 更新申请单时间
    updated_request = service.manipulate_request_time(request_id, validated_times)
    
    return {
        "message": "申请单时间修改成功",
        "request_id": request_id,
        "updated_times": validated_times
    }
