"""
库存管理相关的API路由
"""
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_
from typing import List, Optional
from datetime import datetime, date
from decimal import Decimal

from app.core.database import get_db
from app.models.inventory import (
    DepartmentInventory, InventoryChangeRecord,
    InventoryAlert
)
from app.models.item import Item
from app.models.user import Department, User
from app.schemas.inventory import (
    DepartmentInventoryResponse, DepartmentInventoryUpdate,
    InventoryChangeRecordResponse,
    InventoryAlertResponse,
    ManualInRequest, StockAdjustRequest,
    InventoryOverview, StartInventoryRequest
)
from app.services.auth import require_roles
from app.models.supplier import ItemSupplier, SupplierPrice
from app.models.exchange_rate import ExchangeRate
from app.services.usage_statistics_service import UsageStatisticsService
from app.services.inventory_service import InventoryService
from app.services.purchase_cart_service import PurchaseCartService
from app.schemas.purchase_cart import AddCartItemRequest
from app.services.item_price_service import ItemPriceService
from app.core.exceptions import BusinessException

router = APIRouter(tags=["库存管理"])


def get_or_create_inventory(db: Session, department_id: int, item_id: int) -> DepartmentInventory:
    """获取或创建库存记录"""
    inventory = db.query(DepartmentInventory).filter(
        and_(
            DepartmentInventory.department_id == department_id,
            DepartmentInventory.item_id == item_id,
            DepartmentInventory.is_active == True
        )
    ).first()
    
    if not inventory:
        inventory = DepartmentInventory(
            department_id=department_id,
            item_id=item_id,
            current_quantity=Decimal("0"),
            min_quantity=Decimal("0"),
            max_quantity=None,
            is_active=True
        )
        db.add(inventory)
        db.flush()
    
    return inventory


def _update_alerts(db: Session, inventory: DepartmentInventory, current_user: User):
    """更新库存预警 - 只处理有效的库存记录"""
    # 只处理有效的库存记录
    if not inventory.is_active:
        return
    
    # 使用模型的动态计算属性
    status = inventory.status
    
    # 检查预警条件
    is_low = (status == "low")
    is_oos = (status == "out_of_stock")
    is_over = (status == "overstock")
    
    # 处理预警
    alert_conditions = {
        "low_stock": is_low,
        "out_of_stock": is_oos,
        "overstock": is_over,
    }
    
    for alert_type, should_exist in alert_conditions.items():
        active_alert = db.query(InventoryAlert).filter(
            and_(
                InventoryAlert.department_id == inventory.department_id,
                InventoryAlert.item_id == inventory.item_id,
                InventoryAlert.alert_type == alert_type,
                InventoryAlert.is_active == True,
                InventoryAlert.is_resolved == False,
            )
        ).first()
        
        if should_exist and not active_alert:
            # 创建新预警
            messages = {
                "low_stock": "库存低于最小阈值",
                "out_of_stock": "库存为0，缺货",
                "overstock": "库存超过最大阈值",
            }
            
            alert = InventoryAlert(
                department_id=inventory.department_id,
                item_id=inventory.item_id,
                alert_type=alert_type,
                alert_level="warning" if alert_type != "out_of_stock" else "critical",
                message=messages[alert_type],
                current_stock=inventory.current_quantity,
                threshold_value=inventory.min_quantity if alert_type == "low_stock" else (
                    inventory.max_quantity if alert_type == "overstock" else Decimal("0")
                ),
                is_active=True,
                is_resolved=False
            )
            db.add(alert)
        
        elif not should_exist and active_alert:
            # 解决预警
            active_alert.is_resolved = True
            active_alert.resolved_by = current_user.id
            active_alert.resolved_at = datetime.now()


@router.get("/overview", response_model=InventoryOverview)
def get_inventory_overview(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["super_admin", "system_admin", "item_admin", "dept_manager", "dept_item_admin"]))
):
    """获取库存概览 - 统计当前用户部门的有效库存记录"""
    department_id = current_user.department_id
    
    # 获取所有物品和对应的库存记录
    items_with_inventory = db.query(Item, DepartmentInventory).outerjoin(
        DepartmentInventory, and_(
            DepartmentInventory.item_id == Item.id,
            DepartmentInventory.department_id == department_id,
            DepartmentInventory.is_active == True
        )
    ).all()
    
    # 统计各种状态的物品数量（按物品数量统计，不是库存数量）
    low_stock_items = 0
    out_of_stock_items = 0
    overstock_items = 0
    normal_stock_items = 0
    total_value = Decimal("0")
    total_inventory_items = 0
    
    for item, inventory in items_with_inventory:
        # 如果没有库存记录，跳过
        if not inventory:
            continue
            
        total_inventory_items += 1
        
        # 确定库存状态
        status = inventory.status
        
        # 按状态统计物品数量
        if status == "low":
            low_stock_items += 1
        elif status == "out_of_stock":
            out_of_stock_items += 1
        elif status == "overstock":
            overstock_items += 1
        elif status == "normal":
            normal_stock_items += 1
        
        # 计算库存价值：使用最高优先级供应商的当前有效最低价格，并按 qty_per_up 折算
        if inventory.current_quantity and inventory.current_quantity > 0:
            preferred_supplier = db.query(ItemSupplier).filter(
                and_(
                    ItemSupplier.item_id == item.id,
                    ItemSupplier.priority == 0,
                    ItemSupplier.status == "active"
                )
            ).first()

            if preferred_supplier:
                current_time = datetime.now()
                lowest_price = (
                    db.query(SupplierPrice)
                    .filter(
                        and_(
                            SupplierPrice.item_supplier_id == preferred_supplier.id,
                            SupplierPrice.status == "active",
                            SupplierPrice.valid_from <= current_time,
                            or_(
                                SupplierPrice.valid_to.is_(None),
                                SupplierPrice.valid_to > current_time
                            )
                        )
                    )
                    .order_by(SupplierPrice.unit_price.asc())
                    .first()
                )

                if lowest_price:
                    # 使用ItemPriceService计算价格
                    item_price_service = ItemPriceService(db)
                    try:
                        # 计算采购单位数量
                        qty_per_up = item.qty_per_up or 1
                        purchase_units = (
                            (inventory.current_quantity / qty_per_up)
                            if qty_per_up > 0 else inventory.current_quantity
                        )
                        
                        # 获取价格信息（包含USD转换）
                        price_info = item_price_service.get_item_price_info(
                            item_id=item.id,
                            quantity=None,
                            supplier_id=preferred_supplier.supplier_id
                        )
                        
                        if price_info["unit_price"]["usd_amount"]:
                            usd_unit_price = Decimal(str(price_info["unit_price"]["usd_amount"]))
                            item_value = usd_unit_price * purchase_units
                            total_value += item_value
                    except BusinessException:
                        # 如果价格计算失败，跳过此物品的价值计算
                        continue
    
    # 统计活跃预警
    active_alerts = db.query(InventoryAlert).filter(
        and_(
            InventoryAlert.department_id == department_id,
            InventoryAlert.is_active == True,
            InventoryAlert.is_resolved == False
        )
    ).count()
    
    return InventoryOverview(
        total_items=total_inventory_items,  # 库存物品数（有库存记录的物品数量）
        total_departments=1,  # 只统计当前部门
        low_stock_items=low_stock_items,  # 处于低库存状态的物品数量
        out_of_stock_items=out_of_stock_items,  # 处于缺货状态的物品数量
        overstock_items=overstock_items,  # 处于超储状态的物品数量
        normal_stock_items=normal_stock_items,  # 处于正常状态的物品数量
        total_value=total_value,  # 根据最高优先级供应商价格计算的库存总价值
        total_value_usd=total_value,  # USD价值（当前已转换为USD）
        active_alerts=active_alerts
    )


@router.get("/department-inventories")
def get_department_inventories(
    item_id: Optional[int] = Query(None),
    status: Optional[str] = Query(None),
    low_stock_only: bool = Query(False),
    out_of_stock_only: bool = Query(False),
    search: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    include_all_items: bool = Query(True),  # 默认展示所有物品（保留参数以兼容前端，当前逻辑总是返回全部Item）
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["super_admin", "system_admin", "item_admin", "dept_manager", "dept_item_admin"]))
):
    """获取部门库存列表 - 展示所有Item，但只有有效DepartmentInventory时才显示库存信息"""
    department_id = current_user.department_id

    # 参数占位使用，保持对 include_all_items 的显式引用，当前逻辑恒定返回全部Item
    if include_all_items is True or include_all_items is False:
        pass

    # 统一使用左连接查询，展示所有物品
    query = db.query(Item, DepartmentInventory, Department).outerjoin(
        DepartmentInventory, and_(
            DepartmentInventory.item_id == Item.id,
            DepartmentInventory.department_id == department_id,
            DepartmentInventory.is_active == True  # 只关联有效的库存记录
        )
    ).outerjoin(Department, Department.id == department_id)
    
    # 筛选条件
    if item_id:
        query = query.filter(Item.id == item_id)
    
    if search:
        search_filter = or_(
            Item.name.ilike(f"%{search}%"),
            Item.code.ilike(f"%{search}%"),
            Department.name.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    # include_all_items 目前恒定返回全部Item（参数仅为兼容），未来如需按此控制可在此处分支
    _ = include_all_items

    # 排序
    query = query.order_by(Item.name.asc())
    
    # 获取所有记录
    all_records = query.all()
    
    # 统一构建响应
    result = []
    now_dt = datetime.now()
    
    for item, inv, dept in all_records:
        # 处理库存数据 - 只有有效DepartmentInventory时才显示库存信息
        if inv and inv.is_active:
            current_quantity = inv.current_quantity
            min_quantity = inv.min_quantity if inv.min_quantity is not None else Decimal("0")
            max_quantity = inv.max_quantity
            status_value = inv.status
            storage_location = inv.storage_location
            rack_number = inv.rack_number
            notes = inv.notes
            # 使用动态计算/业务规则属性
            last_purchase_price = inv.last_purchase_price
            average_cost = inv.average_cost

            # 计算库存总价值：按最高优先级(0)供应商的当前有效最低价格计算
            total_value = None
            calculated_usd_price = None
            original_currency_price = None
            currency_code = None
            exchange_rate = None
            
            if current_quantity and current_quantity > 0:
                preferred_supplier = db.query(ItemSupplier).filter(
                    and_(
                        ItemSupplier.item_id == item.id,
                        ItemSupplier.priority == 0,
                        ItemSupplier.status == "active"
                    )
                ).first()

                if preferred_supplier:
                    current_time = datetime.now()
                    # 取当前有效的最低单价
                    lowest_price_record = (
                        db.query(SupplierPrice)
                        .filter(
                            and_(
                                SupplierPrice.item_supplier_id == preferred_supplier.id,
                                SupplierPrice.status == "active",
                                SupplierPrice.valid_from <= current_time,
                                or_(
                                    SupplierPrice.valid_to.is_(None),
                                    SupplierPrice.valid_to >= current_time
                                ),
                            )
                        )
                        .order_by(SupplierPrice.unit_price.asc())
                        .first()
                    )

                    if lowest_price_record:
                        # 使用ItemPriceService计算价格
                        item_price_service = ItemPriceService(db)
                        
                        try:
                            # 获取价格信息（包含USD转换）
                            price_info = item_price_service.get_item_price_info(
                                item_id=item.id,
                                quantity=None,
                                supplier_id=preferred_supplier.supplier_id
                            )
                            
                            # 设置价格相关变量
                            original_currency_price = price_info["unit_price"]["amount"]
                            currency_code = price_info["unit_price"]["currency"]
                            exchange_rate = price_info["exchange_rate"]
                            
                            if price_info["unit_price"]["usd_amount"]:
                                calculated_usd_price = Decimal(str(price_info["unit_price"]["usd_amount"]))
                                
                                # 将库存单位数量折算为采购单位数量进行估值
                                purchase_units = (
                                    (current_quantity / item.qty_per_up)
                                    if item.qty_per_up and item.qty_per_up > 0
                                    else current_quantity
                                )
                                # 使用计算后的USD价格计算总价值
                                total_value = calculated_usd_price * purchase_units
                            else:
                                calculated_usd_price = None
                                total_value = None
                        except BusinessException:
                            # 如果价格计算失败，设置默认值
                            calculated_usd_price = None
                            total_value = None
                            original_currency_price = None
                            currency_code = None
                            exchange_rate = None
            
            last_updated = inv.last_updated
            created_at = inv.created_at
            updated_at = inv.updated_at
        else:
            # 无有效库存记录时，显示默认值
            current_quantity = Decimal("0")
            min_quantity = Decimal("0")
            max_quantity = None
            status_value = "no_inventory"
            storage_location = None
            rack_number = None
            notes = None
            # 这些字段现在是动态计算的，无库存时返回None
            last_purchase_price = None
            average_cost = None
            total_value = None
            calculated_usd_price = None
            original_currency_price = None
            currency_code = None
            exchange_rate = None
            last_updated = now_dt
            created_at = now_dt
            updated_at = now_dt
        
        # 根据过滤条件决定是否包含此记录
        if low_stock_only and not (current_quantity > 0 and current_quantity <= min_quantity):
            continue
        
        if out_of_stock_only and not (current_quantity == 0 and (min_quantity is None or min_quantity > 0)):
            continue
        
        # 状态筛选逻辑
        if status:
            if status == "enabled":
                # "enabled" 状态：只显示已启用的库存记录
                if not (inv and inv.is_active):
                    continue
            elif status_value != status:
                # 其他状态：按库存状态筛选
                continue
        
        # 计算采购单位数量
        purchase_unit_quantity = None
        if item.qty_per_up > 1 and current_quantity > 0:
            purchase_unit_quantity = current_quantity / item.qty_per_up
        
        result.append(DepartmentInventoryResponse(
            id=inv.id if inv else item.id,
            department_id=department_id,
            item_id=item.id,
            current_quantity=current_quantity,
            min_quantity=min_quantity,
            max_quantity=max_quantity,
            storage_location=storage_location,
            rack_number=rack_number,
            status=status_value,
            notes=notes,
            department_name=dept.name if dept else "",
            item_name=item.name,
            item_code=item.code,
            purchase_unit=item.purchase_unit,
            inventory_unit=item.inventory_unit,
            qty_per_up=item.qty_per_up,
            purchase_unit_quantity=purchase_unit_quantity,
            last_purchase_price=last_purchase_price,
            average_cost=average_cost,
            total_value=total_value,
            calculated_usd_price=calculated_usd_price,
            original_currency_price=original_currency_price,
            currency_code=currency_code,
            exchange_rate=exchange_rate,
            last_updated=last_updated,
            created_at=created_at,
            updated_at=updated_at,
            item_image_url=item.image_url,
            has_inventory=inv is not None and inv.is_active,
            is_active=inv.is_active if inv else True
        ))
    
    # 获取总记录数（过滤后）
    total_count = len(result)
    
    # 应用分页
    start_index = skip
    end_index = skip + limit
    paginated_result = result[start_index:end_index]
    
    return {
        "items": paginated_result,
        "total": total_count,
        "skip": skip,
        "limit": limit
    }


@router.put("/department-inventories/{inventory_id}", response_model=dict)
def update_inventory_settings(
    inventory_id: int,
    request: DepartmentInventoryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["super_admin", "system_admin", "warehouse_keeper", "dept_item_admin"]))
):
    """更新库存设置 - 仅限当前用户部门"""
    # 验证库存记录是否属于当前用户部门
    inventory = db.query(DepartmentInventory).filter(
        and_(
            DepartmentInventory.id == inventory_id,
            DepartmentInventory.department_id == current_user.department_id,
            DepartmentInventory.is_active == True
        )
    ).first()
    
    if not inventory:
        raise HTTPException(status_code=404, detail="库存记录不存在或不属于当前部门")
    
    # 更新字段
    if request.min_quantity is not None:
        inventory.min_quantity = request.min_quantity
    if request.max_quantity is not None:
        inventory.max_quantity = request.max_quantity
    if request.storage_location is not None:
        inventory.storage_location = request.storage_location
    if request.rack_number is not None:
        inventory.rack_number = request.rack_number
    if request.notes is not None:
        inventory.notes = request.notes
    if request.is_active is not None:
        inventory.is_active = request.is_active
    
    inventory.updated_at = datetime.now()
    inventory.last_updated = datetime.now()
    
    _update_alerts(db, inventory, current_user)
    db.commit()
    
    return {
        "success": True,
        "message": "库存设置更新成功",
        "inventory_id": inventory.id
    }


@router.post("/department-inventories/start", response_model=dict)
def start_inventory(
    request: StartInventoryRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["super_admin", "system_admin", "warehouse_keeper", "dept_item_admin"]))
):
    """启动库存 - 为无库存记录的物品创建库存记录"""
    department_id = current_user.department_id
    
    # 检查是否已存在库存记录
    existing_inventory = db.query(DepartmentInventory).filter(
        and_(
            DepartmentInventory.department_id == department_id,
            DepartmentInventory.item_id == request.item_id,
            DepartmentInventory.is_active == True
        )
    ).first()
    
    if existing_inventory:
        raise HTTPException(status_code=400, detail="该物品已有库存记录")
    
    # 验证物品是否存在
    item = db.query(Item).filter(Item.id == request.item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="物品不存在")
    
    # 创建新的库存记录
    inventory = DepartmentInventory(
        department_id=department_id,
        item_id=request.item_id,
        current_quantity=Decimal("0"),
        min_quantity=request.min_quantity if request.min_quantity is not None else Decimal("0"),
        max_quantity=request.max_quantity,
        storage_location=request.storage_location,
        rack_number=request.rack_number,
        notes=request.notes,
        is_active=True
    )
    
    db.add(inventory)
    db.flush()
    
    # 更新预警
    _update_alerts(db, inventory, current_user)
    db.commit()
    
    return {
        "success": True,
        "message": "库存启动成功",
        "inventory_id": inventory.id
    }


@router.get("/inventory-change-records", response_model=List[InventoryChangeRecordResponse])
def get_inventory_change_records(
    item_id: Optional[int] = Query(None),
    change_type: Optional[str] = Query(None),
    operator_id: Optional[int] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["super_admin", "system_admin", "item_admin", "dept_manager", "dept_item_admin"]))
):
    """获取库存变更记录 - 仅限当前用户部门"""
    department_id = current_user.department_id
    
    query = db.query(InventoryChangeRecord, Item, Department, User).join(
        Item, InventoryChangeRecord.item_id == Item.id
    ).join(
        Department, InventoryChangeRecord.department_id == Department.id
    ).join(
        User, InventoryChangeRecord.operator_id == User.id
    ).filter(
        InventoryChangeRecord.department_id == department_id
    )
    
    if item_id:
        query = query.filter(InventoryChangeRecord.item_id == item_id)
    if change_type:
        query = query.filter(InventoryChangeRecord.change_type == change_type)
    if operator_id:
        query = query.filter(InventoryChangeRecord.operator_id == operator_id)
    if start_date:
        query = query.filter(InventoryChangeRecord.change_date >= start_date)
    if end_date:
        query = query.filter(InventoryChangeRecord.change_date <= end_date)
    
    records = query.order_by(desc(InventoryChangeRecord.change_date)).offset(skip).limit(limit).all()
    
    result = []
    for record, item, dept, operator in records:
        result.append(InventoryChangeRecordResponse(
            id=record.id,
            department_id=record.department_id,
            item_id=record.item_id,
            before_quantity=record.before_quantity,
            after_quantity=record.after_quantity,
            change_quantity=record.change_quantity,
            change_type=record.change_type,
            change_reason=record.change_reason,
            operator_id=record.operator_id,
            remarks=record.remarks,
            department_name=dept.name,
            item_name=item.name,
            item_code=item.code,
            operator_name=operator.full_name or operator.username,
            change_date=record.change_date,
            created_at=record.created_at
        ))
    
    return result


@router.post("/manual-in", response_model=dict)
def process_manual_in(
    request: ManualInRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["super_admin", "system_admin", "dept_item_admin"]))
):
    """处理手工入库操作 - 如果无DepartmentInventory则创建该记录"""
    # 验证物品
    item = db.query(Item).filter(Item.id == request.item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="物品不存在")
    
    # 使用当前用户的部门ID
    department_id = request.department_id or current_user.department_id
    
    # 根据单位类型进行数量换算
    # 如果没有指定单位，根据 qty_per_up 决定：qty_per_up=1 时默认使用库存单位，否则使用采购单位
    quantity_unit = request.quantity_unit
    if not quantity_unit:
        quantity_unit = "purchase" if item.qty_per_up > 1 else "inventory"
    
    if quantity_unit == "purchase":
        # 采购单位转换为库存单位
        change_quantity = request.quantity * item.qty_per_up
    else:
        # 库存单位，直接使用
        change_quantity = request.quantity
    
    # 创建库存记录（如果不存在）
    inventory = get_or_create_inventory(db, department_id, request.item_id)
    
    # 更新价格信息（如果提供了单价）
    if request.unit_price:
        before_quantity = inventory.current_quantity
        inventory.last_purchase_price = request.unit_price
        if inventory.average_cost:
            total_cost = inventory.average_cost * before_quantity + request.unit_price * request.quantity
            total_quantity = before_quantity + request.quantity
            inventory.average_cost = total_cost / total_quantity
        else:
            inventory.average_cost = request.unit_price
        db.flush()  # 确保价格信息更新
    
    # 使用库存服务进行库存变更
    try:
        InventoryService.change_stock(
            db=db,
            department_id=department_id,
            item_id=request.item_id,
            change_amount=change_quantity,
            operator_id=current_user.id,
            reason="手工入库"
        )
        
        # 获取更新后的库存记录用于预警更新
        inventory = db.query(DepartmentInventory).filter(
            and_(
                DepartmentInventory.department_id == department_id,
                DepartmentInventory.item_id == request.item_id,
                DepartmentInventory.is_active == True
            )
        ).first()
        _update_alerts(db, inventory, current_user)
        
    except ValueError as e:
        # 如果库存记录不存在，先创建再变更
        if "库存记录不存在" in str(e):
            inventory = get_or_create_inventory(db, department_id, request.item_id)
            db.flush()
            InventoryService.change_stock(
                db=db,
                department_id=department_id,
                item_id=request.item_id,
                change_amount=change_quantity,
                operator_id=current_user.id,
                reason="手工入库"
            )
            _update_alerts(db, inventory, current_user)
        else:
            raise e
    
    # 注意：新的统计服务直接统计ItemUsageRecord表，不需要更新统计表
    
    db.commit()
    
    # 获取最新的库存数量
    updated_inventory = db.query(DepartmentInventory).filter(
        and_(
            DepartmentInventory.department_id == department_id,
            DepartmentInventory.item_id == request.item_id,
            DepartmentInventory.is_active == True
        )
    ).first()
    
    return {
        "success": True,
        "message": "手工入库操作成功",
        "new_quantity": updated_inventory.current_quantity if updated_inventory else Decimal("0")
    }


@router.post("/adjust-stock", response_model=dict)
def adjust_stock(
    request: StockAdjustRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["super_admin", "system_admin", "dept_item_admin"]))
):
    """库存调整操作 - 如果无DepartmentInventory则创建该记录"""
    # 验证物品
    item = db.query(Item).filter(Item.id == request.item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="物品不存在")
    
    # 使用当前用户的部门ID
    department_id = current_user.department_id
    
    # 根据单位类型进行数量换算
    # 如果没有指定单位，根据 qty_per_up 决定：qty_per_up=1 时默认使用库存单位，否则使用采购单位
    quantity_unit = request.quantity_unit
    if not quantity_unit:
        quantity_unit = "inventory" if item.qty_per_up == 1 else "purchase"
    
    if quantity_unit == "purchase":
        # 采购单位转换为库存单位
        change_quantity = request.quantity * item.qty_per_up
    else:
        # 库存单位，直接使用
        change_quantity = request.quantity
    
    # 创建库存记录（如果不存在）
    inventory = get_or_create_inventory(db, department_id, request.item_id)
    before_quantity = inventory.current_quantity
    new_quantity = before_quantity + change_quantity
    
    if new_quantity < 0:
        raise HTTPException(status_code=400, detail="库存不足，无法调整")
    
    # 使用库存服务进行库存调整
    try:
        InventoryService.adjust_stock(
            db=db,
            department_id=department_id,
            item_id=request.item_id,
            new_quantity=new_quantity,
            operator_id=current_user.id,
            reason=request.adjustment_reason
        )
        
        # 获取更新后的库存记录用于预警更新
        inventory = db.query(DepartmentInventory).filter(
            and_(
                DepartmentInventory.department_id == department_id,
                DepartmentInventory.item_id == request.item_id,
                DepartmentInventory.is_active == True
            )
        ).first()
        _update_alerts(db, inventory, current_user)
        
    except ValueError as e:
        # 如果库存记录不存在，先创建再调整
        if "库存记录不存在" in str(e):
            inventory = get_or_create_inventory(db, department_id, request.item_id)
            db.flush()
            InventoryService.adjust_stock(
                db=db,
                department_id=department_id,
                item_id=request.item_id,
                new_quantity=new_quantity,
                operator_id=current_user.id,
                reason=request.adjustment_reason
            )
            _update_alerts(db, inventory, current_user)
        else:
            raise e
    
    # 注意：新的统计服务直接统计ItemUsageRecord表，不需要更新统计表
    
    db.commit()
    
    # 获取最新的库存数量
    updated_inventory = db.query(DepartmentInventory).filter(
        and_(
            DepartmentInventory.department_id == department_id,
            DepartmentInventory.item_id == request.item_id,
            DepartmentInventory.is_active == True
        )
    ).first()
    
    return {
        "success": True,
        "message": "库存调整操作成功",
        "new_quantity": updated_inventory.current_quantity if updated_inventory else Decimal("0")
    }


@router.get("/usage-statistics", response_model=List[dict])
def get_usage_statistics(
    item_id: Optional[int] = Query(None),
    year: Optional[int] = Query(None),
    month: Optional[int] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["super_admin", "system_admin", "item_admin", "dept_manager", "dept_item_admin"]))
):
    """获取库存使用统计 - 直接统计ItemUsageRecord表"""
    try:
        statistics = UsageStatisticsService.get_usage_statistics(
            db=db,
            department_id=current_user.department_id,
            item_id=item_id,
            year=year,
            month=month,
            start_date=start_date,
            end_date=end_date,
            skip=skip,
            limit=limit
        )
        
        return statistics
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取使用统计失败: {str(e)}")


@router.get("/alerts", response_model=List[InventoryAlertResponse])
def get_inventory_alerts(
    item_id: Optional[int] = Query(None),
    alert_type: Optional[str] = Query(None),
    alert_level: Optional[str] = Query(None),
    is_resolved: Optional[bool] = Query(None),
    is_active: Optional[bool] = Query(True),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["super_admin", "system_admin", "item_admin", "dept_manager", "dept_item_admin"]))
):
    """获取库存预警 - 仅限当前用户部门"""
    department_id = current_user.department_id
    
    query = db.query(InventoryAlert, Item, Department, User).join(
        Item, InventoryAlert.item_id == Item.id
    ).join(
        Department, InventoryAlert.department_id == Department.id
    ).outerjoin(
        User, InventoryAlert.resolved_by == User.id
    ).filter(
        InventoryAlert.department_id == department_id
    )
    
    if item_id:
        query = query.filter(InventoryAlert.item_id == item_id)
    if alert_type:
        query = query.filter(InventoryAlert.alert_type == alert_type)
    if alert_level:
        query = query.filter(InventoryAlert.alert_level == alert_level)
    if is_resolved is not None:
        query = query.filter(InventoryAlert.is_resolved == is_resolved)
    if is_active is not None:
        query = query.filter(InventoryAlert.is_active == is_active)
    
    records = query.order_by(desc(InventoryAlert.created_at)).offset(skip).limit(limit).all()
    
    result = []
    for record, item, dept, resolver in records:
        result.append(InventoryAlertResponse(
            id=record.id,
            department_id=record.department_id,
            item_id=record.item_id,
            alert_type=record.alert_type,
            alert_level=record.alert_level,
            message=record.message,
            current_stock=record.current_stock,
            threshold_value=record.threshold_value,
            department_name=dept.name,
            item_name=item.name,
            item_code=item.code,
            is_active=record.is_active,
            is_resolved=record.is_resolved,
            resolved_by=record.resolved_by,
            resolved_at=record.resolved_at,
            resolver_name=resolver.full_name or resolver.username if resolver else None,
            created_at=record.created_at,
            updated_at=record.updated_at
        ))
    
    return result


@router.post("/alerts/{alert_id}/resolve", response_model=InventoryAlertResponse)
def resolve_alert(
    alert_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["super_admin", "system_admin", "item_admin", "dept_manager", "dept_item_admin"]))
):
    """解决库存预警 - 仅限当前用户部门"""
    department_id = current_user.department_id
    
    alert = db.query(InventoryAlert).filter(
        and_(
            InventoryAlert.id == alert_id,
            InventoryAlert.department_id == department_id
        )
    ).first()
    
    if not alert:
        raise HTTPException(status_code=404, detail="预警不存在或不属于当前部门")
    
    alert.is_resolved = True
    alert.resolved_by = current_user.id
    alert.resolved_at = datetime.now()
    alert.updated_at = datetime.now()
    
    db.commit()
    
    # 返回更新后的预警信息
    item = db.query(Item).filter(Item.id == alert.item_id).first()
    dept = db.query(Department).filter(Department.id == alert.department_id).first()
    resolver = db.query(User).filter(User.id == alert.resolved_by).first()
    
    return InventoryAlertResponse(
        id=alert.id,
        department_id=alert.department_id,
        item_id=alert.item_id,
        alert_type=alert.alert_type,
        alert_level=alert.alert_level,
        message=alert.message,
        current_stock=alert.current_stock,
        threshold_value=alert.threshold_value,
        department_name=dept.name if dept else "",
        item_name=item.name if item else "",
        item_code=item.code if item else "",
        is_active=alert.is_active,
        is_resolved=alert.is_resolved,
        resolved_by=alert.resolved_by,
        resolved_at=alert.resolved_at,
        resolver_name=resolver.full_name or resolver.username if resolver else None,
        created_at=alert.created_at,
        updated_at=alert.updated_at
    )


@router.get("/usage-statistics-summary", response_model=dict)
def get_usage_statistics_summary(
    year: Optional[int] = Query(None, description="年份"),
    month: Optional[int] = Query(None, description="月份"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["super_admin", "system_admin", "item_admin", "dept_manager", "dept_item_admin"]))
):
    """获取使用统计摘要"""
    try:
        if year:
            summary = UsageStatisticsService.get_monthly_statistics(
                db=db,
                department_id=current_user.department_id,
                year=year,
                month=month
            )
        else:
            summary = UsageStatisticsService.get_department_summary(
                db=db,
                department_id=current_user.department_id
            )
        
        return summary
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计摘要失败: {str(e)}")


@router.get("/top-used-items", response_model=List[dict])
def get_top_used_items(
    limit: int = Query(10, ge=1, le=50, description="返回记录数限制"),
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["super_admin", "system_admin", "item_admin", "dept_manager", "dept_item_admin"]))
):
    """获取最常用物品排行"""
    try:
        top_items = UsageStatisticsService.get_top_used_items(
            db=db,
            department_id=current_user.department_id,
            limit=limit,
            days=days
        )
        
        return top_items
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取最常用物品排行失败: {str(e)}")


@router.post("/add-to-cart", response_model=dict)
def add_to_cart(
    request: AddCartItemRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_roles(["super_admin", "system_admin", "item_admin", "dept_manager", "dept_item_admin"]))
):
    """一键将缺货物品添加到购物车"""
    department_id = current_user.department_id
    
    # 获取所有缺货物品
    out_of_stock_items = db.query(Item, DepartmentInventory).outerjoin(
        DepartmentInventory, and_(
            DepartmentInventory.item_id == Item.id,
            DepartmentInventory.department_id == department_id,
            DepartmentInventory.is_active == True,
            DepartmentInventory.status == "out_of_stock" # 只添加缺货物品
        )
    ).all()
    
    added_items = []
    for item, inventory in out_of_stock_items:
        if inventory: # 确保有库存记录
            try:
                # 使用采购单位进行数量换算
                qty_per_up = item.qty_per_up or 1
                quantity_to_add = request.quantity * qty_per_up
                
                # 使用库存服务添加到购物车
                PurchaseCartService.add_item_to_cart(
                    db=db,
                    department_id=department_id,
                    item_id=item.id,
                    quantity=quantity_to_add,
                    operator_id=current_user.id,
                    reason="一键添加到购物车"
                )
                added_items.append({
                    "item_id": item.id,
                    "item_name": item.name,
                    "quantity": quantity_to_add,
                    "unit": item.inventory_unit
                })
            except ValueError as e:
                # 如果添加失败，可能是库存记录不存在或数量不足
                if "库存记录不存在" in str(e):
                    # 尝试创建库存记录并添加
                    inventory = get_or_create_inventory(db, department_id, item.id)
                    db.flush()
                    qty_per_up = item.qty_per_up or 1
                    quantity_to_add = request.quantity * qty_per_up
                    PurchaseCartService.add_item_to_cart(
                        db=db,
                        department_id=department_id,
                        item_id=item.id,
                        quantity=quantity_to_add,
                        operator_id=current_user.id,
                        reason="一键添加到购物车"
                    )
                    added_items.append({
                        "item_id": item.id,
                        "item_name": item.name,
                        "quantity": quantity_to_add,
                        "unit": item.inventory_unit
                    })
                else:
                    raise e
    
    return {
        "success": True,
        "message": "已将所有缺货物品添加到购物车",
        "added_items": added_items
    }
