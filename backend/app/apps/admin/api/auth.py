from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from app.core.config import settings
from app.core.database import get_db
from app.models.user import User
from app.schemas.user import User<PERSON>ogin, Token, User as UserSchema, UserCreate
from app.services.auth import (
    authenticate_user, 
    create_access_token, 
    get_current_active_user,
    get_password_hash,
    get_user_with_permissions,
    update_login_info,
    get_user
)

router = APIRouter()

@router.post("/login", response_model=Token)
async def login(user_credentials: UserLogin, db: Session = Depends(get_db), request: Request = None):
    """用户登录"""
    result = authenticate_user(db, user_credentials.username, user_credentials.password)
    
    if isinstance(result, tuple) and len(result) == 2:
        user, error_message = result
        if not user:
            # 登录失败，更新失败次数
            db_user = get_user(db, user_credentials.username)
            if db_user:
                update_login_info(db, db_user, 
                                ip_address=request.client.host if request else None, 
                                success=False)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=error_message,
                headers={"WWW-Authenticate": "Bearer"},
            )
    else:
        # 向后兼容
        user = result
        if not user:
            # 登录失败，更新失败次数
            db_user = get_user(db, user_credentials.username)
            if db_user:
                update_login_info(db, db_user, 
                                ip_address=request.client.host if request else None, 
                                success=False)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    # 登录成功，更新登录信息
    update_login_info(db, user, 
                      ip_address=request.client.host if request else None, 
                      success=True)
    
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    
    # 获取包含权限的用户信息
    user_data = get_user_with_permissions(db, user)
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.access_token_expire_minutes * 60,  # 转换为秒
        "user": user_data
    }

@router.post("/register", response_model=UserSchema)
async def register(user: UserCreate, db: Session = Depends(get_db)):
    """用户注册"""
    # 检查用户名是否已存在
    db_user = db.query(User).filter(User.username == user.username).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    db_user = db.query(User).filter(User.email == user.email).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )
    
    # 创建新用户
    hashed_password = get_password_hash(user.password)
    db_user = User(
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        department=user.department,
        role=user.role,
        hashed_password=hashed_password
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return UserSchema.from_orm(db_user)

@router.get("/me", response_model=dict)
async def read_users_me(current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    """获取当前用户信息"""
    # 返回包含权限的用户信息
    return get_user_with_permissions(db, current_user)

@router.post("/logout")
async def logout():
    """用户登出（客户端需要删除token）"""
    return {"message": "成功登出"}
