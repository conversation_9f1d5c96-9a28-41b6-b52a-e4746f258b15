"""
文件上传API
支持图片上传、文件管理等功能
"""

from typing import List
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Form
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.file_upload_service import file_upload_service
from app.services.auth import get_current_user
from app.models.user import User
from pathlib import Path

router = APIRouter(tags=["文件上传"])


@router.post("/images")
async def upload_image(
    file: UploadFile = File(...),
    sub_dir: str = Form("items"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    上传单个图片
    
    Args:
        file: 上传的图片文件
        sub_dir: 子目录
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        上传结果信息
    """
    try:
        result = await file_upload_service.upload_image(file, sub_dir)
        return {
            "success": True,
            "message": "图片上传成功",
            "data": result
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")


@router.post("/images/batch")
async def upload_images_batch(
    files: List[UploadFile] = File(...),
    sub_dir: str = "items",
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    批量上传图片
    
    Args:
        files: 上传的图片文件列表
        sub_dir: 子目录
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        批量上传结果
    """
    try:
        results = await file_upload_service.batch_upload_images(files, sub_dir)
        return {
            "success": True,
            "message": "批量上传完成",
            "data": results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量上传失败: {str(e)}")





@router.get("/images/{sub_dir}/{filename}")
async def get_image(sub_dir: str, filename: str):
    """
    获取图片文件
    
    Args:
        sub_dir: 子目录
        filename: 文件名
        
    Returns:
        图片文件
    """
    try:
        image_path = file_upload_service.images_dir / sub_dir / filename
        if not image_path.exists():
            raise HTTPException(status_code=404, detail="图片不存在")
        
        return FileResponse(image_path)
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图片失败: {str(e)}")


@router.delete("/images/{sub_dir}/{filename}")
async def delete_image(
    sub_dir: str,
    filename: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除图片文件
    
    Args:
        sub_dir: 子目录
        filename: 文件名
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        删除结果
    """
    try:
        file_path = file_upload_service.images_dir / sub_dir / filename
        success = await file_upload_service.delete_image(str(file_path))
        
        if success:
            return {
                "success": True,
                "message": "图片删除成功"
            }
        else:
            raise HTTPException(status_code=404, detail="图片不存在或删除失败")
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除图片失败: {str(e)}")


@router.get("/images/info/{sub_dir}/{filename}")
async def get_image_info(
    sub_dir: str,
    filename: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取图片信息
    
    Args:
        sub_dir: 子目录
        filename: 文件名
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        图片信息
    """
    try:
        file_path = file_upload_service.images_dir / sub_dir / filename
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="图片不存在")
        
        info = file_upload_service.get_image_info(str(file_path))
        if info:
            return {
                "success": True,
                "data": info
            }
        else:
            raise HTTPException(status_code=500, detail="获取图片信息失败")
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图片信息失败: {str(e)}")


@router.post("/cleanup")
async def cleanup_temp_files(
    max_age_hours: int = 24,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    清理临时文件
    
    Args:
        max_age_hours: 最大保留时间（小时）
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        清理结果
    """
    try:
        file_upload_service.cleanup_temp_files(max_age_hours)
        return {
            "success": True,
            "message": "临时文件清理完成"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理临时文件失败: {str(e)}") 