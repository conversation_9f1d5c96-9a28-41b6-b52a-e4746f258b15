"""
部门管理API
提供完整的部门管理功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.orm import Session
from sqlalchemy import or_
from typing import List, Optional

from app.core.database import get_db
from app.models.user import Department
from app.schemas.supplier import (
    Department as DepartmentSchema, DepartmentCreate, DepartmentUpdate
)
from app.services.auth import (
    get_current_user, require_permissions, log_action
)

router = APIRouter(tags=["部门管理"])

@router.get("", response_model=List[DepartmentSchema])
async def get_departments(
    search: Optional[str] = Query(None, description="搜索关键词 (按名称或编码)"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    current_user = Depends(require_permissions(["department.read"])),
    db: Session = Depends(get_db)
):
    """获取部门列表（支持按名称/编码搜索）"""
    query = db.query(Department)
    
    # 状态过滤
    if is_active is not None:
        query = query.filter(Department.is_active == is_active)
    else:
        # 默认只显示激活的部门
        query = query.filter(Department.is_active == True)
    
    # 搜索过滤
    if search:
        pattern = f"%{search}%"
        query = query.filter(or_(Department.name.ilike(pattern), Department.code.ilike(pattern)))
    
    departments = query.order_by(Department.name.asc()).all()
    
    # 转换为Pydantic模型
    department_schemas = [DepartmentSchema.model_validate(department) for department in departments]
    
    return department_schemas

@router.post("", response_model=DepartmentSchema, status_code=status.HTTP_201_CREATED)
async def create_department(
    department: DepartmentCreate,
    request: Request,
    current_user = Depends(require_permissions(["department.create"])),
    db: Session = Depends(get_db)
):
    """创建部门"""
    # 检查编码是否重复
    existing = db.query(Department).filter(Department.code == department.code).first()
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail="部门编码已存在"
        )
    
    # 创建部门
    db_department = Department(**department.model_dump())
    db.add(db_department)
    db.commit()
    db.refresh(db_department)
    
    # 记录操作日志
    log_action(
        db, current_user, 
        action="department.create",
        resource_type="department",
        resource_id=db_department.id,
        details=f"创建部门: {db_department.name} ({db_department.code})"
    )
    
    return db_department

@router.get("/{department_id}", response_model=DepartmentSchema)
@router.get("/{department_id}/", response_model=DepartmentSchema)
async def get_department(
    department_id: int,
    current_user = Depends(require_permissions(["department.read"])),
    db: Session = Depends(get_db)
):
    """获取部门详情"""
    department = db.query(Department).filter(Department.id == department_id).first()
    if not department:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="部门不存在"
        )
    
    return department

@router.put("/{department_id}", response_model=DepartmentSchema)
@router.put("/{department_id}/", response_model=DepartmentSchema)
async def update_department(
    department_id: int,
    department_update: DepartmentUpdate,
    request: Request,
    current_user = Depends(require_permissions(["department.update"])),
    db: Session = Depends(get_db)
):
    """更新部门"""
    department = db.query(Department).filter(Department.id == department_id).first()
    if not department:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="部门不存在"
        )
    
    # 检查编码是否重复（排除当前部门）
    if department_update.code and department_update.code != department.code:
        existing = db.query(Department).filter(
            Department.code == department_update.code,
            Department.id != department_id
        ).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail="部门编码已存在"
            )
    
    # 更新字段
    update_data = department_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(department, field, value)
    
    db.commit()
    db.refresh(department)
    
    # 记录操作日志
    log_action(
        db, current_user, 
        action="department.update",
        resource_type="department",
        resource_id=department.id,
        details=f"更新部门: {department.name} ({department.code})"
    )
    
    return department

@router.delete("/{department_id}", status_code=status.HTTP_204_NO_CONTENT)
@router.delete("/{department_id}/", status_code=status.HTTP_204_NO_CONTENT)
async def delete_department(
    department_id: int,
    request: Request,
    current_user = Depends(require_permissions(["department.delete"])),
    db: Session = Depends(get_db)
):
    """删除部门"""
    department = db.query(Department).filter(Department.id == department_id).first()
    if not department:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="部门不存在"
        )
    
    # 检查是否有用户关联到此部门
    from app.models.user import User
    user_count = db.query(User).filter(User.department_id == department_id).count()
    if user_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail=f"无法删除部门，该部门下还有 {user_count} 个用户"
        )
    
    # 记录操作日志
    log_action(
        db, current_user, 
        action="department.delete",
        resource_type="department",
        resource_id=department.id,
        details=f"删除部门: {department.name} ({department.code})"
    )
    
    # 删除部门
    db.delete(department)
    db.commit()
    
    return None

@router.post("/{department_id}/toggle-status", response_model=DepartmentSchema)
@router.post("/{department_id}/toggle-status/", response_model=DepartmentSchema)
async def toggle_department_status(
    department_id: int,
    request: Request,
    current_user = Depends(require_permissions(["department.update"])),
    db: Session = Depends(get_db)
):
    """切换部门状态"""
    department = db.query(Department).filter(Department.id == department_id).first()
    if not department:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="部门不存在"
        )
    
    # 切换状态
    old_status = department.is_active
    department.is_active = not department.is_active
    db.commit()
    db.refresh(department)
    
    # 记录操作日志
    new_status = "启用" if department.is_active else "禁用"
    log_action(
        db, current_user, 
        action="department.toggle_status",
        resource_type="department",
        resource_id=department.id,
        details=f"{new_status}部门: {department.name} ({department.code})"
    )
    
    return department
