from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.purchase_cart_service import PurchaseCartService
from app.schemas.purchase_cart import (
    AddCartItemRequest, 
    UpdateCartItemRequest, 
    CartItemResponse,
    CartSummaryResponse,
    CartInventoryCheckResponse
)
from app.services.auth import get_current_user, require_permissions
from app.models.user import User
from typing import List

router = APIRouter(tags=["购物车管理"])


@router.get("/department/{department_id}/items", response_model=List[CartItemResponse])
def get_department_cart_items(
    department_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["cart.view"]))
):
    """获取部门购物车中的所有物品"""
    service = PurchaseCartService(db)
    items = service.get_department_cart_items(department_id, current_user)
    return items


@router.post("/department/{department_id}/items", response_model=CartItemResponse)
def add_item_to_cart(
    department_id: int,
    item_data: AddCartItemRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["cart.add_item"]))
):
    """添加物品到部门购物车"""
    service = PurchaseCartService(db)
    cart_item = service.add_item_to_cart(department_id, item_data, current_user)
    return cart_item


@router.put("/items/{cart_item_id}", response_model=CartItemResponse)
def update_cart_item(
    cart_item_id: int,
    updates: UpdateCartItemRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["cart.update_item"]))
):
    """更新购物车项目"""
    service = PurchaseCartService(db)
    cart_item = service.update_cart_item(cart_item_id, updates, current_user)
    return cart_item


@router.delete("/items/{cart_item_id}")
def remove_cart_item(
    cart_item_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["cart.remove_item"]))
):
    """从购物车移除物品"""
    service = PurchaseCartService(db)
    service.remove_cart_item(cart_item_id, current_user)
    return {"message": "物品已从购物车移除"}


@router.delete("/department/{department_id}/clear")
def clear_department_cart(
    department_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["cart.remove_item"]))
):
    """清空部门购物车"""
    service = PurchaseCartService(db)
    service.clear_department_cart(department_id, current_user)
    return {"message": "购物车已清空"}


@router.get("/department/{department_id}/summary", response_model=CartSummaryResponse)
def get_cart_summary(
    department_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["cart.view"]))
):
    """获取部门购物车摘要信息"""
    service = PurchaseCartService(db)
    summary = service.get_cart_summary(department_id, current_user)
    return summary


@router.get("/items/{cart_item_id}", response_model=CartItemResponse)
def get_cart_item(
    cart_item_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["cart.view"]))
):
    """根据ID获取购物车项目"""
    service = PurchaseCartService(db)
    cart_item = service.get_cart_item_by_id(cart_item_id)
    if not cart_item:
        raise HTTPException(status_code=404, detail="购物车项目不存在")
    return cart_item


@router.post("/department/{department_id}/submit")
def submit_cart(
    department_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["cart.submit"]))
):
    """提交购物车申请"""
    service = PurchaseCartService(db)
    result = service.submit_cart(department_id, current_user)
    return {"message": "购物车提交成功", "request_id": result}


@router.get("/department/{department_id}/inventory-check", response_model=CartInventoryCheckResponse)
def check_cart_inventory(
    department_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["cart.view"]))
):
    """检查部门购物车的库存量"""
    from app.services.inventory_check_service import InventoryCheckService
    
    service = InventoryCheckService(db)
    result = service.check_and_alert_cart_inventory(department_id)
    
    # 添加时间戳
    from datetime import datetime
    result["check_timestamp"] = datetime.now()
    
    return result


@router.get("/inventory-alerts/files")
def get_inventory_alert_files(
    limit: int = 10,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["cart.view"]))
):
    """获取库存预警文件列表"""
    from app.services.inventory_check_service import InventoryCheckService
    
    service = InventoryCheckService(db)
    files = service.get_alert_files(limit)
    return {"files": files}


@router.delete("/inventory-alerts/files/{filename}")
def delete_inventory_alert_file(
    filename: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["cart.view"]))
):
    """删除库存预警文件"""
    from app.services.inventory_check_service import InventoryCheckService
    
    service = InventoryCheckService(db)
    success = service.delete_alert_file(filename)
    
    if success:
        return {"message": "预警文件删除成功"}
    else:
        raise HTTPException(status_code=404, detail="预警文件不存在或删除失败")
