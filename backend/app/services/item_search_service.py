"""
物品搜索服务
支持全文搜索、多条件筛选、固定属性搜索等高级功能
"""

import re
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, text
from app.models.item import Item, ItemCategory, ItemPrimaryCategory


class ItemSearchService:
    """物品搜索服务"""
    
    @staticmethod
    def search_items(
        db: Session,
        search: Optional[str] = None,
        category_id: Optional[int] = None,
        primary_category_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        is_purchasable: Optional[bool] = None,
        brand: Optional[str] = None,
        spec_material: Optional[str] = None,
        size_dimension: Optional[str] = None,
        page: int = 1,
        size: int = 20,
        sort_by: str = "name",
        sort_order: str = "asc"
    ) -> Tuple[List[Item], int]:
        """
        搜索物品
        
        Args:
            db: 数据库会话
            search: 搜索关键词
            category_id: 分类ID
            primary_category_id: 一级分类ID
            is_active: 是否启用
            is_purchasable: 是否可购买
            brand: 品牌筛选
            spec_material: 规格/材质筛选
            size_dimension: 尺寸/规格筛选
            page: 页码
            size: 每页数量
            sort_by: 排序字段
            sort_order: 排序方向
            
        Returns:
            (物品列表, 总数)
        """
        # 构建基础查询
        query = db.query(Item).options(
            joinedload(Item.category).joinedload(ItemCategory.primary_category)
        )
        
        # 应用筛选条件
        query = ItemSearchService._apply_filters(
            query, search, category_id, primary_category_id, 
            is_active, is_purchasable, brand, spec_material, size_dimension
        )
        
        # 获取总数
        total = query.count()
        
        # 应用排序
        query = ItemSearchService._apply_sorting(query, sort_by, sort_order)
        
        # 应用分页
        offset = (page - 1) * size
        items = query.offset(offset).limit(size).all()
        
        return items, total
    
    @staticmethod
    def _apply_filters(
        query,
        search: Optional[str] = None,
        category_id: Optional[int] = None,
        primary_category_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        is_purchasable: Optional[bool] = None,
        brand: Optional[str] = None,
        spec_material: Optional[str] = None,
        size_dimension: Optional[str] = None
    ):
        """应用筛选条件"""
        
        # 基础筛选
        if category_id is not None:
            query = query.filter(Item.category_id == category_id)
        
        if primary_category_id is not None:
            query = query.join(Item.category).join(ItemCategory.primary_category).filter(
                ItemPrimaryCategory.id == primary_category_id
            )
        
        if is_active is not None:
            query = query.filter(Item.is_active == is_active)
        
        if is_purchasable is not None:
            query = query.filter(Item.is_purchasable == is_purchasable)
        
        # 固定属性筛选
        if brand:
            # 支持多值OR查询，用逗号分隔
            brand_values = [b.strip() for b in brand.split(',') if b.strip()]
            if brand_values:
                brand_conditions = [Item.brand.ilike(f"%{value}%") for value in brand_values]
                query = query.filter(or_(*brand_conditions))
        
        if spec_material:
            # 支持多值OR查询，用逗号分隔
            spec_values = [s.strip() for s in spec_material.split(',') if s.strip()]
            if spec_values:
                spec_conditions = [Item.spec_material.ilike(f"%{value}%") for value in spec_values]
                query = query.filter(or_(*spec_conditions))
        
        if size_dimension:
            # 支持多值OR查询，用逗号分隔
            size_values = [s.strip() for s in size_dimension.split(',') if s.strip()]
            if size_values:
                size_conditions = [Item.size_dimension.ilike(f"%{value}%") for value in size_values]
                query = query.filter(or_(*size_conditions))
        
        # 全文搜索
        if search:
            search_conditions = [
                Item.name.ilike(f"%{search}%"),
                Item.code.ilike(f"%{search}%"),
                Item.description.ilike(f"%{search}%"),
                Item.brand.ilike(f"%{search}%"),
                Item.spec_material.ilike(f"%{search}%"),
                Item.size_dimension.ilike(f"%{search}%")
            ]
            query = query.filter(or_(*search_conditions))
        
        return query
    
    @staticmethod
    def _apply_sorting(query, sort_by: str, sort_order: str):
        """应用排序"""
        sort_column = getattr(Item, sort_by, Item.name)
        
        if sort_order.lower() == "desc":
            sort_column = sort_column.desc()
        
        return query.order_by(sort_column)
    
    @staticmethod
    def search_by_attributes(
        db: Session,
        brand: Optional[str] = None,
        spec_material: Optional[str] = None,
        size_dimension: Optional[str] = None,
        page: int = 1,
        size: int = 20
    ) -> Tuple[List[Item], int]:
        """
        按固定属性搜索物品
        
        Args:
            db: 数据库会话
            brand: 品牌
            spec_material: 规格/材质
            size_dimension: 尺寸/规格
            page: 页码
            size: 每页数量
            
        Returns:
            (物品列表, 总数)
        """
        query = db.query(Item).options(
            joinedload(Item.category).joinedload(ItemCategory.primary_category)
        )
        
        # 应用属性筛选
        if brand:
            query = query.filter(Item.brand.ilike(f"%{brand}%"))
        
        if spec_material:
            query = query.filter(Item.spec_material.ilike(f"%{spec_material}%"))
        
        if size_dimension:
            query = query.filter(Item.size_dimension.ilike(f"%{size_dimension}%"))
        
        # 获取总数
        total = query.count()
        
        # 应用分页
        offset = (page - 1) * size
        items = query.offset(offset).limit(size).all()
        
        return items, total
    
    @staticmethod
    def get_search_suggestions(
        db: Session,
        search: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        获取搜索建议
        
        Args:
            db: 数据库会话
            search: 搜索关键词
            limit: 返回数量限制
            
        Returns:
            搜索建议列表
        """
        suggestions = []
        
        # 物品名称建议
        name_suggestions = db.query(Item.name).filter(
            Item.name.ilike(f"%{search}%")
        ).limit(limit).all()
        
        for suggestion in name_suggestions:
            suggestions.append({
                "type": "name",
                "value": suggestion[0],
                "label": f"物品名称: {suggestion[0]}"
            })
        
        # 物品编码建议
        code_suggestions = db.query(Item.code).filter(
            Item.code.ilike(f"%{search}%")
        ).limit(limit).all()
        
        for suggestion in code_suggestions:
            suggestions.append({
                "type": "code",
                "value": suggestion[0],
                "label": f"物品编码: {suggestion[0]}"
            })
        
        # 品牌建议
        brand_suggestions = db.query(Item.brand).filter(
            Item.brand.isnot(None),
            Item.brand.ilike(f"%{search}%")
        ).distinct().limit(limit).all()
        
        for suggestion in brand_suggestions:
            if suggestion[0]:
                suggestions.append({
                    "type": "brand",
                    "value": suggestion[0],
                    "label": f"品牌: {suggestion[0]}"
                })
        
        # 规格/材质建议
        spec_suggestions = db.query(Item.spec_material).filter(
            Item.spec_material.isnot(None),
            Item.spec_material.ilike(f"%{search}%")
        ).distinct().limit(limit).all()
        
        for suggestion in spec_suggestions:
            if suggestion[0]:
                suggestions.append({
                    "type": "spec_material",
                    "value": suggestion[0],
                    "label": f"规格/材质: {suggestion[0]}"
                })
        
        return suggestions[:limit]
    
    @staticmethod
    def get_search_statistics(db: Session) -> Dict[str, Any]:
        """
        获取搜索统计信息
        
        Args:
            db: 数据库会话
            
        Returns:
            统计信息字典
        """
        # 总物品数
        total_items = db.query(Item).count()
        
        # 活跃物品数
        active_items = db.query(Item).filter(Item.is_active == True).count()
        
        # 可购买物品数
        purchasable_items = db.query(Item).filter(Item.is_purchasable == True).count()
        
        # 分类统计
        category_stats = db.query(
            ItemCategory.name,
            func.count(Item.id).label('count')
        ).outerjoin(Item).group_by(ItemCategory.name).all()
        
        # 品牌统计
        brand_stats = db.query(
            Item.brand,
            func.count(Item.id).label('count')
        ).filter(Item.brand.isnot(None)).group_by(Item.brand).all()
        
        return {
            "total_items": total_items,
            "active_items": active_items,
            "purchasable_items": purchasable_items,
            "category_stats": [{"name": name, "count": count} for name, count in category_stats],
            "brand_stats": [{"name": name, "count": count} for name, count in brand_stats if name]
        }
    
    @staticmethod
    def get_popular_searches(db: Session, limit: int = 10) -> List[str]:
        """
        获取热门搜索关键词
        
        Args:
            db: 数据库会话
            limit: 返回数量限制
            
        Returns:
            热门搜索关键词列表
        """
        # 这里可以实现基于搜索历史的统计
        # 目前返回一些常见的搜索词
        return ["螺丝", "螺母", "轴承", "电机", "传感器", "开关", "电缆", "工具", "配件", "材料"]


def search_items(
    search: Optional[str] = None,
    category_id: Optional[int] = None,
    primary_category_id: Optional[int] = None,
    page: int = 1,
    size: int = 20
) -> Tuple[List[Item], int]:
    """
    简化的物品搜索函数
    
    Args:
        search: 搜索关键词
        category_id: 分类ID
        primary_category_id: 一级分类ID
        page: 页码
        size: 每页数量
        
    Returns:
        (物品列表, 总数)
    """
    from app.core.database import SessionLocal
    
    db = SessionLocal()
    try:
        return ItemSearchService.search_items(
            db=db,
            search=search,
            category_id=category_id,
            primary_category_id=primary_category_id,
            page=page,
            size=size
        )
    finally:
        db.close() 