"""
库存服务 - 公共库存操作服务
提供原子操作和行锁操作，确保数据一致性
"""

from decimal import Decimal
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, update, and_
from sqlalchemy.exc import OperationalError
import time
import logging

logger = logging.getLogger(__name__)

from app.models.inventory import (
    DepartmentInventory, 
    InventoryChangeRecord,
    InventoryChangeType
)
from app.models.item import Item
from app.services.notification_integration_service import NotificationIntegrationService


class InventoryService:
    """库存服务 - 提供原子操作和行锁操作"""
    

    
    # ==================== 单表原子化操作 ====================
    
    @staticmethod
    def change_stock(
        db: Session, 
        department_id: int, 
        item_id: int, 
        change_amount: Decimal,
        operator_id: int,
        reason: str = "库存变更"
    ) -> bool:
        """
        变更库存数量（增加或减少）
        
        Args:
            db: 数据库会话
            department_id: 部门ID
            item_id: 物品ID
            change_amount: 变更数量（正数增加，负数减少）
            operator_id: 操作员ID
            reason: 操作原因
            
        Returns:
            bool: 操作是否成功
            
        Raises:
            ValueError: 库存记录不存在或库存不足
        """
        try:
            # 1. 获取当前库存记录
            inventory = db.query(DepartmentInventory).filter(
                and_(
                    DepartmentInventory.department_id == department_id,
                    DepartmentInventory.item_id == item_id
                )
            ).first()
            
            if not inventory:
                raise ValueError("库存记录不存在")
            
            # 2. 验证库存是否充足（减少时）
            if change_amount < 0 and inventory.current_quantity + change_amount < 0:
                raise ValueError("库存不足")
            
            # 3. 保存原始数量用于记录
            before_quantity = inventory.current_quantity
            after_quantity = before_quantity + change_amount
            
            # 4. 原子化更新库存数量
            stmt = update(DepartmentInventory).where(
                and_(
                    DepartmentInventory.department_id == department_id,
                    DepartmentInventory.item_id == item_id
                )
            ).values(
                current_quantity=DepartmentInventory.current_quantity + change_amount,
                last_updated=func.now()
            )
            
            db.execute(stmt)
            
            # 5. 创建库存变更记录
            change_record = InventoryChangeRecord(
                department_id=department_id,
                item_id=item_id,
                before_quantity=before_quantity,
                after_quantity=after_quantity,
                change_quantity=change_amount,
                change_type=InventoryChangeType.MANUAL_IN if change_amount > 0 else InventoryChangeType.PICKUP_OUT,
                change_reason=reason,
                operator_id=operator_id
            )
            db.add(change_record)
            
            # 6. 创建库存变更通知
            try:
                notification_service = NotificationIntegrationService(db)
                notification_service.create_inventory_change_notification(
                    change_record=change_record,
                    operator_id=operator_id
                )
                
                # 检查并创建库存预警通知
                notification_service.check_and_create_inventory_alerts(
                    department_id=department_id,
                    item_id=item_id,
                    current_quantity=after_quantity
                )
            except Exception as e:
                # 通知创建失败不影响库存操作
                logger.warning(f"创建库存变更通知失败: {str(e)}")
            
            db.commit()
            
            return True
            
        except Exception as e:
            db.rollback()
            raise e
    
    @staticmethod
    def adjust_stock(
        db: Session,
        department_id: int,
        item_id: int,
        new_quantity: Decimal,
        operator_id: int,
        reason: str = "库存调整"
    ) -> bool:
        """
        调整库存数量到指定值
        
        Args:
            db: 数据库会话
            department_id: 部门ID
            item_id: 物品ID
            new_quantity: 新的库存数量
            operator_id: 操作员ID
            reason: 调整原因
            
        Returns:
            bool: 操作是否成功
            
        Raises:
            ValueError: 库存记录不存在
        """
        try:
            # 1. 获取当前库存
            inventory = db.query(DepartmentInventory).filter(
                and_(
                    DepartmentInventory.department_id == department_id,
                    DepartmentInventory.item_id == item_id
                )
            ).first()
            
            if not inventory:
                raise ValueError("库存记录不存在")
            
            before_quantity = inventory.current_quantity
            change_quantity = new_quantity - before_quantity
            
            # 2. 原子化更新库存数量
            stmt = update(DepartmentInventory).where(
                and_(
                    DepartmentInventory.department_id == department_id,
                    DepartmentInventory.item_id == item_id
                )
            ).values(
                current_quantity=new_quantity,
                last_updated=func.now()
            )
            db.execute(stmt)
            
            # 3. 创建库存变更记录
            change_record = InventoryChangeRecord(
                department_id=department_id,
                item_id=item_id,
                before_quantity=before_quantity,
                after_quantity=new_quantity,
                change_quantity=change_quantity,
                change_type=InventoryChangeType.ADJUST,
                change_reason=reason,
                operator_id=operator_id
            )
            db.add(change_record)
            
            # 4. 创建库存变更通知
            try:
                notification_service = NotificationIntegrationService(db)
                notification_service.create_inventory_change_notification(
                    change_record=change_record,
                    operator_id=operator_id
                )
                
                # 检查并创建库存预警通知
                notification_service.check_and_create_inventory_alerts(
                    department_id=department_id,
                    item_id=item_id,
                    current_quantity=new_quantity
                )
            except Exception as e:
                # 通知创建失败不影响库存操作
                logger.warning(f"创建库存调整通知失败: {str(e)}")
            
            db.commit()
            
            return True
            
        except Exception as e:
            db.rollback()
            raise e
    
    # ==================== 多表行锁操作 ====================
    

    

    
    # ==================== 查询方法 ====================
    
    @staticmethod
    def get_inventory_info(
        db: Session, 
        department_id: int, 
        item_id: int
    ) -> Optional[Dict[str, Any]]:
        """
        获取库存信息
        
        Args:
            db: 数据库会话
            department_id: 部门ID
            item_id: 物品ID
            
        Returns:
            Dict: 库存信息字典
        """
        inventory = db.query(DepartmentInventory).filter(
            and_(
                DepartmentInventory.department_id == department_id,
                DepartmentInventory.item_id == item_id
            )
        ).first()
        
        if not inventory:
            return None
        
        # 获取物品信息
        item = db.query(Item).filter(Item.id == item_id).first()
        
        return {
            'id': inventory.id,
            'department_id': inventory.department_id,
            'item_id': inventory.item_id,
            'current_quantity': float(inventory.current_quantity),
            'min_quantity': float(inventory.min_quantity) if inventory.min_quantity else None,
            'max_quantity': float(inventory.max_quantity) if inventory.max_quantity else None,
            'storage_location': inventory.storage_location,
            'rack_number': inventory.rack_number,
            'status': inventory.status,
            'last_updated': inventory.last_updated,
            'item_name': item.name if item else None,
            'item_code': item.code if item else None
        }
    
    @staticmethod
    def get_department_inventories(
        db: Session, 
        department_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        获取部门所有库存信息
        
        Args:
            db: 数据库会话
            department_id: 部门ID
            skip: 跳过记录数
            limit: 限制记录数
            
        Returns:
            List[Dict]: 库存信息列表
        """
        inventories = db.query(DepartmentInventory).filter(
            DepartmentInventory.department_id == department_id,
            DepartmentInventory.is_active == True
        ).offset(skip).limit(limit).all()
        
        result = []
        for inventory in inventories:
            item = db.query(Item).filter(Item.id == inventory.item_id).first()
            
            result.append({
                'id': inventory.id,
                'department_id': inventory.department_id,
                'item_id': inventory.item_id,
                'current_quantity': float(inventory.current_quantity),
                'min_quantity': float(inventory.min_quantity) if inventory.min_quantity else None,
                'max_quantity': float(inventory.max_quantity) if inventory.max_quantity else None,
                'storage_location': inventory.storage_location,
                'rack_number': inventory.rack_number,
                'status': inventory.status,
                'last_updated': inventory.last_updated,
                'item_name': item.name if item else None,
                'item_code': item.code if item else None
            })
        
        return result
    
    # ==================== 重试机制 ====================
    
    @staticmethod
    def retry_with_lock_timeout(
        operation_func, 
        max_retries: int = 3,
        base_delay: float = 0.1
    ):
        """
        带重试机制的行锁操作
        
        Args:
            operation_func: 要执行的操作函数
            max_retries: 最大重试次数
            base_delay: 基础延迟时间（秒）
            
        Returns:
            操作结果
            
        Raises:
            Exception: 操作超时
        """
        for attempt in range(max_retries):
            try:
                return operation_func()
            except OperationalError as e:
                if "lock timeout" in str(e).lower() or "deadlock" in str(e).lower():
                    if attempt < max_retries - 1:
                        # 等待一段时间后重试
                        delay = base_delay * (2 ** attempt)  # 指数退避
                        time.sleep(delay)
                        continue
                    else:
                        raise Exception("操作超时，请稍后重试")
                else:
                    raise e
            except Exception as e:
                raise e
    

