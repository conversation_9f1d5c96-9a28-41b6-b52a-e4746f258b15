from sqlalchemy.orm import Session
from sqlalchemy import and_
from typing import List, Dict, Any, Optional
from decimal import Decimal
from datetime import datetime
import uuid

from app.models.purchase import PurchaseRequest, PurchaseRequestItem
from app.models.inventory import DepartmentInventory, InventoryChangeRecord
from app.models.item import Item
from app.models.user import User, Department
from app.schemas.inbound import (
    ScanQRCodeResponse,
    InboundItemInfo,
    InboundRequest,
    InboundResponse,
    InboundItemResult
)


class InboundService:
    """入库服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def scan_qr_code(self, qr_code: str, user_id: int) -> ScanQRCodeResponse:
        """扫描二维码获取采购申请单信息"""
        try:
            # 解析二维码内容，获取采购申请单ID
            # 这里假设二维码内容就是申请单ID或申请单号
            purchase_request = None
            
            # 尝试通过ID查找
            try:
                request_id = int(qr_code)
                purchase_request = self.db.query(PurchaseRequest).filter(
                    PurchaseRequest.id == request_id
                ).first()
            except ValueError:
                # 如果不是数字，尝试通过申请单号查找
                purchase_request = self.db.query(PurchaseRequest).filter(
                    PurchaseRequest.request_no == qr_code
                ).first()
            
            if not purchase_request:
                return ScanQRCodeResponse(
                    success=False,
                    message="未找到对应的采购申请单",
                    items=[]
                )
            
            # 获取申请单物品列表
            items = self.db.query(PurchaseRequestItem).filter(
                PurchaseRequestItem.request_id == purchase_request.id
            ).all()
            
            if not items:
                return ScanQRCodeResponse(
                    success=False,
                    message="采购申请单中没有物品",
                    items=[]
                )
            
            # 转换为响应格式
            inbound_items = []
            for item in items:
                # 计算申请数量（SPQ数量 * SPQ个数）
                requested_quantity = item.spq_quantity * item.spq_count
                
                inbound_item = InboundItemInfo(
                    id=item.id,
                    item_id=item.item_id,
                    item_code=item.item_code,
                    item_name=item.item_name,
                    spq_quantity=item.spq_quantity,
                    spq_count=item.spq_count,
                    spq_unit=item.spq_unit,
                    requested_quantity=requested_quantity,
                    notes=item.notes
                )
                inbound_items.append(inbound_item)
            
            # 获取申请单基本信息
            request_info = {
                "id": purchase_request.id,
                "request_no": purchase_request.request_no,
                "department_id": purchase_request.department_id,
                "submitter_id": purchase_request.submitter_id,
                "status": purchase_request.status,
                "submitted_at": purchase_request.submitted_at,
                "notes": purchase_request.notes
            }
            
            # 添加部门名称和提交人姓名
            if purchase_request.department_id:
                department = self.db.query(Department).filter(Department.id == purchase_request.department_id).first()
                if department:
                    request_info["department_name"] = department.name
                    request_info["department_code"] = department.code
            
            if purchase_request.submitter_id:
                submitter = self.db.query(User).filter(User.id == purchase_request.submitter_id).first()
                if submitter:
                    request_info["submitter_name"] = submitter.full_name or submitter.username
            
            return ScanQRCodeResponse(
                success=True,
                message="成功获取采购申请单信息",
                purchase_request=request_info,
                items=inbound_items
            )
            
        except Exception as e:
            return ScanQRCodeResponse(
                success=False,
                message=f"扫描二维码失败: {str(e)}",
                items=[]
            )
    
    def execute_inbound(self, request: InboundRequest, user_id: int) -> InboundResponse:
        """执行入库操作"""
        try:
            # 验证采购申请单
            purchase_request = self.db.query(PurchaseRequest).filter(
                PurchaseRequest.id == request.purchase_request_id
            ).first()
            
            if not purchase_request:
                return InboundResponse(
                    success=False,
                    message="采购申请单不存在",
                    total_items=0,
                    success_items=0,
                    failed_items=0,
                    created_at=datetime.now()
                )
            
            # 验证部门
            department = self.db.query(Department).filter(
                Department.id == request.department_id
            ).first()
            
            if not department:
                return InboundResponse(
                    success=False,
                    message="目标部门不存在",
                    total_items=0,
                    success_items=0,
                    failed_items=0,
                    created_at=datetime.now()
                )
            
            # 生成入库ID
            inbound_id = f"INB{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:8].upper()}"
            
            results = []
            success_count = 0
            failed_count = 0
            
            # 执行入库操作
            for item_request in request.items:
                try:
                    # 获取物品信息
                    item = self.db.query(Item).filter(Item.id == item_request.item_id).first()
                    if not item:
                        results.append(InboundItemResult(
                            item_id=item_request.item_id,
                            item_code="",
                            item_name="",
                            inbound_quantity=item_request.inbound_quantity,
                            before_quantity=Decimal('0'),
                            after_quantity=Decimal('0'),
                            success=False,
                            message="物品不存在"
                        ))
                        failed_count += 1
                        continue
                    
                    # 获取或创建部门库存记录
                    inventory = self.db.query(DepartmentInventory).filter(
                        and_(
                            DepartmentInventory.department_id == request.department_id,
                            DepartmentInventory.item_id == item_request.item_id
                        )
                    ).first()
                    
                    if not inventory:
                        # 创建新的库存记录
                        inventory = DepartmentInventory(
                            department_id=request.department_id,
                            item_id=item_request.item_id,
                            current_quantity=Decimal('0'),
                            min_quantity=Decimal('0'),
                            is_active=True
                        )
                        self.db.add(inventory)
                        self.db.flush()  # 获取ID
                    
                    # 记录变更前的数量
                    before_quantity = inventory.current_quantity
                    
                    # 更新库存数量
                    inventory.current_quantity += item_request.inbound_quantity
                    inventory.last_updated = datetime.now()
                    
                    # 创建库存变更记录
                    change_record = InventoryChangeRecord(
                        department_id=request.department_id,
                        item_id=item_request.item_id,
                        before_quantity=before_quantity,
                        after_quantity=inventory.current_quantity,
                        change_quantity=item_request.inbound_quantity,
                        change_type="manual_in",
                        change_reason="扫码入库",
                        operator_id=user_id,
                        change_date=datetime.now(),
                        remarks=f"采购申请单: {purchase_request.request_no}"
                    )
                    
                    self.db.add(change_record)
                    
                    # 记录成功结果
                    results.append(InboundItemResult(
                        item_id=item_request.item_id,
                        item_code=item.code,
                        item_name=item.name,
                        inbound_quantity=item_request.inbound_quantity,
                        before_quantity=before_quantity,
                        after_quantity=inventory.current_quantity,
                        success=True,
                        message="入库成功"
                    ))
                    success_count += 1
                    
                except Exception as e:
                    # 记录失败结果
                    results.append(InboundItemResult(
                        item_id=item_request.item_id,
                        item_code="",
                        item_name="",
                        inbound_quantity=item_request.inbound_quantity,
                        before_quantity=Decimal('0'),
                        after_quantity=Decimal('0'),
                        success=False,
                        message=f"入库失败: {str(e)}"
                    ))
                    failed_count += 1
            
            # 提交事务
            self.db.commit()
            
            return InboundResponse(
                success=True,
                message="入库操作完成",
                inbound_id=inbound_id,
                results=results,
                total_items=len(request.items),
                success_items=success_count,
                failed_items=failed_count,
                created_at=datetime.now()
            )
            
        except Exception as e:
            # 回滚事务
            self.db.rollback()
            return InboundResponse(
                success=False,
                message=f"入库操作失败: {str(e)}",
                total_items=len(request.items),
                success_items=0,
                failed_items=len(request.items),
                created_at=datetime.now()
            )
