from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from app.models.notification import Notification, NotificationEmail
from app.models.user import User
from app.schemas.notification import NotificationCreate, NotificationUpdate, NotificationFilter
from sqlalchemy import func


class NotificationService:
    """通知服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_notification(self, notification_data: NotificationCreate) -> Notification:
        """创建通知"""
        notification = Notification(
            user_id=notification_data.user_id,
            title=notification_data.title,
            content=notification_data.content,
            notification_type=notification_data.notification_type,
            business_data=notification_data.business_data,
            action_url=notification_data.action_url
        )
        
        self.db.add(notification)
        self.db.commit()
        self.db.refresh(notification)
        
        # 如果用户启用了邮件通知，创建邮件记录
        user = self.db.query(User).filter(User.id == notification_data.user_id).first()
        if user and user.email_notifications_enabled:
            email_record = NotificationEmail(
                notification_id=notification.id,
                user_id=notification_data.user_id
            )
            self.db.add(email_record)
            self.db.commit()
        
        return notification
    
    def get_notifications(
        self, 
        user_id: int, 
        filter_params: Optional[NotificationFilter] = None,
        page: int = 1,
        size: int = 20
    ) -> Dict[str, Any]:
        """获取用户通知列表"""
        query = self.db.query(Notification).filter(Notification.user_id == user_id)
        
        # 应用筛选条件
        if filter_params:
            if filter_params.notification_type:
                query = query.filter(Notification.notification_type == filter_params.notification_type)
            if filter_params.status:
                query = query.filter(Notification.status == filter_params.status)
            if filter_params.start_date:
                query = query.filter(Notification.created_at >= filter_params.start_date)
            if filter_params.end_date:
                query = query.filter(Notification.created_at <= filter_params.end_date)
        
        # 按创建时间倒序排列
        query = query.order_by(desc(Notification.created_at))
        
        # 分页
        total = query.count()
        notifications = query.offset((page - 1) * size).limit(size).all()
        
        return {
            "data": notifications,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }
    
    def get_all_notifications(
        self,
        filter_params: Optional[NotificationFilter] = None,
        page: int = 1,
        size: int = 20
    ) -> Dict[str, Any]:
        """获取所有通知列表（超级管理员使用）"""
        query = self.db.query(Notification)
        
        # 应用筛选条件
        if filter_params:
            if filter_params.user_id:
                query = query.filter(Notification.user_id == filter_params.user_id)
            if filter_params.notification_type:
                query = query.filter(Notification.notification_type == filter_params.notification_type)
            if filter_params.status:
                query = query.filter(Notification.status == filter_params.status)
            if filter_params.start_date:
                query = query.filter(Notification.created_at >= filter_params.start_date)
            if filter_params.end_date:
                query = query.filter(Notification.created_at <= filter_params.end_date)
        
        # 按创建时间倒序排列
        query = query.order_by(desc(Notification.created_at))
        
        # 分页
        total = query.count()
        notifications = query.offset((page - 1) * size).limit(size).all()
        
        return {
            "data": notifications,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }
    
    def get_notification_by_id(self, notification_id: int, user_id: Optional[int] = None) -> Optional[Notification]:
        """根据ID获取通知详情"""
        query = self.db.query(Notification).filter(Notification.id == notification_id)
        
        # 如果指定了用户ID，则只能查看自己的通知
        if user_id:
            query = query.filter(Notification.user_id == user_id)
        
        return query.first()
    
    def mark_as_read(self, notification_id: int, user_id: int) -> Optional[Notification]:
        """标记通知为已读"""
        notification = self.get_notification_by_id(notification_id, user_id)
        if notification and notification.status == "unread":
            notification.status = "read"
            notification.read_at = func.now()
            self.db.commit()
            self.db.refresh(notification)
        
        return notification
    
    def batch_mark_as_read(self, notification_ids: List[int], user_id: int) -> int:
        """批量标记通知为已读"""
        result = self.db.query(Notification).filter(
            and_(
                Notification.id.in_(notification_ids),
                Notification.user_id == user_id,
                Notification.status == "unread"
            )
        ).update({
            "status": "read",
            "read_at": func.now()
        }, synchronize_session=False)
        
        self.db.commit()
        return result
    
    def delete_notification(self, notification_id: int, user_id: int) -> bool:
        """删除通知"""
        notification = self.get_notification_by_id(notification_id, user_id)
        if notification:
            self.db.delete(notification)
            self.db.commit()
            return True
        return False
    
    def batch_delete_notifications(self, notification_ids: List[int], user_id: int) -> int:
        """批量删除通知"""
        result = self.db.query(Notification).filter(
            and_(
                Notification.id.in_(notification_ids),
                Notification.user_id == user_id
            )
        ).delete(synchronize_session=False)
        
        self.db.commit()
        return result
    
    def get_notification_stats(self, user_id: Optional[int] = None) -> Dict[str, Any]:
        """获取通知统计信息"""
        query = self.db.query(Notification)
        
        if user_id:
            query = query.filter(Notification.user_id == user_id)
        
        total = query.count()
        unread = query.filter(Notification.status == "unread").count()
        read = query.filter(Notification.status == "read").count()
        
        # 按类型统计
        type_stats = {}
        for notification_type in ["inventory_alert", "approval_flow"]:
            count = query.filter(Notification.notification_type == notification_type).count()
            type_stats[notification_type] = count
        
        return {
            "total": total,
            "unread": unread,
            "read": read,
            "by_type": type_stats
        }
    
    def create_inventory_alert(
        self, 
        user_id: int, 
        alert_type: str, 
        item_name: str, 
        current_quantity: int,
        threshold_quantity: int,
        action_url: str
    ) -> Notification:
        """创建库存告警通知"""
        alert_messages = {
            "low_stock": f"Low stock alert: {item_name} current quantity ({current_quantity}) is below threshold ({threshold_quantity})",
            "out_of_stock": f"Out of stock alert: {item_name} is completely out of stock",
            "overstock": f"Overstock alert: {item_name} current quantity ({current_quantity}) exceeds maximum threshold ({threshold_quantity})"
        }
        
        title = f"Inventory Alert: {item_name}"
        content = alert_messages.get(alert_type, f"Inventory alert for {item_name}")
        
        notification_data = NotificationCreate(
            user_id=user_id,
            title=title,
            content=content,
            notification_type="inventory_alert",
            business_data={
                "alert_type": alert_type,
                "item_name": item_name,
                "current_quantity": current_quantity,
                "threshold_quantity": threshold_quantity
            },
            action_url=action_url
        )
        
        return self.create_notification(notification_data)
    
    def create_approval_notification(
        self,
        user_id: int,
        approval_type: str,
        request_id: int,
        request_title: str,
        action_url: str
    ) -> Notification:
        """创建审批流程通知"""
        approval_messages = {
            "submitted": f"Purchase request '{request_title}' has been submitted and is waiting for approval",
            "approved": f"Purchase request '{request_title}' has been approved",
            "rejected": f"Purchase request '{request_title}' has been rejected",
            "status_changed": f"Purchase request '{request_title}' status has changed"
        }
        
        title = f"Approval Notification: {request_title}"
        content = approval_messages.get(approval_type, f"Approval notification for request {request_title}")
        
        notification_data = NotificationCreate(
            user_id=user_id,
            title=title,
            content=content,
            notification_type="approval_flow",
            business_data={
                "approval_type": approval_type,
                "request_id": request_id,
                "request_title": request_title
            },
            action_url=action_url
        )
        
        return self.create_notification(notification_data)
