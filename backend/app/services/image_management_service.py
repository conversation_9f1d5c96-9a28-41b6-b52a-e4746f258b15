"""
图片管理服务
扫描上传目录，匹配资源，识别孤立文件
"""

from typing import List, Dict, Any
from pathlib import Path
from datetime import datetime
from sqlalchemy.orm import Session
from app.models.item import Item
from app.services.file_upload_service import file_upload_service


class ImageManagementService:
    """图片管理服务"""
    
    def __init__(self):
        """初始化图片管理服务"""
        self.upload_service = file_upload_service
        self.images_dir = self.upload_service.images_dir
    
    def scan_directory_images(self, sub_dir: str = "items") -> Dict[str, Any]:
        """
        扫描指定目录下的所有图片文件
        
        Args:
            sub_dir: 子目录名称
            
        Returns:
            扫描结果统计
        """
        target_dir = self.images_dir / sub_dir
        if not target_dir.exists():
            return {
                "directory": str(target_dir),
                "exists": False,
                "total_files": 0,
                "total_size": 0,
                "files": []
            }
        
        files = []
        total_size = 0
        
        # 扫描主目录中的图片文件
        for file_path in target_dir.glob("*.jpg"):
            if file_path.is_file():
                file_stat = file_path.stat()
                file_info = {
                    "filename": file_path.name,
                    "size": file_stat.st_size,
                    "created_time": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                    "modified_time": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                    "path": str(file_path),
                    "url": f"/api/admin/upload/images/{sub_dir}/{file_path.name}"
                }
                files.append(file_info)
                total_size += file_stat.st_size
        
        return {
            "directory": str(target_dir),
            "exists": True,
            "total_files": len(files),
            "total_size": total_size,
            "files": files
        }
    
    def match_items_with_images(self, db: Session, sub_dir: str = "items") -> Dict[str, Any]:
        """
        匹配物品记录与图片文件
        
        Args:
            db: 数据库会话
            sub_dir: 子目录名称
            
        Returns:
            匹配结果
        """
        # 扫描目录中的所有图片
        scan_result = self.scan_directory_images(sub_dir)
        if not scan_result["exists"]:
            return {
                "directory_exists": False,
                "matched_items": [],
                "orphaned_images": [],
                "missing_images": [],
                "statistics": {
                    "total_items": 0,
                    "total_images": 0,
                    "matched_count": 0,
                    "orphaned_count": 0,
                    "missing_count": 0
                }
            }
        
        # 获取所有物品记录
        items = db.query(Item).all()
        
        # 创建文件名到文件信息的映射
        image_files = {file_info["filename"]: file_info for file_info in scan_result["files"]}
        
        matched_items = []
        missing_images = []
        used_images = set()
        
        # 遍历所有物品，检查图片匹配情况
        for item in items:
            if item.image_url:
                # 从URL中提取文件名
                filename = Path(item.image_url).name
                
                if filename in image_files:
                    # 找到匹配的图片
                    matched_items.append({
                        "item_id": item.id,
                        "item_name": item.name,
                        "item_code": item.code,
                        "image_filename": filename,
                        "image_url": item.image_url,
                        "image_info": image_files[filename]
                    })
                    used_images.add(filename)
                else:
                    # 物品有图片URL但找不到对应文件
                    missing_images.append({
                        "item_id": item.id,
                        "item_name": item.name,
                        "item_code": item.code,
                        "image_url": item.image_url,
                        "missing_filename": filename
                    })
        
        # 找出孤立的图片文件（没有被任何物品引用）
        orphaned_images = []
        for filename, file_info in image_files.items():
            if filename not in used_images:
                orphaned_images.append(file_info)
        
        return {
            "directory_exists": True,
            "matched_items": matched_items,
            "orphaned_images": orphaned_images,
            "missing_images": missing_images,
            "statistics": {
                "total_items": len(items),
                "total_images": len(image_files),
                "matched_count": len(matched_items),
                "orphaned_count": len(orphaned_images),
                "missing_count": len(missing_images),
                "total_orphaned_size": sum(img["size"] for img in orphaned_images)
            }
        }
    
    def delete_orphaned_images(self, filenames: List[str], sub_dir: str = "items") -> Dict[str, Any]:
        """
        删除指定的孤立图片文件
        
        Args:
            filenames: 要删除的文件名列表
            sub_dir: 子目录名称
            
        Returns:
            删除结果
        """
        target_dir = self.images_dir / sub_dir
        deleted_files = []
        failed_files = []
        total_freed_size = 0
        
        for filename in filenames:
            file_path = target_dir / filename
            
            if file_path.exists():
                try:
                    file_size = file_path.stat().st_size
                    
                    # 删除图片
                    file_path.unlink()
                    
                    deleted_files.append({
                        "filename": filename,
                        "size": file_size
                    })
                    total_freed_size += file_size
                    
                except Exception as e:
                    failed_files.append({
                        "filename": filename,
                        "error": str(e)
                    })
            else:
                failed_files.append({
                    "filename": filename,
                    "error": "文件不存在"
                })
        
        return {
            "deleted_files": deleted_files,
            "failed_files": failed_files,
            "deleted_count": len(deleted_files),
            "failed_count": len(failed_files),
            "total_freed_size": total_freed_size
        }
    
    def get_directory_statistics(self, sub_dir: str = "items") -> Dict[str, Any]:
        """
        获取目录统计信息
        
        Args:
            sub_dir: 子目录名称
            
        Returns:
            目录统计信息
        """
        target_dir = self.images_dir / sub_dir
        
        if not target_dir.exists():
            return {
                "directory": str(target_dir),
                "exists": False
            }
        
        # 统计图片文件
        image_files = list(target_dir.glob("*.jpg"))
        total_size = sum(f.stat().st_size for f in image_files if f.is_file())
        
        return {
            "directory": str(target_dir),
            "exists": True,
            "images": {
                "count": len(image_files),
                "total_size": total_size
            },
            "total": {
                "files": len(image_files),
                "size": total_size
            }
        }


# 全局图片管理服务实例
image_management_service = ImageManagementService()
