"""
文件上传服务
支持图片上传、文件验证、存储管理等功能
"""

import uuid
from typing import Optional, List, Dict, Any
from pathlib import Path
from datetime import datetime
from fastapi import UploadFile, HTTPException
from PIL import Image
import io

class FileUploadService:
    """文件上传服务"""
    
    # 支持的图片格式
    SUPPORTED_IMAGE_FORMATS = {'.jpg', '.jpeg', '.png', '.gif', '.webp'}
    
    # 最大文件大小 (500KB)
    MAX_FILE_SIZE = 500 * 1024
    
    # 最大图片尺寸
    MAX_IMAGE_DIMENSION = 2048
    
    def __init__(self, upload_dir: str = "uploads"):
        """
        初始化文件上传服务
        
        Args:
            upload_dir: 上传目录
        """
        self.upload_dir = Path(upload_dir)
        self.images_dir = self.upload_dir / "images"
        self.temp_dir = self.upload_dir / "temp"
        
        # 创建必要的目录
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的目录"""
        self.images_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    async def upload_image(self, file: UploadFile, sub_dir: str = "items") -> Dict[str, Any]:
        """
        上传图片文件
        
        Args:
            file: 上传的文件
            sub_dir: 子目录
            
        Returns:
            上传结果信息
        """
        # 验证文件
        self._validate_image_file(file)
        
        # 生成唯一文件名
        unique_filename = self._generate_filename()
        
        # 创建目标目录
        target_dir = self.images_dir / sub_dir
        target_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存并处理文件
        file_path = target_dir / unique_filename
        await self._save_and_process_file(file, file_path)
        
        # 返回文件信息
        return {
            "original_name": file.filename,
            "filename": unique_filename,
            "file_path": str(file_path),
            "file_size": file_path.stat().st_size,
            "content_type": "image/jpeg",  # 统一转换为JPEG
            "upload_time": datetime.now().isoformat(),
            "url": f"/uploads/images/{sub_dir}/{unique_filename}"
        }
    
    def _validate_image_file(self, file: UploadFile):
        """
        验证图片文件
        
        Args:
            file: 上传的文件
            
        Raises:
            HTTPException: 文件验证失败
        """
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 检查文件扩展名
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in self.SUPPORTED_IMAGE_FORMATS:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的图片格式。支持的格式: {', '.join(self.SUPPORTED_IMAGE_FORMATS)}"
            )
        
        # 检查文件大小
        if hasattr(file, 'size') and file.size > self.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400, 
                detail=f"文件大小超过限制。最大大小: {self.MAX_FILE_SIZE / 1024}KB"
            )
    
    def _generate_filename(self) -> str:
        """生成随机唯一文件名"""
        # 使用UUID生成随机文件名，不再与资源信息绑定
        unique_id = str(uuid.uuid4())
        return f"{unique_id}.jpg"
    

    
    async def _save_and_process_file(self, file: UploadFile, file_path: Path):
        """
        保存并处理文件
        
        Args:
            file: 上传的文件
            file_path: 目标文件路径
        """
        # 读取文件内容
        content = await file.read()
        
        # 使用PIL处理图片
        with Image.open(io.BytesIO(content)) as img:
            # 检查图片尺寸
            if img.width > self.MAX_IMAGE_DIMENSION or img.height > self.MAX_IMAGE_DIMENSION:
                # 压缩图片
                img = self._compress_image(img)
            
            # 转换为RGB模式（如果是RGBA）
            if img.mode in ('RGBA', 'LA'):
                img = img.convert('RGB')
            
            # 保存为JPEG格式，质量85%
            img.save(file_path, 'JPEG', quality=85, optimize=True)
    
    def _compress_image(self, img: Image.Image) -> Image.Image:
        """
        压缩图片
        
        Args:
            img: 原图片
            
        Returns:
            压缩后的图片
        """
        # 计算新的尺寸，保持宽高比
        if img.width > img.height:
            new_width = self.MAX_IMAGE_DIMENSION
            new_height = int(img.height * self.MAX_IMAGE_DIMENSION / img.width)
        else:
            new_height = self.MAX_IMAGE_DIMENSION
            new_width = int(img.width * self.MAX_IMAGE_DIMENSION / img.height)
        
        return img.resize((new_width, new_height), Image.Resampling.LANCZOS)
    

    
    async def delete_image(self, file_path: str) -> bool:
        """
        删除图片文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否删除成功
        """
        try:
            path = Path(file_path)
            if path.exists():
                path.unlink()
                return True
            return False
        except Exception as e:
            print(f"删除文件失败: {e}")
            return False
    
    def get_image_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        获取图片信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            图片信息
        """
        try:
            path = Path(file_path)
            if not path.exists():
                return None
            
            with Image.open(path) as img:
                return {
                    "width": img.width,
                    "height": img.height,
                    "format": img.format,
                    "mode": img.mode,
                    "size": path.stat().st_size
                }
        except Exception as e:
            print(f"获取图片信息失败: {e}")
            return None
    
    async def batch_upload_images(self, files: List[UploadFile], sub_dir: str = "items") -> List[Dict[str, Any]]:
        """
        批量上传图片
        
        Args:
            files: 文件列表
            sub_dir: 子目录
            
        Returns:
            上传结果列表
        """
        results = []
        for file in files:
            try:
                result = await self.upload_image(file, sub_dir)
                results.append(result)
            except Exception as e:
                results.append({
                    "error": str(e),
                    "original_name": file.filename
                })
        
        return results
    
    def cleanup_temp_files(self, max_age_hours: int = 24):
        """
        清理临时文件
        
        Args:
            max_age_hours: 最大保留时间（小时）
        """
        try:
            current_time = datetime.now()
            for file_path in self.temp_dir.rglob("*"):
                if file_path.is_file():
                    file_age = current_time - datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_age.total_seconds() > max_age_hours * 3600:
                        file_path.unlink()
        except Exception as e:
            print(f"清理临时文件失败: {e}")


# 全局文件上传服务实例
file_upload_service = FileUploadService() 