from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from app.models.notification import SystemConfig


class ConfigService:
    """配置管理服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_config(self, config_key: str) -> Optional[str]:
        """获取单个配置值"""
        config = self.db.query(SystemConfig).filter(
            SystemConfig.config_key == config_key
        ).first()
        
        return config.config_value if config else None
    
    def get_configs_by_keys(self, config_keys: List[str]) -> Dict[str, str]:
        """批量获取配置值"""
        configs = self.db.query(SystemConfig).filter(
            SystemConfig.config_key.in_(config_keys)
        ).all()
        
        return {config.config_key: config.config_value for config in configs}
    
    def get_all_configs(self) -> List[SystemConfig]:
        """获取所有配置"""
        return self.db.query(SystemConfig).order_by(SystemConfig.config_key).all()
    
    def set_config(self, config_key: str, config_value: str, description: Optional[str] = None) -> bool:
        """设置配置值"""
        try:
            config = self.db.query(SystemConfig).filter(
                SystemConfig.config_key == config_key
            ).first()
            
            if config:
                # 更新现有配置
                config.config_value = config_value
                if description:
                    config.description = description
            else:
                # 创建新配置
                config = SystemConfig(
                    config_key=config_key,
                    config_value=config_value,
                    description=description or config_key
                )
                self.db.add(config)
            
            self.db.commit()
            return True
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def set_configs(self, configs: Dict[str, str]) -> bool:
        """批量设置配置值"""
        try:
            for key, value in configs.items():
                self.set_config(key, value)
            
            return True
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def delete_config(self, config_key: str) -> bool:
        """删除配置"""
        try:
            config = self.db.query(SystemConfig).filter(
                SystemConfig.config_key == config_key
            ).first()
            
            if config:
                self.db.delete(config)
                self.db.commit()
                return True
            
            return False
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def get_smtp_config(self) -> Dict[str, str]:
        """获取SMTP配置"""
        smtp_keys = [
            "smtp_host", "smtp_port", "smtp_username", 
            "smtp_password", "smtp_use_tls"
        ]
        
        configs = self.get_configs_by_keys(smtp_keys)
        
        # 设置默认值
        defaults = {
            "smtp_host": "smtp.gmail.com",
            "smtp_port": "587",
            "smtp_username": "",
            "smtp_password": "",
            "smtp_use_tls": "true"
        }
        
        return {key: configs.get(key, defaults[key]) for key in smtp_keys}
    
    def set_smtp_config(self, smtp_config: Dict[str, str]) -> bool:
        """设置SMTP配置"""
        try:
            # 验证必需的配置项
            required_keys = ["smtp_host", "smtp_port", "smtp_username", "smtp_password"]
            for key in required_keys:
                if key not in smtp_config or not smtp_config[key]:
                    raise ValueError(f"SMTP配置项 {key} 不能为空")
            
            # 设置配置
            for key, value in smtp_config.items():
                if key in required_keys or key == "smtp_use_tls":
                    self.set_config(key, value)
            
            return True
            
        except Exception as e:
            raise e
    
    def get_notification_config(self) -> Dict[str, str]:
        """获取通知配置"""
        notification_keys = [
            "notification_enabled", "email_send_interval"
        ]
        
        configs = self.get_configs_by_keys(notification_keys)
        
        # 设置默认值
        defaults = {
            "notification_enabled": "true",
            "email_send_interval": "5"
        }
        
        return {key: configs.get(key, defaults[key]) for key in notification_keys}
    
    def set_notification_config(self, notification_config: Dict[str, str]) -> bool:
        """设置通知配置"""
        try:
            # 验证配置值
            if "notification_enabled" in notification_config:
                value = notification_config["notification_enabled"].lower()
                if value not in ["true", "false"]:
                    raise ValueError("notification_enabled 必须是 true 或 false")
            
            if "email_send_interval" in notification_config:
                try:
                    interval = int(notification_config["email_send_interval"])
                    if interval < 1 or interval > 60:
                        raise ValueError("email_send_interval 必须在 1-60 分钟之间")
                except ValueError:
                    raise ValueError("email_send_interval 必须是有效的数字")
            
            # 设置配置
            for key, value in notification_config.items():
                if key in ["notification_enabled", "email_send_interval"]:
                    self.set_config(key, value)
            
            return True
            
        except Exception as e:
            raise e
    
    def is_notification_enabled(self) -> bool:
        """检查通知功能是否启用"""
        enabled = self.get_config("notification_enabled")
        return enabled and enabled.lower() == "true"
    
    def get_email_send_interval(self) -> int:
        """获取邮件发送间隔（分钟）"""
        interval = self.get_config("email_send_interval")
        try:
            return int(interval) if interval else 5
        except ValueError:
            return 5
