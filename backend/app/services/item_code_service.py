"""
物品编码生成服务
基于二级分类配置自动生成唯一编码，支持并发安全
"""

import re
from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.models.item import ItemPrimaryCategory, Item, ItemCategory
from app.core.database import get_db


class ItemCodeService:
    """物品编码生成服务"""
    
    @staticmethod
    def generate_item_code(db: Session, category_id: int, item_name: str = None) -> str:
        """
        生成物品编码
        
        Args:
            db: 数据库会话
            category_id: 二级分类ID
            item_name: 物品名称（可选，用于生成更友好的编码）
            
        Returns:
            生成的物品编码
            
        Raises:
            ValueError: 二级分类不存在或配置错误
        """
        # 获取二级分类配置
        category = db.query(ItemCategory).filter(
            ItemCategory.id == category_id,
            ItemCategory.is_active == True
        ).first()
        
        if not category:
            raise ValueError(f"二级分类 {category_id} 不存在或已禁用")
        
        # 获取一级分类配置
        primary_category = db.query(ItemPrimaryCategory).filter(
            ItemPrimaryCategory.id == category.primary_category_id,
            ItemPrimaryCategory.is_active == True
        ).first()
        
        if not primary_category:
            raise ValueError(f"一级分类 {category.primary_category_id} 不存在或已禁用")
        
        # 重新查询以获取最新数据
        primary_category = db.query(ItemPrimaryCategory).filter(
            ItemPrimaryCategory.id == category.primary_category_id
        ).with_for_update().first()
        
        if not primary_category:
            raise ValueError(f"一级分类 {category.primary_category_id} 不存在")
        
        # 生成编码
        code = ItemCodeService._generate_code_with_sequence(
            db, primary_category, category, item_name
        )
        
        # 更新序列号
        primary_category.current_sequence += 1
        db.commit()
        
        return code
    
    @staticmethod
    def _generate_code_with_sequence(
        db: Session, 
        primary_category: ItemPrimaryCategory,
        category: ItemCategory,
        item_name: str = None
    ) -> str:
        """
        使用序列号生成编码
        
        Args:
            db: 数据库会话
            primary_category: 一级分类对象
            category: 二级分类对象
            item_name: 物品名称
            
        Returns:
            生成的编码
        """
        # 使用IDM-一级分类code_prefix-序列号的格式
        category_prefix = primary_category.code_prefix if primary_category.code_prefix else "GEN"
        sequence = primary_category.current_sequence
        
        # 生成基础编码
        base_code = f"IDM-{category_prefix}-{sequence:04d}"
        
        # 检查编码是否已存在
        existing_item = db.query(Item).filter(Item.code == base_code).first()
        if existing_item:
            # 如果编码已存在，尝试添加后缀
            return ItemCodeService._generate_unique_code(db, base_code, item_name)
        
        return base_code
    
    @staticmethod
    def _generate_unique_code(db: Session, base_code: str, item_name: str = None) -> str:
        """
        生成唯一编码（处理冲突情况）
        
        Args:
            db: 数据库会话
            base_code: 基础编码
            item_name: 物品名称
            
        Returns:
            唯一的编码
        """
        # 尝试添加数字后缀
        suffix = 1
        while suffix <= 999:
            unique_code = f"{base_code}-{suffix:03d}"
            existing_item = db.query(Item).filter(Item.code == unique_code).first()
            if not existing_item:
                return unique_code
            suffix += 1
        
        # 如果数字后缀都用完了，使用时间戳
        import time
        timestamp = int(time.time() % 10000)  # 取后4位
        return f"{base_code}-T{timestamp:04d}"
    
    @staticmethod
    def validate_code_format(code: str, category: ItemCategory = None) -> bool:
        """
        验证编码格式是否符合新的标准
        
        Args:
            code: 待验证的编码
            category: 二级分类对象（可选，用于详细验证）
            
        Returns:
            是否符合格式要求
        """
        if not code:
            return False
        
        # 检查格式：IDM-一级分类前缀-序列号
        # 示例：IDM-LB-0001, IDM-BG-0002
        pattern = r'^IDM-[A-Z]{2,}-\d{4}(-\d{3}|-T\d{4})?$'
        if not re.match(pattern, code):
            return False
        
        # 如果提供了分类信息，可以通过一级分类进行验证
        if category and category.primary_category and category.primary_category.code_prefix:
            parts = code.split('-')
            if len(parts) >= 3 and parts[1] != category.primary_category.code_prefix:
                return False
        
        return True
    
    @staticmethod
    def get_next_sequence(db: Session, category_id: int) -> int:
        """
        获取下一个序列号（不更新数据库）
        
        Args:
            db: 数据库会话
            category_id: 二级分类ID
            
        Returns:
            下一个序列号
        """
        category = db.query(ItemCategory).filter(
            ItemCategory.id == category_id,
            ItemCategory.is_active == True
        ).first()
        
        if not category:
            raise ValueError(f"二级分类 {category_id} 不存在")
        
        primary_category = db.query(ItemPrimaryCategory).filter(
            ItemPrimaryCategory.id == category.primary_category_id
        ).first()
        
        if not primary_category:
            raise ValueError(f"一级分类 {category.primary_category_id} 不存在")
        
        return primary_category.current_sequence
    
    @staticmethod
    def reset_sequence(db: Session, category_id: int, new_sequence: int = 1) -> None:
        """
        重置序列号
        
        Args:
            db: 数据库会话
            category_id: 二级分类ID
            new_sequence: 新的序列号
        """
        category = db.query(ItemCategory).filter(
            ItemCategory.id == category_id,
            ItemCategory.is_active == True
        ).first()
        
        if not category:
            raise ValueError(f"二级分类 {category_id} 不存在")
        
        primary_category = db.query(ItemPrimaryCategory).filter(
            ItemPrimaryCategory.id == category.primary_category_id
        ).first()
        
        if not primary_category:
            raise ValueError(f"一级分类 {category.primary_category_id} 不存在")
        
        primary_category.current_sequence = new_sequence
        db.commit()
    
    @staticmethod
    def get_code_statistics(db: Session, category_id: int) -> dict:
        """
        获取编码统计信息
        
        Args:
            db: 数据库会话
            category_id: 二级分类ID
            
        Returns:
            统计信息字典
        """
        category = db.query(ItemCategory).filter(
            ItemCategory.id == category_id,
            ItemCategory.is_active == True
        ).first()
        
        if not category:
            raise ValueError(f"二级分类 {category_id} 不存在")
        
        primary_category = db.query(ItemPrimaryCategory).filter(
            ItemPrimaryCategory.id == category.primary_category_id
        ).first()
        
        if not primary_category:
            raise ValueError(f"一级分类 {category.primary_category_id} 不存在")
        
        # 统计该分类下的物品数量
        item_count = db.query(Item).filter(Item.category_id == category_id).count()
        
        return {
            "category_id": category_id,
            "category_name": category.name,
            "code_prefix": f"IDM-{primary_category.code_prefix or 'GEN'}",
            "code_format": "0000",
            "current_sequence": primary_category.current_sequence,
            "item_count": item_count,
            "next_code": f"IDM-{primary_category.code_prefix or 'GEN'}-{primary_category.current_sequence:04d}"
        }


# 便捷函数
def generate_item_code(category_id: int, item_name: str = None) -> str:
    """
    便捷函数：生成物品编码
    
    Args:
        category_id: 二级分类ID
        item_name: 物品名称
        
    Returns:
        生成的物品编码
    """
    db = next(get_db())
    try:
        return ItemCodeService.generate_item_code(db, category_id, item_name)
    finally:
        db.close()


def validate_item_code(code: str, category_id: int = None) -> bool:
    """
    便捷函数：验证物品编码格式
    
    Args:
        code: 待验证的编码
        category_id: 二级分类ID（可选）
        
    Returns:
        是否符合格式要求
    """
    if not code:
        return False
    
    # 检查格式：IDM-一级分类前缀-序列号
    pattern = r'^IDM-[A-Z]{2,}-\d{4}(-\d{3}|-T\d{4})?$'
    if not re.match(pattern, code):
        return False
    
    return True 