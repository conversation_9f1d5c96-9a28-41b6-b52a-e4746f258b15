from typing import Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func
from datetime import datetime, date
from decimal import Decimal

from app.models.supplier import ItemSupplier, SupplierPrice
from app.models.exchange_rate import ExchangeRate
from app.core.exceptions import BusinessException, ResourceNotFoundException


class ItemPriceService:
    """物品价格计算服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_item_price_info(
        self, 
        item_id: int, 
        quantity: Optional[int],
        supplier_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        获取物品价格信息（包含原币价格和USD价格）
        
        Args:
            item_id: 物品ID
            quantity: 数量，如果为None则返回最小数量的价格记录
            supplier_id: 指定供应商ID（可选，如果不指定则自动选择最高优先级供应商）
            
        Returns:
            Dict: 包含价格信息的字典
        """
        try:
            # 1. 获取供应商（指定或最高优先级）
            if supplier_id:
                item_supplier = self._get_specific_item_supplier(item_id, supplier_id)
            else:
                item_supplier = self._get_highest_priority_supplier(item_id)
            
            if not item_supplier:
                raise BusinessException(f"物品 {item_id} 没有可用的供应商")
            
            # 2. 获取价格记录（支持数量阶梯定价）
            price_record = self._get_tier_price_record(item_supplier.id, quantity)
            
            if not price_record:
                quantity_text = f"数量 {quantity}" if quantity is not None else "最小数量"
                raise BusinessException(f"物品 {item_id} 在{quantity_text}下没有有效价格")
            
            # 3. 获取汇率（如果非USD）
            exchange_rate = None
            if price_record.currency_code != "USD":
                exchange_rate = self._get_latest_exchange_rate(price_record.currency_code)
            
            # 4. 计算价格
            original_price = price_record.unit_price
            # 如果数量为None，使用最小数量计算总价
            effective_quantity = quantity if quantity is not None else price_record.min_quantity
            total_original_price = original_price * effective_quantity
            
            # 5. 计算USD价格
            usd_price = None
            total_usd_price = None
            if exchange_rate:
                usd_price = self._convert_to_usd(original_price, exchange_rate)
                total_usd_price = usd_price * effective_quantity
            elif price_record.currency_code == "USD":
                usd_price = original_price
                total_usd_price = total_original_price
            
            return {
                "item_id": item_id,
                "supplier_id": item_supplier.supplier_id,
                "supplier_name": item_supplier.supplier.name_en or item_supplier.supplier.name_cn,
                "supplier_names": {
                    "name_cn": item_supplier.supplier.name_cn,
                    "name_en": item_supplier.supplier.name_en
                },
                "priority": item_supplier.priority,
                "quantity": effective_quantity,
                "unit_price": {
                    "amount": float(original_price),
                    "currency": price_record.currency_code,
                    "usd_amount": float(usd_price) if usd_price else None
                },
                "total_price": {
                    "amount": float(total_original_price),
                    "currency": price_record.currency_code,
                    "usd_amount": float(total_usd_price) if total_usd_price else None
                },
                "exchange_rate": float(exchange_rate) if exchange_rate else None,
                "price_record": {
                    "min_quantity": price_record.min_quantity,
                    "max_quantity": price_record.max_quantity,
                    "valid_from": price_record.valid_from,
                    "valid_to": price_record.valid_to,
                    "remarks": price_record.remarks
                }
            }
            
        except Exception as e:
            if isinstance(e, (BusinessException, ResourceNotFoundException)):
                raise
            raise BusinessException(f"获取物品价格信息失败: {str(e)}")
    
    def _get_specific_item_supplier(self, item_id: int, supplier_id: int) -> Optional[ItemSupplier]:
        """获取指定的物品供应商关联"""
        return self.db.query(ItemSupplier).filter(
            and_(
                ItemSupplier.item_id == item_id,
                ItemSupplier.supplier_id == supplier_id,
                ItemSupplier.status == "active"
            )
        ).first()
    
    def _get_highest_priority_supplier(self, item_id: int) -> Optional[ItemSupplier]:
        """获取最高优先级的供应商（优先级数字最小）"""
        return self.db.query(ItemSupplier).filter(
            and_(
                ItemSupplier.item_id == item_id,
                ItemSupplier.status == "active"
            )
        ).order_by(ItemSupplier.priority.asc()).first()
    
    def _get_tier_price_record(self, item_supplier_id: int, quantity: Optional[int]) -> Optional[SupplierPrice]:
        """获取数量阶梯定价记录
        
        Args:
            item_supplier_id: 物品供应商关联ID
            quantity: 数量，如果为None则返回最小数量的价格记录
            
        Returns:
            SupplierPrice: 价格记录
        """
        current_time = datetime.now()
        
        # 查找所有有效价格记录，按最小数量排序
        price_records = self.db.query(SupplierPrice).filter(
            and_(
                SupplierPrice.item_supplier_id == item_supplier_id,
                SupplierPrice.status == "active",
                SupplierPrice.valid_from <= current_time,
                or_(
                    SupplierPrice.valid_to.is_(None),
                    SupplierPrice.valid_to > current_time
                )
            )
        ).order_by(SupplierPrice.min_quantity.asc()).all()
        
        if not price_records:
            return None
        
        # 如果数量为None，返回最小数量的价格记录
        if quantity is None:
            return price_records[0]  # 已经是按min_quantity升序排列的
        
        # 找到适合当前数量的价格记录
        for price_record in price_records:
            if (quantity >= price_record.min_quantity and 
                (price_record.max_quantity is None or quantity <= price_record.max_quantity)):
                return price_record
        
        return None
    
    def _get_latest_exchange_rate(self, currency_code: str) -> Optional[Decimal]:
        """获取最新的有效汇率"""
        if currency_code == "USD":
            return None
            
        exchange_rate = self.db.query(ExchangeRate).filter(
            and_(
                ExchangeRate.currency_code == currency_code,
                ExchangeRate.status == "active"
            )
        ).order_by(ExchangeRate.effective_month.desc()).first()
        
        if not exchange_rate:
            raise BusinessException(f"货币 {currency_code} 没有可用的汇率")
        
        return exchange_rate.rate
    
    def _convert_to_usd(self, price: Decimal, exchange_rate: Decimal) -> Decimal:
        """将价格转换为USD"""
        usd_price = price / exchange_rate
        
        # 根据金额大小确定精度
        if usd_price >= 1:
            return usd_price.quantize(Decimal('0.01'))
        else:
            return usd_price.quantize(Decimal('0.0001'))
    
    def get_supplier_price_comparison(
        self, 
        item_id: int, 
        quantity: Optional[int]
    ) -> Dict[str, Any]:
        """
        获取所有供应商的价格比较（按优先级排序）
        
        Args:
            item_id: 物品ID
            quantity: 数量，如果为None则返回最小数量的价格记录
            
        Returns:
            Dict: 包含所有供应商价格比较的字典
        """
        try:
            # 获取所有活跃的供应商
            item_suppliers = self.db.query(ItemSupplier).filter(
                and_(
                    ItemSupplier.item_id == item_id,
                    ItemSupplier.status == "active"
                )
            ).order_by(ItemSupplier.priority.asc()).all()
            
            if not item_suppliers:
                raise BusinessException(f"物品 {item_id} 没有可用的供应商")
            
            supplier_prices = []
            
            for item_supplier in item_suppliers:
                try:
                    price_info = self.get_item_price_info(item_id, quantity, item_supplier.supplier_id)
                    supplier_prices.append(price_info)
                except BusinessException as e:
                    # 如果某个供应商没有有效价格，跳过但不影响其他供应商
                    continue
            
            if not supplier_prices:
                quantity_text = f"数量 {quantity}" if quantity is not None else "最小数量"
                raise BusinessException(f"物品 {item_id} 在{quantity_text}下没有可用的价格")
            
            return {
                "item_id": item_id,
                "quantity": quantity,
                "quantity_display": quantity if quantity is not None else "最小数量",
                "supplier_count": len(supplier_prices),
                "suppliers": supplier_prices
            }
            
        except Exception as e:
            if isinstance(e, (BusinessException, ResourceNotFoundException)):
                raise
            raise BusinessException(f"获取供应商价格比较失败: {str(e)}")
