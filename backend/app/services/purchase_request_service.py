from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, Float, Integer
from app.models.purchase import PurchaseRequest, PurchaseRequestItem, PurchaseCartItem
from app.models.user import User
from app.schemas.purchase_request import (
    CreateRequestRequest, UpdateRequestRequest, PurchaseRequestWithDetails, 
    RequestItemWithDetails, ExchangeRateInfo, PurchaseRequestResponse, RequestItemResponse
)
from app.core.exceptions import BusinessException, ResourceNotFoundException
from app.core.constants import (
    PurchaseRequestStatus, FlowActionType, ApprovalLevel, 
    StatusFlow, ProgressCalculator
)
from app.services.notification_integration_service import NotificationIntegrationService
import uuid
import logging
from datetime import datetime, timedelta
from sqlalchemy import or_
from decimal import Decimal
from app.services.exchange_rate_service import ExchangeRateService


class PurchaseRequestService:
    """采购申请服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_request_from_cart_items(
        self, 
        department_id: int, 
        submitter_id: int, 
        cart_item_ids: List[int],
        notes: Optional[str] = None
    ) -> PurchaseRequest:
        """从购物车项目创建采购申请"""
        # 获取购物车项目
        cart_items = self.db.query(PurchaseCartItem).filter(
            PurchaseCartItem.id.in_(cart_item_ids),
            PurchaseCartItem.department_id == department_id
        ).all()
        
        if not cart_items:
            raise BusinessException("没有找到有效的购物车项目")
        
        # 生成申请单号 - 使用新的PR前缀格式
        request_no = f"PR{datetime.now().strftime('%Y%m%d%H%M%S')}{submitter_id:04d}"
        
        # 创建采购申请
        request = PurchaseRequest(
            request_no=request_no,
            department_id=department_id,
            submitter_id=submitter_id,
            status=PurchaseRequestStatus.PENDING_SUBMISSION,
            notes=notes,
            submitted_at=datetime.now()
        )
        
        self.db.add(request)
        self.db.flush()  # 获取request.id
        
        # 创建申请明细
        for cart_item in cart_items:
            # 从物品表获取物品信息
            from app.models.item import Item
            item = self.db.query(Item).filter(Item.id == cart_item.item_id).first()
            
            if item:
                item_code = item.code
                item_name = item.name
            else:
                item_code = ""
                item_name = "未知物品"
            
            request_item = PurchaseRequestItem(
                request_id=request.id,
                item_id=cart_item.item_id,
                item_code=item_code,
                item_name=item_name,
                spq_quantity=cart_item.spq_quantity,
                spq_count=cart_item.spq_count,
                spq_unit=cart_item.spq_unit,
                notes=cart_item.notes
            )
            
            self.db.add(request_item)
        
        # 检查汇率有效性
        self._check_exchange_rate_validity(request.id)
        
        self.db.commit()
        self.db.refresh(request)
        
        # 清空已提交的购物车项目
        self.db.query(PurchaseCartItem).filter(
            PurchaseCartItem.id.in_(cart_item_ids)
        ).delete()
        self.db.commit()
        
        # 创建采购申请提交通知
        try:
            notification_service = NotificationIntegrationService(self.db)
            
            # 获取申请物品名称（取第一个物品作为代表）
            first_item = self.db.query(PurchaseRequestItem).filter(
                PurchaseRequestItem.request_id == request.id
            ).first()
            
            if first_item:
                item_name = first_item.item_name
            else:
                item_name = "未知物品"
            
            # 为申请人创建通知
            notification_service.create_approval_notification(
                user_id=submitter_id,
                approval_type="purchase_request",
                item_name=item_name,
                request_id=request.id,
                status="已提交"
            )
            
        except Exception as e:
            # 通知创建失败不影响申请流程
            logging.warning(f"创建采购申请提交通知失败: {str(e)}")
        
        return request
    
    def _check_exchange_rate_validity(self, request_id: int):
        """
        检查采购申请的汇率有效性
        
        Args:
            request_id: 采购申请ID
        
        Raises:
            BusinessException: 当汇率无效时抛出异常
        """
        # 获取申请明细
        request_items = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.request_id == request_id
        ).all()
        
        if not request_items:
            return
        
        # 收集所有涉及的货币
        currencies = set()
        for item in request_items:
            # 获取物品的供应商价格信息
            from app.models.supplier import ItemSupplier, SupplierPrice
            from datetime import datetime
            
            # 查找物品的供应商关系
            item_suppliers = self.db.query(ItemSupplier).filter(
                ItemSupplier.item_id == item.item_id,
                ItemSupplier.status == "active"
            ).all()
            
            for item_supplier in item_suppliers:
                # 查找有效的价格记录
                current_time = datetime.now()
                prices = self.db.query(SupplierPrice).filter(
                    and_(
                        SupplierPrice.item_supplier_id == item_supplier.id,
                        SupplierPrice.status == "active",
                        SupplierPrice.valid_from <= current_time,
                        or_(
                            SupplierPrice.valid_to.is_(None),
                            SupplierPrice.valid_to > current_time
                        )
                    )
                ).all()
                
                for price in prices:
                    if price.currency_code != "USD":
                        currencies.add(price.currency_code)
        
        # 检查每种货币的汇率有效性
        if currencies:
            exchange_rate_service = ExchangeRateService(self.db)
            validation_results = exchange_rate_service.validate_purchase_request_exchange_rates([
                {"currency_code": currency} for currency in currencies
            ])
            
            if not validation_results["overall_valid"]:
                blocking_currencies = ", ".join(validation_results["blocking_currencies"])
                raise BusinessException(f"以下货币没有有效汇率，无法提交采购申请: {blocking_currencies}")
            
            # 记录汇率检查结果
            for item_validation in validation_results["items_validation"]:
                if not item_validation["has_current_month_rate"]:
                    logging.warning(f"货币 {item_validation['currency_code']} 使用历史汇率: {item_validation['message']}")
    
    def _validate_supplier_prices(self, request_id: int) -> Dict[str, Any]:
        """验证采购申请中所有物品的供应商价格配置（在最终审批前调用）"""
        from app.models.supplier import ItemSupplier, SupplierPrice
        
        request = self.get_request_by_id(request_id)
        if not request or not request.items:
            return {"is_valid": False, "error_message": "采购申请不存在或无物品明细"}
        
        validation_errors = []
        
        for item in request.items:
            # 检查是否有有效的供应商价格
            latest_price = self.db.query(SupplierPrice.unit_price, ItemSupplier.supplier_id).join(
                ItemSupplier, ItemSupplier.id == SupplierPrice.item_supplier_id
            ).filter(
                ItemSupplier.item_id == item.item_id,
                ItemSupplier.status == "active",
                SupplierPrice.status == "active"
            ).order_by(ItemSupplier.priority.asc(), SupplierPrice.updated_at.desc()).first()
            
            if not latest_price:
                validation_errors.append(
                    f"物品 '{item.item_name}' (编码: {item.item_code}) 没有配置有效的供应商价格"
                )
        
        if validation_errors:
            return {
                "is_valid": False,
                "error_message": "以下物品缺少有效的供应商价格配置：\n" + "\n".join(validation_errors)
            }
        
        return {"is_valid": True, "error_message": ""}
    
    def _lock_final_prices(self, request_id: int):
        """锁定采购申请的最终价格（在最终审批通过后调用）"""
        from app.models.supplier import ItemSupplier, SupplierPrice
        from app.models.purchase import PurchaseRequest, PurchaseRequestItem
        from datetime import datetime
        
        # 直接查询SQLAlchemy模型对象，确保在正确的session中
        request = self.db.query(PurchaseRequest).filter(PurchaseRequest.id == request_id).first()
        if not request:
            logging.error(f"申请 {request_id} 不存在")
            return
        
        # 确保items被加载
        if not request.items:
            logging.error(f"申请 {request_id} 没有物品明细")
            return
        
        total_final_amount = 0
        
        for item in request.items:
            # 获取最优先供应商的最新价格
            latest_price = self.db.query(SupplierPrice.unit_price, ItemSupplier.supplier_id).join(
                ItemSupplier, ItemSupplier.id == SupplierPrice.item_supplier_id
            ).filter(
                ItemSupplier.item_id == item.item_id,
                ItemSupplier.status == "active",
                SupplierPrice.status == "active"
            ).order_by(ItemSupplier.priority.asc(), SupplierPrice.updated_at.desc()).first()
            
            # 由于在审批前已经验证过价格，这里应该总是能找到价格
            if latest_price:
                unit_price = float(latest_price[0])
                supplier_id = latest_price[1]
                
                # 计算总价：SPQ数量 × SPQ个数 × 单价
                total_quantity = float(item.spq_quantity) * item.spq_count
                total_price = total_quantity * unit_price
                
                # 锁定价格 - 这些修改现在会被SQLAlchemy正确跟踪
                item.final_unit_price = unit_price
                item.final_total_price = total_price
                item.final_supplier_id = supplier_id
                item.price_locked_at = datetime.now()
                
                total_final_amount += total_price
                
                logging.info(f"物品 {item.item_name} 价格锁定成功: 单价={unit_price}, 总价={total_price}, 供应商ID={supplier_id}")
            else:
                # 这种情况不应该发生，因为审批前已经验证过价格
                # 如果发生，说明数据在审批过程中被修改，需要回滚
                raise BusinessException(
                    f"物品 '{item.item_name}' (编码: {item.item_code}) 在价格锁定过程中未找到有效价格，"
                    f"可能数据已被修改。请重新进行审批流程。"
                )
        
        # 更新申请的总金额
        request.final_total = total_final_amount
        
        # 现在commit会正确保存所有修改
        self.db.commit()
        logging.info(f"申请 {request_id} 价格锁定完成，总金额: {total_final_amount}")
    
    def get_request_by_id(self, request_id: int, current_user: User = None) -> Optional[PurchaseRequestWithDetails]:
        """根据ID获取采购申请（带权限检查）"""
        from sqlalchemy.orm import joinedload
        from app.models.user import Department, User as UserModel
        from app.services.auth import has_permission
        
        # 使用join查询获取部门和用户信息
        result = self.db.query(
            PurchaseRequest,
            Department.name.label('department_name'),
            UserModel.full_name.label('submitter_name')
        ).join(
            Department, PurchaseRequest.department_id == Department.id
        ).join(
            UserModel, PurchaseRequest.submitter_id == UserModel.id
        ).options(
            joinedload(PurchaseRequest.items),
            joinedload(PurchaseRequest.flow_history)
        ).filter(PurchaseRequest.id == request_id).first()
        
        if result:
            request = result[0]  # PurchaseRequest对象
            department_name = result[1]  # department_name
            submitter_name = result[2]   # submitter_name
            
            # 权限检查
            if current_user:
                # 超级用户可以看到所有申请
                if current_user.is_superuser:
                    pass
                # 有purchase.read_all权限的用户可以看到所有申请
                elif has_permission(current_user, "purchase.read_all", self.db):
                    pass
                # 有purchase.read权限的用户只能看到自己部门的申请
                elif has_permission(current_user, "purchase.read", self.db):
                    if current_user.department_id and request.department_id != current_user.department_id:
                        return None  # 权限不足
                # 没有权限的用户只能看到自己提交的申请
                else:
                    if request.submitter_id != current_user.id:
                        return None  # 权限不足
            
            # 处理申请明细，填充扩展信息
            detailed_items = []
            total_amount = 0
            
            if request.items:
                for item in request.items:
                    detailed_item = self._create_request_item_with_details(item)
                    detailed_items.append(detailed_item)
                    if detailed_item.estimated_total_price:
                        total_amount += float(detailed_item.estimated_total_price)
            
            # 创建响应对象
            response = PurchaseRequestWithDetails(
                id=request.id,
                request_no=request.request_no,
                request_uuid=request.request_uuid,
                department_id=request.department_id,
                submitter_id=request.submitter_id,
                status=request.status,
                final_total=request.final_total,
                notes=request.notes,
                qr_code=request.qr_code,
                submitted_at=request.submitted_at,
                created_at=request.created_at,
                updated_at=request.updated_at,
                department_name=department_name,
                submitter_name=submitter_name,
                items=detailed_items,
                flow_history=request.flow_history
            )
            
            return response
        
        return None
    
    def get_requests_by_department(self, department_id: int, current_user: User = None) -> List[PurchaseRequestWithDetails]:
        """获取部门的采购申请列表（带权限检查）"""
        from sqlalchemy.orm import joinedload
        from app.models.user import Department, User as UserModel
        from app.services.auth import has_permission
        
        # 权限检查
        if current_user:
            # 超级用户可以看到所有部门
            if current_user.is_superuser:
                pass
            # 有purchase.read_all权限的用户可以看到所有部门
            elif has_permission(current_user, "purchase.read_all", self.db):
                pass
            # 有purchase.read权限的用户只能看到自己部门
            elif has_permission(current_user, "purchase.read", self.db):
                if current_user.department_id and department_id != current_user.department_id:
                    return []  # 权限不足
            # 没有权限的用户只能看到自己提交的申请
            else:
                # 只能看到自己提交的申请，不按部门过滤
                return self.get_all_requests({"submitter_id": current_user.id}, current_user)
        
        # 使用join查询获取部门和用户信息
        query = self.db.query(
            PurchaseRequest,
            Department.name.label('department_name'),
            UserModel.full_name.label('submitter_name')
        ).join(
            Department, PurchaseRequest.department_id == Department.id
        ).join(
            UserModel, PurchaseRequest.submitter_id == UserModel.id
        ).options(
            joinedload(PurchaseRequest.items),
            joinedload(PurchaseRequest.flow_history)
        ).filter(
            PurchaseRequest.department_id == department_id
        )
        
        results = query.order_by(PurchaseRequest.created_at.desc()).all()
        
        # 将查询结果转换为PurchaseRequestWithDetails对象
        requests = []
        for result in results:
            request = result[0]  # PurchaseRequest对象
            department_name = result[1]  # department_name
            submitter_name = result[2]   # submitter_name
            
            # 处理申请明细，填充扩展信息
            detailed_items = []
            total_amount = 0
            
            if request.items:
                for item in request.items:
                    detailed_item = self._create_request_item_with_details(item)
                    detailed_items.append(detailed_item)
                    if detailed_item.estimated_total_price:
                        total_amount += float(detailed_item.estimated_total_price)
            
            # 创建响应对象
            response = PurchaseRequestWithDetails(
                id=request.id,
                request_no=request.request_no,
                request_uuid=request.request_uuid,
                department_id=request.department_id,
                submitter_id=request.submitter_id,
                status=request.status,
                final_total=request.final_total,
                notes=request.notes,
                qr_code=request.qr_code,
                submitted_at=request.submitted_at,
                created_at=request.created_at,
                updated_at=request.updated_at,
                department_name=department_name,
                submitter_name=submitter_name,
                items=detailed_items,
                flow_history=request.flow_history
            )
            
            requests.append(response)
        
        return requests
    
    def get_all_requests_for_api(self, filters: Dict[str, Any] = None, current_user: User = None) -> List[PurchaseRequestResponse]:
        """获取所有采购申请（带权限过滤）- 返回API响应格式"""
        from sqlalchemy.orm import joinedload
        from app.models.user import Department, User as UserModel
        from app.services.auth import has_permission
        from app.schemas.purchase_request import PurchaseRequestResponse, RequestItemResponse
        
        # 使用join查询获取部门和用户信息
        query = self.db.query(
            PurchaseRequest,
            Department.name.label('department_name'),
            UserModel.full_name.label('submitter_name')
        ).join(
            Department, PurchaseRequest.department_id == Department.id
        ).join(
            UserModel, PurchaseRequest.submitter_id == UserModel.id
        ).options(
            joinedload(PurchaseRequest.items),
            joinedload(PurchaseRequest.flow_history)
        )
        
        # 权限过滤逻辑
        if current_user:
            # 超级用户可以看到所有申请
            if current_user.is_superuser:
                pass
            # 有purchase.read_all权限的用户可以看到所有申请
            elif has_permission(current_user, "purchase.read_all", self.db):
                pass
            # 有purchase.read权限的用户只能看到自己部门的申请
            elif has_permission(current_user, "purchase.read", self.db):
                if current_user.department_id:
                    query = query.filter(PurchaseRequest.department_id == current_user.department_id)
                else:
                    # 用户没有部门，只能看到自己提交的申请
                    query = query.filter(PurchaseRequest.submitter_id == current_user.id)
            # 没有权限的用户只能看到自己提交的申请
            else:
                query = query.filter(PurchaseRequest.submitter_id == current_user.id)
        
        # 应用其他过滤条件
        if filters:
            if filters.get("department_id"):
                query = query.filter(PurchaseRequest.department_id == filters["department_id"])
            if filters.get("status"):
                query = query.filter(PurchaseRequest.status == filters["status"])
            if filters.get("submitter_id"):
                query = query.filter(PurchaseRequest.submitter_id == filters["submitter_id"])
        
        results = query.order_by(PurchaseRequest.created_at.desc()).all()
        
        # 将查询结果转换为PurchaseRequestResponse对象
        requests = []
        for result in results:
            request = result[0]  # PurchaseRequest对象
            department_name = result[1]  # department_name
            submitter_name = result[2]   # submitter_name
            
            # 处理申请明细，填充扩展信息
            detailed_items = []
            total_amount = 0
            
            if request.items:
                for item in request.items:
                    detailed_item = self._create_request_item_with_details(item)
                    # 转换为RequestItemResponse格式
                    response_item = RequestItemResponse(
                        id=detailed_item.id,
                        request_id=detailed_item.request_id,
                        item_id=detailed_item.item_id,
                        item_code=detailed_item.item_code,
                        item_name=detailed_item.item_name,
                        item_image_url=detailed_item.item_image_url,
                        spq_quantity=detailed_item.spq_quantity,
                        spq_count=detailed_item.spq_count,
                        spq_unit=detailed_item.spq_unit,
                        final_unit_price=detailed_item.final_unit_price,
                        final_total_price=detailed_item.final_total_price,
                        final_supplier_id=detailed_item.final_supplier_id,
                        estimated_unit_price=detailed_item.estimated_unit_price,
                        estimated_total_price=detailed_item.estimated_total_price,
                        preferred_supplier=detailed_item.preferred_supplier,
                        available_suppliers=detailed_item.available_suppliers,
                        notes=detailed_item.notes,
                        created_at=detailed_item.created_at,
                        updated_at=detailed_item.updated_at
                    )
                    detailed_items.append(response_item)
                    if detailed_item.estimated_total_price:
                        total_amount += float(detailed_item.estimated_total_price)
            
            # 创建响应对象
            response = PurchaseRequestResponse(
                id=request.id,
                request_no=request.request_no,
                request_uuid=request.request_uuid,
                department_id=request.department_id,
                submitter_id=request.submitter_id,
                status=request.status,
                final_total=request.final_total,
                notes=request.notes,
                qr_code=request.qr_code,
                submitted_at=request.submitted_at,
                created_at=request.created_at,
                updated_at=request.updated_at,
                department_name=department_name,
                submitter_name=submitter_name,
                items=detailed_items,
                flow_history=request.flow_history
            )
            
            requests.append(response)
        
        return requests
    
    def get_all_requests(self, filters: Dict[str, Any] = None, current_user: User = None) -> List[PurchaseRequestWithDetails]:
        """获取所有采购申请（带权限过滤）"""
        from sqlalchemy.orm import joinedload
        from app.models.user import Department, User as UserModel
        from app.services.auth import has_permission
        
        # 使用join查询获取部门和用户信息
        query = self.db.query(
            PurchaseRequest,
            Department.name.label('department_name'),
            UserModel.full_name.label('submitter_name')
        ).join(
            Department, PurchaseRequest.department_id == Department.id
        ).join(
            UserModel, PurchaseRequest.submitter_id == UserModel.id
        ).options(
            joinedload(PurchaseRequest.items),
            joinedload(PurchaseRequest.flow_history)
        )
        
        # 权限过滤逻辑
        if current_user:
            # 超级用户可以看到所有申请
            if current_user.is_superuser:
                pass
            # 有purchase.read_all权限的用户可以看到所有申请
            elif has_permission(current_user, "purchase.read_all", self.db):
                pass
            # 有purchase.read权限的用户只能看到自己部门的申请
            elif has_permission(current_user, "purchase.read", self.db):
                if current_user.department_id:
                    query = query.filter(PurchaseRequest.department_id == current_user.department_id)
                else:
                    # 用户没有部门，只能看到自己提交的申请
                    query = query.filter(PurchaseRequest.submitter_id == current_user.id)
            # 没有权限的用户只能看到自己提交的申请
            else:
                query = query.filter(PurchaseRequest.submitter_id == current_user.id)
        
        # 应用其他过滤条件
        if filters:
            if filters.get("department_id"):
                query = query.filter(PurchaseRequest.department_id == filters["department_id"])
            if filters.get("status"):
                query = query.filter(PurchaseRequest.status == filters["status"])
            if filters.get("submitter_id"):
                query = query.filter(PurchaseRequest.submitter_id == filters["submitter_id"])
        
        results = query.order_by(PurchaseRequest.created_at.desc()).all()
        
        # 将查询结果转换为PurchaseRequestWithDetails对象
        requests = []
        for result in results:
            request = result[0]  # PurchaseRequest对象
            department_name = result[1]  # department_name
            submitter_name = result[2]   # submitter_name
            
            # 处理申请明细，填充扩展信息
            detailed_items = []
            total_amount = 0
            
            if request.items:
                for item in request.items:
                    detailed_item = self._create_request_item_with_details(item)
                    detailed_items.append(detailed_item)
                    if detailed_item.estimated_total_price:
                        total_amount += float(detailed_item.estimated_total_price)
            
            # 创建响应对象
            response = PurchaseRequestWithDetails(
                id=request.id,
                request_no=request.request_no,
                request_uuid=request.request_uuid,
                department_id=request.department_id,
                submitter_id=request.submitter_id,
                status=request.status,
                final_total=request.final_total,
                notes=request.notes,
                qr_code=request.qr_code,
                submitted_at=request.submitted_at,
                created_at=request.created_at,
                updated_at=request.updated_at,
                department_name=department_name,
                submitter_name=submitter_name,
                items=detailed_items,
                flow_history=request.flow_history
            )
            
            requests.append(response)
        
        return requests
    
    def update_request_status(self, request_id: int, new_status: str, operator_id: int = 1, operator_name: str = "系统", action: str = "status_update", comments: str = "") -> PurchaseRequestWithDetails:
        """更新申请状态 - 已废弃，请使用 ApprovalService.update_request_status"""
        # 委托给 ApprovalService 处理
        from app.services.approval_service import ApprovalService
        approval_service = ApprovalService(self.db)
        return approval_service.update_request_status(request_id, new_status, operator_id, operator_name, action, comments)
    
    def approve_request(self, request_id: int, action: str, comments: Optional[str] = None, approver_id: int = 1, approver_name: str = "审批人") -> PurchaseRequestWithDetails:
        """审批申请 - 已废弃，请使用 ApprovalService 的相应方法"""
        # 委托给 ApprovalService 处理
        from app.services.approval_service import ApprovalService
        approval_service = ApprovalService(self.db)

        # 根据当前状态调用相应的审批方法
        request = self.db.query(PurchaseRequest).filter(PurchaseRequest.id == request_id).first()
        if not request:
            raise ResourceNotFoundException("采购申请", request_id)

        # 构造审批数据
        from app.schemas.approval import ReviewRequest, ApprovalRequest

        if request.status == "under_review":
            review_data = ReviewRequest(approved=(action == "approve"), comments=comments or "")
            approval_service.submit_for_review(request_id, approver_id, approver_name, review_data)
        elif request.status == "under_principle_approval":
            approval_data = ApprovalRequest(approved=(action == "approve"), comments=comments or "")
            approval_service.principle_approval(request_id, approver_id, approver_name, approval_data)
        elif request.status == "under_final_approval":
            approval_data = ApprovalRequest(approved=(action == "approve"), comments=comments or "")
            approval_service.final_approval(request_id, approver_id, approver_name, approval_data)
        else:
            raise BusinessException("当前状态不允许审批")

        # 返回更新后的申请详情
        return self.get_request_by_id(request_id)
    
    def withdraw_request(self, request_id: int, user_id: int) -> PurchaseRequestWithDetails:
        """撤回采购申请 - 已废弃，请使用 ApprovalService.withdraw_request"""
        # 委托给 ApprovalService 处理
        from app.services.approval_service import ApprovalService
        approval_service = ApprovalService(self.db)
        return approval_service.withdraw_request(request_id, user_id)
    
    def withdraw_to_pending(self, request_id: int, user_id: int) -> PurchaseRequestWithDetails:
        """撤销申请回到待提交状态 - 已废弃，请使用 ApprovalService.withdraw_to_pending"""
        # 委托给 ApprovalService 处理
        from app.services.approval_service import ApprovalService
        approval_service = ApprovalService(self.db)
        return approval_service.withdraw_to_pending(request_id, user_id)
    
    def get_request_items(self, request_id: int) -> List[RequestItemWithDetails]:
        """获取申请明细"""
        from app.models.item import Item
        
        items = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.request_id == request_id
        ).all()
        
        # 为每个申请明细创建详细信息对象
        detailed_items = []
        for item in items:
            try:
                detailed_item = self._create_request_item_with_details(item)
                detailed_items.append(detailed_item)
            except Exception as e:
                logging.error(f"处理申请明细时出错: item_id={item.item_id}, error={str(e)}")
                continue
        
        return detailed_items
    
    def add_item_from_cart(self, request_id: int, item_data: dict, user_id: int) -> PurchaseRequestItem:
        """从购物车添加物品到申请"""
        request = self.get_request_by_id(request_id)
        if not request:
            raise ResourceNotFoundException("采购申请", request_id)
        
        # 检查权限：只有申请人可以修改申请
        if request.submitter_id != user_id:
            raise BusinessException("只能修改自己提交的申请")
        
        # 检查状态：只有在待提交、已拒绝、已退回状态下可以修改
        if request.status not in ["pending_submission", "rejected", "returned"]:
            raise BusinessException("当前状态不允许修改")
        
        # 检查是否已经存在于申请中
        existing_item = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.request_id == request_id,
            PurchaseRequestItem.item_id == item_data["item_id"]
        ).first()
        
        if existing_item:
            # 如果已存在，增加SPQ个数
            existing_item.spq_count += item_data["spq_count"]
            existing_item.updated_at = datetime.now()
            self.db.commit()
            
            # Refresh the item object to get updated data from database
            self.db.refresh(existing_item)
            
            return existing_item
        else:
            # 创建新的申请明细
            request_item = PurchaseRequestItem(
                request_id=request_id,
                item_id=item_data["item_id"],
                item_code=item_data.get("item_code", ""),
                item_name=item_data.get("item_name", ""),
                spq_quantity=item_data["spq_quantity"],
                spq_count=item_data["spq_count"],
                spq_unit=item_data.get("spq_unit", ""),
                notes=item_data.get("notes", "")
            )
            
            self.db.add(request_item)
            self.db.commit()
            
            # Refresh the item object to get updated data from database
            self.db.refresh(request_item)
            
            # 更新申请总金额
            self._update_request_total(request_id)
            
            return request_item
    
    def remove_item_from_request(self, request_id: int, item_id: int, user_id: int) -> bool:
        """从申请中删除物品"""
        request = self.get_request_by_id(request_id)
        if not request:
            raise ResourceNotFoundException("采购申请", request_id)
        
        # 检查权限：只有申请人可以修改申请
        if request.submitter_id != user_id:
            raise BusinessException("只能修改自己提交的申请")
        
        # 检查状态：只有在待提交、已拒绝、已退回状态下可以修改
        if request.status not in ["pending_submission", "rejected", "returned"]:
            raise BusinessException("当前状态不允许修改")
        
        # 删除申请明细
        deleted_count = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.request_id == request_id,
            PurchaseRequestItem.id == item_id
        ).delete()
        
        if deleted_count > 0:
            self.db.commit()
            # 更新申请总金额
            self._update_request_total(request_id)
            return True
        
        return False
    
    def update_item_quantity(self, request_id: int, item_id: int, spq_count: int, user_id: int) -> PurchaseRequestItem:
        """更新申请物品SPQ个数"""
        request = self.get_request_by_id(request_id)
        if not request:
            raise ResourceNotFoundException("采购申请", request_id)
        
        # 检查权限：只有申请人可以修改申请
        if request.submitter_id != user_id:
            raise BusinessException("只能修改自己提交的申请")
        
        # 检查状态：只有在待提交、已拒绝、已退回状态下可以修改
        if request.status not in ["pending_submission", "rejected", "returned"]:
            raise BusinessException("当前状态不允许修改")
        
        # 更新物品SPQ个数
        request_item = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.request_id == request_id,
            PurchaseRequestItem.id == item_id
        ).first()
        
        if not request_item:
            raise ResourceNotFoundException("申请明细", item_id)
        
        request_item.spq_count = spq_count
        request_item.updated_at = datetime.now()
        
        self.db.commit()
        
        # Refresh the item object to get updated data from database
        self.db.refresh(request_item)
        
        # 更新申请总金额
        self._update_request_total(request_id)
        
        return request_item

    def update_item_notes(self, request_id: int, item_id: int, notes: str, user_id: int) -> PurchaseRequestItem:
        """更新申请物品备注"""
        request = self.get_request_by_id(request_id)
        if not request:
            raise ResourceNotFoundException("采购申请", request_id)
        
        # 检查权限：只有申请人可以修改申请
        if request.submitter_id != user_id:
            raise BusinessException("只能修改自己提交的申请")
        
        # 检查状态：只有在待提交、已拒绝、已退回状态下可以修改
        if request.status not in ["pending_submission", "rejected", "returned"]:
            raise BusinessException("当前状态不允许修改")
        
        # 更新物品备注
        request_item = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.request_id == request_id,
            PurchaseRequestItem.id == item_id
        ).first()
        
        if not request_item:
            raise ResourceNotFoundException("申请明细", item_id)
        
        request_item.notes = notes
        request_item.updated_at = datetime.now()
        
        self.db.commit()
        # Remove the refresh call as it's not needed after commit and causes errors
        
        return request_item
    
    def _update_request_total(self, request_id: int):
        """更新申请总金额"""
        request = self.get_request_by_id(request_id)
        if not request:
            return
        
        # 由于预估价格字段已移除，总金额计算逻辑需要调整
        # 暂时设置为0，或者通过其他方式计算
        request.final_total = 0
        request.updated_at = datetime.now()
        
        self.db.commit()
        # Remove the refresh call as it's not needed after commit and causes errors
    
    def delete_request(self, request_id: int, user_id: int, return_to_cart: bool = False) -> bool:
        """删除采购申请"""
        request = self.get_request_by_id(request_id)
        if not request:
            raise ResourceNotFoundException("采购申请", request_id)
        
        # 检查权限：只有申请人或管理员可以删除
        if request.submitter_id != user_id:
            # 检查用户是否有管理员权限
            from app.models.user import User
            from app.models.permission import Role
            user = self.db.query(User).filter(User.id == user_id).first()
            if user and user.role_id:
                user_role = self.db.query(Role).filter(Role.id == user.role_id).first()
                if not user_role or user_role.code not in ["super_admin", "system_admin", "item_manager"]:
                    raise BusinessException("只能删除自己提交的申请")
            else:
                raise BusinessException("只能删除自己提交的申请")
        
        # 检查状态：只有草稿、待提交、已拒绝、已撤回状态的申请可以删除
        # 已经进入审批流程的申请（under_review, under_principle_approval, under_final_approval, approved）不能删除
        if request.status not in ["draft", "pending_submission", "rejected", "withdrawn"]:
            raise BusinessException("当前状态不允许删除，已进入审批流程的申请不能被删除")
        
        # 如果需要返回购物车，先保存申请明细信息
        items_to_return = []
        if return_to_cart:
            items_to_return = self.get_request_items(request_id)
        
        # 删除申请明细
        self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.request_id == request_id
        ).delete()
        
        # 删除申请
        self.db.delete(request)
        self.db.commit()
        
        # 如果需要返回购物车，将物品添加到购物车
        if return_to_cart and items_to_return:
            self._return_items_to_cart(items_to_return, request.department_id, user_id)
        
        return True
    
    def update_request(self, request_id: int, update_data: dict) -> PurchaseRequest:
        """更新采购申请"""
        try:
            print(f"DEBUG: 开始更新采购申请: request_id={request_id}, update_data={update_data}")
            logging.info(f"开始更新采购申请: request_id={request_id}, update_data={update_data}")
            
            # 获取申请单
            request = self.db.query(PurchaseRequest).filter(
                PurchaseRequest.id == request_id
            ).first()
            
            if not request:
                raise BusinessException("申请单不存在")
            
            print(f"DEBUG: 找到申请单: id={request.id}, status={request.status}")
            logging.info(f"找到申请单: id={request.id}, status={request.status}")
            
            # 检查状态是否允许编辑
            # 草稿、待提交、已拒绝、已撤回状态的申请可以编辑
            if request.status not in ["draft", "pending_submission", "rejected", "withdrawn"]:
                raise BusinessException("当前状态不允许编辑")
            
            # 更新基本信息
            if "notes" in update_data and update_data["notes"] is not None:
                request.notes = update_data["notes"]
                print(f"DEBUG: 更新备注: {update_data['notes']}")
                logging.info(f"更新备注: {update_data['notes']}")
            
            # 处理物品添加
            if "items_to_add" in update_data and update_data["items_to_add"]:
                print(f"DEBUG: 处理物品添加: {len(update_data['items_to_add'])} 个物品")
                logging.info(f"处理物品添加: {len(update_data['items_to_add'])} 个物品")
                for item_data in update_data["items_to_add"]:
                    self._add_item_to_request(request_id, item_data)
            
            # 处理物品删除
            if "items_to_delete" in update_data and update_data["items_to_delete"]:
                print(f"DEBUG: 处理物品删除: {update_data['items_to_delete']}")
                logging.info(f"处理物品删除: {update_data['items_to_delete']}")
                for item_id in update_data["items_to_delete"]:
                    self._remove_item_from_request(request_id, item_id)
            
            # 处理物品更新
            if "items_to_update" in update_data and update_data["items_to_update"]:
                print(f"DEBUG: 处理物品更新: {len(update_data['items_to_update'])} 个物品")
                logging.info(f"处理物品更新: {len(update_data['items_to_update'])} 个物品")
                for i, item_data in enumerate(update_data["items_to_update"]):
                    print(f"DEBUG: 处理第 {i+1} 个物品更新: {item_data}")
                    # 确保item_data包含必要的字段
                    if "id" not in item_data and "item_id" not in item_data:
                        raise BusinessException("更新物品数据缺少明细项ID或物品ID")
                    self._update_request_item(request_id, item_data)
            
            # 如果申请状态是已拒绝或已撤回，保存后自动改为待提交状态
            if request.status in ["rejected", "withdrawn"]:
                request.status = "pending_submission"
                print(f"DEBUG: 申请状态从 {request.status} 自动改为 pending_submission")
                logging.info(f"申请状态从 {request.status} 自动改为 pending_submission")
                
                # 清除锁定的货币字段
                self.clear_locked_currency_fields(request_id, "编辑拒绝申请回待提交状态时")
            
            # 更新修改时间
            request.updated_at = datetime.now()
            
            # 保存更改
            self.db.commit()
            # Remove the refresh call as it's not needed after commit and causes errors
            
            print(f"DEBUG: 采购申请更新成功: request_id={request_id}")
            logging.info(f"采购申请更新成功: request_id={request_id}")
            return request
            
        except Exception as e:
            self.db.rollback()
            print(f"DEBUG: 更新采购申请失败: request_id={request_id}, error={str(e)}")
            logging.error(f"更新采购申请失败: request_id={request_id}, error={str(e)}")
            raise BusinessException(f"更新申请失败: {str(e)}")
    
    def _add_item_to_request(self, request_id: int, item_data: dict):
        """添加物品到申请单"""
        try:
            # 创建新的申请明细
            request_item = PurchaseRequestItem(
                request_id=request_id,
                item_id=item_data["item_id"],
                item_code=item_data["item_code"],
                item_name=item_data["item_name"],
                spq_quantity=item_data["spq_quantity"],
                spq_count=item_data["spq_count"],
                spq_unit=item_data["spq_unit"],
                notes=item_data.get("notes")
            )
            
            self.db.add(request_item)
            self.db.commit()
            # Remove the refresh call as it's not needed after commit and causes errors
            
        except Exception as e:
            self.db.rollback()
            raise BusinessException(f"添加物品失败: {str(e)}")
    
    def _remove_item_from_request(self, request_id: int, detail_item_id: int):
        """从申请单移除物品"""
        try:
            # 前端传递的是 PurchaseRequestItem.id（申请明细项ID），不是物品ID
            request_item = self.db.query(PurchaseRequestItem).filter(
                PurchaseRequestItem.id == detail_item_id,
                PurchaseRequestItem.request_id == request_id
            ).first()
            
            if request_item:
                self.db.delete(request_item)
                self.db.commit()
                print(f"DEBUG: 成功删除申请明细项: id={detail_item_id}, item_id={request_item.item_id}, item_name={request_item.item_name}")
                logging.info(f"成功删除申请明细项: id={detail_item_id}, item_id={request_item.item_id}, item_name={request_item.item_name}")
            else:
                print(f"DEBUG: 要删除的申请明细项不存在: detail_item_id={detail_item_id}, request_id={request_id}")
                logging.warning(f"要删除的申请明细项不存在: detail_item_id={detail_item_id}, request_id={request_id}")
                
        except Exception as e:
            self.db.rollback()
            print(f"DEBUG: 删除申请明细项失败: detail_item_id={detail_item_id}, request_id={request_id}, error={str(e)}")
            logging.error(f"删除申请明细项失败: detail_item_id={detail_item_id}, request_id={request_id}, error={str(e)}")
            raise BusinessException(f"移除物品失败: {str(e)}")
    
    def _update_request_item(self, request_id: int, item_data: dict):
        """更新申请明细"""
        try:
            print(f"DEBUG: 开始更新申请明细: request_id={request_id}, item_data={item_data}")
            logging.info(f"更新申请明细: request_id={request_id}, item_data={item_data}")
            
            # 前端统一传递明细项ID
            if "id" not in item_data:
                raise BusinessException("更新物品数据缺少明细项ID")
            
            detail_item_id = item_data["id"]
            request_item = self.db.query(PurchaseRequestItem).filter(
                PurchaseRequestItem.id == detail_item_id,
                PurchaseRequestItem.request_id == request_id
            ).first()
            
            if not request_item:
                print(f"DEBUG: 申请明细不存在: request_id={request_id}, detail_item_id={detail_item_id}")
                logging.error(f"申请明细不存在: request_id={request_id}, detail_item_id={detail_item_id}")
                raise BusinessException("申请明细不存在")
            
            print(f"DEBUG: 找到申请明细: id={request_item.id}, item_id={request_item.item_id}, item_name={request_item.item_name}")
            logging.info(f"找到申请明细: id={request_item.id}, item_id={request_item.item_id}, item_name={request_item.item_name}")
            
            # 更新字段
            if "spq_count" in item_data:
                old_count = request_item.spq_count
                request_item.spq_count = item_data["spq_count"]
                print(f"DEBUG: 更新SPQ数量: {old_count} -> {item_data['spq_count']}")
                logging.info(f"更新SPQ数量: {old_count} -> {item_data['spq_count']}")
            if "notes" in item_data:
                old_notes = request_item.notes
                request_item.notes = item_data["notes"]
                print(f"DEBUG: 更新备注: {old_notes} -> {item_data['notes']}")
                logging.info(f"更新备注: {old_notes} -> {item_data['notes']}")
            
            request_item.updated_at = datetime.now()
            self.db.commit()
            # Remove the refresh call as it's not needed after commit and causes errors
            print(f"DEBUG: 申请明细更新成功: id={request_item.id}")
            logging.info(f"申请明细更新成功: id={request_item.id}")
            
        except Exception as e:
            self.db.rollback()
            print(f"DEBUG: 更新申请明细失败: request_id={request_id}, item_data={item_data}, error={str(e)}")
            logging.error(f"更新申请明细失败: request_id={request_id}, item_data={item_data}, error={str(e)}")
            raise BusinessException(f"更新申请明细失败: {str(e)}")



    
    def _return_items_to_cart(self, items: List[PurchaseRequestItem], department_id: int, user_id: int):
        """将申请物品返回购物车"""
        from app.services.purchase_cart_service import PurchaseCartService
        from app.schemas.purchase_cart import AddCartItemRequest
        from app.models.user import User
        
        cart_service = PurchaseCartService(self.db)
        
        # 获取用户对象
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            import logging
            logging.error(f"用户不存在: user_id={user_id}")
            return
        
        for item in items:
            # 创建购物车项目数据
            cart_item_data = AddCartItemRequest(
                item_id=item.item_id,
                spq_count=item.spq_count,
                notes=item.notes
            )
            
            try:
                # 添加到购物车
                cart_service.add_item_to_cart(department_id, cart_item_data, user)
            except Exception as e:
                # 记录错误但继续处理其他物品
                import logging
                logging.error(f"将物品返回购物车失败: item_id={item.item_id}, error={str(e)}")
                continue

    def get_request_item_history(self, request_id: int) -> List[Dict[str, Any]]:
        """获取指定申请单对应的物品申请历史统计（返回总数量：spq_quantity × spq_count）"""
        try:
            from sqlalchemy import func, extract
            
            # 获取申请单信息
            request = self.db.query(PurchaseRequest).filter(
                PurchaseRequest.id == request_id
            ).first()
            
            if not request:
                raise BusinessException("申请单不存在")
            
            # 获取申请单中的物品ID列表
            item_ids = [item.item_id for item in request.items] if request.items else []
            if not item_ids:
                return []
            
            # 获取过去12个月的历史数据，包括当前申请单数据
            end_date = datetime.now()
            
            # 精确计算起始日期：当前月的1号往前推12个月
            start_date = end_date.replace(day=1)  # 当前月1号
            for i in range(12):  # 往前推12个月
                # 获取上个月的最后一天
                if start_date.month == 1:
                    start_date = start_date.replace(year=start_date.year - 1, month=12)
                else:
                    start_date = start_date.replace(month=start_date.month - 1)
            
            # 查询历史数据，用OR关系包含当前申请单
            history_query = self.db.query(
                PurchaseRequestItem.item_id,
                func.to_char(PurchaseRequest.created_at, 'YYYY-MM').label('month'),
                func.sum(
                    func.cast(PurchaseRequestItem.spq_quantity, Float) * 
                    func.cast(PurchaseRequestItem.spq_count, Integer)
                ).label('count')
            ).join(
                PurchaseRequest, PurchaseRequestItem.request_id == PurchaseRequest.id
            ).filter(
                PurchaseRequest.department_id == request.department_id,
                PurchaseRequestItem.item_id.in_(item_ids),
                PurchaseRequest.created_at >= start_date,
                PurchaseRequest.created_at <= end_date,
                # 用OR关系：已批准/已执行的申请 OR 当前申请单
                or_(
                    PurchaseRequest.status.in_([
                        PurchaseRequestStatus.APPROVED,
                        PurchaseRequestStatus.EXECUTED
                    ]),
                    PurchaseRequest.id == request_id  # 包含当前申请单
                )
            ).group_by(
                PurchaseRequestItem.item_id,
                func.to_char(PurchaseRequest.created_at, 'YYYY-MM')
            )
            
            history_result = history_query.all()
            
            # 转换为字典格式，便于查找
            history_dict = {}
            for row in history_result:
                key = (row.item_id, row.month)
                history_dict[key] = row.count  # 这里的count实际上是总数量
            
            # 生成完整的月份列表（前12个月，包含当前申请单的月份）
            months = []
            current_month = request.created_at.strftime('%Y-%m')
            
            # 确保包含当前申请单的月份
            current_date = datetime.now()
            
            # 从起始月份开始，逐月生成到当前月
            temp_date = start_date
            while temp_date <= current_date:
                month_str = temp_date.strftime('%Y-%m')
                months.append(month_str)
                
                # 移动到下个月
                if temp_date.month == 12:
                    temp_date = temp_date.replace(year=temp_date.year + 1, month=1)
                else:
                    temp_date = temp_date.replace(month=temp_date.month + 1)
            
            # 如果当前申请单的月份不在列表中，添加进去
            if current_month not in months:
                months.append(current_month)
                months.sort()  # 按时间排序
            
            # 生成最终数据
            final_data = []
            for item_id in item_ids:
                for month in months:
                    key = (item_id, month)
                    count = history_dict.get(key, 0)
                    
                    final_data.append({
                        "item_id": item_id,
                        "month": month,
                        "count": count
                    })
            
            return final_data
            
        except Exception as e:
            logging.error(f"获取申请单物品历史失败: request_id={request_id}, error={str(e)}")
            raise BusinessException(f"获取申请历史失败: {str(e)}")

    def get_department_amount_history(self, department_ids: List[int]) -> List[Dict[str, Any]]:
        """获取部门采购金额历史统计（12个月）"""
        try:
            from sqlalchemy import func
            from datetime import datetime, timedelta
            
            # 获取过去12个月的历史数据
            end_date = datetime.now()
            start_date = datetime.now()
            start_date = start_date.replace(day=1) - timedelta(days=1)
            start_date = start_date.replace(day=1) - timedelta(days=11*30)
            
            # 查询各部门的历史采购金额
            history_query = self.db.query(
                PurchaseRequest.department_id,
                func.to_char(PurchaseRequest.created_at, 'YYYY-MM').label('month'),
                func.sum(
                    func.cast(PurchaseRequest.final_total, Float)
                ).label('total_amount')
            ).filter(
                PurchaseRequest.department_id.in_(department_ids),
                PurchaseRequest.created_at >= start_date,
                PurchaseRequest.created_at <= end_date,
                PurchaseRequest.status.in_([
                    PurchaseRequestStatus.APPROVED,
                    PurchaseRequestStatus.EXECUTED
                ])
            ).group_by(
                PurchaseRequest.department_id,
                func.to_char(PurchaseRequest.created_at, 'YYYY-MM')
            )
            
            history_result = history_query.all()
            
            # 转换为字典格式，便于查找
            history_dict = {}
            for row in history_result:
                key = (row.department_id, row.month)
                history_dict[key] = float(row.total_amount) if row.total_amount else 0.0
            
            # 生成完整的月份列表（前12个月 + 当前月 = 13个月）
            months = []
            current_month = datetime.now().strftime('%Y-%m')
            
            # 生成前12个月
            for i in range(11, -1, -1):
                date = datetime.now()
                date = date.replace(day=1) - timedelta(days=1)
                date = date.replace(day=1) - timedelta(days=i*30)
                month_str = date.strftime('%Y-%m')
                months.append(month_str)
            
            # 如果当前月不在列表中，添加进去
            if current_month not in months:
                months.append(current_month)
                months.sort()  # 按时间排序
            
            # 生成最终数据
            final_data = []
            for dept_id in department_ids:
                for month in months:
                    key = (dept_id, month)
                    amount = history_dict.get(key, 0.0)
                    
                    final_data.append({
                        "department_id": dept_id,
                        "month": month,
                        "amount": amount
                    })
            
            return final_data
            
        except Exception as e:
            logging.error(f"获取部门采购金额历史失败: department_ids={department_ids}, error={str(e)}")
            raise BusinessException(f"获取部门采购金额历史失败: {str(e)}")

    def get_supplier_amount_history(self, supplier_ids: List[int]) -> List[Dict[str, Any]]:
        """获取供应商采购金额历史统计（13个月）"""
        try:
            from sqlalchemy import func
            from datetime import datetime, timedelta
            from app.models.supplier import ItemSupplier
            from app.models.purchase import PurchaseRequest, PurchaseRequestItem
            from app.core.constants import PurchaseRequestStatus
            
            # 获取过去12个月的历史数据 + 当前月 = 13个月
            end_date = datetime.now()
            
            # 精确计算起始日期：当前月的1号往前推12个月
            start_date = end_date.replace(day=1)  # 当前月1号
            for i in range(12):  # 往前推12个月
                # 获取上个月的最后一天
                if start_date.month == 1:
                    start_date = start_date.replace(year=start_date.year - 1, month=12)
                else:
                    start_date = start_date.replace(month=start_date.month - 1)
            
            # 查询各供应商的历史采购金额
            history_query = self.db.query(
                ItemSupplier.supplier_id,
                func.to_char(PurchaseRequest.created_at, 'YYYY-MM').label('month'),
                func.sum(
                    func.cast(PurchaseRequestItem.final_total_price, Float)
                ).label('total_amount')
            ).join(
                PurchaseRequestItem, ItemSupplier.item_id == PurchaseRequestItem.item_id
            ).join(
                PurchaseRequest, PurchaseRequestItem.request_id == PurchaseRequest.id
            ).filter(
                ItemSupplier.supplier_id.in_(supplier_ids),
                ItemSupplier.status == "active",
                ItemSupplier.priority == 0,  # 只统计首选供应商
                PurchaseRequest.created_at >= start_date,
                PurchaseRequest.created_at <= end_date,
                PurchaseRequest.status.in_([
                    PurchaseRequestStatus.APPROVED,
                    PurchaseRequestStatus.EXECUTED
                ])
            ).group_by(
                ItemSupplier.supplier_id,
                func.to_char(PurchaseRequest.created_at, 'YYYY-MM')
            )
            
            history_result = history_query.all()
            
            # 转换为字典格式，便于查找
            history_dict = {}
            for row in history_result:
                key = (row.supplier_id, row.month)
                history_dict[key] = float(row.total_amount) if row.total_amount else 0.0
            
            # 生成完整的月份列表（前12个月 + 当前月 = 13个月）
            months = []
            current_month = datetime.now().strftime('%Y-%m')
            
            # 生成前12个月
            for i in range(11, -1, -1):
                date = datetime.now()
                date = date.replace(day=1) - timedelta(days=1)
                date = date.replace(day=1) - timedelta(days=i*30)
                month_str = date.strftime('%Y-%m')
                months.append(month_str)
            
            # 如果当前月不在列表中，添加进去
            if current_month not in months:
                months.append(current_month)
                months.sort()  # 按时间排序
            
            # 生成最终数据
            final_data = []
            for supplier_id in supplier_ids:
                for month in months:
                    key = (supplier_id, month)
                    amount = history_dict.get(key, 0.0)
                    
                    final_data.append({
                        "supplier_id": supplier_id,
                        "month": month,
                        "amount": amount
                    })
            
            return final_data
            
        except Exception as e:
            logging.error(f"获取供应商采购金额历史失败: supplier_ids={supplier_ids}, error={str(e)}")
            raise BusinessException(f"获取供应商采购金额历史失败: {str(e)}")

    def get_department_item_history(
        self, 
        department_id: int, 
        item_ids: List[int], 
        start_date: datetime, 
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        """获取部门物品申请历史统计"""
        try:
            from sqlalchemy import func, extract
            
            query = self.db.query(
                PurchaseRequestItem.item_id,
                func.to_char(PurchaseRequest.created_at, 'YYYY-MM').label('month'),
                func.count(PurchaseRequestItem.id).label('count')
            ).join(
                PurchaseRequest, PurchaseRequestItem.request_id == PurchaseRequest.id
            ).filter(
                PurchaseRequest.department_id == department_id,
                PurchaseRequestItem.item_id.in_(item_ids),
                PurchaseRequest.created_at >= start_date,
                PurchaseRequest.created_at <= end_date,
                PurchaseRequest.status.in_([
                    PurchaseRequestStatus.APPROVED,
                    PurchaseRequestStatus.EXECUTED
                ])
            ).group_by(
                PurchaseRequestItem.item_id,
                func.to_char(PurchaseRequest.created_at, 'YYYY-MM')
            ).order_by(
                func.to_char(PurchaseRequest.created_at, 'YYYY-MM'),
                PurchaseRequestItem.item_id
            )
            
            result = query.all()
            
            history_data = []
            for row in result:
                history_data.append({
                    "item_id": row.item_id,
                    "month": row.month,
                    "count": row.count
                })
            
            return history_data
            
        except Exception as e:
            logging.error(f"获取部门物品申请历史失败: department_id={department_id}, error={str(e)}")
            raise BusinessException(f"获取申请历史失败: {str(e)}")

    def get_department_item_history_with_current(
        self, 
        department_id: int, 
        item_ids: List[int], 
        start_date: datetime, 
        end_date: datetime,
        current_request_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """获取部门物品申请历史统计，包含当前申请单数据"""
        try:
            from sqlalchemy import func, extract
            from app.models.purchase_request import PurchaseRequestItem
            
            # 获取历史数据
            history_query = self.db.query(
                PurchaseRequestItem.item_id,
                func.to_char(PurchaseRequest.created_at, 'YYYY-MM').label('month'),
                func.count(PurchaseRequestItem.id).label('count')
            ).join(
                PurchaseRequest, PurchaseRequestItem.request_id == PurchaseRequest.id
            ).filter(
                PurchaseRequest.department_id == department_id,
                PurchaseRequestItem.item_id.in_(item_ids),
                PurchaseRequest.created_at >= start_date,
                PurchaseRequest.created_at <= end_date,
                PurchaseRequest.status.in_([
                    PurchaseRequestStatus.APPROVED,
                    PurchaseRequestStatus.EXECUTED
                ])
            ).group_by(
                PurchaseRequestItem.item_id,
                func.to_char(PurchaseRequest.created_at, 'YYYY-MM')
            )
            
            history_result = history_query.all()
            
            # 转换为字典格式，便于查找
            history_dict = {}
            for row in history_result:
                key = (row.item_id, row.month)
                history_dict[key] = row.count
            
            # 获取当前申请单数据
            current_data = {}
            if current_request_id:
                current_request = self.db.query(PurchaseRequest).filter(
                    PurchaseRequest.id == current_request_id
                ).first()
                
                if current_request and current_request.items:
                    current_month = current_request.created_at.strftime('%Y-%m')
                    for item in current_request.items:
                        if item.item_id in item_ids:
                            key = (item.item_id, current_month)
                            if key in current_data:
                                current_data[key] += item.quantity
                            else:
                                current_data[key] = item.quantity
            
            # 生成完整的月份列表（前6个月）
            months = []
            for i in range(5, -1, -1):
                date = datetime.now()
                date = date.replace(day=1) - timedelta(days=1)
                date = date.replace(day=1) - timedelta(days=i*30)
                months.append(date.strftime('%Y-%m'))
            
            # 合并数据
            final_data = []
            for item_id in item_ids:
                for month in months:
                    key = (item_id, month)
                    historical_count = history_dict.get(key, 0)
                    current_count = current_data.get(key, 0)
                    total_count = historical_count + current_count
                    
                    final_data.append({
                        "item_id": item_id,
                        "month": month,
                        "count": total_count
                    })
            
            return final_data
            
        except Exception as e:
            logging.error(f"获取部门物品申请历史（含当前数据）失败: department_id={department_id}, error={str(e)}")
            raise BusinessException(f"获取申请历史失败: {str(e)}")

    def get_requests_summary_analysis(self, request_ids: List[int], current_user: User = None) -> Dict[str, Any]:
        """获取采购申请汇总分析
        
        Args:
            request_ids: 要汇总的申请ID列表
            current_user: 当前用户
            
        Returns:
            Dict: 汇总分析结果
        """
        try:
            from app.models.item import Item
            from app.models.supplier import ItemSupplier, Supplier
            from decimal import Decimal
            
            # 验证申请ID列表
            if not request_ids:
                raise BusinessException("申请ID列表不能为空")
            
            if len(request_ids) > 1000:
                raise BusinessException("单次汇总的申请数量不能超过1000个")
            
            # 获取申请数据
            requests = self.db.query(PurchaseRequest).filter(
                PurchaseRequest.id.in_(request_ids)
            ).all()
            
            if not requests:
                raise BusinessException("没有找到有效的采购申请")
            
            # 验证申请状态（只能汇总特定状态的申请）
            valid_statuses = ["approved", "under_review", "under_principle_approval", "under_final_approval"]
            invalid_requests = [r for r in requests if r.status not in valid_statuses]
            if invalid_requests:
                invalid_nos = [r.request_no for r in invalid_requests]
                raise BusinessException(f"以下申请状态不允许汇总: {', '.join(invalid_nos)}")
            
            # 获取所有申请明细
            all_items = self.db.query(PurchaseRequestItem).filter(
                PurchaseRequestItem.request_id.in_(request_ids)
            ).all()
            
            # 基础统计信息
            total_requests = len(requests)
            department_ids = set(r.department_id for r in requests)
            total_departments = len(department_ids)
            
            # 状态分布统计
            status_distribution = {}
            for request in requests:
                status = request.status
                status_distribution[status] = status_distribution.get(status, 0) + 1
            
            # 物品汇总分析
            item_summaries = {}
            for item in all_items:
                item_id = item.item_id
                
                if item_id not in item_summaries:
                    # 获取物品基本信息
                    item_info = self.db.query(Item).filter(Item.id == item_id).first()
                    category_name = None
                    image_url = None
                    preferred_supplier_id = None
                    preferred_supplier_name = None
                    
                    if item_info and item_info.category_id:
                        # 获取分类信息
                        from app.models.item import ItemCategory
                        category = self.db.query(ItemCategory).filter(ItemCategory.id == item_info.category_id).first()
                        category_name = category.name if category else None
                    
                    # 获取物品图片URL
                    if item_info and hasattr(item_info, 'image_url') and item_info.image_url:
                        image_url = item_info.image_url
                    
                    # 获取物品的最优先供应商
                    from app.models.supplier import ItemSupplier, Supplier
                    preferred_supplier = self.db.query(ItemSupplier, Supplier).join(
                        Supplier, ItemSupplier.supplier_id == Supplier.id
                    ).filter(
                        ItemSupplier.item_id == item_id,
                        ItemSupplier.status == "active",
                        ItemSupplier.priority == 0
                    ).first()
                    
                    if preferred_supplier:
                        preferred_supplier_id = preferred_supplier.ItemSupplier.supplier_id
                        preferred_supplier_name = preferred_supplier.Supplier.name_cn or preferred_supplier.Supplier.name_en or f"供应商{preferred_supplier_id}"
                    
                    item_summaries[item_id] = {
                        "item_id": item_id,
                        "item_code": item.item_code,
                        "item_name": item.item_name,
                        "category_name": category_name,
                        "image_url": image_url,
                        "preferred_supplier_id": preferred_supplier_id,
                        "preferred_supplier_name": preferred_supplier_name,
                        "total_spq_quantity": Decimal("0"),
                        "total_spq_count": 0,
                        "spq_unit": item.spq_unit,
                        "total_amount": Decimal("0"),
                        "request_count": 0,
                        "requests": set()
                    }
                
                # 累加数量
                item_summaries[item_id]["total_spq_quantity"] += item.spq_quantity
                item_summaries[item_id]["total_spq_count"] += item.spq_count
                
                # 保存单个物品的SPQ值（不累加）
                if "single_spq_quantity" not in item_summaries[item_id]:
                    item_summaries[item_id]["single_spq_quantity"] = item.spq_quantity
                    item_summaries[item_id]["single_spq_count"] = item.spq_count
                
                # 计算金额（优先使用最终价格，否则尝试实时计算）
                item_total = Decimal("0")
                if item.final_total_price:
                    item_total = Decimal(str(item.final_total_price))
                else:
                    # 如果没有最终价格信息，尝试实时计算
                    item_total = self._calculate_item_current_price(item_id, item.spq_count)
                
                item_summaries[item_id]["total_amount"] += item_total
                item_summaries[item_id]["requests"].add(str(item.request_id))
            
            # 计算平均价格和申请数量
            for item_id, summary in item_summaries.items():
                summary["request_count"] = len(summary["requests"])
                # 保存申请单ID列表
                summary["request_ids"] = list(summary["requests"])
                if summary["total_spq_count"] > 0:
                    summary["average_price"] = summary["total_amount"] / summary["total_spq_count"]
                else:
                    summary["average_price"] = Decimal("0")
                # 移除临时的requests集合
                del summary["requests"]
            
            # 部门汇总分析
            department_summaries = {}
            for request in requests:
                dept_id = request.department_id
                
                if dept_id not in department_summaries:
                    # 获取部门信息
                    from app.models.user import Department
                    dept = self.db.query(Department).filter(Department.id == dept_id).first()
                    dept_name = dept.name if dept else f"部门{dept_id}"
                    
                    department_summaries[dept_id] = {
                        "department_id": dept_id,
                        "department_name": dept_name,
                        "request_count": 0,
                        "total_amount": Decimal("0")
                    }
                
                department_summaries[dept_id]["request_count"] += 1
                
                # 计算部门总金额
                dept_amount = Decimal("0")
                if request.final_total:
                    dept_amount = Decimal(str(request.final_total))
                else:
                    # 计算该申请的总金额
                    request_items = [item for item in all_items if item.request_id == request.id]
                    for item in request_items:
                        if item.final_total_price:
                            dept_amount += Decimal(str(item.final_total_price))
                        else:
                            dept_amount += self._calculate_item_current_price(item.item_id, item.spq_count)
                
                department_summaries[dept_id]["total_amount"] += dept_amount
            
            # 计算总金额和部门占比
            total_amount = sum(dept["total_amount"] for dept in department_summaries.values())
            for dept_summary in department_summaries.values():
                if total_amount > 0:
                    dept_summary["percentage"] = (dept_summary["total_amount"] / total_amount * 100).quantize(Decimal("0.01"))
                else:
                    dept_summary["percentage"] = Decimal("0")
            
            # 准备申请单列表数据
            requests_list = []
            for request in requests:
                # 获取部门信息
                dept = self.db.query(Department).filter(Department.id == request.department_id).first()
                dept_name = dept.name if dept else f"部门{request.department_id}"
                
                # 获取申请人信息
                submitter = self.db.query(User).filter(User.id == request.submitter_id).first()
                submitter_name = submitter.display_name or submitter.full_name if submitter else f"用户{request.submitter_id}"
                
                requests_list.append({
                    "id": request.id,
                    "request_id": str(request.id),
                    "request_no": request.request_no,
                    "department_name": dept_name,
                    "submitter_name": submitter_name,
                    "status": request.status,
                    "final_total": request.final_total if request.final_total else 0,
                    "created_at": request.created_at.isoformat() if request.created_at else None
                })
            
            # 供应商汇总分析
            supplier_summaries = {}
            for item in all_items:
                # 获取物品的最优先供应商
                preferred_supplier = self.db.query(ItemSupplier, Supplier).join(
                    Supplier, ItemSupplier.supplier_id == Supplier.id
                ).filter(
                    ItemSupplier.item_id == item.item_id,
                    ItemSupplier.status == "active",
                    ItemSupplier.priority == 0
                ).first()
                
                if preferred_supplier:
                    supplier_id = preferred_supplier.ItemSupplier.supplier_id
                    supplier_name = preferred_supplier.Supplier.name_cn or preferred_supplier.Supplier.name_en or f"供应商{supplier_id}"
                    
                    if supplier_id not in supplier_summaries:
                        supplier_summaries[supplier_id] = {
                            "supplier_id": supplier_id,
                            "supplier_name": supplier_name,
                            "item_count": 0,
                            "total_amount": Decimal("0"),
                            "items": set()
                        }
                    
                    supplier_summaries[supplier_id]["items"].add(item.item_id)
                    
                    # 计算供应商相关金额
                    item_amount = Decimal("0")
                    if item.final_total_price:
                        item_amount = Decimal(str(item.final_total_price))
                    else:
                        item_amount = self._calculate_item_current_price(item.item_id, item.spq_count)
                    
                    supplier_summaries[supplier_id]["total_amount"] += item_amount
            
            # 计算供应商平均价格、物品数量和占比
            for supplier_summary in supplier_summaries.values():
                supplier_summary["item_count"] = len(supplier_summary["items"])
                if supplier_summary["item_count"] > 0:
                    supplier_summary["average_price"] = supplier_summary["total_amount"] / supplier_summary["item_count"]
                else:
                    supplier_summary["average_price"] = Decimal("0")
                # 移除临时的items集合
                del supplier_summary["items"]
            
            # 计算供应商占比
            total_supplier_amount = sum(supplier["total_amount"] for supplier in supplier_summaries.values())
            for supplier_summary in supplier_summaries.values():
                if total_supplier_amount > 0:
                    supplier_summary["percentage"] = (supplier_summary["total_amount"] / total_supplier_amount * 100).quantize(Decimal("0.01"))
                else:
                    supplier_summary["percentage"] = Decimal("0")
            
            # 返回汇总分析结果
            return {
                "total_requests": len(requests),
                "total_departments": len(department_summaries),
                "total_amount": total_amount,
                "analysis_time": datetime.now().isoformat(),
                "item_summaries": list(item_summaries.values()),
                "department_summaries": list(department_summaries.values()),
                "supplier_summaries": list(supplier_summaries.values()),
                "status_distribution": status_distribution,
                "requests": requests_list
            }
            
        except Exception as e:
            logging.error(f"获取采购申请汇总分析失败: request_ids={request_ids}, error={str(e)}")
            raise BusinessException(f"汇总分析失败: {str(e)}")
    
    def _calculate_item_current_price(self, item_id: int, spq_count: int) -> Decimal:
        """计算物品当前价格
        
        Args:
            item_id: 物品ID
            spq_count: SPQ个数
            
        Returns:
            Decimal: 当前价格
        """
        try:
            from app.services.item_price_service import ItemPriceService
            
            # 使用ItemPriceService获取价格信息
            item_price_service = ItemPriceService(self.db)
            price_info = item_price_service.get_item_price_info(
                item_id=item_id,
                quantity=spq_count
            )
            
            # 返回USD价格，如果没有则返回原币价格
            if price_info["unit_price"]["usd_amount"]:
                return Decimal(str(price_info["unit_price"]["usd_amount"])) * spq_count
            else:
                return Decimal(str(price_info["unit_price"]["amount"])) * spq_count
                
        except Exception as e:
            logging.error(f"计算物品当前价格失败: item_id={item_id}, spq_count={spq_count}, error={str(e)}")
            return Decimal("0")

    def get_summary_analysis_history(self, request_ids: List[int]) -> Dict[str, Any]:
        """获取汇总分析的历史数据
        
        Args:
            request_ids: 要汇总的申请ID列表
            
        Returns:
            Dict: 历史数据，包括物品历史、部门历史、供应商历史
        """
        try:
            from sqlalchemy import func, or_
            from datetime import datetime, timedelta
            
            # 获取过去12个月 + 当前月的时间范围（共13个月）
            end_date = datetime.now()
            
            # 精确计算起始日期：当前月的1号往前推12个月
            start_date = end_date.replace(day=1)  # 当前月1号
            for i in range(12):  # 往前推12个月
                # 获取上个月的最后一天
                if start_date.month == 1:
                    start_date = start_date.replace(year=start_date.year - 1, month=12)
                else:
                    start_date = start_date.replace(month=start_date.month - 1)
            
            # 获取申请单信息，用于确定物品、部门、供应商范围
            requests = self.db.query(PurchaseRequest).filter(
                PurchaseRequest.id.in_(request_ids)
            ).all()
            
            if not requests:
                return {
                    "item_history": [],
                    "department_history": [],
                    "supplier_history": []
                }
            
            # 确定部门范围（基于选中的申请单）
            department_ids = list(set(r.department_id for r in requests))
            
            # 确定物品范围（基于选中的申请单）
            all_items = self.db.query(PurchaseRequestItem).filter(
                PurchaseRequestItem.request_id.in_(request_ids)
            ).all()
            item_ids = list(set(item.item_id for item in all_items))
            
            # 确定供应商范围（基于选中申请单中物品的最优先供应商）
            supplier_ids = set()
            for item in all_items:
                from app.models.supplier import ItemSupplier
                preferred_supplier = self.db.query(ItemSupplier).filter(
                    ItemSupplier.item_id == item.item_id,
                    ItemSupplier.status == "active",
                    ItemSupplier.priority == 0
                ).first()
                if preferred_supplier:
                    supplier_ids.add(preferred_supplier.supplier_id)
            supplier_ids = list(supplier_ids)
            
            # 1. 物品历史数据 - 基于选中申请单涉及的物品的所有历史采购量
            item_history = []
            if item_ids:
                # 查询物品历史数据，包含所有相关部门的历史数据
                item_history_query = self.db.query(
                    PurchaseRequestItem.item_id,
                    func.to_char(PurchaseRequest.created_at, 'YYYY-MM').label('month'),
                    func.sum(
                        func.cast(PurchaseRequestItem.spq_quantity, Float) * 
                        func.cast(PurchaseRequestItem.spq_count, Integer)
                    ).label('count')
                ).join(
                    PurchaseRequest, PurchaseRequestItem.request_id == PurchaseRequest.id
                ).filter(
                    PurchaseRequest.department_id.in_(department_ids),  # 基于选中申请单涉及的部门
                    PurchaseRequestItem.item_id.in_(item_ids),  # 基于选中申请单涉及的物品
                    PurchaseRequest.created_at >= start_date,
                    PurchaseRequest.created_at <= end_date,
                    # 包含已批准/已执行的申请，以及当前选中的申请单
                    or_(
                        PurchaseRequest.status.in_([
                            PurchaseRequestStatus.APPROVED,
                            PurchaseRequestStatus.EXECUTED
                        ]),
                        PurchaseRequest.id.in_(request_ids)  # 包含当前选中的申请单
                    )
                ).group_by(
                    PurchaseRequestItem.item_id,
                    func.to_char(PurchaseRequest.created_at, 'YYYY-MM')
                )
                
                item_history_result = item_history_query.all()
                
                # 转换为字典格式
                item_history_dict = {}
                for row in item_history_result:
                    key = (row.item_id, row.month)
                    item_history_dict[key] = row.count
                
                # 生成完整的月份列表（过去12个月 + 当前月 = 13个月）
                months = []
                current_date = datetime.now()
                
                # 从起始月份开始，逐月生成到当前月
                temp_date = start_date
                while temp_date <= current_date:
                    month_str = temp_date.strftime('%Y-%m')
                    months.append(month_str)
                    
                    # 移动到下个月
                    if temp_date.month == 12:
                        temp_date = temp_date.replace(year=temp_date.year + 1, month=1)
                    else:
                        temp_date = temp_date.replace(month=temp_date.month + 1)
                
                # 生成最终数据
                for item_id in item_ids:
                    for month in months:
                        key = (item_id, month)
                        count = item_history_dict.get(key, 0)
                        
                        item_history.append({
                            "item_id": item_id,
                            "month": month,
                            "count": count
                        })
            
            # 2. 部门历史数据 - 基于选中申请单涉及部门的历史采购数据
            department_history = []
            if department_ids:
                # 查询部门历史数据
                dept_history_query = self.db.query(
                    PurchaseRequest.department_id,
                    func.to_char(PurchaseRequest.created_at, 'YYYY-MM').label('month'),
                    func.sum(
                        func.cast(PurchaseRequest.final_total, Float)
                    ).label('amount')
                ).filter(
                    PurchaseRequest.department_id.in_(department_ids),  # 基于选中申请单涉及的部门
                    PurchaseRequest.created_at >= start_date,
                    PurchaseRequest.created_at <= end_date,
                    # 包含已批准/已执行的申请，以及当前选中的申请单
                    or_(
                        PurchaseRequest.status.in_([
                            PurchaseRequestStatus.APPROVED,
                            PurchaseRequestStatus.EXECUTED
                        ]),
                        PurchaseRequest.id.in_(request_ids)  # 包含当前选中的申请单
                    )
                ).group_by(
                    PurchaseRequest.department_id,
                    func.to_char(PurchaseRequest.created_at, 'YYYY-MM')
                )
                
                dept_history_result = dept_history_query.all()
                
                # 转换为字典格式
                dept_history_dict = {}
                for row in dept_history_result:
                    key = (row.department_id, row.month)
                    dept_history_dict[key] = row.amount
                
                # 生成最终数据
                for dept_id in department_ids:
                    for month in months:
                        key = (dept_id, month)
                        amount = dept_history_dict.get(key, 0)
                        
                        department_history.append({
                            "department_id": dept_id,
                            "month": month,
                            "amount": amount
                        })
            
            # 3. 供应商历史数据 - 基于选中申请单涉及供应商的历史采购金额趋势
            supplier_history = []
            if supplier_ids:
                # 查询供应商历史数据
                supplier_history_query = self.db.query(
                    ItemSupplier.supplier_id,
                    func.to_char(PurchaseRequest.created_at, 'YYYY-MM').label('month'),
                    func.sum(
                        func.cast(PurchaseRequestItem.final_total_price, Float)
                    ).label('amount')
                ).join(
                    PurchaseRequestItem, ItemSupplier.item_id == PurchaseRequestItem.item_id
                ).join(
                    PurchaseRequest, PurchaseRequestItem.request_id == PurchaseRequest.id
                ).filter(
                    ItemSupplier.supplier_id.in_(supplier_ids),  # 基于选中申请单涉及的供应商
                    PurchaseRequest.created_at >= start_date,
                    PurchaseRequest.created_at <= end_date,
                    # 包含已批准/已执行的申请，以及当前选中的申请单
                    or_(
                        PurchaseRequest.status.in_([
                            PurchaseRequestStatus.APPROVED,
                            PurchaseRequestStatus.EXECUTED
                        ]),
                        PurchaseRequest.id.in_(request_ids)  # 包含当前选中的申请单
                    )
                ).group_by(
                    ItemSupplier.supplier_id,
                    func.to_char(PurchaseRequest.created_at, 'YYYY-MM')
                )
                
                supplier_history_result = supplier_history_query.all()
                
                # 转换为字典格式
                supplier_history_dict = {}
                for row in supplier_history_result:
                    key = (row.supplier_id, row.month)
                    supplier_history_dict[key] = row.amount
                
                # 生成最终数据
                for supplier_id in supplier_ids:
                    for month in months:
                        key = (supplier_id, month)
                        amount = supplier_history_dict.get(key, 0)
                        
                        supplier_history.append({
                            "supplier_id": supplier_id,
                            "month": month,
                            "amount": amount
                        })
            
            return {
                "item_history": item_history,
                "department_history": department_history,
                "supplier_history": supplier_history
            }
            
        except Exception as e:
            logging.error(f"获取汇总分析历史数据失败: request_ids={request_ids}, error={str(e)}")
            raise BusinessException(f"获取历史数据失败: {str(e)}")

    def manipulate_request_time(self, request_id: int, time_data: Dict[str, datetime]) -> PurchaseRequest:
        """修改申请单时间（仅限super_admin角色）"""
        # 获取申请单
        request = self.get_request_by_id(request_id)
        if not request:
            raise ResourceNotFoundException(f"申请单不存在: {request_id}")
        
        # 更新指定时间字段
        for field, new_time in time_data.items():
            if hasattr(request, field):
                setattr(request, field, new_time)
            else:
                raise BusinessException(f"不支持的时间字段: {field}")
        
        # 保存更改
        self.db.commit()
        # Remove the refresh call as it's not needed after commit and causes errors
        
        return request

    def _create_request_item_with_details(self, item: PurchaseRequestItem) -> RequestItemWithDetails:
        """创建包含详细信息的申请明细对象"""
        from app.models.item import Item
        from app.models.supplier import ItemSupplier, Supplier
        from app.services.item_price_service import ItemPriceService
        from sqlalchemy.orm import joinedload
        
        # 获取物品基本信息
        db_item = self.db.query(Item).filter(Item.id == item.item_id).first()
        item_image_url = None
        if db_item and hasattr(db_item, 'image_url'):
            item_image_url = db_item.image_url
        
        # 获取供应商信息
        item_suppliers = self.db.query(ItemSupplier).options(
            joinedload(ItemSupplier.supplier)
        ).filter(
            ItemSupplier.item_id == item.item_id,
            ItemSupplier.status == "active"
        ).order_by(ItemSupplier.priority.asc()).all()
        
        preferred_supplier = None
        available_suppliers = []
        
        if item_suppliers:
            # 首选供应商
            preferred = item_suppliers[0]
            preferred_supplier = {
                'id': preferred.id,
                'supplier_id': preferred.supplier_id,
                'item_id': preferred.item_id,
                'priority': preferred.priority,
                'status': preferred.status,
                'supplier_name': preferred.supplier.name_cn or preferred.supplier.name_en or f'供应商{preferred.supplier_id}'
            }
            
            # 所有可用供应商
            for supplier in item_suppliers:
                available_suppliers.append({
                    'id': supplier.id,
                    'supplier_id': supplier.supplier_id,
                    'item_id': supplier.item_id,
                    'priority': supplier.priority,
                    'status': supplier.status,
                    'supplier_name': supplier.supplier.name_cn or supplier.supplier.name_en or f'供应商{supplier.supplier_id}'
                })
        
        # 获取价格信息
        estimated_unit_price = 0.0
        estimated_total_price = 0.0
        exchange_rate_info = None
        
        try:
            item_price_service = ItemPriceService(self.db)
            total_quantity = float(item.spq_quantity) * item.spq_count
            
            price_info = item_price_service.get_item_price_info(
                item_id=item.item_id,
                quantity=int(total_quantity)
            )
            
            if price_info and price_info["unit_price"]["usd_amount"]:
                estimated_unit_price = price_info["unit_price"]["usd_amount"]
                estimated_total_price = price_info["total_price"]["usd_amount"]
                
                # 始终创建汇率信息，包含供应商的原始货币和价格
                # 对于USD物品，original_unit_price和usd_unit_price相同
                if price_info["unit_price"]["currency"] != "USD":
                    # 非USD货币，需要汇率转换
                    # 获取汇率类型和有效月份（从汇率服务获取）
                    rate_type = "current_month"  # 默认值
                    effective_month = datetime.now().strftime("%Y-%m")  # 默认值
                    
                    # 如果有汇率信息，尝试获取更详细的信息
                    if price_info.get("exchange_rate"):
                        # 这里可以扩展为从汇率服务获取更多信息
                        pass
                    
                    exchange_rate_info = ExchangeRateInfo(
                        currency_code=price_info["unit_price"]["currency"],
                        original_unit_price=price_info["unit_price"]["amount"],
                        original_total_price=price_info["total_price"]["amount"],
                        exchange_rate=price_info.get("exchange_rate", 1.0),
                        usd_unit_price=estimated_unit_price,
                        usd_total_price=estimated_total_price,
                        rate_type=rate_type,
                        effective_month=effective_month,
                        is_valid=True
                    )
                else:
                    # USD货币，不需要汇率转换
                    exchange_rate_info = ExchangeRateInfo(
                        currency_code="USD",
                        original_unit_price=price_info["unit_price"]["amount"],
                        original_total_price=price_info["total_price"]["amount"],
                        exchange_rate=1.0,  # USD对USD汇率为1
                        usd_unit_price=estimated_unit_price,
                        usd_total_price=estimated_total_price,
                        rate_type="current_month",
                        effective_month=datetime.now().strftime("%Y-%m"),
                        is_valid=True
                    )
                    
        except Exception as e:
            logging.warning(f"获取申请明细价格信息失败: item_id={item.id}, error={str(e)}")
        
        # 创建详细信息对象
        return RequestItemWithDetails(
            id=item.id,
            request_id=item.request_id,
            item_id=item.item_id,
            item_code=item.item_code,
            item_name=item.item_name,
            spq_quantity=item.spq_quantity,
            spq_count=item.spq_count,
            spq_unit=item.spq_unit,
            final_unit_price=item.final_unit_price,
            final_total_price=item.final_total_price,
            final_supplier_id=item.final_supplier_id,
            price_locked_at=item.price_locked_at,
            estimated_unit_price=estimated_unit_price,
            estimated_total_price=estimated_total_price,
            item_image_url=item_image_url,
            exchange_rate_info=exchange_rate_info,
            preferred_supplier=preferred_supplier,
            available_suppliers=available_suppliers,
            notes=item.notes,
            created_at=item.created_at,
            updated_at=item.updated_at
        )

    def lock_exchange_rates_on_submit(self, request_id: int) -> None:
        """提交时锁定汇率（第一阶段锁定）"""
        from app.models.supplier import ItemSupplier, SupplierPrice
        from datetime import datetime, date
        
        request_items = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.request_id == request_id
        ).all()
        
        if not request_items:
            return
        
        exchange_rate_service = ExchangeRateService(self.db)
        current_date = datetime.now().date()
        current_month = date(current_date.year, current_date.month, 1)
        
        for item in request_items:
            # 标记是否找到有效价格
            price_found = False
            
            # 获取物品的供应商价格信息
            item_suppliers = self.db.query(ItemSupplier).filter(
                ItemSupplier.item_id == item.item_id,
                ItemSupplier.status == "active"
            ).all()
            
            for item_supplier in item_suppliers:
                # 查找有效的价格记录
                current_time = datetime.now()
                prices = self.db.query(SupplierPrice).filter(
                    and_(
                        SupplierPrice.item_supplier_id == item_supplier.id,
                        SupplierPrice.status == "active",
                        SupplierPrice.valid_from <= current_time,
                        or_(
                            SupplierPrice.valid_to.is_(None),
                            SupplierPrice.valid_to > current_time
                        )
                    )
                ).all()
                
                for price in prices:
                    price_found = True
                    if price.currency_code != "USD":
                        # 获取当前汇率
                        exchange_rate = exchange_rate_service.get_latest_exchange_rate(
                            price.currency_code, current_date
                        )
                        
                        if exchange_rate:
                            # 锁定汇率信息
                            item.locked_currency_code = price.currency_code
                            item.locked_exchange_rate = exchange_rate.rate
                            item.locked_exchange_rate_month = exchange_rate.effective_month
                            item.locked_exchange_rate_stage = "submitted"
                            item.locked_exchange_rate_at = datetime.now()
                            
                            logging.info(f"物品 {item.item_name} 提交时汇率锁定: 货币={price.currency_code}, 汇率={exchange_rate.rate}, 生效月份={exchange_rate.effective_month}")
                        else:
                            # 没有找到有效汇率，记录警告
                            logging.warning(f"物品 {item.item_name} 货币 {price.currency_code} 没有找到有效汇率")
                            item.locked_currency_code = price.currency_code
                            item.locked_exchange_rate = None
                            item.locked_exchange_rate_month = None
                            item.locked_exchange_rate_stage = "submitted"
                            item.locked_exchange_rate_at = datetime.now()
                    else:
                        # USD货币，设置默认值
                        item.locked_currency_code = "USD"
                        item.locked_exchange_rate = 1.0  # USD对USD汇率为1
                        item.locked_exchange_rate_month = current_month
                        item.locked_exchange_rate_stage = "submitted"
                        item.locked_exchange_rate_at = datetime.now()
                        
                        logging.info(f"物品 {item.item_name} 提交时汇率锁定: 货币=USD, 汇率=1.0")
                    
                    break  # 只锁定第一个有效价格
                
                if price_found:
                    break
            
            # 如果没有找到任何价格，设置默认值
            if not price_found:
                logging.warning(f"物品 {item.item_name} 没有找到有效的供应商价格")
                item.locked_currency_code = "USD"  # 默认USD
                item.locked_exchange_rate = 1.0
                item.locked_exchange_rate_month = current_month
                item.locked_exchange_rate_stage = "submitted"
                item.locked_exchange_rate_at = datetime.now()
        
        self.db.commit()
        logging.info(f"申请 {request_id} 提交时汇率锁定完成")

    def clear_exchange_rate_locks_on_review(self, request_id: int) -> None:
        """复核时清除汇率锁定"""
        self.clear_locked_currency_fields(request_id, "复核时")

    def clear_locked_currency_fields(self, request_id: int, context: str = "") -> None:
        """清除采购申请物品的锁定货币字段"""
        request_items = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.request_id == request_id
        ).all()
        
        for item in request_items:
            # 清除锁定的汇率信息
            item.locked_currency_code = None
            item.locked_exchange_rate = None
            item.locked_exchange_rate_month = None
            item.locked_exchange_rate_stage = None
            item.locked_exchange_rate_at = None
        
        self.db.commit()
        logging.info(f"申请 {request_id} {context}汇率锁定已清除")

    def lock_exchange_rates_on_principle_approval(self, request_id: int) -> None:
        """主管审批时锁定汇率（第二阶段锁定）"""
        from app.models.supplier import ItemSupplier, SupplierPrice
        from datetime import datetime, date
        
        request_items = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.request_id == request_id
        ).all()
        
        if not request_items:
            return
        
        exchange_rate_service = ExchangeRateService(self.db)
        current_date = datetime.now().date()
        current_month = date(current_date.year, current_date.month, 1)
        
        for item in request_items:
            # 标记是否找到有效价格
            price_found = False
            
            # 获取物品的供应商价格信息
            item_suppliers = self.db.query(ItemSupplier).filter(
                ItemSupplier.item_id == item.item_id,
                ItemSupplier.status == "active"
            ).all()
            
            for item_supplier in item_suppliers:
                # 查找有效的价格记录
                current_time = datetime.now()
                prices = self.db.query(SupplierPrice).filter(
                    and_(
                        SupplierPrice.item_supplier_id == item_supplier.id,
                        SupplierPrice.status == "active",
                        SupplierPrice.valid_from <= current_time,
                        or_(
                            SupplierPrice.valid_to.is_(None),
                            SupplierPrice.valid_to > current_time
                        )
                    )
                ).all()
                
                for price in prices:
                    price_found = True
                    if price.currency_code != "USD":
                        # 获取当前汇率
                        exchange_rate = exchange_rate_service.get_latest_exchange_rate(
                            price.currency_code, current_date
                        )
                        
                        if exchange_rate:
                            # 锁定汇率信息
                            item.locked_currency_code = price.currency_code
                            item.locked_exchange_rate = exchange_rate.rate
                            item.locked_exchange_rate_month = exchange_rate.effective_month
                            item.locked_exchange_rate_stage = "principle_approved"
                            item.locked_exchange_rate_at = datetime.now()
                            
                            logging.info(f"物品 {item.item_name} 主管审批时汇率锁定: 货币={price.currency_code}, 汇率={exchange_rate.rate}, 生效月份={exchange_rate.effective_month}")
                        else:
                            # 没有找到有效汇率，记录警告
                            logging.warning(f"物品 {item.item_name} 货币 {price.currency_code} 没有找到有效汇率")
                            item.locked_currency_code = price.currency_code
                            item.locked_exchange_rate = None
                            item.locked_exchange_rate_month = None
                            item.locked_exchange_rate_stage = "principle_approved"
                            item.locked_exchange_rate_at = datetime.now()
                    else:
                        # USD货币，设置默认值
                        item.locked_currency_code = "USD"
                        item.locked_exchange_rate = 1.0  # USD对USD汇率为1
                        item.locked_exchange_rate_month = current_month
                        item.locked_exchange_rate_stage = "principle_approved"
                        item.locked_exchange_rate_at = datetime.now()
                        
                        logging.info(f"物品 {item.item_name} 主管审批时汇率锁定: 货币=USD, 汇率=1.0")
                    
                    break  # 只锁定第一个有效价格
                
                if price_found:
                    break
            
            # 如果没有找到任何价格，设置默认值
            if not price_found:
                logging.warning(f"物品 {item.item_name} 没有找到有效的供应商价格")
                item.locked_currency_code = "USD"  # 默认USD
                item.locked_exchange_rate = 1.0
                item.locked_exchange_rate_month = current_month
                item.locked_exchange_rate_stage = "principle_approved"
                item.locked_exchange_rate_at = datetime.now()
        
        self.db.commit()
        logging.info(f"申请 {request_id} 主管审批时汇率锁定完成")

    def lock_exchange_rates_on_final_approval(self, request_id: int) -> None:
        """最终审批时锁定汇率（第三阶段锁定）"""
        from app.models.supplier import ItemSupplier, SupplierPrice
        from datetime import datetime, date
        
        request_items = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.request_id == request_id
        ).all()
        
        if not request_items:
            return
        
        exchange_rate_service = ExchangeRateService(self.db)
        current_date = datetime.now().date()
        current_month = date(current_date.year, current_date.month, 1)
        
        for item in request_items:
            # 标记是否找到有效价格
            price_found = False
            
            # 获取物品的供应商价格信息
            item_suppliers = self.db.query(ItemSupplier).filter(
                ItemSupplier.item_id == item.item_id,
                ItemSupplier.status == "active"
            ).all()
            
            for item_supplier in item_suppliers:
                # 查找有效的价格记录
                current_time = datetime.now()
                prices = self.db.query(SupplierPrice).filter(
                    and_(
                        SupplierPrice.item_supplier_id == item_supplier.id,
                        SupplierPrice.status == "active",
                        SupplierPrice.valid_from <= current_time,
                        or_(
                            SupplierPrice.valid_to.is_(None),
                            SupplierPrice.valid_to > current_time
                        )
                    )
                ).all()
                
                for price in prices:
                    price_found = True
                    if price.currency_code != "USD":
                        # 获取当前汇率
                        exchange_rate = exchange_rate_service.get_latest_exchange_rate(
                            price.currency_code, current_date
                        )
                        
                        if exchange_rate:
                            # 锁定汇率信息
                            item.locked_currency_code = price.currency_code
                            item.locked_exchange_rate = exchange_rate.rate
                            item.locked_exchange_rate_month = exchange_rate.effective_month
                            item.locked_exchange_rate_stage = "final_approved"
                            item.locked_exchange_rate_at = datetime.now()
                            
                            logging.info(f"物品 {item.item_name} 最终审批时汇率锁定: 货币={price.currency_code}, 汇率={exchange_rate.rate}, 生效月份={exchange_rate.effective_month}")
                        else:
                            # 没有找到有效汇率，记录警告
                            logging.warning(f"物品 {item.item_name} 货币 {price.currency_code} 没有找到有效汇率")
                            item.locked_currency_code = price.currency_code
                            item.locked_exchange_rate = None
                            item.locked_exchange_rate_stage = "final_approved"
                            item.locked_exchange_rate_at = datetime.now()
                    else:
                        # USD货币，设置默认值
                        item.locked_currency_code = "USD"
                        item.locked_exchange_rate = 1.0  # USD对USD汇率为1
                        item.locked_exchange_rate_month = current_month
                        item.locked_exchange_rate_stage = "final_approved"
                        item.locked_exchange_rate_at = datetime.now()
                        
                        logging.info(f"物品 {item.item_name} 最终审批时汇率锁定: 货币=USD, 汇率=1.0")
                    
                    break  # 只锁定第一个有效价格
                
                if price_found:
                    break
            
            # 如果没有找到任何价格，设置默认值
            if not price_found:
                logging.warning(f"物品 {item.item_name} 没有找到有效的供应商价格")
                item.locked_currency_code = "USD"  # 默认USD
                item.locked_exchange_rate = 1.0
                item.locked_exchange_rate_month = current_month
                item.locked_exchange_rate_stage = "final_approved"
                item.locked_exchange_rate_at = datetime.now()
        
        self.db.commit()
        logging.info(f"申请 {request_id} 最终审批时汇率锁定完成")
