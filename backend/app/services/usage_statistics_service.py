"""
使用统计服务
直接统计ItemUsageRecord表，不依赖额外的统计表
"""

from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc, extract
from app.models.usage import ItemUsageRecord, UsageStatus
from app.models.item import Item
from app.models.user import Department
from typing import Optional


class UsageStatisticsService:
    """使用统计服务"""
    
    @staticmethod
    def get_usage_statistics(
        db: Session, 
        department_id: int,
        item_id: Optional[int] = None,
        year: Optional[int] = None,
        month: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        skip: int = 0,
        limit: int = 100
    ):
        """
        获取使用统计数据
        
        Args:
            db: 数据库会话
            department_id: 部门ID
            item_id: 物品ID（可选）
            year: 年份（可选）
            month: 月份（可选）
            start_date: 开始日期（可选）
            end_date: 结束日期（可选）
            skip: 跳过记录数
            limit: 限制记录数
            
        Returns:
            list: 统计记录列表
        """
        try:
            # 构建基础查询
            query = db.query(
                ItemUsageRecord.item_id,
                ItemUsageRecord.department_id,
                func.sum(ItemUsageRecord.quantity).label('total_usage'),
                func.count(ItemUsageRecord.id).label('usage_count'),
                func.min(ItemUsageRecord.usage_time).label('first_usage'),
                func.max(ItemUsageRecord.usage_time).label('last_usage')
            ).filter(
                ItemUsageRecord.department_id == department_id,
                ItemUsageRecord.status == UsageStatus.confirmed  # 只统计已确认的记录
            )
            
            # 应用筛选条件
            if item_id:
                query = query.filter(ItemUsageRecord.item_id == item_id)
            
            if year:
                query = query.filter(extract('year', ItemUsageRecord.usage_time) == year)
            
            if month:
                query = query.filter(extract('month', ItemUsageRecord.usage_time) == month)
            
            if start_date:
                query = query.filter(ItemUsageRecord.usage_time >= start_date)
            
            if end_date:
                query = query.filter(ItemUsageRecord.usage_time <= end_date)
            
            # 分组和排序
            query = query.group_by(
                ItemUsageRecord.item_id,
                ItemUsageRecord.department_id
            ).order_by(desc('total_usage'))
            
            # 应用分页
            query = query.offset(skip).limit(limit)
            
            # 执行查询
            results = query.all()
            
            # 转换为字典格式
            statistics = []
            for result in results:
                # 获取物品和部门信息
                item = db.query(Item).filter(Item.id == result.item_id).first()
                dept = db.query(Department).filter(Department.id == result.department_id).first()
                
                if item and dept:
                    statistics.append({
                        'item_id': result.item_id,
                        'department_id': result.department_id,
                        'item_name': item.name,
                        'item_code': item.code,
                        'department_name': dept.name,
                        'total_usage': result.total_usage,
                        'usage_count': result.usage_count,
                        'avg_unit_price': Decimal('0'),  # ItemUsageRecord没有价格字段，设为0
                        'first_usage': result.first_usage,
                        'last_usage': result.last_usage,
                        'unit': item.inventory_unit or '个'
                    })
            
            return statistics
            
        except Exception as e:
            print(f"❌ 获取使用统计数据失败: {str(e)}")
            return []
    
    @staticmethod
    def get_monthly_statistics(
        db: Session,
        department_id: int,
        year: int,
        month: Optional[int] = None
    ):
        """
        获取月度统计数据
        
        Args:
            db: 数据库会话
            department_id: 部门ID
            year: 年份
            month: 月份（可选，如果不指定则统计全年）
            
        Returns:
            dict: 月度统计摘要
        """
        try:
            # 构建查询条件
            query = db.query(ItemUsageRecord).filter(
                ItemUsageRecord.department_id == department_id,
                ItemUsageRecord.status == UsageStatus.confirmed,
                extract('year', ItemUsageRecord.usage_time) == year
            )
            
            if month:
                query = query.filter(extract('month', ItemUsageRecord.usage_time) == month)
            
            # 获取记录
            records = query.all()
            
            if not records:
                return {
                    'year': year,
                    'month': month,
                    'total_usage': Decimal('0'),
                    'total_records': 0,
                    'total_items': 0,
                    'avg_usage_per_item': Decimal('0')
                }
            
            # 计算统计信息
            total_usage = sum(record.quantity for record in records)
            total_records = len(records)
            unique_items = len(set(record.item_id for record in records))
            avg_usage_per_item = total_usage / unique_items if unique_items > 0 else Decimal('0')
            
            return {
                'year': year,
                'month': month,
                'total_usage': total_usage,
                'total_records': total_records,
                'total_items': unique_items,
                'avg_usage_per_item': avg_usage_per_item
            }
            
        except Exception as e:
            print(f"❌ 获取月度统计数据失败: {str(e)}")
            return {
                'year': year,
                'month': month,
                'total_usage': Decimal('0'),
                'total_records': 0,
                'total_items': 0,
                'avg_usage_per_item': Decimal('0')
            }
    
    @staticmethod
    def get_department_summary(db: Session, department_id: int):
        """
        获取部门使用统计摘要
        
        Args:
            db: 数据库会话
            department_id: 部门ID
            
        Returns:
            dict: 部门统计摘要
        """
        try:
            # 获取总使用量
            total_usage = db.query(func.sum(ItemUsageRecord.quantity)).filter(
                ItemUsageRecord.department_id == department_id,
                ItemUsageRecord.status == UsageStatus.confirmed
            ).scalar() or Decimal('0')
            
            # 获取总记录数
            total_records = db.query(func.count(ItemUsageRecord.id)).filter(
                ItemUsageRecord.department_id == department_id,
                ItemUsageRecord.status == UsageStatus.confirmed
            ).scalar() or 0
            
            # 获取涉及物品数
            total_items = db.query(func.count(func.distinct(ItemUsageRecord.item_id))).filter(
                ItemUsageRecord.department_id == department_id,
                ItemUsageRecord.status == UsageStatus.confirmed
            ).scalar() or 0
            
            # 获取本月统计
            current_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            month_usage = db.query(func.sum(ItemUsageRecord.quantity)).filter(
                ItemUsageRecord.department_id == department_id,
                ItemUsageRecord.status == UsageStatus.confirmed,
                ItemUsageRecord.usage_time >= current_month
            ).scalar() or Decimal('0')
            
            # 获取本月记录数
            month_records = db.query(func.count(ItemUsageRecord.id)).filter(
                ItemUsageRecord.department_id == department_id,
                ItemUsageRecord.status == UsageStatus.confirmed,
                ItemUsageRecord.usage_time >= current_month
            ).scalar() or 0
            
            return {
                'department_id': department_id,
                'total_usage': total_usage,
                'total_records': total_records,
                'total_items': total_items,
                'month_usage': month_usage,
                'month_records': month_records,
                'avg_usage_per_record': total_usage / total_records if total_records > 0 else Decimal('0')
            }
            
        except Exception as e:
            print(f"❌ 获取部门统计摘要失败: {str(e)}")
            return {
                'department_id': department_id,
                'total_usage': Decimal('0'),
                'total_records': 0,
                'total_items': 0,
                'month_usage': Decimal('0'),
                'month_records': 0,
                'avg_usage_per_record': Decimal('0')
            }
    
    @staticmethod
    def get_top_used_items(
        db: Session,
        department_id: int,
        limit: int = 10,
        days: int = 30
    ):
        """
        获取最常用物品排行
        
        Args:
            db: 数据库会话
            department_id: 部门ID
            limit: 返回记录数限制
            days: 统计天数
            
        Returns:
            list: 最常用物品列表
        """
        try:
            # 计算开始日期
            start_date = datetime.now() - timedelta(days=days)
            
            # 查询最常用物品
            results = db.query(
                ItemUsageRecord.item_id,
                func.sum(ItemUsageRecord.quantity).label('total_usage'),
                func.count(ItemUsageRecord.id).label('usage_count')
            ).filter(
                ItemUsageRecord.department_id == department_id,
                ItemUsageRecord.status == UsageStatus.confirmed,
                ItemUsageRecord.usage_time >= start_date
            ).group_by(
                ItemUsageRecord.item_id
            ).order_by(
                desc('total_usage')
            ).limit(limit).all()
            
            # 转换为字典格式
            top_items = []
            for result in results:
                item = db.query(Item).filter(Item.id == result.item_id).first()
                if item:
                    top_items.append({
                        'item_id': result.item_id,
                        'item_name': item.name,
                        'item_code': item.code,
                        'total_usage': result.total_usage,
                        'usage_count': result.usage_count,
                        'unit': item.inventory_unit or '个'
                    })
            
            return top_items
            
        except Exception as e:
            print(f"❌ 获取最常用物品排行失败: {str(e)}")
            return []
