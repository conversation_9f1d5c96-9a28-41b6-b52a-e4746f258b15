from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional
from datetime import datetime
from app.models.purchase import PurchaseCartItem
from app.models.user import User
from app.schemas.purchase_cart import AddCartItemRequest, UpdateCartItemRequest, CartItemWithDetails
from app.core.exceptions import ResourceNotFoundException
from app.services.inventory_check_service import InventoryCheckService
from app.services.item_price_service import ItemPriceService


class PurchaseCartService:
    """购物车管理服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_department_cart_items(self, department_id: int, current_user: User = None) -> List[CartItemWithDetails]:
        """获取部门购物车中的所有物品（带权限检查和库存信息）"""
        from app.models.item import Item
        from app.services.auth import has_permission
        import logging
        
        # 权限检查 - 严格限制部门访问
        if current_user:
            # 超级用户可以看到所有部门
            if current_user.is_superuser:
                pass
            # 有cart.view_all权限的用户可以看到所有部门
            elif has_permission(current_user, "cart.view_all", self.db):
                pass
            # 其他所有用户（包括有cart.view权限的）只能看到自己部门
            else:
                if not current_user.department_id:
                    return []  # 用户没有部门，无法访问任何购物车
                if department_id != current_user.department_id:
                    return []  # 只能访问自己部门的购物车
        
        try:
            # 关联查询物品信息
            cart_items = self.db.query(PurchaseCartItem).filter(
                PurchaseCartItem.department_id == department_id
            ).all()
            
            logging.info(f"获取部门购物车项目: department_id={department_id}, 项目数量={len(cart_items)}")
            
            # 为每个购物车项目创建详细信息对象
            inventory_check_service = InventoryCheckService(self.db)
            item_price_service = ItemPriceService(self.db)
            detailed_cart_items = []
            
            for cart_item in cart_items:
                try:
                    item = self.db.query(Item).filter(Item.id == cart_item.item_id).first()
                    if item:
                        # 计算总数量：SPQ数量 × SPQ个数
                        total_quantity = float(cart_item.spq_quantity) * cart_item.spq_count
                        
                        # 获取价格信息，使用实际数量进行阶梯定价
                        estimated_unit_price = 0.0
                        total_price = 0.0
                        try:
                            price_info = item_price_service.get_item_price_info(
                                item_id=cart_item.item_id,
                                quantity=int(total_quantity)
                            )
                            
                            if price_info and price_info["unit_price"]["usd_amount"]:
                                estimated_unit_price = price_info["unit_price"]["usd_amount"]
                                total_price = price_info["total_price"]["usd_amount"]
                                
                        except Exception as price_error:
                            logging.warning(f"获取购物车项目价格信息失败: cart_item_id={cart_item.id}, error={str(price_error)}")
                        
                        # 获取库存信息
                        current_quantity = 0.0
                        max_quantity = None
                        is_overstock = False
                        overstock_message = ""
                        
                        try:
                            from app.models.inventory import DepartmentInventory
                            inventory = self.db.query(DepartmentInventory).filter(
                                DepartmentInventory.department_id == cart_item.department_id,
                                DepartmentInventory.item_id == cart_item.item_id,
                                DepartmentInventory.is_active == True
                            ).first()
                            
                            if inventory:
                                current_quantity = float(inventory.current_quantity) if inventory.current_quantity else 0
                                max_quantity = float(inventory.max_quantity) if inventory.max_quantity else None
                                total_after_purchase = current_quantity + total_quantity
                                
                                # 判断是否超出最大库存
                                if max_quantity is not None and total_after_purchase > max_quantity:
                                    is_overstock = True
                                    overstock_message = f"超出最大库存({max_quantity})"
                            else:
                                total_after_purchase = total_quantity
                                
                        except Exception as e:
                            logging.error(f"获取购物车项目库存信息时出错: cart_item_id={cart_item.id}, error={str(e)}")
                            total_after_purchase = total_quantity
                        
                        # 创建详细信息对象
                        detailed_item = CartItemWithDetails(
                            id=cart_item.id,
                            department_id=cart_item.department_id,
                            item_id=cart_item.item_id,
                            spq_quantity=cart_item.spq_quantity,
                            spq_count=cart_item.spq_count,
                            spq_unit=cart_item.spq_unit,
                            notes=cart_item.notes,
                            created_by=cart_item.created_by,
                            created_at=cart_item.created_at,
                            updated_at=cart_item.updated_at,
                            item_code=item.code,
                            item_name=item.name,
                            item_image_url=item.image_url,
                            estimated_unit_price=estimated_unit_price,
                            total_price=total_price,
                            current_quantity=current_quantity,
                            max_quantity=max_quantity,
                            cart_quantity=total_quantity,
                            total_after_purchase=total_after_purchase,
                            is_overstock=is_overstock,
                            overstock_message=overstock_message
                        )
                        
                        detailed_cart_items.append(detailed_item)
                        logging.debug(f"购物车项目创建: item_id={cart_item.id}, spq_count={cart_item.spq_count}")
                    else:
                        logging.warning(f"购物车项目对应的物品不存在: cart_item_id={cart_item.id}, item_id={cart_item.item_id}")
                        
                except Exception as e:
                    logging.error(f"处理购物车项目时出错: cart_item_id={cart_item.id}, error={str(e)}")
                    # 继续处理其他项目，不中断整个流程
                    continue
            
            return detailed_cart_items
            
        except Exception as e:
            logging.error(f"获取部门购物车项目失败: department_id={department_id}, error={str(e)}")
            raise
    
    def add_item_to_cart(self, department_id: int, item_data: AddCartItemRequest, current_user: User) -> PurchaseCartItem:
        """添加物品到部门购物车（带权限检查）"""
        from app.models.supplier import ItemSupplier, SupplierPrice
        from app.services.auth import has_permission
        
        # 创建库存检查服务
        inventory_check_service = InventoryCheckService(self.db)
        
        # 权限检查 - 严格限制部门访问
        if current_user:
            # 超级用户可以为任何部门添加物品
            if current_user.is_superuser:
                pass
            # 有cart.add_item_all权限的用户可以为任何部门添加物品
            elif has_permission(current_user, "cart.add_item_all", self.db):
                pass
            # 其他所有用户（包括有cart.add_item权限的）只能为自己部门添加物品
            else:
                if not current_user.department_id:
                    from app.core.exceptions import PermissionException
                    raise PermissionException("用户没有部门，无法添加购物车物品")
                if department_id != current_user.department_id:
                    from app.core.exceptions import PermissionException
                    raise PermissionException("只能为自己部门添加购物车物品")
        
        # 检查是否已存在相同物品
        existing_item = self.db.query(PurchaseCartItem).filter(
            PurchaseCartItem.department_id == department_id,
            PurchaseCartItem.item_id == item_data.item_id
        ).first()
        
        if existing_item:
            # 如果已存在，更新SPQ个数
            existing_item.spq_count += item_data.spq_count
            existing_item.notes = item_data.notes or existing_item.notes
            existing_item.updated_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(existing_item)
            return existing_item
        
        # 从最优先供应商获取SPQ信息
        spq_quantity = 1.0  # 默认SPQ数量
        spq_unit = "piece"     # 默认SPQ单位
        
        # 查询最优先供应商的SPQ信息
        priority_supplier = self.db.query(ItemSupplier).filter(
            ItemSupplier.item_id == item_data.item_id,
            ItemSupplier.status == "active"
        ).order_by(ItemSupplier.priority.asc()).first()
        
        if not priority_supplier:
            raise ResourceNotFoundException("ItemSupplier", item_data.item_id)
        
        spq_quantity = float(priority_supplier.spq)
        # 从物品信息中获取采购单位
        from app.models.item import Item
        item = self.db.query(Item).filter(Item.id == item_data.item_id).first()
        if item and item.purchase_unit:
            spq_unit = item.purchase_unit
        
        # 创建新的购物车项目
        cart_item = PurchaseCartItem(
            department_id=department_id,
            item_id=item_data.item_id,
            spq_quantity=spq_quantity,
            spq_count=item_data.spq_count,
            spq_unit=spq_unit,
            created_by=current_user.id
        )
        
        # 设置备注字段
        if item_data.notes:
            cart_item.notes = item_data.notes
        
        self.db.add(cart_item)
        self.db.commit()
        self.db.refresh(cart_item)
        
        return cart_item
    
    def update_cart_item(self, cart_item_id: int, updates: UpdateCartItemRequest, current_user: User = None) -> PurchaseCartItem:
        """更新购物车项目（带权限检查）"""
        cart_item = self.db.query(PurchaseCartItem).filter(PurchaseCartItem.id == cart_item_id).first()
        if not cart_item:
            raise ResourceNotFoundException("PurchaseCartItem", cart_item_id)
        
        # 权限检查 - 只能更新自己部门的购物车项目
        if current_user and not current_user.is_superuser:
            if not current_user.department_id:
                from app.core.exceptions import PermissionException
                raise PermissionException("用户没有部门，无法更新购物车项目")
            if cart_item.department_id != current_user.department_id:
                from app.core.exceptions import PermissionException
                raise PermissionException("只能更新自己部门的购物车项目")
        
        # 更新字段
        if updates.spq_count is not None:
            cart_item.spq_count = updates.spq_count
        if updates.notes is not None:
            cart_item.notes = updates.notes
        
        cart_item.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(cart_item)
        return cart_item
    
    def remove_cart_item(self, cart_item_id: int, current_user: User = None) -> bool:
        """从购物车移除物品（带权限检查）"""
        cart_item = self.db.query(PurchaseCartItem).filter(PurchaseCartItem.id == cart_item_id).first()
        if not cart_item:
            raise ResourceNotFoundException("PurchaseCartItem", cart_item_id)
        
        # 权限检查 - 只能移除自己部门的购物车项目
        if current_user and not current_user.is_superuser:
            if not current_user.department_id:
                from app.core.exceptions import PermissionException
                raise PermissionException("用户没有部门，无法移除购物车项目")
            if cart_item.department_id != current_user.department_id:
                from app.core.exceptions import PermissionException
                raise PermissionException("只能移除自己部门的购物车项目")
        
        self.db.delete(cart_item)
        self.db.commit()
        return True
    
    def clear_department_cart(self, department_id: int, current_user: User = None) -> bool:
        """清空部门购物车（带权限检查）"""
        # 权限检查 - 只能清空自己部门的购物车
        if current_user and not current_user.is_superuser:
            if not current_user.department_id:
                from app.core.exceptions import PermissionException
                raise PermissionException("用户没有部门，无法清空购物车")
            if department_id != current_user.department_id:
                from app.core.exceptions import PermissionException
                raise PermissionException("只能清空自己部门的购物车")
        
        self.db.query(PurchaseCartItem).filter(
            PurchaseCartItem.department_id == department_id
        ).delete()
        self.db.commit()
        return True
    
    def get_cart_item_by_id(self, cart_item_id: int) -> Optional[CartItemWithDetails]:
        """根据ID获取购物车项目"""
        from app.models.item import Item
        
        cart_item = self.db.query(PurchaseCartItem).filter(PurchaseCartItem.id == cart_item_id).first()
        if cart_item:
            # 关联查询物品信息
            item = self.db.query(Item).filter(Item.id == cart_item.item_id).first()
            if item:
                # 计算总数量：SPQ数量 × SPQ个数
                total_quantity = float(cart_item.spq_quantity) * cart_item.spq_count
                
                # 使用ItemPriceService获取价格信息（转换为USD）
                estimated_unit_price = 0.0
                total_price = 0.0
                try:
                    item_price_service = ItemPriceService(self.db)
                    
                    # 获取价格信息，使用实际数量进行阶梯定价
                    price_info = item_price_service.get_item_price_info(
                        item_id=cart_item.item_id,
                        quantity=int(total_quantity)
                    )
                    
                    if price_info and price_info["unit_price"]["usd_amount"]:
                        estimated_unit_price = price_info["unit_price"]["usd_amount"]
                        total_price = price_info["total_price"]["usd_amount"]
                        
                except Exception as price_error:
                    # 如果获取价格失败，设置默认值
                    pass
                
                # 获取库存信息
                current_quantity = 0.0
                max_quantity = None
                is_overstock = False
                overstock_message = ""
                
                try:
                    from app.models.inventory import DepartmentInventory
                    inventory = self.db.query(DepartmentInventory).filter(
                        DepartmentInventory.department_id == cart_item.department_id,
                        DepartmentInventory.item_id == cart_item.item_id,
                        DepartmentInventory.is_active == True
                    ).first()
                    
                    if inventory:
                        current_quantity = float(inventory.current_quantity) if inventory.current_quantity else 0
                        max_quantity = float(inventory.max_quantity) if inventory.max_quantity else None
                        total_after_purchase = current_quantity + total_quantity
                        
                        # 判断是否超出最大库存
                        if max_quantity is not None and total_after_purchase > max_quantity:
                            is_overstock = True
                            overstock_message = f"超出最大库存({max_quantity})"
                    else:
                        total_after_purchase = total_quantity
                        
                except Exception as e:
                    total_after_purchase = total_quantity
                
                # 创建详细信息对象
                return CartItemWithDetails(
                    id=cart_item.id,
                    department_id=cart_item.department_id,
                    item_id=cart_item.item_id,
                    spq_quantity=cart_item.spq_quantity,
                    spq_count=cart_item.spq_count,
                    spq_unit=cart_item.spq_unit,
                    notes=cart_item.notes,
                    created_by=cart_item.created_by,
                    created_at=cart_item.created_at,
                    updated_at=cart_item.updated_at,
                    item_code=item.code,
                    item_name=item.name,
                    item_image_url=item.image_url,
                    estimated_unit_price=estimated_unit_price,
                    total_price=total_price,
                    current_quantity=current_quantity,
                    max_quantity=max_quantity,
                    cart_quantity=total_quantity,
                    total_after_purchase=total_after_purchase,
                    is_overstock=is_overstock,
                    overstock_message=overstock_message
                )
        
        return None
    
    def get_cart_summary(self, department_id: int, current_user: User = None) -> dict:
        """获取部门购物车摘要信息（带权限检查）"""
        items = self.get_department_cart_items(department_id, current_user)
        total_items = len(items)
        # 计算总数量：SPQ数量 × SPQ个数
        total_quantity = sum(float(item.spq_quantity) * item.spq_count for item in items)
        # 计算预估总金额
        total_amount = sum(item.total_price or 0 for item in items)
        
        return {
            "department_id": department_id,
            "total_items": total_items,
            "total_quantity": total_quantity,
            "total_amount": total_amount,
            "items": items
        }
    
    def submit_cart(self, department_id: int, current_user: User) -> int:
        """提交购物车申请（带权限检查）"""
        from app.services.auth import has_permission
        from app.services.purchase_request_service import PurchaseRequestService
        
        # 权限检查 - 严格限制部门访问
        if current_user:
            # 超级用户可以提交任何部门的购物车
            if current_user.is_superuser:
                pass
            # 有cart.submit_all权限的用户可以提交任何部门的购物车
            elif has_permission(current_user, "cart.submit_all", self.db):
                pass
            # 其他所有用户（包括有cart.submit权限的）只能提交自己部门的购物车
            else:
                if not current_user.department_id:
                    from app.core.exceptions import PermissionException
                    raise PermissionException("用户没有部门，无法提交购物车")
                if department_id != current_user.department_id:
                    from app.core.exceptions import PermissionException
                    raise PermissionException("只能提交自己部门的购物车")
        
        # 获取部门购物车中的所有物品
        cart_items = self.get_department_cart_items(department_id, current_user)
        if not cart_items:
            raise ValueError("购物车中没有物品")
        
        # 获取购物车项目的ID列表
        cart_item_ids = [item.id for item in cart_items]
        
        # 使用采购申请服务创建申请
        purchase_service = PurchaseRequestService(self.db)
        request = purchase_service.create_request_from_cart_items(
            department_id=department_id,
            submitter_id=current_user.id,
            cart_item_ids=cart_item_ids,
            notes="从购物车提交的采购申请"
        )
        
        return request.id
