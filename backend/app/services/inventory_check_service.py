import os
import json
from datetime import datetime
from typing import List, Dict, Optional
from sqlalchemy.orm import Session
from decimal import Decimal
import logging

from app.models.inventory import DepartmentInventory
from app.models.purchase import PurchaseCartItem
from app.models.item import Item
from app.models.user import Department

logger = logging.getLogger(__name__)


class InventoryCheckService:
    """库存检查服务 - 检查购物车物品的库存量"""
    
    def __init__(self, db: Session):
        self.db = db
        self.alerts_dir = "inventory_alerts"
        self._ensure_alerts_directory()
    
    def _ensure_alerts_directory(self):
        """确保库存预警目录存在"""
        if not os.path.exists(self.alerts_dir):
            os.makedirs(self.alerts_dir)
            logger.info(f"创建库存预警目录: {self.alerts_dir}")
    
    def check_cart_item_risk(self, cart_item: PurchaseCartItem) -> Dict:
        """检查购物车项目是否有库存风险
        
        Returns:
            Dict: 包含风险检查结果的字典
                - has_risk: bool - 是否有风险
                - risk_type: str - 风险类型（overstock/out_of_stock）
                - risk_message: str - 风险提示消息
        """
        try:
            # 获取当前库存
            inventory = self.db.query(DepartmentInventory).filter(
                DepartmentInventory.department_id == cart_item.department_id,
                DepartmentInventory.item_id == cart_item.item_id,
                DepartmentInventory.is_active == True
            ).first()
            
            if not inventory:
                # 如果没有库存记录，创建默认记录
                inventory = DepartmentInventory(
                    department_id=cart_item.department_id,
                    item_id=cart_item.item_id,
                    current_quantity=Decimal("0"),
                    min_quantity=Decimal("0"),
                    max_quantity=None,
                    is_active=True
                )
                self.db.add(inventory)
                self.db.flush()
            
            # 获取物品信息
            item = self.db.query(Item).filter(Item.id == cart_item.item_id).first()
            if not item:
                return {
                    "has_risk": False,
                    "risk_type": None,
                    "risk_message": ""
                }
            
            # 计算购物车中的总数量（SPQ数量 × SPQ个数）
            cart_quantity = Decimal(str(cart_item.spq_quantity)) * Decimal(str(cart_item.spq_count))
            
            # 计算购买后的总库存量
            total_after_purchase = inventory.current_quantity + cart_quantity
            
            # 检查风险
            has_risk = False
            risk_type = None
            risk_message = ""
            
            # 检查超储风险
            if inventory.max_quantity is not None and total_after_purchase > inventory.max_quantity:
                has_risk = True
                risk_type = "overstock"
                risk_message = "超储风险"
            
            # 检查缺货风险（当前库存为0）
            elif inventory.current_quantity == 0:
                has_risk = True
                risk_type = "out_of_stock"
                risk_message = "缺货风险"
            
            return {
                "has_risk": has_risk,
                "risk_type": risk_type,
                "risk_message": risk_message
            }
            
        except Exception as e:
            logger.error(f"检查购物车项目风险时出错: cart_item_id={cart_item.id}, error={str(e)}")
            return {
                "has_risk": False,
                "risk_type": None,
                "risk_message": f"检查出错: {str(e)}"
            }
    
    def check_department_cart_risk(self, department_id: int) -> List[Dict]:
        """检查部门购物车中所有物品的风险
        
        Returns:
            List[Dict]: 包含所有购物车项目风险检查结果的列表
        """
        try:
            # 获取部门购物车中的所有物品
            cart_items = self.db.query(PurchaseCartItem).filter(
                PurchaseCartItem.department_id == department_id
            ).all()
            
            results = []
            for cart_item in cart_items:
                result = self.check_cart_item_risk(cart_item)
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"检查部门购物车风险时出错: department_id={department_id}, error={str(e)}")
            return []
    
    def generate_overstock_alert_file(self, overstock_items: List[Dict], department_id: int) -> str:
        """生成超储预警文件
        
        Args:
            overstock_items: 超储物品列表
            department_id: 部门ID
            
        Returns:
            str: 生成的文件路径
        """
        try:
            # 获取部门信息
            department = self.db.query(Department).filter(Department.id == department_id).first()
            department_name = department.name if department else f"部门{department_id}"
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"overstock_alert_{department_name}_{timestamp}.json"
            filepath = os.path.join(self.alerts_dir, filename)
            
            # 准备预警数据
            alert_data = {
                "alert_type": "overstock_warning",
                "department_id": department_id,
                "department_name": department_name,
                "generated_at": datetime.now().isoformat(),
                "overstock_items": overstock_items,
                "summary": {
                    "total_items": len(overstock_items),
                    "total_cart_quantity": sum(item.get("cart_quantity", 0) for item in overstock_items),
                    "total_after_purchase": sum(item.get("total_after_purchase", 0) for item in overstock_items)
                }
            }
            
            # 写入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(alert_data, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"生成超储预警文件: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"生成超储预警文件时出错: error={str(e)}")
            return ""
    
    def check_and_alert_cart_inventory(self, department_id: int) -> Dict:
        """检查部门购物车库存并生成预警文件
        
        Returns:
            Dict: 包含检查结果和预警文件信息的字典
        """
        try:
            # 检查所有购物车项目的库存
            inventory_results = self.check_department_cart_inventory(department_id)
            
            # 筛选出超储的物品
            overstock_items = [item for item in inventory_results if item.get("is_overstock", False)]
            
            result = {
                "department_id": department_id,
                "total_items": len(inventory_results),
                "overstock_items": overstock_items,
                "overstock_count": len(overstock_items),
                "alert_file_path": "",
                "alert_generated": False
            }
            
            # 如果有超储物品，生成预警文件
            if overstock_items:
                alert_file_path = self.generate_overstock_alert_file(overstock_items, department_id)
                if alert_file_path:
                    result["alert_file_path"] = alert_file_path
                    result["alert_generated"] = True
                    
                    logger.warning(f"部门 {department_id} 购物车中发现 {len(overstock_items)} 个超储物品，已生成预警文件")
            
            return result
            
        except Exception as e:
            logger.error(f"检查并预警购物车库存时出错: department_id={department_id}, error={str(e)}")
            return {
                "department_id": department_id,
                "total_items": 0,
                "overstock_items": [],
                "overstock_count": 0,
                "alert_file_path": "",
                "alert_generated": False,
                "error": str(e)
            }
    
    def get_alert_files(self, limit: int = 10) -> List[Dict]:
        """获取最近的预警文件列表
        
        Args:
            limit: 返回文件数量限制
            
        Returns:
            List[Dict]: 预警文件信息列表
        """
        try:
            if not os.path.exists(self.alerts_dir):
                return []
            
            files = []
            for filename in os.listdir(self.alerts_dir):
                if filename.endswith('.json'):
                    filepath = os.path.join(self.alerts_dir, filename)
                    file_stat = os.stat(filepath)
                    
                    files.append({
                        "filename": filename,
                        "filepath": filepath,
                        "size": file_stat.st_size,
                        "created_at": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                        "modified_at": datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                    })
            
            # 按修改时间排序，返回最新的文件
            files.sort(key=lambda x: x["modified_at"], reverse=True)
            return files[:limit]
            
        except Exception as e:
            logger.error(f"获取预警文件列表时出错: error={str(e)}")
            return []
    
    def delete_alert_file(self, filename: str) -> bool:
        """删除预警文件
        
        Args:
            filename: 要删除的文件名
            
        Returns:
            bool: 是否删除成功
        """
        try:
            filepath = os.path.join(self.alerts_dir, filename)
            if os.path.exists(filepath):
                os.remove(filepath)
                logger.info(f"删除预警文件: {filepath}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"删除预警文件时出错: filename={filename}, error={str(e)}")
            return False
