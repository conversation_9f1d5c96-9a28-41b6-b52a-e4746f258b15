"""
部门编码生成服务
基于序列号自动生成唯一部门编码，支持多种格式
"""

from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.models.user import Department
from app.core.database import get_db


class DepartmentCodeService:
    """部门编码生成服务"""
    
    @staticmethod
    def generate_department_code(db: Session, department_name: str = None) -> str:
        """
        生成部门编码
        
        Args:
            db: 数据库会话
            department_name: 部门名称（可选，用于生成更友好的编码）
            
        Returns:
            生成的部门编码
        """
        if department_name:
            # 如果提供了部门名称，尝试生成有意义的编码
            # 取前两个字符的大写
            code_prefix = department_name[:2].upper()
            if len(code_prefix) < 2:
                code_prefix = code_prefix.ljust(2, 'X')
            
            # 检查编码是否已存在
            existing_dept = db.query(Department).filter(Department.code == code_prefix).first()
            if not existing_dept:
                return code_prefix
        
        # 如果没有提供名称或编码已存在，使用默认格式
        # 获取当前最大编码
        max_code = db.query(Department.code).filter(
            Department.is_active == True
        ).order_by(Department.code.desc()).first()
        
        if max_code and max_code[0]:
            # 尝试解析现有编码，生成下一个
            try:
                # 检查是否是数字编码
                if max_code[0].isdigit():
                    next_num = int(max_code[0]) + 1
                    return str(next_num)
                # 检查是否是字母编码
                elif max_code[0].isalpha():
                    # 生成下一个字母编码
                    if max_code[0] == "Z":
                        return "AA"
                    elif len(max_code[0]) == 1:
                        return chr(ord(max_code[0]) + 1)
                    else:
                        # 处理双字母编码
                        first_char = max_code[0][0]
                        second_char = max_code[0][1]
                        if second_char == "Z":
                            if first_char == "Z":
                                return "AAA"
                            else:
                                return first_char + chr(ord(second_char) + 1)
            except (ValueError, IndexError):
                pass
        
        # 默认返回第一个编码
        return "A"
    
    @staticmethod
    def get_next_department_code(db: Session, department_name: str = None) -> str:
        """
        获取下一个部门编码（不更新数据库）
        
        Args:
            db: 数据库会话
            department_name: 部门名称（可选）
            
        Returns:
            下一个部门编码
        """
        return DepartmentCodeService.generate_department_code(db, department_name)
    
    @staticmethod
    def is_department_code_exists(db: Session, code: str) -> bool:
        """
        检查部门编码是否已存在
        
        Args:
            db: 数据库会话
            code: 部门编码
            
        Returns:
            是否存在
        """
        if not code:
            return False
        
        existing_dept = db.query(Department).filter(Department.code == code).first()
        return existing_dept is not None


# 便捷函数
def generate_department_code(department_name: str = None) -> str:
    """
    便捷函数：生成部门编码
    
    Args:
        department_name: 部门名称（可选）
        
    Returns:
        生成的部门编码
    """
    db = next(get_db())
    try:
        return DepartmentCodeService.generate_department_code(db, department_name)
    finally:
        db.close()
