"""
数据权限过滤服务
实现基于部门的数据隔离和权限控制
"""

from typing import List, Optional, Any
from sqlalchemy.orm import Session, Query
from sqlalchemy import or_, and_
from app.models.user import User
from app.models.user import Department

class DataPermissionFilter:
    """数据权限过滤器"""
    
    def __init__(self, db: Session, current_user: User):
        self.db = db
        self.current_user = current_user
    
    def get_accessible_department_ids(self) -> List[int]:
        """获取用户可访问的部门ID列表"""
        from app.models.permission import Role
        
        # 获取用户角色
        user_role = None
        if self.current_user.role_id:
            user_role = self.db.query(Role).filter(Role.id == self.current_user.role_id).first()
        
        # 超级管理员可以访问所有部门
        if self.current_user.is_superuser or (user_role and user_role.code == "admin"):
            return self._get_all_department_ids()
        
        # 系统管理员可以访问所有部门
        if user_role and user_role.code == "system_admin":
            return self._get_all_department_ids()
        
        # 物品管理员可以访问所有部门
        if user_role and user_role.code == "item_manager":
            return self._get_all_department_ids()
        
        # 部门经理可以访问本部门
        if user_role and user_role.code == "department_manager":
            return [self.current_user.department_id] if self.current_user.department_id else []
        
        # 其他用户只能访问自己的部门
        return [self.current_user.department_id] if self.current_user.department_id else []
    
    def get_accessible_user_ids(self) -> Optional[List[int]]:
        """获取用户可访问的用户ID列表，返回None表示无限制"""
        from app.models.permission import Role
        
        # 获取用户角色
        user_role = None
        if self.current_user.role_id:
            user_role = self.db.query(Role).filter(Role.id == self.current_user.role_id).first()
        
        # 超级管理员和系统管理员可以访问所有用户
        if (self.current_user.is_superuser or 
            (user_role and user_role.code == "admin") or
            (user_role and user_role.code == "system_admin")):
            return None
        
        # 物品管理员可以访问所有用户
        if user_role and user_role.code == "item_manager":
            return None
        
        # 部门经理可以访问本部门用户
        if user_role and user_role.code == "department_manager":
            dept_ids = [self.current_user.department_id] if self.current_user.department_id else []
            return self._get_users_by_departments(dept_ids)
        
        # 部门物品管理员可以访问本部门用户
        if user_role and user_role.code == "department_item_admin":
            return self._get_users_by_departments([self.current_user.department_id])
        
        # 普通用户只能访问自己
        return [self.current_user.id]
    
    def filter_by_department(self, query: Query, model: Any, department_field: str = "department_id") -> Query:
        """根据部门权限过滤查询"""
        accessible_dept_ids = self.get_accessible_department_ids()
        
        if not accessible_dept_ids:
            # 没有可访问的部门，返回空结果
            return query.filter(False)
        
        # 添加部门过滤条件
        dept_column = getattr(model, department_field, None)
        if dept_column is not None:
            return query.filter(dept_column.in_(accessible_dept_ids))
        
        return query
    
    def filter_by_user(self, query: Query, model: Any, user_field: str = "user_id") -> Query:
        """根据用户权限过滤查询"""
        accessible_user_ids = self.get_accessible_user_ids()
        
        if accessible_user_ids is None:
            # 无限制，返回原查询
            return query
        
        if not accessible_user_ids:
            # 没有可访问的用户，返回空结果
            return query.filter(False)
        
        # 添加用户过滤条件
        user_column = getattr(model, user_field, None)
        if user_column is not None:
            return query.filter(user_column.in_(accessible_user_ids))
        
        return query
    
    def filter_by_creator(self, query: Query, model: Any, creator_field: str = "created_by") -> Query:
        """根据创建者权限过滤查询"""
        return self.filter_by_user(query, model, creator_field)
    
    def can_access_department(self, department_id: int) -> bool:
        """检查是否可以访问指定部门"""
        accessible_dept_ids = self.get_accessible_department_ids()
        return department_id in accessible_dept_ids
    
    def can_access_user(self, user_id: int) -> bool:
        """检查是否可以访问指定用户"""
        accessible_user_ids = self.get_accessible_user_ids()
        
        if accessible_user_ids is None:
            return True
        
        return user_id in accessible_user_ids
    
    def can_manage_user(self, target_user: User) -> bool:
        """检查是否可以管理指定用户"""
        from app.models.permission import Role
        
        # 不能管理自己
        if target_user.id == self.current_user.id:
            return False
        
        # 获取当前用户角色
        current_user_role = None
        if self.current_user.role_id:
            current_user_role = self.db.query(Role).filter(Role.id == self.current_user.role_id).first()
        
        # 获取目标用户角色
        target_user_role = None
        if target_user.role_id:
            target_user_role = self.db.query(Role).filter(Role.id == target_user.role_id).first()
        
        # 超级管理员可以管理所有用户
        if (self.current_user.is_superuser or 
            (current_user_role and current_user_role.code == "admin")):
            return True
        
        # 系统管理员可以管理非超级管理员用户
        if current_user_role and current_user_role.code == "system_admin":
            return not target_user.is_superuser
        
        # 部门经理可以管理本部门的普通用户
        if current_user_role and current_user_role.code == "department_manager":
            if target_user.department_id:
                return (target_user.department_id == self.current_user.department_id and
                        not (target_user_role and target_user_role.code in ["admin", "system_admin", "item_manager"]))
        
        return False
    
    def _get_all_department_ids(self) -> List[int]:
        """获取所有部门ID"""
        try:
            departments = self.db.query(Department.id).filter(Department.is_active == True).all()
            return [dept.id for dept in departments]
        except Exception:
            return []
    

    
    def _get_users_by_departments(self, dept_ids: List[int]) -> List[int]:
        """获取指定部门的所有用户ID"""
        if not dept_ids:
            return []
        
        try:
            users = self.db.query(User.id).filter(
                User.department_id.in_(dept_ids),
                User.is_active == True
            ).all()
            return [user.id for user in users]
        except Exception:
            return []

def create_data_filter(db: Session, current_user: User) -> DataPermissionFilter:
    """创建数据权限过滤器"""
    return DataPermissionFilter(db, current_user)

# 装饰器函数
def apply_department_filter(query: Query, model: Any, current_user: User, db: Session, 
                           department_field: str = "department_id") -> Query:
    """应用部门数据过滤的便捷函数"""
    filter_service = create_data_filter(db, current_user)
    return filter_service.filter_by_department(query, model, department_field)

def apply_user_filter(query: Query, model: Any, current_user: User, db: Session,
                     user_field: str = "user_id") -> Query:
    """应用用户数据过滤的便捷函数"""
    filter_service = create_data_filter(db, current_user)
    return filter_service.filter_by_user(query, model, user_field)

# 权限检查函数
def check_department_access(department_id: int, current_user: User, db: Session) -> bool:
    """检查部门访问权限"""
    filter_service = create_data_filter(db, current_user)
    return filter_service.can_access_department(department_id)

def check_user_access(user_id: int, current_user: User, db: Session) -> bool:
    """检查用户访问权限"""
    filter_service = create_data_filter(db, current_user)
    return filter_service.can_access_user(user_id)

def check_user_management(target_user: User, current_user: User, db: Session) -> bool:
    """检查用户管理权限"""
    filter_service = create_data_filter(db, current_user)
    return filter_service.can_manage_user(target_user) 