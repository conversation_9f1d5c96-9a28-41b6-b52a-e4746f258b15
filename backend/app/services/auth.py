from datetime import datetime, timedelta, timezone
from typing import List, Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy.orm import Session
from sqlalchemy import or_
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.core.config import settings
from app.core.database import get_db
from app.models.user import User, Department
from app.models.permission import Permission, Role, AuditLog
from app.schemas.user import TokenData
from app.core.permissions import get_role_permissions

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT Bearer认证
security = HTTPBearer()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """生成密码hash"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: timedelta = None):
    """创建JWT访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.jwt_secret_key, algorithm=settings.jwt_algorithm)
    return encoded_jwt

def verify_token(token: str, credentials_exception: HTTPException):
    """验证JWT令牌"""
    try:
        payload = jwt.decode(token, settings.jwt_secret_key, algorithms=[settings.jwt_algorithm])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    return token_data

def get_user(db: Session, username: str):
    """根据用户名获取用户"""
    return db.query(User).filter(User.username == username).first()

def authenticate_user(db: Session, username: str, password: str):
    """认证用户"""
    user = get_user(db, username)
    if not user:
        return False, "用户名或密码错误"
    
    # 检查账号状态
    if not user.is_active:
        return False, "账号已被禁用"
    
    if user.account_status == "locked":
        if user.locked_until and user.locked_until > datetime.now(timezone.utc):
            return False, f"账号已被锁定，请等待{user.locked_until.strftime('%H:%M:%S')}后重试"
        else:
            # 锁定时间已过，重置状态
            user.account_status = "active"
            user.login_attempts = 0
            user.locked_until = None
            db.commit()
    
    if not verify_password(password, user.hashed_password):
        return False, "用户名或密码错误"
    
    return user, None

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """获取当前登录用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    token_data = verify_token(credentials.credentials, credentials_exception)
    user = get_user(db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)):
    """获取当前活跃用户"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

def require_roles(allowed_roles: List[str]):
    """角色权限检查装饰器（保持向后兼容）"""
    def role_checker(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
        if not current_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user"
            )
        
        # 超级用户放行
        if getattr(current_user, "is_superuser", False):
            return current_user

        # 获取用户角色
        user_role = None
        if current_user.role_id:
            user_role = db.query(Role).filter(Role.id == current_user.role_id).first()
        
        # 检查用户是否有允许的角色
        if not user_role or user_role.code not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Operation requires one of the following roles: {', '.join(allowed_roles)}"
            )
        
        return current_user
    
    return role_checker

def get_user_permissions(db: Session, user: User) -> List[str]:
    """获取用户所有权限"""
    permissions = set()
    
    # 1. 从用户角色获取权限
    if user.role_id:
        role = db.query(Role).filter(Role.id == user.role_id).first()
        if role and role.is_active:
            for permission in role.permissions:
                if permission.is_active:
                    permissions.add(permission.code)
    
    return list(permissions)

def has_permission(user: User, permission_code: str, db: Session = None) -> bool:
    """检查用户是否有指定权限"""
    # 超级用户拥有所有权限
    if user.is_superuser:
        return True
    
    # 如果没有数据库连接，无法检查权限
    if db is None:
        return False
    
    # 完整权限检查
    user_permissions = get_user_permissions(db, user)
    return permission_code in user_permissions

def has_any_permission(user: User, permission_codes: List[str], db: Session = None) -> bool:
    """检查用户是否有任意一个权限"""
    return any(has_permission(user, code, db) for code in permission_codes)

def has_all_permissions(user: User, permission_codes: List[str], db: Session = None) -> bool:
    """检查用户是否有所有权限"""
    return all(has_permission(user, code, db) for code in permission_codes)

def get_user_with_permissions(db: Session, user: User) -> dict:
    """获取包含权限信息的用户数据"""
    # 获取用户权限
    permissions = get_user_permissions(db, user)
    
    # 获取角色信息
    role_name = None
    role_code = None
    if user.role_id:
        role = db.query(Role).filter(Role.id == user.role_id).first()
        if role:
            role_name = role.name
            role_code = role.code
    
    # 获取部门信息
    department_name = None
    if user.department_id:
        department = db.query(Department).filter(Department.id == user.department_id).first()
        if department:
            department_name = department.name
    
    # 构建用户数据
    user_data = {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "full_name": user.full_name,
        "phone": user.phone,
        "employee_id": user.employee_id,
        "position": user.position,
        "is_active": user.is_active,
        "is_superuser": user.is_superuser,
        "account_status": user.account_status,
        "password_status": user.password_status,
        "department_id": user.department_id,
        "department": department_name,
        "role_id": user.role_id,
        "role_name": role_name,
        "role_code": role_code,
        "permissions": permissions,
        "last_login_at": user.last_login_at,
        "created_at": user.created_at
    }
    
    return user_data

def require_permissions(required_permissions: List[str]):
    """权限检查装饰器 - 用于FastAPI依赖注入"""
    def permission_checker(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
        if not current_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user"
            )
        
        if not has_any_permission(current_user, required_permissions, db):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Operation requires one of the following permissions: {', '.join(required_permissions)}"
            )
        
        return current_user
    
    return permission_checker

def require_any(roles: List[str] = None, permissions: List[str] = None):
    """混合检查装饰器：角色或权限任意满足一个即可"""
    def checker(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
        if not current_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user"
            )
        
        # 检查角色
        if roles:
                    user_role = None
        if current_user.role_id:
            user_role = db.query(Role).filter(Role.id == current_user.role_id).first()
        if user_role and user_role.code in roles:
                return current_user
        
        # 检查权限
        if permissions:
            if has_any_permission(current_user, permissions, db):
                return current_user
        
        # 构建错误消息
        requirements = []
        if roles:
            requirements.append(f"roles: {', '.join(roles)}")
        if permissions:
            requirements.append(f"permissions: {', '.join(permissions)}")
        
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Operation requires one of the following: {' or '.join(requirements)}"
        )
    
    return checker

def check_account_status(user: User) -> bool:
    """检查账号状态"""
    if not user.is_active:
        return False
    
    if user.account_status != "active":
        return False
    
    if user.locked_until:
        if user.locked_until > datetime.now(timezone.utc):
            return False
    
    return True

def update_login_info(db: Session, user: User, ip_address: str = None, success: bool = True):
    """更新用户登录信息"""
    try:
        if success:
            # 登录成功
            user.last_login_at = datetime.now(timezone.utc)
            user.login_attempts = 0
            user.locked_until = None
            
            if user.password_status == "temporary":
                user.password_status = "need_reset"
        else:
            # 登录失败
            user.login_attempts = (user.login_attempts or 0) + 1
            
            # 连续失败5次锁定30分钟
            if user.login_attempts >= 5:
                user.locked_until = datetime.now(timezone.utc) + timedelta(minutes=30)
                user.account_status = "locked"
        
        db.commit()
        
        # 记录审计日志
        log_action(db, user.id, "login_success" if success else "login_failed", 
                  "user", user.id, ip_address=ip_address)
        
    except Exception as e:
        db.rollback()
        raise e

def log_action(db: Session, user_id: int, action: str, resource_type: str = None, 
               resource_id: int = None, old_value: str = None, new_value: str = None,
               ip_address: str = None, user_agent: str = None):
    """记录操作审计日志"""
    try:
        audit_log = AuditLog(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            old_value=old_value,
            new_value=new_value,
            ip_address=ip_address,
            user_agent=user_agent
        )
        db.add(audit_log)
        db.commit()
    except Exception:
        # 审计日志记录失败不应该影响主要业务
        db.rollback() 