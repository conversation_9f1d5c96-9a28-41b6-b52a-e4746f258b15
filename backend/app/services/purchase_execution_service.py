from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from typing import List, Dict, Any, Optional
from datetime import datetime
from decimal import Decimal
import uuid
import logging

from app.models.purchase import (
    PurchaseRequest, PurchaseRequestItem, PurchaseExecutionBatch, 
    PurchaseExecutionItem, RequestFlowHistory
)
from app.models.supplier import Supplier
from app.schemas.purchase_execution import (
    ExecutionBatchCreateRequest, ExecutionBatchFilters,
    ExecutionBatchResponse, ExecutionItemResponse,
    BatchExecutionPreviewResponse
)
from app.core.exceptions import BusinessException
# from app.services.supplier_service import SupplierService


class PurchaseExecutionService:
    """采购执行服务"""
    
    def __init__(self, db: Session):
        self.db = db
        # self.supplier_service = SupplierService(db)
    
    def validate_request_executable(self, request_id: int) -> Dict[str, Any]:
        """验证采购申请是否可以执行（检查价格锁定状态）"""
        try:
            request = self.db.query(PurchaseRequest).filter(
                PurchaseRequest.id == request_id,
                PurchaseRequest.status == "approved"
            ).first()
            
            if not request:
                return {
                    "executable": False,
                    "error_message": "采购申请不存在或状态不是已批准"
                }
            
            validation_errors = []
            
            for item in request.items:
                if not item.final_unit_price or not item.final_total_price:
                    validation_errors.append(
                        f"物品 '{item.item_name}' (编码: {item.item_code}) 价格未锁定"
                    )
                elif not item.final_supplier_id:
                    validation_errors.append(
                        f"物品 '{item.item_name}' (编码: {item.item_code}) 供应商未确定"
                    )
            
            if validation_errors:
                return {
                    "executable": False,
                    "error_message": "以下问题导致无法执行采购：\n" + "\n".join(validation_errors)
                }
            
            return {
                "executable": True,
                "error_message": ""
            }
            
        except Exception as e:
            logging.error(f"验证采购申请可执行性失败: {str(e)}")
            return {
                "executable": False,
                "error_message": f"验证过程中发生系统错误: {str(e)}"
            }
    
    def get_approved_requests(self, filters: Dict[str, Any] = None, page: int = 1, size: int = 20) -> Dict[str, Any]:
        """获取已批准的采购申请列表"""
        try:
            query = self.db.query(PurchaseRequest).filter(
                PurchaseRequest.status == "approved"
            )
            
            # 应用筛选条件
            if filters:
                if filters.get("department_id"):
                    query = query.filter(PurchaseRequest.department_id == filters["department_id"])
                if filters.get("submitter_id"):
                    query = query.filter(PurchaseRequest.submitter_id == filters["submitter_id"])
                if filters.get("start_date") and filters.get("end_date"):
                    query = query.filter(and_(
                        PurchaseRequest.submitted_at >= filters["start_date"],
                        PurchaseRequest.submitted_at <= filters["end_date"]
                    ))
            
            # 排除已执行的申请
            executed_request_ids = self.db.query(PurchaseExecutionItem.request_id).distinct().subquery()
            query = query.filter(~PurchaseRequest.id.in_(executed_request_ids))
            
            total = query.count()
            
            # 分页
            offset = (page - 1) * size
            requests = query.order_by(desc(PurchaseRequest.submitted_at)).offset(offset).limit(size).all()
            
            # 转换为响应格式
            request_list = []
            for request in requests:
                request_dict = {
                    "id": request.id,
                    "request_no": request.request_no,
                    "department_id": request.department_id,
                    "submitter_id": request.submitter_id,
                    "status": request.status,
                    "final_total": float(request.final_total) if request.final_total else 0.0,
                    "submitted_at": request.submitted_at.isoformat() if request.submitted_at else None,
                    "created_at": request.created_at.isoformat() if request.created_at else None,
                    "items": []
                }
                
                # 添加申请明细
                for item in request.items:
                    item_dict = {
                        "id": item.id,
                        "item_id": item.item_id,
                        "item_code": item.item_code,
                        "item_name": item.item_name,
                        "spq_quantity": float(item.spq_quantity),
                        "spq_count": item.spq_count,
                        "spq_unit": item.spq_unit,
                        "final_unit_price": float(item.final_unit_price) if item.final_unit_price else None,
                        "final_total_price": float(item.final_total_price) if item.final_total_price else None,
                        "final_supplier_id": item.final_supplier_id
                    }
                    request_dict["items"].append(item_dict)
                
                request_list.append(request_dict)
            
            return {
                "requests": request_list,
                "total": total,
                "page": page,
                "size": size
            }
            
        except Exception as e:
            logging.error(f"获取已批准申请列表失败: {str(e)}")
            raise BusinessException(f"获取已批准申请列表失败: {str(e)}")
    
    def preview_batch_execution(self, request_ids: List[int]) -> BatchExecutionPreviewResponse:
        """预览批量执行"""
        try:
            # 验证申请状态
            requests = self.db.query(PurchaseRequest).filter(
                PurchaseRequest.id.in_(request_ids),
                PurchaseRequest.status == "approved"
            ).all()
            
            if len(requests) != len(request_ids):
                raise BusinessException("部分申请不存在或状态不正确")
            
            # 检查是否已执行
            executed_request_ids = self.db.query(PurchaseExecutionItem.request_id).filter(
                PurchaseExecutionItem.request_id.in_(request_ids)
            ).distinct().all()
            
            if executed_request_ids:
                executed_ids = [r[0] for r in executed_request_ids]
                raise BusinessException(f"以下申请已被执行: {executed_ids}")
            
            # 计算统计信息
            total_amount = Decimal(0)
            supplier_summary = {}
            requests_preview = []
            warnings = []
            
            for request in requests:
                request_dict = {
                    "id": request.id,
                    "request_no": request.request_no,
                    "final_total": float(request.final_total) if request.final_total else 0.0,
                    "items_count": len(request.items)
                }
                
                # 检查价格锁定
                for item in request.items:
                    if not item.final_unit_price or not item.final_total_price:
                        warnings.append(f"申请 {request.request_no} 的物品 {item.item_name} 价格未锁定")
                        continue
                    
                    total_amount += item.final_total_price
                    
                    # 按供应商汇总
                    supplier_id = item.final_supplier_id
                    if supplier_id:
                        if supplier_id not in supplier_summary:
                            supplier_summary[supplier_id] = {
                                "supplier_id": supplier_id,
                                "supplier_name": "",
                                "total_amount": Decimal(0),
                                "items_count": 0
                            }
                        supplier_summary[supplier_id]["total_amount"] += item.final_total_price
                        supplier_summary[supplier_id]["items_count"] += 1
                
                requests_preview.append(request_dict)
            
            # 获取供应商名称 (暂时跳过，直接使用ID)
            # supplier_ids = list(supplier_summary.keys())
            # if supplier_ids:
            #     suppliers = self.supplier_service.get_suppliers_by_ids(supplier_ids)
            #     for supplier in suppliers:
            #         if supplier.id in supplier_summary:
            #             supplier_summary[supplier.id]["supplier_name"] = supplier.name
            
            # 生成预估批次编号
            estimated_batch_no = self._generate_batch_no()
            
            return BatchExecutionPreviewResponse(
                request_count=len(requests),
                total_amount=total_amount,
                estimated_batch_no=estimated_batch_no,
                requests_preview=requests_preview,
                supplier_summary=list(supplier_summary.values()),
                warnings=warnings
            )
            
        except Exception as e:
            logging.error(f"预览批量执行失败: {str(e)}")
            raise BusinessException(f"预览批量执行失败: {str(e)}")
    
    def execute_batch(self, request_ids: List[int], executor_id: int, executor_name: str, 
                     batch_name: str = None, notes: str = None) -> ExecutionBatchResponse:
        """执行批量采购申请"""
        try:
            # 验证申请状态
            requests = self.db.query(PurchaseRequest).filter(
                PurchaseRequest.id.in_(request_ids),
                PurchaseRequest.status == "approved"
            ).all()
            
            logging.info(f"加载了 {len(requests)} 个已批准的申请")
            for req in requests:
                logging.info(f"申请 {req.request_no} (ID: {req.id}): status={req.status}, items_count={len(req.items)}")
            
            if len(requests) != len(request_ids):
                raise BusinessException("部分申请不存在或状态不正确")
            
            # 检查是否已执行
            executed_request_ids = self.db.query(PurchaseExecutionItem.request_id).filter(
                PurchaseExecutionItem.request_id.in_(request_ids)
            ).distinct().all()
            
            if executed_request_ids:
                executed_ids = [r[0] for r in executed_request_ids]
                raise BusinessException(f"以下申请已被执行: {executed_ids}")
            
            # 验证所有申请都可以执行（价格已锁定）
            for request in requests:
                validation_result = self.validate_request_executable(request.id)
                if not validation_result["executable"]:
                    raise BusinessException(
                        f"申请 {request.request_no} 无法执行：{validation_result['error_message']}"
                    )
            
            # 生成批次编号
            batch_no = self._generate_batch_no()
            executed_at = datetime.now()
            
            if not batch_name:
                batch_name = f"批次执行_{executed_at.strftime('%Y%m%d_%H%M%S')}"
            
            # 计算总金额
            total_amount = Decimal(0)
            for request in requests:
                if request.final_total:
                    total_amount += request.final_total
            
            # 创建执行批次
            execution_batch = PurchaseExecutionBatch(
                batch_no=batch_no,
                batch_name=batch_name,
                executor_id=executor_id,
                executor_name=executor_name,
                executed_at=executed_at,
                request_count=len(requests),
                total_amount=total_amount,
                status="active",
                notes=notes
            )
            
            self.db.add(execution_batch)
            self.db.flush()  # 获取ID
            
            # 创建执行明细
            execution_items = []
            for request in requests:
                for item in request.items:
                    # 验证价格锁定
                    if not item.final_unit_price or not item.final_total_price:
                        raise BusinessException(f"申请 {request.request_no} 的物品 {item.item_name} 价格未锁定")
                    
                    # 获取供应商信息
                    supplier_name = ""
                    if item.final_supplier_id:
                        # 直接查询供应商表获取名称
                        supplier = self.db.query(Supplier).filter(Supplier.id == item.final_supplier_id).first()
                        if supplier:
                            supplier_name = supplier.name_cn or supplier.name_en or f"供应商_{item.final_supplier_id}"
                        else:
                            supplier_name = f"供应商_{item.final_supplier_id}"
                    else:
                        supplier_name = "未分配供应商"
                    
                    execution_item = PurchaseExecutionItem(
                        batch_id=execution_batch.id,
                        request_id=request.id,
                        request_item_id=item.id,
                        item_id=item.item_id,
                        item_code=item.item_code,
                        item_name=item.item_name,
                        spq_quantity=item.spq_quantity,
                        spq_count=item.spq_count,
                        spq_unit=item.spq_unit,
                        unit_price=item.final_unit_price,
                        total_price=item.final_total_price,
                        supplier_id=item.final_supplier_id,
                        supplier_name=supplier_name,
                        price_locked_at=item.price_locked_at
                    )
                    execution_items.append(execution_item)
                    self.db.add(execution_item)
                
                # 更新申请状态为已执行
                request.status = "executed"
                request.updated_at = datetime.now()
                
                # 添加流转历史
                flow_history = RequestFlowHistory(
                    request_id=request.id,
                    action="execute",
                    from_status="approved",
                    to_status="executed",
                    operator_id=executor_id,
                    operator_name=executor_name,
                    comments=f"批量执行，批次编号: {batch_no}"
                )
                self.db.add(flow_history)
            
            self.db.commit()
            
            logging.info(f"批量执行成功: 批次编号={batch_no}, 申请数量={len(requests)}, 执行人={executor_name}")
            
            # 返回执行结果
            execution_batch.execution_items = execution_items
            
            # 手动转换数据，确保类型正确
            batch_dict = {
                "id": execution_batch.id,
                "batch_no": execution_batch.batch_no,
                "batch_name": execution_batch.batch_name,
                "executor_id": execution_batch.executor_id,
                "executor_name": execution_batch.executor_name,
                "executed_at": execution_batch.executed_at,
                "request_count": execution_batch.request_count,
                "total_amount": float(execution_batch.total_amount) if execution_batch.total_amount else 0.0,
                "status": execution_batch.status,
                "notes": execution_batch.notes,
                "created_at": execution_batch.created_at,
                "updated_at": execution_batch.updated_at,
                "execution_items": []
            }
            
            # 转换执行明细数据
            for item in execution_items:
                item_dict = {
                    "id": item.id,
                    "batch_id": item.batch_id,
                    "request_id": item.request_id,
                    "request_item_id": item.request_item_id,
                    "item_id": item.item_id,
                    "item_code": item.item_code,
                    "item_name": item.item_name,
                    "spq_quantity": float(item.spq_quantity) if item.spq_quantity else 0.0,
                    "spq_count": item.spq_count,
                    "spq_unit": item.spq_unit,
                    "unit_price": float(item.unit_price) if item.unit_price else 0.0,
                    "total_price": float(item.total_price) if item.total_price else 0.0,
                    "supplier_id": item.supplier_id,
                    "supplier_name": item.supplier_name,
                    "price_locked_at": item.price_locked_at,
                    "created_at": item.created_at
                }
                batch_dict["execution_items"].append(ExecutionItemResponse(**item_dict))
            
            return ExecutionBatchResponse(**batch_dict)
            
        except Exception as e:
            self.db.rollback()
            logging.error(f"批量执行失败: {str(e)}")
            raise BusinessException(f"批量执行失败: {str(e)}")
    
    def get_execution_batches(self, filters: ExecutionBatchFilters = None, 
                            page: int = 1, size: int = 20) -> Dict[str, Any]:
        """获取执行批次列表"""
        try:
            query = self.db.query(PurchaseExecutionBatch)
            
            # 应用筛选条件
            if filters:
                if filters.executor_id:
                    query = query.filter(PurchaseExecutionBatch.executor_id == filters.executor_id)
                if filters.status:
                    query = query.filter(PurchaseExecutionBatch.status == filters.status)
                if filters.start_date and filters.end_date:
                    query = query.filter(and_(
                        PurchaseExecutionBatch.executed_at >= filters.start_date,
                        PurchaseExecutionBatch.executed_at <= filters.end_date
                    ))
                if filters.batch_no:
                    query = query.filter(PurchaseExecutionBatch.batch_no.like(f"%{filters.batch_no}%"))
            
            total = query.count()
            
            # 分页
            offset = (page - 1) * size
            batches = query.order_by(desc(PurchaseExecutionBatch.executed_at)).offset(offset).limit(size).all()
            
            # 手动转换数据，确保类型正确
            batch_list = []
            for batch in batches:
                # 加载执行明细
                batch_with_items = self.db.query(PurchaseExecutionBatch).options(
                    joinedload(PurchaseExecutionBatch.execution_items)
                ).filter(PurchaseExecutionBatch.id == batch.id).first()
                
                # 转换执行明细数据
                execution_items = []
                if batch_with_items and batch_with_items.execution_items:
                    for item in batch_with_items.execution_items:
                        item_dict = {
                            "id": item.id,
                            "batch_id": item.batch_id,
                            "request_id": item.request_id,
                            "request_item_id": item.request_item_id,
                            "item_id": item.item_id,
                            "item_code": item.item_code,
                            "item_name": item.item_name,
                            "spq_quantity": float(item.spq_quantity) if item.spq_quantity else 0.0,
                            "spq_count": item.spq_count,
                            "spq_unit": item.spq_unit,
                            "unit_price": float(item.unit_price) if item.unit_price else 0.0,
                            "total_price": float(item.total_price) if item.total_price else 0.0,
                            "supplier_id": item.supplier_id,
                            "supplier_name": item.supplier_name,
                            "price_locked_at": item.price_locked_at,
                            "created_at": item.created_at
                        }
                        execution_items.append(ExecutionItemResponse(**item_dict))
                
                batch_dict = {
                    "id": batch.id,
                    "batch_no": batch.batch_no,
                    "batch_name": batch.batch_name,
                    "executor_id": batch.executor_id,
                    "executor_name": batch.executor_name,
                    "executed_at": batch.executed_at,
                    "request_count": batch.request_count,
                    "total_amount": float(batch.total_amount) if batch.total_amount else 0.0,
                    "status": batch.status,
                    "notes": batch.notes,
                    "created_at": batch.created_at,
                    "updated_at": batch.updated_at,
                    "execution_items": execution_items
                }
                batch_list.append(ExecutionBatchResponse(**batch_dict))
            
            return {
                "batches": batch_list,
                "total": total,
                "page": page,
                "size": size
            }
            
        except Exception as e:
            logging.error(f"获取执行批次列表失败: {str(e)}")
            raise BusinessException(f"获取执行批次列表失败: {str(e)}")
    
    def get_execution_batch_detail(self, batch_id: int) -> ExecutionBatchResponse:
        """获取执行批次详情"""
        try:
            # 使用 joinedload 来加载关联的执行明细
            
            batch = self.db.query(PurchaseExecutionBatch).options(
                joinedload(PurchaseExecutionBatch.execution_items)
            ).filter(
                PurchaseExecutionBatch.id == batch_id
            ).first()
            
            if not batch:
                raise BusinessException("执行批次不存在")
            
            # 转换执行明细数据
            execution_items = []
            logging.info(f"Batch execution_items count: {len(batch.execution_items) if batch.execution_items else 0}")
            if batch.execution_items:
                for item in batch.execution_items:
                    logging.info(f"Processing execution item: {item.id}, item_name: {item.item_name}")
                    item_dict = {
                        "id": item.id,
                        "batch_id": item.batch_id,
                        "request_id": item.request_id,
                        "request_item_id": item.request_item_id,
                        "item_id": item.item_id,
                        "item_code": item.item_code,
                        "item_name": item.item_name,
                        "spq_quantity": float(item.spq_quantity) if item.spq_quantity else 0.0,
                        "spq_count": item.spq_count,
                        "spq_unit": item.spq_unit,
                        "unit_price": float(item.unit_price) if item.unit_price else 0.0,
                        "total_price": float(item.total_price) if item.total_price else 0.0,
                        "supplier_id": item.supplier_id,
                        "supplier_name": item.supplier_name,
                        "price_locked_at": item.price_locked_at,
                        "created_at": item.created_at
                    }
                    execution_items.append(ExecutionItemResponse(**item_dict))
            else:
                logging.warning(f"No execution items found for batch {batch_id}")
            
            # 手动转换数据，确保类型正确
            batch_dict = {
                "id": batch.id,
                "batch_no": batch.batch_no,
                "batch_name": batch.batch_name,
                "executor_id": batch.executor_id,
                "executor_name": batch.executor_name,
                "executed_at": batch.executed_at,
                "request_count": batch.request_count,
                "total_amount": float(batch.total_amount) if batch.total_amount else 0.0,
                "status": batch.status,
                "notes": batch.notes,
                "created_at": batch.created_at,
                "updated_at": batch.updated_at,
                "execution_items": execution_items
            }
            return ExecutionBatchResponse(**batch_dict)
            
        except Exception as e:
            logging.error(f"获取执行批次详情失败: {str(e)}")
            raise BusinessException(f"获取执行批次详情失败: {str(e)}")
    
    def get_execution_summary(self, batch_ids: List[int] = None) -> Dict[str, Any]:
        """获取执行汇总数据"""
        try:
            query = self.db.query(PurchaseExecutionItem)
            
            if batch_ids:
                query = query.filter(PurchaseExecutionItem.batch_id.in_(batch_ids))
            
            execution_items = query.all()
            
            # 按供应商汇总
            supplier_summary = {}
            category_summary = {}
            total_amount = Decimal(0)
            unique_suppliers = set()
            unique_items = set()
            
            for item in execution_items:
                # 供应商汇总
                supplier_id = item.supplier_id
                if supplier_id not in supplier_summary:
                    supplier_summary[supplier_id] = {
                        "supplier_id": supplier_id,
                        "supplier_name": item.supplier_name,
                        "total_amount": Decimal(0),
                        "items_count": 0
                    }
                supplier_summary[supplier_id]["total_amount"] += item.total_price
                supplier_summary[supplier_id]["items_count"] += 1
                
                total_amount += item.total_price
                unique_suppliers.add(supplier_id)
                unique_items.add(item.item_id)
            
            return {
                "supplier_summary": list(supplier_summary.values()),
                "category_summary": list(category_summary.values()),
                "time_summary": {},
                "total_requests": len(set(item.request_id for item in execution_items)),
                "total_amount": total_amount,
                "unique_suppliers": len(unique_suppliers),
                "unique_items": len(unique_items)
            }
            
        except Exception as e:
            logging.error(f"获取执行汇总数据失败: {str(e)}")
            raise BusinessException(f"获取执行汇总数据失败: {str(e)}")
    
    def _generate_batch_no(self) -> str:
        """生成批次编号"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_str = str(uuid.uuid4())[:8].upper()
        return f"EXEC{timestamp}{random_str}"
