"""
员工工号生成服务
基于序列号自动生成唯一工号，支持并发安全
"""

from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.models.user import User
from app.core.database import get_db


class EmployeeCodeService:
    """员工工号生成服务"""
    
    @staticmethod
    def generate_employee_code(db: Session) -> str:
        """
        生成员工工号
        
        Args:
            db: 数据库会话
            
        Returns:
            生成的员工工号
        """
        # 获取当前最大工号
        max_code = db.query(User.employee_id).filter(
            User.employee_id.like("EMP%"),
            User.employee_id.isnot(None)
        ).order_by(User.employee_id.desc()).first()
        
        if max_code and max_code[0]:
            # 提取数字部分
            try:
                current_num = int(max_code[0][3:])  # 去掉EMP前缀
                next_num = current_num + 1
            except (ValueError, IndexError):
                next_num = 1
        else:
            next_num = 1
        
        return f"EMP{next_num:06d}"
    
    @staticmethod
    def validate_employee_code(code: str) -> bool:
        """
        验证员工工号格式
        
        Args:
            code: 待验证的工号
            
        Returns:
            是否符合格式要求
        """
        if not code:
            return False
        
        # 检查前缀 - 必须以EMP开头
        if not code.startswith("EMP"):
            return False
        
        # 检查格式 - EMP + 数字部分
        if len(code) < 4:  # EMP + 至少1位数字
            return False
        
        # 检查数字部分
        number_part = code[3:]  # 去掉EMP前缀
        if not number_part.isdigit():
            return False
        
        return True
    
    @staticmethod
    def get_next_employee_code(db: Session) -> str:
        """
        获取下一个员工工号（不更新数据库）
        
        Args:
            db: 数据库会话
            
        Returns:
            下一个员工工号
        """
        return EmployeeCodeService.generate_employee_code(db)
    
    @staticmethod
    def is_employee_code_exists(db: Session, code: str) -> bool:
        """
        检查员工工号是否已存在
        
        Args:
            db: 数据库会话
            code: 员工工号
            
        Returns:
            是否存在
        """
        if not code:
            return False
        
        existing_user = db.query(User).filter(User.employee_id == code).first()
        return existing_user is not None


# 便捷函数
def generate_employee_code() -> str:
    """
    便捷函数：生成员工工号
    
    Returns:
        生成的员工工号
    """
    db = next(get_db())
    try:
        return EmployeeCodeService.generate_employee_code(db)
    finally:
        db.close()


def validate_employee_code(code: str) -> bool:
    """
    便捷函数：验证员工工号格式
    
    Args:
        code: 待验证的工号
        
    Returns:
        是否符合格式要求
    """
    return EmployeeCodeService.validate_employee_code(code)
