from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_
from app.models.purchase import RequestFlowHistory, PurchaseRequest, PurchaseRequestItem
from app.schemas.approval import ReviewRequest, ApprovalRequest, RejectRequest
from app.schemas.purchase_request import PurchaseRequestWithDetails
from app.core.exceptions import BusinessException, ResourceNotFoundException
from app.core.constants import (
    PurchaseRequestStatus, FlowActionType, ApprovalLevel,
    StatusFlow, ProgressCalculator
)
from app.services.notification_integration_service import NotificationIntegrationService
from datetime import datetime
import json
import logging


class ApprovalService:
    """审批流程服务"""

    def __init__(self, db: Session):
        self.db = db

    def submit_for_review(self, request_id: int, approver_id: int, approver_name: str, review_data: ReviewRequest) -> RequestFlowHistory:
        """提交部门经理复核"""
        request = self._get_and_validate_request(request_id, PurchaseRequestStatus.UNDER_REVIEW)
        
        # 创建流转历史记录
        flow_record = RequestFlowHistory(
            request_id=request_id,
            action=FlowActionType.REVIEW,
            from_status=PurchaseRequestStatus.UNDER_REVIEW,
            to_status=PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL if review_data.approved else PurchaseRequestStatus.REJECTED,
            operator_id=approver_id,
            operator_name=approver_name,
            approval_level=ApprovalLevel.REVIEW,
            comments=review_data.comments,
            created_at=datetime.now()
        )
        
        self.db.add(flow_record)
        
        # 更新申请状态
        if review_data.approved:
            request.status = PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL
            # 清除汇率锁定，准备重新锁定
            from app.services.purchase_request_service import PurchaseRequestService
            purchase_service = PurchaseRequestService(self.db)
            purchase_service.clear_exchange_rate_locks_on_review(request_id)
        else:
            request.status = PurchaseRequestStatus.REJECTED
        
        request.updated_at = datetime.now()
        
        self.db.commit()
        self.db.refresh(flow_record)

        # 创建审批通知
        action_type = FlowActionType.APPROVE if review_data.approved else FlowActionType.REJECT
        self._create_approval_notification(request_id, action_type, request.status)

        return flow_record

    def principle_approval(self, request_id: int, approver_id: int, approver_name: str, approval_data: ApprovalRequest) -> RequestFlowHistory:
        """物品管理员主管审批"""
        request = self._get_and_validate_request(request_id, PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL)
        
        # 检查汇率有效性
        self._check_exchange_rate_validity_for_approval(request_id)
        
        # 创建流转历史记录
        flow_record = RequestFlowHistory(
            request_id=request_id,
            action=FlowActionType.PRINCIPLE_APPROVE,
            from_status=PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL,
            to_status=PurchaseRequestStatus.UNDER_FINAL_APPROVAL if approval_data.approved else PurchaseRequestStatus.REJECTED,
            operator_id=approver_id,
            operator_name=approver_name,
            approval_level=ApprovalLevel.PRINCIPLE_APPROVAL,
            comments=approval_data.comments,
            created_at=datetime.now()
        )
        
        self.db.add(flow_record)
        
        # 更新申请状态
        if approval_data.approved:
            request.status = PurchaseRequestStatus.UNDER_FINAL_APPROVAL
            # 锁定汇率（第二阶段锁定）
            from app.services.purchase_request_service import PurchaseRequestService
            purchase_service = PurchaseRequestService(self.db)
            purchase_service.lock_exchange_rates_on_principle_approval(request_id)
        else:
            request.status = PurchaseRequestStatus.REJECTED
        
        request.updated_at = datetime.now()
        
        self.db.commit()
        self.db.refresh(flow_record)

        # 创建审批通知
        action_type = FlowActionType.APPROVE if approval_data.approved else FlowActionType.REJECT
        self._create_approval_notification(request_id, action_type, request.status)

        return flow_record

    def final_approval(self, request_id: int, approver_id: int, approver_name: str, approval_data: ApprovalRequest) -> RequestFlowHistory:
        """公司主管最终审批"""
        request = self._get_and_validate_request(request_id, PurchaseRequestStatus.UNDER_FINAL_APPROVAL)
        
        # 如果是要批准，先验证所有物品都有有效的供应商价格
        if approval_data.approved:
            try:
                from app.services.purchase_request_service import PurchaseRequestService
                purchase_service = PurchaseRequestService(self.db)
                
                # 验证价格配置 - 在审批前检查，而不是审批后
                validation_result = purchase_service._validate_supplier_prices(request.id)
                if not validation_result["is_valid"]:
                    raise BusinessException(
                        f"无法批准采购申请：{validation_result['error_message']}。"
                        f"请先配置有效的供应商价格后再进行审批。"
                    )
                
            except BusinessException:
                # 重新抛出业务异常，不包装
                raise
            except Exception as e:
                logging.error(f"申请 {request_id} 价格验证失败: {str(e)}")
                raise BusinessException(
                    f"价格验证过程中发生系统错误，请联系管理员。错误详情: {str(e)}"
                )
        
        # 创建流转历史记录
        flow_record = RequestFlowHistory(
            request_id=request_id,
            action=FlowActionType.FINAL_APPROVE,
            from_status=PurchaseRequestStatus.UNDER_FINAL_APPROVAL,
            to_status=PurchaseRequestStatus.APPROVED if approval_data.approved else PurchaseRequestStatus.REJECTED,
            operator_id=approver_id,
            operator_name=approver_name,
            approval_level=ApprovalLevel.FINAL_APPROVAL,
            comments=approval_data.comments,
            created_at=datetime.now()
        )
        
        self.db.add(flow_record)
        
        # 更新申请状态
        if approval_data.approved:
            request.status = PurchaseRequestStatus.APPROVED
            
            # 触发价格锁定流程 - 此时价格验证已经通过，锁定应该成功
            # 注意：汇率已经在principle approval时锁定，这里不需要重复锁定
            try:
                logging.info(f"开始为申请 {request_id} 锁定价格")
                purchase_service._lock_final_prices(request.id)
                logging.info(f"申请 {request_id} 价格锁定成功")
            except Exception as e:
                logging.error(f"申请 {request_id} 价格锁定失败: {str(e)}")
                # 价格锁定失败应该回滚审批状态，因为这是关键步骤
                request.status = PurchaseRequestStatus.UNDER_FINAL_APPROVAL
                self.db.rollback()
                raise BusinessException(
                    f"价格锁定失败，审批已回滚。请检查系统配置后重试。错误详情: {str(e)}"
                )
        else:
            request.status = PurchaseRequestStatus.REJECTED
        
        request.updated_at = datetime.now()
        
        self.db.commit()
        self.db.refresh(flow_record)

        # 创建审批通知
        action_type = FlowActionType.APPROVE if approval_data.approved else FlowActionType.REJECT
        self._create_approval_notification(request_id, action_type, request.status)

        return flow_record

    def reject_request(self, request_id: int, approver_id: int, approver_name: str, reject_data: RejectRequest) -> RequestFlowHistory:
        """拒绝申请"""
        request = self._get_and_validate_request(request_id)
        
        # 检查当前状态是否允许拒绝
        if request.status not in [PurchaseRequestStatus.UNDER_REVIEW, PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL, PurchaseRequestStatus.UNDER_FINAL_APPROVAL]:
            raise BusinessException("当前状态不允许拒绝")
        
        # 创建流转历史记录
        flow_record = RequestFlowHistory(
            request_id=request_id,
            action=FlowActionType.REJECT,
            from_status=request.status,
            to_status=PurchaseRequestStatus.REJECTED,
            operator_id=approver_id,
            operator_name=approver_name,
            approval_level=self._get_current_approval_level(request.status),
            comments=reject_data.comments,
            created_at=datetime.now()
        )
        
        self.db.add(flow_record)
        
        # 更新申请状态
        request.status = PurchaseRequestStatus.REJECTED
        request.updated_at = datetime.now()
        
        self.db.commit()
        self.db.refresh(flow_record)

        # 创建拒绝通知
        self._create_approval_notification(request_id, FlowActionType.REJECT, PurchaseRequestStatus.REJECTED)

        return flow_record

    def return_request(self, request_id: int, approver_id: int, approver_name: str, return_data: Dict[str, Any]) -> RequestFlowHistory:
        """退回申请"""
        request = self._get_and_validate_request(request_id)
        
        # 检查当前状态是否允许退回
        if request.status not in [PurchaseRequestStatus.UNDER_REVIEW, PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL, PurchaseRequestStatus.UNDER_FINAL_APPROVAL]:
            raise BusinessException("当前状态不允许退回")
        
        # 创建流转历史记录
        flow_record = RequestFlowHistory(
            request_id=request_id,
            action=FlowActionType.RETURN,
            from_status=request.status,
            to_status=PurchaseRequestStatus.PENDING_SUBMISSION,
            operator_id=approver_id,
            operator_name=approver_name,
            approval_level=self._get_current_approval_level(request.status),
            comments=return_data.get("comments"),
            created_at=datetime.now()
        )
        
        self.db.add(flow_record)
        
        # 更新申请状态
        request.status = PurchaseRequestStatus.PENDING_SUBMISSION
        request.updated_at = datetime.now()
        
        # 清除锁定的货币字段
        from app.services.purchase_request_service import PurchaseRequestService
        purchase_service = PurchaseRequestService(self.db)
        purchase_service.clear_locked_currency_fields(request_id, "退回申请到待提交状态时")
        
        self.db.commit()
        self.db.refresh(flow_record)
        return flow_record

    def get_approval_history(self, request_id: int) -> List[RequestFlowHistory]:
        """获取审批历史"""
        return self.db.query(RequestFlowHistory).filter(
            RequestFlowHistory.request_id == request_id
        ).order_by(RequestFlowHistory.created_at).all()

    def get_pending_approvals(self, approver_id: int, approval_level: str = None) -> List[PurchaseRequest]:
        """获取待审批的申请列表"""
        query = self.db.query(PurchaseRequest)
        
        if approval_level == ApprovalLevel.REVIEW:
            query = query.filter(PurchaseRequest.status == PurchaseRequestStatus.UNDER_REVIEW)
        elif approval_level == ApprovalLevel.PRINCIPLE_APPROVAL:
            query = query.filter(PurchaseRequest.status == PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL)
        elif approval_level == ApprovalLevel.FINAL_APPROVAL:
            query = query.filter(PurchaseRequest.status == PurchaseRequestStatus.UNDER_FINAL_APPROVAL)
        else:
            # 获取所有待审批状态
            query = query.filter(PurchaseRequest.status.in_([
                PurchaseRequestStatus.UNDER_REVIEW, 
                PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL, 
                PurchaseRequestStatus.UNDER_FINAL_APPROVAL
            ]))
        
        return query.order_by(PurchaseRequest.created_at).all()

    def _check_exchange_rate_validity_for_approval(self, request_id: int) -> None:
        """检查采购申请中使用的汇率在审批时是否仍然有效"""
        from app.services.exchange_rate_service import ExchangeRateService
        
        # 获取采购申请及其汇率记录
        request = self.db.query(PurchaseRequest).filter(PurchaseRequest.id == request_id).first()
        if not request:
            raise BusinessException("采购申请不存在")
        
        # 获取汇率记录
        exchange_rate_service = ExchangeRateService(self.db)
        exchange_rate_records = exchange_rate_service.get_purchase_request_exchange_rates(request_id)
        
        if not exchange_rate_records:
            # 如果没有汇率记录，说明可能都是USD，无需检查
            return
        
        # 检查每个汇率记录的有效性
        current_date = datetime.now().date()
        invalid_currencies = []
        
        for record in exchange_rate_records:
            if record.currency_code == "USD":
                continue  # USD不需要汇率转换
                
            # 检查汇率是否仍然有效
            validity = exchange_rate_service.check_exchange_rate_validity(
                record.currency_code, 
                current_date
            )
            
            if not validity["is_valid"]:
                invalid_currencies.append(record.currency_code)
        
        # 如果有无效的汇率，抛出异常阻止审批
        if invalid_currencies:
            currency_list = ", ".join(invalid_currencies)
            raise BusinessException(
                f"采购申请中包含无效汇率的货币: {currency_list}。"
                "请更新相关货币的汇率后再进行审批。"
            )
        
        # 检查是否有使用历史汇率的情况，给出警告但不阻止审批
        warnings = []
        for record in exchange_rate_records:
            if record.currency_code == "USD":
                continue
                
            validity = exchange_rate_service.check_exchange_rate_validity(
                record.currency_code, 
                current_date
            )
            
            if not validity["has_current_month_rate"]:
                warnings.append(
                    f"货币 {record.currency_code} 使用历史汇率 "
                    f"({validity['fallback_month'].strftime('%Y-%m')})，"
                    "建议更新当前月汇率"
                )
        
        # 记录警告信息到日志
        if warnings:
            logging.warning(f"采购申请 {request_id} 汇率检查警告: {'; '.join(warnings)}")

    def _get_current_approval_level(self, status: str) -> str:
        """根据状态获取当前审批级别"""
        level_mapping = {
            PurchaseRequestStatus.UNDER_REVIEW: ApprovalLevel.REVIEW,
            PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL: ApprovalLevel.PRINCIPLE_APPROVAL,
            PurchaseRequestStatus.UNDER_FINAL_APPROVAL: ApprovalLevel.FINAL_APPROVAL
        }
        return level_mapping.get(status, "unknown")

    def _get_and_validate_request(self, request_id: int, expected_status: str = None) -> PurchaseRequest:
        """获取并验证申请"""
        request = self.db.query(PurchaseRequest).filter(PurchaseRequest.id == request_id).first()
        if not request:
            raise BusinessException("采购申请不存在")
        
        if expected_status and request.status != expected_status:
            raise BusinessException(f"申请状态不正确，期望: {expected_status}，实际: {request.status}")
        
        return request

    def submit_request(self, request_id: int, submitter_id: int) -> PurchaseRequestWithDetails:
        """提交采购申请进入审批流程"""
        request = self._get_and_validate_request(request_id, PurchaseRequestStatus.PENDING_SUBMISSION)

        if request.submitter_id != submitter_id:
            raise BusinessException("只能提交自己创建的申请")

        # 检查汇率有效性
        from app.services.purchase_request_service import PurchaseRequestService
        purchase_service = PurchaseRequestService(self.db)
        purchase_service._check_exchange_rate_validity(request_id)

        # 获取操作人姓名
        from app.models.user import User
        operator = self.db.query(User).filter(User.id == submitter_id).first()
        operator_name = operator.full_name or operator.username if operator else "未知用户"

        # 创建流转历史记录
        flow_record = RequestFlowHistory(
            request_id=request_id,
            action=FlowActionType.SUBMIT,
            from_status=PurchaseRequestStatus.PENDING_SUBMISSION,
            to_status=PurchaseRequestStatus.UNDER_REVIEW,
            operator_id=submitter_id,
            operator_name=operator_name,
            comments="申请人提交申请",
            created_at=datetime.now()
        )

        self.db.add(flow_record)

        # 更新申请状态
        request.status = PurchaseRequestStatus.UNDER_REVIEW
        request.updated_at = datetime.now()

        # 锁定汇率（第一阶段锁定）
        purchase_service.lock_exchange_rates_on_submit(request_id)

        self.db.commit()
        self.db.refresh(request)

        # 创建提交通知
        self._create_submission_notification(request_id, submitter_id)

        # 返回详细信息
        return purchase_service.get_request_by_id(request_id)

    def withdraw_request(self, request_id: int, user_id: int) -> PurchaseRequestWithDetails:
        """撤回采购申请"""
        request = self._get_and_validate_request(request_id)

        if request.submitter_id != user_id:
            raise BusinessException("只能撤回自己提交的申请")

        if request.status not in [PurchaseRequestStatus.PENDING_SUBMISSION, PurchaseRequestStatus.UNDER_REVIEW]:
            raise BusinessException("当前状态不允许撤回")

        # 获取操作人姓名
        from app.models.user import User
        operator = self.db.query(User).filter(User.id == user_id).first()
        operator_name = operator.full_name or operator.username if operator else "未知用户"

        # 创建流转历史记录
        flow_record = RequestFlowHistory(
            request_id=request_id,
            action=FlowActionType.WITHDRAW,
            from_status=request.status,
            to_status=PurchaseRequestStatus.WITHDRAWN,
            operator_id=user_id,
            operator_name=operator_name,
            comments="申请人撤回申请",
            created_at=datetime.now()
        )

        self.db.add(flow_record)

        # 更新申请状态
        request.status = PurchaseRequestStatus.WITHDRAWN
        request.updated_at = datetime.now()

        self.db.commit()
        self.db.refresh(request)

        # 返回详细信息
        from app.services.purchase_request_service import PurchaseRequestService
        purchase_service = PurchaseRequestService(self.db)
        return purchase_service.get_request_by_id(request_id)

    def withdraw_to_pending(self, request_id: int, user_id: int) -> PurchaseRequestWithDetails:
        """撤销申请回到待提交状态"""
        request = self._get_and_validate_request(request_id)

        if request.submitter_id != user_id:
            raise BusinessException("只能撤销自己提交的申请")

        # 允许从待审批状态撤销回待提交状态
        if request.status not in [PurchaseRequestStatus.UNDER_REVIEW, PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL,
                                 PurchaseRequestStatus.UNDER_FINAL_APPROVAL, PurchaseRequestStatus.REJECTED]:
            raise BusinessException("当前状态不允许撤销")

        # 获取操作人姓名
        from app.models.user import User
        operator = self.db.query(User).filter(User.id == user_id).first()
        operator_name = operator.full_name or operator.username if operator else "未知用户"

        # 创建流转历史记录
        flow_record = RequestFlowHistory(
            request_id=request_id,
            action=FlowActionType.WITHDRAW_TO_PENDING,
            from_status=request.status,
            to_status=PurchaseRequestStatus.PENDING_SUBMISSION,
            operator_id=user_id,
            operator_name=operator_name,
            comments="申请人撤销回待提交状态",
            created_at=datetime.now()
        )

        self.db.add(flow_record)

        # 更新申请状态
        request.status = PurchaseRequestStatus.PENDING_SUBMISSION
        request.updated_at = datetime.now()

        # 清除锁定的货币字段
        from app.services.purchase_request_service import PurchaseRequestService
        purchase_service = PurchaseRequestService(self.db)
        purchase_service.clear_locked_currency_fields(request_id, "撤销回待提交状态时")

        self.db.commit()
        self.db.refresh(request)

        # 返回详细信息
        return purchase_service.get_request_by_id(request_id)

    def update_request_status(self, request_id: int, new_status: str, operator_id: int = 1,
                             operator_name: str = "系统", action: str = "status_update",
                             comments: str = "") -> PurchaseRequestWithDetails:
        """通用状态更新方法"""
        request = self._get_and_validate_request(request_id)

        current_status = request.status

        # 创建流转历史记录
        flow_record = RequestFlowHistory(
            request_id=request_id,
            action=action,
            from_status=current_status,
            to_status=new_status,
            operator_id=operator_id,
            operator_name=operator_name,
            comments=comments,
            created_at=datetime.now()
        )

        self.db.add(flow_record)

        # 更新申请状态
        request.status = new_status
        request.updated_at = datetime.now()

        # 如果是提交操作，锁定汇率（第一阶段锁定）
        if new_status == PurchaseRequestStatus.UNDER_REVIEW and action == FlowActionType.SUBMIT:
            from app.services.purchase_request_service import PurchaseRequestService
            purchase_service = PurchaseRequestService(self.db)
            purchase_service.lock_exchange_rates_on_submit(request_id)

        self.db.commit()
        self.db.refresh(request)

        # 返回详细信息
        from app.services.purchase_request_service import PurchaseRequestService
        purchase_service = PurchaseRequestService(self.db)
        return purchase_service.get_request_by_id(request_id)

    def _create_submission_notification(self, request_id: int, submitter_id: int):
        """创建申请提交通知"""
        try:
            notification_service = NotificationIntegrationService(self.db)

            # 获取申请物品名称（取第一个物品作为代表）
            first_item = self.db.query(PurchaseRequestItem).filter(
                PurchaseRequestItem.request_id == request_id
            ).first()

            if first_item:
                item_name = first_item.item_name
            else:
                item_name = "未知物品"

            # 为申请人创建通知
            notification_service.create_approval_notification(
                user_id=submitter_id,
                approval_type="purchase_request",
                item_name=item_name,
                request_id=request_id,
                status="已提交"
            )

        except Exception as e:
            # 通知创建失败不影响申请流程
            logging.warning(f"创建采购申请提交通知失败: {str(e)}")

    def _create_approval_notification(self, request_id: int, action: str, new_status: str):
        """创建审批通知"""
        try:
            notification_service = NotificationIntegrationService(self.db)

            # 获取申请信息
            request = self.db.query(PurchaseRequest).filter(PurchaseRequest.id == request_id).first()
            if not request:
                return

            # 获取申请物品名称（取第一个物品作为代表）
            first_item = self.db.query(PurchaseRequestItem).filter(
                PurchaseRequestItem.request_id == request_id
            ).first()

            if first_item:
                item_name = first_item.item_name
            else:
                item_name = "未知物品"

            if action == FlowActionType.APPROVE:
                if new_status == PurchaseRequestStatus.APPROVED:
                    # 最终审批通过通知
                    notification_service.create_approval_notification(
                        user_id=request.submitter_id,
                        approval_type="approval_complete",
                        item_name=item_name,
                        request_id=request_id,
                        status="审批通过"
                    )
                else:
                    # 审批状态更新通知
                    notification_service.create_approval_notification(
                        user_id=request.submitter_id,
                        approval_type="approval_status",
                        item_name=item_name,
                        request_id=request_id,
                        status=new_status
                    )
            elif action == FlowActionType.REJECT:
                # 审批拒绝通知
                notification_service.create_approval_notification(
                    user_id=request.submitter_id,
                    approval_type="approval_status",
                    item_name=item_name,
                    request_id=request_id,
                    status="审批拒绝"
                )

        except Exception as e:
            # 通知创建失败不影响审批流程
            logging.warning(f"创建审批通知失败: {str(e)}")
