from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func
from datetime import datetime, date
from decimal import Decimal

from app.models.exchange_rate import ExchangeRate, ExchangeRateLog, PurchaseRequestExchangeRate
from app.schemas.exchange_rate import (
    ExchangeRateCreate, 
    ExchangeRateUpdate, 
    ExchangeRateQuery,
    ExchangeRateHistoryQuery,
    PurchaseRequestExchangeRateCreate
)
from app.core.exceptions import BusinessException, ResourceNotFoundException


class ExchangeRateService:
    """汇率管理服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_exchange_rate(self, exchange_rate_data: ExchangeRateCreate, user_id: int) -> ExchangeRate:
        """创建汇率记录"""
        # 检查同一货币同一月份是否已存在汇率
        existing_rate = self.db.query(ExchangeRate).filter(
            and_(
                ExchangeRate.currency_code == exchange_rate_data.currency_code,
                ExchangeRate.effective_month == exchange_rate_data.effective_month,
                ExchangeRate.status == "active"
            )
        ).first()
        
        if existing_rate:
            raise BusinessException(f"货币 {exchange_rate_data.currency_code} 在 {exchange_rate_data.effective_month} 月份已存在有效汇率")
        
        # 创建汇率记录
        exchange_rate = ExchangeRate(
            **exchange_rate_data.model_dump(),
            created_by=user_id
        )
        
        self.db.add(exchange_rate)
        self.db.commit()
        self.db.refresh(exchange_rate)
        
        return exchange_rate
    
    def update_exchange_rate(self, rate_id: int, update_data: ExchangeRateUpdate, user_id: int) -> ExchangeRate:
        """更新汇率记录"""
        exchange_rate = self.db.query(ExchangeRate).filter(ExchangeRate.id == rate_id).first()
        if not exchange_rate:
            raise ResourceNotFoundException(f"汇率记录 {rate_id} 不存在")
        
        # 记录修改前的汇率值
        old_rate = exchange_rate.rate
        
        # 更新汇率记录
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            if field != "change_reason":
                setattr(exchange_rate, field, value)
        
        exchange_rate.updated_by = user_id
        
        # 创建修改日志
        if "rate" in update_dict:
            log = ExchangeRateLog(
                exchange_rate_id=rate_id,
                old_rate=old_rate,
                new_rate=update_dict["rate"],
                change_reason=update_data.change_reason,
                changed_by=user_id
            )
            self.db.add(log)
        
        self.db.commit()
        self.db.refresh(exchange_rate)
        
        return exchange_rate
    
    def get_exchange_rate(self, rate_id: int) -> ExchangeRate:
        """获取汇率记录"""
        exchange_rate = self.db.query(ExchangeRate).filter(ExchangeRate.id == rate_id).first()
        if not exchange_rate:
            raise ResourceNotFoundException(f"汇率记录 {rate_id} 不存在")
        return exchange_rate
    
    def get_exchange_rates(self, query: ExchangeRateQuery) -> Dict[str, Any]:
        """查询汇率记录列表"""
        query_obj = self.db.query(ExchangeRate)
        
        # 应用过滤条件
        if query.currency_code:
            query_obj = query_obj.filter(ExchangeRate.currency_code == query.currency_code)
        
        if query.effective_month:
            query_obj = query_obj.filter(ExchangeRate.effective_month == query.effective_month)
        
        if query.status:
            query_obj = query_obj.filter(ExchangeRate.status == query.status)
        
        # 计算总数
        total = query_obj.count()
        
        # 分页
        exchange_rates = query_obj.order_by(desc(ExchangeRate.effective_month), desc(ExchangeRate.created_at)) \
            .offset((query.page - 1) * query.size) \
            .limit(query.size) \
            .all()
        
        return {
            "data": exchange_rates,
            "total": total,
            "page": query.page,
            "size": query.size
        }
    
    def get_exchange_rate_by_currency_month(self, currency_code: str, effective_month: date) -> Optional[ExchangeRate]:
        """根据货币代码和生效月份获取汇率"""
        return self.db.query(ExchangeRate).filter(
            and_(
                ExchangeRate.currency_code == currency_code,
                ExchangeRate.effective_month == effective_month,
                ExchangeRate.status == "active"
            )
        ).first()
    
    def get_latest_exchange_rate(self, currency_code: str, target_date: date = None) -> Optional[ExchangeRate]:
        """获取指定货币的最新有效汇率"""
        if target_date is None:
            target_date = date.today()
        
        # 先尝试获取目标月份的有效汇率
        target_month = date(target_date.year, target_date.month, 1)
        rate = self.get_exchange_rate_by_currency_month(currency_code, target_month)
        
        if rate:
            return rate
        
        # 如果目标月份没有汇率，获取最近的有效汇率
        return self.db.query(ExchangeRate).filter(
            and_(
                ExchangeRate.currency_code == currency_code,
                ExchangeRate.status == "active",
                ExchangeRate.effective_month < target_month
            )
        ).order_by(desc(ExchangeRate.effective_month)).first()
    
    def get_exchange_rate_history(self, query: ExchangeRateHistoryQuery) -> List[ExchangeRate]:
        """获取汇率历史记录"""
        if query.start_date is None:
            # 如果没有指定开始日期，使用查询天数计算
            query.start_date = date.today()
        
        if query.end_date is None:
            query.end_date = query.start_date
        
        return self.db.query(ExchangeRate).filter(
            and_(
                ExchangeRate.currency_code == query.currency_code,
                ExchangeRate.effective_month >= query.start_date,
                ExchangeRate.effective_month <= query.end_date,
                ExchangeRate.status == "active"
            )
        ).order_by(ExchangeRate.effective_month).all()
    
    def get_exchange_rate_summary(self) -> List[Dict[str, Any]]:
        """获取汇率汇总信息"""
        # 获取所有货币的最新汇率
        subquery = self.db.query(
            ExchangeRate.currency_code,
            func.max(ExchangeRate.effective_month).label("max_month")
        ).filter(ExchangeRate.status == "active").group_by(ExchangeRate.currency_code).subquery()
        
        exchange_rates = self.db.query(ExchangeRate).join(
            subquery,
            and_(
                ExchangeRate.currency_code == subquery.c.currency_code,
                ExchangeRate.effective_month == subquery.c.max_month
            )
        ).all()
        
        summary = []
        for rate in exchange_rates:
            summary.append({
                "currency_code": rate.currency_code,
                "current_rate": rate.rate,
                "last_updated": rate.updated_at or rate.created_at,
                "status": rate.status,
                "is_valid": rate.status == "active"
            })
        
        return summary
    
    def create_purchase_request_exchange_rate(self, data: PurchaseRequestExchangeRateCreate) -> PurchaseRequestExchangeRate:
        """创建采购申请汇率记录"""
        exchange_rate_record = PurchaseRequestExchangeRate(**data.model_dump())
        self.db.add(exchange_rate_record)
        self.db.commit()
        self.db.refresh(exchange_rate_record)
        return exchange_rate_record
    
    def get_purchase_request_exchange_rates(self, request_id: int) -> List[PurchaseRequestExchangeRate]:
        """获取采购申请的汇率记录"""
        return self.db.query(PurchaseRequestExchangeRate).filter(
            PurchaseRequestExchangeRate.purchase_request_id == request_id
        ).order_by(PurchaseRequestExchangeRate.recorded_at).all()
    
    def check_exchange_rate_validity(self, currency_code: str, target_date: date = None) -> Dict[str, Any]:
        """
        检查指定货币在指定日期的汇率有效性
        
        Args:
            currency_code: 货币代码
            target_date: 目标日期，默认为当前日期
        
        Returns:
            包含有效性状态和汇率信息的字典
        """
        if target_date is None:
            target_date = date.today()
        
        # 获取目标月份
        target_month = date(target_date.year, target_date.month, 1)
        
        # 检查当前月是否有有效汇率
        current_month_rate = self.get_exchange_rate_by_currency_month(currency_code, target_month)
        
        if current_month_rate:
            return {
                "is_valid": True,
                "has_current_month_rate": True,
                "current_month_rate": current_month_rate.rate,
                "effective_month": current_month_rate.effective_month,
                "fallback_rate": None,
                "fallback_month": None,
                "message": f"货币 {currency_code} 在 {target_month.strftime('%Y-%m')} 月份有有效汇率"
            }
        
        # 如果当前月没有汇率，获取最近的有效汇率
        fallback_rate = self.db.query(ExchangeRate).filter(
            and_(
                ExchangeRate.currency_code == currency_code,
                ExchangeRate.status == "active",
                ExchangeRate.effective_month < target_month
            )
        ).order_by(desc(ExchangeRate.effective_month)).first()
        
        if fallback_rate:
            return {
                "is_valid": True,
                "has_current_month_rate": False,
                "current_month_rate": None,
                "effective_month": target_month,
                "fallback_rate": fallback_rate.rate,
                "fallback_month": fallback_rate.effective_month,
                "message": f"货币 {currency_code} 在 {target_month.strftime('%Y-%m')} 月份无汇率，使用 {fallback_rate.effective_month.strftime('%Y-%m')} 月份汇率 {fallback_rate.rate}"
            }
        
        # 完全没有有效汇率
        return {
            "is_valid": False,
            "has_current_month_rate": False,
            "current_month_rate": None,
            "effective_month": target_month,
            "fallback_rate": None,
            "fallback_month": None,
            "message": f"货币 {currency_code} 完全没有有效汇率"
        }
    
    def validate_purchase_request_exchange_rates(self, request_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        验证采购申请中所有物品的汇率有效性
        
        Args:
            request_items: 采购申请物品列表，每个物品包含currency_code等信息
        
        Returns:
            验证结果字典
        """
        validation_results = {
            "overall_valid": True,
            "items_validation": [],
            "blocking_currencies": [],
            "warnings": []
        }
        
        # 收集所有涉及的货币
        currencies = set()
        for item in request_items:
            if "currency_code" in item and item["currency_code"] != "USD":
                currencies.add(item["currency_code"])
        
        # 检查每种货币的汇率有效性
        for currency in currencies:
            validity = self.check_exchange_rate_validity(currency)
            
            item_validation = {
                "currency_code": currency,
                "is_valid": validity["is_valid"],
                "has_current_month_rate": validity["has_current_month_rate"],
                "message": validity["message"]
            }
            
            validation_results["items_validation"].append(item_validation)
            
            if not validity["is_valid"]:
                validation_results["overall_valid"] = False
                validation_results["blocking_currencies"].append(currency)
            elif not validity["has_current_month_rate"]:
                validation_results["warnings"].append(f"货币 {currency} 使用历史汇率，建议更新当前月汇率")
        
        return validation_results
    
    def get_exchange_rate_by_stage(self, request_id: int, stage: str) -> Optional[PurchaseRequestExchangeRate]:
        """获取采购申请指定阶段的汇率记录"""
        return self.db.query(PurchaseRequestExchangeRate).filter(
            and_(
                PurchaseRequestExchangeRate.purchase_request_id == request_id,
                PurchaseRequestExchangeRate.stage == stage
            )
        ).first()
    
    def convert_currency_to_usd(self, amount: Decimal, currency_code: str, target_date: date = None) -> Optional[Decimal]:
        """将指定货币转换为美元"""
        if currency_code == "USD":
            return amount
        
        exchange_rate = self.get_latest_exchange_rate(currency_code, target_date)
        if not exchange_rate:
            return None
        
        # 转换公式：美元价格 = 原币价格 ÷ 汇率
        return amount / exchange_rate.rate
    
    def convert_usd_to_currency(self, usd_amount: Decimal, currency_code: str, target_date: date = None) -> Optional[Decimal]:
        """将美元转换为指定货币"""
        if currency_code == "USD":
            return usd_amount
        
        exchange_rate = self.get_latest_exchange_rate(currency_code, target_date)
        if not exchange_rate:
            return None
        
        # 转换公式：原币价格 = 美元价格 × 汇率
        return usd_amount * exchange_rate.rate
    
    def format_price_display(self, usd_price: Decimal) -> str:
        """格式化价格显示（大于1美元显示2位小数，小于1美元显示4位小数）"""
        if usd_price >= 1:
            return f"${usd_price:.2f}"
        else:
            return f"${usd_price:.4f}"
    
    def is_exchange_rate_valid(self, currency_code: str, target_date: date = None) -> bool:
        """检查指定货币在指定日期是否有有效汇率"""
        if currency_code == "USD":
            return True
        
        if target_date is None:
            target_date = date.today()
        
        # 检查目标月份是否有有效汇率
        target_month = date(target_date.year, target_date.month, 1)
        rate = self.get_exchange_rate_by_currency_month(currency_code, target_month)
        
        if rate:
            return True
        
        # 检查是否有历史有效汇率
        historical_rate = self.get_latest_exchange_rate(currency_code, target_date)
        return historical_rate is not None
