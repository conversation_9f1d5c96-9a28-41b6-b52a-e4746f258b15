from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.purchase import PurchaseRequest, PurchaseRequestItem
from app.core.exceptions import BusinessException
from datetime import datetime, timedelta
import json


class SnapshotService:
    """快照管理服务"""

    def __init__(self, db: Session):
        self.db = db

    def create_item_snapshot(self, request_item_id: int, trigger: str) -> Dict[str, Any]:
        """创建物品快照"""
        # 获取申请明细
        request_item = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.id == request_item_id
        ).first()
        
        if not request_item:
            raise BusinessException("申请明细不存在")

        # 获取申请信息
        request = self.db.query(PurchaseRequest).filter(
            PurchaseRequest.id == request_item.request_id
        ).first()

        # 创建快照数据
        snapshot = {
            "item": {
                "id": request_item.item_id,
                "code": request_item.item_code,
                "name": request_item.item_name,
                "quantity": float(request_item.quantity),
                "unit": request_item.unit,
                "requirement_notes": request_item.requirement_notes,
                "expected_delivery": request_item.expected_delivery.isoformat() if request_item.expected_delivery else None
            },
            "request": {
                "id": request.id,
                "request_no": request.request_no,
                "status": request.status,
                "department_id": request.department_id,
                "submitter_id": request.submitter_id,
                "priority": request.priority
            },
            "snapshot_time": datetime.now().isoformat(),
            "snapshot_reason": trigger,
            "snapshot_type": "item_snapshot"
        }

        # 更新申请明细的快照字段（如果存在）
        if hasattr(request_item, 'item_snapshot'):
            request_item.item_snapshot = json.dumps(snapshot, ensure_ascii=False)
            request_item.updated_at = datetime.now()
            self.db.commit()
        
        return snapshot

    def create_approval_snapshot(self, request_id: int, approval_level: str) -> Dict[str, Any]:
        """创建审批快照"""
        request = self.db.query(PurchaseRequest).filter(PurchaseRequest.id == request_id).first()
        if not request:
            raise BusinessException("采购申请不存在")

        # 获取申请明细
        items = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.request_id == request_id
        ).all()

        # 创建快照数据
        snapshot = {
            "request": {
                "id": request.id,
                "request_no": request.request_no,
                "status": request.status,
                "department_id": request.department_id,
                "submitter_id": request.submitter_id,
                "priority": request.priority,
                "business_justification": request.business_justification,
                "expected_delivery": request.expected_delivery.isoformat() if request.expected_delivery else None
            },
            "items": [
                {
                    "id": item.id,
                    "item_code": item.item_code,
                    "item_name": item.item_name,
                    "quantity": float(item.quantity),
                    "unit": item.unit,
                    "requirement_notes": item.requirement_notes
                }
                for item in items
            ],
            "approval_context": {
                "approval_level": approval_level,
                "approval_time": datetime.now().isoformat(),
                "system_version": "v1.0.0"
            },
            "snapshot_time": datetime.now().isoformat(),
            "snapshot_reason": f"{approval_level}审批时创建",
            "snapshot_type": "approval_snapshot"
        }

        return snapshot

    def get_snapshot_history(self, entity_type: str, entity_id: int) -> List[Dict[str, Any]]:
        """获取快照历史"""
        snapshots = []
        
        if entity_type == "request":
            # 获取申请相关的所有快照
            request = self.db.query(PurchaseRequest).filter(PurchaseRequest.id == entity_id).first()
            if request:
                # 获取申请明细快照
                items = self.db.query(PurchaseRequestItem).filter(
                    PurchaseRequestItem.request_id == entity_id
                ).all()
                
                for item in items:
                    if hasattr(item, 'item_snapshot') and item.item_snapshot:
                        try:
                            snapshot_data = json.loads(item.item_snapshot)
                            snapshots.append(snapshot_data)
                        except json.JSONDecodeError:
                            continue
        
        elif entity_type == "item":
            # 获取物品相关的快照
            request_items = self.db.query(PurchaseRequestItem).filter(
                PurchaseRequestItem.item_id == entity_id
            ).all()
            
            for item in request_items:
                if hasattr(item, 'item_snapshot') and item.item_snapshot:
                    try:
                        snapshot_data = json.loads(item.item_snapshot)
                        snapshots.append(snapshot_data)
                    except json.JSONDecodeError:
                        continue
        
        # 按时间排序
        snapshots.sort(key=lambda x: x.get("snapshot_time", ""), reverse=True)
        return snapshots

    def cleanup_expired_snapshots(self, days: int = 90) -> Dict[str, int]:
        """清理过期的快照数据"""
        cutoff_date = datetime.now() - timedelta(days=days)
        cleaned_count = 0
        
        # 清理申请明细中的过期快照
        request_items = self.db.query(PurchaseRequestItem).filter(
            PurchaseRequestItem.updated_at < cutoff_date
        ).all()
        
        for item in request_items:
            if hasattr(item, 'item_snapshot') and item.item_snapshot:
                try:
                    snapshot_data = json.loads(item.item_snapshot)
                    snapshot_time = datetime.fromisoformat(snapshot_data.get("snapshot_time", ""))
                    if snapshot_time < cutoff_date:
                        item.item_snapshot = None
                        cleaned_count += 1
                except (json.JSONDecodeError, ValueError):
                    # 如果快照数据无效，也清理掉
                    item.item_snapshot = None
                    cleaned_count += 1
        
        if cleaned_count > 0:
            self.db.commit()
        
        return {
            "cleaned_snapshots": cleaned_count,
            "cutoff_date": cutoff_date.isoformat()
        }

    def get_snapshot_statistics(self) -> Dict[str, Any]:
        """获取快照统计信息"""
        # 统计申请明细快照
        total_item_snapshots = 0
        active_item_snapshots = 0
        
        request_items = self.db.query(PurchaseRequestItem).all()
        for item in request_items:
            if hasattr(item, 'item_snapshot') and item.item_snapshot:
                total_item_snapshots += 1
                try:
                    snapshot_data = json.loads(item.item_snapshot)
                    snapshot_time = datetime.fromisoformat(snapshot_data.get("snapshot_time", ""))
                    if snapshot_time > datetime.now() - timedelta(days=30):
                        active_item_snapshots += 1
                except (json.JSONDecodeError, ValueError):
                    continue
        
        return {
            "total_item_snapshots": total_item_snapshots,
            "active_item_snapshots": active_item_snapshots,
            "expired_item_snapshots": total_item_snapshots - active_item_snapshots,
            "snapshot_types": ["item_snapshot", "approval_snapshot"]
        }
