"""
二维码服务
用于生成和解析物品管理系统的二维码内容
"""

from typing import Dict, Any, Optional, Union
from enum import Enum
import json
from sqlalchemy.orm import Session
from app.models.item import Item
from app.models.user import User, Department
from app.models.supplier import Supplier
from app.models.purchase import PurchaseRequest
from sqlalchemy import and_


class QRCodeType(str, Enum):
    """二维码类型枚举"""
    ITEM = "i"  # 物品
    DEPARTMENT = "d"  # 部门
    CUSTOMER = "c"  # 员工/客户
    SUPPLIER = "s"  # 供应商
    PURCHASE_REQUEST = "pr"  # 采购申请


class QRCodeService:
    """二维码服务类"""
    
    @classmethod
    def generate_item_qr_code(cls, item: Item) -> str:
        """
        生成物品二维码内容（简化版）
        
        Args:
            item: 物品对象
            
        Returns:
            二维码内容字符串
        """
        return item.code if item.code else ""
    
    @classmethod
    def generate_department_qr_code(cls, department: Department) -> str:
        """
        生成部门二维码内容（简化版）
        
        Args:
            department: 部门对象
            
        Returns:
            二维码内容字符串
        """
        return department.code if department.code else ""
    
    @classmethod
    def generate_employee_qr_code(cls, user: User) -> str:
        """
        生成员工二维码内容（简化版）
        
        Args:
            user: 用户对象
            
        Returns:
            二维码内容字符串
        """
        return user.employee_id if user.employee_id else ""
    
    @classmethod
    def generate_supplier_qr_code(cls, supplier: Supplier) -> str:
        """
        生成供应商二维码内容（简化版）
        
        Args:
            supplier: 供应商对象
            
        Returns:
            二维码内容字符串
        """
        return supplier.code if supplier.code else ""
    
    @classmethod
    def generate_purchase_request_qr_code(cls, request: PurchaseRequest) -> str:
        """
        生成采购申请二维码内容（简化版）
        
        Args:
            request: 采购申请对象
            
        Returns:
            二维码内容字符串
        """
        return request.request_no if request.request_no else ""
    
    @classmethod
    def detect_qr_code_type(cls, code: str) -> Optional[QRCodeType]:
        """
        根据编码格式判断二维码类型
        
        Args:
            code: 编码字符串
            
        Returns:
            二维码类型枚举值
        """
        if not code:
            return None
            
        code = code.strip()
        
        if code.startswith("IDM"):
            return QRCodeType.ITEM
        elif code.startswith("DEPT") or code in ["IT", "HR", "PUR", "WH", "ME", "SI", "AD", "QA"]:
            return QRCodeType.DEPARTMENT
        elif code.startswith("SUP"):
            return QRCodeType.SUPPLIER
        elif code.startswith("EMP"):
            return QRCodeType.CUSTOMER
        elif code.startswith("PR"):
            return QRCodeType.PURCHASE_REQUEST
        
        return None
    
    @classmethod
    def parse_qr_code(cls, qr_content: str) -> Dict[str, Any]:
        """
        解析二维码内容（简化版）
        
        Args:
            qr_content: 二维码内容字符串
            
        Returns:
            解析后的数据字典
        """
        try:
            if not qr_content or not qr_content.strip():
                raise ValueError("二维码内容为空")
            
            code = qr_content.strip()
            qr_type = cls.detect_qr_code_type(code)
            
            if not qr_type:
                raise ValueError(f"无法识别的编码格式：{code}")
            
            return {
                'type': qr_type.value,
                'code': code,
                'name': '',
                'remark': '',
                'version': '2.0',  # 简化版版本号
                'raw_content': qr_content
            }
            
        except Exception as e:
            raise ValueError(f"二维码解析失败：{str(e)}")
    
    @classmethod
    def get_qr_code_type(cls, qr_content: str) -> Optional[QRCodeType]:
        """
        获取二维码类型
        
        Args:
            qr_content: 二维码内容字符串
            
        Returns:
            二维码类型枚举值
        """
        try:
            parsed = cls.parse_qr_code(qr_content)
            return QRCodeType(parsed['type'])
        except:
            return None
    
    @classmethod
    def is_valid_qr_code(cls, qr_content: str) -> bool:
        """
        验证二维码内容是否有效
        
        Args:
            qr_content: 二维码内容字符串
            
        Returns:
            是否有效
        """
        try:
            cls.parse_qr_code(qr_content)
            return True
        except:
            return False

    @classmethod
    def get_item_by_qr_code(cls, qr_content: str, db: Session) -> Optional[Item]:
        """
        根据二维码内容查询物品
        
        Args:
            qr_content: 二维码内容字符串
            db: 数据库会话
            
        Returns:
            物品对象，如果未找到则返回None
        """
        try:
            qr_data = cls.parse_qr_code(qr_content)
            
            # 验证是否为物品类型
            if qr_data['type'] != 'i':
                return None
            
            # 根据编码查询物品
            item = db.query(Item).filter(
                Item.code == qr_data['code'],
                Item.is_active == True
            ).first()
            
            return item
        except:
            return None

    @classmethod
    def get_employee_by_qr_code(cls, qr_content: str, db: Session, department_id: int = None) -> Optional[User]:
        """
        根据二维码内容查询员工
        
        Args:
            qr_content: 二维码内容字符串
            db: 数据库会话
            department_id: 部门ID，如果提供则只查询该部门的员工
            
        Returns:
            员工对象，如果未找到则返回None
        """
        try:
            qr_data = cls.parse_qr_code(qr_content)
            
            # 验证是否为员工类型
            if qr_data['type'] != 'c':
                return None
            
            # 构建查询条件
            query_conditions = [
                User.employee_id == qr_data['code'],
                User.is_active == True
            ]
            
            # 如果指定了部门ID，添加部门限制
            if department_id is not None:
                query_conditions.append(User.department_id == department_id)
            
            # 根据工号查询员工
            employee = db.query(User).filter(and_(*query_conditions)).first()
            
            return employee
        except:
            return None

    @classmethod
    def get_department_by_qr_code(cls, qr_content: str, db: Session) -> Optional[Department]:
        """
        根据二维码内容查询部门
        
        Args:
            qr_content: 二维码内容字符串
            db: 数据库会话
            
        Returns:
            部门对象，如果未找到则返回None
        """
        try:
            qr_data = cls.parse_qr_code(qr_content)
            
            # 验证是否为部门类型
            if qr_data['type'] != 'd':
                return None
            
            # 根据编码查询部门
            department = db.query(Department).filter(
                Department.code == qr_data['code'],
                Department.is_active == True
            ).first()
            
            return department
        except:
            return None

    @classmethod
    def get_supplier_by_qr_code(cls, qr_content: str, db: Session) -> Optional[Supplier]:
        """
        根据二维码内容查询供应商
        
        Args:
            qr_content: 二维码内容字符串
            db: 数据库会话
            
        Returns:
            供应商对象，如果未找到则返回None
        """
        try:
            qr_data = cls.parse_qr_code(qr_content)
            
            # 验证是否为供应商类型
            if qr_data['type'] != 's':
                return None
            
            # 根据编码查询供应商
            supplier = db.query(Supplier).filter(
                Supplier.code == qr_data['code'],
                Supplier.status == "active"
            ).first()
            
            return supplier
        except:
            return None
