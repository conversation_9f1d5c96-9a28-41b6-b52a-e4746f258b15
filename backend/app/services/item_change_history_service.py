from sqlalchemy.orm import Session
from app.models.item import ItemChangeHistory
from typing import Dict, Any, Optional


class ItemChangeHistoryService:
    """物品变更历史服务"""
    
    @staticmethod
    def create_history_record(
        db: Session,
        item_id: int,
        user_id: int,
        action: str,
        field_name: Optional[str] = None,
        old_value: Optional[str] = None,
        new_value: Optional[str] = None
    ) -> ItemChangeHistory:
        """创建变更历史记录"""
        history = ItemChangeHistory(
            item_id=item_id,
            user_id=user_id,
            action=action,
            field_name=field_name,
            old_value=str(old_value) if old_value is not None else None,
            new_value=str(new_value) if new_value is not None else None
        )
        db.add(history)
        db.commit()
        return history
    
    @staticmethod
    def record_item_creation(
        db: Session,
        item_id: int,
        user_id: int
    ) -> ItemChangeHistory:
        """记录物品创建"""
        return ItemChangeHistoryService.create_history_record(
            db=db,
            item_id=item_id,
            user_id=user_id,
            action="create"
        )
    
    @staticmethod
    def record_item_update(
        db: Session,
        item_id: int,
        user_id: int,
        old_data: Dict[str, Any],
        new_data: Dict[str, Any]
    ) -> list[ItemChangeHistory]:
        """记录物品更新"""
        history_records = []
        
        for field_name, new_value in new_data.items():
            old_value = old_data.get(field_name)
            
            # 处理None值的比较
            if old_value is None and new_value is None:
                continue
            elif old_value is None and new_value is not None:
                # 从None更新为有值
                history = ItemChangeHistoryService.create_history_record(
                    db=db,
                    item_id=item_id,
                    user_id=user_id,
                    action="update",
                    field_name=field_name,
                    old_value=None,
                    new_value=str(new_value)
                )
                history_records.append(history)
            elif old_value is not None and new_value is None:
                # 从有值更新为None
                history = ItemChangeHistoryService.create_history_record(
                    db=db,
                    item_id=item_id,
                    user_id=user_id,
                    action="update",
                    field_name=field_name,
                    old_value=str(old_value),
                    new_value=None
                )
                history_records.append(history)
            elif str(old_value) != str(new_value):
                # 值确实发生了变化
                history = ItemChangeHistoryService.create_history_record(
                    db=db,
                    item_id=item_id,
                    user_id=user_id,
                    action="update",
                    field_name=field_name,
                    old_value=str(old_value),
                    new_value=str(new_value)
                )
                history_records.append(history)
        
        return history_records
    
    @staticmethod
    def record_item_deletion(
        db: Session,
        item_id: int,
        user_id: int
    ) -> ItemChangeHistory:
        """记录物品删除"""
        return ItemChangeHistoryService.create_history_record(
            db=db,
            item_id=item_id,
            user_id=user_id,
            action="delete"
        ) 