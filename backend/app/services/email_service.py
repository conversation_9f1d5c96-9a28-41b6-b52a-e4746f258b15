import asyncio
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc
from aiosmtplib import SMTP
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from app.models.notification import Notification, NotificationEmail
from app.models.user import User
from app.services.config_service import ConfigService

logger = logging.getLogger(__name__)


class EmailService:
    """邮件服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.config_service = ConfigService(db)
    
    async def send_email(
        self, 
        to_email: str, 
        subject: str, 
        content: str, 
        html_content: Optional[str] = None
    ) -> bool:
        """发送邮件"""
        try:
            # 获取SMTP配置
            smtp_config = await self._get_smtp_config()
            if not smtp_config:
                logger.error("SMTP配置未设置")
                return False
            
            # 创建邮件消息
            message = MIMEMultipart("alternative")
            message["From"] = smtp_config["username"]
            message["To"] = to_email
            message["Subject"] = subject
            
            # 添加纯文本内容
            text_part = MIMEText(content, "plain", "utf-8")
            message.attach(text_part)
            
            # 添加HTML内容（如果提供）
            if html_content:
                html_part = MIMEText(html_content, "html", "utf-8")
                message.attach(html_part)
            
            # 发送邮件
            async with SMTP(
                hostname=smtp_config["host"],
                port=smtp_config["port"],
                use_tls=smtp_config["use_tls"]
            ) as smtp:
                await smtp.login(smtp_config["username"], smtp_config["password"])
                await smtp.send_message(message)
            
            logger.info(f"邮件发送成功: {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"邮件发送失败: {to_email}, 错误: {str(e)}")
            return False
    
    async def send_notification_email(self, notification_email: NotificationEmail) -> bool:
        """发送通知邮件"""
        try:
            # 获取通知和用户信息
            notification = self.db.query(Notification).filter(
                Notification.id == notification_email.notification_id
            ).first()
            
            user = self.db.query(User).filter(User.id == notification_email.user_id).first()
            
            if not notification or not user:
                logger.error(f"通知或用户信息不存在: notification_id={notification_email.notification_id}, user_id={notification_email.user_id}")
                return False
            
            # 生成邮件内容
            subject, content, html_content = self._generate_notification_email(
                notification, user
            )
            
            # 发送邮件
            success = await self.send_email(user.email, subject, content, html_content)
            
            # 更新邮件状态
            if success:
                notification_email.email_status = "sent"
                notification_email.sent_at = datetime.utcnow()
            else:
                notification_email.email_status = "failed"
                notification_email.failure_reason = "SMTP发送失败"
            
            self.db.commit()
            return success
            
        except Exception as e:
            logger.error(f"发送通知邮件时出错: {str(e)}")
            # 更新失败状态
            notification_email.email_status = "failed"
            notification_email.failure_reason = str(e)
            self.db.commit()
            return False
    
    async def send_batch_emails(self, email_ids: List[int]) -> Dict[str, int]:
        """批量发送邮件"""
        results = {"success": 0, "failed": 0}
        
        for email_id in email_ids:
            email_record = self.db.query(NotificationEmail).filter(
                NotificationEmail.id == email_id
            ).first()
            
            if email_record and email_record.email_status == "pending":
                success = await self.send_notification_email(email_record)
                if success:
                    results["success"] += 1
                else:
                    results["failed"] += 1
        
        return results
    
    def get_pending_emails(self, limit: int = 100) -> List[NotificationEmail]:
        """获取待发送的邮件列表"""
        return self.db.query(NotificationEmail).filter(
            and_(
                NotificationEmail.email_status == "pending",
                NotificationEmail.notification_id.isnot(None)
            )
        ).order_by(NotificationEmail.created_at.asc()).limit(limit).all()
    
    def get_email_stats(self) -> Dict[str, Any]:
        """获取邮件发送统计信息"""
        total = self.db.query(NotificationEmail).count()
        pending = self.db.query(NotificationEmail).filter(
            NotificationEmail.email_status == "pending"
        ).count()
        sent = self.db.query(NotificationEmail).filter(
            NotificationEmail.email_status == "sent"
        ).count()
        failed = self.db.query(NotificationEmail).filter(
            NotificationEmail.email_status == "failed"
        ).count()
        
        # 按日期统计发送数量
        today = datetime.utcnow().date()
        today_sent = self.db.query(NotificationEmail).filter(
            and_(
                NotificationEmail.email_status == "sent",
                NotificationEmail.sent_at >= today
            )
        ).count()
        
        return {
            "total": total,
            "pending": pending,
            "sent": sent,
            "failed": failed,
            "today_sent": today_sent,
            "success_rate": (sent / total * 100) if total > 0 else 0
        }
    
    def get_failed_emails(
        self, 
        page: int = 1, 
        size: int = 20
    ) -> Dict[str, Any]:
        """获取发送失败的邮件列表"""
        query = self.db.query(NotificationEmail).filter(
            NotificationEmail.email_status == "failed"
        ).order_by(desc(NotificationEmail.created_at))
        
        total = query.count()
        emails = query.offset((page - 1) * size).limit(size).all()
        
        return {
            "data": emails,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }
    
    def retry_failed_email(self, email_id: int) -> bool:
        """重试发送失败的邮件"""
        email_record = self.db.query(NotificationEmail).filter(
            NotificationEmail.id == email_id
        ).first()
        
        if email_record and email_record.email_status == "failed":
            email_record.email_status = "pending"
            email_record.failure_reason = None
            self.db.commit()
            return True
        
        return False
    
    async def _get_smtp_config(self) -> Optional[Dict[str, Any]]:
        """获取SMTP配置"""
        try:
            configs = await self.config_service.get_configs_by_keys([
                "smtp_host", "smtp_port", "smtp_username", 
                "smtp_password", "smtp_use_tls"
            ])
            
            if not all(key in configs for key in ["smtp_host", "smtp_port", "smtp_username", "smtp_password"]):
                return None
            
            return {
                "host": configs["smtp_host"],
                "port": int(configs["smtp_port"]),
                "username": configs["smtp_username"],
                "password": configs["smtp_password"],
                "use_tls": configs.get("smtp_use_tls", "true").lower() == "true"
            }
            
        except Exception as e:
            logger.error(f"获取SMTP配置时出错: {str(e)}")
            return None
    
    def _generate_notification_email(
        self, 
        notification: Notification, 
        user: User
    ) -> tuple[str, str, str]:
        """生成通知邮件内容"""
        # 邮件主题
        subject = f"[BizLinkSpeedy IDM] {notification.title}"
        
        # 纯文本内容
        text_content = f"""
Dear {user.display_name or user.full_name or user.username},

{notification.content}

Notification Type: {notification.notification_type}
Created At: {notification.created_at.strftime('%Y-%m-%d %H:%M:%S')}

If you have any questions, please contact the system administrator.

Best regards,
BizLinkSpeedy IDM System
        """.strip()
        
        # HTML内容
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .header {{ background-color: #f8f9fa; padding: 20px; border-bottom: 1px solid #dee2e6; }}
        .content {{ padding: 20px; }}
        .footer {{ background-color: #f8f9fa; padding: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; }}
        .notification-type {{ display: inline-block; background-color: #007bff; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; }}
        .action-button {{ display: inline-block; background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 15px; }}
    </style>
</head>
<body>
    <div class="header">
        <h2>BizLinkSpeedy IDM Notification</h2>
    </div>
    
    <div class="content">
        <p>Dear <strong>{user.display_name or user.full_name or user.username}</strong>,</p>
        
        <p>{notification.content}</p>
        
        <p>
            <span class="notification-type">{notification.notification_type}</span>
            <br>
            <small>Created: {notification.created_at.strftime('%Y-%m-%d %H:%M:%S')}</small>
        </p>
        
        {f'<a href="{notification.action_url}" class="action-button">View Details</a>' if notification.action_url else ''}
    </div>
    
    <div class="footer">
        <p>If you have any questions, please contact the system administrator.</p>
        <p>Best regards,<br>BizLinkSpeedy IDM System</p>
    </div>
</body>
</html>
        """.strip()
        
        return subject, text_content, html_content
    
    async def test_email_connection(self, test_email: str) -> Dict[str, Any]:
        """测试邮件连接"""
        try:
            smtp_config = await self._get_smtp_config()
            if not smtp_config:
                return {
                    "success": False,
                    "message": "SMTP配置未设置"
                }
            
            # 发送测试邮件
            subject = "[BizLinkSpeedy IDM] Test Email"
            content = "This is a test email from BizLinkSpeedy IDM system."
            
            success = await self.send_email(test_email, subject, content)
            
            if success:
                return {
                    "success": True,
                    "message": "测试邮件发送成功"
                }
            else:
                return {
                    "success": False,
                    "message": "测试邮件发送失败"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"测试邮件发送时出错: {str(e)}"
            }
