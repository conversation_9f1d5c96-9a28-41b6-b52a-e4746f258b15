import logging
import asyncio
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from app.services.email_service import EmailService
from app.services.config_service import ConfigService

logger = logging.getLogger(__name__)


class EmailScheduler:
    """邮件发送定时任务服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.scheduler = AsyncIOScheduler()
        self.email_service = EmailService(db)
        self.config_service = ConfigService(db)
        self.job_id = "email_sender_job"
        self.is_running = False
    
    def start(self):
        """启动定时任务"""
        if self.is_running:
            logger.info("定时任务已经在运行中")
            return
        
        try:
            # 获取邮件发送间隔
            interval_minutes = self.config_service.get_email_send_interval()
            
            # 添加定时任务
            self.scheduler.add_job(
                func=self._send_pending_emails,
                trigger=IntervalTrigger(minutes=interval_minutes),
                id=self.job_id,
                name="Send Pending Emails",
                replace_existing=True
            )
            
            # 启动调度器
            self.scheduler.start()
            self.is_running = True
            
            logger.info(f"邮件发送定时任务已启动，间隔: {interval_minutes} 分钟")
            
        except Exception as e:
            logger.error(f"启动定时任务时出错: {str(e)}")
            raise e
    
    def stop(self):
        """停止定时任务"""
        if not self.is_running:
            logger.info("定时任务未在运行")
            return
        
        try:
            self.scheduler.shutdown(wait=False)
            self.is_running = False
            logger.info("邮件发送定时任务已停止")
            
        except Exception as e:
            logger.error(f"停止定时任务时出错: {str(e)}")
            raise e
    
    def restart(self):
        """重启定时任务"""
        logger.info("重启邮件发送定时任务...")
        self.stop()
        self.start()
    
    def update_interval(self, new_interval_minutes: int):
        """更新邮件发送间隔"""
        try:
            # 验证间隔时间
            if new_interval_minutes < 1 or new_interval_minutes > 60:
                raise ValueError("邮件发送间隔必须在 1-60 分钟之间")
            
            # 更新配置
            self.config_service.set_config("email_send_interval", str(new_interval_minutes))
            
            # 如果调度器正在运行，重启任务
            if self.is_running:
                self.restart()
            
            logger.info(f"邮件发送间隔已更新为 {new_interval_minutes} 分钟")
            
        except Exception as e:
            logger.error(f"更新邮件发送间隔时出错: {str(e)}")
            raise e
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        if not self.is_running:
            return {
                "status": "stopped",
                "next_run": None,
                "interval_minutes": self.config_service.get_email_send_interval()
            }
        
        try:
            job = self.scheduler.get_job(self.job_id)
            if job:
                return {
                    "status": "running",
                    "next_run": job.next_run_time.isoformat() if job.next_run_time else None,
                    "interval_minutes": self.config_service.get_email_send_interval()
                }
            else:
                return {
                    "status": "error",
                    "next_run": None,
                    "interval_minutes": self.config_service.get_email_send_interval()
                }
        except Exception as e:
            logger.error(f"获取调度器状态时出错: {str(e)}")
            return {
                "status": "error",
                "next_run": None,
                "interval_minutes": self.config_service.get_email_send_interval()
            }
    
    async def _send_pending_emails(self):
        """发送待发送的邮件（定时任务执行函数）"""
        try:
            logger.info("开始执行邮件发送定时任务...")
            
            # 检查通知功能是否启用
            if not self.config_service.is_notification_enabled():
                logger.info("通知功能已禁用，跳过邮件发送")
                return
            
            # 获取待发送的邮件
            pending_emails = self.email_service.get_pending_emails(limit=50)
            
            if not pending_emails:
                logger.info("没有待发送的邮件")
                return
            
            logger.info(f"找到 {len(pending_emails)} 封待发送邮件")
            
            # 批量发送邮件
            success_count = 0
            failed_count = 0
            
            for email_record in pending_emails:
                try:
                    success = await self.email_service.send_notification_email(email_record)
                    if success:
                        success_count += 1
                    else:
                        failed_count += 1
                except Exception as e:
                    logger.error(f"发送邮件 {email_record.id} 时出错: {str(e)}")
                    failed_count += 1
            
            logger.info(f"邮件发送完成: 成功 {success_count} 封，失败 {failed_count} 封")
            
        except Exception as e:
            logger.error(f"执行邮件发送定时任务时出错: {str(e)}")
    
    async def manual_trigger(self) -> Dict[str, Any]:
        """手动触发邮件发送"""
        try:
            logger.info("手动触发邮件发送...")
            
            # 检查通知功能是否启用
            if not self.config_service.is_notification_enabled():
                return {
                    "success": False,
                    "message": "通知功能已禁用"
                }
            
            # 获取待发送的邮件
            pending_emails = self.email_service.get_pending_emails(limit=100)
            
            if not pending_emails:
                return {
                    "success": True,
                    "message": "没有待发送的邮件",
                    "processed": 0
                }
            
            # 执行发送
            await self._send_pending_emails()
            
            # 获取发送结果统计
            stats = self.email_service.get_email_stats()
            
            return {
                "success": True,
                "message": f"手动触发完成，处理了 {len(pending_emails)} 封邮件",
                "processed": len(pending_emails),
                "stats": stats
            }
            
        except Exception as e:
            logger.error(f"手动触发邮件发送时出错: {str(e)}")
            return {
                "success": False,
                "message": f"手动触发失败: {str(e)}"
            }
    
    def get_job_history(self, limit: int = 100) -> Dict[str, Any]:
        """获取任务执行历史"""
        try:
            # 这里可以扩展为从数据库记录任务执行历史
            # 目前返回基本信息
            return {
                "last_execution": datetime.utcnow().isoformat(),
                "total_executions": 0,
                "success_rate": 100.0
            }
        except Exception as e:
            logger.error(f"获取任务执行历史时出错: {str(e)}")
            return {
                "last_execution": None,
                "total_executions": 0,
                "success_rate": 0.0
            }
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            status = self.get_scheduler_status()
            
            # 检查数据库连接
            db_healthy = True
            try:
                self.db.execute("SELECT 1")
            except Exception:
                db_healthy = False
            
            # 检查配置服务
            config_healthy = True
            try:
                self.config_service.get_config("notification_enabled")
            except Exception:
                config_healthy = False
            
            return {
                "scheduler_status": status,
                "database_healthy": db_healthy,
                "config_service_healthy": config_healthy,
                "overall_healthy": status["status"] == "running" and db_healthy and config_healthy
            }
            
        except Exception as e:
            logger.error(f"健康检查时出错: {str(e)}")
            return {
                "scheduler_status": {"status": "error"},
                "database_healthy": False,
                "config_service_healthy": False,
                "overall_healthy": False,
                "error": str(e)
            }
