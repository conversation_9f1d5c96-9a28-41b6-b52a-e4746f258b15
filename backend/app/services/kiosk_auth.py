"""
客户端认证服务
专门为部门仓库客户端提供认证和权限验证服务
"""

from fastapi import HTTPException, status, Depends
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from sqlalchemy import and_
from jose import JWTError, jwt
from passlib.context import CryptContext
from datetime import datetime, timedelta
from typing import Optional

from app.core.database import get_db
from app.core.config import settings
from app.models.user import User, Department
from app.models.permission import Role, Permission

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT Bearer认证
security = HTTPBearer()


class ClientAuthService:
    """客户端认证服务"""
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """获取密码哈希"""
        return pwd_context.hash(password)
    
    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.jwt_secret_key, algorithm=settings.jwt_algorithm)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str) -> Optional[dict]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, settings.jwt_secret_key, algorithms=[settings.jwt_algorithm])
            return payload
        except JWTError:
            return None
    
    @staticmethod
    def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
        """认证用户"""
        user = db.query(User).filter(
            and_(
                User.username == username,
                User.is_active == True,
                User.account_status == "active"
            )
        ).first()
        
        if not user:
            return None
        
        if not ClientAuthService.verify_password(password, user.hashed_password):
            return None
        
        return user
    
    @staticmethod
    def verify_dept_warehouse_role(user: User, db: Session) -> bool:
        """验证用户是否具有部门仓库角色"""
        if not user.role_id:
            return False
        
        role = db.query(Role).filter(
            and_(
                Role.id == user.role_id,
                Role.code == "dept_warehouse",
                Role.is_active == True
            )
        ).first()
        
        return role is not None
    
    @staticmethod
    def get_user_permissions(user: User, db: Session) -> list:
        """获取用户权限列表"""
        if not user.role_id:
            return []
        
        role = db.query(Role).filter(Role.id == user.role_id).first()
        if not role:
            return []
        
        permissions = []
        for permission in role.permissions:
            if permission.is_active:
                permissions.append({
                    "code": permission.code,
                    "name": permission.name,
                    "module": permission.module
                })
        
        return permissions


async def get_current_client_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前客户端用户"""
    token = credentials.credentials
    
    payload = ClientAuthService.verify_token(token)
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    username: str = payload.get("sub")
    if username is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = db.query(User).filter(User.username == username).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户已被禁用",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


async def require_dept_warehouse_role(
    current_user: User = Depends(get_current_client_user),
    db: Session = Depends(get_db)
) -> User:
    """要求用户具有部门仓库角色"""
    if not ClientAuthService.verify_dept_warehouse_role(current_user, db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要部门仓库角色"
        )
    
    return current_user


async def require_dept_warehouse_permission(
    permission_code: str,
    current_user: User = Depends(require_dept_warehouse_role),
    db: Session = Depends(get_db)
) -> User:
    """要求用户具有特定权限"""
    permissions = ClientAuthService.get_user_permissions(current_user, db)
    permission_codes = [p["code"] for p in permissions]
    
    if permission_code not in permission_codes:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"权限不足，需要 {permission_code} 权限"
        )
    
    return current_user
