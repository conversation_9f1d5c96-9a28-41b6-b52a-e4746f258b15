"""
通知集成服务 - 在业务操作中自动创建通知
集成库存变更、审批流程等业务事件的通知功能
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from decimal import Decimal
import logging

from app.models.inventory import DepartmentInventory, InventoryChangeRecord, InventoryChangeType
from app.models.notification import Notification
from app.models.user import User, Department
from app.models.item import Item
from app.services.notification_service import NotificationService

# 通知类型常量
class NotificationType:
    INVENTORY_ALERT = "inventory_alert"
    APPROVAL_FLOW = "approval_flow"
    SYSTEM = "system"
    OTHER = "other"

logger = logging.getLogger(__name__)


class NotificationIntegrationService:
    """通知集成服务 - 在业务操作中自动创建通知"""
    
    def __init__(self, db: Session):
        self.db = db
        self.notification_service = NotificationService(db)
    
    def create_inventory_alert_notification(
        self,
        department_id: int,
        item_id: int,
        alert_type: str,
        current_quantity: Decimal,
        threshold_value: Optional[Decimal] = None,
        operator_id: Optional[int] = None
    ) -> Optional[Notification]:
        """
        创建库存预警通知
        
        Args:
            department_id: 部门ID
            item_id: 物品ID
            alert_type: 预警类型 (low_stock/out_of_stock/overstock)
            current_quantity: 当前库存数量
            threshold_value: 阈值数量
            operator_id: 操作员ID
            
        Returns:
            Notification: 创建的通知对象，失败时返回None
        """
        try:
            # 获取部门信息
            department = self.db.query(Department).filter(Department.id == department_id).first()
            if not department:
                logger.error(f"部门不存在: department_id={department_id}")
                return None
            
            # 获取物品信息
            item = self.db.query(Item).filter(Item.id == item_id).first()
            if not item:
                logger.error(f"物品不存在: item_id={item_id}")
                return None
            
            # 获取部门管理员用户
            admin_users = self.db.query(User).filter(
                User.department_id == department_id,
                User.is_superuser == True
            ).all()
            
            if not admin_users:
                logger.warning(f"部门 {department.name} 没有管理员用户")
                return None
            
            # 根据预警类型生成通知内容
            if alert_type == "low_stock":
                title = f"库存预警 - {item.name}"
                content = f"部门 {department.name} 的物品 {item.name} 库存不足。当前库存: {current_quantity} {item.purchase_unit}，低于最小库存阈值: {threshold_value} {item.purchase_unit}。请及时补充库存。"
                notification_type = NotificationType.INVENTORY_ALERT
            elif alert_type == "out_of_stock":
                title = f"缺货通知 - {item.name}"
                content = f"部门 {department.name} 的物品 {item.name} 已缺货。当前库存: {current_quantity} {item.purchase_unit}。请立即补充库存。"
                notification_type = NotificationType.INVENTORY_ALERT
            elif alert_type == "overstock":
                title = f"超储提醒 - {item.name}"
                content = f"部门 {department.name} 的物品 {item.name} 库存超储。当前库存: {current_quantity} {item.purchase_unit}，超过最大库存阈值: {threshold_value} {item.purchase_unit}。请注意库存管理。"
                notification_type = NotificationType.INVENTORY_ALERT
            else:
                logger.warning(f"未知的预警类型: {alert_type}")
                return None
            
            # 为每个管理员用户创建通知
            notifications = []
            for admin_user in admin_users:
                from app.schemas.notification import NotificationCreate
                
                notification_data = NotificationCreate(
                    user_id=admin_user.id,
                    title=title,
                    content=content,
                    notification_type=notification_type,
                    business_data={
                        "related_module": "inventory",
                        "related_id": item_id,
                        "priority": "high" if alert_type in ["out_of_stock", "low_stock"] else "medium"
                    }
                )
                
                notification = self.notification_service.create_notification(notification_data)
                if notification:
                    notifications.append(notification)
            
            logger.info(f"为部门 {department.name} 的物品 {item.name} 创建了 {len(notifications)} 个库存预警通知")
            return notifications[0] if notifications else None
            
        except Exception as e:
            logger.error(f"创建库存预警通知失败: department_id={department_id}, item_id={item_id}, error={str(e)}")
            return None
    
    def create_inventory_change_notification(
        self,
        change_record: InventoryChangeRecord,
        operator_id: int
    ) -> Optional[Notification]:
        """
        创建库存变更通知
        
        Args:
            change_record: 库存变更记录
            operator_id: 操作员ID
            
        Returns:
            Notification: 创建的通知对象，失败时返回None
        """
        try:
            # 获取操作员信息
            operator = self.db.query(User).filter(User.id == operator_id).first()
            if not operator:
                logger.error(f"操作员不存在: operator_id={operator_id}")
                return None
            
            # 获取部门信息
            department = self.db.query(Department).filter(Department.id == change_record.department_id).first()
            if not department:
                logger.error(f"部门不存在: department_id={change_record.department_id}")
                return None
            
            # 获取物品信息
            item = self.db.query(Item).filter(Item.id == change_record.item_id).first()
            if not item:
                logger.error(f"物品不存在: item_id={change_record.item_id}")
                return None
            
            # 根据变更类型生成通知内容
            if change_record.change_type == InventoryChangeType.MANUAL_IN:
                title = f"入库通知 - {item.name}"
                content = f"操作员 {operator.full_name or operator.username} 在部门 {department.name} 为物品 {item.name} 入库 {change_record.change_quantity} {item.purchase_unit}。当前库存: {change_record.after_quantity} {item.purchase_unit}。"
                notification_type = NotificationType.INVENTORY_ALERT
            elif change_record.change_type == InventoryChangeType.PICKUP_OUT:
                title = f"出库通知 - {item.name}"
                content = f"操作员 {operator.full_name or operator.username} 在部门 {department.name} 为物品 {item.name} 出库 {abs(change_record.change_quantity)} {item.purchase_unit}。当前库存: {change_record.after_quantity} {item.purchase_unit}。"
                notification_type = NotificationType.INVENTORY_ALERT
            elif change_record.change_type == InventoryChangeType.ADJUST:
                title = f"库存调整通知 - {item.name}"
                content = f"操作员 {operator.full_name or operator.username} 在部门 {department.name} 调整物品 {item.name} 库存从 {change_record.before_quantity} {item.purchase_unit} 到 {change_record.after_quantity} {item.purchase_unit}。调整原因: {change_record.change_reason or '无'}。"
                notification_type = NotificationType.INVENTORY_ALERT
            else:
                # 其他变更类型不创建通知
                return None
            
            # 为部门管理员创建通知
            admin_users = self.db.query(User).filter(
                User.department_id == change_record.department_id,
                User.is_superuser == True
            ).all()
            
            if not admin_users:
                logger.warning(f"部门 {department.name} 没有管理员用户")
                return None
            
            # 为每个管理员用户创建通知
            notifications = []
            for admin_user in admin_users:
                # 跳过操作员自己
                if admin_user.id == operator_id:
                    continue
                    
                from app.schemas.notification import NotificationCreate
                
                notification_data = NotificationCreate(
                    user_id=admin_user.id,
                    title=title,
                    content=content,
                    notification_type=notification_type,
                    business_data={
                        "related_module": "inventory",
                        "related_id": item.id,
                        "priority": "medium"
                    }
                )
                
                notification = self.notification_service.create_notification(notification_data)
                if notification:
                    notifications.append(notification)
            
            logger.info(f"为库存变更创建了 {len(notifications)} 个通知")
            return notifications[0] if notifications else None
            
        except Exception as e:
            logger.error(f"创建库存变更通知失败: change_record_id={change_record.id}, error={str(e)}")
            return None
    
    def create_approval_notification(
        self,
        user_id: int,
        approval_type: str,
        item_name: str,
        request_id: int,
        status: str,
        approver_id: Optional[int] = None
    ) -> Optional[Notification]:
        """
        创建审批流程通知
        
        Args:
            user_id: 接收通知的用户ID
            approval_type: 审批类型 (purchase_request/approval_status/approval_complete)
            item_name: 物品名称
            request_id: 请求ID
            status: 审批状态
            approver_id: 审批人ID
            
        Returns:
            Notification: 创建的通知对象，失败时返回None
        """
        try:
            # 获取用户信息
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                logger.error(f"用户不存在: user_id={user_id}")
                return None
            
            # 根据审批类型生成通知内容
            if approval_type == "purchase_request":
                title = f"采购申请提交 - {item_name}"
                content = f"您的采购申请已提交，申请物品: {item_name}，申请编号: {request_id}。请等待审批。"
                notification_type = NotificationType.APPROVAL_FLOW
                priority = "medium"
            elif approval_type == "approval_status":
                title = f"审批状态更新 - {item_name}"
                content = f"您的采购申请审批状态已更新，申请物品: {item_name}，申请编号: {request_id}，当前状态: {status}。"
                notification_type = NotificationType.APPROVAL_FLOW
                priority = "medium"
            elif approval_type == "approval_complete":
                title = f"审批完成 - {item_name}"
                content = f"您的采购申请审批已完成，申请物品: {item_name}，申请编号: {request_id}，审批结果: {status}。"
                notification_type = NotificationType.APPROVAL_FLOW
                priority = "high"
            else:
                logger.warning(f"未知的审批类型: {approval_type}")
                return None
            
            # 创建通知
            from app.schemas.notification import NotificationCreate
            
            notification_data = NotificationCreate(
                user_id=user_id,
                title=title,
                content=content,
                notification_type=notification_type,
                business_data={
                    "related_module": "purchase",
                    "related_id": request_id,
                    "priority": priority
                }
            )
            
            notification = self.notification_service.create_notification(notification_data)
            
            if notification:
                logger.info(f"为用户 {user.username} 创建了审批通知: {title}")
            
            return notification
            
        except Exception as e:
            logger.error(f"创建审批通知失败: user_id={user_id}, approval_type={approval_type}, error={str(e)}")
            return None
    
    def check_and_create_inventory_alerts(
        self,
        department_id: int,
        item_id: int,
        current_quantity: Decimal
    ) -> List[Notification]:
        """
        检查库存状态并创建相应的预警通知
        
        Args:
            department_id: 部门ID
            item_id: 物品ID
            current_quantity: 当前库存数量
            
        Returns:
            List[Notification]: 创建的通知列表
        """
        try:
            # 获取库存记录
            inventory = self.db.query(DepartmentInventory).filter(
                DepartmentInventory.department_id == department_id,
                DepartmentInventory.item_id == item_id
            ).first()
            
            if not inventory:
                logger.warning(f"库存记录不存在: department_id={department_id}, item_id={item_id}")
                return []
            
            notifications = []
            
            # 检查低库存预警
            if (inventory.min_quantity is not None and 
                current_quantity > 0 and 
                current_quantity <= inventory.min_quantity):
                
                notification = self.create_inventory_alert_notification(
                    department_id=department_id,
                    item_id=item_id,
                    alert_type="low_stock",
                    current_quantity=current_quantity,
                    threshold_value=inventory.min_quantity
                )
                if notification:
                    notifications.append(notification)
            
            # 检查缺货预警
            if current_quantity == 0:
                notification = self.create_inventory_alert_notification(
                    department_id=department_id,
                    item_id=item_id,
                    alert_type="out_of_stock",
                    current_quantity=current_quantity
                )
                if notification:
                    notifications.append(notification)
            
            # 检查超储预警
            if (inventory.max_quantity is not None and 
                current_quantity > inventory.max_quantity):
                
                notification = self.create_inventory_alert_notification(
                    department_id=department_id,
                    item_id=item_id,
                    alert_type="overstock",
                    current_quantity=current_quantity,
                    threshold_value=inventory.max_quantity
                )
                if notification:
                    notifications.append(notification)
            
            return notifications
            
        except Exception as e:
            logger.error(f"检查库存预警失败: department_id={department_id}, item_id={item_id}, error={str(e)}")
            return []
