"""
中间件
处理请求和响应的中间件
"""

from fastapi import Request, Response
from fastapi.responses import JSONResponse
from app.core.json_encoder import json_encoder
import json


class JSONMiddleware:
    """JSON序列化中间件"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            # 处理HTTP请求
            request = Request(scope, receive)
            
            # 处理响应
            async def custom_send(message):
                if message["type"] == "http.response.body":
                    # 如果是JSON响应，使用自定义编码器
                    if "content-type" in message.get("headers", []) and b"application/json" in message["headers"][b"content-type"]:
                        try:
                            # 尝试解析和重新编码JSON
                            body = message["body"]
                            if body:
                                data = json.loads(body.decode())
                                # 使用自定义编码器重新编码
                                message["body"] = json_encoder.encode(data).encode()
                        except (json.JSONDecodeError, UnicodeDecodeError):
                            # 如果解析失败，保持原样
                            pass
                
                await send(message)
            
            try:
                await self.app(scope, receive, custom_send)
            except Exception as e:
                # 如果应用抛出异常，直接传递给FastAPI的异常处理器
                raise e
        else:
            await self.app(scope, receive, send) 