"""
自定义JSON编码器
支持datetime、date等类型的序列化
"""

import json
from datetime import datetime, date
from decimal import Decimal
from typing import Any, Union
from uuid import UUID


class CustomJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，支持更多数据类型"""
    
    def default(self, obj: Any) -> Any:
        """处理特殊类型的序列化"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, date):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, UUID):
            return str(obj)
        elif hasattr(obj, '__dict__'):
            # 处理自定义对象
            return obj.__dict__
        else:
            return super().default(obj)


def json_dumps(obj: Any, **kwargs) -> str:
    """自定义JSON序列化函数"""
    return json.dumps(obj, cls=CustomJSONEncoder, **kwargs)


def json_loads(s: Union[str, bytes], **kwargs) -> Any:
    """自定义JSON反序列化函数"""
    return json.loads(s, **kwargs)


# 全局JSON编码器实例
json_encoder = CustomJSONEncoder() 