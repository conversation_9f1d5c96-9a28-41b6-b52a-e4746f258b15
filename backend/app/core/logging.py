"""
日志配置模块
配置应用的日志输出格式和处理器
"""

import logging
import sys
from typing import Optional


def setup_logging(
    level: str = "INFO",
    format_string: Optional[str] = None,
    log_to_file: bool = False,
    log_file_path: str = "logs/app.log"
) -> None:
    """
    设置应用日志配置
    
    Args:
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_string: 日志格式字符串
        log_to_file: 是否同时输出到文件
        log_file_path: 日志文件路径
    """
    # 默认日志格式
    if format_string is None:
        format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 设置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # 清除现有的处理器
    root_logger.handlers.clear()
    
    # 创建格式化器
    formatter = logging.Formatter(format_string)
    
    # 添加控制台处理器（输出到stdout）
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 如果启用文件日志，添加文件处理器
    if log_to_file:
        try:
            # 确保日志目录存在
            import os
            from pathlib import Path
            log_dir = Path(log_file_path).parent
            log_dir.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
            file_handler.setLevel(getattr(logging, level.upper()))
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        except Exception as e:
            # 如果文件日志设置失败，记录警告但不影响控制台日志
            console_handler.warning(f"无法设置文件日志: {e}")
    
    # 设置第三方库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
    logging.getLogger("passlib").setLevel(logging.WARNING)
    
    # 记录日志配置完成
    root_logger.info(f"日志配置完成 - 级别: {level}, 输出: {'控制台' + (' + 文件' if log_to_file else '')}")


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    return logging.getLogger(name)


# 默认配置
def setup_default_logging():
    """设置默认日志配置"""
    setup_logging(
        level="INFO",
        format_string="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        log_to_file=False
    )
