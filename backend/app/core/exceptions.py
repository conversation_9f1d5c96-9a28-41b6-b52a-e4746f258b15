class BusinessException(Exception):
    """业务异常基类"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(BusinessException):
    """数据验证异常"""
    
    def __init__(self, message: str, field: str = None, value: any = None):
        details = {}
        if field:
            details["field"] = field
        if value is not None:
            details["value"] = value
        super().__init__(message, "VALIDATION_ERROR", details)


class PermissionException(BusinessException):
    """权限异常"""
    
    def __init__(self, message: str, required_permission: str = None, user_id: int = None):
        details = {}
        if required_permission:
            details["required_permission"] = required_permission
        if user_id:
            details["user_id"] = user_id
        super().__init__(message, "PERMISSION_DENIED", details)


class ResourceNotFoundException(BusinessException):
    """资源不存在异常"""
    
    def __init__(self, resource_type: str, resource_id: any):
        message = f"{resource_type} with id {resource_id} not found"
        details = {
            "resource_type": resource_type,
            "resource_id": resource_id
        }
        super().__init__(message, "RESOURCE_NOT_FOUND", details)


class StateTransitionException(BusinessException):
    """状态转换异常"""
    
    def __init__(self, current_state: str, target_state: str, allowed_transitions: list = None):
        message = f"Invalid state transition from {current_state} to {target_state}"
        details = {
            "current_state": current_state,
            "target_state": target_state,
            "allowed_transitions": allowed_transitions or []
        }
        super().__init__(message, "INVALID_STATE_TRANSITION", details)


class DuplicateResourceException(BusinessException):
    """重复资源异常"""
    
    def __init__(self, resource_type: str, duplicate_fields: dict):
        message = f"{resource_type} already exists with the same values"
        details = {
            "resource_type": resource_type,
            "duplicate_fields": duplicate_fields
        }
        super().__init__(message, "DUPLICATE_RESOURCE", details)


class BusinessRuleViolationException(BusinessException):
    """业务规则违反异常"""
    
    def __init__(self, rule_name: str, rule_description: str, violation_details: dict = None):
        message = f"Business rule violation: {rule_name}"
        details = {
            "rule_name": rule_name,
            "rule_description": rule_description,
            "violation_details": violation_details or {}
        }
        super().__init__(message, "BUSINESS_RULE_VIOLATION", details)
