# 采购申请状态常量
class PurchaseRequestStatus:
    """采购申请状态常量"""
    # 基础状态
    DRAFT = "draft"                           # 草稿
    PENDING_SUBMISSION = "pending_submission" # 待提交
    UNDER_REVIEW = "under_review"             # 待审批（部门经理复核）
    UNDER_PRINCIPLE_APPROVAL = "under_principle_approval"  # 主管审批中（物品管理员主管审批）
    UNDER_FINAL_APPROVAL = "under_final_approval"          # 最终审批中（公司主管最终审批）
    APPROVED = "approved"                     # 已批准
    REJECTED = "rejected"                     # 已拒绝
    WITHDRAWN = "withdrawn"                   # 已撤回
    EXECUTED = "executed"                     # 已执行

# 操作类型常量
class FlowActionType:
    """流转操作类型常量"""
    # 基础操作
    CREATE = "create"                         # 创建申请
    SUBMIT = "submit"                         # 提交申请
    RESUBMIT = "resubmit"                     # 重新提交
    WITHDRAW = "withdraw"                     # 撤回申请
    EDIT = "edit"                             # 编辑申请
    
    # 审批操作
    REVIEW = "review"                         # 部门经理复核
    PRINCIPLE_APPROVE = "principle_approve"   # 物品管理员主管审批
    FINAL_APPROVE = "final_approve"           # 公司主管最终审批
    REJECT = "reject"                         # 拒绝申请
    RETURN = "return"                         # 退回申请
    
    # 其他操作
    DELETE = "delete"                         # 删除申请
    LOCK_PRICE = "lock_price"                 # 锁定价格

# 审批级别常量
class ApprovalLevel:
    """审批级别常量"""
    REVIEW = "review"                         # 部门经理复核
    PRINCIPLE_APPROVAL = "principle_approval" # 物品管理员主管审批
    FINAL_APPROVAL = "final_approval"         # 公司主管最终审批

# 状态流转映射
class StatusFlow:
    """状态流转映射"""
    # 状态流转规则
    FLOW_RULES = {
        PurchaseRequestStatus.DRAFT: {
            "next_states": [PurchaseRequestStatus.PENDING_SUBMISSION],
            "allowed_actions": [FlowActionType.SUBMIT, FlowActionType.EDIT, FlowActionType.DELETE],
            "description": "草稿状态，可以编辑、删除或提交申请"
        },
        PurchaseRequestStatus.PENDING_SUBMISSION: {
            "next_states": [PurchaseRequestStatus.UNDER_REVIEW],
            "allowed_actions": [FlowActionType.SUBMIT, FlowActionType.EDIT, FlowActionType.DELETE],
            "description": "待提交状态，可以编辑、删除或提交申请"
        },
        PurchaseRequestStatus.UNDER_REVIEW: {
            "next_states": [PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL, PurchaseRequestStatus.REJECTED, PurchaseRequestStatus.WITHDRAWN],
            "allowed_actions": [FlowActionType.REVIEW, FlowActionType.REJECT, FlowActionType.WITHDRAW],
            "description": "待审批状态，部门经理进行复核，可以撤回申请"
        },
        PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL: {
            "next_states": [PurchaseRequestStatus.UNDER_FINAL_APPROVAL, PurchaseRequestStatus.REJECTED, PurchaseRequestStatus.WITHDRAWN],
            "allowed_actions": [FlowActionType.PRINCIPLE_APPROVE, FlowActionType.REJECT, FlowActionType.WITHDRAW],
            "description": "主管审批中状态，物品管理员主管进行审批，可以撤回申请"
        },
        PurchaseRequestStatus.UNDER_FINAL_APPROVAL: {
            "next_states": [PurchaseRequestStatus.APPROVED, PurchaseRequestStatus.REJECTED, PurchaseRequestStatus.WITHDRAWN],
            "allowed_actions": [FlowActionType.FINAL_APPROVE, FlowActionType.REJECT, FlowActionType.WITHDRAW],
            "description": "最终审批中状态，公司主管进行最终审批，可以撤回申请"
        },
        PurchaseRequestStatus.APPROVED: {
            "next_states": [PurchaseRequestStatus.EXECUTED],
            "allowed_actions": [FlowActionType.LOCK_PRICE],
            "description": "已批准状态，申请通过所有审批环节，可以执行采购"
        },
        PurchaseRequestStatus.EXECUTED: {
            "next_states": [],
            "allowed_actions": [],
            "description": "已执行状态，采购申请已转换为采购订单，不可编辑和删除"
        },
        PurchaseRequestStatus.REJECTED: {
            "next_states": [PurchaseRequestStatus.PENDING_SUBMISSION],
            "allowed_actions": [FlowActionType.EDIT, FlowActionType.DELETE],
            "description": "已拒绝状态，可以编辑修改后重新提交或删除申请"
        },
        PurchaseRequestStatus.WITHDRAWN: {
            "next_states": [PurchaseRequestStatus.PENDING_SUBMISSION],
            "allowed_actions": [FlowActionType.EDIT, FlowActionType.DELETE],
            "description": "已撤回状态，可以编辑修改后重新提交或删除申请"
        }
    }
    
    # 状态显示名称
    STATUS_DISPLAY_NAMES = {
        PurchaseRequestStatus.DRAFT: "草稿",
        PurchaseRequestStatus.PENDING_SUBMISSION: "待提交",
        PurchaseRequestStatus.UNDER_REVIEW: "待审批",
        PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL: "主管审批中",
        PurchaseRequestStatus.UNDER_FINAL_APPROVAL: "最终审批中",
        PurchaseRequestStatus.APPROVED: "已批准",
        PurchaseRequestStatus.EXECUTED: "已执行",
        PurchaseRequestStatus.REJECTED: "已拒绝",
        PurchaseRequestStatus.WITHDRAWN: "已撤回"
    }
    
    # 操作类型显示名称
    ACTION_DISPLAY_NAMES = {
        FlowActionType.CREATE: "创建申请",
        FlowActionType.SUBMIT: "提交申请",
        FlowActionType.RESUBMIT: "重新提交",
        FlowActionType.WITHDRAW: "撤回申请",
        FlowActionType.EDIT: "编辑申请",
        FlowActionType.REVIEW: "部门经理复核",
        FlowActionType.PRINCIPLE_APPROVE: "物品管理员主管审批",
        FlowActionType.FINAL_APPROVE: "公司主管最终审批",
        FlowActionType.REJECT: "拒绝申请",
        FlowActionType.RETURN: "退回申请",
        FlowActionType.DELETE: "删除申请",
        FlowActionType.LOCK_PRICE: "锁定价格"
    }
    
    # 审批级别显示名称
    APPROVAL_LEVEL_DISPLAY_NAMES = {
        ApprovalLevel.REVIEW: "部门经理复核",
        ApprovalLevel.PRINCIPLE_APPROVAL: "物品管理员主管审批",
        ApprovalLevel.FINAL_APPROVAL: "公司主管最终审批"
    }

# 申请进度计算
class ProgressCalculator:
    """申请进度计算器"""
    
    @staticmethod
    def calculate_progress(status: str) -> dict:
        """计算申请进度"""
        progress_mapping = {
            PurchaseRequestStatus.DRAFT: {"step": 0, "total": 5, "percentage": 0, "description": "草稿状态"},
            PurchaseRequestStatus.PENDING_SUBMISSION: {"step": 0, "total": 5, "percentage": 0, "description": "待提交状态"},
            PurchaseRequestStatus.UNDER_REVIEW: {"step": 1, "total": 5, "percentage": 20, "description": "部门经理复核中"},
            PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL: {"step": 2, "total": 5, "percentage": 40, "description": "物品管理员主管审批中"},
            PurchaseRequestStatus.UNDER_FINAL_APPROVAL: {"step": 3, "total": 5, "percentage": 60, "description": "公司主管最终审批中"},
            PurchaseRequestStatus.APPROVED: {"step": 4, "total": 5, "percentage": 80, "description": "审批完成"},
            PurchaseRequestStatus.EXECUTED: {"step": 5, "total": 5, "percentage": 100, "description": "采购执行完成"},
            PurchaseRequestStatus.REJECTED: {"step": -1, "total": 5, "percentage": 0, "description": "申请被拒绝"},
            PurchaseRequestStatus.WITHDRAWN: {"step": -1, "total": 5, "percentage": 0, "description": "申请已撤回"}
        }
        
        return progress_mapping.get(status, {"step": 0, "total": 5, "percentage": 0, "description": "未知状态"})
    
    @staticmethod
    def get_next_step_description(status: str) -> str:
        """获取下一步操作描述"""
        next_step_mapping = {
            PurchaseRequestStatus.DRAFT: "请提交申请开始审批流程",
            PurchaseRequestStatus.PENDING_SUBMISSION: "请提交申请开始审批流程",
            PurchaseRequestStatus.UNDER_REVIEW: "等待部门经理复核",
            PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL: "等待物品管理员主管审批",
            PurchaseRequestStatus.UNDER_FINAL_APPROVAL: "等待公司主管最终审批",
            PurchaseRequestStatus.APPROVED: "审批完成，可以执行采购",
            PurchaseRequestStatus.EXECUTED: "采购执行完成",
            PurchaseRequestStatus.REJECTED: "申请被拒绝，请修改后重新提交",
            PurchaseRequestStatus.WITHDRAWN: "申请已撤回，请修改后重新提交"
        }
        
        return next_step_mapping.get(status, "未知状态")
