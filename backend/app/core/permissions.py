"""
权限常量定义
定义系统中所有的权限点和角色
"""

from enum import Enum
from typing import Dict, List, Tuple

class PermissionModule(str, Enum):
    """权限模块枚举"""
    USER = "user"
    ROLE = "role"
    ITEM = "item"
    INVENTORY = "inventory"
    PURCHASE = "purchase"
    SUPPLIER = "supplier"
    REPORT = "report"
    SYSTEM = "system"
    DEPARTMENT = "department"

class PermissionAction(str, Enum):
    """权限操作枚举"""
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"

    APPROVE = "approve"
    REJECT = "reject"
    ASSIGN = "assign"
    REVOKE = "revoke"

# 权限定义 (code, name, description, module, category)
PERMISSION_DEFINITIONS: List[Tuple[str, str, str, str, str]] = [
    # 用户管理权限
    ("user.create", "创建用户", "创建新用户账号", "user", "用户管理"),
    ("user.read", "查看用户", "查看用户信息", "user", "用户管理"),
    ("user.update", "修改用户", "修改用户基本信息", "user", "用户管理"),
    # ("user.delete", "删除用户", "删除用户账号", "user", "用户管理"),  # 删除功能已移除
    ("user.reset_password", "重置密码", "重置用户密码", "user", "用户管理"),

    ("user.manage_status", "管理用户状态", "启用/禁用用户账号", "user", "用户管理"),
    
    # 角色权限管理
    ("role.create", "创建角色", "创建新的角色", "role", "角色管理"),
    ("role.read", "查看角色", "查看角色信息", "role", "角色管理"),
    ("role.update", "修改角色", "修改角色信息和权限", "role", "角色管理"),
    ("role.delete", "删除角色", "删除自定义角色", "role", "角色管理"),
    ("role.assign", "分配角色", "为用户分配角色", "role", "角色管理"),
    ("role.revoke", "回收角色", "回收用户角色", "role", "角色管理"),
    ("permission.read", "查看权限", "查看系统权限", "role", "权限管理"),
    ("permission.assign", "分配权限", "为角色分配权限", "role", "权限管理"),
    
    # 物品管理权限
    ("item.create", "创建物品", "创建新物品", "item", "物品管理"),
    ("item.read", "查看物品", "查看物品信息", "item", "物品管理"),
    ("item.update", "修改物品", "修改物品信息", "item", "物品管理"),
    ("item.delete", "删除物品", "删除物品", "item", "物品管理"),

    ("item.category_manage", "分类管理", "管理物品分类", "item", "物品管理"),
    
    # 库存管理权限
    ("inventory.read", "查看库存", "查看库存信息", "inventory", "库存管理"),
    ("inventory.update", "更新库存", "更新库存数量", "inventory", "库存管理"),
    ("inventory.transfer", "库存调拨", "在部门间调拨库存", "inventory", "库存管理"),
    ("inventory.count", "库存盘点", "执行库存盘点", "inventory", "库存管理"),
    ("inventory.adjust", "库存调整", "调整库存数量", "inventory", "库存管理"),
    ("inventory.alert", "库存预警", "设置和管理库存预警", "inventory", "库存管理"),
    
    # 采购管理权限
    ("purchase.request", "提交采购申请", "创建采购申请", "purchase", "采购管理"),
    ("purchase.read", "查看采购信息", "查看采购申请和订单", "purchase", "采购管理"),
    ("purchase.update", "修改采购信息", "修改采购申请", "purchase", "采购管理"),
    ("purchase.delete", "删除采购申请", "删除采购申请", "purchase", "采购管理"),
    ("purchase.review", "复核采购申请", "部门经理复核采购申请", "purchase", "采购管理"),
    ("purchase.principle_approve", "主管审批", "物品管理员主管审批", "purchase", "采购管理"),
    ("purchase.approve", "审批采购申请", "审批部门采购申请", "purchase", "采购管理"),
    ("purchase.reject", "拒绝采购申请", "拒绝采购申请", "purchase", "采购管理"),
    ("purchase.execute", "执行采购", "执行采购订单", "purchase", "采购管理"),
    ("purchase.receive", "到货处理", "处理采购到货", "purchase", "采购管理"),
    ("purchase.final_approve", "最终审批", "采购的最终审批", "purchase", "采购管理"),
    
    # 供应商管理权限
    ("supplier.create", "创建供应商", "创建新供应商", "supplier", "供应商管理"),
    ("supplier.read", "查看供应商", "查看供应商信息", "supplier", "供应商管理"),
    ("supplier.update", "修改供应商", "修改供应商信息", "supplier", "供应商管理"),
    ("supplier.delete", "删除供应商", "删除供应商", "supplier", "供应商管理"),
    ("supplier.price_manage", "价格管理", "管理供应商价格", "supplier", "供应商管理"),
    ("supplier.evaluate", "供应商评估", "对供应商进行评估", "supplier", "供应商管理"),
    
    # 报表分析权限
    ("report.usage", "使用统计报表", "查看物品使用统计", "report", "报表分析"),
    ("report.inventory", "库存报表", "查看库存相关报表", "report", "报表分析"),
    ("report.purchase", "采购报表", "查看采购相关报表", "report", "报表分析"),
    ("report.cost", "成本分析", "查看成本分析报表", "report", "报表分析"),

    ("report.admin", "报表管理", "管理报表配置", "report", "报表分析"),
    
    # 部门管理权限
    ("department.create", "创建部门", "创建新部门", "department", "部门管理"),
    ("department.read", "查看部门", "查看部门信息", "department", "部门管理"),
    ("department.update", "修改部门", "修改部门信息", "department", "部门管理"),
    ("department.delete", "删除部门", "删除部门", "department", "部门管理"),
    ("department.manage_users", "管理部门用户", "管理部门内用户", "department", "部门管理"),
    
    # 系统管理权限
    ("system.config", "系统配置", "修改系统配置", "system", "系统管理"),
    ("system.log", "查看日志", "查看系统日志", "system", "系统管理"),
    ("system.backup", "数据备份", "执行数据备份", "system", "系统管理"),
    ("system.restore", "数据恢复", "执行数据恢复", "system", "系统管理"),
    ("system.audit", "审计管理", "查看和管理审计日志", "system", "系统管理"),

]

# 预设角色定义 (code, name, description, permissions)
ROLE_DEFINITIONS: List[Tuple[str, str, str, List[str]]] = [
    (
        "super_admin",
        "超级管理员",
        "系统最高权限，拥有所有功能权限",
        [perm[0] for perm in PERMISSION_DEFINITIONS]  # 所有权限
    ),
    (
        "system_admin",
        "系统管理员",
        "系统管理权限，负责用户、角色、系统配置",
        [
            "user.create", "user.read", "user.update", "user.delete", 
            "user.reset_password", "user.manage_status",
            "role.create", "role.read", "role.update", "role.delete",
            "role.assign", "role.revoke", "permission.read", "permission.assign",
            "department.create", "department.read", "department.update", "department.delete",
            "department.manage_users", "system.config", "system.log", "system.audit",
        
        ]
    ),
    (
        "item_manager",
        "物品管理员",
        "物品相关管理权限，负责物品、供应商、采购审批",
        [
            "item.create", "item.read", "item.update", "item.delete",
            "item.category_manage",
            "inventory.read", "inventory.update", "inventory.adjust", "inventory.alert",
            "supplier.create", "supplier.read", "supplier.update", "supplier.delete",
            "supplier.price_manage", "supplier.evaluate",
            "purchase.read", "purchase.approve", "purchase.reject", "purchase.final_approve",
            "purchase.execute", "purchase.receive",
            "report.usage", "report.inventory", "report.purchase", "report.cost"
        ]
    ),
    (
        "department_manager",
        "部门经理",
        "部门管理权限，负责部门采购审批、人员管理",
        [
            "user.read", "department.read", "department.manage_users",
            "item.read", "inventory.read",
            "purchase.request", "purchase.read", "purchase.update", "purchase.approve", "purchase.reject",
            "report.usage", "report.inventory", "report.purchase"
        ]
    ),
    (
        "department_item_admin",
        "部门物品管理员",
        "部门物品管理权限，负责部门库存、采购申请",
        [
            "item.read", "inventory.read", "inventory.update", "inventory.count",
            "purchase.request", "purchase.read", "purchase.update", "purchase.delete",
            "supplier.read", "report.usage", "report.inventory"
        ]
    ),
    (
        "purchaser",
        "采购员",
        "采购执行权限，负责采购单处理、供应商对接",
        [
            "item.read", "supplier.read", "supplier.update", "supplier.price_manage",
            "purchase.read", "purchase.update", "purchase.execute", "purchase.receive",
            "report.purchase", "report.cost"
        ]
    ),
    (
        "warehouse_keeper",
        "仓库管理员",
        "仓库操作权限，负责出入库、库存盘点",
        [
            "item.read", "inventory.read", "inventory.update", "inventory.transfer",
            "inventory.count", "inventory.adjust", "purchase.receive", "report.inventory"
        ]
    ),
    (
        "user",
        "普通用户",
        "基础使用权限，只能进行物品领取等基本操作",
        [
            "item.read", "inventory.read"
        ]
    ),
]

# 权限分组结构（用于前端显示）
PERMISSION_GROUPS = {
    "用户管理": {
        "module": "user",
        "permissions": [
            "user.create", "user.read", "user.update", "user.delete",
            "user.reset_password", "user.manage_status"
        ]
    },
    "角色权限": {
        "module": "role",
        "permissions": [
            "role.create", "role.read", "role.update", "role.delete",
            "role.assign", "role.revoke", "permission.read", "permission.assign"
        ]
    },
    "物品管理": {
        "module": "item",
        "permissions": [
            "item.create", "item.read", "item.update", "item.delete",
            "item.category_manage"
        ]
    },
    "库存管理": {
        "module": "inventory",
        "permissions": [
            "inventory.read", "inventory.update", "inventory.transfer",
            "inventory.count", "inventory.adjust", "inventory.alert"
        ]
    },
    "采购管理": {
        "module": "purchase",
        "permissions": [
            "purchase.request", "purchase.read", "purchase.update", "purchase.delete",
            "purchase.review", "purchase.principle_approve", "purchase.approve", "purchase.reject",
            "purchase.execute", 
            "purchase.receive", "purchase.final_approve"
        ]
    },
    "供应商管理": {
        "module": "supplier",
        "permissions": [
            "supplier.create", "supplier.read", "supplier.update", "supplier.delete",
            "supplier.price_manage", "supplier.evaluate"
        ]
    },
    "报表分析": {
        "module": "report",
        "permissions": [
            "report.usage", "report.inventory", "report.purchase",
            "report.cost", "report.admin"
        ]
    },
    "部门管理": {
        "module": "department",
        "permissions": [
            "department.create", "department.read", "department.update",
            "department.delete", "department.manage_users"
        ]
    },
    "系统管理": {
        "module": "system",
        "permissions": [
            "system.config", "system.log", "system.backup",
            "system.restore", "system.audit"
        ]
    }
}

def get_permission_by_code(code: str) -> Tuple[str, str, str, str, str]:
    """根据权限代码获取权限信息"""
    for perm in PERMISSION_DEFINITIONS:
        if perm[0] == code:
            return perm
    return None

def get_role_permissions(role_code: str) -> List[str]:
    """获取角色的权限列表"""
    for role in ROLE_DEFINITIONS:
        if role[0] == role_code:
            return role[3]
    return []

def get_all_permissions() -> List[str]:
    """获取所有权限代码"""
    return [perm[0] for perm in PERMISSION_DEFINITIONS]

def get_permissions_by_module(module: str) -> List[str]:
    """获取指定模块的权限"""
    return [perm[0] for perm in PERMISSION_DEFINITIONS if perm[3] == module] 