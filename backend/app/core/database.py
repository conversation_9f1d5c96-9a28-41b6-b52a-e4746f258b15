from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker, declarative_base
from app.core.config import settings

# 根据数据库类型创建引擎
if 'sqlite' in settings.database_url:
    engine = create_engine(
        settings.database_url,
        connect_args={"check_same_thread": False}  # SQLite特殊配置
    )
else:
    # PostgreSQL/MySQL等不需要特殊配置
    engine = create_engine(settings.database_url)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

def get_db():
    """获取数据库会话的依赖项"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """创建所有数据库表"""
    Base.metadata.create_all(bind=engine) 