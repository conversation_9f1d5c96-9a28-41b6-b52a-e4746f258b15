from pydantic_settings import BaseSettings
from pydantic import ConfigDict
from typing import Optional, List

class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基础配置
    app_name: str = "工厂物品管理系统"
    debug: bool = False
    
    # 数据库配置
    database_url: str = "****************************************/bizlink_idm"

    # JWT配置
    jwt_secret_key: str = "your-secret-key-change-this-in-production"
    jwt_algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # CORS配置
    allowed_origins: List[str] = ["http://localhost", "http://localhost:3000"]
    
    # 文件上传配置
    upload_dir: str = "uploads"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    
    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra='ignore'
    )

settings = Settings() 