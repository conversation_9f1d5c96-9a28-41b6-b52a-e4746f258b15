"""
工具函数
提供常用的工具方法
"""

from typing import Any, Dict, List, Optional
from datetime import datetime, date
from decimal import Decimal
from app.core.json_encoder import json_encoder


def serialize_datetime(obj: Any) -> Any:
    """序列化包含datetime的对象"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, date):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, dict):
        return {key: serialize_datetime(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [serialize_datetime(item) for item in obj]
    else:
        return obj


def format_response_data(data: Any) -> Any:
    """格式化响应数据，确保可以正确序列化"""
    return serialize_datetime(data)


def create_success_response(data: Any = None, message: str = "success", status_code: int = 200) -> Dict:
    """创建标准成功响应"""
    response = {
        "success": True,
        "message": message,
        "data": format_response_data(data) if data is not None else None
    }
    return response


def create_error_response(message: str = "error", status_code: int = 400, details: Any = None) -> Dict:
    """创建标准错误响应"""
    response = {
        "success": False,
        "message": message,
        "error_code": status_code
    }
    if details:
        response["details"] = format_response_data(details)
    return response


def paginate_response(items: List, total: int, page: int, page_size: int) -> Dict:
    """创建分页响应"""
    return {
        "items": format_response_data(items),
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": (total + page_size - 1) // page_size
    } 