"""
Kiosk API路由
为Kiosk自助终端提供物品领取相关的API接口
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, desc, or_
from typing import List, Optional
from datetime import datetime
import uuid

from app.core.database import get_db
from app.models.usage import ItemUsageRecord
from app.models.item import Item
from app.models.user import User
from app.models.inventory import DepartmentInventory, InventoryChangeRecord
from app.services.kiosk_auth import (
    ClientAuthService, 
    get_current_client_user
)
from app.services.qr_code_service import QRCodeService
from app.services.inventory_service import InventoryService
from app.schemas.kiosk import (
    ClientLoginRequest, ClientLoginResponse, ClientUserInfo,
    ItemScanRequest, ItemScanResponse, EmployeeScanRequest, EmployeeScanResponse,
    PickupRequest, PickupResponse, PickupHistoryResponse,
    UndoPickupRequest, UndoPickupResponse
)

router = APIRouter()


@router.post("/auth/login", response_model=ClientLoginResponse)
async def client_login(
    login_data: ClientLoginRequest,
    db: Session = Depends(get_db)
):
    """客户端登录"""
    user = ClientAuthService.authenticate_user(db, login_data.username, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    # 验证是否具有部门仓库角色
    if not ClientAuthService.verify_dept_warehouse_role(user, db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要部门仓库角色"
        )
    
    # 创建访问令牌
    access_token = ClientAuthService.create_access_token(
        data={"sub": user.username, "user_id": user.id, "department_id": user.department_id}
    )
    
    # 获取用户权限
    permissions = ClientAuthService.get_user_permissions(user, db)
    
    return ClientLoginResponse(
        access_token=access_token,
        token_type="bearer",
        user_info=ClientUserInfo(
            user_id=user.id,
            username=user.username,
            full_name=user.full_name,
            department_id=user.department_id,
            department_name=user.department.name if user.department else None,
            permissions=permissions
        )
    )


@router.get("/auth/verify", response_model=ClientUserInfo)
async def verify_token(
    current_user: User = Depends(get_current_client_user),
    db: Session = Depends(get_db)
):
    """验证令牌并获取用户信息"""
    permissions = ClientAuthService.get_user_permissions(current_user, db)
    
    return ClientUserInfo(
        user_id=current_user.id,
        username=current_user.username,
        full_name=current_user.full_name,
        department_id=current_user.department_id,
        department_name=current_user.department.name if current_user.department else None,
        permissions=permissions
    )


@router.post("/items/scan", response_model=ItemScanResponse)
async def scan_item(
    scan_request: ItemScanRequest,
    current_user: User = Depends(get_current_client_user),
    db: Session = Depends(get_db)
):
    """扫描物品二维码"""
    try:
        # 验证用户权限
        if not ClientAuthService.verify_dept_warehouse_role(current_user, db):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要部门仓库角色"
            )
        
        # 使用QRCodeService查询物品
        item = QRCodeService.get_item_by_qr_code(scan_request.qr_code, db)
        
        # 如果新格式解析失败，尝试直接使用原始内容作为编码（向后兼容）
        if not item:
            item = db.query(Item).filter(
                and_(
                    Item.code == scan_request.qr_code,
                    Item.is_active == True
                )
            ).first()
        
        if not item:
            return ItemScanResponse(
                success=False,
                message="无效的物品二维码"
            )
        
        # 查询部门库存（仅限本部门）
        inventory = db.query(DepartmentInventory).filter(
            and_(
                DepartmentInventory.item_id == item.id,
                DepartmentInventory.department_id == current_user.department_id,  # 只查询本部门库存
                DepartmentInventory.is_active == True
            )
        ).first()
        
        if not inventory:
            return ItemScanResponse(
                success=False,
                message="该物品在您的部门中没有库存"
            )
        
        return ItemScanResponse(
            success=True,
            item_info={
                "item_id": item.id,
                "item_name": item.name,
                "item_code": item.code,
                "category_name": item.category.name if item.category else None,
                "brand": item.brand,
                "spec_material": item.spec_material,
                "size_dimension": item.size_dimension,
                "purchase_unit": item.purchase_unit,
                "inventory_unit": item.inventory_unit,
                "qty_per_up": item.qty_per_up,
                "available_stock": float(inventory.current_quantity or 0),
                "min_quantity": float(inventory.min_quantity) if inventory.min_quantity else 0,
                "image_url": item.image_url
            },
            message="物品扫描成功"
        )
        
    except Exception as e:
        return ItemScanResponse(
            success=False,
            message=f"扫描失败: {str(e)}"
        )

@router.get("/items/available", response_model=List[dict])
async def get_available_items(
    query: str = Query("", description="筛选关键词（物品编码或名称）"),
    current_user: User = Depends(get_current_client_user),
    db: Session = Depends(get_db)
):
    """获取有库存的物品列表"""
    try:
        # 验证用户权限
        if not ClientAuthService.verify_dept_warehouse_role(current_user, db):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要部门仓库角色"
            )
        
        # 构建查询条件
        query_conditions = [
            Item.is_active == True
        ]
        
        # 如果有筛选关键词，添加模糊查询条件
        if query.strip():
            query_conditions.append(
                or_(
                    Item.code.ilike(f"%{query.strip()}%"),
                    Item.name.ilike(f"%{query.strip()}%")
                )
            )
        
        # 查询有库存的物品（仅限本部门）
        items_with_inventory = db.query(Item, DepartmentInventory).join(
            DepartmentInventory,
            and_(
                Item.id == DepartmentInventory.item_id,
                DepartmentInventory.department_id == current_user.department_id,  # 只查询本部门库存
                DepartmentInventory.is_active == True,
                DepartmentInventory.current_quantity > 0  # 使用当前库存数量而不是available_stock
            )
        ).filter(*query_conditions).all()
        
        # 构建结果
        result = []
        for item, inventory in items_with_inventory:
            result.append({
                "item_id": item.id,
                "item_name": item.name,
                "item_code": item.code,
                "category_name": item.category.name if item.category else None,
                "brand": item.brand,
                "spec_material": item.spec_material,
                "size_dimension": item.size_dimension,
                "purchase_unit": item.purchase_unit,
                "inventory_unit": item.inventory_unit,
                "qty_per_up": item.qty_per_up,
                "available_stock": float(inventory.current_quantity or 0),
                "min_quantity": float(inventory.min_quantity) if inventory.min_quantity else 0,
                "image_url": item.image_url
            })
        
        # 按库存数量降序排序
        result.sort(key=lambda x: x['available_stock'], reverse=True)
        
        return result
        
    except Exception as e:
        print(f"获取可用物品列表错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取失败: {str(e)}"
        )

@router.get("/employees/search", response_model=List[dict])
async def search_employees(
    query: str = Query(..., description="员工用户名或工号"),
    current_user: User = Depends(get_current_client_user),
    db: Session = Depends(get_db)
):
    """搜索员工（仅限本部门）- 支持用户名或工号搜索"""
    try:
        # 验证用户权限
        if not ClientAuthService.verify_dept_warehouse_role(current_user, db):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要部门仓库角色"
            )
        
        # 搜索员工（仅限本部门）- 支持用户名或工号
        employee = db.query(User).filter(
            and_(
                User.is_active == True,
                or_(
                    User.username == query,           # 通过用户名搜索
                    User.employee_id == query         # 通过工号搜索
                ),
                User.department_id == current_user.department_id  # 只查询本部门员工
            )
        ).first()
        
        if not employee:
            return []
        
        return [{
            "employee_id": employee.id,
            "employee_code": employee.employee_id,
            "employee_name": employee.display_name or employee.full_name or employee.username,
            "department_id": employee.department_id,
            "department_name": employee.department.name if employee.department else None,
            "position": employee.position or "员工"
        }]
        
    except Exception as e:
        print(f"搜索员工错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索失败: {str(e)}"
        )


@router.post("/employees/scan", response_model=EmployeeScanResponse)
async def scan_employee(
    scan_request: EmployeeScanRequest,
    current_user: User = Depends(get_current_client_user),
    db: Session = Depends(get_db)
):
    """扫描员工工卡"""
    try:
        # 验证用户权限
        if not ClientAuthService.verify_dept_warehouse_role(current_user, db):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要部门仓库角色"
            )
        
        # 使用QRCodeService查询员工（仅限本部门）
        employee = QRCodeService.get_employee_by_qr_code(scan_request.qr_code, db, current_user.department_id)
        
        # 如果新格式解析失败，尝试直接使用原始内容作为编码（向后兼容）
        if not employee:
            employee = db.query(User).filter(
                and_(
                    User.employee_id == scan_request.qr_code,
                    User.is_active == True,
                    User.department_id == current_user.department_id  # 只查询本部门员工
                )
            ).first()
        
        if not employee:
            return EmployeeScanResponse(
                success=False,
                message="无效的员工工卡"
            )
        
        return EmployeeScanResponse(
            success=True,
            employee_info={
                "employee_id": employee.id,
                "employee_code": employee.employee_id,
                "employee_name": employee.full_name,
                "department_id": employee.department_id,
                "department_name": employee.department.name if employee.department else None,
                "position": employee.position
            },
            message="员工验证成功"
        )
        
    except Exception as e:
        return EmployeeScanResponse(
            success=False,
            message=f"验证失败: {str(e)}"
        )


@router.post("/pickup/execute", response_model=PickupResponse)
async def execute_pickup(
    pickup_request: PickupRequest,
    current_user: User = Depends(get_current_client_user),
    db: Session = Depends(get_db)
):
    """执行物品领取操作"""
    try:
        # 验证用户权限
        if not ClientAuthService.verify_dept_warehouse_role(current_user, db):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要部门仓库角色"
            )
        
        # 验证物品是否存在
        item = db.query(Item).filter(Item.id == pickup_request.item_id).first()
        if not item:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="物品不存在"
            )
        
        # 验证员工是否存在
        employee = db.query(User).filter(User.id == pickup_request.employee_id).first()
        if not employee:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="员工不存在"
            )
        
        # 查询部门库存
        inventory = db.query(DepartmentInventory).filter(
            and_(
                DepartmentInventory.item_id == pickup_request.item_id,
                DepartmentInventory.department_id == current_user.department_id,
                DepartmentInventory.is_active == True
            )
        ).first()
        
        if not inventory:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该物品在您的部门中没有库存"
            )
        
        # 验证库存是否充足
        if inventory.current_quantity < pickup_request.quantity:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"库存不足，当前库存: {inventory.current_quantity}"
            )
        
        try:
            # 1. 使用库存服务进行库存扣减
            from decimal import Decimal
            pickup_quantity = Decimal(str(pickup_request.quantity))
            before_qty = inventory.current_quantity
            
            InventoryService.change_stock(
                db=db,
                department_id=current_user.department_id,
                item_id=pickup_request.item_id,
                change_amount=-pickup_quantity,  # 负数表示减少库存
                operator_id=current_user.id,
                reason="员工领取"
            )
            
            # 2. 获取最新的变更记录ID（用于关联使用记录）
            latest_change = db.query(InventoryChangeRecord).filter(
                and_(
                    InventoryChangeRecord.department_id == current_user.department_id,
                    InventoryChangeRecord.item_id == pickup_request.item_id,
                    InventoryChangeRecord.operator_id == current_user.id
                )
            ).order_by(desc(InventoryChangeRecord.id)).first()
            
            # 3. 创建使用记录
            usage_record = ItemUsageRecord(
                record_id=f"UR{datetime.utcnow().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:8]}",
                item_id=pickup_request.item_id,
                department_id=current_user.department_id,
                employee_id=pickup_request.employee_id,
                quantity=pickup_quantity,
                unit=pickup_request.unit,
                usage_purpose=pickup_request.usage_purpose,
                item_qr_code=pickup_request.item_qr_code,
                user_card_code=pickup_request.user_card_code,
                status="confirmed",
                usage_time=datetime.utcnow(),
                confirmed_at=datetime.utcnow(),
                confirmed_by=current_user.id,
                location=pickup_request.location,
                device_info=pickup_request.device_info,
                inventory_change_id=latest_change.id if latest_change else None,
                notes=pickup_request.notes
            )
            db.add(usage_record)
            
            # 4. 获取更新后的库存数量
            updated_inventory = db.query(DepartmentInventory).filter(
                and_(
                    DepartmentInventory.item_id == pickup_request.item_id,
                    DepartmentInventory.department_id == current_user.department_id,
                    DepartmentInventory.is_active == True
                )
            ).first()
            
            # 提交事务
            db.commit()
            
            return PickupResponse(
                success=True,
                record_id=usage_record.record_id,
                message="物品领取成功",
                inventory_change={
                    "quantity_before": float(before_qty),
                    "quantity_after": float(updated_inventory.current_quantity if updated_inventory else 0),
                    "change_type": "pickup_out"
                }
            )
            
        except Exception as e:
            db.rollback()
            raise e
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"领取操作失败: {str(e)}"
        )


@router.get("/history/pickup", response_model=List[PickupHistoryResponse])
async def get_pickup_history(
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    item_id: Optional[int] = Query(None, description="物品ID"),
    employee_id: Optional[int] = Query(None, description="员工ID"),
    limit: int = Query(50, description="限制数量"),
    offset: int = Query(0, description="偏移量"),
    current_user: User = Depends(get_current_client_user),
    db: Session = Depends(get_db)
):
    """获取领取历史"""
    # 验证用户权限
    if not ClientAuthService.verify_dept_warehouse_role(current_user, db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要部门仓库角色"
        )
    
    query = db.query(ItemUsageRecord).filter(
        and_(
            ItemUsageRecord.department_id == current_user.department_id,
            ItemUsageRecord.status == "confirmed"  # 只显示已确认的记录，过滤掉已撤销的记录
        )
    )
    
    # 添加筛选条件
    if start_date:
        query = query.filter(ItemUsageRecord.usage_time >= start_date)
    if end_date:
        query = query.filter(ItemUsageRecord.usage_time <= end_date)
    if item_id:
        query = query.filter(ItemUsageRecord.item_id == item_id)
    if employee_id:
        query = query.filter(ItemUsageRecord.employee_id == employee_id)
    
    # 排序和分页
    records = query.options(
        joinedload(ItemUsageRecord.item),
        joinedload(ItemUsageRecord.employee),
        joinedload(ItemUsageRecord.inventory_change)
    ).order_by(desc(ItemUsageRecord.usage_time)).offset(offset).limit(limit).all()
    
    # 转换为响应模型
    history_list = []
    for record in records:
        history_list.append(PickupHistoryResponse(
            record_id=record.record_id,
            item_name=record.item.name if record.item else None,
            item_code=record.item.code if record.item else None,
            employee_name=record.employee.full_name if record.employee else None,
            quantity=float(record.quantity),
            unit=record.unit,
            usage_time=record.usage_time,
            location=record.location,
            notes=record.notes,
            status=record.status.value if record.status else "confirmed"
        ))
    
    return history_list


@router.post("/pickup/undo", response_model=UndoPickupResponse)
async def undo_pickup(
    undo_request: UndoPickupRequest,
    current_user: User = Depends(get_current_client_user),
    db: Session = Depends(get_db)
):
    """撤销物品领取操作"""
    try:
        # 验证用户权限
        if not ClientAuthService.verify_dept_warehouse_role(current_user, db):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要部门仓库角色"
            )
        
        # 查找领取记录
        usage_record = db.query(ItemUsageRecord).filter(
            and_(
                ItemUsageRecord.record_id == undo_request.record_id,
                ItemUsageRecord.department_id == current_user.department_id,
                ItemUsageRecord.status == "confirmed"  # 只允许撤销已确认的记录
            )
        ).first()
        
        if not usage_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="领取记录不存在或无法撤销"
            )
        
        # 检查是否已经撤销过
        if usage_record.status == "cancelled":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该记录已经被撤销"
            )
        
        # 检查是否在允许撤销的时间范围内（5分钟内）
        time_diff = datetime.utcnow() - usage_record.usage_time
        if time_diff.total_seconds() > 300:  # 5分钟 = 300秒
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只能撤销5分钟内的领取记录"
            )
        
        # 查询部门库存
        inventory = db.query(DepartmentInventory).filter(
            and_(
                DepartmentInventory.item_id == usage_record.item_id,
                DepartmentInventory.department_id == current_user.department_id,
                DepartmentInventory.is_active == True
            )
        ).first()
        
        if not inventory:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该物品在您的部门中没有库存记录"
            )
        
        try:
            # 1. 使用库存服务进行库存恢复
            from decimal import Decimal
            undo_quantity = Decimal(str(usage_record.quantity))
            before_qty = inventory.current_quantity
            
            InventoryService.change_stock(
                db=db,
                department_id=current_user.department_id,
                item_id=usage_record.item_id,
                change_amount=undo_quantity,  # 正数表示增加库存
                operator_id=current_user.id,
                reason="撤销领取"
            )
            
            # 2. 获取最新的变更记录ID（用于关联撤销记录）
            latest_change = db.query(InventoryChangeRecord).filter(
                and_(
                    InventoryChangeRecord.department_id == current_user.department_id,
                    InventoryChangeRecord.item_id == usage_record.item_id,
                    InventoryChangeRecord.operator_id == current_user.id
                )
            ).order_by(desc(InventoryChangeRecord.id)).first()
            
            # 3. 更新使用记录状态
            usage_record.status = "cancelled"
            usage_record.cancelled_at = datetime.utcnow()
            usage_record.cancelled_by = current_user.id
            usage_record.undo_inventory_change_id = latest_change.id if latest_change else None
            usage_record.notes = f"撤销领取记录，操作人: {current_user.full_name}"
            
            # 4. 获取更新后的库存数量
            updated_inventory = db.query(DepartmentInventory).filter(
                and_(
                    DepartmentInventory.item_id == usage_record.item_id,
                    DepartmentInventory.department_id == current_user.department_id,
                    DepartmentInventory.is_active == True
                )
            ).first()
            
            # 提交事务
            db.commit()
            
            return UndoPickupResponse(
                success=True,
                message="撤销领取成功",
                inventory_change={
                    "quantity_before": float(before_qty),
                    "quantity_after": float(updated_inventory.current_quantity if updated_inventory else 0),
                    "change_type": "pickup_undo"
                }
            )
            
        except Exception as e:
            db.rollback()
            raise e
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"撤销领取操作失败: {str(e)}"
        )
