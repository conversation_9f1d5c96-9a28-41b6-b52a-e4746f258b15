from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from app.core.config import settings
from app.core.database import create_tables
from app.core.middleware import JSONMiddleware
from app.core.logging import setup_default_logging
from app.api.kiosk import router as kiosk_api_router
from app.core.exceptions import PermissionException, BusinessException

from app.apps.admin.routes import router as admin_router
from app.apps.kiosk.routes import router as kiosk_router
from app.apps.inbound.routes import router as inbound_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    setup_default_logging()  # 配置日志
    create_tables()
    yield
    # 关闭时执行
    pass

app = FastAPI(
    title="工厂物品管理系统 API",
    description="BizLinkSpeedy IDM - 工厂物品耗材管理系统",
    version="1.0.0",
    lifespan=lifespan
)

# 配置中间件
app.add_middleware(JSONMiddleware)  # JSON序列化中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含路由
app.include_router(kiosk_api_router, prefix="/api/kiosk", tags=["Kiosk API"])

app.include_router(admin_router, prefix="/api/admin", tags=["Admin 管理端"])
app.include_router(kiosk_router, prefix="/api/kiosk-app", tags=["Kiosk 应用"])
app.include_router(inbound_router, prefix="/api/inbound", tags=["Inbound 入库端"])

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")
# 挂载上传文件
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# SPA fallback - 为深链提供支持（单SPA，三个前缀路径共用一个入口）
@app.get("/{client:admin|kiosk|inbound}")
@app.get("/{client:admin|kiosk|inbound}/{full_path:path}")
async def spa_entry(client: str, full_path: str | None = None):
    from pathlib import Path
    from fastapi.responses import FileResponse
    return FileResponse(Path(f"static/{client}/index.html"))


@app.get("/")
async def root():
    return {"message": "工厂物品管理系统 API 服务已启动"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}


# 全局异常处理器
@app.exception_handler(PermissionException)
async def permission_exception_handler(request, exc: PermissionException):
    """处理权限异常"""
    return HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail=exc.message
    )


@app.exception_handler(BusinessException)
async def business_exception_handler(request, exc: BusinessException):
    """处理业务异常"""
    return HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail=exc.message
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    """处理通用异常"""
    import logging
    import traceback
    
    # 记录错误日志
    logging.error(f"Unhandled exception: {str(exc)}")
    logging.error(f"Request URL: {request.url}")
    logging.error(f"Request method: {request.method}")
    logging.error(f"Traceback: {traceback.format_exc()}")
    
    # 根据异常类型返回不同的错误信息
    if "database" in str(exc).lower() or "connection" in str(exc).lower():
        return HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="数据库连接失败，请稍后重试"
        )
    elif "timeout" in str(exc).lower():
        return HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="请求超时，请稍后重试"
        )
    elif "memory" in str(exc).lower() or "resource" in str(exc).lower():
        return HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="服务器资源不足，请稍后重试"
        )
    else:
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误，请联系管理员"
        ) 