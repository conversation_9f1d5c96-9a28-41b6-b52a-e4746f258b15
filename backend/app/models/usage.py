"""
物品领取记录模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Numeric, Enum, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base
from enum import Enum as PyEnum


class UsageStatus(str, PyEnum):
    """使用状态枚举"""
    pending = "pending"      # 待确认
    confirmed = "confirmed"  # 已确认
    cancelled = "cancelled"  # 已取消


class ItemUsageRecord(Base):
    """物品使用记录表"""
    __tablename__ = "item_usage_records"
    
    id = Column(Integer, primary_key=True, index=True)
    record_id = Column(String(50), unique=True, index=True, comment="记录唯一标识")
    item_id = Column(Integer, nullable=False, comment="物品ID")
    department_id = Column(Integer, nullable=False, comment="部门ID")
    employee_id = Column(Integer, nullable=False, comment="员工ID")
    quantity = Column(Numeric(15, 4), nullable=False, comment="领取数量（支持小数）")
    unit = Column(String(20), nullable=False, comment="领取单位")
    usage_purpose = Column(Text, comment="使用目的")
    item_qr_code = Column(String(200), comment="物品二维码数据")
    user_card_code = Column(String(200), comment="工卡二维码数据")
    status = Column(Enum(UsageStatus), default=UsageStatus.confirmed, comment="状态")
    usage_time = Column(DateTime(timezone=True), server_default=func.now(), comment="使用时间")
    confirmed_at = Column(DateTime(timezone=True), comment="确认时间")
    confirmed_by = Column(Integer, comment="确认人")
    location = Column(String(200), comment="领取地点")
    device_info = Column(Text, comment="设备信息")
    inventory_change_id = Column(Integer, nullable=True, comment="关联的库存变更记录ID")
    notes = Column(Text, comment="备注")
    cancelled_at = Column(DateTime(timezone=True), comment="撤销时间")
    cancelled_by = Column(Integer, comment="撤销人")
    undo_inventory_change_id = Column(Integer, nullable=True, comment="撤销操作关联的库存变更记录ID")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_usage_dept_item', 'department_id', 'item_id'),
        Index('idx_usage_time', 'usage_time'),
        Index('idx_usage_status', 'status'),
        Index('idx_usage_employee', 'employee_id'),
        Index('idx_usage_created_at', 'created_at'),
        Index('idx_usage_inventory_change', 'inventory_change_id'),
    )

    # 关系定义
    item = relationship("Item", primaryjoin="foreign(ItemUsageRecord.item_id) == Item.id", uselist=False, viewonly=True)
    department = relationship("Department", primaryjoin="foreign(ItemUsageRecord.department_id) == Department.id", uselist=False, viewonly=True)
    employee = relationship("User", primaryjoin="foreign(ItemUsageRecord.employee_id) == User.id", uselist=False, viewonly=True)
    inventory_change = relationship("InventoryChangeRecord", primaryjoin="foreign(ItemUsageRecord.inventory_change_id) == InventoryChangeRecord.id", uselist=False, viewonly=True)


class UsageRequest(Base):
    """物品使用申请表"""
    __tablename__ = "usage_requests"

    id = Column(Integer, primary_key=True, index=True)
    request_number = Column(String(50), unique=True, nullable=False, index=True, comment="申请单号")
    
    # 申请信息
    department_id = Column(Integer, nullable=False, comment="申请部门")
    requester_id = Column(Integer, nullable=False, comment="申请人")
    request_date = Column(DateTime(timezone=True), server_default=func.now(), comment="申请日期")
    required_date = Column(DateTime(timezone=True), comment="需要日期")
    
    # 状态
    status = Column(String(20), default="draft", comment="状态: draft/submitted/approved/issued/completed/cancelled")
    
    # 审批信息
    approver_id = Column(Integer, comment="审批人")
    approved_at = Column(DateTime(timezone=True), comment="审批时间")
    approval_notes = Column(Text, comment="审批意见")
    
    # 发放信息
    issuer_id = Column(Integer, comment="发放人")
    issued_at = Column(DateTime(timezone=True), comment="发放时间")
    
    # 用途
    purpose = Column(String(200), comment="用途")
    project_code = Column(String(50), comment="项目代码")
    cost_center = Column(String(50), comment="成本中心")
    
    # 总计
    total_items = Column(Integer, default=0, comment="物品总数")
    total_amount = Column(Numeric(15, 2), default=0, comment="总金额")
    
    # 备注
    remarks = Column(Text, comment="备注")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_usage_req_dept', 'department_id'),
        Index('idx_usage_req_requester', 'requester_id'),
        Index('idx_usage_req_status', 'status'),
        Index('idx_usage_req_date', 'request_date'),
        Index('idx_usage_req_required_date', 'required_date'),
        Index('idx_usage_req_created_at', 'created_at'),
    )

    # 关系
    department = relationship("Department", primaryjoin="foreign(UsageRequest.department_id) == Department.id", uselist=False, viewonly=True)
    requester = relationship("User", primaryjoin="foreign(UsageRequest.requester_id) == User.id", uselist=False, viewonly=True)
    approver = relationship("User", primaryjoin="foreign(UsageRequest.approver_id) == User.id", uselist=False, viewonly=True)
    issuer = relationship("User", primaryjoin="foreign(UsageRequest.issuer_id) == User.id", uselist=False, viewonly=True)
    request_items = relationship("UsageRequestItem", primaryjoin="UsageRequest.id == foreign(UsageRequestItem.request_id)", back_populates="usage_request")


class UsageRequestItem(Base):
    """物品使用申请明细表"""
    __tablename__ = "usage_request_items"

    id = Column(Integer, primary_key=True, index=True)
    request_id = Column(Integer, nullable=False, comment="申请ID")
    item_id = Column(Integer, nullable=False, comment="物品ID")
    
    # 申请数量
    requested_quantity = Column(Integer, nullable=False, comment="申请数量")
    approved_quantity = Column(Integer, comment="批准数量")
    issued_quantity = Column(Integer, default=0, comment="已发放数量")
    
    # 价格信息
    unit_price = Column(Numeric(10, 4), comment="单价")
    total_amount = Column(Numeric(15, 2), comment="总价")
    
    # 状态
    status = Column(String(20), default="pending", comment="状态")
    
    # 备注
    remarks = Column(Text, comment="备注")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_usage_req_item_request', 'request_id'),
        Index('idx_usage_req_item_item', 'item_id'),
        Index('idx_usage_req_item_status', 'status'),
    )

    # 关系
    usage_request = relationship("UsageRequest", primaryjoin="foreign(UsageRequestItem.request_id) == UsageRequest.id", uselist=False, back_populates="request_items")
    item = relationship("Item", primaryjoin="foreign(UsageRequestItem.item_id) == Item.id", uselist=False, viewonly=True) 