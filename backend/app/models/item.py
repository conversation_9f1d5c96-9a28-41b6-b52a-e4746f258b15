from sqlalchemy import Column, Integer, String, Text, Numeric, DateTime, Boolean, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base

class ItemPrimaryCategory(Base):
    """物品一级分类模型"""
    __tablename__ = "item_primary_categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(Text)
    
    # 编码配置
    code_prefix = Column(String(10), nullable=False)  # 编码前缀，如 "LB", "BG", "SC"
    code_format = Column(String(20), default="0000")  # 编码格式，如 "0000"
    current_sequence = Column(Integer, default=1)  # 当前序号
    
    # 状态
    is_active = Column(Boolean, default=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_primary_cat_code_prefix', 'code_prefix'),
    )
    
    # 关系
    categories = relationship("ItemCategory", primaryjoin="ItemPrimaryCategory.id == foreign(ItemCategory.primary_category_id)", viewonly=True)

class ItemCategory(Base):
    """物品二级分类模型"""
    __tablename__ = "item_categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # 所属一级分类
    primary_category_id = Column(Integer, nullable=False)
    
    # 状态
    is_active = Column(Boolean, default=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 组合唯一约束：同一一级分类下，二级分类名称唯一
    __table_args__ = (
        Index('idx_item_categories_primary_name', 'primary_category_id', 'name', unique=True),
        Index('idx_item_categories_primary', 'primary_category_id'),
    )
    
    # 关系
    primary_category = relationship("ItemPrimaryCategory", primaryjoin="foreign(ItemCategory.primary_category_id) == ItemPrimaryCategory.id", uselist=False, viewonly=True)
    items = relationship("Item", primaryjoin="ItemCategory.id == foreign(Item.category_id)", viewonly=True)
    property_configs = relationship("ItemPropertyConfig", primaryjoin="ItemCategory.id == foreign(ItemPropertyConfig.category_id)", viewonly=True)

class ItemPropertyConfig(Base):
    """物品属性配置模型"""
    __tablename__ = "item_property_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    category_id = Column(Integer, nullable=False, index=True)
    attribute_name = Column(String(50), nullable=False)  # brand, spec_material, size_dimension
    input_type = Column(String(20), nullable=False)  # select, text
    options = Column(Text)  # JSON格式存储选项值数组，仅select类型使用
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 复合唯一约束：同一分类下属性名称唯一
    __table_args__ = (
        Index('idx_item_property_configs_unique', 'category_id', 'attribute_name', unique=True),
        Index('idx_item_property_configs_input_type', 'input_type'),
    )
    
    # 关系
    category = relationship("ItemCategory", primaryjoin="foreign(ItemPropertyConfig.category_id) == ItemCategory.id", uselist=False, viewonly=True)

class Item(Base):
    """物品模型"""
    __tablename__ = "items"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(200), nullable=False, index=True)
    code = Column(String(50), unique=True, index=True)  # 物品编码
    description = Column(Text)
    
    # 所属分类
    category_id = Column(Integer, nullable=False)
    
    # 基本信息
    image_url = Column(String(500))  # 物品图片URL
    purchase_unit = Column(String(20), default="piece")  # 采购单位
    inventory_unit = Column(String(20), default="piece")  # 库存单位
    qty_per_up = Column(Integer, default=1)  # 每采购单位包含的库存单位数量，正整数
    is_purchasable = Column(Boolean, default=True)  # 是否可购买
    is_active = Column(Boolean, default=True)
    
    # 新的固定属性字段
    brand = Column(String(200))  # 品牌
    spec_material = Column(String(500))  # 规格/材质
    size_dimension = Column(String(200))  # 尺寸/规格
    
    # 库存信息
    total_stock = Column(Integer, default=0)  # 总库存（各部门库存之和）
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 组合唯一约束：同一分类下，物品名称唯一
    __table_args__ = (
        Index('idx_items_category_name_unique', 'category_id', 'name', unique=True),
        Index('idx_items_category', 'category_id'),
        Index('idx_items_brand', 'brand'),
        Index('idx_items_total_stock', 'total_stock'),
        Index('idx_items_created_at', 'created_at'),
    )
    
    # 关系
    category = relationship("ItemCategory", primaryjoin="foreign(ItemCategory.id) == Item.category_id", uselist=False, viewonly=True)
    item_suppliers = relationship("ItemSupplier", primaryjoin="foreign(ItemSupplier.item_id) == Item.id", viewonly=True)
    change_history = relationship("ItemChangeHistory", primaryjoin="foreign(ItemChangeHistory.item_id) == Item.id", viewonly=True)
    usage_records = relationship("ItemUsageRecord", primaryjoin="foreign(ItemUsageRecord.item_id) == Item.id", viewonly=True)

class ItemChangeHistory(Base):
    """物品变更历史模型"""
    __tablename__ = "item_change_history"
    
    id = Column(Integer, primary_key=True, index=True)
    item_id = Column(Integer, nullable=False)
    user_id = Column(Integer, nullable=False)
    
    # 变更信息
    action = Column(String(50), nullable=False)  # create, update, delete
    field_name = Column(String(100))
    old_value = Column(Text)
    new_value = Column(Text)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_item_change_item', 'item_id'),
        Index('idx_item_change_user', 'user_id'),
        Index('idx_item_change_action', 'action'),
        Index('idx_item_change_created_at', 'created_at'),
    )
    
    # 关系
    item = relationship("Item", primaryjoin="foreign(ItemChangeHistory.item_id) == Item.id", uselist=False, viewonly=True)
    user = relationship("User", primaryjoin="foreign(ItemChangeHistory.user_id) == User.id", uselist=False, viewonly=True) 