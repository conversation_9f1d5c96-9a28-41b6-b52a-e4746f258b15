from sqlalchemy import Column, Integer, String, Boolean, DateTime, JSON, Text, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base

class Department(Base):
    """部门表"""
    __tablename__ = "departments"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="部门名称")
    code = Column(String(50), unique=True, nullable=False, comment="部门编码")

    # 部门信息
    description = Column(Text, comment="部门描述")

    # 状态
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_dept_created_at', 'created_at'),
    )

    # 关系
    users = relationship(
        "User", 
        primaryjoin="foreign(User.department_id) == Department.id",
        back_populates="department")

class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100))
    display_name = Column(String(100), comment="显示名/备注名")
    
    # 扩展个人信息
    phone = Column(String(20), comment="联系电话")
    employee_id = Column(String(50), unique=True, comment="工号")
    position = Column(String(100), comment="职位")
    avatar = Column(String(255), comment="头像URL")
    
    # 状态管理
    is_active = Column(Boolean, default=True, comment="账号是否激活")
    is_superuser = Column(Boolean, default=False, comment="是否超级用户")
    account_status = Column(String(20), default="active", comment="账号状态: active, disabled, locked, pending")
    password_status = Column(String(20), default="normal", comment="密码状态: normal, need_reset, temporary")
    
    # 登录相关
    last_login_at = Column(DateTime(timezone=True), comment="最后登录时间")
    password_changed_at = Column(DateTime(timezone=True), comment="密码修改时间")
    login_attempts = Column(Integer, default=0, comment="连续登录失败次数")
    locked_until = Column(DateTime(timezone=True), comment="锁定到期时间")
    
    # 组织关系
    department_id = Column(Integer, comment="主部门")
    role_id = Column(Integer, comment="用户角色ID")
    
    # 通知设置
    email_notifications_enabled = Column(Boolean, default=True, comment="是否接收邮件通知")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_user_department', 'department_id'),
        Index('idx_user_role', 'role_id'),
        Index('idx_user_account_status', 'account_status'),
        Index('idx_user_last_login', 'last_login_at'),
        Index('idx_user_created_at', 'created_at'),
    )
    
    # 关系
    department = relationship(
        "Department", 
        primaryjoin="foreign(User.department_id) == Department.id",
        uselist=False, 
        back_populates="users", 
        viewonly=True)
    role = relationship("Role", primaryjoin="User.role_id == foreign(Role.id)", viewonly=True)
 