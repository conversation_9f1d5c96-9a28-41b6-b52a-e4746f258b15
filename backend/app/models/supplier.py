from sqlalchemy import Column, Integer, String, Text, DateTime, Numeric, Index
from sqlalchemy.sql import func
from sqlalchemy.schema import UniqueConstraint
from sqlalchemy.orm import relationship
from ..core.database import Base
import enum


class SupplierStatus(str, enum.Enum):
    """供应商状态枚举"""
    active = "active"        # 活跃
    inactive = "inactive"    # 停用


class PriceType(str, enum.Enum):
    """价格类型枚举"""
    fixed = "fixed"          # 固定价格
    tiered = "tiered"        # 阶梯价格
    negotiated = "negotiated" # 议价


class Supplier(Base):
    """供应商表"""
    __tablename__ = "suppliers"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(50), unique=True, nullable=False, index=True, comment="供应商编码")
    name_cn = Column(String(200), nullable=True, index=True, comment="供应商中文名")
    name_en = Column(String(200), nullable=True, index=True, comment="供应商英文名")
    company_address = Column(Text, comment="公司地址")
    contact_person = Column(String(100), comment="联系人")
    phone = Column(String(50), comment="联系电话")
    email = Column(String(200), comment="邮箱地址")
    rating = Column(Integer, default=0, comment="供应商评级 (1-5)")
    status = Column(String(20), default="active", comment="状态 (active/inactive)")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(Integer, comment="创建人")
    updated_by = Column(Integer, comment="更新人")

    # 关系
    item_suppliers = relationship("ItemSupplier", back_populates="supplier", primaryjoin="foreign(ItemSupplier.supplier_id) == Supplier.id")


class ItemSupplier(Base):
    """物品-供应商关联表"""
    __tablename__ = "item_suppliers"
    __table_args__ = (
        UniqueConstraint('item_id', 'supplier_id', name='uix_item_supplier'),
    )

    id = Column(Integer, primary_key=True, index=True)
    item_id = Column(Integer, nullable=False, comment="物品ID")
    supplier_id = Column(Integer, nullable=False, comment="供应商ID")
    
    # 优先级
    priority = Column(Integer, default=1, comment="优先级 (0=preferred, 1+=alternative)")
    status = Column(String(20), default="active", comment="状态 (active/inactive)")
    
    # 供应信息
    delivery_days = Column(Integer, default=7, comment="交货天数")
    quality_rating = Column(Integer, default=3, comment="质量评级 (1-5)")
    spq = Column(Integer, default=1, comment="标准包装数量")
    moq = Column(Integer, default=1, comment="最小订购数量")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系
    item = relationship("Item", primaryjoin="foreign(ItemSupplier.item_id) == Item.id", viewonly=True)
    supplier = relationship("Supplier", back_populates="item_suppliers", primaryjoin="foreign(ItemSupplier.supplier_id) == Supplier.id")
    price_records = relationship("SupplierPrice", back_populates="item_supplier", primaryjoin="foreign(SupplierPrice.item_supplier_id) == ItemSupplier.id")


class SupplierPrice(Base):
    """供应商价格表"""
    __tablename__ = "supplier_prices"

    id = Column(Integer, primary_key=True, index=True)
    item_supplier_id = Column(Integer, nullable=False, comment="物品供应商关系ID")
    
    # 价格信息
    unit_price = Column(Numeric(10, 4), nullable=False, comment="单价")
    currency_code = Column(String(10), nullable=False, default="USD", comment="货币代码 (USD, CNY, EUR等)")
    min_quantity = Column(Integer, default=1, comment="最小数量")
    max_quantity = Column(Integer, comment="最大数量 (NULL表示无上限)")
    
    # 有效期
    valid_from = Column(DateTime(timezone=True), nullable=False, comment="生效日期")
    valid_to = Column(DateTime(timezone=True), comment="失效日期 (NULL表示永久有效)")
    
    # 状态和备注
    status = Column(String(20), default="active", comment="状态 (active/inactive)")
    remarks = Column(Text, comment="价格备注说明")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(Integer, comment="创建人")
    updated_by = Column(Integer, comment="更新人")

    # 关系
    item_supplier = relationship("ItemSupplier", back_populates="price_records", primaryjoin="foreign(SupplierPrice.item_supplier_id) == ItemSupplier.id")

    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_supplier_price_item_supplier', 'item_supplier_id'),
        Index('idx_supplier_price_currency_code', 'currency_code'),
        Index('idx_supplier_price_validity', 'valid_from', 'valid_to'),
        Index('idx_supplier_price_status', 'status'),
        Index('idx_supplier_price_unit_price', 'unit_price'),
        Index('idx_supplier_price_created_at', 'created_at'),
        Index('idx_supplier_price_created_by', 'created_by'),
    )


