from sqlalchemy import Column, Integer, String, Boolean, DateTime, JSON, Text, Index, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class Notification(Base):
    """通知表"""
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, comment="接收用户ID")
    title = Column(String(200), nullable=False, comment="通知标题")
    content = Column(Text, nullable=False, comment="通知内容")
    notification_type = Column(String(50), nullable=False, comment="通知类型")
    status = Column(String(20), default="unread", comment="通知状态: unread, read")
    business_data = Column(JSON, comment="业务相关数据")
    action_url = Column(String(500), comment="相关业务操作链接")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    read_at = Column(DateTime(timezone=True), comment="阅读时间")

    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_notifications_user_id', 'user_id'),
        Index('idx_notifications_type', 'notification_type'),
        Index('idx_notifications_status', 'status'),
        Index('idx_notifications_created_at', 'created_at'),
    )

    # 关系
    user = relationship("User", primaryjoin="foreign(Notification.user_id) == User.id", viewonly=True)
    emails = relationship("NotificationEmail", back_populates="notification", cascade="all, delete-orphan")


class NotificationEmail(Base):
    """邮件发送记录表"""
    __tablename__ = "notification_emails"

    id = Column(Integer, primary_key=True, index=True)
    notification_id = Column(Integer, ForeignKey("notifications.id"), nullable=False, comment="关联的通知ID")
    user_id = Column(Integer, nullable=False, comment="接收用户ID")
    email_status = Column(String(20), default="pending", comment="邮件发送状态: pending, sent, failed")
    failure_reason = Column(Text, comment="发送失败原因")
    sent_at = Column(DateTime(timezone=True), comment="发送成功时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")

    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_notification_emails_notification_id', 'notification_id'),
        Index('idx_notification_emails_user_id', 'user_id'),
        Index('idx_notification_emails_status', 'email_status'),
        Index('idx_notification_emails_created_at', 'created_at'),
    )

    # 关系
    notification = relationship("Notification", back_populates="emails")
    user = relationship("User", primaryjoin="foreign(NotificationEmail.user_id) == User.id", viewonly=True)


class SystemConfig(Base):
    """系统配置表"""
    __tablename__ = "system_configs"

    id = Column(Integer, primary_key=True, index=True)
    config_key = Column(String(100), unique=True, nullable=False, comment="配置键")
    config_value = Column(Text, comment="配置值")
    description = Column(String(200), comment="配置描述")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_system_configs_key', 'config_key'),
    )
