from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Numeric, JSON, Enum, and_, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship, foreign
from ..core.database import Base
import enum
from sqlalchemy import UniqueConstraint
from decimal import Decimal
from typing import Optional





class InventoryStatus(str, enum.Enum):
    """库存状态枚举"""
    NORMAL = "normal"     # 正常
    LOW = "low"          # 低库存
    OUT = "out"          # 缺货
    RESERVED = "reserved" # 预留


class InventoryChangeType(str, enum.Enum):
    """库存变更类型枚举"""
    MANUAL_IN = "manual_in"      # 手工入库
    PICKUP_OUT = "pickup_out"    # 工人领取
    ADJUST = "adjust"            # 库存调整
    RETURN = "return"            # 退货


class DepartmentInventory(Base):
    """部门库存表"""
    __tablename__ = "department_inventories"

    id = Column(Integer, primary_key=True, index=True)
    department_id = Column(Integer, nullable=False, comment="部门ID")
    item_id = Column(Integer, nullable=False, comment="物品ID")
    
    # 库存数量 - 支持小数点后4位精度
    current_quantity = Column(Numeric(15, 4), default=0, comment="当前库存数量")
    
    # 库存阈值
    min_quantity = Column(Numeric(15, 4), default=0, comment="最小库存量")
    max_quantity = Column(Numeric(15, 4), comment="最大库存量")
    
    # 库存位置
    storage_location = Column(String(100), comment="存储位置")
    rack_number = Column(String(50), comment="货架号")
    
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 备注
    notes = Column(Text, comment="备注")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    last_updated = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="最后更新时间")
    last_count_date = Column(DateTime(timezone=True), comment="最后盘点时间")

    # 唯一约束和索引：确保每个部门-物品组合只有一条库存记录
    __table_args__ = (
        UniqueConstraint('department_id', 'item_id', name='uq_department_item'),
        Index('idx_dept_inv_last_updated', 'last_updated'),
        Index('idx_dept_inv_current_qty', 'current_quantity'),
        Index('idx_dept_inv_min_qty', 'min_quantity'),
    )

    # 关系
    department = relationship("Department", primaryjoin="foreign(DepartmentInventory.department_id) == Department.id", uselist=False, viewonly=True)
    item = relationship("Item", primaryjoin="foreign(DepartmentInventory.item_id) == Item.id", uselist=False, viewonly=True)

    change_records = relationship("InventoryChangeRecord", 
                                primaryjoin="and_(DepartmentInventory.department_id == foreign(InventoryChangeRecord.department_id), DepartmentInventory.item_id == foreign(InventoryChangeRecord.item_id))", 
                                viewonly=True)

    @property
    def status(self) -> str:
        """动态计算库存状态"""
        if self.current_quantity is None:
            return "out_of_stock"
        
        # 缺货状态：库存为0
        if self.current_quantity == 0:
            return "out_of_stock"
        
        # 低库存状态：库存大于0但小于等于最小库存
        if self.min_quantity is not None and self.current_quantity > 0 and self.current_quantity <= self.min_quantity:
            return "low"
        
        # 超储状态：库存超过最大库存
        if self.max_quantity is not None and self.current_quantity > self.max_quantity:
            return "overstock"
        
        return "normal"

    @property
    def last_purchase_price(self) -> Optional[Decimal]:
        """动态计算最后采购价格 - 从最近的采购记录获取"""
        # 这里需要从采购记录中获取，暂时返回None
        # 实际实现时需要查询采购订单或供应商价格表
        return None

    @property
    def average_cost(self) -> Optional[Decimal]:
        """动态计算平均成本 - 基于历史采购记录计算"""
        # 这里需要基于历史采购记录计算，暂时返回None
        # 实际实现时需要查询采购记录并计算加权平均
        return None

    @property
    def total_value(self) -> Optional[Decimal]:
        """动态计算库存总价值 - 基于当前库存和最新价格计算"""
        # 这里需要基于当前库存和最新价格计算，暂时返回None
        # 实际实现时需要查询供应商价格表并计算
        return None





class InventoryChangeRecord(Base):
    """库存变更记录表 - 统一记录所有库存变更操作"""
    __tablename__ = "inventory_change_records"

    id = Column(Integer, primary_key=True, index=True)
    department_id = Column(Integer, nullable=False, comment="部门ID")
    item_id = Column(Integer, nullable=False, comment="物品ID")
    
    # 变更信息
    before_quantity = Column(Numeric(15, 4), nullable=False, comment="变更前库存数量")
    after_quantity = Column(Numeric(15, 4), nullable=False, comment="变更后库存数量")
    change_quantity = Column(Numeric(15, 4), nullable=False, comment="变更数量")
    change_type = Column(String(20), nullable=False, comment="变更类型: manual_in/pickup_out/adjust/return")
    change_reason = Column(String(100), comment="变更原因")
    
    # 操作信息
    operator_id = Column(Integer, nullable=False, comment="操作员ID")
    change_date = Column(DateTime(timezone=True), server_default=func.now(), comment="变更日期")
    remarks = Column(Text, comment="备注信息")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")

    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_inv_change_dept_item', 'department_id', 'item_id'),
        Index('idx_inv_change_date', 'change_date'),
        Index('idx_inv_change_type', 'change_type'),
        Index('idx_inv_change_operator', 'operator_id'),
        Index('idx_inv_change_created_at', 'created_at'),
    )

    # 关系
    department = relationship("Department", primaryjoin="foreign(InventoryChangeRecord.department_id) == Department.id", uselist=False, viewonly=True)
    item = relationship("Item", primaryjoin="foreign(InventoryChangeRecord.item_id) == Item.id", uselist=False, viewonly=True)
    operator = relationship("User", primaryjoin="foreign(InventoryChangeRecord.operator_id) == User.id", uselist=False, viewonly=True)


class InventoryAlert(Base):
    """库存预警表"""
    __tablename__ = "inventory_alerts"

    id = Column(Integer, primary_key=True, index=True)
    department_id = Column(Integer, nullable=False, comment="部门ID")
    item_id = Column(Integer, nullable=False, comment="物品ID")
    
    # 预警信息
    alert_type = Column(String(20), nullable=False, comment="预警类型: low_stock/out_of_stock/overstock/expiry")
    alert_level = Column(String(20), default="warning", comment="预警级别: info/warning/critical")
    message = Column(Text, comment="预警消息")
    
    # 库存信息
    current_stock = Column(Numeric(15, 4), comment="当前库存")
    threshold_value = Column(Numeric(15, 4), comment="阈值")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_resolved = Column(Boolean, default=False, comment="是否已解决")
    resolved_by = Column(Integer, comment="解决人")
    resolved_at = Column(DateTime(timezone=True), comment="解决时间")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_inv_alert_dept_item', 'department_id', 'item_id'),
        Index('idx_inv_alert_type', 'alert_type'),
        Index('idx_inv_alert_level', 'alert_level'),
        Index('idx_inv_alert_created_at', 'created_at'),
    )

    # 关系
    department = relationship("Department", primaryjoin="foreign(InventoryAlert.department_id) == Department.id", uselist=False, viewonly=True)
    item = relationship("Item", primaryjoin="foreign(InventoryAlert.item_id) == Item.id", uselist=False, viewonly=True)
    resolver = relationship("User", primaryjoin="foreign(InventoryAlert.resolved_by) == User.id", uselist=False, viewonly=True) 