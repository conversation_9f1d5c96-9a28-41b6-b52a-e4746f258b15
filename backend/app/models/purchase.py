from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, Numeric, func, Date, Index
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.sqlite import JSON
from app.core.database import Base
# 移除汇率导入，改为在申请明细表中直接存储锁定的汇率
# from app.models.exchange_rate import PurchaseRequestExchangeRate
import uuid
from datetime import datetime


# PurchaseCart 表已移除，直接用 purchase_cart_items 代表各部门的购物车


class PurchaseCartItem(Base):
    """购物车明细表 - 直接代表各部门的购物车"""
    __tablename__ = "purchase_cart_items"

    id = Column(Integer, primary_key=True, index=True)
    department_id = Column(Integer, nullable=False, comment="部门ID")
    item_id = Column(Integer, nullable=False, comment="物品ID")
    spq_quantity = Column(Numeric(10, 3), nullable=False, comment="SPQ数量（标准包装数量）")
    spq_count = Column(Integer, nullable=False, comment="SPQ个数（需要多少个标准包装）")
    spq_unit = Column(String(20), nullable=False, comment="SPQ单位")
    notes = Column(Text, comment="备注")
    created_by = Column(Integer, nullable=False, comment="添加人ID")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 索引
    __table_args__ = (
        Index('idx_cart_items_department', 'department_id'),
        Index('idx_cart_items_item_id', 'item_id'),
        Index('idx_cart_items_unique', 'department_id', 'item_id', unique=True),
        Index('idx_cart_items_created_by', 'created_by'),
    )


class PurchaseRequest(Base):
    """采购申请表"""
    __tablename__ = "purchase_requests"

    id = Column(Integer, primary_key=True, index=True)
    request_no = Column(String(50), unique=True, nullable=False, comment="申请单号")
    request_uuid = Column(String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()), comment="申请唯一标识")
    department_id = Column(Integer, nullable=False, comment="申请部门ID")
    submitter_id = Column(Integer, nullable=False, comment="提交人ID")
    status = Column(String(30), default="pending_submission", comment="申请状态")
    final_total = Column(Numeric(15, 2), comment="最终总金额")
    notes = Column(Text, comment="备注")
    qr_code = Column(String(500), comment="申请二维码")
    submitted_at = Column(DateTime(timezone=True), comment="提交时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系
    # cart 关系已移除，直接从购物车项目创建申请
    items = relationship("PurchaseRequestItem", primaryjoin="PurchaseRequest.id == foreign(PurchaseRequestItem.request_id)", back_populates="request", cascade="all, delete-orphan")
    flow_history = relationship("RequestFlowHistory", primaryjoin="PurchaseRequest.id == foreign(RequestFlowHistory.request_id)", back_populates="request", cascade="all, delete-orphan")
    # 移除价格快照关系，改为在申请明细表中直接存储最终价格
    # price_snapshots = relationship("PriceSnapshot", primaryjoin="PurchaseRequest.id == foreign(PriceSnapshot.request_id)", back_populates="request", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_purchase_requests_no', 'request_no'),
        Index('idx_purchase_requests_dept', 'department_id'),
        Index('idx_purchase_requests_submitter', 'submitter_id'),
        Index('idx_purchase_requests_status', 'status'),
        Index('idx_purchase_requests_date', 'submitted_at'),
    )


class PurchaseRequestItem(Base):
    """采购申请明细表"""
    __tablename__ = "purchase_request_items"

    id = Column(Integer, primary_key=True, index=True)
    request_id = Column(Integer, nullable=False, comment="申请ID")
    item_id = Column(Integer, nullable=False, comment="物品ID")
    item_code = Column(String(20), nullable=False, comment="物品编码")
    item_name = Column(String(200), nullable=False, comment="物品名称")
    spq_quantity = Column(Numeric(10, 3), nullable=False, comment="SPQ数量（标准包装数量）")
    spq_count = Column(Integer, nullable=False, comment="SPQ个数（需要多少个标准包装）")
    spq_unit = Column(String(20), nullable=False, comment="SPQ单位")
    
    # 锁定的汇率信息（按阶段锁定）
    locked_currency_code = Column(String(10), comment="锁定的货币代码")
    locked_exchange_rate = Column(Numeric(10, 6), comment="锁定的汇率值")
    locked_exchange_rate_month = Column(Date, comment="锁定的汇率生效月份")
    locked_exchange_rate_stage = Column(String(20), comment="汇率锁定阶段: submitted, principle_approved, final_approved")
    locked_exchange_rate_at = Column(DateTime(timezone=True), comment="汇率锁定时间")
    
    # 最终价格（审批后锁定）
    final_unit_price = Column(Numeric(12, 4), comment="最终单价")
    final_total_price = Column(Numeric(15, 2), comment="最终总价")
    final_supplier_id = Column(Integer, comment="最终供应商ID")
    price_locked_at = Column(DateTime(timezone=True), comment="价格锁定时间")
    
    notes = Column(Text, comment="备注")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系
    request = relationship("PurchaseRequest", primaryjoin="foreign(PurchaseRequestItem.request_id) == PurchaseRequest.id", back_populates="items")
    order_items = relationship("PurchaseOrderItem", primaryjoin="foreign(PurchaseOrderItem.request_item_id) == PurchaseRequestItem.id", back_populates="request_item")

    # 索引
    __table_args__ = (
        Index('idx_request_items_request_id', 'request_id'),
        Index('idx_request_items_item_id', 'item_id'),
        Index('idx_request_items_code', 'item_code'),
        Index('idx_request_items_final_supplier', 'final_supplier_id'),
        Index('idx_request_items_price_locked', 'price_locked_at'),
        Index('idx_request_items_created_at', 'created_at'),
        Index('idx_request_items_exchange_rate_stage', 'locked_exchange_rate_stage'),
        Index('idx_request_items_exchange_rate_month', 'locked_exchange_rate_month'),
    )


class RequestFlowHistory(Base):
    """申请流转历史表"""
    __tablename__ = "request_flow_history"

    id = Column(Integer, primary_key=True, index=True)
    request_id = Column(Integer, nullable=False, comment="申请ID")
    action = Column(String(50), nullable=False, comment="操作类型: submit, resubmit, approve, reject, return, withdraw, edit")
    from_status = Column(String(30), comment="操作前状态")
    to_status = Column(String(30), nullable=False, comment="操作后状态")
    operator_id = Column(Integer, nullable=False, comment="操作人ID")
    operator_name = Column(String(100), nullable=False, comment="操作人姓名")
    approval_level = Column(String(30), comment="审批级别: review, principle_approval, final_approval (仅在审批操作时有效)")
    comments = Column(Text, comment="操作说明或审批意见")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="操作时间")

    # 关系
    request = relationship("PurchaseRequest", primaryjoin="foreign(RequestFlowHistory.request_id) == PurchaseRequest.id", back_populates="flow_history")

    # 索引
    __table_args__ = (
        Index('idx_flow_history_request', 'request_id'),
        Index('idx_flow_history_operator', 'operator_id'),
        Index('idx_flow_history_action', 'action'),
        Index('idx_flow_history_created_at', 'created_at'),
        Index('idx_flow_history_from_to_status', 'from_status', 'to_status'),
        Index('idx_flow_history_approval_level', 'approval_level'),
    )





class PurchaseOrder(Base):
    """采购订单表"""
    __tablename__ = "purchase_orders"

    id = Column(Integer, primary_key=True, index=True)
    order_no = Column(String(50), unique=True, nullable=False, comment="采购订单号")
    order_uuid = Column(String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()), comment="订单唯一标识")
    supplier_id = Column(Integer, nullable=False, comment="供应商ID")
    supplier_name = Column(String(200), nullable=False, comment="供应商名称")
    total_amount = Column(Numeric(15, 2), nullable=False, comment="订单总金额")
    currency = Column(String(10), default="CNY", comment="货币类型")
    status = Column(String(20), default="pending", comment="订单状态: pending, confirmed, delivered, closed")
    expected_delivery = Column(Date, comment="期望交期")
    confirmed_delivery = Column(Date, comment="确认交期")
    notes = Column(Text, comment="订单说明")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系
    items = relationship("PurchaseOrderItem", primaryjoin="PurchaseOrder.id == foreign(PurchaseOrderItem.order_id)", back_populates="order", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_purchase_orders_no', 'order_no'),
        Index('idx_purchase_orders_supplier', 'supplier_id'),
        Index('idx_purchase_orders_status', 'status'),
        Index('idx_purchase_orders_date', 'expected_delivery'),
    )


class PurchaseOrderItem(Base):
    """采购订单明细表"""
    __tablename__ = "purchase_order_items"

    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, nullable=False, comment="订单ID")
    request_item_id = Column(Integer, comment="关联申请明细ID")
    item_id = Column(Integer, nullable=False, comment="物品ID")
    item_code = Column(String(20), nullable=False, comment="物品编码")
    item_name = Column(String(200), nullable=False, comment="物品名称")
    spq_quantity = Column(Numeric(10, 3), nullable=False, comment="SPQ数量（标准包装数量）")
    spq_count = Column(Integer, nullable=False, comment="SPQ个数（需要多少个标准包装）")
    spq_unit = Column(String(20), nullable=False, comment="SPQ单位")
    unit_price = Column(Numeric(12, 4), nullable=False, comment="单价")
    total_price = Column(Numeric(15, 2), nullable=False, comment="总价")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系
    order = relationship("PurchaseOrder", primaryjoin="foreign(PurchaseOrderItem.order_id) == PurchaseOrder.id", back_populates="items")
    request_item = relationship("PurchaseRequestItem", primaryjoin="foreign(PurchaseOrderItem.request_item_id) == PurchaseRequestItem.id", back_populates="order_items")

    # 索引
    __table_args__ = (
        Index('idx_purchase_order_items_order', 'order_id'),
        Index('idx_purchase_order_items_request_item', 'request_item_id'),
        Index('idx_purchase_order_items_item', 'item_id'),
        Index('idx_purchase_order_items_code', 'item_code'),
        Index('idx_purchase_order_items_created_at', 'created_at'),
    )


class PurchaseExecutionBatch(Base):
    """采购执行批次表"""
    __tablename__ = "purchase_execution_batches"

    id = Column(Integer, primary_key=True, index=True)
    batch_no = Column(String(50), unique=True, nullable=False, comment="执行批次编号")
    batch_name = Column(String(100), nullable=False, comment="批次名称")
    executor_id = Column(Integer, nullable=False, comment="执行人ID")
    executor_name = Column(String(100), nullable=False, comment="执行人姓名")
    executed_at = Column(DateTime(timezone=True), nullable=False, comment="执行时间")
    request_count = Column(Integer, nullable=False, comment="申请单数量")
    total_amount = Column(Numeric(15, 2), nullable=False, comment="总金额")
    status = Column(String(20), default="active", comment="批次状态")
    notes = Column(Text, comment="备注说明")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系
    execution_items = relationship("PurchaseExecutionItem", primaryjoin="PurchaseExecutionBatch.id == foreign(PurchaseExecutionItem.batch_id)", back_populates="batch", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_execution_batches_batch_no', 'batch_no'),
        Index('idx_execution_batches_executor', 'executor_id'),
        Index('idx_execution_batches_executed_at', 'executed_at'),
        Index('idx_execution_batches_status', 'status'),
    )


class PurchaseExecutionItem(Base):
    """采购执行明细表"""
    __tablename__ = "purchase_execution_items"

    id = Column(Integer, primary_key=True, index=True)
    batch_id = Column(Integer, nullable=False, comment="执行批次ID")
    request_id = Column(Integer, nullable=False, comment="采购申请ID")
    request_item_id = Column(Integer, nullable=False, comment="申请明细ID")
    item_id = Column(Integer, nullable=False, comment="物品ID")
    item_code = Column(String(20), nullable=False, comment="物品编码")
    item_name = Column(String(200), nullable=False, comment="物品名称")
    spq_quantity = Column(Numeric(10, 3), nullable=False, comment="SPQ数量")
    spq_count = Column(Integer, nullable=False, comment="SPQ个数")
    spq_unit = Column(String(20), nullable=False, comment="SPQ单位")
    unit_price = Column(Numeric(12, 4), nullable=False, comment="最终锁定单价（来自审批后的价格）")
    total_price = Column(Numeric(15, 2), nullable=False, comment="最终锁定总价（来自审批后的价格）")
    supplier_id = Column(Integer, nullable=False, comment="最终确定的供应商ID（来自审批后的选择）")
    supplier_name = Column(String(200), nullable=False, comment="最终确定的供应商名称（来自审批后的选择）")
    price_locked_at = Column(DateTime(timezone=True), comment="价格锁定时间（记录最终审批通过时间）")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")

    # 关系
    batch = relationship("PurchaseExecutionBatch", primaryjoin="foreign(PurchaseExecutionItem.batch_id) == PurchaseExecutionBatch.id", back_populates="execution_items")

    # 索引
    __table_args__ = (
        Index('idx_execution_items_batch', 'batch_id'),
        Index('idx_execution_items_request', 'request_id'),
        Index('idx_execution_items_item', 'item_id'),
        Index('idx_execution_items_supplier', 'supplier_id'),
        Index('idx_execution_items_price_locked', 'price_locked_at'),
        Index('idx_execution_items_created_at', 'created_at'),
    ) 