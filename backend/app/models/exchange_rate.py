from sqlalchemy import Column, Integer, String, DateTime, Text, Numeric, func, Date, Index, ForeignKey
from sqlalchemy.orm import relationship
from app.core.database import Base
from datetime import datetime


class ExchangeRate(Base):
    """汇率表"""
    __tablename__ = "exchange_rates"

    id = Column(Integer, primary_key=True, index=True)
    currency_code = Column(String(10), nullable=False, comment="货币代码 (CNY, EUR, JPY等)")
    rate = Column(Numeric(10, 6), nullable=False, comment="汇率值 (1 USD = rate 外币)")
    effective_month = Column(Date, nullable=False, comment="生效月份 (格式: YYYY-MM-01)")
    status = Column(String(20), default="active", comment="状态 (active/inactive)")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(Integer, comment="创建人ID")
    updated_by = Column(Integer, comment="更新人ID")

    # 关系
    logs = relationship("ExchangeRateLog", primaryjoin="ExchangeRate.id == foreign(ExchangeRateLog.exchange_rate_id)", back_populates="exchange_rate", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_exchange_rates_currency_code', 'currency_code'),
        Index('idx_exchange_rates_effective_month', 'effective_month'),
        Index('idx_exchange_rates_status', 'status'),
        Index('idx_exchange_rates_created_at', 'created_at'),
        # 复合唯一约束：同一货币同一月份只能有一条有效汇率
        Index('idx_exchange_rates_currency_month_unique', 'currency_code', 'effective_month', unique=True),
    )


class ExchangeRateLog(Base):
    """汇率修改日志表"""
    __tablename__ = "exchange_rate_logs"

    id = Column(Integer, primary_key=True, index=True)
    exchange_rate_id = Column(Integer, nullable=False, comment="汇率记录ID")
    old_rate = Column(Numeric(10, 6), nullable=False, comment="修改前的汇率值")
    new_rate = Column(Numeric(10, 6), nullable=False, comment="修改后的汇率值")
    change_reason = Column(Text, nullable=False, comment="修改原因")
    changed_by = Column(Integer, nullable=False, comment="修改人ID")
    changed_at = Column(DateTime(timezone=True), server_default=func.now(), comment="修改时间")

    # 关系
    exchange_rate = relationship("ExchangeRate", primaryjoin="foreign(ExchangeRateLog.exchange_rate_id) == ExchangeRate.id", back_populates="logs")

    # 索引
    __table_args__ = (
        Index('idx_exchange_rate_logs_exchange_rate_id', 'exchange_rate_id'),
        Index('idx_exchange_rate_logs_changed_at', 'changed_at'),
        Index('idx_exchange_rate_logs_changed_by', 'changed_by'),
    )


class PurchaseRequestExchangeRate(Base):
    """采购申请汇率记录表"""
    __tablename__ = "purchase_request_exchange_rates"

    id = Column(Integer, primary_key=True, index=True)
    purchase_request_id = Column(Integer, nullable=False, comment="采购申请ID")
    stage = Column(String(20), nullable=False, comment="阶段 (submitted, principle_approved)")
    currency_code = Column(String(10), nullable=False, comment="货币代码")
    rate = Column(Numeric(10, 6), nullable=False, comment="汇率值")
    recorded_at = Column(DateTime(timezone=True), server_default=func.now(), comment="记录时间")
    recorded_by = Column(Integer, nullable=False, comment="记录人ID")

    # 索引
    __table_args__ = (
        Index('idx_purchase_request_exchange_rates_request_id', 'purchase_request_id'),
        Index('idx_purchase_request_exchange_rates_stage', 'stage'),
        Index('idx_purchase_request_exchange_rates_currency_code', 'currency_code'),
        Index('idx_purchase_request_exchange_rates_recorded_at', 'recorded_at'),
    )
