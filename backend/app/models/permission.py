from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Table, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship, foreign
from app.core.database import Base

# 角色权限关联表
role_permissions = Table(
    'role_permissions',
    Base.metadata,
    Column('role_id', Integer, primary_key=True),
    Column('permission_id', Integer, primary_key=True),
    Column('created_at', DateTime(timezone=True), server_default=func.now()),
    Column('created_by', Integer)
)



class Permission(Base):
    """权限模型"""
    __tablename__ = "permissions"
    
    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(100), unique=True, nullable=False, comment="权限代码")
    name = Column(String(100), nullable=False, comment="权限名称")
    description = Column(Text, comment="权限描述")
    module = Column(String(50), nullable=False, comment="所属模块")
    

    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_system = Column(Boolean, default=False, comment="是否系统权限（不可删除）")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(Integer)
    
    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_permission_module', 'module'),
        Index('idx_permission_created_at', 'created_at'),
    )
    
    # 关系
    roles = relationship(
        "Role", 
        secondary=role_permissions, 
        primaryjoin="Permission.id == foreign(role_permissions.c.permission_id)", 
        secondaryjoin="Role.id == foreign(role_permissions.c.role_id)", 
        viewonly=True)

class Role(Base):
    """角色模型"""
    __tablename__ = "roles"
    
    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(50), unique=True, nullable=False, comment="角色代码")
    name = Column(String(100), nullable=False, comment="角色名称")
    description = Column(Text, comment="角色描述")
    
    # 角色属性
    is_system = Column(Boolean, default=False, comment="是否系统角色（不可删除）")
    is_active = Column(Boolean, default=True, comment="是否启用")

    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(Integer)
    
    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_role_created_at', 'created_at'),
    )
    
    # 关系
    permissions = relationship(
        "Permission", 
        secondary=role_permissions, 
        primaryjoin="Role.id == foreign(role_permissions.c.role_id)", 
        secondaryjoin="Permission.id == foreign(role_permissions.c.permission_id)", 
        viewonly=True)


class AuditLog(Base):
    """权限审计日志"""
    __tablename__ = "audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer)
    action = Column(String(50), nullable=False, comment="操作类型")
    resource_type = Column(String(50), comment="资源类型")
    resource_id = Column(Integer, comment="资源ID")
    old_value = Column(Text, comment="变更前的值")
    new_value = Column(Text, comment="变更后的值")
    ip_address = Column(String(45), comment="IP地址")
    user_agent = Column(Text, comment="用户代理")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 索引：优化查询性能
    __table_args__ = (
        Index('idx_audit_log_user', 'user_id'),
        Index('idx_audit_log_action', 'action'),
        Index('idx_audit_log_resource', 'resource_type', 'resource_id'),
        Index('idx_audit_log_created_at', 'created_at'),
    )
    
    # 关系
    user = relationship("User", primaryjoin="foreign(AuditLog.user_id) == User.id", uselist=False, viewonly=True) 