from pydantic import BaseModel, ConfigDict, Field, field_validator, model_validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal
from ..models.supplier import SupplierStatus, PriceType


# 供应商相关 Schemas
class SupplierBase(BaseModel):
    """供应商基础信息"""
    name_cn: Optional[str] = Field(None, description="供应商中文名")
    name_en: Optional[str] = Field(None, description="供应商英文名")
    code: str = Field(None, description="供应商编码")
    company_address: Optional[str] = Field(None, description="公司地址")
    contact_person: Optional[str] = Field(None, description="联系人")
    phone: Optional[str] = Field(None, description="联系电话")
    email: Optional[str] = Field(None, description="邮箱地址")
    rating: int = Field(0, ge=0, le=5, description="供应商评级 (1-5)")
    status: str = Field("active", description="状态 (active/inactive)")
    
    @field_validator('name_cn', 'name_en', mode='before')
    @classmethod
    def validate_at_least_one_name(cls, v, info):
        """验证至少提供一个名称"""
        if info.field_name == 'name_cn':
            return v
        if info.field_name == 'name_en':
            return v
        return v


class SupplierCreate(SupplierBase):
    """创建供应商"""
    pass


class SupplierUpdate(BaseModel):
    """更新供应商"""
    name_cn: Optional[str] = None
    name_en: Optional[str] = None
    company_address: Optional[str] = None
    contact_person: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    rating: Optional[int] = Field(None, ge=0, le=5)
    status: Optional[str] = None


class Supplier(SupplierBase):
    """供应商响应"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None
    updated_by: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)


# 物品供应商关联 Schemas
class ItemSupplierBase(BaseModel):
    """物品供应商关联基础信息"""
    item_id: int = Field(..., description="物品ID")
    supplier_id: int = Field(..., description="供应商ID")
    priority: Optional[int] = Field(None, ge=0, description="优先级 (0=Preferred, 1=Alternate1, 2=Alternate2, 3=Alternate3, null=无优先级)")
    status: str = Field("active", description="状态 (active/inactive)")
    delivery_days: int = Field(7, description="交货天数")
    quality_rating: int = Field(3, ge=1, le=5, description="质量评级 (1-5)")
    spq: int = Field(1, gt=0, description="标准包装数量")
    moq: int = Field(1, gt=0, description="最小订购数量")
    
    @field_validator('moq')
    @classmethod
    def validate_moq_is_spq_multiple(cls, v, info):
        """验证MOQ必须是SPQ的整数倍"""
        if 'spq' in info.data and v is not None and info.data['spq'] is not None:
            if v % info.data['spq'] != 0:
                raise ValueError(f"MOQ必须是SPQ的整数倍，当前SPQ: {info.data['spq']}, MOQ: {v}")
        return v


class ItemSupplierCreate(BaseModel):
    """创建物品供应商关联"""
    item_id: int = Field(..., description="物品ID")
    priority: Optional[int] = Field(None, ge=0, description="优先级 (0=Preferred, 1=Alternate1, 2=Alternate2, 3=Alternate3, null=无优先级)")
    status: str = Field("active", description="状态 (active/inactive)")
    delivery_days: int = Field(7, description="交货天数")
    quality_rating: int = Field(3, ge=1, le=5, description="质量评级 (1-5)")
    spq: int = Field(1, gt=0, description="标准包装数量")
    moq: int = Field(1, gt=0, description="最小订购数量")
    
    @field_validator('moq')
    @classmethod
    def validate_moq_is_spq_multiple(cls, v, info):
        """验证MOQ必须是SPQ的整数倍"""
        if 'spq' in info.data and v is not None and info.data['spq'] is not None:
            if v % info.data['spq'] != 0:
                raise ValueError(f"MOQ必须是SPQ的整数倍，当前SPQ: {info.data['spq']}, MOQ: {v}")
        return v


class ItemSupplierUpdate(BaseModel):
    """更新物品供应商关联"""
    priority: Optional[int] = Field(None, ge=0)
    status: Optional[str] = None
    delivery_days: Optional[int] = None
    quality_rating: Optional[int] = Field(None, ge=1, le=5)
    spq: Optional[int] = Field(None, gt=0)
    moq: Optional[int] = Field(None, gt=0)
    
    @model_validator(mode='after')
    def validate_moq_spq_relationship(self) -> 'ItemSupplierUpdate':
        """验证MOQ和SPQ的关系"""
        if self.moq is not None and self.spq is not None:
            if self.moq % self.spq != 0:
                raise ValueError(f"MOQ必须是SPQ的整数倍，当前SPQ: {self.spq}, MOQ: {self.moq}")
        return self


# 物品分类信息 Schema
class ItemCategoryInfo(BaseModel):
    """物品分类信息"""
    id: int
    name: str
    description: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


# 物品信息 Schema
class ItemInfo(BaseModel):
    """物品信息"""
    id: int
    name: str
    code: str
    description: Optional[str] = None
    image_url: Optional[str] = None
    spec_material: Optional[str] = None
    size_dimension: Optional[str] = None
    purchase_unit: Optional[str] = None
    inventory_unit: Optional[str] = None
    qty_per_up: Optional[int] = None
    brand: Optional[str] = None
    category: Optional[ItemCategoryInfo] = None

    model_config = ConfigDict(from_attributes=True)


class ItemSupplier(ItemSupplierBase):
    """物品供应商关联响应"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    supplier: Optional[Supplier] = None
    item: Optional[ItemInfo] = None

    model_config = ConfigDict(from_attributes=True)


# 供应商价格 Schemas
class SupplierPriceBase(BaseModel):
    """供应商价格基础信息"""
    item_supplier_id: int = Field(..., description="物品供应商关系ID")
    unit_price: Decimal = Field(..., gt=0, description="单价")
    currency_code: str = Field("USD", description="货币代码 (USD, CNY, EUR等)")
    min_quantity: int = Field(1, description="最小数量")
    max_quantity: Optional[int] = Field(None, description="最大数量 (NULL表示无上限)")
    valid_from: datetime = Field(..., description="生效日期")
    valid_to: Optional[datetime] = Field(None, description="失效日期 (NULL表示永久有效)")
    status: str = Field("active", description="状态 (active/inactive)")
    remarks: Optional[str] = Field(None, description="价格备注说明")


class SupplierPriceCreate(SupplierPriceBase):
    """创建供应商价格"""
    pass


class SupplierPriceUpdate(BaseModel):
    """更新供应商价格"""
    unit_price: Optional[Decimal] = Field(None, gt=0)
    currency_code: Optional[str] = Field(None, description="货币代码")
    min_quantity: Optional[int] = None
    max_quantity: Optional[int] = None
    valid_from: Optional[datetime] = None
    valid_to: Optional[datetime] = None
    status: Optional[str] = None
    remarks: Optional[str] = None


class SupplierPrice(SupplierPriceBase):
    """供应商价格响应"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None
    updated_by: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)


# 部门 Schemas
class DepartmentBase(BaseModel):
    """部门基础信息"""
    name: str = Field(..., description="部门名称")
    code: str = Field(..., description="部门编码")
    description: Optional[str] = Field(None, description="部门描述")
    is_active: bool = Field(True, description="是否激活")


class DepartmentCreate(DepartmentBase):
    """创建部门"""
    pass


class DepartmentUpdate(BaseModel):
    """更新部门"""
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


class Department(DepartmentBase):
    """部门响应"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# 采购申请 Schemas
class PurchaseRequestItemBase(BaseModel):
    """采购申请物品基础信息"""
    item_id: int = Field(..., description="物品ID")
    requested_quantity: int = Field(..., gt=0, description="申请数量")
    estimated_price: Optional[Decimal] = Field(None, description="预估单价")
    recommended_supplier_id: Optional[int] = Field(None, description="推荐供应商")
    remarks: Optional[str] = Field(None, description="备注")


class PurchaseRequestItemCreate(PurchaseRequestItemBase):
    """创建采购申请物品"""
    pass


class PurchaseRequestItem(PurchaseRequestItemBase):
    """采购申请物品响应"""
    id: int
    request_id: int
    total_amount: Optional[Decimal] = None
    status: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class PurchaseRequestBase(BaseModel):
    """采购申请基础信息"""
    department_id: int = Field(..., description="申请部门")
    required_date: Optional[datetime] = Field(None, description="需要日期")
    description: Optional[str] = Field(None, description="申请说明")
    remarks: Optional[str] = Field(None, description="备注")


class PurchaseRequestCreate(PurchaseRequestBase):
    """创建采购申请"""
    items: List[PurchaseRequestItemCreate] = Field(..., description="申请物品列表")


class PurchaseRequestUpdate(BaseModel):
    """更新采购申请"""
    required_date: Optional[datetime] = None
    description: Optional[str] = None
    remarks: Optional[str] = None
    notes: Optional[str] = None
    status: Optional[str] = None


class PurchaseRequest(PurchaseRequestBase):
    """采购申请响应"""
    id: int
    request_number: str
    applicant_id: int
    request_date: datetime
    status: str
    total_amount: Decimal
    items: List[PurchaseRequestItem] = []
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# 供应商列表查询参数
class SupplierQuery(BaseModel):
    """供应商查询参数"""
    search: Optional[str] = Field(None, description="搜索关键词")
    status: Optional[str] = Field(None, description="状态过滤")
    rating_min: Optional[int] = Field(None, ge=0, le=5, description="最小评级")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页数量")


# 供应商价格查询参数
class SupplierPriceQuery(BaseModel):
    """供应商价格查询参数"""
    supplier_id: Optional[int] = Field(None, description="供应商ID")
    item_id: Optional[int] = Field(None, description="物品ID")
    status: Optional[str] = Field("active", description="状态")
    valid_from: Optional[datetime] = Field(None, description="生效日期开始")
    valid_to: Optional[datetime] = Field(None, description="生效日期结束")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页数量")


# 价格走势相关 Schemas
class PricePoint(BaseModel):
    """价格点"""
    date: str = Field(..., description="日期 (YYYY-MM-DD)")
    unit_price: float = Field(..., description="单价")
    total_price: float = Field(..., description="总价")
    is_valid: bool = Field(..., description="是否有效")
    remarks: Optional[str] = Field(None, description="价格备注")
    currency_code: str = Field(..., description="货币代码")
    # USD价格字段
    usd_unit_price: Optional[float] = Field(None, description="USD单价")
    usd_total_price: Optional[float] = Field(None, description="USD总价")
    exchange_rate: Optional[float] = Field(None, description="汇率")

    model_config = ConfigDict(from_attributes=True)


class PriceTrendData(BaseModel):
    """价格趋势数据"""
    tier_id: int = Field(..., description="阶梯ID (基于min_quantity)")
    min_quantity: int = Field(..., description="最小数量")
    max_quantity: Optional[int] = Field(None, description="最大数量")
    tier_name: str = Field(..., description="阶梯名称 (如'1-100', '101-500'等)")
    price_points: List[PricePoint] = Field(..., description="价格点列表")

    model_config = ConfigDict(from_attributes=True)


class PriceTrendResponse(BaseModel):
    """价格走势响应"""
    item_supplier_id: int = Field(..., description="物品供应商关联ID")
    item_name: str = Field(..., description="物品名称")
    supplier_name: str = Field(..., description="供应商名称")
    price_trends: List[PriceTrendData] = Field(..., description="价格趋势数据列表")
    date_range: Dict[str, str] = Field(..., description="日期范围")

    model_config = ConfigDict(from_attributes=True) 