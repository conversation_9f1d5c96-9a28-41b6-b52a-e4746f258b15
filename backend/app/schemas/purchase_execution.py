from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal


class ExecutionBatchCreateRequest(BaseModel):
    """创建执行批次请求"""
    request_ids: List[int] = Field(..., description="采购申请ID列表")
    batch_name: Optional[str] = Field(None, description="批次名称")
    notes: Optional[str] = Field(None, description="备注说明")


class ExecutionBatchFilters(BaseModel):
    """执行批次筛选条件"""
    executor_id: Optional[int] = Field(None, description="执行人ID")
    status: Optional[str] = Field(None, description="批次状态")
    start_date: Optional[datetime] = Field(None, description="开始时间")
    end_date: Optional[datetime] = Field(None, description="结束时间")
    batch_no: Optional[str] = Field(None, description="批次编号")


class ExecutionItemResponse(BaseModel):
    """执行明细响应"""
    id: int
    batch_id: int
    request_id: int
    request_item_id: int
    item_id: int
    item_code: str
    item_name: str
    spq_quantity: Decimal
    spq_count: int
    spq_unit: str
    unit_price: Decimal
    total_price: Decimal
    supplier_id: int
    supplier_name: str
    price_locked_at: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True


class ExecutionBatchResponse(BaseModel):
    """执行批次响应"""
    id: int
    batch_no: str
    batch_name: str
    executor_id: int
    executor_name: str
    executed_at: datetime
    request_count: int
    total_amount: Decimal
    status: str
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    # 批次明细
    execution_items: Optional[List[ExecutionItemResponse]] = None

    class Config:
        from_attributes = True


class ExecutionBatchListResponse(BaseModel):
    """执行批次列表响应"""
    batches: List[ExecutionBatchResponse]
    total: int
    page: int
    size: int


class ExecutionSummaryResponse(BaseModel):
    """执行汇总响应"""
    # 按供应商汇总
    supplier_summary: List[Dict[str, Any]]
    # 按物品分类汇总
    category_summary: List[Dict[str, Any]]
    # 按时间维度汇总
    time_summary: Dict[str, Any]
    # 总计信息
    total_requests: int
    total_amount: Decimal
    unique_suppliers: int
    unique_items: int


class ExecutionExportRequest(BaseModel):
    """导出请求"""
    batch_ids: List[int] = Field(..., description="批次ID列表")
    export_format: str = Field(default="xlsx", description="导出格式: xlsx, pdf, csv")
    include_details: bool = Field(default=True, description="是否包含详细信息")


class ApprovedRequestsListResponse(BaseModel):
    """已批准申请列表响应"""
    requests: List[Dict[str, Any]]
    total: int
    page: int
    size: int


class BatchExecutionConfirmRequest(BaseModel):
    """批量执行确认请求"""
    request_ids: List[int] = Field(..., description="采购申请ID列表")
    batch_name: Optional[str] = Field(None, description="批次名称")
    notes: Optional[str] = Field(None, description="备注说明")
    confirm: bool = Field(True, description="确认执行")


class BatchExecutionPreviewResponse(BaseModel):
    """批量执行预览响应"""
    request_count: int
    total_amount: Decimal
    estimated_batch_no: str
    requests_preview: List[Dict[str, Any]]
    supplier_summary: List[Dict[str, Any]]
    warnings: List[str] = []
