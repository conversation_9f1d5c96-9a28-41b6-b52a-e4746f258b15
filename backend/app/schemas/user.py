from pydantic import BaseModel, EmailStr, Field, field_validator
from typing import Optional, List
from datetime import datetime
from enum import Enum

class AccountStatus(str, Enum):
    """账号状态枚举"""
    ACTIVE = "active"
    DISABLED = "disabled"
    LOCKED = "locked"
    PENDING = "pending"

class PasswordStatus(str, Enum):
    """密码状态枚举"""
    NORMAL = "normal"
    NEED_RESET = "need_reset"
    TEMPORARY = "temporary"

class UserBase(BaseModel):
    """用户基础信息"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱")
    full_name: Optional[str] = Field(None, max_length=100, description="真实姓名")
    display_name: Optional[str] = Field(None, max_length=100, description="显示名/备注名")
    phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    employee_id: Optional[str] = Field(None, max_length=50, description="工号")
    position: Optional[str] = Field(None, max_length=100, description="职位")
    department_id: Optional[int] = Field(None, description="主部门ID")


class UserCreate(UserBase):
    """创建用户时的数据结构"""
    password: str = Field(..., min_length=6, description="密码")
    role_id: Optional[int] = Field(None, description="用户角色ID")
    account_status: AccountStatus = Field(default=AccountStatus.ACTIVE, description="账号状态")
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        """密码验证 - 内网环境简化要求"""
        if len(v) < 6:
            raise ValueError('密码长度至少6位')
        return v

class UserUpdate(BaseModel):
    """更新用户信息"""
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, max_length=100)
    display_name: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    employee_id: Optional[str] = Field(None, max_length=50)
    position: Optional[str] = Field(None, max_length=100)
    department_id: Optional[int] = None

    role_id: Optional[int] = None
    is_active: Optional[bool] = None
    account_status: Optional[AccountStatus] = None

class UserPasswordUpdate(BaseModel):
    """用户密码更新"""
    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., min_length=6, description="新密码")
    
    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v):
        """新密码验证 - 内网环境简化要求"""
        if len(v) < 6:
            raise ValueError('密码长度至少6位')
        return v

class UserPasswordReset(BaseModel):
    """管理员重置用户密码"""
    new_password: str = Field(..., min_length=6, description="新密码")
    is_temporary: bool = Field(default=True, description="是否为临时密码")

class User(UserBase):
    """返回给客户端的用户信息"""
    id: int
    is_active: bool
    is_superuser: bool
    account_status: AccountStatus
    password_status: PasswordStatus
    last_login_at: Optional[datetime] = None
    created_at: datetime
    role_id: Optional[int]
    
    # 关联信息
    department_name: Optional[str] = None
    
    # 权限相关
    role_name: Optional[str] = None
    role_code: Optional[str] = None
    permissions: List[str] = Field(default_factory=list, description="用户权限列表")
    
    model_config = {"from_attributes": True}

class UserInDB(User):
    """数据库中的用户信息（包含密码hash）"""
    hashed_password: str
    password_changed_at: Optional[datetime] = None
    login_attempts: int
    locked_until: Optional[datetime] = None

class UserProfile(BaseModel):
    """用户个人资料"""
    id: int
    username: str
    email: EmailStr
    full_name: Optional[str]
    display_name: Optional[str]
    phone: Optional[str]
    employee_id: Optional[str]
    position: Optional[str]
    avatar: Optional[str]
    department_name: Optional[str]
    supervisor_name: Optional[str]
    role_id: Optional[int]
    last_login_at: Optional[datetime]
    created_at: datetime



class UserListQuery(BaseModel):
    """用户列表查询参数"""
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=10, ge=1, le=100)
    search: Optional[str] = None
    department_id: Optional[int] = None
    role_id: Optional[int] = None
    account_status: Optional[AccountStatus] = None
    is_active: Optional[bool] = None

# 登录相关
class UserLogin(BaseModel):
    """用户登录请求"""
    username: str = Field(..., description="用户名/邮箱/工号")
    password: str = Field(..., description="密码")

class Token(BaseModel):
    """JWT Token响应"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: User

class TokenData(BaseModel):
    """Token中包含的数据"""
    username: Optional[str] = None
    user_id: Optional[int] = None 