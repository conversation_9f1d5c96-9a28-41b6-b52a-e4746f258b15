from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal

# 导入用户相关的数据模式
from .user import User

# ==================== 一级分类相关 ====================

class ItemPrimaryCategoryBase(BaseModel):
    """物品一级分类基础信息"""
    name: str = Field(..., description="一级分类名称")
    description: Optional[str] = Field(None, description="一级分类描述")
    code_prefix: str = Field(..., description="编码前缀")
    code_format: str = Field("0000", description="编码格式")
    current_sequence: int = Field(1, description="当前序号")
    is_active: bool = Field(True, description="是否启用")

class ItemPrimaryCategoryCreate(ItemPrimaryCategoryBase):
    """创建一级分类时的数据结构"""
    pass

class ItemPrimaryCategoryUpdate(BaseModel):
    """更新一级分类信息"""
    name: Optional[str] = Field(None, description="一级分类名称")
    description: Optional[str] = Field(None, description="一级分类描述")
    code_prefix: Optional[str] = Field(None, description="编码前缀")
    code_format: Optional[str] = Field(None, description="编码格式")
    current_sequence: Optional[int] = Field(None, description="当前序号")
    is_active: Optional[bool] = Field(None, description="是否启用")

class ItemPrimaryCategory(ItemPrimaryCategoryBase):
    """返回给客户端的一级分类信息"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    model_config = ConfigDict(from_attributes=True)

# ==================== 二级分类相关 ====================

class ItemCategoryBase(BaseModel):
    """物品二级分类基础信息"""
    name: str = Field(..., description="二级分类名称")
    description: Optional[str] = Field(None, description="二级分类描述")
    primary_category_id: Optional[int] = Field(None, description="所属一级分类ID")
    is_active: bool = Field(True, description="是否启用")

class ItemCategoryCreate(ItemCategoryBase):
    """创建二级分类时的数据结构"""
    pass

class ItemCategoryUpdate(BaseModel):
    """更新二级分类信息"""
    name: Optional[str] = Field(None, description="二级分类名称")
    description: Optional[str] = Field(None, description="二级分类描述")
    primary_category_id: Optional[int] = Field(None, description="所属一级分类ID")
    is_active: Optional[bool] = Field(None, description="是否启用")

class ItemCategory(ItemCategoryBase):
    """返回给客户端的二级分类信息"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    primary_category: Optional[ItemPrimaryCategory] = None
    
    model_config = ConfigDict(from_attributes=True)

# ==================== 物品属性配置相关 ====================

class ItemPropertyConfigBase(BaseModel):
    """物品属性配置基础信息"""
    category_id: int = Field(..., description="分类ID")
    attribute_name: str = Field(..., description="属性名称：brand/spec_material/size_dimension")
    input_type: str = Field(..., description="输入类型：select/text")
    options: Optional[str] = Field(None, description="JSON格式的选项值数组，仅select类型使用")

class ItemPropertyConfigCreate(ItemPropertyConfigBase):
    """创建属性配置时的数据结构"""
    pass

class ItemPropertyConfigUpdate(BaseModel):
    """更新属性配置信息"""
    input_type: Optional[str] = Field(None, description="输入类型：select/text")
    options: Optional[str] = Field(None, description="JSON格式的选项值数组，仅select类型使用")

class ItemPropertyConfig(ItemPropertyConfigBase):
    """返回给客户端的属性配置信息"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    category: Optional[ItemCategory] = None
    
    model_config = ConfigDict(from_attributes=True)

# ==================== 物品相关 ====================

class ItemBase(BaseModel):
    """物品基础信息"""
    name: str = Field(..., description="物品名称")
    code: Optional[str] = Field(None, description="物品编码")
    description: Optional[str] = Field(None, description="物品描述")
    category_id: int = Field(..., description="所属分类ID")
    image_url: Optional[str] = Field(None, description="物品图片URL")
    purchase_unit: str = Field("个", description="采购单位")
    inventory_unit: str = Field("个", description="库存单位")
    qty_per_up: int = Field(1, ge=1, description="每采购单位包含的库存单位数量")
    is_purchasable: bool = Field(True, description="是否可购买")
    is_active: bool = Field(True, description="是否启用")
    # 新的固定属性字段
    brand: Optional[str] = Field(None, description="品牌")
    spec_material: Optional[str] = Field(None, description="规格/材质")
    size_dimension: Optional[str] = Field(None, description="尺寸/规格")

class ItemCreate(ItemBase):
    """创建物品时的数据结构"""
    pass

class ItemUpdate(BaseModel):
    """更新物品信息"""
    name: Optional[str] = Field(None, description="物品名称")
    code: Optional[str] = Field(None, description="物品编码")
    description: Optional[str] = Field(None, description="物品描述")
    category_id: Optional[int] = Field(None, description="所属分类ID")
    image_url: Optional[str] = Field(None, description="物品图片URL")
    purchase_unit: Optional[str] = Field(None, description="采购单位")
    inventory_unit: Optional[str] = Field(None, description="库存单位")
    qty_per_up: Optional[int] = Field(None, description="每采购单位包含的库存单位数量")
    is_purchasable: Optional[bool] = Field(None, description="是否可购买")
    is_active: Optional[bool] = Field(None, description="是否启用")
    total_stock: Optional[int] = Field(None, description="总库存")
    # 新的固定属性字段
    brand: Optional[str] = Field(None, description="品牌")
    spec_material: Optional[str] = Field(None, description="规格/材质")
    size_dimension: Optional[str] = Field(None, description="尺寸/规格")

class Item(ItemBase):
    """返回给客户端的物品信息"""
    id: int
    total_stock: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    category: Optional[ItemCategory] = None
    
    model_config = ConfigDict(from_attributes=True)

class ItemListResponse(BaseModel):
    """物品列表响应"""
    items: List[Item]
    total: int
    page: int
    size: int

# ==================== 物品变更历史相关 ====================

class ItemChangeHistoryBase(BaseModel):
    """物品变更历史基础信息"""
    item_id: int = Field(..., description="物品ID")
    user_id: int = Field(..., description="用户ID")
    action: str = Field(..., description="操作类型")
    field_name: Optional[str] = Field(None, description="字段名称")
    old_value: Optional[str] = Field(None, description="旧值")
    new_value: Optional[str] = Field(None, description="新值")

class ItemChangeHistoryCreate(ItemChangeHistoryBase):
    """创建变更历史时的数据结构"""
    pass

class ItemChangeHistory(ItemChangeHistoryBase):
    """返回给客户端的变更历史信息"""
    id: int
    created_at: datetime
    user: Optional[User] = Field(None, description="操作用户信息")
    
    model_config = ConfigDict(from_attributes=True)

# ==================== 搜索和筛选相关 ====================

class ItemSearchParams(BaseModel):
    """物品搜索参数"""
    search: Optional[str] = Field(None, description="搜索关键词")
    category_id: Optional[int] = Field(None, description="分类ID")
    primary_category_id: Optional[int] = Field(None, description="一级分类ID")
    is_active: Optional[bool] = Field(None, description="是否启用")
    is_purchasable: Optional[bool] = Field(None, description="是否可购买")
    min_price: Optional[Decimal] = Field(None, description="最低价格")
    max_price: Optional[Decimal] = Field(None, description="最高价格")
    # 新的属性筛选字段
    brand: Optional[str] = Field(None, description="品牌筛选")
    spec_material: Optional[str] = Field(None, description="规格/材质筛选")
    size_dimension: Optional[str] = Field(None, description="尺寸/规格筛选")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页数量")
    sort_by: Optional[str] = Field("name", description="排序字段")
    sort_order: Optional[str] = Field("asc", description="排序方向")

class ItemCategorySearchParams(BaseModel):
    """分类搜索参数"""
    primary_category_id: Optional[int] = Field(None, description="一级分类ID")
    is_active: Optional[bool] = Field(None, description="是否启用")
    search: Optional[str] = Field(None, description="搜索关键词") 