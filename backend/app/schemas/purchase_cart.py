from pydantic import BaseModel, Field
from typing import Optional, List, Dict
from datetime import date, datetime
from decimal import Decimal


class AddCartItemRequest(BaseModel):
    """添加购物车项目请求"""
    item_id: int = Field(..., description="物品ID")
    spq_count: int = Field(..., description="SPQ个数（需要多少个标准包装）")
    notes: Optional[str] = Field(None, description="备注")


class UpdateCartItemRequest(BaseModel):
    """更新购物车项目请求"""
    spq_count: Optional[int] = Field(None, description="SPQ个数（需要多少个标准包装）")
    notes: Optional[str] = Field(None, description="备注")


class CartItemResponse(BaseModel):
    """购物车项目响应"""
    id: int
    department_id: int
    item_id: int
    item_code: Optional[str] = None
    item_name: Optional[str] = None
    item_image_url: Optional[str] = None
    spq_quantity: Decimal
    spq_count: int
    spq_unit: str
    estimated_unit_price: Optional[float] = None
    total_price: Optional[float] = None
    notes: Optional[str]
    created_by: int
    created_at: datetime
    updated_at: datetime
    
    # 库存信息
    current_quantity: Optional[float] = None
    max_quantity: Optional[float] = None
    cart_quantity: Optional[float] = None
    total_after_purchase: Optional[float] = None
    is_overstock: Optional[bool] = None
    overstock_message: Optional[str] = None
    


    class Config:
        from_attributes = True


class CartItemWithDetails(BaseModel):
    """购物车项目详细信息（包含物品信息和库存信息）"""
    # 基础购物车信息
    id: int
    department_id: int
    item_id: int
    spq_quantity: Decimal
    spq_count: int
    spq_unit: str
    notes: Optional[str]
    created_by: int
    created_at: datetime
    updated_at: datetime
    
    # 物品信息
    item_code: str
    item_name: str
    item_image_url: Optional[str] = None
    
    # 价格信息
    estimated_unit_price: float = 0.0
    total_price: float = 0.0
    
    # 库存信息
    current_quantity: float = 0.0
    max_quantity: Optional[float] = None
    cart_quantity: float
    total_after_purchase: float
    is_overstock: bool = False
    overstock_message: str = ""

    class Config:
        from_attributes = True


class CartSummaryResponse(BaseModel):
    """购物车摘要响应"""
    department_id: int
    total_items: int
    total_quantity: Decimal
    total_amount: Optional[float] = None
    items: List[CartItemResponse]

    class Config:
        from_attributes = True


class InventoryCheckResult(BaseModel):
    """库存检查结果"""
    is_overstock: bool = Field(..., description="是否超过最大库存")
    current_quantity: Decimal = Field(..., description="当前库存量")
    max_quantity: Optional[Decimal] = Field(None, description="最大库存量")
    cart_quantity: Decimal = Field(..., description="购物车中的数量")
    total_after_purchase: Decimal = Field(..., description="购买后的总库存量")
    alert_message: str = Field(..., description="预警消息")
    item_name: Optional[str] = Field(None, description="物品名称")
    item_code: Optional[str] = Field(None, description="物品编码")


class CartInventoryCheckResponse(BaseModel):
    """购物车库存检查响应"""
    department_id: int
    total_items: int
    overstock_items: List[InventoryCheckResult]
    overstock_count: int
    alert_file_path: Optional[str] = None
    alert_generated: bool
    check_timestamp: datetime
