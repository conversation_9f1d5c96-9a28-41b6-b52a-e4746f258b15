"""
报表相关的Pydantic schemas
"""
from pydantic import BaseModel, Field
from typing import List, Dict, Any
from datetime import datetime
from decimal import Decimal


class DashboardOverview(BaseModel):
    """仪表板概览数据"""
    total_items: int = Field(..., description="物品总数")
    total_departments: int = Field(..., description="部门总数")
    total_suppliers: int = Field(..., description="供应商总数")
    today_usages: int = Field(..., description="今日领取次数")
    period_usages: int = Field(..., description="本期领取次数")
    pending_usages: int = Field(..., description="待确认领取")
    total_cost: Decimal = Field(..., description="本期总费用")
    active_users: int = Field(..., description="活跃用户数")
    low_stock_items: int = Field(..., description="低库存物品数")
    period_days: int = Field(..., description="统计天数")


class ConsumptionReport(BaseModel):
    """消耗报表"""
    item_id: int = Field(..., description="物品ID")
    item_name: str = Field(..., description="物品名称")
    item_code: str = Field(..., description="物品编码")
    inventory_unit: str = Field(..., description="库存单位")
    total_quantity: int = Field(..., description="总消耗量")
    total_amount: Decimal = Field(..., description="总金额")
    usage_count: int = Field(..., description="领取次数")
    avg_quantity: float = Field(..., description="平均单次领取量")


class InventoryReport(BaseModel):
    """库存报表"""
    item_id: int = Field(..., description="物品ID")
    item_name: str = Field(..., description="物品名称")
    item_code: str = Field(..., description="物品编码")
    inventory_unit: str = Field(..., description="库存单位")
    current_stock: int = Field(..., description="当前库存")
    min_stock: int = Field(..., description="最低库存")
    stock_status: str = Field(..., description="库存状态")
    department_name: str = Field(..., description="所属部门")


class ProcurementReport(BaseModel):
    """采购报表"""
    request_id: int = Field(..., description="申请ID")
    request_code: str = Field(..., description="申请编号")
    status: str = Field(..., description="状态")
    total_amount: Decimal = Field(..., description="总金额")
    created_at: datetime = Field(..., description="创建时间")
    department_name: str = Field(..., description="申请部门")
    requester_name: str = Field(..., description="申请人")


class DepartmentUsageReport(BaseModel):
    """部门使用情况报表"""
    department_id: int = Field(..., description="部门ID")
    department_name: str = Field(..., description="部门名称")
    usage_count: int = Field(..., description="领取次数")
    total_quantity: int = Field(..., description="总领取量")
    total_amount: Decimal = Field(..., description="总金额")
    active_users: int = Field(..., description="活跃用户数")
    item_types: int = Field(..., description="物品种类数")


class TopItemsReport(BaseModel):
    """热门物品报表"""
    period_days: int = Field(..., description="统计天数")
    sort_by: str = Field(..., description="排序方式")
    total_items: int = Field(..., description="物品总数")
    items: List[Dict[str, Any]] = Field(..., description="物品列表")


class TrendAnalysis(BaseModel):
    """趋势分析数据"""
    period_days: int = Field(..., description="分析天数")
    granularity: str = Field(..., description="时间粒度")
    data_points: int = Field(..., description="数据点数量")
    trend_data: List[Dict[str, Any]] = Field(..., description="趋势数据")


class CostAnalysis(BaseModel):
    """成本分析数据"""
    group_by: str = Field(..., description="分组方式")
    total_cost: float = Field(..., description="总成本")
    group_count: int = Field(..., description="分组数量")
    cost_data: List[Dict[str, Any]] = Field(..., description="成本数据")


# 导出Excel报表请求
class ExportReportRequest(BaseModel):
    """导出报表请求"""
    report_type: str = Field(..., description="报表类型")
    start_date: datetime = Field(..., description="开始日期")
    end_date: datetime = Field(..., description="结束日期")
    filters: Dict[str, Any] = Field(default={}, description="筛选条件")
    format: str = Field(default="xlsx", description="导出格式")


# 报表配置
class ReportConfig(BaseModel):
    """报表配置"""
    report_id: str = Field(..., description="报表ID")
    report_name: str = Field(..., description="报表名称")
    description: str = Field(..., description="报表描述")
    default_period: int = Field(default=30, description="默认统计天数")
    available_filters: List[str] = Field(..., description="可用筛选器")
    chart_types: List[str] = Field(..., description="支持的图表类型") 