"""
物品领取相关的Pydantic schemas
"""
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from app.models.usage import UsageStatus


# 二维码相关schemas
class QRCodeBase(BaseModel):
    code: str = Field(..., description="二维码内容")
    code_type: str = Field(..., description="类型: item, user_card, location")
    related_id: Optional[int] = Field(None, description="关联对象ID")
    related_table: Optional[str] = Field(None, description="关联表名")
    location_name: Optional[str] = Field(None, description="位置名称")
    shelf_number: Optional[str] = Field(None, description="货架编号")
    position: Optional[str] = Field(None, description="具体位置")
    is_active: bool = Field(True, description="是否激活")


class QRCodeCreate(QRCodeBase):
    pass


class QRCodeUpdate(BaseModel):
    code: Optional[str] = None
    code_type: Optional[str] = None
    related_id: Optional[int] = None
    related_table: Optional[str] = None
    location_name: Optional[str] = None
    shelf_number: Optional[str] = None
    position: Optional[str] = None
    is_active: Optional[bool] = None


class QRCodeResponse(QRCodeBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    created_by: Optional[int]

    model_config = ConfigDict(from_attributes=True)


# 物品领取相关schemas
class ItemUsageBase(BaseModel):
    item_id: int = Field(..., description="物品ID")
    quantity: Decimal = Field(..., gt=0, description="领取数量（库存单位）")
    usage_purpose: Optional[str] = Field(None, description="使用目的")
    item_qr_code: Optional[str] = Field(None, description="物品二维码数据")
    user_card_code: Optional[str] = Field(None, description="工卡二维码数据")
    location: Optional[str] = Field(None, description="领取地点")
    notes: Optional[str] = Field(None, description="备注")


class ItemUsageCreate(ItemUsageBase):
    device_info: Optional[str] = Field(None, description="设备信息")


class ItemUsageUpdate(BaseModel):
    quantity: Optional[Decimal] = Field(None, gt=0, description="领取数量（库存单位）")
    usage_purpose: Optional[str] = None
    status: Optional[UsageStatus] = None
    notes: Optional[str] = None



class ItemUsageResponse(ItemUsageBase):
    id: int
    usage_code: str
    user_id: int
    department_id: int
    unit_price: Optional[Decimal]
    total_amount: Optional[Decimal]
    status: UsageStatus
    usage_date: datetime
    confirmed_at: Optional[datetime]
    confirmed_by: Optional[int]
    device_info: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]
    
    # 关联对象信息
    item_name: Optional[str] = None
    item_code: Optional[str] = None
    purchase_unit: Optional[str] = None
    inventory_unit: Optional[str] = None
    qty_per_up: Optional[int] = None
    user_name: Optional[str] = None
    department_name: Optional[str] = None
    confirmer_name: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class ItemUsageListResponse(BaseModel):
    items: List[ItemUsageResponse]
    total: int
    page: int
    size: int
    pages: int


# 扫码相关schemas
class QRCodeScanRequest(BaseModel):
    code: str = Field(..., description="扫描到的二维码内容")
    code_type: str = Field(..., description="二维码类型")


class QRCodeScanResponse(BaseModel):
    success: bool
    code_type: str
    data: dict = Field(..., description="解析后的数据")
    message: Optional[str] = None


class ItemQRCodeData(BaseModel):
    item_id: int
    item_name: str
    item_code: str
    category_name: str
    unit: str
    available_stock: int
    location_name: Optional[str] = None
    shelf_number: Optional[str] = None


class UserCardQRCodeData(BaseModel):
    user_id: int
    username: str
    full_name: str
    department_id: int
    department_name: str
    roles: List[str]



# 统计相关schemas
class UsageStatistics(BaseModel):
    total_usages: int
    today_usages: int
    pending_confirmations: int
    total_amount: Decimal
    top_items: List[dict]
    department_statistics: List[dict] 