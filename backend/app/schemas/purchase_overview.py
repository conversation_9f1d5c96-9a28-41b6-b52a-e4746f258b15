"""
采购概览相关的Pydantic schemas
"""

from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import date


class PurchaseOverviewResponse(BaseModel):
    """采购概览统计数据响应"""
    total_requests: int = Field(..., description="采购申请总数")
    pending_requests: int = Field(..., description="待处理申请数")
    approved_requests: int = Field(..., description="已批准申请数")
    total_amount: float = Field(..., description="总采购金额")
    total_cart_items: int = Field(..., description="购物车物品总数")
    active_carts: int = Field(..., description="活跃购物车数")



class RecentActivityResponse(BaseModel):
    """最近活动响应"""
    id: int = Field(..., description="活动ID")
    type: str = Field(..., description="活动类型")
    title: str = Field(..., description="活动标题")
    description: str = Field(..., description="活动描述")
    time: str = Field(..., description="时间描述")
    user: str = Field(..., description="操作用户")
    status: str = Field(..., description="状态")


class ApprovalTodoResponse(BaseModel):
    """审批待办统计响应"""
    department_review: int = Field(..., description="部门经理复核待办数")
    principle_approval: int = Field(..., description="主管审批待办数")
    final_approval: int = Field(..., description="Boss审批待办数")



