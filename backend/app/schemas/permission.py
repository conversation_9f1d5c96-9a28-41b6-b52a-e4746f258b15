from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List
from datetime import datetime

# Permission Schemas
class PermissionBase(BaseModel):
    """权限基础信息"""
    code: str = Field(..., description="权限代码")
    name: str = Field(..., description="权限名称")
    description: Optional[str] = Field(None, description="权限描述")
    module: str = Field(..., description="所属模块")



class PermissionCreate(PermissionBase):
    """创建权限"""
    is_active: bool = Field(default=True, description="是否启用")

class PermissionUpdate(BaseModel):
    """更新权限"""
    name: Optional[str] = None
    description: Optional[str] = None
    module: Optional[str] = None

    is_active: Optional[bool] = None

class Permission(PermissionBase):
    """权限响应"""
    id: int
    is_active: bool
    is_system: bool
    created_at: datetime

    
    model_config = ConfigDict(from_attributes=True)

# Role Schemas
class RoleBase(BaseModel):
    """角色基础信息"""
    code: str = Field(..., description="角色代码")
    name: str = Field(..., description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")



class RoleCreate(RoleBase):
    """创建角色"""
    is_active: bool = Field(default=True, description="是否启用")
    permission_ids: List[int] = Field(default=[], description="权限ID列表")

class RoleUpdate(BaseModel):
    """更新角色"""
    name: Optional[str] = None
    description: Optional[str] = None


    is_active: Optional[bool] = None
    permission_ids: Optional[List[int]] = None

class Role(RoleBase):
    """角色响应"""
    id: int
    is_active: bool
    is_system: bool
    created_at: datetime
    permissions: List[Permission] = []

    
    model_config = ConfigDict(from_attributes=True)

class RolePermissionMatrix(BaseModel):
    """角色权限矩阵"""
    role: Role
    permissions: List[Permission]


# User Role Assignment
class UserRoleAssign(BaseModel):
    """用户角色分配"""
    user_id: int
    role_id: int



class UserPermissions(BaseModel):
    """用户权限信息"""
    user_id: int
    role: Optional[Role] = None
    all_permissions: List[str] = []  # 权限代码列表

# Permission Groups
class PermissionNode(BaseModel):
    """权限分组节点"""
    id: int
    code: str
    name: str
    description: Optional[str]
    module: str
    is_active: bool
    permissions: List['Permission'] = []




# Audit Log
class AuditLogBase(BaseModel):
    """审计日志基础信息"""
    action: str
    resource_type: Optional[str] = None
    resource_id: Optional[int] = None
    old_value: Optional[str] = None
    new_value: Optional[str] = None

class AuditLog(AuditLogBase):
    """审计日志响应"""
    id: int
    user_id: Optional[int]
    ip_address: Optional[str]
    user_agent: Optional[str]
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

# Query Schemas
class PermissionQuery(BaseModel):
    """权限查询参数"""
    module: Optional[str] = None
    is_active: Optional[bool] = None
    search: Optional[str] = None

class RoleQuery(BaseModel):
    """角色查询参数"""
    is_active: Optional[bool] = None
    search: Optional[str] = None

# 递归模型更新
Permission.model_rebuild()
Role.model_rebuild()
PermissionNode.model_rebuild()
 