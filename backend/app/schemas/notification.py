from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field


class NotificationBase(BaseModel):
    """通知基础模式"""
    title: str = Field(..., min_length=1, max_length=200, description="通知标题")
    content: str = Field(..., min_length=1, description="通知内容")
    notification_type: str = Field(..., description="通知类型")
    business_data: Optional[Dict[str, Any]] = Field(None, description="业务相关数据")
    action_url: Optional[str] = Field(None, max_length=500, description="相关业务操作链接")


class NotificationCreate(NotificationBase):
    """创建通知模式"""
    user_id: int = Field(..., description="接收用户ID")


class NotificationUpdate(BaseModel):
    """更新通知模式"""
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="通知标题")
    content: Optional[str] = Field(None, min_length=1, description="通知内容")
    business_data: Optional[Dict[str, Any]] = Field(None, description="业务相关数据")
    action_url: Optional[str] = Field(None, max_length=500, description="相关业务操作链接")


class NotificationFilter(BaseModel):
    """通知筛选模式"""
    user_id: Optional[int] = Field(None, description="用户ID")
    notification_type: Optional[str] = Field(None, description="通知类型")
    status: Optional[str] = Field(None, description="通知状态")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")


class NotificationResponse(NotificationBase):
    """通知响应模式"""
    id: int
    user_id: int
    status: str
    created_at: datetime
    read_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class NotificationListResponse(BaseModel):
    """通知列表响应模式"""
    data: List[NotificationResponse]
    total: int
    page: int
    size: int
    pages: int


class NotificationEmailBase(BaseModel):
    """邮件记录基础模式"""
    notification_id: int = Field(..., description="关联的通知ID")
    user_id: int = Field(..., description="接收用户ID")


class NotificationEmailResponse(NotificationEmailBase):
    """邮件记录响应模式"""
    id: int
    email_status: str
    failure_reason: Optional[str] = None
    sent_at: Optional[datetime] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class NotificationEmailListResponse(BaseModel):
    """邮件记录列表响应模式"""
    data: List[NotificationEmailResponse]
    total: int
    page: int
    size: int
    pages: int


class BatchOperationRequest(BaseModel):
    """批量操作请求模式"""
    notification_ids: List[int] = Field(..., min_items=1, description="通知ID列表")


class NotificationStatsResponse(BaseModel):
    """通知统计响应模式"""
    total: int
    unread: int
    read: int
    by_type: Dict[str, int]


class EmailStatsResponse(BaseModel):
    """邮件统计响应模式"""
    total: int
    pending: int
    sent: int
    failed: int
    today_sent: int
    success_rate: float


class SchedulerStatusResponse(BaseModel):
    """调度器状态响应模式"""
    status: str
    next_run: Optional[str] = None
    interval_minutes: int


class SchedulerHealthResponse(BaseModel):
    """调度器健康检查响应模式"""
    scheduler_status: SchedulerStatusResponse
    database_healthy: bool
    config_service_healthy: bool
    overall_healthy: bool
    error: Optional[str] = None


class TestEmailRequest(BaseModel):
    """测试邮件请求模式"""
    email: str = Field(..., description="测试邮箱地址")


class TestEmailResponse(BaseModel):
    """测试邮件响应模式"""
    success: bool
    message: str


class ManualTriggerResponse(BaseModel):
    """手动触发响应模式"""
    success: bool
    message: str
    processed: int
    stats: Optional[EmailStatsResponse] = None
