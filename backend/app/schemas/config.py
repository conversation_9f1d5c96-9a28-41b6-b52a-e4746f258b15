from typing import Optional, Dict, Any
from pydantic import BaseModel, Field


class SystemConfigBase(BaseModel):
    """系统配置基础模式"""
    config_key: str = Field(..., min_length=1, max_length=100, description="配置键")
    config_value: Optional[str] = Field(None, description="配置值")
    description: Optional[str] = Field(None, max_length=200, description="配置描述")


class SystemConfigCreate(SystemConfigBase):
    """创建系统配置模式"""
    pass


class SystemConfigUpdate(BaseModel):
    """更新系统配置模式"""
    config_value: Optional[str] = Field(None, description="配置值")
    description: Optional[str] = Field(None, max_length=200, description="配置描述")


class SystemConfigResponse(SystemConfigBase):
    """系统配置响应模式"""
    id: int
    updated_at: str
    
    class Config:
        from_attributes = True


class SMTPConfigRequest(BaseModel):
    """SMTP配置请求模式"""
    smtp_host: str = Field(..., min_length=1, description="SMTP服务器地址")
    smtp_port: str = Field(..., description="SMTP服务器端口")
    smtp_username: str = Field(..., min_length=1, description="SMTP用户名")
    smtp_password: str = Field(..., min_length=1, description="SMTP密码")
    smtp_use_tls: bool = Field(True, description="是否使用TLS加密")


class SMTPConfigResponse(BaseModel):
    """SMTP配置响应模式"""
    smtp_host: str
    smtp_port: str
    smtp_username: str
    smtp_password: str
    smtp_use_tls: bool


class NotificationConfigRequest(BaseModel):
    """通知配置请求模式"""
    notification_enabled: bool = Field(True, description="系统通知功能开关")
    email_send_interval: int = Field(5, ge=1, le=60, description="邮件发送间隔（分钟）")


class NotificationConfigResponse(BaseModel):
    """通知配置响应模式"""
    notification_enabled: bool
    email_send_interval: int


class ConfigBulkUpdateRequest(BaseModel):
    """批量更新配置请求模式"""
    configs: Dict[str, str] = Field(..., description="配置键值对")


class ConfigBulkUpdateResponse(BaseModel):
    """批量更新配置响应模式"""
    success: bool
    message: str
    updated_count: int
