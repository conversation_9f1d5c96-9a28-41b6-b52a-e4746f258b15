"""
库存管理相关的Pydantic schemas
"""
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from decimal import Decimal
from enum import Enum


class InventoryStatus(str, Enum):
    """库存状态枚举"""
    NORMAL = "normal"
    LOW = "low"
    OUT_OF_STOCK = "out_of_stock"
    OVERSTOCK = "overstock"


class InventoryChangeType(str, Enum):
    """库存变更类型枚举"""
    MANUAL_IN = "manual_in"
    PICKUP_OUT = "pickup_out"
    ADJUST = "adjust"
    TRANSFER = "transfer"
    RETURN = "return"


class AlertType(str, Enum):
    """预警类型枚举"""
    LOW_STOCK = "low_stock"
    OUT_OF_STOCK = "out_of_stock"
    OVERSTOCK = "overstock"
    EXPIRY = "expiry"


class AlertLevel(str, Enum):
    """预警级别枚举"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"


class DepartmentInventoryBase(BaseModel):
    """部门库存基础模型"""
    department_id: int = Field(..., description="部门ID")
    item_id: int = Field(..., description="物品ID")
    current_quantity: Decimal = Field(0, description="当前库存数量")
    min_quantity: Decimal = Field(0, description="最小库存量")
    max_quantity: Optional[Decimal] = Field(None, description="最大库存量")
    storage_location: Optional[str] = Field(None, description="存储位置")
    rack_number: Optional[str] = Field(None, description="货架号")
    # status字段现在是动态计算的，不在基础模型中定义
    notes: Optional[str] = Field(None, description="备注")


class DepartmentInventoryCreate(DepartmentInventoryBase):
    """创建部门库存"""
    pass


class DepartmentInventoryUpdate(BaseModel):
    """更新部门库存"""
    department_id: Optional[int] = Field(None, description="部门ID")
    item_id: Optional[int] = Field(None, description="物品ID")
    current_quantity: Optional[Decimal] = Field(None, description="当前库存数量")
    min_quantity: Optional[Decimal] = Field(None, description="最小库存量")
    max_quantity: Optional[Decimal] = Field(None, description="最大库存量")
    storage_location: Optional[str] = Field(None, description="存储位置")
    rack_number: Optional[str] = Field(None, description="货架号")
    # status字段现在是动态计算的，不能手动更新
    notes: Optional[str] = Field(None, description="备注")
    is_active: Optional[bool] = Field(None, description="是否激活库存管理")


class StartInventoryRequest(BaseModel):
    """启动库存请求模型"""
    item_id: int = Field(..., description="物品ID")
    min_quantity: Optional[Decimal] = Field(None, description="最小库存量")
    max_quantity: Optional[Decimal] = Field(None, description="最大库存量")
    storage_location: Optional[str] = Field(None, description="存储位置")
    rack_number: Optional[str] = Field(None, description="货架号")
    notes: Optional[str] = Field(None, description="备注")


class DepartmentInventoryResponse(DepartmentInventoryBase):
    """部门库存响应模型"""
    id: int = Field(..., description="库存ID")
    department_name: str = Field(..., description="部门名称")
    item_name: str = Field(..., description="物品名称")
    item_code: str = Field(..., description="物品编码")
    purchase_unit: str = Field(..., description="采购单位")
    inventory_unit: str = Field(..., description="库存单位")
    qty_per_up: int = Field(..., description="每采购单位包含的库存单位数量")
    purchase_unit_quantity: Optional[Decimal] = Field(None, description="按采购单位换算的数量")
    status: str = Field(..., description="库存状态（动态计算）")
    last_purchase_price: Optional[Decimal] = Field(None, description="最后采购价格（动态计算）")
    average_cost: Optional[Decimal] = Field(None, description="平均成本（动态计算）")
    total_value: Optional[Decimal] = Field(None, description="库存总价值（动态计算）")
    calculated_usd_price: Optional[Decimal] = Field(None, description="计算后的USD单价")
    original_currency_price: Optional[Decimal] = Field(None, description="原始货币价格")
    currency_code: Optional[str] = Field(None, description="货币代码")
    exchange_rate: Optional[Decimal] = Field(None, description="使用的汇率")
    last_updated: datetime = Field(..., description="最后更新时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    item_image_url: Optional[str] = Field(None, description="物品图片URL")
    has_inventory: bool = Field(..., description="是否有库存记录")
    is_active: bool = Field(..., description="是否激活库存管理")


    class Config:
        from_attributes = True


class InventoryChangeRecordBase(BaseModel):
    """库存变更记录基础模型"""
    department_id: int = Field(..., description="部门ID")
    item_id: int = Field(..., description="物品ID")
    before_quantity: Decimal = Field(..., description="变更前库存数量")
    after_quantity: Decimal = Field(..., description="变更后库存数量")
    change_quantity: Decimal = Field(..., description="变更数量")
    change_type: str = Field(..., description="变更类型")
    change_reason: Optional[str] = Field(None, description="变更原因")
    operator_id: int = Field(..., description="操作员ID")
    remarks: Optional[str] = Field(None, description="备注信息")


class InventoryChangeRecordCreate(InventoryChangeRecordBase):
    """创建库存变更记录"""
    pass


class InventoryChangeRecordResponse(InventoryChangeRecordBase):
    """库存变更记录响应模型"""
    id: int = Field(..., description="记录ID")
    department_name: str = Field(..., description="部门名称")
    item_name: str = Field(..., description="物品名称")
    item_code: str = Field(..., description="物品编码")
    operator_name: str = Field(..., description="操作员姓名")
    change_date: datetime = Field(..., description="变更日期")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class InventoryUsageStatisticsBase(BaseModel):
    """库存使用统计基础模型"""
    department_id: int = Field(..., description="部门ID")
    item_id: int = Field(..., description="物品ID")
    year: int = Field(..., description="统计年份")
    month: int = Field(..., description="统计月份")
    total_usage: Decimal = Field(0, description="总使用量")
    total_value: Decimal = Field(0, description="总使用价值")
    avg_unit_price: Decimal = Field(0, description="平均单价")


class InventoryUsageStatisticsResponse(InventoryUsageStatisticsBase):
    """库存使用统计响应模型"""
    id: int = Field(..., description="统计ID")
    department_name: str = Field(..., description="部门名称")
    item_name: str = Field(..., description="物品名称")
    item_code: str = Field(..., description="物品编码")
    inventory_unit: str = Field(..., description="库存单位")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class InventoryAlertBase(BaseModel):
    """库存预警基础模型"""
    department_id: int = Field(..., description="部门ID")
    item_id: int = Field(..., description="物品ID")
    alert_type: str = Field(..., description="预警类型")
    alert_level: str = Field("warning", description="预警级别")
    message: str = Field(..., description="预警消息")
    current_stock: Decimal = Field(..., description="当前库存")
    threshold_value: Decimal = Field(..., description="阈值")


class InventoryAlertCreate(InventoryAlertBase):
    """创建库存预警"""
    pass


class InventoryAlertUpdate(BaseModel):
    """更新库存预警"""
    is_resolved: Optional[bool] = Field(None, description="是否已解决")
    resolved_by: Optional[int] = Field(None, description="解决人ID")


class InventoryAlertResponse(InventoryAlertBase):
    """库存预警响应模型"""
    id: int = Field(..., description="预警ID")
    department_name: str = Field(..., description="部门名称")
    item_name: str = Field(..., description="物品名称")
    item_code: str = Field(..., description="物品编码")
    is_active: bool = Field(..., description="是否激活")
    is_resolved: bool = Field(..., description="是否已解决")
    resolved_by: Optional[int] = Field(None, description="解决人ID")
    resolved_at: Optional[datetime] = Field(None, description="解决时间")
    resolver_name: Optional[str] = Field(None, description="解决人姓名")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True





class ManualInRequest(BaseModel):
    """手工入库请求模型"""
    department_id: int = Field(..., description="部门ID")
    item_id: int = Field(..., description="物品ID")
    quantity: Decimal = Field(..., gt=0, description="入库数量")
    quantity_unit: Optional[str] = Field(None, description="数量单位：purchase(采购单位) 或 inventory(库存单位)，不传递时默认使用采购单位")
    unit_price: Optional[Decimal] = Field(None, ge=0, description="单价")
    storage_location: Optional[str] = Field(None, description="存储位置")
    rack_number: Optional[str] = Field(None, description="货架号")
    notes: Optional[str] = Field(None, description="备注")


class StockAdjustRequest(BaseModel):
    """库存调整请求模型"""
    department_id: int = Field(..., description="部门ID")
    item_id: int = Field(..., description="物品ID")
    quantity: Decimal = Field(..., description="调整数量，正数为增加，负数为减少")
    quantity_unit: Optional[str] = Field(None, description="数量单位：purchase(采购单位) 或 inventory(库存单位)，不传递时默认使用库存单位")
    adjustment_reason: str = Field(..., description="调整原因")
    storage_location: Optional[str] = Field(None, description="存储位置")
    rack_number: Optional[str] = Field(None, description="货架号")
    notes: Optional[str] = Field(None, description="备注")


class InventoryQueryParams(BaseModel):
    """库存查询参数"""
    department_id: Optional[int] = Field(None, description="部门ID")
    item_id: Optional[int] = Field(None, description="物品ID")
    status: Optional[str] = Field(None, description="库存状态")
    low_stock_only: bool = Field(False, description="仅显示低库存")
    out_of_stock_only: bool = Field(False, description="仅显示缺货")
    search: Optional[str] = Field(None, description="搜索关键词")


class InventoryChangeQueryParams(BaseModel):
    """库存变更查询参数"""
    department_id: Optional[int] = Field(None, description="部门ID")
    item_id: Optional[int] = Field(None, description="物品ID")
    change_type: Optional[str] = Field(None, description="变更类型")
    operator_id: Optional[int] = Field(None, description="操作员ID")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")


class InventoryUsageStatisticsQueryParams(BaseModel):
    """库存使用统计查询参数"""
    department_id: Optional[int] = Field(None, description="部门ID")
    item_id: Optional[int] = Field(None, description="物品ID")
    year: Optional[int] = Field(None, description="年份")
    month: Optional[int] = Field(None, description="月份")


class InventoryAlertQueryParams(BaseModel):
    """库存预警查询参数"""
    department_id: Optional[int] = Field(None, description="部门ID")
    item_id: Optional[int] = Field(None, description="物品ID")
    alert_type: Optional[str] = Field(None, description="预警类型")
    alert_level: Optional[str] = Field(None, description="预警级别")
    is_resolved: Optional[bool] = Field(None, description="是否已解决")
    is_active: Optional[bool] = Field(None, description="是否激活")


class InventoryOverview(BaseModel):
    """库存概览"""
    total_items: int = Field(..., description="库存总数")
    total_departments: int = Field(..., description="部门总数")
    low_stock_items: int = Field(..., description="低库存数量")
    out_of_stock_items: int = Field(..., description="缺货数量")
    overstock_items: int = Field(..., description="超储数量")
    normal_stock_items: int = Field(..., description="正常库存数量")
    total_value: Decimal = Field(..., description="库存总价值")
    total_value_usd: Decimal = Field(..., description="库存总价值(USD)")
    active_alerts: int = Field(..., description="活跃预警数")


class InventorySummary(BaseModel):
    """库存汇总"""
    department_id: int = Field(..., description="部门ID")
    department_name: str = Field(..., description="部门名称")
    item_count: int = Field(..., description="物品数量")
    total_value: Decimal = Field(..., description="库存总价值")
    low_stock_count: int = Field(..., description="低库存物品数")
    out_of_stock_count: int = Field(..., description="缺货物品数")
