from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import date, datetime
from decimal import Decimal


class CreateRequestRequest(BaseModel):
    """创建采购申请请求"""
    department_id: int = Field(..., description="部门ID")
    cart_item_ids: List[int] = Field(..., description="购物车项目ID列表")
    notes: Optional[str] = Field(None, description="备注")


class UpdateRequestRequest(BaseModel):
    """更新采购申请请求"""
    notes: Optional[str] = Field(None, description="备注")
    items_to_add: Optional[List[dict]] = Field(None, description="要添加的物品列表")
    items_to_delete: Optional[List[int]] = Field(None, description="要删除的物品ID列表")
    items_to_update: Optional[List[dict]] = Field(None, description="要更新的物品列表")


class ExchangeRateInfo(BaseModel):
    """汇率信息"""
    currency_code: str
    original_unit_price: float
    original_total_price: float
    exchange_rate: float
    usd_unit_price: float
    usd_total_price: float
    rate_type: str  # 'current_month' or 'historical'
    effective_month: str
    is_valid: bool
    warning: Optional[str] = None
    error: Optional[str] = None


class SupplierInfo(BaseModel):
    """供应商信息"""
    id: int
    supplier_id: int
    item_id: int
    priority: int
    status: str
    supplier_name: str


class RequestItemWithDetails(BaseModel):
    """申请明细详细信息（包含扩展信息）"""
    # 基础信息
    id: int
    request_id: int
    item_id: int
    item_code: str
    item_name: str
    spq_quantity: Decimal
    spq_count: int
    spq_unit: str
    
    # 最终价格（审批后锁定）
    final_unit_price: Optional[Decimal] = None
    final_total_price: Optional[Decimal] = None
    final_supplier_id: Optional[int] = None
    price_locked_at: Optional[datetime] = None
    
    # 预估价格（申请阶段）
    estimated_unit_price: Optional[float] = None
    estimated_total_price: Optional[float] = None
    
    # 扩展信息
    item_image_url: Optional[str] = None
    exchange_rate_info: Optional[ExchangeRateInfo] = None
    preferred_supplier: Optional[Dict[str, Any]] = None
    available_suppliers: Optional[List[Dict[str, Any]]] = None
    
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class RequestItemResponse(BaseModel):
    """申请明细响应"""
    id: int
    request_id: int
    item_id: int
    item_code: str
    item_name: str
    item_image_url: Optional[str] = None  # 动态添加的图片URL
    spq_quantity: Decimal
    spq_count: int
    spq_unit: str
    
    # 最终价格（审批后锁定）
    final_unit_price: Optional[Decimal]
    final_total_price: Optional[Decimal]
    final_supplier_id: Optional[int]
    
    # 锁定的汇率信息（按阶段锁定）
    locked_currency_code: Optional[str] = None
    locked_exchange_rate: Optional[Decimal] = None
    locked_exchange_rate_month: Optional[date] = None
    locked_exchange_rate_stage: Optional[str] = None
    locked_exchange_rate_at: Optional[datetime] = None
    
    # 预估价格（申请阶段）
    estimated_unit_price: Optional[Decimal] = None
    estimated_total_price: Optional[Decimal] = None
    
    # 汇率信息（包含供应商原始货币和价格）
    exchange_rate_info: Optional[ExchangeRateInfo] = None
    
    # 供应商信息（动态添加）
    preferred_supplier: Optional[Dict[str, Any]] = None
    available_suppliers: Optional[List[Dict[str, Any]]] = None
    
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class RequestFlowHistoryResponse(BaseModel):
    """申请流转历史响应"""
    id: int
    request_id: int
    action: str
    from_status: Optional[str] = None
    to_status: str
    operator_id: int
    operator_name: str
    approval_level: Optional[str] = None
    comments: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True


class PurchaseRequestWithDetails(BaseModel):
    """采购申请详细信息（包含扩展信息）"""
    # 基础信息
    id: int
    request_no: str
    request_uuid: str
    department_id: int
    submitter_id: int
    status: str
    final_total: Optional[Decimal] = None
    notes: Optional[str] = None
    qr_code: Optional[str] = None
    submitted_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    # 扩展信息
    department_name: Optional[str] = None
    submitter_name: Optional[str] = None
    
    # 关联数据
    items: List[RequestItemWithDetails] = []
    flow_history: List[RequestFlowHistoryResponse] = []

    class Config:
        from_attributes = True


class PurchaseRequestResponse(BaseModel):
    """采购申请响应"""
    id: int
    request_no: str
    request_uuid: str
    department_id: int
    submitter_id: int
    # 添加名称字段
    department_name: Optional[str] = None
    submitter_name: Optional[str] = None
    status: str
    final_total: Optional[Decimal]
    notes: Optional[str]
    qr_code: Optional[str]
    submitted_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    items: List[RequestItemResponse] = []
    # 添加流转历史
    flow_history: List[RequestFlowHistoryResponse] = []

    class Config:
        from_attributes = True


class RequestFilters(BaseModel):
    """申请过滤条件"""
    status: Optional[str] = Field(None, description="申请状态")
    department_id: Optional[int] = Field(None, description="部门ID")
    submitter_id: Optional[int] = Field(None, description="提交人ID")
    priority: Optional[str] = Field(None, description="优先级")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")


class StatusUpdateRequest(BaseModel):
    """状态更新请求"""
    new_status: Optional[str] = Field(None, description="新状态")
    action: Optional[str] = Field(None, description="审批操作: approve, reject, return")
    comments: Optional[str] = Field(None, description="备注说明或审批意见")


class WithdrawRequest(BaseModel):
    """撤回申请请求"""
    reason: Optional[str] = Field(None, description="撤回原因")


class RequestSummaryResponse(BaseModel):
    """申请汇总响应"""
    total_requests: int
    pending_submission: int
    under_review: int
    under_principle_approval: int
    under_final_approval: int
    approved: int
    executed: int
    rejected: int


# 新增汇总分析相关的Schema
class SummaryAnalysisRequest(BaseModel):
    """汇总分析请求"""
    request_ids: List[int] = Field(..., description="要汇总的申请ID列表", min_items=1, max_items=1000)
    analysis_name: Optional[str] = Field(None, description="汇总分析名称")

    class Config:
        from_attributes = True


class ItemSummaryResponse(BaseModel):
    """物品汇总响应"""
    item_id: int
    item_code: str
    item_name: str
    category_name: Optional[str] = None
    image_url: Optional[str] = None
    preferred_supplier_id: Optional[int] = None
    preferred_supplier_name: Optional[str] = None
    total_spq_quantity: Decimal
    total_spq_count: int
    spq_unit: str
    single_spq_quantity: Decimal  # 单个物品的SPQ数量
    single_spq_count: int  # 单个物品的SPQ包装数量
    total_amount: Decimal
    request_count: int
    request_ids: List[str]  # 申请单ID列表
    average_price: Decimal

    class Config:
        from_attributes = True


class DepartmentSummaryResponse(BaseModel):
    """部门汇总响应"""
    department_id: int
    department_name: str
    request_count: int
    total_amount: Decimal
    percentage: Decimal

    class Config:
        from_attributes = True


class SupplierSummaryResponse(BaseModel):
    """供应商汇总响应"""
    supplier_id: int
    supplier_name: str
    item_count: int
    total_amount: Decimal
    average_price: Decimal
    percentage: Decimal

    class Config:
        from_attributes = True


class RequestSummaryAnalysisResponse(BaseModel):
    """采购申请汇总分析响应"""
    # 汇总统计
    total_requests: int
    total_departments: int
    total_amount: Decimal
    analysis_time: str
    
    # 物品汇总
    item_summaries: List[ItemSummaryResponse]
    
    # 部门汇总
    department_summaries: List[DepartmentSummaryResponse]
    
    # 供应商汇总
    supplier_summaries: List[SupplierSummaryResponse]
    
    # 申请状态分布
    status_distribution: Dict[str, int]
    
    # 申请单列表
    requests: List[Dict[str, Any]]

    class Config:
        from_attributes = True


# 汇率记录相关模式 - 已移动到 exchange_rate.py 中
from app.schemas.exchange_rate import PurchaseRequestExchangeRate, PurchaseRequestExchangeRateCreate


class PurchaseRequestWithExchangeRates(PurchaseRequestResponse):
    """包含汇率记录的采购申请响应"""
    exchange_rates: List[PurchaseRequestExchangeRate] = []
