from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal


class ScanQRCodeRequest(BaseModel):
    """扫描二维码请求"""
    qr_code: str = Field(..., description="二维码内容")


class InboundItemInfo(BaseModel):
    """入库物品信息"""
    id: int
    item_id: int
    item_code: str
    item_name: str
    spq_quantity: Decimal
    spq_count: int
    spq_unit: str
    requested_quantity: Decimal  # 申请数量（SPQ数量 * SPQ个数）
    notes: Optional[str] = None

    class Config:
        from_attributes = True


class ScanQRCodeResponse(BaseModel):
    """扫描二维码响应"""
    success: bool
    message: str
    purchase_request: Optional[Dict[str, Any]] = None
    items: List[InboundItemInfo] = []


class InboundItemRequest(BaseModel):
    """入库物品请求"""
    item_id: int
    inbound_quantity: Decimal = Field(..., gt=0, description="入库数量")
    notes: Optional[str] = Field(None, description="备注")


class InboundRequest(BaseModel):
    """入库请求"""
    purchase_request_id: int
    department_id: int
    items: List[InboundItemRequest]
    notes: Optional[str] = Field(None, description="备注")


class InboundItemResult(BaseModel):
    """入库物品结果"""
    item_id: int
    item_code: str
    item_name: str
    inbound_quantity: Decimal
    before_quantity: Decimal
    after_quantity: Decimal
    success: bool
    message: str


class InboundResponse(BaseModel):
    """入库响应"""
    success: bool
    message: str
    inbound_id: Optional[str] = None
    results: List[InboundItemResult] = []
    total_items: int
    success_items: int
    failed_items: int
    created_at: datetime
