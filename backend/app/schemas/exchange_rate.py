from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List
from decimal import Decimal
from datetime import datetime, date


class ExchangeRateBase(BaseModel):
    """汇率基础信息"""
    currency_code: str = Field(..., description="货币代码 (CNY, EUR, JPY等)")
    rate: Decimal = Field(..., gt=0, description="汇率值 (1 USD = rate 外币)")
    effective_month: date = Field(..., description="生效月份 (格式: YYYY-MM-01)")
    status: str = Field("active", description="状态 (active/inactive)")


class ExchangeRateCreate(ExchangeRateBase):
    """创建汇率"""
    pass


class ExchangeRateUpdate(BaseModel):
    """更新汇率"""
    rate: Optional[Decimal] = Field(None, gt=0, description="汇率值")
    status: Optional[str] = Field(None, description="状态")
    change_reason: str = Field(..., description="修改原因")


class ExchangeRate(ExchangeRateBase):
    """汇率响应"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None
    updated_by: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)


class ExchangeRateLogBase(BaseModel):
    """汇率修改日志基础信息"""
    exchange_rate_id: int = Field(..., description="汇率记录ID")
    old_rate: Decimal = Field(..., description="修改前的汇率值")
    new_rate: Decimal = Field(..., description="修改后的汇率值")
    change_reason: str = Field(..., description="修改原因")
    changed_by: int = Field(..., description="修改人ID")


class ExchangeRateLogCreate(ExchangeRateLogBase):
    """创建汇率修改日志"""
    pass


class ExchangeRateLog(ExchangeRateLogBase):
    """汇率修改日志响应"""
    id: int
    changed_at: datetime

    model_config = ConfigDict(from_attributes=True)


class PurchaseRequestExchangeRateBase(BaseModel):
    """采购申请汇率记录基础信息"""
    purchase_request_id: int = Field(..., description="采购申请ID")
    stage: str = Field(..., description="阶段 (submitted, principle_approved)")
    currency_code: str = Field(..., description="货币代码")
    rate: Decimal = Field(..., gt=0, description="汇率值")
    recorded_by: int = Field(..., description="记录人ID")


class PurchaseRequestExchangeRateCreate(PurchaseRequestExchangeRateBase):
    """创建采购申请汇率记录"""
    pass


class PurchaseRequestExchangeRate(PurchaseRequestExchangeRateBase):
    """采购申请汇率记录响应"""
    id: int
    recorded_at: datetime

    model_config = ConfigDict(from_attributes=True)


class ExchangeRateWithLogs(ExchangeRate):
    """汇率响应（包含修改日志）"""
    logs: List[ExchangeRateLog] = []


class ExchangeRateQuery(BaseModel):
    """汇率查询参数"""
    currency_code: Optional[str] = Field(None, description="货币代码")
    effective_month: Optional[date] = Field(None, description="生效月份")
    status: Optional[str] = Field(None, description="状态")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页大小")


class ExchangeRateHistoryQuery(BaseModel):
    """汇率历史查询参数"""
    currency_code: str = Field(..., description="货币代码")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")
    days: int = Field(180, ge=1, le=365, description="查询天数（默认180天）")


class CurrencyInfo(BaseModel):
    """货币信息"""
    code: str = Field(..., description="货币代码")
    name: str = Field(..., description="货币名称")
    symbol: str = Field(..., description="货币符号")
    is_base: bool = Field(False, description="是否为基础货币（USD）")


class ExchangeRateSummary(BaseModel):
    """汇率汇总信息"""
    currency_code: str = Field(..., description="货币代码")
    current_rate: Optional[Decimal] = Field(None, description="当前汇率")
    last_updated: Optional[datetime] = Field(None, description="最后更新时间")
    status: str = Field(..., description="状态")
    is_valid: bool = Field(..., description="是否有效")
