from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class ReviewRequest(BaseModel):
    """部门经理复核请求"""
    approved: bool = Field(..., description="是否通过")
    comments: Optional[str] = Field(None, description="审批意见")


class ApprovalRequest(BaseModel):
    """审批请求"""
    approved: bool = Field(..., description="是否通过")
    comments: Optional[str] = Field(None, description="审批意见")


class RejectRequest(BaseModel):
    """拒绝请求"""
    comments: str = Field(..., description="拒绝原因")


class ReturnRequest(BaseModel):
    """退回请求"""
    comments: Optional[str] = Field(None, description="退回原因")


class RequestFlowHistoryResponse(BaseModel):
    """申请流转历史响应"""
    id: int
    request_id: int
    action: str
    from_status: Optional[str] = None
    to_status: str
    operator_id: int
    operator_name: str
    approval_level: Optional[str] = None
    comments: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True


class ApprovalSummaryResponse(BaseModel):
    """审批汇总响应"""
    total_pending: int
    under_review: int
    under_principle_approval: int
    under_final_approval: int


class BatchApprovalRequest(BaseModel):
    """批量审批请求"""
    request_ids: List[int]
    action: str  # "approve" 或 "reject"
    comments: str
    approval_level: str  # "review", "principle_approval", "final_approval"

class FailedApprovalInfo(BaseModel):
    """审批失败信息"""
    request_id: int
    request_no: str
    reason: str

class BatchApprovalResponse(BaseModel):
    """批量审批响应"""
    success_count: int
    failure_count: int
    total_count: int
    failed_requests: List[FailedApprovalInfo]
    message: str
