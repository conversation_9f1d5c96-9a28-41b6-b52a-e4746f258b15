"""
客户端相关的Pydantic数据模型
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal


# 认证相关模型
class ClientLoginRequest(BaseModel):
    """客户端登录请求"""
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class ClientUserInfo(BaseModel):
    """客户端用户信息"""
    user_id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    full_name: str = Field(..., description="全名")
    department_id: int = Field(..., description="部门ID")
    department_name: Optional[str] = Field(None, description="部门名称")
    permissions: List[Dict[str, Any]] = Field(..., description="权限列表")


class ClientLoginResponse(BaseModel):
    """客户端登录响应"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(..., description="令牌类型")
    user_info: ClientUserInfo = Field(..., description="用户信息")


# 物品扫描相关模型
class ItemScanRequest(BaseModel):
    """物品扫描请求"""
    qr_code: str = Field(..., description="物品二维码内容")


class ItemInfo(BaseModel):
    """物品信息"""
    item_id: int = Field(..., description="物品ID")
    item_name: str = Field(..., description="物品名称")
    item_code: str = Field(..., description="物品编码")
    category_name: Optional[str] = Field(None, description="分类名称")
    brand: Optional[str] = Field(None, description="品牌")
    spec_material: Optional[str] = Field(None, description="规格/材质")
    size_dimension: Optional[str] = Field(None, description="尺寸/规格")
    purchase_unit: str = Field(..., description="采购单位")
    inventory_unit: str = Field(..., description="库存单位")
    qty_per_up: int = Field(..., description="每采购单位包含的库存单位数量")
    available_stock: float = Field(..., description="可用库存")
    min_quantity: float = Field(..., description="最小库存量")
    image_url: Optional[str] = Field(None, description="物品图片URL")


class ItemScanResponse(BaseModel):
    """物品扫描响应"""
    success: bool = Field(..., description="是否成功")
    item_info: Optional[ItemInfo] = Field(None, description="物品信息")
    message: str = Field(..., description="响应消息")


# 员工验证相关模型
class EmployeeScanRequest(BaseModel):
    """员工扫描请求"""
    qr_code: str = Field(..., description="员工工卡二维码内容")


class EmployeeInfo(BaseModel):
    """员工信息"""
    employee_id: int = Field(..., description="员工ID")
    employee_code: str = Field(..., description="员工工号")
    employee_name: str = Field(..., description="员工姓名")
    department_id: int = Field(..., description="部门ID")
    department_name: Optional[str] = Field(None, description="部门名称")
    position: Optional[str] = Field(None, description="职位")


class EmployeeScanResponse(BaseModel):
    """员工扫描响应"""
    success: bool = Field(..., description="是否成功")
    employee_info: Optional[EmployeeInfo] = Field(None, description="员工信息")
    message: str = Field(..., description="响应消息")


# 物品领取相关模型
class PickupRequest(BaseModel):
    """物品领取请求"""
    item_id: int = Field(..., description="物品ID")
    employee_id: int = Field(..., description="员工ID")
    quantity: float = Field(..., description="领取数量")
    unit: str = Field(..., description="领取单位")
    usage_purpose: Optional[str] = Field(None, description="使用目的")
    item_qr_code: Optional[str] = Field(None, description="物品二维码")
    user_card_code: Optional[str] = Field(None, description="工卡二维码")
    location: Optional[str] = Field(None, description="领取地点")
    device_info: Optional[str] = Field(None, description="设备信息")
    notes: Optional[str] = Field(None, description="备注")


class InventoryChangeInfo(BaseModel):
    """库存变更信息"""
    quantity_before: float = Field(..., description="变更前数量")
    quantity_after: float = Field(..., description="变更后数量")
    change_type: str = Field(..., description="变更类型")


class PickupResponse(BaseModel):
    """物品领取响应"""
    success: bool = Field(..., description="是否成功")
    record_id: Optional[str] = Field(None, description="记录ID")
    message: str = Field(..., description="响应消息")
    inventory_change: Optional[InventoryChangeInfo] = Field(None, description="库存变更信息")


class UndoPickupRequest(BaseModel):
    """撤销领取请求"""
    record_id: str = Field(..., description="领取记录ID")


class UndoPickupResponse(BaseModel):
    """撤销领取响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    inventory_change: Optional[InventoryChangeInfo] = Field(None, description="库存变更信息")


# 历史记录相关模型
class PickupHistoryResponse(BaseModel):
    """领取历史响应"""
    record_id: str = Field(..., description="记录ID")
    item_name: Optional[str] = Field(None, description="物品名称")
    item_code: Optional[str] = Field(None, description="物品编码")
    employee_name: Optional[str] = Field(None, description="员工姓名")
    quantity: float = Field(..., description="领取数量")
    unit: str = Field(..., description="单位")
    usage_time: datetime = Field(..., description="使用时间")
    location: Optional[str] = Field(None, description="领取地点")
    notes: Optional[str] = Field(None, description="备注")
    status: str = Field(..., description="记录状态")
