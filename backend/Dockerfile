# 使用Python 3.12官方镜像
FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    default-libmysqlclient-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装Poetry
RUN pip install poetry

# 配置Poetry不创建虚拟环境
ENV POETRY_NO_VIRTUALENV=1

# 复制Poetry配置文件
COPY pyproject.toml poetry.lock ./

# 安装依赖（只安装主依赖，不安装开发依赖）
RUN poetry install --only main

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p uploads/images uploads/temp

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["poetry", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
