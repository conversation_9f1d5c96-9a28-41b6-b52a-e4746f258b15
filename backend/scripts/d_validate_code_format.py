#!/usr/bin/env python3
"""
编码格式验证脚本
验证系统中的各类编码是否符合新的编码格式标准
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.item import Item
from app.models.user import User, Department
from app.models.supplier import Supplier
from app.models.purchase import PurchaseRequest
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def validate_item_codes(session):
    """验证物品编码格式"""
    print("🔍 验证物品编码格式...")
    
    # 查找不符合IDM前缀的物品
    invalid_items = session.query(Item).filter(
        ~Item.code.like('IDM%'),
        Item.is_active == True
    ).all()
    
    if invalid_items:
        print(f"❌ 发现 {len(invalid_items)} 个不符合格式的物品编码:")
        for item in invalid_items:
            print(f"  - {item.code} (物品: {item.name})")
        return False
    else:
        print("✅ 所有物品编码都符合IDM前缀格式")
        return True

def validate_department_codes(session):
    """验证部门编码格式"""
    print("🔍 验证部门编码格式...")
    
    # 查找不符合DEPT前缀的部门
    invalid_departments = session.query(Department).filter(
        ~Department.code.like('DEPT%'),
        Department.is_active == True
    ).all()
    
    if invalid_departments:
        print(f"❌ 发现 {len(invalid_departments)} 个不符合格式的部门编码:")
        for dept in invalid_departments:
            print(f"  - {dept.code} (部门: {dept.name})")
        return False
    else:
        print("✅ 所有部门编码都符合DEPT前缀格式")
        return True

def validate_employee_codes(session):
    """验证员工工号格式"""
    print("🔍 验证员工工号格式...")
    
    # 查找不符合EMP前缀的员工
    invalid_employees = session.query(User).filter(
        ~User.employee_id.like('EMP%'),
        User.is_active == True,
        User.employee_id.isnot(None)
    ).all()
    
    if invalid_employees:
        print(f"❌ 发现 {len(invalid_employees)} 个不符合格式的员工工号:")
        for emp in invalid_employees:
            print(f"  - {emp.employee_id} (员工: {emp.username})")
        return False
    else:
        print("✅ 所有员工工号都符合EMP前缀格式")
        return True

def validate_supplier_codes(session):
    """验证供应商编码格式"""
    print("🔍 验证供应商编码格式...")
    
    # 查找不符合SUP前缀的供应商
    invalid_suppliers = session.query(Supplier).filter(
        ~Supplier.code.like('SUP%'),
        Supplier.status == "active"
    ).all()
    
    if invalid_suppliers:
        print(f"❌ 发现 {len(invalid_suppliers)} 个不符合格式的供应商编码:")
        for supplier in invalid_suppliers:
            print(f"  - {supplier.code} (供应商: {supplier.name_cn or supplier.name_en})")
        return False
    else:
        print("✅ 所有供应商编码都符合SUP前缀格式")
        return True

def validate_purchase_request_codes(session):
    """验证采购申请号格式"""
    print("🔍 验证采购申请号格式...")
    
    # 查找不符合PR前缀的采购申请
    invalid_requests = session.query(PurchaseRequest).filter(
        ~PurchaseRequest.request_no.like('PR%')
    ).all()
    
    if invalid_requests:
        print(f"❌ 发现 {len(invalid_requests)} 个不符合格式的采购申请号:")
        for req in invalid_requests:
            print(f"  - {req.request_no}")
        return False
    else:
        print("✅ 所有采购申请号都符合PR前缀格式")
        return True

def get_code_statistics(session):
    """获取编码统计信息"""
    print("\n📊 编码格式统计信息:")
    
    # 物品编码统计
    total_items = session.query(Item).filter(Item.is_active == True).count()
    idm_items = session.query(Item).filter(Item.code.like('IDM%')).count()
    print(f"  - 物品编码: {idm_items}/{total_items} 符合IDM格式")
    
    # 部门编码统计
    total_depts = session.query(Department).filter(Department.is_active == True).count()
    dept_codes = session.query(Department).filter(Department.code.like('DEPT%')).count()
    print(f"  - 部门编码: {dept_codes}/{total_depts} 符合DEPT格式")
    
    # 员工工号统计
    total_emps = session.query(User).filter(User.is_active == True, User.employee_id.isnot(None)).count()
    emp_codes = session.query(User).filter(User.employee_id.like('EMP%')).count()
    print(f"  - 员工工号: {emp_codes}/{total_emps} 符合EMP格式")
    
    # 供应商编码统计
    total_suppliers = session.query(Supplier).filter(Supplier.status == "active").count()
    sup_codes = session.query(Supplier).filter(Supplier.code.like('SUP%')).count()
    print(f"  - 供应商编码: {sup_codes}/{total_suppliers} 符合SUP格式")
    
    # 采购申请号统计
    total_requests = session.query(PurchaseRequest).count()
    pr_codes = session.query(PurchaseRequest).filter(PurchaseRequest.request_no.like('PR%')).count()
    print(f"  - 采购申请号: {pr_codes}/{total_requests} 符合PR格式")

def main():
    """主函数"""
    try:
        logger.info("开始验证编码格式...")
        
        # 创建数据库连接
        engine = create_engine(settings.DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        session = SessionLocal()
        
        try:
            # 验证各类编码格式
            item_valid = validate_item_codes(session)
            dept_valid = validate_department_codes(session)
            emp_valid = validate_employee_codes(session)
            supplier_valid = validate_supplier_codes(session)
            request_valid = validate_purchase_request_codes(session)
            
            # 获取统计信息
            get_code_statistics(session)
            
            # 总结验证结果
            print("\n" + "=" * 50)
            if all([item_valid, dept_valid, emp_valid, supplier_valid, request_valid]):
                print("🎉 所有编码格式验证通过！")
                print("✅ 系统已完全符合新的编码格式标准")
            else:
                print("⚠️  编码格式验证未完全通过")
                print("❌ 请运行 update_qr_code_format.py 脚本更新编码格式")
            print("=" * 50)
            
        finally:
            session.close()
            
    except Exception as e:
        logger.error(f"验证失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
