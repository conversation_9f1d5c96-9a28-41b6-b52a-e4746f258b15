#!/usr/bin/env python3
"""
从Excel文件导入供应商、物品分类和物品数据（支持供应商关联）
"""

import sys
import csv
import random
import os
import re
import logging
from pathlib import Path
from decimal import Decimal
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.database import engine, SessionLocal
from app.models.item import ItemPrimaryCategory, ItemCategory, Item
from app.models.supplier import Supplier, ItemSupplier, SupplierPrice

USE_DEFAULT_IMAGE = True

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 静态资源图片目录 - 使用后端静态资源目录
STATIC_IMAGES_DIR = Path(__file__).parent.parent / "static" / "sample-images" / "items"

# 全局缓存可用图片列表
_available_images_cache = None

def get_available_images():
    """获取可用的图片文件列表"""
    global _available_images_cache
    
    # 如果缓存存在，直接返回
    if _available_images_cache is not None:
        return _available_images_cache
    
    if not STATIC_IMAGES_DIR.exists():
        _available_images_cache = []
        return _available_images_cache
    
    image_files = []
    for file_path in STATIC_IMAGES_DIR.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
            # 返回相对于后端静态资源根目录的路径
            relative_path = f"/static/sample-images/items/{file_path.name}"
            image_files.append(relative_path)
    
    _available_images_cache = image_files
    return image_files

def get_random_image_url():
    """从静态资源中随机选择一张图片"""
    available_images = get_available_images()
    if USE_DEFAULT_IMAGE or not available_images:
        # 如果没有图片，返回默认图片路径
        return "/static/sample-images/items/no-image-icon.png"
    
    return random.choice(available_images)

def clear_existing_data(db_session):
    """清空现有的供应商、分类和物品数据"""
    try:
        # 删除价格记录
        db_session.query(SupplierPrice).delete()
        
        # 删除物品供应商关联
        db_session.query(ItemSupplier).delete()
        
        # 删除物品
        db_session.query(Item).delete()
        
        # 删除二级分类
        db_session.query(ItemCategory).delete()
        
        # 删除一级分类
        db_session.query(ItemPrimaryCategory).delete()
        
        # 删除供应商
        db_session.query(Supplier).delete()
        
        db_session.commit()
        
    except Exception as e:
        logger.error(f"清空数据时发生错误: {e}")
        db_session.rollback()
        raise

def import_suppliers(db_session, csv_file_path):
    """导入供应商数据"""
    imported_count = 0
    suppliers_dict = {}
    skipped_count = 0
    error_count = 0
    
    # 统计信息
    stats = {
        'total_rows': 0,
        'approved_count': 0,
        'locked_count': 0,
        'other_status_count': 0,
        'no_code_count': 0,
        'no_name_count': 0,
        'duplicate_count': 0,
        'error_count': 0
    }
    
    with open(csv_file_path, 'r', encoding='utf-8-sig') as file:  # 使用utf-8-sig来处理BOM
        reader = csv.DictReader(file)
        for row_num, row in enumerate(reader, 1):
            stats['total_rows'] += 1
            
            try:
                # 检查供应商状态
                status = row.get('Status', '').strip().replace('\n', '').replace('\r', '')
                
                # 提取供应商信息
                vendor_code = row.get('Vendor Code', '').strip()
                if not vendor_code:
                    stats['no_code_count'] += 1
                    skipped_count += 1
                    continue
                
                name_cn = row.get('Registered CN Name', '').strip()
                name_en = row.get('Registered Eng Name', '').strip()
                
                # 至少需要一个名称
                if not name_cn and not name_en:
                    stats['no_name_count'] += 1
                    skipped_count += 1
                    continue
                
                # 检查是否已存在
                existing_supplier = db_session.query(Supplier).filter(Supplier.code == vendor_code).first()
                if existing_supplier:
                    suppliers_dict[vendor_code] = existing_supplier
                    stats['duplicate_count'] += 1
                    continue
                
                # 根据状态设置供应商状态
                supplier_status = 'active'
                if status == '1-Approved':
                    supplier_status = 'active'
                    stats['approved_count'] += 1
                elif status == '3-Locked':
                    supplier_status = 'locked'
                    stats['locked_count'] += 1
                else:
                    supplier_status = 'inactive'
                    stats['other_status_count'] += 1
                
                # 创建供应商
                supplier = Supplier(
                    code=vendor_code,
                    name_cn=name_cn if name_cn else None,
                    name_en=name_en if name_en else None,
                    company_address=row.get('Country', '').strip(),
                    contact_person=row.get('Contact Name', '').strip(),
                    phone=row.get('Telephone', '').strip(),
                    email=row.get('E-Mail', '').strip(),
                    rating=3,  # 默认评级
                    status=supplier_status
                )
                
                db_session.add(supplier)
                suppliers_dict[vendor_code] = supplier
                imported_count += 1
                
            except Exception as e:
                logger.error(f"导入供应商失败 (行 {row_num}): {e}")
                error_count += 1
                stats['error_count'] += 1
                skipped_count += 1
                continue
    
    try:
        db_session.commit()
        logger.info(f"✅ 供应商导入完成: {imported_count} 个成功, {skipped_count} 个跳过, {error_count} 个错误")
        
    except Exception as e:
        logger.error(f"提交供应商数据时发生错误: {e}")
        db_session.rollback()
        raise
    
    return suppliers_dict

def find_supplier_by_name(suppliers_dict, supplier_name, db_session=None):
    """通过供应商名称模糊匹配查找供应商，如果找不到则创建新供应商"""
    if not supplier_name:
        return None
    
    supplier_name = supplier_name.strip()
    if not supplier_name:
        return None
    
    # 首先尝试精确匹配
    for supplier in suppliers_dict.values():
        if (supplier.name_cn and supplier.name_cn.strip() == supplier_name) or \
           (supplier.name_en and supplier.name_en.strip() == supplier_name):
            return supplier
    
    # 然后尝试模糊匹配（包含匹配）
    for supplier in suppliers_dict.values():
        if supplier.name_cn and supplier_name.lower() in supplier.name_cn.lower():
            return supplier
        if supplier.name_en and supplier_name.lower() in supplier.name_en.lower():
            return supplier
    
    # 尝试字符序列匹配（新的匹配算法）
    for supplier in suppliers_dict.values():
        if supplier.name_en and is_character_sequence_match(supplier_name, supplier.name_en):
            return supplier
        if supplier.name_cn and is_character_sequence_match(supplier_name, supplier.name_cn):
            return supplier
    
    # 尝试简写匹配（处理缩写情况）
    for supplier in suppliers_dict.values():
        if supplier.name_en:
            # 检查是否是首字母缩写
            if is_acronym_match(supplier_name, supplier.name_en):
                return supplier
            
            # 检查是否是常见简写
            if is_abbreviation_match(supplier_name, supplier.name_en):
                return supplier
    
    # 如果找不到匹配的供应商，且提供了数据库会话，则创建新供应商
    if db_session:
        new_supplier = create_new_supplier(supplier_name, db_session)
        if new_supplier:
            suppliers_dict[new_supplier.code] = new_supplier
            return new_supplier
    
    return None

def is_character_sequence_match(short_name, full_name):
    """检查字符序列匹配：从短名称的第一个字符开始，依次在长名称中查找后续字符"""
    if not short_name or not full_name:
        return False
    
    short_name = short_name.strip().upper()
    full_name = full_name.strip().upper()
    
    # 如果短名称长度小于2，不进行序列匹配
    if len(short_name) < 2:
        return False
    
    # 从短名称的第一个字符开始
    short_index = 0
    full_index = 0
    
    while short_index < len(short_name) and full_index < len(full_name):
        # 查找当前短名称字符在长名称中的位置
        char_found = False
        for i in range(full_index, len(full_name)):
            if full_name[i] == short_name[short_index]:
                # 找到匹配字符，移动到下一个位置
                full_index = i + 1
                short_index += 1
                char_found = True
                break
        
        if not char_found:
            # 当前字符在剩余部分找不到，匹配失败
            return False
    
    # 如果所有短名称字符都找到了，则匹配成功
    return short_index == len(short_name)

def is_acronym_match(short_name, full_name):
    """检查是否是首字母缩写匹配"""
    if not short_name or not full_name:
        return False
    
    short_name = short_name.strip().upper()
    full_name = full_name.strip().upper()
    
    # 如果短名称长度小于2，不可能是缩写
    if len(short_name) < 2:
        return False
    
    # 提取全名中每个单词的首字母
    words = full_name.split()
    if len(words) < 2:
        return False
    
    acronym = ''.join([word[0] for word in words if word])
    
    # 检查是否匹配
    return short_name == acronym

def is_abbreviation_match(short_name, full_name):
    """检查是否是常见简写匹配"""
    if not short_name or not full_name:
        return False
    
    short_name = short_name.strip().upper()
    full_name = full_name.strip().upper()
    
    # 如果短名称长度小于2，不可能是简写
    if len(short_name) < 2:
        return False
    
    # 特殊匹配规则
    special_matches = {
        'OMEGA TECHNOLOGIES': 'OMEGA ENGINEERING PTE LTD',
        'TIPTAPE': 'TAPES & INSULATED PRODUCTS PTE LTD',
        'Victory Hardware': 'INSULATED PRODUCTS PTE LTD',
    }
    
    # 检查特殊匹配
    if short_name in special_matches and full_name == special_matches[short_name]:
        return True
    
    # 检查是否是连续字符匹配（如 "YRT" 匹配 "YEAR ROUND TECHNOLOGY"）
    if len(short_name) >= 3:
        # 提取全名中每个单词的首字母或前几个字母
        words = full_name.split()
        if len(words) >= len(short_name):
            # 尝试匹配连续字符
            for i in range(len(words) - len(short_name) + 1):
                match_found = True
                for j, char in enumerate(short_name):
                    if i + j < len(words) and not words[i + j].startswith(char):
                        match_found = False
                        break
                if match_found:
                    return True
    
    # 检查是否是部分单词匹配（如 "TECH" 匹配 "TECHNOLOGY"）
    words = full_name.split()
    for word in words:
        if short_name in word or word.startswith(short_name):
            return True
    
    return False

def analyze_excel_data(excel_file_path):
    """分析Excel文件数据，提取分类信息"""
    try:
        import pandas as pd
        
        # 读取Part List sheet
        df_part_list = pd.read_excel(excel_file_path, sheet_name='Part List', header=1)
        
        primary_categories = set()
        categories = set()
        total_rows = len(df_part_list)
        
        for _, row in df_part_list.iterrows():
            # 提取一级分类
            primary_cat = row.get('Category', '').strip()
            if primary_cat and pd.notna(primary_cat):
                primary_categories.add(primary_cat)
            
            # 提取二级分类
            secondary_cat = row.get('Sub Category?', '').strip()
            if primary_cat and secondary_cat and pd.notna(primary_cat) and pd.notna(secondary_cat):
                categories.add((primary_cat, secondary_cat))
        
        return primary_categories, categories, total_rows
        
    except ImportError:
        logger.error("需要安装pandas和openpyxl: poetry add pandas openpyxl")
        raise
    except Exception as e:
        logger.error(f"分析Excel文件时发生错误: {e}")
        raise

def create_primary_categories(db_session, primary_categories):
    """创建一级分类"""
    created_categories = {}
    created_count = 0
    
    for name in primary_categories:
        try:
            # 生成编码前缀（取前两个字符的大写）
            code_prefix = name[:2].upper()
            
            category = ItemPrimaryCategory(
                name=name,
                description=f"{name}分类",
                code_prefix=code_prefix,
                code_format="0000",
                current_sequence=1,
                is_active=True
            )
            
            db_session.add(category)
            created_categories[name] = category
            created_count += 1
            
        except Exception as e:
            logger.error(f"创建一级分类失败: {name}, 错误: {e}")
            continue
    
    try:
        db_session.commit()
        logger.info(f"✅ 一级分类创建完成: {created_count} 个")
    except Exception as e:
        logger.error(f"提交一级分类时发生错误: {e}")
        db_session.rollback()
        raise
    
    return created_categories

def create_categories(db_session, categories, primary_categories_dict):
    """创建二级分类"""
    created_categories = {}
    created_count = 0
    skipped_count = 0
    
    for primary_name, secondary_name in categories:
        try:
            if primary_name not in primary_categories_dict:
                skipped_count += 1
                continue
            
            primary_category = primary_categories_dict[primary_name]
            
            category = ItemCategory(
                name=secondary_name,
                description=f"{secondary_name}分类",
                primary_category_id=primary_category.id,
                is_active=True
            )
            
            db_session.add(category)
            created_categories[(primary_name, secondary_name)] = category
            created_count += 1
            
        except Exception as e:
            logger.error(f"创建二级分类失败: {primary_name} -> {secondary_name}, 错误: {e}")
            skipped_count += 1
            continue
    
    try:
        db_session.commit()
        logger.info(f"✅ 二级分类创建完成: {created_count} 个")
    except Exception as e:
        logger.error(f"提交二级分类时发生错误: {e}")
        db_session.rollback()
        raise
    
    return created_categories

def parse_price(price_str):
    """解析价格字符串"""
    if not price_str:
        return Decimal('0')
    
    try:
        # 检查是否为NaN值（pandas的NaN）
        if str(price_str).lower() == 'nan':
            return Decimal('0')
        
        # 移除可能的货币符号和空格
        price_str = re.sub(r'[^\d.,]', '', str(price_str))
        if ',' in price_str:
            price_str = price_str.replace(',', '')
        return Decimal(price_str)
    except Exception as e:
        return Decimal('0')

def import_items_from_part_list(db_session, excel_file_path, primary_categories_dict, categories_dict):
    """从Part List sheet导入物品基础信息"""
    try:
        import pandas as pd
        
        df_part_list = pd.read_excel(excel_file_path, sheet_name='Part List', header=1)
        
        imported_count = 0
        skipped_count = 0
        error_count = 0
        
        # 统计信息
        stats = {
            'total_rows': len(df_part_list),
            'no_category_count': 0,
            'no_name_count': 0,
            'error_count': 0
        }
        
        for index, row in df_part_list.iterrows():
            try:
                # 获取物品名称
                item_name = row.get('Item Name', '').strip()
                if not item_name or pd.isna(item_name):
                    stats['no_name_count'] += 1
                    skipped_count += 1
                    continue
                
                # 查找对应的分类
                primary_name = row.get('Category', '').strip()
                secondary_name = row.get('Sub Category?', '').strip()
                
                if not primary_name or not secondary_name or pd.isna(primary_name) or pd.isna(secondary_name):
                    stats['no_category_count'] += 1
                    skipped_count += 1
                    continue
                
                category_key = (primary_name, secondary_name)
                if category_key not in categories_dict:
                    stats['no_category_count'] += 1
                    skipped_count += 1
                    continue
                
                category = categories_dict[category_key]
                
                # 生成物品编码（使用一级分类前3个字母作为前缀）
                primary_category = primary_categories_dict[primary_name]
                code_prefix = primary_category.code_prefix
                if len(code_prefix) < 3:
                    code_prefix = code_prefix.ljust(3, 'X')
                
                # 生成编码
                item_code = f"{code_prefix}{imported_count + 1:04d}"
                
                # 获取属性信息
                brand = row.get('Brand', '').strip() if pd.notna(row.get('Brand')) else None
                spec_material = row.get('Spec/Material', '').strip() if pd.notna(row.get('Spec/Material')) else None
                size_dimension = row.get('Size/Dimension', '').strip() if pd.notna(row.get('Size/Dimension')) else None
                
                # 创建物品（基础信息）
                item = Item(
                    name=item_name,
                    code=item_code,
                    description=f"{item_name} - {brand or ''} - {spec_material or ''} - {size_dimension or ''}".strip(' -'),
                    category_id=category.id,
                    brand=brand,
                    spec_material=spec_material,
                    size_dimension=size_dimension,
                    image_url=get_random_image_url(),
                    purchase_unit='piece',  # 默认值，后续从Buy List更新
                    inventory_unit='piece',  # 默认值，后续从Buy List更新
                    qty_per_up=1,  # 默认值，后续从Buy List更新
                    is_purchasable=True,
                    is_active=True,
                )
                
                db_session.add(item)
                imported_count += 1
                
            except Exception as e:
                logger.error(f"导入物品失败 (行 {index + 2}): {e}")
                error_count += 1
                stats['error_count'] += 1
                continue
        
        try:
            db_session.commit()
            logger.info(f"✅ 物品基础信息导入完成: {imported_count} 个成功, {skipped_count} 个跳过, {error_count} 个错误")
        except Exception as e:
            logger.error(f"提交物品基础信息时发生错误: {e}")
            db_session.rollback()
            raise
        
        return imported_count
        
    except ImportError:
        logger.error("需要安装pandas和openpyxl: poetry add pandas openpyxl")
        raise
    except Exception as e:
        logger.error(f"从Part List导入物品时发生错误: {e}")
        raise

def import_supplier_relations_from_buy_list(db_session, excel_file_path, suppliers_dict):
    """从Sept25 Buy List sheet导入供应商关联和价格信息"""
    try:
        import pandas as pd
        
        df_buy_list = pd.read_excel(excel_file_path, sheet_name='Sept25 Buy List', header=1)
        
        item_supplier_count = 0
        price_created_count = 0
        skipped_count = 0
        error_count = 0
        unmatched_items = []
        no_price_items = []
        
        # 统计信息
        stats = {
            'total_rows': len(df_buy_list),
            'supplier_found_count': 0,
            'supplier_not_found_count': 0,
            'price_created_count': 0,
            'no_price_count': 0,
            'error_count': 0,
            'moq_warnings': 0,
            'spq_warnings': 0
        }
        
        for index, row in df_buy_list.iterrows():
            try:
                # 获取物品名称
                item_name = row.get('Item Name', '').strip()
                if not item_name or pd.isna(item_name):
                    skipped_count += 1
                    continue
                
                # 查找物品
                item = db_session.query(Item).filter(Item.name == item_name).first()
                if not item:
                    unmatched_items.append(item_name)
                    skipped_count += 1
                    continue
                
                # 获取供应商信息
                supplier_name = row.get('Supplier ', '').strip()
                if not supplier_name or pd.isna(supplier_name):
                    skipped_count += 1
                    continue
                
                supplier = find_supplier_by_name(suppliers_dict, supplier_name, db_session)
                if not supplier:
                    stats['supplier_not_found_count'] += 1
                    skipped_count += 1
                    continue
                
                # 获取单位信息
                uom = row.get('UOM', 'piece').strip()
                if pd.isna(uom):
                    uom = 'piece'
                
                try:
                    qty_per_up = int(float(row.get('Qty per UP', '1')))
                except (ValueError, TypeError):
                    qty_per_up = 1
                
                # 更新物品的单位信息
                if qty_per_up > 1:
                    item.purchase_unit = uom
                    item.inventory_unit = 'piece'
                else:
                    item.purchase_unit = uom
                    item.inventory_unit = uom
                item.qty_per_up = qty_per_up
                
                # 获取SPQ和MOQ
                try:
                    spq = int(float(row.get('SPQ', '1')))
                except (ValueError, TypeError):
                    spq = 1
                
                try:
                    moq = int(float(row.get('MOQ', '1')))
                except (ValueError, TypeError):
                    moq = 1
                
                # 检查MOQ是否是SPQ的整数倍
                if spq > 0 and moq % spq != 0:
                    stats['moq_warnings'] += 1
                
                # 创建物品供应商关联
                item_supplier = ItemSupplier(
                    item_id=item.id,
                    supplier_id=supplier.id,
                    priority=0,  # 0表示Preferred
                    status='active',
                    delivery_days=7,  # 默认交货天数
                    quality_rating=3,  # 默认质量评级
                    spq=spq,
                    moq=moq,
                )
                
                db_session.add(item_supplier)
                db_session.flush()  # 获取item_supplier.id
                
                # 创建价格记录（优先使用USD价格）
                usd_price = row.get('U/Price (USD)')
                trans_price = row.get('U/Price (Trans Currency)')
                trans_currency = row.get('Currency', 'USD')  # 获取交易货币
                
                # 确定价格和货币
                if pd.notna(trans_price) and parse_price(trans_price) > 0:
                    unit_price = parse_price(trans_price)
                    currency_code = trans_currency if trans_currency else 'USD'
                else:
                    unit_price = 0
                    currency_code = 'USD'
                
                if unit_price > 0:
                    supplier_price = SupplierPrice(
                        item_supplier_id=item_supplier.id,
                        unit_price=unit_price,
                        currency_code=currency_code,
                        min_quantity=moq,
                        valid_from=datetime.now(),
                        status='active'
                    )
                    db_session.add(supplier_price)
                    price_created_count += 1
                    stats['price_created_count'] += 1
                else:
                    no_price_items.append(f"{item_name} - {supplier_name}")
                    stats['no_price_count'] += 1
                
                item_supplier_count += 1
                stats['supplier_found_count'] += 1
                
            except Exception as e:
                logger.error(f"导入供应商关联失败 (行 {index + 2}): {e}")
                error_count += 1
                stats['error_count'] += 1
                continue
        
        try:
            db_session.commit()
            logger.info(f"✅ 供应商关联导入完成: {item_supplier_count} 个成功, {skipped_count} 个跳过, {error_count} 个错误")
            logger.info(f"✅ 价格记录创建: {price_created_count} 个")
            
            # 打印未匹配的物品
            if unmatched_items:
                logger.warning(f"⚠️ 未在Part List中找到的物品 ({len(unmatched_items)} 个):")
                for item_name in unmatched_items:  # 显示所有未匹配的物品
                    logger.warning(f"  - {item_name}")
            
            # 打印没有价格的物品
            if no_price_items:
                logger.warning(f"⚠️ 没有价格信息的物品 ({len(no_price_items)} 个):")
                for item_info in no_price_items:  # 显示所有没有价格的物品
                    logger.warning(f"  - {item_info}")
                    
        except Exception as e:
            logger.error(f"提交供应商关联时发生错误: {e}")
            db_session.rollback()
            raise
        
        return item_supplier_count, price_created_count
        
    except ImportError:
        logger.error("需要安装pandas和openpyxl: poetry add pandas openpyxl")
        raise
    except Exception as e:
        logger.error(f"从Buy List导入供应商关联时发生错误: {e}")
        raise

def create_new_supplier(supplier_name, db_session):
    """创建新的供应商"""
    try:
        # 生成供应商编码（使用名称的前3个字符 + 随机数字）
        import random
        prefix = supplier_name[:3].upper().replace(' ', '')
        if len(prefix) < 3:
            prefix = prefix.ljust(3, 'X')
        
        # 检查编码是否已存在
        existing_code = None
        for i in range(100):  # 尝试100次
            code = f"{prefix}{random.randint(1000, 9999)}"
            existing = db_session.query(Supplier).filter(Supplier.code == code).first()
            if not existing:
                existing_code = code
                break
        
        if not existing_code:
            logger.error(f"无法为供应商 {supplier_name} 生成唯一编码")
            return None
        
        # 创建新供应商
        new_supplier = Supplier(
            code=existing_code,
            name_en=supplier_name,
            name_cn=None,
            company_address=None,
            contact_person=None,
            phone=None,
            email=None,
            rating=3,  # 默认评级
            status='active'
        )
        
        db_session.add(new_supplier)
        db_session.flush()  # 获取ID
        
        return new_supplier
        
    except Exception as e:
        logger.error(f"创建新供应商失败: {supplier_name}, 错误: {e}")
        return None

def main():
    """主函数"""
    logger.info("=== 开始导入供应商和物品数据 ===")
    
    vendor_csv_path = Path(__file__).parent / "vender-list-data.csv"
    excel_file_path = Path(__file__).parent / "IDM Consumables Part List-Sept.xlsx"
    
    # 检查文件是否存在
    if not vendor_csv_path.exists():
        logger.error(f"错误: 找不到供应商CSV文件 {vendor_csv_path}")
        return
    
    if not excel_file_path.exists():
        logger.error(f"错误: 找不到Excel文件 {excel_file_path}")
        return
    
    # 创建数据库会话
    db_session = SessionLocal()
    
    try:
        # 1. 清空现有数据
        clear_existing_data(db_session)
        
        # 2. 导入供应商数据
        suppliers_dict = import_suppliers(db_session, vendor_csv_path)
        
        # 3. 分析Excel数据
        primary_categories, categories, total_rows = analyze_excel_data(excel_file_path)
        
        # 4. 创建一级分类
        primary_categories_dict = create_primary_categories(db_session, primary_categories)
        
        # 5. 创建二级分类
        categories_dict = create_categories(db_session, categories, primary_categories_dict)
        
        # 6. 从Part List导入物品基础信息
        imported_items_count = import_items_from_part_list(db_session, excel_file_path, primary_categories_dict, categories_dict)
        
        # 7. 从Buy List导入供应商关联和价格信息
        item_supplier_count, price_count = import_supplier_relations_from_buy_list(db_session, excel_file_path, suppliers_dict)
        
        # 8. 最终统计
        logger.info("=== 数据导入完成 ===")
        logger.info(f"📊 最终统计:")
        logger.info(f"  - 供应商数量: {len(suppliers_dict)}")
        logger.info(f"  - 一级分类数量: {len(primary_categories_dict)}")
        logger.info(f"  - 二级分类数量: {len(categories_dict)}")
        logger.info(f"  - 物品数量: {imported_items_count}")
        logger.info(f"  - 物品供应商关联数量: {item_supplier_count}")
        logger.info(f"  - 价格记录数量: {price_count}")
        
    except Exception as e:
        logger.error(f"导入过程中发生错误: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        db_session.rollback()
    finally:
        db_session.close()

if __name__ == '__main__':
    main() 