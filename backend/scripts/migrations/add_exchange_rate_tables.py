#!/usr/bin/env python3
"""
数据库迁移脚本：添加汇率管理相关表和字段
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Integer, Numeric, Date, DateTime, Text, Index
from sqlalchemy.dialects.sqlite import JSON
from app.core.config import settings
from app.core.database import get_database_url


def create_exchange_rate_tables():
    """创建汇率管理相关表"""
    
    # 获取数据库连接
    database_url = get_database_url()
    engine = create_engine(database_url)
    
    with engine.connect() as conn:
        # 创建汇率表
        print("创建汇率表...")
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS exchange_rates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                currency_code VARCHAR(10) NOT NULL,
                rate DECIMAL(10,6) NOT NULL,
                effective_month DATE NOT NULL,
                status VARCHAR(20) DEFAULT 'active',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                updated_by INTEGER
            )
        """))
        
        # 创建汇率修改日志表
        print("创建汇率修改日志表...")
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS exchange_rate_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                exchange_rate_id INTEGER NOT NULL,
                old_rate DECIMAL(10,6) NOT NULL,
                new_rate DECIMAL(10,6) NOT NULL,
                change_reason TEXT NOT NULL,
                changed_by INTEGER NOT NULL,
                changed_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """))
        
        # 创建采购申请汇率记录表
        print("创建采购申请汇率记录表...")
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS purchase_request_exchange_rates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                purchase_request_id INTEGER NOT NULL,
                stage VARCHAR(20) NOT NULL,
                currency_code VARCHAR(10) NOT NULL,
                rate DECIMAL(10,6) NOT NULL,
                recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                recorded_by INTEGER NOT NULL
            )
        """))
        
        # 创建索引
        print("创建索引...")
        
        # 汇率表索引
        conn.execute(text("CREATE INDEX IF NOT EXISTS idx_exchange_rates_currency_code ON exchange_rates(currency_code)"))
        conn.execute(text("CREATE INDEX IF NOT EXISTS idx_exchange_rates_effective_month ON exchange_rates(effective_month)"))
        conn.execute(text("CREATE INDEX IF NOT EXISTS idx_exchange_rates_status ON exchange_rates(status)"))
        conn.execute(text("CREATE INDEX IF NOT EXISTS idx_exchange_rates_created_at ON exchange_rates(created_at)"))
        conn.execute(text("CREATE UNIQUE INDEX IF NOT EXISTS idx_exchange_rates_currency_month_unique ON exchange_rates(currency_code, effective_month)"))
        
        # 汇率修改日志表索引
        conn.execute(text("CREATE INDEX IF NOT EXISTS idx_exchange_rate_logs_exchange_rate_id ON exchange_rate_logs(exchange_rate_id)"))
        conn.execute(text("CREATE INDEX IF NOT EXISTS idx_exchange_rate_logs_changed_at ON exchange_rate_logs(changed_at)"))
        conn.execute(text("CREATE INDEX IF NOT EXISTS idx_exchange_rate_logs_changed_by ON exchange_rate_logs(changed_by)"))
        
        # 采购申请汇率记录表索引
        conn.execute(text("CREATE INDEX IF NOT EXISTS idx_purchase_request_exchange_rates_request_id ON purchase_request_exchange_rates(purchase_request_id)"))
        conn.execute(text("CREATE INDEX IF NOT EXISTS idx_purchase_request_exchange_rates_stage ON purchase_request_exchange_rates(stage)"))
        conn.execute(text("CREATE INDEX IF NOT EXISTS idx_purchase_request_exchange_rates_currency_code ON purchase_request_exchange_rates(currency_code)"))
        conn.execute(text("CREATE INDEX IF NOT EXISTS idx_purchase_request_exchange_rates_recorded_at ON purchase_request_exchange_rates(recorded_at)"))
        
        # 提交事务
        conn.commit()
        print("汇率管理相关表创建完成！")


def add_currency_code_to_supplier_prices():
    """为供应商价格表添加货币代码字段"""
    
    # 获取数据库连接
    database_url = get_database_url()
    engine = create_engine(database_url)
    
    with engine.connect() as conn:
        # 检查字段是否已存在
        result = conn.execute(text("PRAGMA table_info(supplier_prices)"))
        columns = [row[1] for row in result.fetchall()]
        
        if 'currency_code' not in columns:
            print("为供应商价格表添加货币代码字段...")
            conn.execute(text("ALTER TABLE supplier_prices ADD COLUMN currency_code VARCHAR(10) DEFAULT 'USD'"))
            
            # 创建索引
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_supplier_prices_currency_code ON supplier_prices(currency_code)"))
            
            # 提交事务
            conn.commit()
            print("货币代码字段添加完成！")
        else:
            print("货币代码字段已存在，跳过添加")


def update_existing_supplier_prices():
    """更新现有供应商价格数据，设置默认货币为USD"""
    
    # 获取数据库连接
    database_url = get_database_url()
    engine = create_engine(database_url)
    
    with engine.connect() as conn:
        print("更新现有供应商价格数据...")
        
        # 更新所有现有记录的货币代码为USD
        result = conn.execute(text("""
            UPDATE supplier_prices 
            SET currency_code = 'USD' 
            WHERE currency_code IS NULL OR currency_code = ''
        """))
        
        updated_count = result.rowcount
        print(f"更新了 {updated_count} 条供应商价格记录")
        
        # 提交事务
        conn.commit()


def insert_sample_exchange_rates():
    """插入示例汇率数据"""
    
    # 获取数据库连接
    database_url = get_database_url()
    engine = create_engine(database_url)
    
    with engine.connect() as conn:
        print("插入示例汇率数据...")
        
        # 检查是否已有数据
        result = conn.execute(text("SELECT COUNT(*) FROM exchange_rates"))
        count = result.fetchone()[0]
        
        if count == 0:
            # 插入示例汇率数据（2024年1月）
            sample_rates = [
                ("CNY", "7.200000", "2024-01-01"),
                ("EUR", "0.920000", "2024-01-01"),
                ("JPY", "148.500000", "2024-01-01"),
                ("GBP", "0.790000", "2024-01-01"),
                ("KRW", "1330.000000", "2024-01-01"),
                ("SGD", "1.340000", "2024-01-01"),
                ("HKD", "7.820000", "2024-01-01"),
            ]
            
            for currency_code, rate, effective_month in sample_rates:
                conn.execute(text("""
                    INSERT INTO exchange_rates (currency_code, rate, effective_month, status, created_at)
                    VALUES (:currency_code, :rate, :effective_month, 'active', CURRENT_TIMESTAMP)
                """), {
                    "currency_code": currency_code,
                    "rate": rate,
                    "effective_month": effective_month
                })
            
            # 提交事务
            conn.commit()
            print("示例汇率数据插入完成！")
        else:
            print("汇率表已有数据，跳过示例数据插入")


def main():
    """主函数"""
    print("开始执行汇率管理数据库迁移...")
    
    try:
        # 1. 创建汇率管理相关表
        create_exchange_rate_tables()
        
        # 2. 为供应商价格表添加货币代码字段
        add_currency_code_to_supplier_prices()
        
        # 3. 更新现有供应商价格数据
        update_existing_supplier_prices()
        
        # 4. 插入示例汇率数据
        insert_sample_exchange_rates()
        
        print("汇率管理数据库迁移完成！")
        
    except Exception as e:
        print(f"迁移过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
