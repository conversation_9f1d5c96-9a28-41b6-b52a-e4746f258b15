#!/usr/bin/env python3
"""
数据库迁移脚本：为采购申请明细表添加最终价格字段
移除价格快照逻辑和预估价格字段，改为在申请明细表中直接存储最终价格
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sqlalchemy import create_engine, text
from app.core.config import settings

def migrate():
    """执行数据库迁移"""
    # 创建数据库连接
    engine = create_engine(settings.DATABASE_URL)
    
    with engine.connect() as conn:
        try:
            # 开始事务
            trans = conn.begin()
            
            print("开始执行数据库迁移...")
            
            # 1. 为 purchase_request_items 表添加最终价格字段
            print("1. 添加最终价格字段...")
            
            # 检查字段是否已存在
            check_sql = """
            SELECT COUNT(*) FROM pragma_table_info('purchase_request_items') 
            WHERE name = 'final_unit_price'
            """
            result = conn.execute(text(check_sql)).scalar()
            
            if result == 0:
                # 添加最终价格字段
                alter_sql = """
                ALTER TABLE purchase_request_items 
                ADD COLUMN final_unit_price DECIMAL(12,4) COMMENT '最终单价'
                """
                conn.execute(text(alter_sql))
                print("   - 添加 final_unit_price 字段")
            else:
                print("   - final_unit_price 字段已存在")
            
            # 检查 final_total_price 字段
            check_sql = """
            SELECT COUNT(*) FROM pragma_table_info('purchase_request_items') 
            WHERE name = 'final_total_price'
            """
            result = conn.execute(text(check_sql)).scalar()
            
            if result == 0:
                alter_sql = """
                ALTER TABLE purchase_request_items 
                ADD COLUMN final_total_price DECIMAL(15,2) COMMENT '最终总价'
                """
                conn.execute(text(alter_sql))
                print("   - 添加 final_total_price 字段")
            else:
                print("   - final_total_price 字段已存在")
            
            # 检查 final_supplier_id 字段
            check_sql = """
            SELECT COUNT(*) FROM pragma_table_info('purchase_request_items') 
            WHERE name = 'final_supplier_id'
            """
            result = conn.execute(text(check_sql)).scalar()
            
            if result == 0:
                alter_sql = """
                ALTER TABLE purchase_request_items 
                ADD COLUMN final_supplier_id INTEGER COMMENT '最终供应商ID'
                """
                conn.execute(text(alter_sql))
                print("   - 添加 final_supplier_id 字段")
            else:
                print("   - final_supplier_id 字段已存在")
            
            # 检查 price_locked_at 字段
            check_sql = """
            SELECT COUNT(*) FROM pragma_table_info('purchase_request_items') 
            WHERE name = 'price_locked_at'
            """
            result = conn.execute(text(check_sql)).scalar()
            
            if result == 0:
                alter_sql = """
                ALTER TABLE purchase_request_items 
                ADD COLUMN price_locked_at DATETIME COMMENT '价格锁定时间'
                """
                conn.execute(text(alter_sql))
                print("   - 添加 price_locked_at 字段")
            else:
                print("   - price_locked_at 字段已存在")
            
            # 2. 移除预估价格字段
            print("2. 移除预估价格字段...")
            
            # 检查并移除 estimated_unit_price 字段
            check_sql = """
            SELECT COUNT(*) FROM pragma_table_info('purchase_request_items') 
            WHERE name = 'estimated_unit_price'
            """
            result = conn.execute(text(check_sql)).scalar()
            
            if result > 0:
                # SQLite不支持DROP COLUMN，需要重建表
                print("   - 注意：SQLite不支持DROP COLUMN，estimated_unit_price字段将保留但不再使用")
                print("   - 建议在后续版本中重建表结构以完全移除该字段")
            
            # 检查并移除 total_price 字段
            check_sql = """
            SELECT COUNT(*) FROM pragma_table_info('purchase_request_items') 
            WHERE name = 'total_price'
            """
            result = conn.execute(text(check_sql)).scalar()
            
            if result > 0:
                print("   - 注意：SQLite不支持DROP COLUMN，total_price字段将保留但不再使用")
                print("   - 建议在后续版本中重建表结构以完全移除该字段")
            
            # 3. 为 purchase_requests 表移除预估总金额字段
            print("3. 移除预估总金额字段...")
            
            # 检查并移除 estimated_total 字段
            check_sql = """
            SELECT COUNT(*) FROM pragma_table_info('purchase_requests') 
            WHERE name = 'estimated_total'
            """
            result = conn.execute(text(check_sql)).scalar()
            
            if result > 0:
                print("   - 注意：SQLite不支持DROP COLUMN，estimated_total字段将保留但不再使用")
                print("   - 建议在后续版本中重建表结构以完全移除该字段")
            
            # 4. 创建索引
            print("4. 创建索引...")
            
            # 检查索引是否已存在
            check_index_sql = """
            SELECT COUNT(*) FROM pragma_index_list('purchase_request_items') 
            WHERE name = 'idx_request_items_final_supplier'
            """
            result = conn.execute(text(check_index_sql)).scalar()
            
            if result == 0:
                create_index_sql = """
                CREATE INDEX idx_request_items_final_supplier 
                ON purchase_request_items (final_supplier_id)
                """
                conn.execute(text(create_index_sql))
                print("   - 创建 final_supplier_id 索引")
            else:
                print("   - final_supplier_id 索引已存在")
            
            # 5. 移除价格快照表
            print("5. 移除价格快照表...")
            
            # 检查价格快照表是否存在
            check_table_sql = """
            SELECT COUNT(*) FROM sqlite_master 
            WHERE type='table' AND name='price_snapshots'
            """
            result = conn.execute(text(check_table_sql)).scalar()
            
            if result > 0:
                # 检查表是否为空
                check_empty_sql = "SELECT COUNT(*) FROM price_snapshots"
                count = conn.execute(text(check_empty_sql)).scalar()
                
                if count == 0:
                    # 如果表为空，可以安全删除
                    drop_table_sql = "DROP TABLE price_snapshots"
                    conn.execute(text(drop_table_sql))
                    print("   - 删除空的 price_snapshots 表")
                else:
                    print(f"   - price_snapshots 表存在 {count} 条数据，保留表结构")
                    print("   - 建议在数据迁移完成后手动删除该表")
            
            # 提交事务
            trans.commit()
            print("数据库迁移完成！")
            print("\n注意事项：")
            print("- 由于SQLite限制，预估价格字段无法完全移除，但已不再使用")
            print("- 建议在后续版本中重建表结构以完全清理这些字段")
            print("- 价格快照表已移除，系统现在使用实时价格计算")
            
        except Exception as e:
            # 回滚事务
            trans.rollback()
            print(f"迁移失败: {str(e)}")
            raise

if __name__ == "__main__":
    migrate()
