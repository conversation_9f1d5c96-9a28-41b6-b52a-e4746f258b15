#!/usr/bin/env python3
"""
修复采购申请状态值不一致问题

本脚本将数据库中的状态值更新为与代码中定义的常量完全一致的值。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.core.database import get_database_url
from app.core.constants import PurchaseRequestStatus
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_purchase_request_status_values():
    """修复采购申请状态值"""
    
    # 获取数据库连接
    database_url = get_database_url()
    engine = create_engine(database_url)
    
    try:
        with engine.connect() as conn:
            # 开始事务
            trans = conn.begin()
            
            try:
                # 检查当前数据库中的状态值
                result = conn.execute(text("""
                    SELECT DISTINCT status, COUNT(*) as count 
                    FROM purchase_requests 
                    GROUP BY status
                """))
                
                current_statuses = {row.status: row.count for row in result}
                logger.info(f"当前数据库中的状态值: {current_statuses}")
                
                # 定义正确的状态值映射
                status_mapping = {
                    # 如果数据库中有错误的状态值，在这里定义映射关系
                    # 例如: 'pending_review': 'under_review'
                }
                
                # 检查是否有需要修复的状态值
                needs_fix = False
                for old_status, new_status in status_mapping.items():
                    if old_status in current_statuses:
                        needs_fix = True
                        logger.info(f"需要将状态 '{old_status}' 更新为 '{new_status}'")
                
                if not needs_fix:
                    logger.info("数据库中的状态值已经是正确的，无需修复")
                    return
                
                # 执行状态值更新
                for old_status, new_status in status_mapping.items():
                    if old_status in current_statuses:
                        # 更新状态值
                        result = conn.execute(text("""
                            UPDATE purchase_requests 
                            SET status = :new_status, updated_at = CURRENT_TIMESTAMP
                            WHERE status = :old_status
                        """), {"old_status": old_status, "new_status": new_status})
                        
                        logger.info(f"已更新 {result.rowcount} 条记录的状态从 '{old_status}' 到 '{new_status}'")
                        
                        # 同时更新流转历史表中的状态值
                        result = conn.execute(text("""
                            UPDATE request_flow_history 
                            SET from_status = :new_status, updated_at = CURRENT_TIMESTAMP
                            WHERE from_status = :old_status
                        """), {"old_status": old_status, "new_status": new_status})
                        
                        logger.info(f"已更新 {result.rowcount} 条流转历史记录的 from_status 从 '{old_status}' 到 '{new_status}'")
                        
                        result = conn.execute(text("""
                            UPDATE request_flow_history 
                            SET to_status = :new_status, updated_at = CURRENT_TIMESTAMP
                            WHERE to_status = :old_status
                        """), {"old_status": old_status, "new_status": new_status})
                        
                        logger.info(f"已更新 {result.rowcount} 条流转历史记录的 to_status 从 '{old_status}' 到 '{new_status}'")
                
                # 验证修复结果
                result = conn.execute(text("""
                    SELECT DISTINCT status, COUNT(*) as count 
                    FROM purchase_requests 
                    GROUP BY status
                """))
                
                final_statuses = {row.status: row.count for row in result}
                logger.info(f"修复后的状态值: {final_statuses}")
                
                # 提交事务
                trans.commit()
                logger.info("状态值修复完成")
                
            except Exception as e:
                # 回滚事务
                trans.rollback()
                logger.error(f"修复状态值时发生错误: {e}")
                raise
                
    except Exception as e:
        logger.error(f"连接数据库时发生错误: {e}")
        raise
    finally:
        engine.dispose()

def verify_status_values():
    """验证状态值是否正确"""
    
    # 获取数据库连接
    database_url = get_database_url()
    engine = create_engine(database_url)
    
    try:
        with engine.connect() as conn:
            # 检查数据库中的状态值
            result = conn.execute(text("""
                SELECT DISTINCT status 
                FROM purchase_requests 
                ORDER BY status
            """))
            
            db_statuses = {row.status for row in result}
            
            # 代码中定义的正确状态值
            expected_statuses = {
                PurchaseRequestStatus.DRAFT,
                PurchaseRequestStatus.PENDING_SUBMISSION,
                PurchaseRequestStatus.UNDER_REVIEW,
                PurchaseRequestStatus.UNDER_PRINCIPLE_APPROVAL,
                PurchaseRequestStatus.UNDER_FINAL_APPROVAL,
                PurchaseRequestStatus.APPROVED,
                PurchaseRequestStatus.REJECTED,
                PurchaseRequestStatus.WITHDRAWN,
                PurchaseRequestStatus.EXECUTED
            }
            
            # 检查是否有不一致的状态值
            invalid_statuses = db_statuses - expected_statuses
            missing_statuses = expected_statuses - db_statuses
            
            if invalid_statuses:
                logger.warning(f"数据库中存在无效的状态值: {invalid_statuses}")
            else:
                logger.info("数据库中的状态值都是有效的")
                
            if missing_statuses:
                logger.info(f"数据库中缺少的状态值: {missing_statuses}")
            else:
                logger.info("数据库包含了所有预期的状态值")
                
            logger.info(f"数据库中的状态值: {sorted(db_statuses)}")
            logger.info(f"预期的状态值: {sorted(expected_statuses)}")
            
    except Exception as e:
        logger.error(f"验证状态值时发生错误: {e}")
        raise
    finally:
        engine.dispose()

if __name__ == "__main__":
    logger.info("开始修复采购申请状态值...")
    
    try:
        # 先验证当前状态
        verify_status_values()
        
        # 执行修复
        fix_purchase_request_status_values()
        
        # 再次验证
        verify_status_values()
        
        logger.info("状态值修复和验证完成")
        
    except Exception as e:
        logger.error(f"执行失败: {e}")
        sys.exit(1)
