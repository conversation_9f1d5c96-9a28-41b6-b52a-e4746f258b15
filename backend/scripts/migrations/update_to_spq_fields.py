#!/usr/bin/env python3
"""
数据库迁移脚本：将数量字段改为SPQ相关字段

此脚本将：
1. 在purchase_cart_items表中添加spq_quantity、spq_count、spq_unit字段
2. 在purchase_request_items表中添加spq_quantity、spq_count、spq_unit字段
3. 在purchase_order_items表中添加spq_quantity、spq_count、spq_unit字段
4. 迁移现有数据（如果存在）
5. 删除旧的quantity和unit字段（如果数据库支持）

注意：SQLite不支持DROP COLUMN，所以旧字段将保留但不使用
"""

import sqlite3
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.core.database import get_database_url, engine
from sqlalchemy import text

def run_migration():
    """运行数据库迁移"""
    print("开始SPQ字段迁移...")
    
    try:
        # 获取数据库连接
        with engine.connect() as conn:
            # 开始事务
            trans = conn.begin()
            
            try:
                # 1. 更新purchase_cart_items表
                print("1. 更新purchase_cart_items表...")
                
                # 检查是否已存在SPQ字段
                result = conn.execute(text("PRAGMA table_info(purchase_cart_items)"))
                columns = [row[1] for row in result.fetchall()]
                
                if 'spq_quantity' not in columns:
                    conn.execute(text("ALTER TABLE purchase_cart_items ADD COLUMN spq_quantity DECIMAL(10,3) DEFAULT 1.0"))
                    print("   - 添加spq_quantity字段")
                
                if 'spq_count' not in columns:
                    conn.execute(text("ALTER TABLE purchase_cart_items ADD COLUMN spq_count INTEGER DEFAULT 1"))
                    print("   - 添加spq_count字段")
                
                if 'spq_unit' not in columns:
                    conn.execute(text("ALTER TABLE purchase_cart_items ADD COLUMN spq_unit VARCHAR(20) DEFAULT '个'"))
                    print("   - 添加spq_unit字段")
                
                # 迁移现有数据（如果存在quantity和unit字段）
                if 'quantity' in columns and 'unit' in columns:
                    print("   - 迁移现有数据...")
                    # 将现有的quantity作为spq_count，unit作为spq_unit
                    conn.execute(text("""
                        UPDATE purchase_cart_items 
                        SET spq_count = CAST(quantity AS INTEGER),
                            spq_unit = unit
                        WHERE spq_count IS NULL OR spq_unit IS NULL
                    """))
                    print("   - 数据迁移完成")
                
                # 2. 更新purchase_request_items表
                print("2. 更新purchase_request_items表...")
                
                result = conn.execute(text("PRAGMA table_info(purchase_request_items)"))
                columns = [row[1] for row in result.fetchall()]
                
                if 'spq_quantity' not in columns:
                    conn.execute(text("ALTER TABLE purchase_request_items ADD COLUMN spq_quantity DECIMAL(10,3) DEFAULT 1.0"))
                    print("   - 添加spq_quantity字段")
                
                if 'spq_count' not in columns:
                    conn.execute(text("ALTER TABLE purchase_request_items ADD COLUMN spq_count INTEGER DEFAULT 1"))
                    print("   - 添加spq_count字段")
                
                if 'spq_unit' not in columns:
                    conn.execute(text("ALTER TABLE purchase_request_items ADD COLUMN spq_unit VARCHAR(20) DEFAULT '个'"))
                    print("   - 添加spq_unit字段")
                
                # 迁移现有数据
                if 'quantity' in columns and 'unit' in columns:
                    print("   - 迁移现有数据...")
                    conn.execute(text("""
                        UPDATE purchase_request_items 
                        SET spq_count = CAST(quantity AS INTEGER),
                            spq_unit = unit
                        WHERE spq_count IS NULL OR spq_unit IS NULL
                    """))
                    print("   - 数据迁移完成")
                
                # 3. 更新purchase_order_items表
                print("3. 更新purchase_order_items表...")
                
                result = conn.execute(text("PRAGMA table_info(purchase_order_items)"))
                columns = [row[1] for row in result.fetchall()]
                
                if 'spq_quantity' not in columns:
                    conn.execute(text("ALTER TABLE purchase_order_items ADD COLUMN spq_quantity DECIMAL(10,3) DEFAULT 1.0"))
                    print("   - 添加spq_quantity字段")
                
                if 'spq_count' not in columns:
                    conn.execute(text("ALTER TABLE purchase_order_items ADD COLUMN spq_count INTEGER DEFAULT 1"))
                    print("   - 添加spq_count字段")
                
                if 'spq_unit' not in columns:
                    conn.execute(text("ALTER TABLE purchase_order_items ADD COLUMN spq_unit VARCHAR(20) DEFAULT '个'"))
                    print("   - 添加spq_unit字段")
                
                # 迁移现有数据
                if 'quantity' in columns and 'unit' in columns:
                    print("   - 迁移现有数据...")
                    conn.execute(text("""
                        UPDATE purchase_order_items 
                        SET spq_count = CAST(quantity AS INTEGER),
                            spq_unit = unit
                        WHERE spq_count IS NULL OR spq_unit IS NULL
                    """))
                    print("   - 数据迁移完成")
                
                # 4. 创建索引
                print("4. 创建索引...")
                
                # 为SPQ字段创建索引
                try:
                    conn.execute(text("CREATE INDEX IF NOT EXISTS idx_cart_items_spq ON purchase_cart_items(spq_quantity, spq_count)"))
                    conn.execute(text("CREATE INDEX IF NOT EXISTS idx_request_items_spq ON purchase_request_items(spq_quantity, spq_count)"))
                    conn.execute(text("CREATE INDEX IF NOT EXISTS idx_order_items_spq ON purchase_order_items(spq_quantity, spq_count)"))
                    print("   - SPQ索引创建完成")
                except Exception as e:
                    print(f"   - 索引创建警告: {e}")
                
                # 提交事务
                trans.commit()
                print("✅ 迁移完成！")
                
                # 5. 显示迁移结果
                print("\n迁移结果:")
                print("- purchase_cart_items表: 已添加SPQ字段")
                print("- purchase_request_items表: 已添加SPQ字段")
                print("- purchase_order_items表: 已添加SPQ字段")
                print("- 现有数据已迁移到新字段")
                print("\n注意: 由于SQLite限制，旧的quantity和unit字段仍然存在但不使用")
                
            except Exception as e:
                # 回滚事务
                trans.rollback()
                print(f"❌ 迁移失败: {e}")
                raise
                
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("SPQ字段迁移脚本")
    print("=" * 50)
    
    success = run_migration()
    
    if success:
        print("\n🎉 迁移成功完成！")
        sys.exit(0)
    else:
        print("\n💥 迁移失败！")
        sys.exit(1)
