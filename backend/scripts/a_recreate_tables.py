#!/usr/bin/env python3
"""
重新创建数据库表，确保约束正确
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text, inspect
from app.core.database import engine, Base
from app.models import *

def clear_database_completely():
    """完全清空数据库，适用于PostgreSQL"""
    with engine.connect() as conn:
        # 禁用外键约束检查
        conn.execute(text("SET session_replication_role = replica;"))
        
        # 获取所有用户表
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        print(f"发现 {len(tables)} 个表需要删除")
        
        # 删除所有表
        for table in tables:
            try:
                conn.execute(text(f"DROP TABLE IF EXISTS {table} CASCADE;"))
                print(f"✅ 删除表: {table}")
            except Exception as e:
                print(f"⚠️ 删除表 {table} 时出错: {e}")
        
        # 重新启用外键约束检查
        conn.execute(text("SET session_replication_role = DEFAULT;"))
        conn.commit()
        
        print("✅ 数据库完全清空完成")

def recreate_tables():
    """重新创建数据库表，适用于PostgreSQL数据库"""
    print("=== 开始重建数据库 ===")
    
    # 1. 完全清空数据库
    print("步骤1: 完全清空数据库...")
    clear_database_completely()
    
    # 2. 重新创建所有表
    print("步骤2: 重新创建所有表...")
    Base.metadata.create_all(bind=engine)
    
    print("✅ 数据库表重新创建完成")

if __name__ == '__main__':
    recreate_tables() 