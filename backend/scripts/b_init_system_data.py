#!/usr/bin/env python3
"""
系统初始化脚本
创建管理员账号、权限数据、演示部门数据、预设角色和部门仓库角色
集成功能：
- 基础系统数据初始化
- 部门仓库角色和权限初始化  
- 部门仓库用户账号初始化
- 采购申请执行功能权限初始化
- 所有用户都可查看物品列表权限配置
- 审批和批量采购角色采购申请模块访问权限配置
"""

import os
import hashlib
import bcrypt
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 导入项目配置
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.core.config import settings
from app.core.database import engine, SessionLocal


DEPARTMENTS = [
    {
        "name": "Information Technology",
        "code": "IT",
        "description": "Responsible for maintaining and developing the company's information technology system"
    },
    {
        "name": "Human Resources", 
        "code": "HR",
        "description": "Responsible for managing and recruiting the company's human resources"
    },
    {
        "name": "Purchasing",
        "code": "PUR",
        "description": "Responsible for managing and purchasing the company's suppliers"
    },
    {
        "name": "Warehouse",
        "code": "WH",
        "description": "Responsible for managing and storing the company's inventory"
    },
    {
        "name": "Manufacturing Engineering",
        "code": "ME",
        "description": "Responsible for managing and producing the company's products"
    },
    {
        "name": "Systems Integration",
        "code": "SI",
        "description": "Responsible for integrating the company's systems"
    },
    {
        "name": "Administration",
        "code": "AD",
        "description": "Responsible for managing and supporting the company's administration"
    },
    {
        "name": "Quality Assurance",
        "code": "QA",
        "description": "Responsible for managing and supporting the company's quality assurance"
    }
]

EMPLOYEE_ID_PREFIX = "EMP"
EMPLOYEE_ID_MAX = 0

def apply_employee_id(dept_code):
    global EMPLOYEE_ID_MAX
    EMPLOYEE_ID_MAX += 1
    return f"{EMPLOYEE_ID_PREFIX}{EMPLOYEE_ID_MAX:03d}"

def hash_password(password):
    """使用bcrypt加密密码"""
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def get_insert_ignore_sql(table_name, columns, values_placeholder):
    """根据数据库类型生成INSERT OR IGNORE语句"""
    database_url = settings.database_url.lower()
    
    if 'sqlite' in database_url:
        # SQLite使用 INSERT OR IGNORE
        return f"INSERT OR IGNORE INTO {table_name} ({columns}) VALUES ({values_placeholder})"
    elif 'postgresql' in database_url:
        # PostgreSQL使用 INSERT ... ON CONFLICT DO NOTHING
        return f"INSERT INTO {table_name} ({columns}) VALUES ({values_placeholder}) ON CONFLICT DO NOTHING"
    elif 'mysql' in database_url:
        # MySQL使用 INSERT ... ON DUPLICATE KEY UPDATE
        return f"INSERT INTO {table_name} ({columns}) VALUES ({values_placeholder}) ON DUPLICATE KEY UPDATE id=id"
    else:
        # 默认使用SQLite语法
        return f"INSERT OR IGNORE INTO {table_name} ({columns}) VALUES ({values_placeholder})"

def init_dept_warehouse_role(db):
    """初始化部门仓库角色和权限"""
    print("初始化部门仓库角色和权限...")
    
    try:
        # 1. 创建或更新部门仓库角色
        result = db.execute(text("SELECT id FROM roles WHERE code = 'dept_warehouse'"))
        dept_warehouse_role = result.fetchone()
        
        if not dept_warehouse_role:
            sql = get_insert_ignore_sql(
                "roles",
                "code, name, description, is_active, is_system, created_at, updated_at",
                ":code, :name, :description, :is_active, :is_system, :created_at, :updated_at"
            )
            db.execute(text(sql), {
                "code": "dept_warehouse",
                "name": "Department Warehouse",
                "description": "Department Warehouse, with item pickup and record view permissions",
                "is_active": True,
                "is_system": False,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            })
            db.flush()
            print("✅ 创建部门仓库角色成功")
        else:
            print("✅ 部门仓库角色已存在")
        
        # 2. 创建或更新相关权限（补充部门仓库所需权限）
        dept_warehouse_permissions = [
            {"code": "item_pickup", "name": "Item Pickup", "description": "Perform item pickup operations", "module": "inventory"},
            {"code": "record_view", "name": "Record View", "description": "View records of items picked up by this department", "module": "usage"},
            {"code": "inventory_query", "name": "Inventory Query", "description": "Query inventory of this department", "module": "inventory"},
            {"code": "employee_verify", "name": "Employee Verify", "description": "Verify employee identity", "module": "user"}
        ]
        
        created_permissions = []
        for perm_data in dept_warehouse_permissions:
            result = db.execute(text("SELECT id FROM permissions WHERE code = :code"), {"code": perm_data["code"]})
            permission = result.fetchone()
            if not permission:
                sql = get_insert_ignore_sql(
                    "permissions",
                    "code, name, description, module, is_active, is_system, created_at, updated_at",
                    ":code, :name, :description, :module, :is_active, :is_system, :created_at, :updated_at"
                )
                db.execute(text(sql), {
                    "code": perm_data["code"],
                    "name": perm_data["name"],
                    "description": perm_data["description"],
                    "module": perm_data["module"],
                    "is_active": True,
                    "is_system": False,
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                })
                created_permissions.append(perm_data["code"])
        
        if created_permissions:
            db.flush()
            print(f"✅ 创建部门仓库权限成功: {', '.join(created_permissions)}")
        else:
            print("✅ 所有部门仓库权限已存在")
        
        # 3. 为部门仓库角色分配权限（包括 item.read 权限）
        dept_warehouse_perm_codes = [p["code"] for p in dept_warehouse_permissions] + ["item.read"]
        
        # 获取部门仓库角色ID
        result = db.execute(text("SELECT id FROM roles WHERE code = 'dept_warehouse'"))
        dept_warehouse_role = result.fetchone()
        if dept_warehouse_role:
            role_id = dept_warehouse_role[0]
            
            # 清除现有权限关联
            db.execute(text("DELETE FROM role_permissions WHERE role_id = :role_id"), {"role_id": role_id})
            
            # 重新分配权限
            for perm_code in dept_warehouse_perm_codes:
                result = db.execute(text("SELECT id FROM permissions WHERE code = :code"), {"code": perm_code})
                perm_result = result.fetchone()
                if perm_result:
                    perm_id = perm_result[0]
                    sql = get_insert_ignore_sql(
                        "role_permissions",
                        "role_id, permission_id, created_at",
                        ":role_id, :permission_id, :created_at"
                    )
                    db.execute(text(sql), {
                        "role_id": role_id,
                        "permission_id": perm_id,
                        "created_at": datetime.now()
                    })
            
            print("✅ 部门仓库角色权限分配成功")
            print("ℹ️ Pad 查询物品需要 item.read 权限，已自动授予 dept_warehouse")
        
    except Exception as e:
        print(f"❌ 初始化部门仓库角色失败: {str(e)}")
        raise

def init_dept_warehouse_users(db):
    """初始化部门仓库用户账号"""
    print("初始化部门仓库用户账号...")
    
    try:
        # 获取部门仓库角色ID
        result = db.execute(text("SELECT id FROM roles WHERE code = :code"), {"code": "dept_warehouse"})
        dept_warehouse_role = result.fetchone()
        if not dept_warehouse_role:
            print("❌ 部门仓库角色不存在，请先初始化部门仓库角色")
            return
        
        dept_warehouse_role_id = dept_warehouse_role[0]
        
        # 获取所有部门
        result = db.execute(text("SELECT id, name, code FROM departments WHERE is_active = :is_active"), {"is_active": True})
        departments = result.fetchall()
        
        if not departments:
            print("❌ 没有找到活跃的部门")
            return
        
        # 部门仓库账号命名规则：ir_<部门简写>
        created_users = []
        existing_users = []
        
        for dept in departments:
            dept_id, dept_name, dept_code = dept
            username = f"ir_{dept_code.lower()}"
            
            # 检查用户是否已存在
            result = db.execute(text("SELECT id FROM users WHERE username = :username"), {"username": username})
            existing_user = result.fetchone()
            
            if existing_user:
                # 更新现有用户
                db.execute(text("""
                    UPDATE users 
                    SET role_id = :role_id, department_id = :department_id, is_active = :is_active, updated_at = :updated_at
                    WHERE username = :username
                """), {
                    "role_id": dept_warehouse_role_id,
                    "department_id": dept_id,
                    "is_active": True,
                    "updated_at": datetime.now(),
                    "username": username
                })
                existing_users.append(username)
                print(f"✅ 更新用户: {username} -> {dept_name}")
            else:
                raise RuntimeError(f"用户 {username} 不存在")
        
        if created_users or existing_users:
            print(f"\n🎯 部门仓库用户初始化完成")
            if created_users:
                print(f"新创建用户: {', '.join(created_users)}")
            if existing_users:
                print(f"更新用户: {', '.join(existing_users)}")
            print(f"默认密码: 123456")
            print(f"请提醒用户及时修改密码")
        else:
            print("✅ 所有部门仓库用户已存在")
        
    except Exception as e:
        print(f"❌ 初始化部门仓库用户失败: {str(e)}")
        raise

def init_system_data():
    """初始化系统数据"""
    print(f"使用数据库: {settings.database_url}")
    
    # 使用项目配置的数据库连接
    db = SessionLocal()
    
    try:
        print("开始初始化系统数据...")
        
        # 1. 创建演示部门数据
        print("创建演示部门数据...")
        for dept in DEPARTMENTS:
            sql = get_insert_ignore_sql(
                "departments", 
                "name, code, description, is_active, created_at, updated_at",
                ":name, :code, :description, :is_active, :created_at, :updated_at"
            )
            db.execute(text(sql), {
                "name": dept["name"], 
                "code": dept["code"], 
                "description": dept["description"],
                "is_active": True, 
                "created_at": datetime.now(), 
                "updated_at": datetime.now()
            })
        
        print(f"✓ 创建了 {len(DEPARTMENTS)} 个演示部门")
        
        # 2. 创建预设角色
        roles = [
            {
                "code": "super_admin",
                "name": "Super Admin",
                "description": "System highest privilege, with all functional permissions",
                "is_system": True,
                "is_active": True
            },
            {
                "code": "system_admin", 
                "name": "System Admin",
                "description": "User, role, system configuration management permissions",
                "is_system": True,
                "is_active": True
            },
            {
                "code": "company_director",
                "name": "Company Director",
                "description": "Third-level approval of purchase applications, with the highest business decision-making authority",
                "is_system": True,
                "is_active": True
            },
            {
                "code": "item_admin",
                "name": "Item Admin", 
                "description": "Item, supplier, purchase second-level principle approval permissions",
                "is_system": True,
                "is_active": True
            },
            {
                "code": "dept_manager",
                "name": "Department Manager",
                "description": "Final approval of purchase applications within the department",
                "is_system": True,
                "is_active": True
            },
            {
                "code": "dept_item_admin",
                "name": "Department Item Admin",
                "description": "Department inventory, purchase application management permissions", 
                "is_system": True,
                "is_active": True
            },
            {
                "code": "purchaser",
                "name": "Purchaser",
                "description": "Purchase application submission, supplier management permissions",
                "is_system": True,
                "is_active": True
            },
            {
                "code": "warehouse_keeper",
                "name": "Warehouse Keeper",
                "description": "Inventory operation, Scan In function",
                "is_system": True,
                "is_active": True
            },
            {
                "code": "employee",
                "name": "Employee",
                "description": "Item pickup, view permissions",
                "is_system": True,
                "is_active": True
            }
        ]
        
        print("创建预设角色...")
        for role in roles:
            sql = get_insert_ignore_sql(
                "roles", 
                "code, name, description, is_system, is_active, created_at, updated_at",
                ":code, :name, :description, :is_system, :is_active, :created_at, :updated_at"
            )
            db.execute(text(sql), {
                "code": role["code"], 
                "name": role["name"], 
                "description": role["description"], 
                "is_system": role["is_system"], 
                "is_active": role["is_active"],
                "created_at": datetime.now(), 
                "updated_at": datetime.now()
            })
        
        print(f"✓ 创建了 {len(roles)} 个预设角色")
        
        # 3. 创建权限点
        permissions = [
            # 用户管理权限
            {"code": "user.create", "name": "Create User", "description": "Create new user", "module": "user"},
            {"code": "user.read", "name": "View User", "description": "View user information", "module": "user"},
            {"code": "user.update", "name": "Update User", "description": "Update user information", "module": "user"},
            {"code": "user.delete", "name": "Delete User", "description": "Delete user", "module": "user"},
            {"code": "user.reset_password", "name": "Reset Password", "description": "Reset user password", "module": "user"},
            {"code": "user.manage_status", "name": "Manage Status", "description": "Manage user status", "module": "user"},
            
            # 角色管理权限
            {"code": "role.create", "name": "Create Role", "description": "Create new role", "module": "role"},
            {"code": "role.read", "name": "View Role", "description": "View role information", "module": "role"},
            {"code": "role.update", "name": "Update Role", "description": "Update role information", "module": "role"},
            {"code": "role.delete", "name": "Delete Role", "description": "Delete role", "module": "role"},
            {"code": "role.assign", "name": "Assign Role", "description": "Assign role to user", "module": "role"},
            {"code": "role.revoke", "name": "Revoke Role", "description": "Revoke user role", "module": "role"},
            
            # 权限管理权限
            {"code": "permission.read", "name": "View Permission", "description": "View permission information", "module": "permission"},
            {"code": "permission.assign", "name": "Assign Permission", "description": "Assign permission to role", "module": "permission"},
            
            # 物品管理权限
            {"code": "item.create", "name": "Create Item", "description": "Create new item", "module": "item"},
            {"code": "item.read", "name": "View Item", "description": "View item information", "module": "item"},
            {"code": "item.update", "name": "Update Item", "description": "Update item information", "module": "item"},
            {"code": "item.delete", "name": "Delete Item", "description": "Delete item", "module": "item"},
            {"code": "item.category_manage", "name": "Manage Category", "description": "Manage item category", "module": "item"},
            
            # 库存管理权限
            {"code": "inventory.read", "name": "View Inventory", "description": "View inventory information", "module": "inventory"},
            {"code": "inventory.update", "name": "Update Inventory", "description": "Update inventory information", "module": "inventory"},
            {"code": "inventory.transfer", "name": "Inventory Transfer", "description": "Inventory transfer operation", "module": "inventory"},
            {"code": "inventory.count", "name": "Inventory Count", "description": "Inventory count operation", "module": "inventory"},
            {"code": "inventory.adjust", "name": "Inventory Adjust", "description": "Inventory adjustment operation", "module": "inventory"},
            {"code": "inventory.alert", "name": "Inventory Alert", "description": "Inventory alert management", "module": "inventory"},
            {"code": "inventory.scan_in", "name": "Scan In", "description": "Scan In operation", "module": "inventory"},
            
            # 采购管理权限
            {"code": "purchase.request", "name": "Submit Purchase Request", "description": "Submit purchase request", "module": "purchase"},
            {"code": "purchase.read", "name": "View Purchase", "description": "View purchase information", "module": "purchase"},
            {"code": "purchase.read_all", "name": "View All Purchase", "description": "View all departments purchase information", "module": "purchase"},
            {"code": "purchase.update", "name": "Update Purchase", "description": "Update purchase information", "module": "purchase"},
            {"code": "purchase.delete", "name": "Delete Purchase", "description": "Delete purchase request", "module": "purchase"},
            {"code": "purchase.review", "name": "First Level Review", "description": "Department manager review purchase request", "module": "purchase"},
            {"code": "purchase.principle_approve", "name": "Second Level Principle Approval", "description": "Item admin principle approval", "module": "purchase"},
            {"code": "purchase.approve", "name": "Third Level Approval", "description": "Company director final approval", "module": "purchase"},
            {"code": "purchase.reject", "name": "Reject Purchase", "description": "Reject purchase request", "module": "purchase"},
            {"code": "purchase.execute", "name": "Execute Purchase", "description": "Execute purchase order", "module": "purchase"},
            {"code": "purchase.receive", "name": "Receive Item", "description": "Receive purchased items", "module": "purchase"},
            {"code": "purchase.withdraw", "name": "Withdraw Purchase", "description": "Withdraw purchase request", "module": "purchase"},
            {"code": "purchase.execution_manage", "name": "Purchase Execution Management", "description": "Batch execute purchase requests, manage execution batches", "module": "purchase"},
            
            # 供应商管理权限
            {"code": "supplier.create", "name": "Create Supplier", "description": "Create new supplier", "module": "supplier"},
            {"code": "supplier.read", "name": "View Supplier", "description": "View supplier information", "module": "supplier"},
            {"code": "supplier.update", "name": "Update Supplier", "description": "Update supplier information", "module": "supplier"},
            {"code": "supplier.delete", "name": "Delete Supplier", "description": "Delete supplier", "module": "supplier"},
            {"code": "supplier.price_manage", "name": "Manage Price", "description": "Manage supplier price", "module": "supplier"},
            {"code": "supplier.evaluate", "name": "Evaluate Supplier", "description": "Evaluate supplier", "module": "supplier"},
            
            # 报表分析权限
            {"code": "report.usage", "name": "Usage Report", "description": "View usage report", "module": "report"},
            {"code": "report.inventory", "name": "Inventory Report", "description": "View inventory report", "module": "report"},
            {"code": "report.purchase", "name": "Purchase Report", "description": "View purchase report", "module": "report"},
            {"code": "report.cost", "name": "Cost Report", "description": "View cost report", "module": "report"},
            {"code": "report.admin", "name": "Admin Report", "description": "Manage report", "module": "report"},
            {"code": "report.department", "name": "Department Report", "description": "View department purchase statistics", "module": "report"},
            
            # 部门管理权限
            {"code": "department.create", "name": "Create Department", "description": "Create new department", "module": "department"},
            {"code": "department.read", "name": "View Department", "description": "View department information", "module": "department"},
            {"code": "department.update", "name": "Update Department", "description": "Update department information", "module": "department"},
            {"code": "department.delete", "name": "Delete Department", "description": "Delete department", "module": "department"},
            {"code": "department.manage_users", "name": "Manage Users", "description": "Manage department users", "module": "department"},
            
            # 移动端领取权限
            {"code": "mobile.pickup", "name": "Mobile Pickup", "description": "Mobile item pickup operation", "module": "mobile"},
            {"code": "mobile.scan", "name": "Mobile Scan", "description": "Mobile scan operation", "module": "mobile"},
            {"code": "mobile.offline_sync", "name": "Offline Sync", "description": "Mobile offline data sync", "module": "mobile"},
            
            # 购物车管理权限
            {"code": "cart.add_item", "name": "Add Item to Cart", "description": "Add item to department cart", "module": "cart"},
            {"code": "cart.update_item", "name": "Update Cart Item", "description": "Update item in cart", "module": "cart"},
            {"code": "cart.remove_item", "name": "Remove Cart Item", "description": "Remove item from cart", "module": "cart"},
            {"code": "cart.view", "name": "View Cart", "description": "View department cart content", "module": "cart"},
            {"code": "cart.submit", "name": "Submit Cart Request", "description": "Convert cart to purchase request", "module": "cart"},
            
            # 系统管理权限
            {"code": "system.config", "name": "System Configuration", "description": "System configuration management", "module": "system"},
            {"code": "system.log", "name": "System Log", "description": "View system log", "module": "system"},
            {"code": "system.backup", "name": "System Backup", "description": "System backup", "module": "system"},
            {"code": "system.restore", "name": "System Restore", "description": "System restore", "module": "system"},
            {"code": "system.audit", "name": "System Audit", "description": "System audit", "module": "system"},
            
        ]
        
        print("创建权限点...")
        for perm in permissions:
            sql = get_insert_ignore_sql(
                "permissions", 
                "code, name, description, module, is_active, is_system, created_at, updated_at",
                ":code, :name, :description, :module, :is_active, :is_system, :created_at, :updated_at"
            )
            db.execute(text(sql), {
                "code": perm["code"], 
                "name": perm["name"], 
                "description": perm["description"], 
                "module": perm["module"],
                "is_active": True, 
                "is_system": True, 
                "created_at": datetime.now(), 
                "updated_at": datetime.now()
            })
        
        print(f"✓ 创建了 {len(permissions)} 个权限点 (包含采购执行管理权限)")
        
        # 4. 建立角色权限关联
        role_permissions = {
            "super_admin": [p["code"] for p in permissions],  # 所有权限
            "system_admin": [
                "user.create", "user.read", "user.update", "user.delete", "user.reset_password", "user.manage_status",
                "role.create", "role.read", "role.update", "role.delete", "role.assign", "role.revoke",
                "permission.read", "permission.assign",
                "department.create", "department.read", "department.update", "department.delete", "department.manage_users",
                "system.config", "system.log", "system.audit",
                "item.read"  # 所有用户都可查看物品列表
            ],
            "company_director": [  # 公司主管 - 第三级审批
                "inventory.read",
                "supplier.read",
                "purchase.read", "purchase.read_all",  # 查看采购申请
                "purchase.approve", "purchase.reject",  # 第三级审批权限
                "report.usage", "report.inventory", "report.purchase", "report.cost", "report.admin", "report.department",
                "item.read"  # 所有用户都可查看物品列表
            ],
            "item_admin": [  # 物品管理员 - 第二级原则审批
                "item.create", "item.read", "item.update", "item.delete", "item.category_manage",
                "supplier.read",
                "inventory.read", "inventory.update", "inventory.transfer", "inventory.count", "inventory.adjust", "inventory.alert",
                "purchase.read", "purchase.read_all",  # 查看采购申请
                "purchase.principle_approve", "purchase.reject",  # 第二级原则审批权限
                "purchase.execution_manage",  # 采购执行管理权限
                "supplier.create", "supplier.read", "supplier.update", "supplier.delete", "supplier.price_manage", "supplier.evaluate"
            ],
            "dept_manager": [  # 部门经理 - 第一级复核
                "inventory.read",
                "supplier.read",
                "purchase.read",  # 查看采购申请
                "purchase.review", "purchase.reject",  # 第一级复核权限
                "report.usage", "report.inventory", "report.purchase", "report.cost", "report.department",
                "item.read"  # 所有用户都可查看物品列表
            ],
            "dept_item_admin": [  # 部门物品管理员
                "inventory.read", "inventory.update", "inventory.transfer", "inventory.count", "inventory.adjust",
                "supplier.read",
                "purchase.request", "purchase.read", "purchase.update", "purchase.delete", "purchase.withdraw",
                "purchase.execution_manage",  # 采购执行管理权限
                "cart.add_item", "cart.update_item", "cart.remove_item", "cart.view", "cart.submit",  # 购物车管理权限
                "item.read"  # 所有用户都可查看物品列表
            ],
            "purchaser": [  # 采购员
                "purchase.request", "purchase.read", "purchase.update", "purchase.delete", "purchase.withdraw",
                "supplier.read", "supplier.update", "supplier.evaluate",
                "item.read"  # 所有用户都可查看物品列表
            ],
            "warehouse_keeper": [  # 库管员
                "inventory.read", "inventory.update", "inventory.transfer", "inventory.count", "inventory.adjust", "inventory.scan_in",
                "mobile.pickup", "mobile.scan", "mobile.offline_sync",  # 移动端操作权限
                "item.read"  # 所有用户都可查看物品列表
            ],
            "employee": [  # 普通员工
                "item.read",
                "inventory.read",
                "mobile.pickup"  # 移动端领取权限
            ]
        }
        
        print("建立角色权限关联...")
        for role_code, perm_codes in role_permissions.items():
            # 获取角色ID
            result = db.execute(text("SELECT id FROM roles WHERE code = :code"), {"code": role_code})
            role_result = result.fetchone()
            if not role_result:
                continue
            role_id = role_result[0]
            
            # 获取权限ID列表
            for perm_code in perm_codes:
                result = db.execute(text("SELECT id FROM permissions WHERE code = :code"), {"code": perm_code})
                perm_result = result.fetchone()
                if perm_result:
                    perm_id = perm_result[0]
                    sql = get_insert_ignore_sql(
                        "role_permissions", 
                        "role_id, permission_id, created_at",
                        ":role_id, :permission_id, :created_at"
                    )
                    db.execute(text(sql), {
                        "role_id": role_id, 
                        "permission_id": perm_id, 
                        "created_at": datetime.now()
                    })
        
        print("✓ 建立了角色权限关联")
        print("ℹ️ 所有用户角色都已配置 item.read 权限，可以查看物品列表")
        print("ℹ️ 审批和批量采购相关角色都已配置 purchase.read 权限，可以访问采购申请模块")
        
        # 5. 初始化部门仓库角色和权限
        init_dept_warehouse_role(db)
        
        # 6. 创建管理员账号
        print("创建管理员账号...")
        
        # 获取超级管理员角色ID
        result = db.execute(text("SELECT id FROM roles WHERE code = :code"), {"code": "super_admin"})
        super_admin_role = result.fetchone()
        if not super_admin_role:
            print("❌ 超级管理员角色不存在")
            return
        super_admin_role_id = super_admin_role[0]
        
        # 获取IT部门ID
        result = db.execute(text("SELECT id FROM departments WHERE code = :code"), {"code": "IT"})
        it_dept = result.fetchone()
        if not it_dept:
            print("❌ IT部门不存在")
            return
        it_dept_id = it_dept[0]
        
        # 创建管理员账号
        admin_password = hash_password("admin123")
        sql = get_insert_ignore_sql(
            "users", 
            "username, email, hashed_password, full_name, display_name, phone, employee_id, position, is_active, is_superuser, account_status, password_status, department_id, role_id, created_at, updated_at",
            ":username, :email, :hashed_password, :full_name, :display_name, :phone, :employee_id, :position, :is_active, :is_superuser, :account_status, :password_status, :department_id, :role_id, :created_at, :updated_at"
        )
        db.execute(text(sql), {
            "username": "admin", 
            "email": "<EMAIL>", 
            "hashed_password": admin_password, 
            "full_name": "System Admin", 
            "display_name": "System Admin",
            "phone": "***********", 
            "employee_id": "EMP001", 
            "position": "System Admin", 
            "is_active": True, 
            "is_superuser": True,
            "account_status": "active", 
            "password_status": "normal", 
            "department_id": it_dept_id, 
            "role_id": super_admin_role_id,
            "created_at": datetime.now(), 
            "updated_at": datetime.now()
        })
        
        print("✓ 创建了管理员账号 (用户名: admin, 密码: admin123)")
        
        # 7. 创建演示用户账号
        print("创建演示用户账号...")
        
        demo_users = [
            {
                "username": "system_admin",
                "email": "<EMAIL>",
                "password": "admin123",
                "full_name": "System Admin",
                "display_name": "System Admin",
                "phone": "***********",
                "employee_id": apply_employee_id("IT"),
                "position": "System Admin",
                "department_code": "IT",
                "role_code": "system_admin"
            },
            {
                "username": "company_admin",
                "email": "<EMAIL>",
                "password": "admin123",
                "full_name": "Company Director",
                "display_name": "Company Director",
                "phone": "13800000002",
                "employee_id": apply_employee_id("AD"),
                "position": "Company Director",
                "department_code": "AD",
                "role_code": "company_director"
            },
            {
                "username": "item_admin",
                "email": "<EMAIL>",
                "password": "admin123",
                "full_name": "Item Admin",
                "display_name": "Item Admin",
                "phone": "13800000003",
                "employee_id": apply_employee_id("PUR"),
                "position": "Item Admin",
                "department_code": "PUR",
                "role_code": "item_admin"
            },
            {
                "username": "dept_manager",
                "email": "<EMAIL>",
                "password": "manager123",
                "full_name": "Department Manager",
                "display_name": "Department Manager",
                "phone": "13800000004",
                "employee_id": apply_employee_id("IT"),
                "position": "Department Manager",
                "department_code": "IT",
                "role_code": "dept_manager"
            },
            *[{
                "username": f"mgr_{d['code'].lower()}",
                "email": f"mgr_{d['code'].lower()}@bizlinkspeedy.com",
                "password": "manager123",
                "full_name": "Department Manager",
                "display_name": "Department Manager",
                "phone": "13800000004",
                "employee_id": apply_employee_id(d['code']),
                "position": "Department Manager",
                "department_code": d['code'],
                "role_code": "dept_manager"

            } for d in DEPARTMENTS],
            *[{
                "username": f"pu_{d['code'].lower()}",
                "email": f"pu_{d['code'].lower()}@bizlinkspeedy.com",
                "password": "purchaser123",
                "full_name": "Department Item Admin",
                "display_name": "Department Item Admin",
                "phone": "13800000005",
                "employee_id": apply_employee_id(d['code']),
                "position": "Department Item Admin",
                "department_code": d['code'],
                "role_code": "dept_item_admin"
            } for d in DEPARTMENTS],
            {
                "username": "purchaser",
                "email": "<EMAIL>",
                "password": "purchaser123",
                "full_name": "Purchaser",
                "display_name": "Purchaser",
                "phone": "13800000006",
                "employee_id": apply_employee_id("PUR"),
                "position": "Purchaser",
                "department_code": "PUR",
                "role_code": "purchaser"
            },
            {
                "username": "gr_wh",
                "email": "<EMAIL>",
                "password": "warehouse123",
                "full_name": "Warehouse Keeper",
                "display_name": "Warehouse Keeper",
                "phone": "13800000007",
                "employee_id": apply_employee_id("WH"),
                "position": "Warehouse Keeper",
                "department_code": "WH",
                "role_code": "warehouse_keeper"
            },
            *[{
                "username": f"ir_{d['code'].lower()}",
                "email": f"emp_{d['code'].lower()}@bizlinkspeedy.com",
                "password": "123456",
                "full_name": "Employee",
                "display_name": "Employee",
                "phone": "13800000008",
                "employee_id": apply_employee_id(d['code']),
                "position": "Employee",
                "department_code": d['code'],
                "role_code": "employee"
            } for d in DEPARTMENTS],
        ]
        
        for user in demo_users:
            # 获取部门ID
            result = db.execute(text("SELECT id FROM departments WHERE code = :code"), {"code": user["department_code"]})
            dept_result = result.fetchone()
            if not dept_result:
                continue
            dept_id = dept_result[0]
            
            # 获取角色ID
            result = db.execute(text("SELECT id FROM roles WHERE code = :code"), {"code": user["role_code"]})
            role_result = result.fetchone()
            if not role_result:
                continue
            role_id = role_result[0]
            
            # 创建用户
            hashed_pwd = hash_password(user["password"])
            sql = get_insert_ignore_sql(
                "users", 
                "username, email, hashed_password, full_name, display_name, phone, employee_id, position, is_active, is_superuser, account_status, password_status, department_id, role_id, created_at, updated_at",
                ":username, :email, :hashed_password, :full_name, :display_name, :phone, :employee_id, :position, :is_active, :is_superuser, :account_status, :password_status, :department_id, :role_id, :created_at, :updated_at"
            )
            db.execute(text(sql), {
                "username": user["username"], 
                "email": user["email"], 
                "hashed_password": hashed_pwd, 
                "full_name": user["full_name"], 
                "display_name": user["display_name"],
                "phone": user["phone"], 
                "employee_id": user["employee_id"], 
                "position": user["position"], 
                "is_active": True, 
                "is_superuser": False,
                "account_status": "active", 
                "password_status": "normal", 
                "department_id": dept_id, 
                "role_id": role_id,
                "created_at": datetime.now(), 
                "updated_at": datetime.now()
            })
        
        print(f"✓ 创建了 {len(demo_users)} 个演示用户账号")
        
        # 8. 初始化部门仓库用户账号
        init_dept_warehouse_users(db)
        
        # 提交更改
        db.commit()
        print("\n🎉 系统初始化完成!")
        print("\n📋 初始化结果:")
        print(f"  - 部门数据: {len(DEPARTMENTS)} 个")
        print(f"  - 预设角色: {len(roles)} 个")
        print(f"  - 权限点: {len(permissions)} 个")
        print(f"  - 演示用户: {len(demo_users) + 1} 个")
        print(f"  - 部门仓库角色: 1 个")
        print(f"  - 部门仓库用户: {len(DEPARTMENTS)} 个")
        print(f"  - 采购执行权限: 已配置物品管理员和部门物品管理员")
        print("\n🔑 登录账号:")
        print("  - 超级管理员: admin/admin123")
        print("  - 系统管理员: system_admin/admin123")
        print("  - 公司主管: company_admin/admin123")
        print("  - 物品管理员: item_admin/admin123")
        print("  - 部门经理: dept_manager/admin123")
        print("  - 部门物品管理员: dept_item_admin/admin123")
        print("  - 采购员: purchaser/admin123")
        print("  - 库管员: warehouse_keeper/admin123")
        print("  - 普通员工: employee/admin123")
        print("  - 部门仓库用户: ir_<部门代码>/123456")
        print("\n📋 新增功能说明:")
        print("  - 采购执行管理: 物品管理员和部门物品管理员可批量执行已批准的采购申请")
        print("  - 执行批次管理: 支持执行批次创建、查看、管理")
        print("  - 采购汇总导出: 支持多种格式的采购单导出")
        print("  - 物品查看权限: 所有用户角色都可查看物品列表")
        print("  - 采购申请访问: 审批和批量采购相关角色都可访问采购申请模块")
        
    except Exception as e:
        print(f"❌ 初始化过程中出错: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_system_data() 