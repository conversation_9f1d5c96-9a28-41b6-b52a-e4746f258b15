#!/usr/bin/env python3
"""
简单测试ItemPriceService
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.item_price_service import ItemPriceService
from app.core.database import get_db

def test_item_price_service():
    """测试ItemPriceService基本功能"""
    print("Testing ItemPriceService...")
    
    # 获取数据库会话
    db = next(get_db())
    
    try:
        # 创建服务实例
        service = ItemPriceService(db)
        print("✅ ItemPriceService created successfully")
        
        # 测试获取价格信息（使用示例数据）
        # 注意：这里需要实际的数据库中有数据才能测试
        print("✅ Service is ready for use")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    test_item_price_service()
