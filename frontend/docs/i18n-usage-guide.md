# i18n 国际化使用指南

## 概述

本项目已集成 `react-i18next` 国际化框架，支持中英文双语切换，专为工业生产领域设计，使用专业术语。

## 技术架构

- **框架**: react-i18next + i18next
- **语言检测**: i18next-browser-languagedetector
- **支持语言**: 中文 (zh) / 英文 (en)
- **默认语言**: 英文
- **语言切换位置**: Admin端用户下拉菜单

## 文件结构

```
frontend/src/shared/
├── config/
│   ├── i18n.ts              # i18n配置文件
│   └── locales/             # 语言包目录
│       ├── zh.json          # 中文语言包
│       └── en.json          # 英文语言包
└── components/
    └── I18nDemo.tsx         # 使用示例组件
```

## 使用方法

### 1. 在组件中使用翻译

```tsx
import React from 'react';
import { useTranslation } from 'react-i18next';

const MyComponent: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div>
      <h1>{t('navigation.dashboard')}</h1>
      <button>{t('common.save')}</button>
      <p>{t('messages.operationSuccess')}</p>
    </div>
  );
};
```

### 2. 语言切换

**Admin端**: 点击右上角用户头像 → 选择语言 → 中文/English

**其他客户端**: 默认使用英文，不支持语言切换

### 3. 动态语言切换

```tsx
import { useTranslation } from 'react-i18next';

const MyComponent: React.FC = () => {
  const { i18n } = useTranslation();

  const changeToEnglish = () => {
    i18n.changeLanguage('en');
  };

  const changeToChinese = () => {
    i18n.changeLanguage('zh');
  };

  return (
    <div>
      <button onClick={changeToEnglish}>English</button>
      <button onClick={changeToChinese}>中文</button>
    </div>
  );
};
```

## 语言包结构

### 通用术语 (common)
- 基础操作: save, cancel, delete, edit, add
- 状态: loading, noData, success, error
- 时间: createdAt, updatedAt

### 导航菜单 (navigation)
- dashboard: 仪表板
- inventory: 库存管理
- purchase: 采购管理
- suppliers: 供应商管理

### 库存管理 (inventory)
- itemName: 物料名称
- itemCode: 物料编码
- specification: 规格型号
- stockQuantity: 库存数量
- unit: 计量单位

### 采购管理 (purchase)
- request: 采购申请
- order: 采购订单
- approval: 审批
- status: 状态

### 供应商管理 (supplier)
- supplierName: 供应商名称
- contactPerson: 联系人
- creditRating: 信用等级

### 系统管理 (system)
- systemName: 系统名称
- login: 登录
- logout: 退出

## 最佳实践

### 1. 命名规范
- 使用点分隔的层级结构: `category.subcategory.key`
- 键名使用驼峰命名: `itemName`, `stockQuantity`
- 避免过深的嵌套: 最多3-4层

### 2. 文本组织
- 按功能模块分组
- 通用术语放在 `common` 下
- 特定业务术语放在对应模块下

### 3. 动态内容
```tsx
// 使用插值
{t('inventory.stockQuantity')}: {quantity} {t('units.piece')}

// 使用复数形式
{t('inventory.itemCount', { count: itemCount })}
```

### 4. 日期和数字本地化
```tsx
// 日期本地化
{new Date(date).toLocaleDateString(i18n.language === 'zh' ? 'zh-CN' : 'en-US')}

// 数字本地化
{amount.toLocaleString(i18n.language === 'zh' ? 'zh-CN' : 'en-US')}
```

## 添加新语言

### 1. 创建语言包文件
```json
// frontend/src/shared/config/locales/ja.json
{
  "common": {
    "save": "保存",
    "cancel": "キャンセル"
  }
}
```

### 2. 更新i18n配置
```typescript
// frontend/src/shared/config/i18n.ts
import ja from './locales/ja.json';

i18n.init({
  resources: {
    en: { translation: en },
    zh: { translation: zh },
    ja: { translation: ja }  // 添加新语言
  }
});
```

### 3. 更新用户菜单
```tsx
// frontend/src/shared/components/DashboardLayout.tsx
{
  key: 'ja',
  label: '日本語',
  onClick: () => i18n.changeLanguage('ja'),
}
```

## 常见问题

### 1. 翻译键不存在
- 检查语言包文件是否正确导入
- 确认键名拼写是否正确
- 使用 `t('key', 'fallback text')` 提供默认值

### 2. 语言切换不生效
- 检查组件是否正确使用 `useTranslation` hook
- 确认语言包文件格式正确
- 检查浏览器控制台是否有错误

### 3. 日期格式问题
- 使用 `toLocaleDateString` 和 `toLocaleString`
- 传入正确的语言参数
- 考虑时区问题

## 测试

### 1. 手动测试
- 在Admin端点击用户头像 → 选择语言
- 检查所有文本是否正确显示
- 检查日期、数字格式是否符合语言习惯
- 验证特殊字符和标点符号

### 2. 自动化测试
```tsx
import { render, screen } from '@testing-library/react';
import { I18nextProvider } from 'react-i18next';
import i18n from './i18n';

test('renders in Chinese', () => {
  i18n.changeLanguage('zh');
  render(
    <I18nextProvider i18n={i18n}>
      <MyComponent />
    </I18nextProvider>
  );
  expect(screen.getByText('保存')).toBeInTheDocument();
});
```

## 维护和更新

### 1. 添加新翻译
- 在对应的语言包文件中添加新键值对
- 确保所有语言包都有对应的翻译
- 更新使用指南文档

### 2. 术语统一
- 建立术语表，确保翻译一致性
- 定期审查和更新专业术语
- 与业务专家确认关键术语翻译

### 3. 性能优化
- 按需加载语言包
- 使用懒加载减少初始包大小
- 缓存翻译结果
