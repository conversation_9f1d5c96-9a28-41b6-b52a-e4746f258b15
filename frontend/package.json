{"name": "bizlinkspeedy-idm-frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-webcam": "^1.1.0", "antd": "^5.27.0", "axios": "^1.6.2", "dayjs": "^1.11.13", "html5-qrcode": "^2.3.8", "i18next": "^23.7.16", "i18next-browser-languagedetector": "^7.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.5.0", "react-qr-code": "^2.0.18", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "recharts": "^3.1.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "@types/fs-extra": "^11.0.4", "@types/jest": "^27.5.2", "fs-extra": "^11.3.1"}}