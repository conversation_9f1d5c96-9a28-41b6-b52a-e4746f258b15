/**
 * 图片工具函数
 * 提供各种网络图片作为fallback选项
 */

// 默认商品图片 - 使用Unsplash的高质量图片
export const DEFAULT_PRODUCT_IMAGE = 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=300&fit=crop&crop=center&auto=format&q=80';

// 备用图片选项
export const FALLBACK_IMAGES = {
  // 商品相关
  product: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
  product2: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
  product3: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
  
  // 电子产品
  electronics: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
  
  // 服装
  clothing: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
  
  // 食品
  food: 'https://images.unsplash.com/photo-1504674900240-9c88349d6053?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
  
  // 书籍
  book: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
  
  // 通用占位符
  placeholder: 'https://via.placeholder.com/300x300/f0f0f0/999999?text=暂无图片',
  placeholder2: 'https://via.placeholder.com/300x300/e8f4fd/1890ff?text=暂无图片',
  placeholder3: 'https://via.placeholder.com/300x300/f6ffed/52c41a?text=暂无图片',
};

/**
 * 根据分类获取合适的fallback图片
 * @param categoryName 分类名称
 * @returns 合适的fallback图片URL
 */
export const getFallbackImageByCategory = (categoryName?: string): string => {
  if (!categoryName) {
    return DEFAULT_PRODUCT_IMAGE;
  }
  
  const category = categoryName.toLowerCase();
  
  if (category.includes('电子') || category.includes('数码') || category.includes('电脑') || category.includes('手机')) {
    return FALLBACK_IMAGES.electronics;
  }
  
  if (category.includes('服装') || category.includes('衣服') || category.includes('鞋') || category.includes('包')) {
    return FALLBACK_IMAGES.clothing;
  }
  
  if (category.includes('食品') || category.includes('饮料') || category.includes('零食') || category.includes('水果')) {
    return FALLBACK_IMAGES.food;
  }
  
  if (category.includes('书籍') || category.includes('图书') || category.includes('杂志')) {
    return FALLBACK_IMAGES.book;
  }
  
  return DEFAULT_PRODUCT_IMAGE;
};

/**
 * 获取随机fallback图片
 * @returns 随机的fallback图片URL
 */
export const getRandomFallbackImage = (): string => {
  const images = Object.values(FALLBACK_IMAGES);
  const randomIndex = Math.floor(Math.random() * images.length);
  return images[randomIndex];
};

/**
 * 验证图片URL是否有效
 * @param url 图片URL
 * @returns Promise<boolean>
 */
export const validateImageUrl = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
  });
};

/**
 * 获取图片的合适尺寸URL
 * @param originalUrl 原始图片URL
 * @param width 目标宽度
 * @param height 目标高度
 * @returns 调整尺寸后的图片URL
 */
export const getResizedImageUrl = (originalUrl: string, width: number, height: number): string => {
  // 如果是Unsplash图片，可以添加尺寸参数
  if (originalUrl.includes('unsplash.com')) {
    return `${originalUrl}&w=${width}&h=${height}&fit=crop&crop=center`;
  }
  
  // 如果是其他图片，返回原URL
  return originalUrl;
}; 