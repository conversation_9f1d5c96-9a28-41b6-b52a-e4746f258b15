/**
 * 汇率管理工具函数
 * 提供汇率计算、价格转换、格式化等通用功能
 */

import { Decimal } from 'decimal.js';

// 支持的货币信息
export const SUPPORTED_CURRENCIES = {
  USD: { name: '美元', symbol: '$', is_base: true },
  CNY: { name: '人民币', symbol: '¥', is_base: false },
  EUR: { name: '欧元', symbol: '€', is_base: false },
  JPY: { name: '日元', symbol: '¥', is_base: false },
  GBP: { name: '英镑', symbol: '£', is_base: false },
  KRW: { name: '韩元', symbol: '₩', is_base: false },
  SGD: { name: '新加坡元', symbol: 'S$', is_base: false },
  HKD: { name: '港币', symbol: 'HK$', is_base: false },
  MYR: { name: '马来西亚林吉特', symbol: 'RM', is_base: false },
} as const;

export type CurrencyCode = keyof typeof SUPPORTED_CURRENCIES;

/**
 * 将指定货币的价格转换为美元
 * @param amount 原币价格
 * @param currencyCode 货币代码
 * @param exchangeRate 汇率 (1 USD = exchangeRate 外币)
 * @returns 转换后的美元价格
 */
export function convertPriceToUsd(
  amount: number | string,
  currencyCode: CurrencyCode,
  exchangeRate: number | string
): number {
  if (currencyCode === 'USD') {
    return Number(amount);
  }

  const rate = new Decimal(exchangeRate);
  if (rate.lte(0)) {
    throw new Error('汇率必须大于0');
  }

  const price = new Decimal(amount);
  // 转换公式：美元价格 = 原币价格 ÷ 汇率
  const usdPrice = price.div(rate);

  // 根据金额大小确定精度
  if (usdPrice.gte(1)) {
    // 大于1美元，保留2位小数
    return Number(usdPrice.toDecimalPlaces(2));
  } else {
    // 小于1美元，保留4位小数
    return Number(usdPrice.toDecimalPlaces(4));
  }
}

/**
 * 将美元价格转换为指定货币
 * @param usdAmount 美元价格
 * @param currencyCode 目标货币代码
 * @param exchangeRate 汇率 (1 USD = exchangeRate 外币)
 * @returns 转换后的目标货币价格
 */
export function convertUsdToCurrency(
  usdAmount: number | string,
  currencyCode: CurrencyCode,
  exchangeRate: number | string
): number {
  if (currencyCode === 'USD') {
    return Number(usdAmount);
  }

  const rate = new Decimal(exchangeRate);
  if (rate.lte(0)) {
    throw new Error('汇率必须大于0');
  }

  const usdPrice = new Decimal(usdAmount);
  // 转换公式：目标货币价格 = 美元价格 × 汇率
  const targetPrice = usdPrice.mul(rate);

  // 根据金额大小确定精度
  if (targetPrice.gte(1)) {
    // 大于1，保留2位小数
    return Number(targetPrice.toDecimalPlaces(2));
  } else {
    // 小于1，保留4位小数
    return Number(targetPrice.toDecimalPlaces(4));
  }
}

/**
 * 格式化价格显示
 * @param price 价格
 * @param currencyCode 货币代码
 * @returns 格式化后的价格字符串
 */
export function formatPriceDisplay(price: number | string, currencyCode: CurrencyCode = 'USD'): string {
  const currency = SUPPORTED_CURRENCIES[currencyCode];
  const symbol = currency?.symbol || currencyCode;
  const numPrice = Number(price);

  if (numPrice >= 1) {
    // 大于1，显示2位小数
    return `${symbol}${numPrice.toFixed(2)}`;
  } else {
    // 小于1，显示4位小数
    return `${symbol}${numPrice.toFixed(4)}`;
  }
}

/**
 * 计算总价格（支持多货币）
 * @param unitPrice 单价
 * @param quantity 数量
 * @param currencyCode 货币代码
 * @param exchangeRate 汇率（可选）
 * @returns 总价格信息
 */
export function calculateTotalPriceWithCurrency(
  unitPrice: number,
  quantity: number,
  currencyCode: CurrencyCode,
  exchangeRate?: number
): {
  totalPrice: number;
  formattedPrice: string;
  usdPrice?: number;
} {
  // 计算原币总价
  const totalPrice = unitPrice * quantity;

  // 格式化显示
  const formattedPrice = formatPriceDisplay(totalPrice, currencyCode);

  // 如果提供了汇率，计算美元价格
  let usdPrice: number | undefined;
  if (exchangeRate && currencyCode !== 'USD') {
    usdPrice = convertPriceToUsd(totalPrice, currencyCode, exchangeRate);
  }

  return {
    totalPrice,
    formattedPrice,
    usdPrice,
  };
}

/**
 * 验证汇率数据有效性
 * @param currencyCode 货币代码
 * @param rate 汇率值
 * @param effectiveMonth 生效月份
 * @returns 验证结果
 */
export function validateExchangeRateData(
  currencyCode: string,
  rate: number,
  effectiveMonth: string
): { isValid: boolean; errorMessage: string } {
  // 验证货币代码
  if (!SUPPORTED_CURRENCIES[currencyCode as CurrencyCode]) {
    return {
      isValid: false,
      errorMessage: `不支持的货币代码: ${currencyCode}`,
    };
  }

  // 验证汇率值
  if (rate <= 0) {
    return {
      isValid: false,
      errorMessage: '汇率必须大于0',
    };
  }

  if (rate > 10000) {
    return {
      isValid: false,
      errorMessage: '汇率值过大，请检查数据',
    };
  }

  // 验证生效月份
  const month = new Date(effectiveMonth);
  const today = new Date();
  
  if (month > today) {
    return {
      isValid: false,
      errorMessage: '生效月份不能是未来日期',
    };
  }

  // 验证月份格式（应该是月初）
  if (month.getDate() !== 1) {
    return {
      isValid: false,
      errorMessage: '生效月份应该是月初（1号）',
    };
  }

  return {
    isValid: true,
    errorMessage: '',
  };
}

/**
 * 获取货币信息
 * @param currencyCode 货币代码
 * @returns 货币信息
 */
export function getCurrencyInfo(currencyCode: CurrencyCode) {
  return SUPPORTED_CURRENCIES[currencyCode] || {
    name: currencyCode,
    symbol: currencyCode,
    is_base: false,
  };
}

/**
 * 计算价格变化百分比
 * @param oldPrice 旧价格
 * @param newPrice 新价格
 * @param currencyCode 货币代码
 * @returns 变化信息
 */
export function calculatePriceChangePercentage(
  oldPrice: number,
  newPrice: number,
  currencyCode: CurrencyCode = 'USD'
): { changePercentage: number; changeDescription: string } {
  if (oldPrice === 0) {
    return {
      changePercentage: 0,
      changeDescription: '无变化',
    };
  }

  const changePercentage = ((newPrice - oldPrice) / oldPrice) * 100;

  if (changePercentage > 0) {
    return {
      changePercentage,
      changeDescription: `上涨 ${changePercentage.toFixed(2)}%`,
    };
  } else if (changePercentage < 0) {
    return {
      changePercentage,
      changeDescription: `下降 ${Math.abs(changePercentage).toFixed(2)}%`,
    };
  } else {
    return {
      changePercentage: 0,
      changeDescription: '无变化',
    };
  }
}

/**
 * 估算月度成本
 * @param unitPrice 单价
 * @param monthlyUsage 月度使用量
 * @param currencyCode 货币代码
 * @param exchangeRate 汇率（可选）
 * @returns 月度成本估算结果
 */
export function estimateMonthlyCost(
  unitPrice: number,
  monthlyUsage: number,
  currencyCode: CurrencyCode,
  exchangeRate?: number
): {
  monthlyCost: number;
  formattedCost: string;
  usdCost?: number;
  usdFormatted?: string;
  currencyCode: CurrencyCode;
  monthlyUsage: number;
} {
  // 计算月度成本
  const monthlyCost = unitPrice * monthlyUsage;

  // 格式化显示
  const formattedCost = formatPriceDisplay(monthlyCost, currencyCode);

  // 如果提供了汇率，计算美元成本
  let usdCost: number | undefined;
  let usdFormatted: string | undefined;
  
  if (exchangeRate && currencyCode !== 'USD') {
    usdCost = convertPriceToUsd(monthlyCost, currencyCode, exchangeRate);
    usdFormatted = formatPriceDisplay(usdCost, 'USD');
  }

  return {
    monthlyCost,
    formattedCost,
    usdCost,
    usdFormatted,
    currencyCode,
    monthlyUsage,
  };
}

/**
 * 格式化汇率显示
 * @param rate 汇率值
 * @param fromCurrency 源货币
 * @param toCurrency 目标货币
 * @returns 格式化的汇率字符串
 */
export function formatExchangeRate(
  rate: number,
  fromCurrency: CurrencyCode = 'USD',
  toCurrency: CurrencyCode = 'CNY'
): string {
  const fromInfo = getCurrencyInfo(fromCurrency);
  const toInfo = getCurrencyInfo(toCurrency);
  
  return `1 ${fromInfo.symbol}${fromCurrency} = ${rate.toFixed(6)} ${toInfo.symbol}${toCurrency}`;
}

/**
 * 检查货币是否为基础货币
 * @param currencyCode 货币代码
 * @returns 是否为基础货币
 */
export function isBaseCurrency(currencyCode: CurrencyCode): boolean {
  return SUPPORTED_CURRENCIES[currencyCode]?.is_base || false;
}

/**
 * 获取所有支持的货币代码
 * @returns 货币代码数组
 */
export function getSupportedCurrencyCodes(): CurrencyCode[] {
  return Object.keys(SUPPORTED_CURRENCIES) as CurrencyCode[];
}

/**
 * 获取非基础货币代码列表
 * @returns 非基础货币代码数组
 */
export function getNonBaseCurrencyCodes(): CurrencyCode[] {
  return getSupportedCurrencyCodes().filter(code => !isBaseCurrency(code));
}
