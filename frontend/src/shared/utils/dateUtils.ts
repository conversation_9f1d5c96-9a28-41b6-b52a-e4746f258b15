import dayjs from 'dayjs';

/**
 * 格式化日期
 * @param date 日期字符串或Date对象
 * @param format 格式化字符串，支持dayjs格式
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | Date, format: string = 'YYYY-MM-DD'): string => {
  if (!date) return '';
  
  try {
    return dayjs(date).format(format);
  } catch (error) {
    console.error('Date formatting error:', error);
    return String(date);
  }
};

/**
 * 格式化日期时间
 * @param date 日期字符串或Date对象
 * @returns 格式化后的日期时间字符串 (YYYY-MM-DD HH:mm:ss)
 */
export const formatDateTime = (date: string | Date): string => {
  return formatDate(date, 'YYYY-MM-DD HH:mm:ss');
};

/**
 * 格式化日期（短格式）
 * @param date 日期字符串或Date对象
 * @returns 格式化后的日期字符串 (YYYY-MM-DD)
 */
export const formatDateShort = (date: string | Date): string => {
  return formatDate(date, 'YYYY-MM-DD');
};

/**
 * 获取相对时间描述
 * @param date 日期字符串或Date对象
 * @returns 相对时间描述
 */
export const getRelativeTime = (date: string | Date): string => {
  if (!date) return '';
  
  try {
    const now = dayjs();
    const targetDate = dayjs(date);
    const diffMinutes = now.diff(targetDate, 'minute');
    
    if (diffMinutes < 1) return '刚刚';
    if (diffMinutes < 60) return `${diffMinutes}分钟前`;
    
    const diffHours = now.diff(targetDate, 'hour');
    if (diffHours < 24) return `${diffHours}小时前`;
    
    const diffDays = now.diff(targetDate, 'day');
    if (diffDays < 7) return `${diffDays}天前`;
    
    return formatDate(date, 'MM-DD');
  } catch (error) {
    console.error('Relative time calculation error:', error);
    return formatDate(date);
  }
};

/**
 * 检查日期是否有效
 * @param date 日期字符串或Date对象
 * @returns 是否为有效日期
 */
export const isValidDate = (date: string | Date): boolean => {
  if (!date) return false;
  
  try {
    return dayjs(date).isValid();
  } catch (error) {
    return false;
  }
};

/**
 * 获取当前时间戳
 * @returns 当前时间戳
 */
export const getCurrentTimestamp = (): number => {
  return Date.now();
};

/**
 * 获取当前日期字符串
 * @returns 当前日期字符串 (YYYY-MM-DD)
 */
export const getCurrentDate = (): string => {
  return dayjs().format('YYYY-MM-DD');
};

/**
 * 获取当前日期时间字符串
 * @returns 当前日期时间字符串 (YYYY-MM-DD HH:mm:ss)
 */
export const getCurrentDateTime = (): string => {
  return dayjs().format('YYYY-MM-DD HH:mm:ss');
};
