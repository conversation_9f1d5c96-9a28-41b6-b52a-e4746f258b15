/**
 * 通用错误处理函数
 * 用于处理API错误响应，提取并格式化错误信息
 */
export const handleApiError = (error: any, defaultMessage: string = '操作失败'): string => {
  if (error.response?.data?.detail) {
    const detail = error.response.data.detail;
    if (Array.isArray(detail)) {
      // 处理字段验证错误
      const errorMessages = detail.map((err: any) => {
        if (err.loc && err.msg) {
          const field = err.loc[err.loc.length - 1];
          return `${field}: ${err.msg}`;
        }
        return err.msg;
      });
      return errorMessages.join(', ');
    } else {
      return detail;
    }
  }
  return defaultMessage;
};

/**
 * 显示API错误消息
 * 结合handleApiError和message.error
 */
export const showApiError = (error: any, defaultMessage: string = '操作失败'): void => {
  const { message } = require('antd');
  message.error(handleApiError(error, defaultMessage));
}; 