{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "loading": "Loading...", "noData": "No Data", "confirm": "Confirm", "back": "Back", "submit": "Submit", "reset": "Reset", "export": "Export", "import": "Import", "refresh": "Refresh", "close": "Close", "open": "Open", "view": "View", "details": "Details", "status": "Status", "operation": "Operation", "remark": "Remark", "description": "Description", "name": "Name", "code": "Code", "type": "Type", "category": "Category", "quantity": "Quantity", "amount": "Amount", "price": "Unit Price", "total": "Total", "date": "Date", "time": "Time", "createdAt": "Created At", "updatedAt": "Updated At", "createdBy": "Created By", "updatedBy": "Updated By", "chinese": "中文", "english": "English", "all": "All", "active": "Active", "inactive": "Inactive", "paginationInfo": "Page {{start}}-{{end}} of {{total}} items", "totalRecords": "Total {{total}} records"}, "navigation": {"dashboard": "Dashboard", "inventory": "Dept. Inventory", "purchase": "Purchase", "suppliers": "Supplier", "users": "User", "roles": "Role", "departments": "Department", "categories": "Category", "reports": "Reports & Statistics", "settings": "System Settings", "items": "Items", "primaryCategories": "Primary Categories", "secondaryCategories": "Secondary Categories", "system": "System", "imageManagement": "Image", "purchaseCart": "Shopping Cart"}, "inventory": {"itemName": "Material Name", "itemCode": "Material Code", "specification": "Specification", "unit": "Unit of Measure", "stockQuantity": "Stock Quantity", "availableQuantity": "Available Quantity", "reservedQuantity": "Reserved Quantity", "minStock": "Minimum Stock", "maxStock": "Maximum Stock", "safetyStock": "Safety Stock", "location": "Storage Location", "warehouse": "Warehouse", "shelf": "<PERSON><PERSON>", "batch": "<PERSON><PERSON>", "expiryDate": "Expiry Date", "manufacturer": "Manufacturer", "supplier": "Supplier", "purchasePrice": "Purchase Price", "standardPrice": "Standard Price", "costPrice": "Cost Price", "inventoryStatus": "Inventory Status", "inStock": "In Stock", "outOfStock": "Out of Stock", "overStock": "Overstocked", "normal": "Normal", "warning": "Warning", "critical": "Critical"}, "item": {"searchPlaceholder": "Search item name, code, description", "statusPlaceholder": "Item status", "purchasable": "Purchasable", "purchasablePlaceholder": "Purchasable status", "notPurchasable": "Not purchasable", "addItem": "Add Item", "noItemsData": "No items data"}, "purchase": {"request": "Purchase Request", "order": "Purchase Order", "requisition": "Requisition", "approval": "Approval", "approve": "Approve", "reject": "Reject", "pending": "Pending", "processing": "Processing", "completed": "Completed", "cancelled": "Cancelled", "urgent": "<PERSON><PERSON>", "normal": "Normal", "low": "Low Priority", "medium": "Medium Priority", "high": "High Priority", "requestor": "Requestor", "approver": "Approver", "department": "Requesting Department", "budget": "Budget", "totalAmount": "Total Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "deliveryDate": "Delivery Date", "expectedDate": "Expected Date", "actualDate": "Actual Date", "paymentTerms": "Payment Terms", "deliveryTerms": "Delivery Terms"}, "supplier": {"supplierName": "Supplier Name", "supplierCode": "Supplier Code", "contactPerson": "Contact Person", "contactPhone": "Contact Phone", "contactEmail": "Contact Email", "address": "Address", "businessLicense": "Business License", "taxNumber": "Tax Number", "bankAccount": "Bank Account", "creditRating": "Credit Rating", "cooperationStatus": "Cooperation Status", "active": "Active", "inactive": "Inactive", "suspended": "Suspended", "terminated": "Terminated", "evaluationScore": "Evaluation Score", "deliveryPerformance": "Delivery Performance", "qualityPerformance": "Quality Performance", "priceCompetitiveness": "Price Competitiveness"}, "user": {"username": "Username", "password": "Password", "fullName": "Full Name", "email": "Email", "phone": "Phone", "employeeId": "Employee ID", "position": "Position", "role": "Role", "department": "Department", "status": "Status", "active": "Active", "inactive": "Inactive", "locked": "Locked", "lastLogin": "Last Login", "loginCount": "Login <PERSON>", "permissions": "Permissions", "superAdmin": "Super Administrator", "admin": "Administrator", "user": "User", "operator": "Operator", "profile": "Profile", "logout": "Logout", "searchPlaceholder": "Search username, name, email", "selectDepartment": "Select Department", "selectRole": "Select Role", "accountStatus": "Account Status", "statusActive": "Active", "statusDisabled": "Disabled", "statusLocked": "Locked", "statusPending": "Pending", "passwordStatus": "Password Status", "passwordStatusNormal": "Normal", "passwordStatusNeedReset": "Need Reset", "passwordStatusTemporary": "Temporary", "addUser": "Add User", "editUser": "Edit User", "viewDetails": "View Details", "assignRole": "Assign Role", "resetPassword": "Reset Password", "disable": "Disable", "enable": "Enable", "userDetails": "User Details", "newPassword": "New Password", "setAsTemporaryPassword": "Set as temporary password (user needs to change on next login)", "roleRequired": "Please select a role", "cannotAssignRoleToSelf": "Cannot assign role to yourself", "unknownRole": "Unknown Role", "noRole": "No Role", "employeeQRCode": "Employee QR Code", "userQRCode": "User QR Code", "usernameRequired": "Please enter username", "usernameLengthRule": "Username length 3-50 characters", "emailRequired": "Please enter email", "emailValid": "Please enter a valid email", "passwordRequired": "Please enter password", "passwordMinLength": "Password must be at least 6 characters", "newPasswordRequired": "Please enter new password"}, "login": {"usernameRequired": "Please enter username!", "usernameMinLength": "<PERSON><PERSON><PERSON> must be at least 3 characters!", "passwordRequired": "Please enter password!", "passwordMinLength": "Password must be at least 6 characters!", "loggingIn": "Logging in...", "testAccounts": "Test Accounts", "adminAccount": "Administrator", "userAccount": "Regular User"}, "system": {"systemName": "Indirect Material Management System", "version": "Version", "login": "<PERSON><PERSON>", "logout": "Logout", "profile": "Profile", "changePassword": "Change Password", "language": "Language", "theme": "Theme", "notifications": "Notifications", "help": "Help", "about": "About", "feedback": "<PERSON><PERSON><PERSON>", "support": "Technical Support", "companyName": "BizLink Speedy Pte. Ltd."}, "messages": {"saveSuccess": "Saved successfully", "saveFailed": "Save failed", "deleteSuccess": "Deleted successfully", "deleteFailed": "Delete failed", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "confirmDelete": "Confirm deletion?", "confirmOperation": "Confirm this operation?", "dataLoading": "Loading data...", "noPermission": "No permission to perform this operation", "networkError": "Network error, please try again later", "serverError": "Server error, please contact administrator", "validationError": "Input validation failed, please check input content", "getPreferredPricesFailed": "Failed to get preferred supplier prices", "getItemListFailed": "Failed to get item list", "loadUserListFailed": "Failed to load user list", "loadRoleListFailed": "Failed to load role list", "loadDepartmentListFailed": "Failed to load department list", "userUpdateSuccess": "User updated successfully", "userCreateSuccess": "User created successfully", "passwordResetSuccess": "Password reset successfully", "passwordResetFailed": "Password reset failed", "roleAssignSuccess": "Role assigned successfully", "roleAssignFailed": "Role assignment failed", "userStatusUpdateSuccess": "User status updated successfully", "statusUpdateFailed": "Status update failed"}, "units": {"piece": "Piece", "box": "Box", "bag": "Bag", "bottle": "<PERSON><PERSON>", "roll": "Roll", "meter": "<PERSON>er", "centimeter": "Centimeter", "millimeter": "Millimeter", "kilogram": "Kilogram", "gram": "Gram", "ton": "Ton", "liter": "Liter", "milliliter": "Milliliter", "squareMeter": "Square Meter", "cubicMeter": "<PERSON><PERSON><PERSON>", "set": "Set", "pair": "Pair", "dozen": "<PERSON><PERSON>"}, "status": {"draft": "Draft", "submitted": "Submitted", "approved": "Approved", "rejected": "Rejected", "processing": "Processing", "completed": "Completed", "cancelled": "Cancelled", "pending": "Pending", "active": "Active", "inactive": "Inactive", "normal": "Normal", "warning": "Warning", "error": "Error", "success": "Success", "failed": "Failed"}}