{"searchPlaceholder": "Search item name, code or description", "status": "Status", "purchasable": "Purchasable", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "allItems": "All Items", "addItem": "Add Item", "editItem": "<PERSON>em", "deleteItem": "Delete Item", "itemName": "Item Name", "itemCode": "Item Code", "category": "Category", "description": "Description", "image": "Image", "purchaseUnit": "Purchase Unit", "inventoryUnit": "Inventory Unit", "packageQuantity": "Package Quantity", "brand": "Brand:", "specMaterial": "Specification/Material", "sizeDimension": "Size/Dimension", "unknownStockStatus": "Unknown", "outOfStock": "Out of Stock", "lowStock": "Low Stock", "normalStock": "Normal", "sufficientStock": "Sufficient", "noPrice": "N/A", "currencySymbol": "$", "priceRange": "Range", "unitPrice": "Unit Price", "remarks": "Remarks", "actionCreate": "Create", "actionUpdate": "Update", "actionDelete": "Delete", "fieldNameName": "Item Name", "fieldNameCode": "Item Code", "fieldNameDescription": "Item Description", "fieldNameCategory": "Category", "fieldNameImage": "Item Image", "fieldNameUnit": "Unit", "fieldNameMoq": "Minimum Order Quantity", "fieldNameSpq": "Standard Package Quantity", "fieldNameQtyPerUp": "QTY", "fieldNameIsPurchasable": "Purchasable Status", "fieldNameIsActive": "Active Status", "fieldNameMinStockAlert": "Minimum Stock Alert", "fieldNameBrand": "Brand", "fieldNameSpecMaterial": "Specification/Material", "fieldNameSizeDimension": "Size/Dimension", "itemNotFound": "Item not found", "back": "Back", "edit": "Edit", "confirmDeleteItem": "Are you sure you want to delete this item?", "deleteItemDescription": "This action cannot be undone.", "confirm": "Confirm", "cancel": "Cancel", "delete": "Delete", "code": "Code", "unpurchasable": "Not Purchasable", "noPreferredSupplierPrice": "No preferred supplier price", "unit": "Unit", "noSupplierConfig": "No supplier configuration", "noPriceInfo": "No price information", "addToCart": "Add to Cart", "quantity": "Quantity", "x": "x", "supplierInfo": "Supplier Information", "addSupplier": "Add Supplier", "supplier": "Supplier", "preferred": "Preferred", "alternative": "Alternative", "spq": "SPQ", "moq": "MOQ", "deliveryDays": "Delivery Days", "days": "days", "qualityRating": "Quality Rating", "tieredPrice": "Tiered Price", "unknownUser": "Unknown User", "field": "Field", "oldValue": "Old Value", "newValue": "New Value", "noChangeHistory": "No change history", "itemCreatedNoChanges": "Item has not been modified since creation", "noPriceAvailable": "No price available", "getPeerCategoriesFailed": "Failed to get peer categories", "getCategoryAttributesFailed": "Failed to get category attributes", "noPeerCategories": "No peer categories", "spec": "Spec:", "size": "Size:", "priceManagement": "Price Management", "addPrice": "Add Price", "editPrice": "Edit Price", "currency": "<PERSON><PERSON><PERSON><PERSON>", "pleaseSelectCurrency": "Please select currency", "quantityRange": "Quantity Range", "validPeriod": "Valid Period", "confirmDeletePrice": "Are you sure you want to delete this price?", "priceDeletedSuccessfully": "Price deleted successfully", "priceDeleteFailed": "Failed to delete price", "getPriceListFailed": "Failed to get price list", "pleaseEnterUnitPrice": "Please enter unit price", "pleaseEnterMinQuantity": "Please enter min quantity", "minQuantity": "Min Quantity", "maxQuantity": "Max Quantity", "pleaseSelectStatus": "Please select status", "pleaseEnterRemarks": "Please enter remarks", "itemConfiguration": "Item Configuration", "saveConfiguration": "Save Configuration", "itemInfoUpdatedSuccessfully": "Item information updated successfully", "updateFailed": "Update failed", "priority": "Priority", "notSet": "Not Set", "pleaseEnterSPQ": "Please enter SPQ", "spqMustBeInteger": "SPQ must be an integer", "spqMustBeGreaterThanZero": "SPQ must be greater than 0", "standardPackagingQuantity": "Standard Packaging Quantity", "pleaseEnterMOQ": "Please enter MOQ", "moqMustBeInteger": "MOQ must be an integer", "moqMustBeGreaterThanZero": "MOQ must be greater than 0", "moqMustBeMultipleOfSPQ": "MOQ must be a multiple of SPQ, current MOQ: {moq}", "minimumOrderQuantity": "Minimum Order Quantity", "pleaseEnterDeliveryDays": "Please enter delivery days", "pleaseSelectQualityRating": "Please select quality rating", "selectItem": "Select Item", "searchItemPlaceholder": "Search item code, name or description...", "searching": "Searching...", "noMatchingItems": "No matching items found", "priceTrendAnalysis": "Price Trend Analysis", "dateRange": "Date Range", "priceTrendChart": "Price Trend Chart", "priceTrendDataFailed": "Failed to get price trend data", "startDateNotAfterEndDate": "Start date cannot be later than end date", "noPriceTrendData": "No price trend data available", "priceLabel": "Price", "updateTime": "Update Time", "operations": "Operations", "noPriceData": "No price data", "noValidPriceData": "No valid price data", "startDate": "Start Date", "pleaseSelectStartDate": "Please select Start Date", "endDate": "End Date", "pleaseSelectEndDate": "Please select End Date", "valid": "<PERSON><PERSON>", "invalid": "Invalid", "expired": "Expired", "update": "Update", "add": "Add", "showAllPrices": "Show All Prices", "showPriceTrend": "Show Price Trend", "priceAddedSuccess": "Price added successfully", "priceAddFailed": "Price add failed", "priceUpdatedSuccess": "Price updated successfully", "priceUpdateFailed": "Price update failed", "maxRangeEndDateSet": "Max range end date set", "pleaseSelectStartDateFirst": "Please select start date first", "oneStar": "⭐", "twoStar": "⭐⭐", "threeStar": "⭐⭐⭐", "fourStar": "⭐⭐⭐⭐", "fiveStar": "⭐⭐⭐⭐⭐"}