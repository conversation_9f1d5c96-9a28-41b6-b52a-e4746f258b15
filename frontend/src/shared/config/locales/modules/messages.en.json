{"saveSuccess": "Saved successfully", "saveFailed": "Save failed", "confirmDelete": "Confirm Delete", "networkError": "Network Error", "getPreferredPricesFailed": "Failed to get preferred prices", "getItemListFailed": "Failed to get item list", "loadUserListFailed": "Failed to load user list", "loadRoleListFailed": "Failed to load role list", "loadDepartmentListFailed": "Failed to load department list", "userUpdateSuccess": "User updated successfully", "userCreateSuccess": "User created successfully", "passwordResetSuccess": "Password reset successfully", "passwordResetFailed": "Password reset failed", "roleAssignSuccess": "Role assigned successfully", "roleAssignFailed": "Role assignment failed", "userStatusUpdateSuccess": "User status updated successfully", "statusUpdateFailed": "Status update failed", "getSupplierListFailed": "Failed to get supplier list", "getPrimaryCategoriesFailed": "Failed to get primary categories", "getCategoriesFailed": "Failed to get categories", "getPropertyConfigsFailed": "Failed to get property configurations", "categoryUpdateSuccess": "Category updated successfully", "categoryCreateSuccess": "Category created successfully", "categoryDeleteSuccess": "Category deleted successfully", "propertyConfigsUpdateSuccess": "Property configurations updated successfully", "propertyConfigsUpdateFailed": "Property configurations update failed", "loadPermissionListFailed": "Failed to load permission list", "loadPermissionMatrixFailed": "Failed to load permission matrix", "roleUpdateSuccess": "Role updated successfully", "roleCreateSuccess": "Role created successfully", "roleDeleteSuccess": "Role deleted successfully", "permissionConfigSuccess": "Permission configuration successful", "permissionConfigFailed": "Permission configuration failed", "initSystemDataSuccess": "System data initialization successful", "initSystemDataFailed": "System data initialization failed", "departmentUpdateSuccess": "Department updated successfully", "departmentCreateSuccess": "Department created successfully", "departmentDeleteSuccess": "Department deleted successfully", "statusUpdateSuccess": "Status updated successfully", "getOverviewDataFailed": "Failed to get overview data", "getConsumptionReportFailed": "Failed to get consumption report", "getDepartmentReportFailed": "Failed to get department report", "getTrendAnalysisFailed": "Failed to get trend analysis", "getTopItemsFailed": "Failed to get top items", "getImageOverviewFailed": "Failed to get image overview", "getImageMatchingResultFailed": "Failed to get image matching result", "imageMatchingSuccess": "Image matching successful", "imageMatchingFailed": "Image matching failed", "noImagesSelected": "No images selected", "deleteOrphanedImagesFailed": "Failed to delete orphaned images", "getItemDetailFailed": "Failed to get item details", "getChangeHistoryFailed": "Failed to get change history", "itemDeleteSuccess": "Item deleted successfully", "itemDeleteFailed": "Item deletion failed", "itemAddedToCart": "Item added to cart", "addToCartFailed": "Failed to add to cart", "supplierAddedSuccess": "Supplier added successfully", "addSupplierFailed": "Failed to add supplier", "priorityUpdatedSuccess": "Priority updated successfully", "getUpdatedSupplierInfoFailed": "Failed to get updated supplier information", "updatePriorityFailed": "Failed to update priority", "addSuccess": "Added successfully", "itemNotFound": "Item not found", "addItem": "Add Item", "editItem": "<PERSON>em", "cancel": "Cancel", "save": "Save", "basicInfo": "Basic Information", "itemImage": "Item Image", "itemName": "Item Name", "itemNameRequired": "Please enter item name", "itemNamePlaceholder": "Please enter item name", "itemCode": "Item Code", "codeAutoGenerated": "Auto-generated when saving", "itemCodePlaceholder": "Item code", "category": "Category", "categoryRequired": "Please select category", "selectCategory": "Please select category", "noMatchingCategories": "No matching categories found", "purchaseUnit": "Purchase Unit", "purchaseUnitRequired": "Please enter purchase unit", "purchaseUnitPlaceholder": "Please enter purchase unit, e.g., box, carton, pack", "inventoryUnit": "Inventory Unit", "inventoryUnitRequired": "Please enter inventory unit", "inventoryUnitPlaceholder": "Please enter inventory unit, e.g., piece, pair, item", "qtyPerUp": "Quantity per Unit", "qtyPerUpRequired": "Please enter quantity per unit", "qtyPerUpMin": "Quantity must be greater than 0", "qtyPerUpTooltip": "Number of individual items contained in each package, e.g., 20 pairs per box, then enter 20", "qtyPerUpPlaceholder": "Quantity per unit", "itemDescription": "Item Description", "itemDescriptionPlaceholder": "Please enter item description", "itemAttributes": "Item Attributes", "brandPlaceholder": "Please enter brand", "specMaterialPlaceholder": "Please enter specification/material", "sizeDimensionPlaceholder": "Please enter size/specification", "statusSettings": "Status Settings", "isActive": "Active Status", "isPurchasable": "Purchasable Status", "outOfStock": "Out of Stock", "lowStock": "Low Stock", "normalStock": "Normal Stock", "sufficientStock": "Sufficient Stock", "minStockAlert": "Minimum Stock Alert", "minStockAlertRequired": "Please enter minimum stock alert", "minStockAlertPlaceholder": "Minimum stock alert", "noPrice": "N/A", "currencySymbol": "$", "getSupplierDetailFailed": "Failed to get supplier details", "startDeleteSupplier": "Starting to delete supplier...", "deleteApiResponse": "Delete API response", "foundRelatedItems": "Found related items, showing confirmation dialog", "relatedItems": "Related Items", "totalItemsCount": "Total {count} items", "userConfirmedDelete": "User confirmed deletion, executing forced deletion...", "userCancelledDelete": "User cancelled deletion", "deleteSuccess": "Deleted successfully", "deleteSupplierFailed": "Failed to delete supplier", "loading": "Loading...", "supplierNotFound": "Supplier not found", "backToList": "Back to List", "unnamedSupplier": "Unnamed Supplier", "edit": "Edit", "delete": "Delete", "supplierInfo": "Supplier Information", "supplierNameCn": "Supplier Chinese Name", "supplierNameEn": "Supplier English Name", "supplierCode": "Supplier Code", "status": "Status", "active": "Active", "inactive": "Inactive", "contactPerson": "Contact Person", "phone": "Phone", "email": "Email", "rating": "Rating", "companyAddress": "Company Address", "createdAt": "Created At", "updatedAt": "Updated At", "departmentCode": "Department Code", "departmentName": "Department Name", "description": "Description", "actions": "Actions", "enabled": "Enabled", "disabled": "Disabled", "viewDetail": "View Detail", "enable": "Enable", "disable": "Disable", "confirmDeleteDepartment": "Confirm Delete Department", "confirm": "Confirm", "atLeastOneNameRequired": "Please fill in at least the Chinese or English name of the supplier", "supplierUpdateSuccess": "Supplier information updated successfully", "supplierCreateSuccess": "Supplier created successfully", "operationFailed": "Operation failed", "getExecutionBatchesFailed": "Failed to get execution batch list", "getPurchaseRequestDetailFailed": "Failed to get request details", "getSupplierInfoFailed": "Failed to get supplier information", "confirmDeleteTitle": "Confirm Delete", "confirmDeleteContent": "Are you sure you want to delete purchase request \"{requestNo}\"?", "returnToCart": "Return request items to cart for future re-application", "requestDeletedWithItemsReturned": "Request deleted, items returned to cart", "requestDeleted": "Request deleted", "deleteRequestFailed": "Failed to delete request", "confirmWithdrawTitle": "Are you sure you want to withdraw this request?", "confirmWithdrawContent": "After withdrawal, it will return to pending status. Please proceed with caution.", "requestWithdrawn": "Request withdrawn", "withdrawRequestFailed": "Failed to withdraw request", "purchaseRequestNotFound": "Purchase request not found", "approvalSuccess": "Approval successful", "approvalFailed": "Approval failed", "pageInitializationFailed": "Page initialization failed", "pageLoadFailed": "Page load failed, please refresh and try again", "noChangesToSave": "No changes to save", "addItems": "Add {count} items", "updateItems": "Update {count} items", "deleteItems": "Delete {count} items", "updateNotes": "Update notes", "confirmSaveChangesTitle": "Confirm Save Changes", "youWillPerformTheFollowingActions": "You will perform the following actions", "areYouSureYouWantToSaveTheseChanges": "Are you sure you want to save these changes?", "purchaseRequestUpdatedSuccessfully": "Purchase request updated successfully", "purchaseRequestUpdatedAndReadyForResubmission": "Purchase request updated successfully and ready for resubmission", "image": "Image", "itemInfo": "Item Information", "quantity": "Request Quantity", "estimatedPrice": "Estimated Price", "finalPrice": "Final Price", "supplier": "Supplier", "supplierConfirmed": "Confirmed", "supplierPending": "Pending", "notes": "Notes", "pleaseEnterNotes": "Please enter notes", "action": "Action", "restore": "Rest<PERSON>", "remove": "Remove", "add": "Add", "unitPrice": "Unit Price", "totalPrice": "Total Price", "back": "Back", "editPurchaseRequestTitle": "Edit Purchase Request - {requestNo}", "saveChanges": "Save Changes", "purchaseRequestInfo": "Request Information", "purchaseRequestItems": "Request Items", "items": "items", "cartItems": "Cart Items", "cartIsEmpty": "Cart is empty", "pendingSubmission": "Pending Submission", "underReview": "Under Review", "underPrincipleApproval": "Under Principle Approval", "underFinalApproval": "Under Final Approval", "approved": "Approved", "rejected": "Rejected", "withdrawn": "Withdrawn", "getSupplierItemsFailed": "Failed to get supplier items", "itemAddedSuccessfully": "Item added successfully", "addFailed": "Add failed", "itemDeletedSuccessfully": "Item deleted successfully", "deleteFailed": "Delete failed", "noValidPrice": "No valid price", "unknownItem": "Unknown Item", "uncategorized": "Uncategorized", "confirmDeleteItem": "Are you sure you want to delete this item?", "unknownSupplier": "Unknown Supplier", "itemManagement": "Item Management", "supplierItemManagement": "Supplier Item Management", "itemList": "Item List", "noSupplierItems": "No supplier items", "updateItemInfoFailed": "Failed to update item information", "updatePriceInfoFailed": "Failed to update price information", "getRoleDetailFailed": "Failed to get role details", "getRoleUsersFailed": "Failed to get role users list", "permissionCode": "Permission Code", "permissionName": "Permission Name", "permissionDescription": "Permission Description", "permissionStatus": "Permission Status", "userAvatar": "User Avatar", "userName": "User Name", "userEmail": "User Email", "userDepartment": "User Department", "userStatus": "User Status", "roleNotFound": "Role not found", "roleDetailTitle": "Role Details - {roleName}", "roleName": "Role Name", "roleCode": "Role Code", "roleDescription": "Role Description", "roleStatus": "Role Status", "noDescription": "No description", "totalPermissions": "Total Permissions", "totalUsers": "Total Users", "activeUsers": "Active Users", "permissions": "Permissions", "users": "Users", "getItemInfoFailed": "Failed to get item information", "getPriceListFailed": "Failed to get price list", "priceAddedSuccessfully": "Price added successfully", "priceUpdatedSuccessfully": "Price updated successfully", "priceDeletedSuccessfully": "Price deleted successfully", "updateFailed": "Update failed", "backToItemList": "Back to Item List", "addPrice": "Add Price", "priceManagement": "Price Management", "itemId": "Item ID", "supplierId": "Supplier", "spq": "SPQ", "moq": "MOQ", "quantityRange": "Quantity Range", "minQuantity": "Min", "maxQuantity": "Max", "validPeriod": "Valid Period", "startDate": "Start", "endDate": "End", "remarks": "Remarks", "confirmDeletePrice": "Are you sure you want to delete this price?", "editPrice": "Edit Price", "minQuantityLabel": "Min Quantity", "maxQuantityLabel": "Max Quantity", "maxQuantityOptional": "Max Quantity (Optional)", "validFrom": "<PERSON><PERSON>", "validTo": "<PERSON><PERSON>", "validToOptional": "<PERSON><PERSON> (Optional)", "statusLabel": "Status", "remarksLabel": "Remarks", "pleaseEnterUnitPrice": "Please enter unit price", "pleaseEnterMinQuantity": "Please enter min quantity", "pleaseSelectValidFrom": "Please select valid from date", "pleaseEnterMaxQuantity": "Please enter max quantity (optional)", "pleaseSelectValidTo": "Please select valid to date (optional)", "pleaseEnterRemarks": "Please enter remarks", "currency": "<PERSON><PERSON><PERSON><PERSON>", "pleaseSelectCurrency": "Please select currency", "update": "Update", "getDepartmentFailed": "Failed to get department details", "getDepartmentUsersFailed": "Failed to get department users", "departmentNotFound": "Department not found", "editDepartment": "Edit Department", "departmentUserCount": "Department User Count", "normal": "Normal", "userList": "User List", "username": "Username", "fullName": "Full Name", "employeeId": "Employee ID", "lastLogin": "Last Login", "noPermissionTitle": "403", "noPermissionSubtitle": "Sorry, you don't have permission to access this page.", "returnHome": "Go Home", "returnPrevious": "Go Back", "noAccessPermission": "You don't have permission to access the Purchase Workbench. Please contact the administrator.", "pleaseSelectImage": "Please select an image file", "fileSizeExceeded": "File size cannot exceed {maxSize}KB", "imageDimensionExceeded": "Image dimensions cannot exceed {maxDimension}x{maxDimension} pixels", "imageUploadSuccess": "Image uploaded successfully", "imageUploadFailed": "Image upload failed", "showing": "Showing", "of": "of", "total": "items", "departments": "Department Management", "createDepartment": "Create Department", "departmentDetail": "Department Detail", "departmentDescription": "Department Description", "pleaseEnterDepartmentName": "Please enter department name", "pleaseEnterDepartmentCode": "Please enter department code", "pleaseEnterDepartmentDescription": "Please enter department description", "totalDepartments": "Total Departments", "activeDepartments": "Active Departments", "inactiveDepartments": "Inactive Departments", "searchDepartmentNameOrCode": "Search department name or code", "statusFilter": "Status Filter", "refresh": "Refresh", "noImage": "No Image", "requestSubmitted": "Application submitted successfully", "submitRequestFailed": "Failed to submit application", "confirmSubmitTitle": "Confirm Submit Application", "confirmSubmitContent": "Are you sure you want to submit this purchase request?", "getExchangeRateInfoFailed": "Failed to get exchange rate information", "exchangeRateValidationFailed": "Exchange rate validation failed", "exchangeRateCheckFailed": "Exchange rate check failed"}