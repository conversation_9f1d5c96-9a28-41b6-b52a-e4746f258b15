{"purchaseOrder": "Purchase Order", "purchaseRequest": "Purchase Request", "purchaseHistory": "Purchase History", "orderNumber": "Order Number", "orderDate": "Order Date", "deliveryDate": "Delivery Date", "supplier": "Supplier", "totalAmount": "Total Amount", "orderStatus": "Order Status", "pending": "Pending", "approved": "Approved", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled", "items": "items", "quantity": "Quantity", "unitPrice": "Unit Price", "subtotal": "Subtotal", "noPermissionToViewCart": "You don't have permission to view the cart. Please contact administrator.", "getCartDataFailed": "Failed to get cart data", "itemRemovedFromCart": "Item removed from cart", "removeItemFailed": "Failed to remove item, please retry", "cartEmptyCannotSubmit": "<PERSON><PERSON> is empty, cannot submit", "purchaseRequestCreatedSuccessfully": "Purchase request created successfully!", "createPurchaseRequestFailed": "Failed to create purchase request, please retry", "confirmClearCartTitle": "Confirm Clear Cart", "confirmClearCartContent": "This action will clear all items in the cart. Do you want to continue?", "cartCleared": "<PERSON><PERSON> cleared", "clearCartFailed": "Failed to clear cart, please retry", "quantityUpdated": "Quantity updated", "updateQuantityFailed": "Failed to update quantity, please retry", "itemIcon": "Item Icon", "noImage": "No Image", "itemInfo": "Item Information", "overstockWarning": "⚠️ Overstock", "inventoryStatus": "Inventory Status", "currentQuantity": "Current", "maxQuantity": "Max", "quantityAfterPurchase": "After Purchase", "priceInfo": "Price Information", "totalPrice": "Total Price", "action": "Action", "confirmRemoveTitle": "Confirm Remove", "confirmRemoveDescription": "Are you sure you want to remove this item from the cart?", "confirm": "Confirm", "cancel": "Cancel", "purchaseCartManagement": "Purchase Cart Management", "managePurchaseItemsInDepartment": "Manage purchase items in department, support add, edit, delete and submit application", "goToCart": "Go to Cart", "cartItemCount": "<PERSON><PERSON>em <PERSON>", "estimatedTotalAmount": "Estimated Total Amount", "actions": "Actions", "submitApplication": "Submit Application", "purchaseCartItems": "Purchase Cart Items", "clearCart": "Clear Cart", "submitPurchaseApplication": "Submit Purchase Application", "applicationSummary": "Application Summary", "itemQuantity": "Item Quantity", "notes": "Notes", "optionalNotes": "Please enter notes (optional)", "pleaseSelectSupplierAndQuantity": "Please select supplier and quantity", "itemAddedToCart": "Item added to cart", "addToCartFailed": "Failed to add to cart", "noDepartmentAssigned": "You have not been assigned to a department and cannot use the shopping cart feature", "canOnlyAddToOwnDepartment": "You can only add items to your own department's shopping cart", "batchNumber": "Batch Number", "executionDate": "Execution Date", "itemCount": "<PERSON><PERSON>", "status": "Status", "statusPending": "Pending", "statusInProgress": "In Progress", "statusCompleted": "Completed", "statusFailed": "Failed", "viewDetails": "View Details", "purchaseExecutionRecords": "Purchase Execution Records", "viewPurchaseExecutionRecordsAndBatchInfo": "View purchase request execution records and batch information, support data export and statistical analysis", "searchBatchNumber": "Search batch number", "startDate": "Start Date", "endDate": "End Date", "clearFilters": "Clear Filters", "exportData": "Export Data", "refresh": "Refresh", "pageRange": "Page {start}-{end} of {total} items", "purchaseRequestSummary": "Purchase Request Summary Analysis", "analysisTime": "Analysis Time", "totalRequests": "Total Requests", "involvedDepartments": "Involved Departments", "itemCategories": "Item Categories", "requestList": "Request List", "itemSummaryAnalysis": "Item Summary Analysis", "departmentSummaryAnalysis": "Department Summary Analysis", "supplierSummaryAnalysis": "Supplier Summary Analysis", "requestNumber": "Request Number", "requestDepartment": "Request Department", "requestor": "Requestor", "requestStatus": "Request Status", "requestAmount": "Request Amount", "requestTime": "Request Time", "batchApproval": "<PERSON><PERSON>", "selectedRequests": "Selected Requests", "differentApprovalLevels": "Selected requests have different approval levels and cannot be batch approved", "approvalAction": "Approval Action", "pleaseSelectAction": "Please select approval action", "approve": "Approve", "reject": "Reject", "approvalComments": "Approval Comments", "pleaseEnterComments": "Please enter approval comments", "enterApprovalComments": "Please enter approval comments...", "batchApprovalInfo": "About to perform {level} approval on {count} requests", "approvalStatus": "Approval Status", "processing": "Processing", "success": "Success", "failed": "Failed", "batchApprovalResults": "Batch Approval Results", "totalProcessed": "Total Processed", "successCount": "Success Count", "failureCount": "Failure Count", "failedRequests": "Failed Requests", "statusLegend": "Status Legend", "itemImage": "Item Image", "summaryQuantity": "Summary Quantity", "requestCount": "Request Count", "averagePrice": "Average Price", "noSupplier": "No Supplier", "departmentName": "Department Name", "itemTypes": "Item Types", "percentage": "Percentage", "supplierName": "Supplier Name", "itemCode": "Item Code", "category": "Category", "requests": "requests", "types": "types", "noRequestsSpecified": "No requests specified for summary", "generatingSummaryAnalysis": "Generating summary analysis...", "getSummaryAnalysisFailed": "Failed to get summary analysis", "noData": "No data", "statusDraft": "Draft", "statusPendingSubmission": "Pending Submission", "statusUnderReview": "Under Review", "statusUnderPrincipleApproval": "Under Principle Approval", "statusUnderFinalApproval": "Under Final Approval", "statusApproved": "Approved", "statusRejected": "Rejected", "statusWithdrawn": "Withdrawn", "statusExecuted": "Executed", "loading": "Loading...", "requestNotFound": "Purchase request not found", "autoPrintFailed": "Auto print failed, please click print button manually", "getDataFailed": "Failed to get data", "purchaseRequestForm": "Purchase Request Form", "applicant": "Applicant", "applicationDate": "Application Date", "applicationStatus": "Application Status", "itemList": "Item List", "itemName": "Item Name", "applicationQuantity": "Application Quantity", "unitPriceLabel": "Unit Price", "remarks": "Remarks", "totalAmountLabel": "Total Amount", "batchDetails": "Batch Details", "executionStatus": "Execution Status", "getBatchDetailsFailed": "Failed to get batch details", "exportFailed": "Export failed", "exportRequested": "Export request submitted", "returnToExecutionList": "Return to Execution List", "executionBatchDetails": "Execution Batch Details", "batchNotFound": "Batch information not found", "draft": "Draft", "myRequests": "My Requests", "allRequests": "All Requests", "requestManagement": "Request Management", "createNewPurchaseRequest": "Create New Purchase Request", "allApplications": "All Applications", "myApplications": "My Applications", "draftTab": "Draft", "pendingTab": "Pending", "approvedTab": "Approved", "rejectedReturnedTab": "Rejected/Returned", "pendingSubmission": "Pending Submission", "underReview": "Under Review", "underPrincipleApproval": "Under Principle Approval", "underFinalApproval": "Under Final Approval", "rejected": "Rejected", "executed": "Executed", "requestInfo": "Request Information", "priority": "Priority", "normal": "Normal", "urgent": "<PERSON><PERSON>", "emergency": "Emergency", "editApplication": "Edit Application", "deleteApplication": "Delete Application", "withdrawToPending": "Withdraw to Pending Submission", "approveApplication": "Approve Application", "executeApplication": "Execute Application", "confirmDelete": "Confirm Delete", "confirmDeleteContent": "Are you sure you want to delete purchase request \"{requestNo}\"?", "confirmDeleteOptions": "Return the items to cart?", "returnToCart": "Return to Cart", "permanentlyDelete": "Permanently Delete", "departmentManagerReview": "Department Manager Review", "itemManagerPrincipleApproval": "Item Manager Principle Approval", "companyManagerApproval": "Company Manager <PERSON><PERSON><PERSON><PERSON>", "purchaseExecution": "Purchase Execution", "waiting": "Waiting", "completed": "Completed", "error": "Error", "currentApprovalLevel": "Current Approval Level", "pendingApproval": "Pending Approval", "executionSuccess": "Execution Successful", "executionFailed": "Execution Failed", "batchExecutionSuccess": "Batch Execution Successful", "batchExecutionFailed": "Batch Execution Failed", "itemStatistics": "Item Statistics", "submitted": "Submitted", "created": "Created", "confirmWithdraw": "Confirm Withdraw", "confirmWithdrawContent": "Are you sure you want to withdraw purchase request \"{requestNo}\"?", "confirmSubmit": "Confirm Submit", "confirmSubmitTitle": "Confirm Submit Application", "confirmSubmitContent": "Are you sure you want to submit purchase request \"{requestNo}\"?", "confirmExecute": "Confirm Execute", "confirmExecuteContent": "Are you sure you want to execute purchase request \"{requestNo}\"? Execution will generate a purchase order.", "passed": "Passed", "withdrawApplication": "Withdraw Application", "submitApproval": "Submit Approval", "currentStatus": "Current Status", "approvalLevel": "Approval Level", "approvalDecision": "Approval Decision", "pleaseSelectApprovalDecision": "Please select approval decision", "selectApprovalDecision": "Select approval decision", "pleaseEnterApprovalComments": "Please enter approval comments", "approvalInstructions": "Approval Instructions", "approveInstruction": "Approve: Application proceeds to next approval level", "rejectInstruction": "Reject: Application is rejected, process ends", "returnInstruction": "Return: Application is returned, applicant can modify", "operationTime": "Operation Time", "operationType": "Operation Type", "approvalPassed": "<PERSON><PERSON><PERSON><PERSON> Passed", "approvalRejected": "<PERSON><PERSON><PERSON><PERSON> Rejected", "returnForModification": "Return for Modification", "operator": "Operator", "system": "System", "basicInformation": "Basic Information", "pleaseSelectRequestDepartment": "Please select request department", "selectRequestDepartment": "Select request department", "productionDepartment": "Production Department", "maintenanceDepartment": "Maintenance Department", "qualityDepartment": "Quality Department", "researchDepartment": "Research Department", "requiredDate": "Required Date", "pleaseSelectRequiredDate": "Please select required date", "pleaseSelectPriority": "Please select priority", "selectPriority": "Select Priority", "applicationDescription": "Application Description", "pleaseEnterApplicationDescription": "Please enter application description", "enterApplicationDescription": "Please enter application description", "businessJustification": "Business Justification", "pleaseEnterBusinessJustification": "Please enter business justification", "businessJustificationPlaceholder": "Please explain the business reasons and necessity for the purchase in detail...", "item": "<PERSON><PERSON>", "delete": "Delete", "enterItemName": "Please enter item name", "estimatedUnitPrice": "Estimated Unit Price", "addItem": "Add Item", "total": "Total", "searchPlaceholder": "Search request number, applicant, description...", "selectStatus": "Select status", "selectDepartment": "Select department", "selectApprovalLevel": "Select approval level", "clear": "Clear", "purchaseRequestDetails": "Purchase Request Details", "close": "Close", "approvalProgress": "Approval Progress", "submittedTime": "Submitted Time", "requestItems": "Request Items", "finalUnitPrice": "Final Unit Price", "viewSummaryAnalysis": "View Summary Analysis", "batchExecute": "Batch Execute", "clearSelection": "Clear Selection", "totalRecords": "Total {total} records", "approvalSuccess": "Approval successful", "confirmBatchExecute": "Confirm Batch Execute", "confirmBatchExecuteContent": "Are you sure you want to batch execute {count} purchase requests? Execution will generate purchase orders.", "getQRCodeFailed": "Failed to get QR code", "exportPDF": "Export PDF", "createdTime": "Created Time", "purchaseRequestQRCode": "Purchase Request QR Code", "qrCodeLoadFailed": "QR code load failed", "clickToViewItemDetails": "Click to view item details: {itemName}", "spqQuantity": "SPQ", "spqCount": "SPQ Count", "unit": "Unit", "currency": "<PERSON><PERSON><PERSON><PERSON>", "estimatedTotalPrice": "Estimated Total Price", "requestItemDetails": "Request Items", "noItemDetails": "No item details", "addToCart": "Add to Cart", "code": "Code", "pleaseEnterQuantity": "Please enter quantity", "pieces": "pieces", "flowHistory": "Flow History", "itemRequestHistoryTrend": "Item Request History Trend", "itemRequestHistoryTrendDesc": "Shows the request quantity trend of each item in this department over the past 6 months (including current request records)", "itemPriceTrendAnalysis": "Item Price Trend Analysis", "itemPriceTrendAnalysisDesc": "Shows the tiered price trend of each item based on the most preferred supplier at current purchase quantity (180 days before and after)", "price": "Price", "month": "Month", "noFlowHistory": "No flow history records", "loadingPriceTrendData": "Loading price trend data...", "noPriceTrendData": "No price trend data", "priceLabel": "Price", "loadingPriceDataFailed": "Failed to load price data", "itemsLoadFailed": "The following items failed to load:", "noRequestHistoryData": "No request history data", "backendAPINotConfigured": "Backend API not configured: /purchase/requests/department/history", "tier": "Tier", "applicationProgress": "Application Progress", "processDescription": "Process Description", "nextStep": "Next Step", "currentProgress": "Current Progress", "steps": "Steps", "statusChange": "Status Change", "allowedActions": "Allowed Actions", "departmentItemManagerSubmit": "Department item manager submits application", "departmentManagerPreliminaryReview": "Department manager conducts preliminary review", "supervisorApproval": "Supervisor Approval", "itemManagerSupervisorApproval": "Item manager supervisor approval", "finalApproval": "Final Approval", "companySupervisorFinalApproval": "Company supervisor final approval", "applicationFormallySubmitted": "Application formally submitted", "applicationSubmitted": "Application submitted", "failedToGetItemRequestHistory": "Failed to get item request history", "failedToLoadItemPriceTrend": "Failed to load item price trend", "shoppingCartRiskAlert": "Shopping Cart Risk Alert", "itemsExistRisk": "The following items have risks, please consider procurement carefully:", "unknownItem": "Unknown Item", "risk": "Risk", "back": "Back", "itemPurchaseHistoryTrend": "Item Purchase History Trend", "itemPurchaseHistoryTrendDesc": "Shows the purchase quantity trend of each item in this department over the past 6 months", "failedToGetItemPurchaseHistory": "Failed to get item purchase history", "editRequest": "Edit Request", "addToRequest": "Add to Request", "saveChanges": "Save Changes", "applicationInfo": "Application Information", "enterBusinessJustification": "Please enter business justification", "expectedDelivery": "Expected Delivery Time", "applicationDetails": "Application Details", "shoppingCartItems": "Shopping Cart Items", "operation": "Operation", "purchaseRequestManagement": "Purchase Request Management", "noPurchaseHistoryData": "No purchase history data", "showPurchaseTrendForLast12Months": "Shows the purchase quantity trend of each item over the past 12 months", "approvalPermissionDenied": "Approval Permission Denied", "noApprovalPermission": "You don't have permission to approve this request"}