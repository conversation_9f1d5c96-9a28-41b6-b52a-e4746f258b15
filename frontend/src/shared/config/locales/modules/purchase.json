{"purchaseOrder": "采购订单", "purchaseRequest": "采购申请", "purchaseHistory": "采购历史", "orderNumber": "订单号", "orderDate": "订单日期", "deliveryDate": "交货日期", "supplier": "供应商", "totalAmount": "总金额", "orderStatus": "订单状态", "pending": "待处理", "approved": "已批准", "shipped": "已发货", "delivered": "已交货", "cancelled": "已取消", "items": "个物品", "quantity": "数量", "unitPrice": "单价", "subtotal": "小计", "noPermissionToViewCart": "您没有访问购物车的权限，请联系管理员。", "getCartDataFailed": "获取购物车数据失败", "itemRemovedFromCart": "物品已从购物车移除", "removeItemFailed": "移除物品失败，请重试", "cartEmptyCannotSubmit": "购物车为空，无法提交", "purchaseRequestCreatedSuccessfully": "采购申请创建成功！", "createPurchaseRequestFailed": "创建采购申请失败，请重试", "confirmClearCartTitle": "确认清空购物车", "confirmClearCartContent": "此操作将清空购物车中的所有物品，是否继续？", "cartCleared": "购物车已清空", "clearCartFailed": "清空购物车失败，请重试", "quantityUpdated": "数量已更新", "updateQuantityFailed": "更新数量失败，请重试", "itemIcon": "物品图标", "noImage": "无图片", "itemInfo": "物品信息", "overstockWarning": "⚠️ 超储", "inventoryStatus": "库存状态", "currentQuantity": "当前", "maxQuantity": "最大", "quantityAfterPurchase": "购买后", "priceInfo": "价格信息", "totalPrice": "总价", "action": "操作", "confirmRemoveTitle": "确认移除", "confirmRemoveDescription": "确定要从购物车中移除这个物品吗？", "confirm": "确定", "cancel": "取消", "close": "关闭", "purchaseCartManagement": "购物车管理", "managePurchaseItemsInDepartment": "管理部门的采购物品，支持添加、编辑、删除和提交申请", "goToCart": "去购物车", "cartItemCount": "购物车物品数", "estimatedTotalAmount": "预估总金额", "actions": "操作", "submitApplication": "提交申请", "purchaseCartItems": "购物车物品", "clearCart": "清空购物车", "submitPurchaseApplication": "提交采购申请", "applicationSummary": "申请摘要", "itemQuantity": "物品数量", "notes": "备注", "optionalNotes": "请输入备注信息（可选）", "pleaseSelectSupplierAndQuantity": "请选择供应商和数量", "itemAddedToCart": "物品已添加到购物车", "addToCartFailed": "添加购物车失败", "noDepartmentAssigned": "您尚未分配部门，无法使用购物车功能", "canOnlyAddToOwnDepartment": "只能为自己部门添加购物车物品", "batchNumber": "批次编号", "executionDate": "执行日期", "itemCount": "物品数量", "status": "状态", "statusPending": "待执行", "statusInProgress": "执行中", "statusCompleted": "已完成", "statusFailed": "执行失败", "viewDetails": "查看详情", "purchaseExecutionRecords": "采购执行记录", "viewPurchaseExecutionRecordsAndBatchInfo": "查看采购申请的执行记录和批次信息，支持数据导出和统计分析", "searchBatchNumber": "搜索批次编号", "startDate": "开始时间", "endDate": "结束时间", "clearFilters": "清空筛选", "exportData": "导出数据", "refresh": "刷新", "pageRange": "第 {start}-{end} 条/共 {total} 条", "purchaseRequestSummary": "采购申请汇总分析", "analysisTime": "分析时间", "totalRequests": "申请总数", "involvedDepartments": "涉及部门", "itemCategories": "物品种类", "requestList": "申请单列表", "itemSummaryAnalysis": "物品汇总分析", "departmentSummaryAnalysis": "部门汇总分析", "supplierSummaryAnalysis": "供应商汇总分析", "requestNumber": "申请单号", "requestDepartment": "申请部门", "requestor": "申请人", "requestStatus": "申请状态", "requestAmount": "申请金额", "requestTime": "申请时间", "batchApproval": "批量审批", "selectedRequests": "已选择的申请", "differentApprovalLevels": "选中的申请审批级别不一致，无法批量审批", "approvalAction": "审批操作", "pleaseSelectAction": "请选择审批操作", "approve": "批准", "reject": "拒绝", "approvalComments": "审批意见", "pleaseEnterComments": "请输入审批意见", "enterApprovalComments": "请输入审批意见...", "batchApprovalInfo": "即将对 {count} 个申请进行{level}审批", "approvalStatus": "审批状态", "processing": "进行中", "success": "成功", "failed": "失败", "batchApprovalResults": "批量审批结果", "totalProcessed": "总处理数量", "successCount": "成功数量", "failureCount": "失败数量", "failedRequests": "失败的申请", "statusLegend": "状态说明", "itemImage": "物品图片", "summaryQuantity": "汇总数量", "requestCount": "申请数量", "averagePrice": "平均单价", "noSupplier": "暂无供应商", "departmentName": "部门名称", "itemTypes": "物品种类", "percentage": "占比", "supplierName": "供应商名称", "itemCode": "物品编码", "category": "分类", "requests": "个申请", "types": "种", "noRequestsSpecified": "未指定要汇总的申请", "generatingSummaryAnalysis": "正在生成汇总分析...", "getSummaryAnalysisFailed": "获取汇总分析失败", "noData": "暂无数据", "statusDraft": "草稿", "statusPendingSubmission": "待提交", "statusUnderReview": "待审批", "statusUnderPrincipleApproval": "主管审批中", "statusUnderFinalApproval": "最终审批中", "statusApproved": "已批准", "statusRejected": "已拒绝", "statusWithdrawn": "已撤回", "statusExecuted": "已执行", "loading": "Loading...", "requestNotFound": "未找到采购申请", "autoPrintFailed": "自动打印失败，请手动点击打印按钮", "getDataFailed": "获取数据失败", "purchaseRequestForm": "采购申请单", "applicant": "申请人", "applicationDate": "申请日期", "applicationStatus": "申请状态", "itemList": "物品清单", "itemName": "物品名称", "applicationQuantity": "申请数量", "unitPriceLabel": "单价", "remarks": "备注", "totalAmountLabel": "总金额", "batchDetails": "批次详情", "executionStatus": "执行状态", "getBatchDetailsFailed": "获取批次详情失败", "exportFailed": "导出失败", "exportRequested": "导出请求已提交", "returnToExecutionList": "返回执行列表", "executionBatchDetails": "执行批次详情", "batchNotFound": "未找到批次信息", "draft": "草稿", "myRequests": "我的申请", "allRequests": "所有申请", "requestManagement": "申请管理", "createNewPurchaseRequest": "创建新采购申请", "allApplications": "所有申请", "myApplications": "我的申请", "draftTab": "草稿", "pendingTab": "待处理", "approvedTab": "已批准", "rejectedReturnedTab": "已拒绝/已退回", "pendingSubmission": "待提交", "underReview": "审核中", "underPrincipleApproval": "原则审批中", "underFinalApproval": "最终审批中", "rejected": "已拒绝", "executed": "已执行", "requestInfo": "申请信息", "priority": "优先级", "normal": "普通", "urgent": "紧急", "emergency": "特急", "editApplication": "编辑申请", "deleteApplication": "删除申请", "withdrawToPending": "撤销回待提交状态", "approveApplication": "审批申请", "executeApplication": "执行申请", "confirmDelete": "确认删除", "confirmDeleteContent": "确定要删除采购申请\"{requestNo}\"吗？", "confirmDeleteOptions": "删除后是否要将物品放回购物车？", "returnToCart": "返回购物车", "permanentlyDelete": "永久删除", "departmentManagerReview": "部门经理复核", "itemManagerPrincipleApproval": "物品管理员原则审批", "companyManagerApproval": "公司主管审批", "purchaseExecution": "采购执行", "waiting": "等待中", "completed": "已完成", "error": "错误", "currentApprovalLevel": "当前审批级别", "pendingApproval": "待审批", "executionSuccess": "执行成功", "executionFailed": "执行失败", "batchExecutionSuccess": "批量执行成功", "batchExecutionFailed": "批量执行失败", "itemStatistics": "物品统计", "submitted": "提交", "created": "创建", "confirmWithdraw": "确认撤销", "confirmWithdrawContent": "确定要撤销采购申请\"{requestNo}\"吗？", "confirmSubmit": "确认提交", "confirmSubmitTitle": "确认提交申请", "confirmSubmitContent": "确定要提交采购申请\"{requestNo}\"吗？", "confirmExecute": "确认执行", "confirmExecuteContent": "确定要执行采购申请\"{requestNo}\"吗？执行后将生成采购订单。", "passed": "已通过", "withdrawApplication": "撤回申请", "submitApproval": "提交审批", "currentStatus": "当前状态", "approvalLevel": "审批级别", "approvalDecision": "审批决定", "pleaseSelectApprovalDecision": "请选择审批决定", "selectApprovalDecision": "选择审批决定", "pleaseEnterApprovalComments": "请输入审批意见", "approvalInstructions": "审批说明", "approveInstruction": "批准：申请进入下一级审批流程", "rejectInstruction": "拒绝：申请被拒绝，流程结束", "returnInstruction": "退回：申请被退回，申请人可重新修改", "operationTime": "操作时间", "operationType": "操作类型", "approvalPassed": "审批通过", "approvalRejected": "审批拒绝", "returnForModification": "退回修改", "operator": "操作人", "system": "系统", "basicInformation": "基本信息", "pleaseSelectRequestDepartment": "请选择申请部门", "selectRequestDepartment": "选择申请部门", "productionDepartment": "生产部", "maintenanceDepartment": "维修部", "qualityDepartment": "质量部", "researchDepartment": "研发部", "requiredDate": "需要日期", "pleaseSelectRequiredDate": "请选择需要日期", "pleaseSelectPriority": "请选择优先级", "selectPriority": "选择优先级", "applicationDescription": "申请说明", "pleaseEnterApplicationDescription": "请输入申请说明", "enterApplicationDescription": "请输入申请说明", "businessJustification": "业务理由", "pleaseEnterBusinessJustification": "请输入业务理由", "businessJustificationPlaceholder": "请详细说明采购的业务理由和必要性...", "item": "物品", "delete": "删除", "enterItemName": "请输入物品名称", "estimatedUnitPrice": "预估单价", "addItem": "添加物品", "total": "总计", "searchPlaceholder": "搜索申请单号、申请人、说明...", "selectStatus": "选择状态", "selectDepartment": "选择部门", "selectApprovalLevel": "选择审批级别", "clear": "清空", "purchaseRequestDetails": "采购申请详情", "approvalProgress": "审批进度", "submittedTime": "提交时间", "requestItems": "申请物品", "finalUnitPrice": "最终单价", "viewSummaryAnalysis": "查看汇总分析", "batchExecute": "批量执行", "clearSelection": "清除选择", "totalRecords": "共 {total} 条记录", "approvalSuccess": "审批成功", "confirmBatchExecute": "确认批量执行", "confirmBatchExecuteContent": "确定要批量执行 {count} 个采购申请吗？执行后将生成采购订单。", "getQRCodeFailed": "获取二维码失败", "exportPDF": "导出PDF", "createdTime": "创建时间", "purchaseRequestQRCode": "采购申请二维码", "qrCodeLoadFailed": "二维码加载失败", "clickToViewItemDetails": "点击查看物品详情: {itemName}", "spqQuantity": "SPQ数量", "spqCount": "SPQ个数", "unit": "单位", "currency": "货币", "estimatedTotalPrice": "预估总价", "requestItemDetails": "申请物品", "noItemDetails": "暂无物品明细", "addToCart": "加入购物车", "code": "编码", "pleaseEnterQuantity": "请输入数量", "pieces": "个", "flowHistory": "流转历史", "itemRequestHistoryTrend": "物品申请历史趋势", "itemRequestHistoryTrendDesc": "显示最近6个月该部门各物品的申请数量趋势（包含当前申请记录）", "itemPriceTrendAnalysis": "物品价格趋势分析", "itemPriceTrendAnalysisDesc": "显示各物品在当前采购数量下根据最优先供应商的阶梯价格趋势（前后180天）", "price": "价格", "month": "月", "noFlowHistory": "暂无流转历史记录", "loadingPriceTrendData": "加载价格趋势数据中...", "noPriceTrendData": "暂无价格趋势数据", "priceLabel": "价格", "loadingPriceDataFailed": "加载价格数据失败", "itemsLoadFailed": "以下物品加载失败：", "noRequestHistoryData": "暂无申请历史数据", "backendAPINotConfigured": "需要配置后端API: /purchase/requests/department/history", "tier": "阶梯", "applicationProgress": "申请进度", "processDescription": "流程说明", "nextStep": "下一步", "currentProgress": "当前进度", "steps": "步骤", "statusChange": "状态变更", "allowedActions": "允许的操作", "departmentItemManagerSubmit": "部门物品管理员提交申请", "departmentManagerPreliminaryReview": "部门经理进行初步审核", "supervisorApproval": "主管审批", "itemManagerSupervisorApproval": "物品管理员主管审批", "finalApproval": "最终审批", "companySupervisorFinalApproval": "公司主管最终审批", "applicationFormallySubmitted": "申请正式提交", "applicationSubmitted": "申请提交", "failedToGetItemRequestHistory": "获取物品申请历史失败", "failedToLoadItemPriceTrend": "加载物品价格趋势失败", "shoppingCartRiskAlert": "购物车风险提醒", "itemsExistRisk": "以下物品存在风险，请谨慎考虑采购：", "unknownItem": "未知物品", "risk": "风险", "back": "返回", "itemPurchaseHistoryTrend": "物品历史采购量趋势", "itemPurchaseHistoryTrendDesc": "显示最近6个月该部门各物品的采购数量趋势", "failedToGetItemPurchaseHistory": "获取物品采购历史失败", "editRequest": "编辑申请", "addToRequest": "添加到申请", "saveChanges": "保存更改", "applicationInfo": "申请信息", "enterBusinessJustification": "请输入业务理由", "expectedDelivery": "预期交货时间", "applicationDetails": "申请明细", "shoppingCartItems": "购物车物品", "operation": "操作", "purchaseRequestManagement": "采购申请管理", "noPurchaseHistoryData": "暂无采购历史数据", "showPurchaseTrendForLast12Months": "显示最近12个月各物品的采购量趋势", "approvalPermissionDenied": "审批权限不足", "noApprovalPermission": "您没有权限审批此申请"}