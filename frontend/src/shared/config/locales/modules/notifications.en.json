{"title": "Notification Management", "type": {"inventory_alert": "Inventory Alert", "approval_flow": "Approval Flow", "system": "System", "other": "Other"}, "status": {"unread": "Unread", "read": "Read"}, "actions": {"mark_read": "<PERSON> <PERSON>", "view": "View", "delete": "Delete"}, "filter": {"status": "Status", "status_placeholder": "Select Status", "type": "Type", "type_placeholder": "Select Type", "search": "Search", "search_placeholder": "Search notification title", "date_range": "Date Range", "start_date": "Start Date", "end_date": "End Date", "page_size": "<PERSON>"}, "batch": {"selected_count": "{{count}} items selected", "actions_available": "Batch actions available", "mark_read": "<PERSON> <PERSON>", "delete": "Delete", "clear_selection": "Clear Selection", "no_selection": "Please select notifications to operate", "delete_confirm_title": "Confirm Delete", "delete_confirm_description": "Are you sure you want to delete {{count}} selected notifications? This action cannot be undone.", "mark_read_success": "Batch mark as read successful", "mark_read_error": "Batch mark as read failed", "delete_success": "Batch delete successful", "delete_error": "Batch delete failed"}, "detail": {"title": "Notification Detail", "info": "Notification Information", "id": "Notification ID", "type": "Notification Type", "status": "Status", "created_at": "Created At", "read_at": "Read At", "action_url": "Action URL", "business_data": "Business Data", "view_related": "View Related", "delete": "Delete"}, "config": {"title": "Notification Configuration", "description": "Configure system notifications and email sending settings", "smtp_title": "SMTP Configuration", "smtp_host": "SMTP Host", "smtp_host_required": "Please enter SMTP host", "smtp_host_placeholder": "e.g., smtp.gmail.com", "smtp_port": "SMTP Port", "smtp_port_required": "Please enter SMTP port", "smtp_port_placeholder": "e.g., 587", "smtp_username": "SMTP Username", "smtp_username_required": "Please enter SMTP username", "smtp_username_placeholder": "Email address", "smtp_password": "SMTP Password", "smtp_password_required": "Please enter SMTP password", "smtp_password_placeholder": "Email password or app password", "smtp_use_tls": "Use TLS Encryption", "email_send_interval": "Email Send Interval", "email_send_interval_required": "Please enter email send interval", "email_send_interval_placeholder": "Send interval (minutes)", "email_send_interval_unit": "minutes", "notification_title": "Notification Settings", "notification_enabled": "Enable System Notifications", "notification_enabled_description": "Control whether system notifications are enabled", "test_email_title": "Test Email", "test_email": "Test Email", "test_email_invalid": "Please enter a valid email address", "test_email_placeholder": "Email address to receive test email", "send_test_email": "Send Test Email", "test_email_description": "Send a test email to verify SMTP configuration is correct", "save": "Save Configuration", "reset": "Reset", "load_error": "Failed to load configuration", "save_success": "Configuration saved successfully", "save_error": "Failed to save configuration", "test_email_success": "Test email sent successfully", "test_email_error": "Failed to send test email", "test_email_validation_error": "Please check form input", "test_email_subject": "Test Email - BizLinkSpeedy IDM System", "test_email_content": "This is a test email to verify that the system email configuration is correct. If you receive this email, it means the email configuration is successful."}, "management": {"title": "Notification Management", "description": "View all notifications, statistics and email sending status", "overview": "Overview", "all_notifications": "All Notifications", "email_logs": "<PERSON><PERSON>", "total_notifications": "Total Notifications", "unread_count": "Unread Count", "total_emails": "Total Emails", "email_success_rate": "Email Success Rate", "notification_type_chart": "Notification Type Distribution", "email_status_chart": "Email Status Distribution", "notification_status_chart": "Notification Status Distribution", "trigger_email": "<PERSON><PERSON>", "refresh": "Refresh", "trigger_email_success": "Email trigger successful", "trigger_email_error": "Email trigger failed", "load_stats_error": "Failed to load statistics", "email_logs_coming_soon": "Email logs feature coming soon"}, "email_status": {"pending": "Pending", "sent": "<PERSON><PERSON>", "failed": "Failed"}, "charts": {"no_data": "No data available", "count": "Count"}, "load_error": "Failed to load notifications", "mark_read_success": "Marked as read successfully", "mark_read_error": "Failed to mark as read", "delete_success": "Deleted successfully", "delete_error": "Failed to delete", "load_detail_error": "Failed to load notification detail", "not_found": "Notification not found", "no_data": "No notification data available"}