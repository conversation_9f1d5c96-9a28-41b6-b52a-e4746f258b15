{"overviewTab": "Inventory Overview", "inventoryListTab": "Inventory List", "changesTab": "Change Records", "alertsTab": "Alert Management", "statisticsTab": "Usage Statistics", "exportFeatureInDevelopment": "Export feature is under development...", "added": "Added", "updated": "Updated", "inventory": "Inventory", "processingFailed": "Processing Failed", "addResultDetails": "Add Result Details", "successfullyProcessedItems": "Successfully Processed Items", "noItemsSuccessfullyAddedToCart": "No items were successfully added to cart", "addOutOfStockItemsToCartFailed": "Failed to add out-of-stock items to cart", "addFailedPleaseRetry": "Add failed, please retry", "alertResolved": "<PERSON><PERSON> resolved", "resolveAlertFailed": "Failed to resolve alert", "stockLevel": "Stock Level", "inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock", "stockQuantity": "Stock Quantity", "minStock": "Minimum Stock", "maxStock": "Maximum Stock", "reorderPoint": "Reorder Point", "warehouse": "Warehouse", "location": "Location", "batchNumber": "Batch Number", "expiryDate": "Expiry Date", "manufacturingDate": "Manufacturing Date", "lastUpdated": "Last Updated", "image": "Image", "noImage": "No Image", "itemInfo": "Item Info", "inventoryStatus": "Inventory Status", "noInventory": "No Inventory", "disabled": "Disabled", "inventoryValue": "Inventory Value", "calculatedUsdPrice": "Unit Price", "justNow": "Just now", "minutesAgo": "minutes ago", "hoursAgo": "hours ago", "totalInventory": "Total Inventory", "lowStockCount": "Low Stock Count", "outOfStockCount": "Out of Stock Count", "totalInventoryValue": "Total Inventory Value", "totalInventoryValueUsd": "Total Inventory Value (USD)", "inventoryStatusDistribution": "Inventory Status Distribution", "normal": "Normal", "overstock": "Overstock", "department": "Department", "itemName": "Item Name", "itemCode": "Item Code", "beforeChange": "Before Change", "afterChange": "After Change", "changeType": "Change Type", "changeTime": "Change Time", "manualIn": "Manual In", "pickupOut": "Pickup Out", "adjust": "Adjust", "transfer": "Transfer", "return": "Return", "alertType": "Alert <PERSON>", "alertLevel": "Alert <PERSON>", "currentStock": "Current Stock", "info": "Info", "warning": "Warning", "critical": "Critical", "usage": "Usage", "usageCount": "Usage Count", "averagePrice": "Average Price", "firstUsage": "First Usage", "lastUsage": "Last Usage", "activeAlerts": "Active Alerts", "unresolvedAlerts": "Unresolved <PERSON>", "unresolvedAlertsMessage": "There are unresolved inventory alerts", "unresolvedAlertsDescription": "Please handle inventory alerts promptly to ensure normal production operations.", "changeQuantity": "Change Quantity", "operator": "Operator", "actions": "Actions", "details": "Details", "searchItemPlaceholder": "Search item name or code", "changeTypePlaceholder": "Change Type", "refresh": "Refresh", "paginationInfo": "{{start}}-{{end}} of {{total}} items", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "resolved": "Resolved", "unresolved": "Unresolved", "createTime": "Create Time", "resolve": "Resolve", "alertLevelPlaceholder": "Alert <PERSON>", "resolvedStatusPlaceholder": "Resolved Status", "resolvedStatus": "Resolved", "unresolvedStatus": "Unresolved", "settings": "Settings", "inventoryStatusPlaceholder": "Inventory Status", "enabled": "Enabled", "low": "Low Stock", "allItems": "All Items", "addOutOfStockToCart": "Add Out-of-Stock Items to Cart", "currentDisplay": "Currently displaying: {{department}} inventory data", "currentDepartment": "Current Department"}