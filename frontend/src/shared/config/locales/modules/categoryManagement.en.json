{"categoryManagement": "Category Management", "name": "Category Name", "description": "Description", "primaryCategory": "Primary Category", "status": "Status", "actions": "Actions", "edit": "Edit", "delete": "Delete", "active": "Active", "inactive": "Inactive", "confirmDelete": "Are you sure you want to delete this category?", "deleteDescription": "This action cannot be undone.", "confirm": "Confirm", "cancel": "Cancel", "addCategory": "Add Category", "editCategory": "Edit Category", "basicInfo": "Basic Information", "nameRequired": "Please enter category name", "nameMaxLength": "Category name cannot exceed 100 characters", "enterName": "Please enter category name", "primaryCategoryRequired": "Please select primary category", "primaryCategoryManagement": "Primary Category Management", "addPrimaryCategory": "Add Primary Category", "editPrimaryCategory": "Edit Primary Category", "primaryCategoryName": "Category Name", "primaryCategoryNameRequired": "Please enter category name", "primaryCategoryNameMaxLength": "Category name cannot exceed 100 characters", "codePrefix": "Code Prefix", "codePrefixRequired": "Please enter code prefix", "codePrefixMaxLength": "Code prefix cannot exceed 10 characters", "codePrefixPattern": "Code prefix can only contain uppercase letters and numbers", "codePrefixPlaceholder": "E.g.: LB, BG, SC", "codeFormat": "Code Format", "codeFormatRequired": "Please enter code format", "codeFormatMaxLength": "Code format cannot exceed 20 characters", "codeFormatPlaceholder": "E.g.: 0000", "currentSequence": "Current Sequence", "currentSequenceRequired": "Please enter current sequence", "currentSequenceMin": "Sequence must be greater than 0", "confirmDeletePrimaryCategory": "Are you sure you want to delete this primary category?", "confirmDeletePrimaryCategoryDescription": "This action cannot be undone and will affect related subcategories.", "primaryCategoryUpdateSuccess": "Primary category updated successfully", "primaryCategoryCreateSuccess": "Primary category created successfully", "primaryCategoryDeleteSuccess": "Primary category deleted successfully", "getPrimaryCategoryListFailed": "Failed to get primary category list", "operationFailed": "Operation failed", "deleteFailed": "Delete failed", "enabled": "Enabled", "disabled": "Disabled", "selectPrimaryCategory": "Please select primary category", "enterDescription": "Please enter category description", "attributeConfigs": "Attribute Configurations", "brand": "Brand (brand)", "specMaterial": "Specification/Material (spec_material)", "sizeDimension": "Size/Dimension (size_dimension)", "optionMode": "Option Mode", "options": "Options", "text": "Text", "optionValues": "Option Values", "optionValuesTooltip": "One option per line", "enterOptionValues": "Please enter option values, one per line", "update": "Update", "create": "Create", "productCategory": "Product Category", "noCategoryData": "No category data", "getCategoryDataFailed": "Failed to get category data", "currentFilter": "Current Filter: ", "clear": "Clear"}