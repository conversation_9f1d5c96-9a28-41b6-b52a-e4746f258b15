// 导入所有模块的语言包
import common from './modules/common.json';
import navigation from './modules/navigation.json';
import user from './modules/user.json';
import item from './modules/item.json';
import inventory from './modules/inventory.json';
import purchase from './modules/purchase.json';
import supplier from './modules/supplier.json';
import system from './modules/system.json';
import messages from './modules/messages.json';
import units from './modules/units.json';
import status from './modules/status.json';
import login from './modules/login.json';
import categoryManagement from './modules/categoryManagement.json';
import roleManagement from './modules/roleManagement.json';
import reports from './modules/reports.json';
import imageManagement from './modules/imageManagement.json';
import notifications from './modules/notifications.json';
import exchangeRate from './modules/exchangeRate.json';

// 英文模块
import commonEn from './modules/common.en.json';
import navigationEn from './modules/navigation.en.json';
import userEn from './modules/user.en.json';
import itemEn from './modules/item.en.json';
import inventoryEn from './modules/inventory.en.json';
import purchaseEn from './modules/purchase.en.json';
import supplierEn from './modules/supplier.en.json';
import systemEn from './modules/system.en.json';
import messagesEn from './modules/messages.en.json';
import unitsEn from './modules/units.en.json';
import statusEn from './modules/status.en.json';
import loginEn from './modules/login.en.json';
import categoryManagementEn from './modules/categoryManagement.en.json';
import roleManagementEn from './modules/roleManagement.en.json';
import reportsEn from './modules/reports.en.json';
import imageManagementEn from './modules/imageManagement.en.json';
import notificationsEn from './modules/notifications.en.json';
import exchangeRateEn from './modules/exchangeRate.en.json';

// 合并中文语言包
export const zh = {
  common,
  navigation,
  user,
  item,
  inventory,
  purchase,
  supplier,
  system,
  messages,
  units,
  status,
  login,
  categoryManagement,
  roleManagement,
  reports,
  imageManagement,
  notifications,
  exchangeRate,
};

// 合并英文语言包
export const en = {
  common: commonEn,
  navigation: navigationEn,
  user: userEn,
  item: itemEn,
  inventory: inventoryEn,
  purchase: purchaseEn,
  supplier: supplierEn,
  system: systemEn,
  messages: messagesEn,
  units: unitsEn,
  status: statusEn,
  login: loginEn,
  categoryManagement: categoryManagementEn,
  roleManagement: roleManagementEn,
  reports: reportsEn,
  imageManagement: imageManagementEn,
  notifications: notificationsEn,
  exchangeRate: exchangeRateEn,
};

// 类型定义
export type LocaleType = 'zh' | 'en';
export type LocaleModules = typeof zh;
