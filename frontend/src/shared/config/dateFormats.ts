// 日期格式配置
export const DATE_FORMATS = {
  // 纯日期格式（前端显示用）
  DATE_ONLY: 'DD-MM-YYYY',
  // 日期+时间格式
  DATE_TIME: 'DD-MM-YYYY HH:mm:ss',
  // 日期+时间（分钟）格式
  DATE_TIME_MINUTES: 'DD-MM-YYYY HH:mm',
  // 图表标签格式
  CHART_LABEL: 'DD-MM',
  // 图表标签（完整日期）格式
  CHART_LABEL_FULL: 'DD-MM-YYYY',
  // API调用用的日期格式（YYYY-MM-DD）
  API_DATE: 'YYYY-MM-DD',
} as const;

// 日期格式类型
export type DateFormatType = typeof DATE_FORMATS[keyof typeof DATE_FORMATS];

// 导出默认格式
export default DATE_FORMATS;
