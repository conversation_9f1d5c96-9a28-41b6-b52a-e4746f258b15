/**
 * 权限配置
 * 定义各个功能模块的权限映射
 */

export const PERMISSIONS = {
  // 用户管理权限
  USER: {
    READ: 'user.read',
    CREATE: 'user.create',
    UPDATE: 'user.update',
    DELETE: 'user.delete',
    RESET_PASSWORD: 'user.reset_password',
    MANAGE_STATUS: 'user.manage_status',
  },

  // 角色管理权限
  ROLE: {
    READ: 'role.read',
    CREATE: 'role.create',
    UPDATE: 'role.update',
    DELETE: 'role.delete',
    ASSIGN: 'role.assign',
    REVOKE: 'role.revoke',
  },

  // 权限管理权限
  PERMISSION: {
    READ: 'permission.read',
    ASSIGN: 'permission.assign',
  },

  // 物品管理权限
  ITEM: {
    READ: 'item.read',
    CREATE: 'item.create',
    UPDATE: 'item.update',
    DELETE: 'item.delete',
    CATEGORY_MANAGE: 'item.category_manage',
  },

  // 库存管理权限
  INVENTORY: {
    READ: 'inventory.read',
    UPDATE: 'inventory.update',
    TRANSFER: 'inventory.transfer',
    COUNT: 'inventory.count',
    ADJUST: 'inventory.adjust',
    ALERT: 'inventory.alert',
  },

  // 采购管理权限
  PURCHASE: {
    REQUEST: 'purchase.request',
    READ: 'purchase.read',
    READ_ALL: 'purchase.read_all',
    UPDATE: 'purchase.update',
    DELETE: 'purchase.delete',
    APPROVE: 'purchase.approve',
    PRINCIPLE_APPROVE: 'purchase.principle_approve',
    REJECT: 'purchase.reject',
    EXECUTE: 'purchase.execute',
    RECEIVE: 'purchase.receive',
    FINAL_APPROVE: 'purchase.final_approve',
    REVIEW: 'purchase.review',
    WITHDRAW: 'purchase.withdraw',
  },

  // 购物车管理权限
  CART: {
    VIEW: 'cart.view',
    VIEW_ALL: 'cart.view_all',
    ADD_ITEM: 'cart.add_item',
    ADD_ITEM_ALL: 'cart.add_item_all',
    UPDATE_ITEM: 'cart.update_item',
    REMOVE_ITEM: 'cart.remove_item',
    SUBMIT: 'cart.submit',
    SUBMIT_ALL: 'cart.submit_all',
  },

  // 供应商管理权限
  SUPPLIER: {
    READ: 'supplier.read',
    CREATE: 'supplier.create',
    UPDATE: 'supplier.update',
    DELETE: 'supplier.delete',
    PRICE_MANAGE: 'supplier.price_manage',
    EVALUATE: 'supplier.evaluate',
  },

  // 供应商管理权限（简化版）
  SUPPLIER_MANAGE: 'supplier.manage',

  // 报表权限
  REPORT: {
    USAGE: 'report.usage',
    INVENTORY: 'report.inventory',
    PURCHASE: 'report.purchase',
    COST: 'report.cost',
    ADMIN: 'report.admin',
  },

  // 部门管理权限
  DEPARTMENT: {
    READ: 'department.read',
    CREATE: 'department.create',
    UPDATE: 'department.update',
    DELETE: 'department.delete',
    MANAGE_USERS: 'department.manage_users',
  },

  // 系统管理权限
  SYSTEM: {
    CONFIG: 'system.config',
    LOG: 'system.log',
    BACKUP: 'system.backup',
    RESTORE: 'system.restore',
    AUDIT: 'system.audit',
  },

  // 通知管理权限
  NOTIFICATION: {
    READ: 'notification.read',
    CREATE: 'notification.create',
    UPDATE: 'notification.update',
    DELETE: 'notification.delete',
    MANAGE: 'notification.manage',
    CONFIG: 'notification.config',
  },
};

/**
 * 功能模块权限映射
 * 用于菜单显示和路由控制
 */
export const MODULE_PERMISSIONS = {
  // 物品管理模块
  ITEMS: [
    PERMISSIONS.ITEM.READ,
    PERMISSIONS.ITEM.CREATE,
    PERMISSIONS.ITEM.UPDATE,
    PERMISSIONS.ITEM.DELETE,
    PERMISSIONS.ITEM.CATEGORY_MANAGE,
  ],

  // 分类管理模块（作为物品管理的一部分）
  CATEGORIES: [
    PERMISSIONS.ITEM.CATEGORY_MANAGE,
  ],

  // 供应商管理模块
  SUPPLIERS: [
    PERMISSIONS.SUPPLIER.READ,
    PERMISSIONS.SUPPLIER.CREATE,
    PERMISSIONS.SUPPLIER.UPDATE,
    PERMISSIONS.SUPPLIER.DELETE,
    PERMISSIONS.SUPPLIER.PRICE_MANAGE,
    PERMISSIONS.SUPPLIER.EVALUATE,
  ],

  // 库存管理模块
  INVENTORY: [
    PERMISSIONS.INVENTORY.READ,
    PERMISSIONS.INVENTORY.UPDATE,
    PERMISSIONS.INVENTORY.TRANSFER,
    PERMISSIONS.INVENTORY.COUNT,
    PERMISSIONS.INVENTORY.ADJUST,
    PERMISSIONS.INVENTORY.ALERT,
  ],

  // 采购申请模块
  PURCHASE_REQUESTS: [
    PERMISSIONS.PURCHASE.REQUEST,
    PERMISSIONS.PURCHASE.READ,
    PERMISSIONS.PURCHASE.READ_ALL,
    PERMISSIONS.PURCHASE.UPDATE,
    PERMISSIONS.PURCHASE.DELETE,
    PERMISSIONS.PURCHASE.APPROVE,
    PERMISSIONS.PURCHASE.PRINCIPLE_APPROVE,
    PERMISSIONS.PURCHASE.REJECT,
    PERMISSIONS.PURCHASE.EXECUTE,
    PERMISSIONS.PURCHASE.RECEIVE,
    PERMISSIONS.PURCHASE.FINAL_APPROVE,
    PERMISSIONS.PURCHASE.REVIEW,
    PERMISSIONS.PURCHASE.WITHDRAW,
  ],

  // 采购执行模块
  PURCHASE_EXECUTION: [
    PERMISSIONS.PURCHASE.EXECUTE,
    PERMISSIONS.PURCHASE.READ,
    PERMISSIONS.PURCHASE.READ_ALL,
  ],

  // 购物车管理模块
  SHOPPING_CART: [
    PERMISSIONS.CART.VIEW,
    PERMISSIONS.CART.VIEW_ALL,
    PERMISSIONS.CART.ADD_ITEM,
    PERMISSIONS.CART.ADD_ITEM_ALL,
    PERMISSIONS.CART.UPDATE_ITEM,
    PERMISSIONS.CART.REMOVE_ITEM,
    PERMISSIONS.CART.SUBMIT,
    PERMISSIONS.CART.SUBMIT_ALL,
  ],

  // 报表模块
  REPORTS: [
    PERMISSIONS.REPORT.USAGE,
    PERMISSIONS.REPORT.INVENTORY,
    PERMISSIONS.REPORT.PURCHASE,
    PERMISSIONS.REPORT.COST,
    PERMISSIONS.REPORT.ADMIN,
  ],

  // 用户管理模块
  USERS: [
    PERMISSIONS.USER.READ,
    PERMISSIONS.USER.CREATE,
    PERMISSIONS.USER.UPDATE,
    PERMISSIONS.USER.DELETE,
    PERMISSIONS.USER.RESET_PASSWORD,
    PERMISSIONS.USER.MANAGE_STATUS,
  ],

  // 角色权限模块
  ROLES: [
    PERMISSIONS.ROLE.READ,
    PERMISSIONS.ROLE.CREATE,
    PERMISSIONS.ROLE.UPDATE,
    PERMISSIONS.ROLE.DELETE,
    PERMISSIONS.ROLE.ASSIGN,
    PERMISSIONS.ROLE.REVOKE,
    PERMISSIONS.PERMISSION.READ,
    PERMISSIONS.PERMISSION.ASSIGN,
  ],

  // 部门管理模块
  DEPARTMENTS: [
    PERMISSIONS.DEPARTMENT.READ,
    PERMISSIONS.DEPARTMENT.CREATE,
    PERMISSIONS.DEPARTMENT.UPDATE,
    PERMISSIONS.DEPARTMENT.DELETE,
    PERMISSIONS.DEPARTMENT.MANAGE_USERS,
  ],

  // 系统管理模块
  SYSTEM_MANAGEMENT: [
    PERMISSIONS.SYSTEM.CONFIG,
    PERMISSIONS.SYSTEM.LOG,
    PERMISSIONS.SYSTEM.BACKUP,
    PERMISSIONS.SYSTEM.RESTORE,
    PERMISSIONS.SYSTEM.AUDIT,
  ],

  // 通知管理模块
  NOTIFICATIONS: [
    PERMISSIONS.NOTIFICATION.READ,
    PERMISSIONS.NOTIFICATION.CREATE,
    PERMISSIONS.NOTIFICATION.UPDATE,
    PERMISSIONS.NOTIFICATION.DELETE,
  ],
};

/**
 * 获取模块权限列表
 * @param module 模块名称
 * @returns 权限列表
 */
export const getModulePermissions = (module: keyof typeof MODULE_PERMISSIONS): string[] => {
  return MODULE_PERMISSIONS[module] || [];
};

/**
 * 检查是否有模块访问权限
 * @param userPermissions 用户权限列表
 * @param module 模块名称
 * @returns 是否有权限
 */
export const hasModulePermission = (userPermissions: string[], module: keyof typeof MODULE_PERMISSIONS): boolean => {
  const modulePermissions = getModulePermissions(module);
  return modulePermissions.some(permission => userPermissions.includes(permission));
}; 