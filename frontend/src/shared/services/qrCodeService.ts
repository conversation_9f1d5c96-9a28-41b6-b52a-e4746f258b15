/**
 * 二维码服务
 * 用于生成和解析物品管理系统的二维码内容
 */

export enum QRCodeType {
  ITEM = "i",           // 物品
  DEPARTMENT = "d",     // 部门
  CUSTOMER = "c",       // 员工/客户
  SUPPLIER = "s"        // 供应商
}

export interface QRCodeData {
  type: QRCodeType;
  code: string;
  name: string;
  remark?: string;
  version: string;
  rawContent: string;
}

export class QRCodeService {
  private static readonly VERSION = "2.0";

  /**
   * 生成物品二维码内容（简化版）
   */
  static generateItemQRCode(item: {
    code: string;
    name: string;
    brand?: string;
    spec_material?: string;
    size_dimension?: string;
  }): string {
    return item.code || "";
  }

  /**
   * 生成部门二维码内容（简化版）
   */
  static generateDepartmentQRCode(department: {
    code: string;
    name: string;
  }): string {
    return department.code || "";
  }

  /**
   * 生成员工二维码内容（简化版）
   */
  static generateEmployeeQRCode(user: {
    employee_id: string;
    display_name?: string;
    full_name?: string;
    username: string;
  }): string {
    return user.employee_id || "";
  }

  /**
   * 生成供应商二维码内容（简化版）
   */
  static generateSupplierQRCode(supplier: {
    code: string;
    name_cn?: string;
    name_en?: string;
  }): string {
    return supplier.code || "";
  }

  /**
   * 根据编码格式判断二维码类型
   */
  static detectQRCodeType(code: string): QRCodeType | null {
    if (!code) {
      return null;
    }
    
    const cleanCode = code.trim();
    
    if (cleanCode.startsWith("IDM")) {
      return QRCodeType.ITEM;
    } else if (cleanCode.startsWith("DEPT") || ["IT", "HR", "PUR", "WH", "ME", "SI", "AD", "QA"].includes(cleanCode)) {
      return QRCodeType.DEPARTMENT;
    } else if (cleanCode.startsWith("SUP")) {
      return QRCodeType.SUPPLIER;
    } else if (cleanCode.startsWith("EMP")) {
      return QRCodeType.CUSTOMER;
    }
    
    return null;
  }

  /**
   * 解析二维码内容（简化版）
   */
  static parseQRCode(qrContent: string): QRCodeData {
    try {
      if (!qrContent || !qrContent.trim()) {
        throw new Error("二维码内容为空");
      }

      const code = qrContent.trim();
      const qrType = this.detectQRCodeType(code);

      if (!qrType) {
        throw new Error(`无法识别的编码格式：${code}`);
      }

      return {
        type: qrType,
        code: code,
        name: "",
        remark: "",
        version: this.VERSION,
        rawContent: qrContent
      };
    } catch (error) {
      throw new Error(`二维码解析失败：${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取二维码类型
   */
  static getQRCodeType(qrContent: string): QRCodeType | null {
    try {
      const parsed = this.parseQRCode(qrContent);
      return parsed.type;
    } catch {
      return null;
    }
  }

  /**
   * 验证二维码内容是否有效
   */
  static isValidQRCode(qrContent: string): boolean {
    try {
      this.parseQRCode(qrContent);
      return true;
    } catch {
      return false;
    }
  }
}
