// 入库管理模块类型定义

export interface ScanQRCodeRequest {
  qr_code: string;
}

export interface InboundItemInfo {
  id: number;
  item_id: number;
  item_code: string;
  item_name: string;
  spq_quantity: number;
  spq_count: number;
  spq_unit: string;
  requested_quantity: number;
  notes?: string;
}

export interface ScanQRCodeResponse {
  success: boolean;
  message: string;
  purchase_request?: PurchaseRequestInfo;
  items: InboundItemInfo[];
}

export interface PurchaseRequestInfo {
  id: number;
  request_no: string;
  department_id: number;
  submitter_id: number;
  status: string;
  submitted_at?: string;
  notes?: string;
  department_name?: string;  // 添加部门名称
  department_code?: string;  // 添加部门代码
  submitter_name?: string;   // 添加提交人姓名
}

export interface InboundItemRequest {
  item_id: number;
  inbound_quantity: number;
  notes?: string;
}

export interface InboundRequest {
  purchase_request_id: number;
  department_id: number;
  items: InboundItemRequest[];
  notes?: string;
}

export interface InboundItemResult {
  item_id: number;
  item_code: string;
  item_name: string;
  inbound_quantity: number;
  before_quantity: number;
  after_quantity: number;
  success: boolean;
  message: string;
}

export interface InboundResponse {
  success: boolean;
  message: string;
  inbound_id?: string;
  results: InboundItemResult[];
  total_items: number;
  success_items: number;
  failed_items: number;
  created_at: string;
}
