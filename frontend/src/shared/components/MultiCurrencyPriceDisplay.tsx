import React from 'react';
import { Tag, Tooltip } from 'antd';
import { InfoCircleOutlined, WarningOutlined } from '@ant-design/icons';
import { formatPriceDisplay, getCurrencyInfo, SUPPORTED_CURRENCIES } from '@shared/utils/exchangeRateUtils';

export interface ExchangeRateInfo {
  currency_code: string;
  original_unit_price: number;
  original_total_price: number;
  exchange_rate: number;
  usd_unit_price: number;
  usd_total_price: number;
  rate_type: 'current_month' | 'historical';
  effective_month: string;
  is_valid: boolean;
  warning?: string;
  error?: string;
}

export interface MultiCurrencyPriceDisplayProps {
  // 价格信息
  originalPrice: number | string;
  currencyCode: string;
  
  // 汇率信息（可选）
  exchangeRateInfo?: ExchangeRateInfo;
  
  // 显示类型
  displayType: 'unit' | 'total';
  
  // 样式配置
  size?: 'small' | 'medium' | 'large';
  showCurrencyTag?: boolean;
  showExchangeRate?: boolean;
  showWarning?: boolean;
  
  // 自定义样式
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 多货币价格显示组件
 * 支持显示原币价格和转换后的美元价格
 */
const MultiCurrencyPriceDisplay: React.FC<MultiCurrencyPriceDisplayProps> = ({
  originalPrice,
  currencyCode,
  exchangeRateInfo,
  displayType,
  size = 'medium',
  showCurrencyTag = true,
  showExchangeRate = true,
  showWarning = true,
  className,
  style
}) => {
  // 获取货币信息
  const currencyInfo = getCurrencyInfo(currencyCode as keyof typeof SUPPORTED_CURRENCIES);
  const isUSD = currencyCode === 'USD';
  
  // 处理价格数值
  const numPrice = typeof originalPrice === 'string' ? parseFloat(originalPrice) : originalPrice;
  const isValidPrice = !isNaN(numPrice) && numPrice > 0;
  
  // 如果没有有效价格，显示占位符
  if (!isValidPrice) {
    return <span className={className} style={style}>-</span>;
  }
  
  // 如果有汇率信息且不是USD，显示多货币价格
  if (exchangeRateInfo && !isUSD && exchangeRateInfo.is_valid) {
    const { usd_unit_price, usd_total_price, exchange_rate, rate_type, effective_month, warning } = exchangeRateInfo;
    const usdPrice = displayType === 'unit' ? usd_unit_price : usd_total_price;
    
    // 根据大小确定字体大小
    const getFontSize = () => {
      switch (size) {
        case 'small': return { main: '12px', sub: '10px', rate: '9px' };
        case 'large': return { main: '16px', sub: '14px', rate: '12px' };
        default: return { main: '14px', sub: '12px', rate: '11px' };
      }
    };
    
    const fontSize = getFontSize();
    
    return (
      <div className={className} style={style}>
        {/* 原币价格 */}
        <div style={{ 
          fontSize: fontSize.sub, 
          color: '#666', 
          marginBottom: '2px',
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }}>
          <span>{currencyInfo.symbol} {numPrice.toFixed(4)}</span>
          {showCurrencyTag && (
            <Tag color="blue">
              {currencyInfo.name}
            </Tag>
          )}
        </div>
        
        {/* 汇率信息 */}
        {showExchangeRate && (
          <div style={{ 
            fontSize: fontSize.rate, 
            color: '#999', 
            marginBottom: '2px',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}>
            <span>汇率: {exchange_rate.toFixed(6)}</span>
            <Tag color={rate_type === 'current_month' ? 'green' : 'orange'}>
              {rate_type === 'current_month' ? '当月' : '历史'} {effective_month}
            </Tag>
          </div>
        )}
        
        {/* 美元价格 */}
        <div style={{ 
          fontSize: fontSize.main, 
          fontWeight: 'bold', 
          color: '#1677ff',
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }}>
          <span>${usdPrice.toFixed(displayType === 'unit' ? 4 : 2)}</span>
          <Tag color="green">USD</Tag>
        </div>
        
        {/* 警告信息 */}
        {showWarning && warning && (
          <div style={{ 
            fontSize: fontSize.rate, 
            color: '#faad14', 
            marginTop: '2px',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}>
            <WarningOutlined />
            <span>{warning}</span>
          </div>
        )}
      </div>
    );
  }
  
  // 如果没有汇率信息或汇率无效，显示原币价格
  if (!exchangeRateInfo || !exchangeRateInfo.is_valid) {
    return (
      <div className={className} style={style}>
        <div style={{ 
          fontSize: size === 'small' ? '12px' : size === 'large' ? '16px' : '14px',
          color: isUSD ? '#52c41a' : '#666'
        }}>
          {isUSD ? '$' : currencyInfo.symbol} {numPrice.toFixed(displayType === 'unit' ? 4 : 2)}
        </div>
        {showCurrencyTag && !isUSD && (
          <div style={{ marginTop: '2px' }}>
            <Tag color="orange">
              {currencyInfo.name}
            </Tag>
            {!exchangeRateInfo && (
              <Tooltip title="暂无汇率数据，无法转换为美元">
                <InfoCircleOutlined style={{ marginLeft: '4px', color: '#faad14' }} />
              </Tooltip>
            )}
          </div>
        )}
      </div>
    );
  }
  
  // 如果是USD，直接显示
  if (isUSD) {
    return (
      <div className={className} style={style}>
        <div style={{ 
          fontSize: size === 'small' ? '12px' : size === 'large' ? '16px' : '14px',
          color: '#52c41a',
          fontWeight: 'bold'
        }}>
          ${numPrice.toFixed(displayType === 'unit' ? 4 : 2)}
        </div>
        {showCurrencyTag && (
          <Tag color="green">USD</Tag>
        )}
      </div>
    );
  }
  
  // 默认情况
  return (
    <div className={className} style={style}>
      <div style={{ 
        fontSize: size === 'small' ? '12px' : size === 'large' ? '16px' : '14px',
        color: '#666'
      }}>
        {currencyInfo.symbol} {numPrice.toFixed(displayType === 'unit' ? 4 : 2)}
      </div>
      {showCurrencyTag && (
        <Tag color="blue">
          {currencyInfo.name}
        </Tag>
      )}
    </div>
  );
};

export default MultiCurrencyPriceDisplay;
