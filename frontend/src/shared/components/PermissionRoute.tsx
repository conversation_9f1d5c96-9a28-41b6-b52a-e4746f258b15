import React from 'react';
import { useAuth } from '../../apps/admin/contexts/AuthContext';
import NoPermission from '@admin/pages/NoPermission';

interface PermissionRouteProps {
  children: React.ReactNode;
  permission?: string;
  permissions?: string[];
  requireAll?: boolean;
}

/**
 * 权限路由组件
 * @param permission 单个权限
 * @param permissions 多个权限
 * @param requireAll 是否需要所有权限（默认false，即任意一个权限即可）
 */
const PermissionRoute: React.FC<PermissionRouteProps> = ({
  children,
  permission,
  permissions,
  requireAll = false,
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions } = useAuth();

  let hasAccess = false;

  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (permissions) {
    if (requireAll) {
      hasAccess = hasAllPermissions(permissions);
    } else {
      hasAccess = hasAnyPermission(permissions);
    }
  } else {
    // 如果没有指定权限要求，则允许访问
    hasAccess = true;
  }

  return hasAccess ? <>{children}</> : <NoPermission />;
};

export default PermissionRoute; 