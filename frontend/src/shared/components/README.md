# 多货币价格显示组件

本目录包含了用于显示多货币价格的通用组件，支持原币价格和转换后的美元价格显示。

## 组件列表

### 1. MultiCurrencyPriceDisplay

用于显示单价和总价的多货币价格显示组件。

#### 功能特性

- 支持多种货币代码（USD、CNY、EUR、JPY、GBP、KRW、SGD、HKD）
- 自动显示原币价格和转换后的美元价格
- 支持汇率信息显示（当前月汇率 vs 历史汇率）
- 支持警告信息显示
- 可配置显示选项（货币标签、汇率信息、警告信息）
- 支持三种尺寸（small、medium、large）

#### 使用示例

```tsx
import MultiCurrencyPriceDisplay from '@shared/components/MultiCurrencyPriceDisplay';

// 基本使用
<MultiCurrencyPriceDisplay
  originalPrice={6.85}
  currencyCode="CNY"
  displayType="unit"
/>

// 带汇率信息
<MultiCurrencyPriceDisplay
  originalPrice={6.85}
  currencyCode="CNY"
  exchangeRateInfo={{
    currency_code: 'CNY',
    original_unit_price: 6.85,
    original_total_price: 68.50,
    exchange_rate: 7.2,
    usd_unit_price: 0.9514,
    usd_total_price: 9.51,
    rate_type: 'current_month',
    effective_month: '2025-01-01',
    is_valid: true
  }}
  displayType="unit"
  size="medium"
  showCurrencyTag={true}
  showExchangeRate={true}
  showWarning={true}
/>
```

#### 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `originalPrice` | `number \| string` | - | 原币价格 |
| `currencyCode` | `string` | - | 货币代码 |
| `exchangeRateInfo` | `ExchangeRateInfo` | `undefined` | 汇率信息 |
| `displayType` | `'unit' \| 'total'` | - | 显示类型（单价/总价） |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | 组件尺寸 |
| `showCurrencyTag` | `boolean` | `true` | 是否显示货币标签 |
| `showExchangeRate` | `boolean` | `true` | 是否显示汇率信息 |
| `showWarning` | `boolean` | `true` | 是否显示警告信息 |
| `className` | `string` | - | 自定义CSS类名 |
| `style` | `CSSProperties` | - | 自定义样式 |

### 2. MultiCurrencyTotalDisplay

用于显示总金额的多货币总金额显示组件。

#### 功能特性

- 专门用于显示总金额
- 支持多货币总金额显示
- 可配置为统计数字样式
- 支持汇率信息和警告信息显示

#### 使用示例

```tsx
import MultiCurrencyTotalDisplay from '@shared/components/MultiCurrencyTotalDisplay';

// 基本使用
<MultiCurrencyTotalDisplay
  totalAmount={6850.00}
  currencyCode="CNY"
/>

// 带汇率信息
<MultiCurrencyTotalDisplay
  totalAmount={6850.00}
  currencyCode="CNY"
  exchangeRateInfo={{
    currency_code: 'CNY',
    exchange_rate: 7.2,
    usd_total_price: 951.39,
    rate_type: 'current_month',
    effective_month: '2025-01-01',
    is_valid: true
  }}
  size="large"
  isStatistic={true}
/>
```

#### 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `totalAmount` | `number \| string` | - | 总金额 |
| `currencyCode` | `string` | - | 货币代码 |
| `exchangeRateInfo` | `object` | `undefined` | 汇率信息 |
| `showCurrencyTag` | `boolean` | `true` | 是否显示货币标签 |
| `showExchangeRate` | `boolean` | `true` | 是否显示汇率信息 |
| `showWarning` | `boolean` | `true` | 是否显示警告信息 |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | 组件尺寸 |
| `className` | `string` | - | 自定义CSS类名 |
| `style` | `CSSProperties` | - | 自定义样式 |
| `isStatistic` | `boolean` | `false` | 是否显示为统计数字样式 |

## 汇率信息接口

### ExchangeRateInfo

```typescript
interface ExchangeRateInfo {
  currency_code: string;           // 货币代码
  original_unit_price: number;     // 原币单价
  original_total_price: number;    // 原币总价
  exchange_rate: number;           // 汇率值
  usd_unit_price: number;         // 美元单价
  usd_total_price: number;        // 美元总价
  rate_type: 'current_month' | 'historical';  // 汇率类型
  effective_month: string;         // 生效月份
  is_valid: boolean;              // 汇率是否有效
  warning?: string;               // 警告信息
  error?: string;                 // 错误信息
}
```

## 显示规则

### 1. 价格精度

- **单价**: 显示4位小数
- **总价**: 显示2位小数
- **总金额**: 显示2位小数

### 2. 汇率状态显示

- **当前月汇率**: 绿色标签，显示"当月"
- **历史汇率**: 橙色标签，显示"历史"
- **无效汇率**: 显示警告信息
- **无汇率**: 显示信息图标提示

### 3. 货币标识

- **USD**: 绿色标签，美元符号
- **其他货币**: 蓝色标签，显示货币名称

### 4. 价格显示优先级

1. 有有效汇率信息：显示原币价格 + 汇率信息 + 美元价格
2. 无汇率信息：显示原币价格 + 货币标签 + 提示信息
3. USD货币：直接显示美元价格

## 使用场景

### 1. 采购申请物品明细

```tsx
// 单价列
<MultiCurrencyPriceDisplay
  originalPrice={item.estimated_unit_price}
  currencyCode={item.currency_code}
  exchangeRateInfo={item.exchange_rate_info}
  displayType="unit"
  showExchangeRate={true}
  showWarning={true}
/>

// 总价列
<MultiCurrencyPriceDisplay
  originalPrice={item.estimated_total_price}
  currencyCode={item.currency_code}
  exchangeRateInfo={item.exchange_rate_info}
  displayType="total"
  showExchangeRate={false}
  showWarning={false}
/>
```

### 2. 供应商价格管理

```tsx
// 单价列
<MultiCurrencyPriceDisplay
  originalPrice={price.unit_price}
  currencyCode={price.currency_code}
  exchangeRateInfo={exchangeRateInfo}
  displayType="unit"
  showCurrencyTag={true}
  showExchangeRate={false}
/>

// 运费列
<MultiCurrencyPriceDisplay
  currencyCode={price.currency_code}
  exchangeRateInfo={exchangeRateInfo}
  displayType="unit"
  showCurrencyTag={false}
  showExchangeRate={false}
/>
```

### 3. 报表和统计

```tsx
// 总金额统计
<MultiCurrencyTotalDisplay
  totalAmount={summary.total_amount}
  currencyCode="USD"
  size="large"
  isStatistic={true}
/>

// 表格中的总金额列
<MultiCurrencyTotalDisplay
  totalAmount={record.total_amount}
  currencyCode="USD"
  size="small"
  showCurrencyTag={false}
/>
```

## 注意事项

1. **汇率数据**: 组件需要后端提供完整的汇率信息才能正确显示
2. **性能考虑**: 在大量数据渲染时，建议使用适当的尺寸和显示选项
3. **国际化**: 组件已支持中英文显示，货币名称会根据语言环境自动切换
4. **错误处理**: 当汇率数据无效时，组件会显示相应的警告信息
5. **样式定制**: 可以通过className和style属性进行样式定制

## 测试

可以使用 `MultiCurrencyTestPage` 组件来测试各种显示状态和配置选项的效果。
