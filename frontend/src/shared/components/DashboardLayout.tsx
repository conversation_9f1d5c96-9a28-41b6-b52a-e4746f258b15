import React, { useState, useMemo } from 'react';
import { Layout, Menu, Avatar, Dropdown, Typography, Space, Button } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ShopOutlined,
  AppstoreOutlined,
  TagsOutlined,
  UserOutlined,
  LogoutOutlined,
  InboxOutlined,
  ShoppingCartOutlined,
  DashboardOutlined,
  TeamOutlined,
  SafetyOutlined,
  SettingOutlined,
  FileImageOutlined,
  CalendarOutlined,
  GlobalOutlined,
  BellOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../apps/admin/contexts/AuthContext';
import { MODULE_PERMISSIONS } from '../config/permissions';
import { useTranslation } from 'react-i18next';
import './DashboardLayout.css';

const { Header, Content } = Layout;
const { Title } = Typography;

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout, hasAnyPermission } = useAuth();
  const { t, i18n } = useTranslation();

  // 根据权限动态生成菜单项
  const menuItems = useMemo(() => {
    const items = [];

    // Items management
    if (hasAnyPermission(MODULE_PERMISSIONS.ITEMS)) {
      items.push({
        key: '/admin/items',
        icon: <AppstoreOutlined />,
        label: t('navigation.items'),
      });
    }

    // Categories management - moved to System
    if (hasAnyPermission(MODULE_PERMISSIONS.CATEGORIES)) {
      // Categories will be moved to System submenu
    }

    // Suppliers management - moved to System
    if (hasAnyPermission(MODULE_PERMISSIONS.SUPPLIERS)) {
      // Suppliers will be moved to System submenu
    }

    // Inventory management
    if (hasAnyPermission(MODULE_PERMISSIONS.INVENTORY)) {
      items.push({
        key: '/admin/inventory',
        icon: <InboxOutlined />,
        label: t('navigation.inventory'),
      });
    }

    // 采购管理模块
    if (hasAnyPermission(MODULE_PERMISSIONS.PURCHASE_REQUESTS)) {
      items.push({
        key: '/admin/purchase-requests',
        icon: <ShoppingCartOutlined />,
        label: t('navigation.purchase'),
      });
    }

    // 报表仪表板
    // if (hasAnyPermission(MODULE_PERMISSIONS.REPORTS)) {
    //   items.push({
    //     key: '/admin/reports',
    //     icon: <DashboardOutlined />,
    //     label: t('navigation.reports'),
    //   });
    // }



    // System management submenu
    const systemChildren = [];
    
    // Categories and Suppliers group (Configuration)
    if (hasAnyPermission(MODULE_PERMISSIONS.CATEGORIES)) {
      systemChildren.push({
        key: '/admin/primary-categories',
        icon: <TagsOutlined />,
        label: t('navigation.primaryCategories'),
      });
      systemChildren.push({
        key: '/admin/categories',
        icon: <TagsOutlined />,
        label: t('navigation.secondaryCategories'),
      });
    }

    if (hasAnyPermission(MODULE_PERMISSIONS.SUPPLIERS)) {
      systemChildren.push({
        key: '/admin/suppliers',
        icon: <ShopOutlined />,
        label: t('navigation.suppliers'),
      });
    }

    // Add divider if both categories and suppliers exist
    if (hasAnyPermission(MODULE_PERMISSIONS.CATEGORIES) || hasAnyPermission(MODULE_PERMISSIONS.SUPPLIERS)) {
      if (hasAnyPermission(MODULE_PERMISSIONS.USERS) || hasAnyPermission(MODULE_PERMISSIONS.ROLES) || hasAnyPermission(MODULE_PERMISSIONS.DEPARTMENTS)) {
        systemChildren.push({
          type: 'divider',
        });
      }
    }

    // Users management group
    if (hasAnyPermission(MODULE_PERMISSIONS.USERS)) {
      systemChildren.push({
        key: '/admin/users',
        icon: <TeamOutlined />,
        label: t('navigation.users'),
      });
    }

    // 角色权限
    if (hasAnyPermission(MODULE_PERMISSIONS.ROLES)) {
      systemChildren.push({
        key: '/admin/roles',
        icon: <SafetyOutlined />,
        label: t('navigation.roles'),
      });
    }

    // Departments management
    if (hasAnyPermission(MODULE_PERMISSIONS.DEPARTMENTS)) {
      systemChildren.push({
        key: '/admin/departments',
        icon: <TeamOutlined />,
        label: t('navigation.departments'),
      });
    }

    // Image management
    if (hasAnyPermission(MODULE_PERMISSIONS.SYSTEM_MANAGEMENT)) {
      // Add divider if there are other system items
      if (systemChildren.length > 0) {
        systemChildren.push({
          type: 'divider',
        });
      }
      systemChildren.push({
        key: '/admin/image-management',
        icon: <FileImageOutlined />,
        label: t('navigation.imageManagement'),
      });
    }

    // 汇率管理
    if (hasAnyPermission(MODULE_PERMISSIONS.SYSTEM_MANAGEMENT)) {
      systemChildren.push({
        key: '/admin/exchange-rates',
        icon: <GlobalOutlined />,
        label: t('navigation.exchangeRates'),
      });
    }

    // 通知配置（仅超级管理员）
    if (user?.is_superuser) {
      // Add divider if there are other system items
      if (systemChildren.length > 0) {
        systemChildren.push({
          type: 'divider',
        });
      }
      systemChildren.push({
        key: '/admin/notification-config',
        icon: <BellOutlined />,
        label: t('navigation.notificationConfig'),
      });
      systemChildren.push({
        key: '/admin/notification-management',
        icon: <BellOutlined />,
        label: t('navigation.notificationManagement'),
      });
    }

    // If system management permissions exist, add system management menu
    if (systemChildren.length > 0) {
      items.push({
        key: 'system',
        icon: <SettingOutlined />,
        label: t('navigation.system'),
        children: systemChildren,
      });
    }

    return items;
  }, [hasAnyPermission, t]);

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: t('user.profile'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: '/admin/notifications',
      icon: <BellOutlined />,
      label: t('navigation.notifications'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'language',
      icon: <GlobalOutlined />,
      label: i18n.language === 'zh' ? t('common.chinese') : t('common.english'),
      children: [
        {
          key: 'zh',
          label: t('common.chinese'),
          onClick: () => i18n.changeLanguage('zh'),
          icon: i18n.language === 'zh' ? '✓' : null,
        },
        {
          key: 'en',
          label: t('common.english'),
          onClick: () => i18n.changeLanguage('en'),
          icon: i18n.language === 'en' ? '✓' : null,
        },
      ],
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: t('user.logout'),
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    if (key === 'logout') {
      logout();
    } else {
      navigate(key);
    }
  };

  const handleUserMenuClick = ({ key }: { key: string }) => {
    if (key === 'logout') {
      logout();
    } else if (key.startsWith('/admin/')) {
      // 处理导航链接
      navigate(key);
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 顶部导航栏 */}
      <Header 
        className="bizlink-header"
        style={{ 
          padding: '0 24px', 
          display: 'flex', 
          alignItems: 'center',
          justifyContent: 'space-between',
          background: '#1476b8',
          boxShadow: '0 2px 8px rgba(20, 118, 184, 0.3)'
        }}
      >
        {/* 左侧 Logo 和品牌名称 */}
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ShopOutlined style={{ fontSize: '24px', color: '#fff', marginRight: '12px' }} />
          <Title level={4} style={{ 
            margin: 0, 
            color: '#fff',
            textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'
          }} className="brand-logo">
            {t('system.companyName')}
          </Title>
        </div>

        {/* 中间导航菜单 */}
        <div style={{ flex: 1, display: 'flex', justifyContent: 'center' }}>
          <Menu
            theme="dark"
            mode="horizontal"
            selectedKeys={[location.pathname]}
            items={menuItems}
            onClick={handleMenuClick}
            style={{ 
              borderBottom: 'none',
              background: 'transparent',
              minWidth: '600px'
            }}
            className="bizlink-menu"
          />
        </div>

        {/* 右侧功能区域 */}
        <Space size="large" align="center">
          {/* 购物车入口 */}
          {hasAnyPermission(MODULE_PERMISSIONS.SHOPPING_CART) && (
            <Button
              type="text"
              icon={<ShoppingCartOutlined />}
              style={{ 
                color: '#fff', 
                fontSize: '18px',
                height: '32px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'all 0.3s ease'
              }}
              onClick={() => navigate('/admin/purchase-cart')}
              title={t('navigation.purchaseCart')}
              className="cart-button"
            />
          )}
          
          {/* 用户信息 */}
          <Dropdown
            menu={{
              items: userMenuItems as any,
              onClick: handleUserMenuClick,
            }}
            placement="bottomRight"
          >
            <Space style={{ cursor: 'pointer', color: '#fff' }} align="center">
              <Avatar icon={<UserOutlined />} style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)', border: '1px solid rgba(255, 255, 255, 0.3)' }} />
              <span>
                {user?.full_name || user?.username}
                {user?.department && <span style={{ color: 'rgba(255,255,255,0.8)' }}> - {user.department}</span>}
                {user?.role_name && <span style={{ color: 'rgba(255,255,255,0.8)' }}> ({user.role_name})</span>}
              </span>
            </Space>
          </Dropdown>
        </Space>
      </Header>

      {/* 主内容区域 */}
      <Content style={{ 
        margin: '8px', 
        padding: '8px',
        background: '#fff',
        borderRadius: '8px',
        minHeight: 'calc(100vh - 112px)', // 减去header高度(64px)和margin(48px)
        overflow: 'hidden', // 防止整体滚动条
        boxShadow: '0 2px 12px rgba(30, 58, 138, 0.1)'
      }}>
        {children}
      </Content>
    </Layout>
  );
};

export default DashboardLayout; 