/* BizLink Dashboard Layout Styles */

/* Header styling - override Ant Design defaults */
.bizlink-header {
  background: #1476b8 !important;
  box-shadow: 0 2px 8px rgba(20, 118, 184, 0.3) !important;
}

.bizlink-header.ant-layout-header {
  background: #1476b8 !important;
  box-shadow: 0 2px 8px rgba(20, 118, 184, 0.3) !important;
}

/* Force override for all possible header selectors */
.ant-layout-header.bizlink-header {
  background: #1476b8 !important;
  box-shadow: 0 2px 8px rgba(20, 118, 184, 0.3) !important;
}

/* Ensure the solid color is visible */
.bizlink-header,
.bizlink-header.ant-layout-header,
.ant-layout-header.bizlink-header {
  background-color: #1476b8 !important;
}

/* Menu styling */
.bizlink-menu .ant-menu-item {
  color: rgba(255, 255, 255, 0.85) !important;
  transition: all 0.3s ease;
}

.bizlink-menu .ant-menu-item:hover {
  color: #fff !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.bizlink-menu .ant-menu-item-selected {
  color: #fff !important;
  background-color: rgba(255, 255, 255, 0.15) !important;
  border-bottom: 2px solid #60a5fa !important;
}

.bizlink-menu .ant-menu-submenu-title {
  color: rgba(255, 255, 255, 0.85) !important;
  transition: all 0.3s ease;
}

.bizlink-menu .ant-menu-submenu-title:hover {
  color: #fff !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.bizlink-menu .ant-menu-submenu-open .ant-menu-submenu-title {
  color: #fff !important;
  background-color: rgba(255, 255, 255, 0.15) !important;
}

/* Submenu dropdown styling - more specific selectors */
.ant-menu-submenu-popup {
  background: #1476b8 !important;
}

.ant-menu-submenu-popup .ant-menu {
  background: #1476b8 !important;
}

.ant-menu-submenu-popup .ant-menu-item {
  background: #1476b8 !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

.ant-menu-submenu-popup .ant-menu-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #fff !important;
}

.ant-menu-submenu-popup .ant-menu-item-selected {
  background: rgba(255, 255, 255, 0.15) !important;
  color: #fff !important;
}

/* Additional submenu overrides */
.ant-menu-submenu-popup .ant-menu-submenu {
  background: #1476b8 !important;
}

.ant-menu-submenu-popup .ant-menu-submenu-title {
  background: #1476b8 !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

.ant-menu-submenu-popup .ant-menu-submenu-title:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #fff !important;
}

/* Force override for all submenu elements */
.ant-menu-submenu-popup * {
  background-color: #1476b8 !important;
}

/* Cart button styling */
.cart-button:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  transform: scale(1.05);
}

/* Brand logo styling */
.brand-logo {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
