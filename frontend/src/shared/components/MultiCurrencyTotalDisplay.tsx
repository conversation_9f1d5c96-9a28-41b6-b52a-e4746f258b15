import React from 'react';
import { Tag, Tooltip } from 'antd';
import { InfoCircleOutlined, WarningOutlined } from '@ant-design/icons';
import { getCurrencyInfo, SUPPORTED_CURRENCIES } from '@shared/utils/exchangeRateUtils';

export interface MultiCurrencyTotalDisplayProps {
  // 总金额信息
  totalAmount: number | string;
  currencyCode: string;
  
  // 汇率信息（可选）
  exchangeRateInfo?: {
    currency_code: string;
    exchange_rate: number;
    usd_total_price: number;
    rate_type: 'current_month' | 'historical';
    effective_month: string;
    is_valid: boolean;
    warning?: string;
  };
  
  // 显示配置
  showCurrencyTag?: boolean;
  showExchangeRate?: boolean;
  showWarning?: boolean;
  size?: 'small' | 'medium' | 'large';
  
  // 自定义样式
  className?: string;
  style?: React.CSSProperties;
  
  // 是否显示为统计数字样式
  isStatistic?: boolean;
}

/**
 * 多货币总金额显示组件
 * 用于显示采购申请等场景的总金额
 */
const MultiCurrencyTotalDisplay: React.FC<MultiCurrencyTotalDisplayProps> = ({
  totalAmount,
  currencyCode,
  exchangeRateInfo,
  showCurrencyTag = true,
  showExchangeRate = true,
  showWarning = true,
  size = 'medium',
  className,
  style,
  isStatistic = false
}) => {
  // 获取货币信息
  const currencyInfo = getCurrencyInfo(currencyCode as keyof typeof SUPPORTED_CURRENCIES);
  const isUSD = currencyCode === 'USD';
  
  // 处理金额数值
  const numAmount = typeof totalAmount === 'string' ? parseFloat(totalAmount) : totalAmount;
  const isValidAmount = !isNaN(numAmount) && numAmount > 0;
  
  // 如果没有有效金额，显示占位符
  if (!isValidAmount) {
    return <span className={className} style={style}>-</span>;
  }
  
  // 根据大小确定字体大小
  const getFontSize = () => {
    switch (size) {
      case 'small': return { main: '12px', sub: '10px', rate: '9px' };
      case 'large': return { main: '16px', sub: '14px', rate: '12px' };
      default: return { main: '14px', sub: '12px', rate: '11px' };
    }
  };
  
  const fontSize = getFontSize();
  
  // 如果有汇率信息且不是USD，显示多货币总金额
  if (exchangeRateInfo && !isUSD && exchangeRateInfo.is_valid) {
    const { usd_total_price, exchange_rate, rate_type, effective_month, warning } = exchangeRateInfo;
    
    return (
      <div className={className} style={style}>
        {/* 原币总金额 */}
        <div style={{ 
          fontSize: fontSize.sub, 
          color: '#666', 
          marginBottom: '2px',
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }}>
          <span>{currencyInfo.symbol} {numAmount.toFixed(2)}</span>
          {showCurrencyTag && (
            <Tag color="blue">
              {currencyInfo.name}
            </Tag>
          )}
        </div>
        
        {/* 汇率信息 */}
        {showExchangeRate && (
          <div style={{ 
            fontSize: fontSize.rate, 
            color: '#999', 
            marginBottom: '2px',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}>
            <span>汇率: {exchange_rate.toFixed(6)}</span>
            <Tag color={rate_type === 'current_month' ? 'green' : 'orange'}>
              {rate_type === 'current_month' ? '当月' : '历史'} {effective_month}
            </Tag>
          </div>
        )}
        
        {/* 美元总金额 */}
        <div style={{ 
          fontSize: fontSize.main, 
          fontWeight: 'bold', 
          color: '#1677ff',
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }}>
          <span>${usd_total_price.toFixed(2)}</span>
          <Tag color="green">USD</Tag>
        </div>
        
        {/* 警告信息 */}
        {showWarning && warning && (
          <div style={{ 
            fontSize: fontSize.rate, 
            color: '#faad14', 
            marginTop: '2px',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}>
            <WarningOutlined />
            <span>{warning}</span>
          </div>
        )}
      </div>
    );
  }
  
  // 如果没有汇率信息或汇率无效，显示原币总金额
  if (!exchangeRateInfo || !exchangeRateInfo.is_valid) {
    return (
      <div className={className} style={style}>
        <div style={{ 
          fontSize: fontSize.main,
          color: isUSD ? '#52c41a' : '#666',
          fontWeight: isStatistic ? 'bold' : 'normal'
        }}>
          {isUSD ? '$' : currencyInfo.symbol} {numAmount.toFixed(2)}
        </div>
        {showCurrencyTag && !isUSD && (
          <div style={{ marginTop: '2px' }}>
            <Tag color="orange">
              {currencyInfo.name}
            </Tag>
            {!exchangeRateInfo && (
              <Tooltip title="暂无汇率数据，无法转换为美元">
                <InfoCircleOutlined style={{ marginLeft: '4px', color: '#faad14' }} />
              </Tooltip>
            )}
          </div>
        )}
      </div>
    );
  }
  
  // 如果是USD，直接显示
  if (isUSD) {
    return (
      <div className={className} style={style}>
        <div style={{ 
          fontSize: fontSize.main,
          color: '#52c41a',
          fontWeight: 'bold'
        }}>
          ${numAmount.toFixed(2)}
        </div>
        {showCurrencyTag && (
          <Tag color="green">USD</Tag>
        )}
      </div>
    );
  }
  
  // 默认情况
  return (
    <div className={className} style={style}>
      <div style={{ 
        fontSize: fontSize.main,
        color: '#666'
      }}>
        {currencyInfo.symbol} {numAmount.toFixed(2)}
      </div>
      {showCurrencyTag && (
        <Tag color="blue">
          {currencyInfo.name}
        </Tag>
      )}
    </div>
  );
};

export default MultiCurrencyTotalDisplay;
