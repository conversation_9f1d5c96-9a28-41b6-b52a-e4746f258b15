import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Button, Space, Typography, Divider } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

const I18nDemo: React.FC = () => {
  const { t, i18n } = useTranslation();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <Card title={t('system.systemName')} style={{ margin: 16 }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 语言切换器 */}
        <div style={{ textAlign: 'right' }}>
          <Space>
            <GlobalOutlined />
            <Button 
              size="small" 
              onClick={() => changeLanguage('zh')}
              type={i18n.language === 'zh' ? 'primary' : 'default'}
            >
              中文
            </Button>
            <Button 
              size="small" 
              onClick={() => changeLanguage('en')}
              type={i18n.language === 'en' ? 'primary' : 'default'}
            >
              English
            </Button>
          </Space>
        </div>

        <Divider />

        {/* 基础功能演示 */}
        <div>
          <Title level={4}>{t('common.operation')}</Title>
          <Space>
            <Button type="primary">{t('common.add')}</Button>
            <Button>{t('common.edit')}</Button>
            <Button danger>{t('common.delete')}</Button>
            <Button>{t('common.view')}</Button>
          </Space>
        </div>

        {/* 导航菜单演示 */}
        <div>
          <Title level={4}>{t('navigation.dashboard')}</Title>
          <Space wrap>
            <Button>{t('navigation.inventory')}</Button>
            <Button>{t('navigation.purchase')}</Button>
            <Button>{t('navigation.suppliers')}</Button>
            <Button>{t('navigation.users')}</Button>
          </Space>
        </div>

        {/* 库存管理演示 */}
        <div>
          <Title level={4}>{t('inventory.itemName')}</Title>
          <Space direction="vertical">
            <Text>{t('inventory.specification')}: {t('inventory.unit')}</Text>
            <Text>{t('inventory.stockQuantity')}: 100 {t('units.piece')}</Text>
            <Text>{t('inventory.supplier')}: ABC Company</Text>
            <Text>{t('inventory.inventoryStatus')}: {t('inventory.normal')}</Text>
          </Space>
        </div>

        {/* 采购管理演示 */}
        <div>
          <Title level={4}>{t('purchase.request')}</Title>
          <Space direction="vertical">
            <Text>{t('purchase.requestor')}: John Doe</Text>
            <Text>{t('purchase.department')}: Production</Text>
            <Text>{t('purchase.totalAmount')}: $1,000</Text>
            <Text>{t('purchase.status')}: {t('purchase.pending')}</Text>
          </Space>
        </div>

        {/* 系统消息演示 */}
        <div>
          <Title level={4}>{t('messages.operationSuccess')}</Title>
          <Space direction="vertical">
            <Text type="success">{t('messages.saveSuccess')}</Text>
            <Text type="warning">{t('messages.confirmDelete')}</Text>
            <Text type="danger">{t('messages.networkError')}</Text>
          </Space>
        </div>
      </Space>
    </Card>
  );
};

export default I18nDemo;
