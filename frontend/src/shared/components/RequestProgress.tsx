import React from 'react';
import { Card, Steps, Tag, Timeline, Typography, Space, Divider } from 'antd';
import { CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';

const { Title, Text, Paragraph } = Typography;

export interface ProgressInfo {
  step: number;
  total: number;
  percentage: number;
  description: string;
}

export interface FlowHistoryItem {
  id: number;
  action: string;
  action_display: string;
  from_status: string;
  from_status_display: string;
  to_status: string;
  to_status_display: string;
  operator_name: string;
  approval_level_display: string;
  comments: string;
  created_at: string;
}

export interface RequestProgressProps {
  requestNo: string;
  currentStatus: string;
  statusDisplay: string;
  progress: ProgressInfo;
  nextStep: string;
  allowedActions: string[];
  flowDescription: string;
  flowHistory: FlowHistoryItem[];
}

const RequestProgress: React.FC<RequestProgressProps> = ({
  requestNo,
  currentStatus,
  statusDisplay,
  progress,
  nextStep,
  allowedActions,
  flowDescription,
  flowHistory
}) => {
  const { t } = useTranslation();
  // 状态颜色映射
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'draft': 'default',
      'pending_submission': 'processing',
      'under_review': 'processing',
      'under_principle_approval': 'processing',
      'under_final_approval': 'processing',
      'approved': 'success',
      'rejected': 'error',
      'withdrawn': 'warning'
    };
    return colorMap[status] || 'default';
  };

  // 操作类型图标映射
  const getActionIcon = (action: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      'create': <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      'submit': <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      'resubmit': <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      'review': <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      'principle_approve': <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      'final_approve': <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      'reject': <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
      'return': <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
      'withdraw': <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
      'edit': <ClockCircleOutlined style={{ color: '#1890ff' }} />,
      'delete': <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
    };
    return iconMap[action] || <ClockCircleOutlined style={{ color: '#1890ff' }} />;
  };

  // 步骤配置
  const steps = [
    { title: t('purchase.submitApplication'), description: t('purchase.departmentItemManagerSubmit') },
    { title: t('purchase.departmentManagerReview'), description: t('purchase.departmentManagerPreliminaryReview') },
    { title: t('purchase.supervisorApproval'), description: t('purchase.itemManagerSupervisorApproval') },
    { title: t('purchase.finalApproval'), description: t('purchase.companySupervisorFinalApproval') }
  ];

  return (
    <div style={{ padding: '16px' }}>
      {/* 申请基本信息 */}
      <Card title={t('purchase.applicationProgress')} style={{ marginBottom: '16px' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Text strong>{t('purchase.requestNumber')}：</Text>
            <Text code>{requestNo}</Text>
          </div>
          <div>
            <Text strong>{t('purchase.currentStatus')}：</Text>
            <Tag color={getStatusColor(currentStatus)}>{statusDisplay}</Tag>
          </div>
          <div>
            <Text strong>{t('purchase.processDescription')}：</Text>
            <Text type="secondary">{flowDescription}</Text>
          </div>
          <div>
            <Text strong>{t('purchase.nextStep')}：</Text>
            <Text type="secondary">{nextStep}</Text>
          </div>
        </Space>
      </Card>

      {/* 审批进度 */}
      <Card title={t('purchase.approvalProgress')} style={{ marginBottom: '16px' }}>
        <Steps
          current={progress.step}
          percent={progress.percentage}
          direction="horizontal"
          size="small"
        >
          {steps.map((step, index) => (
            <Steps.Step
              key={index}
              title={step.title}
              description={step.description}
              status={
                index < progress.step ? 'finish' :
                index === progress.step ? 'process' : 'wait'
              }
            />
          ))}
        </Steps>
        <div style={{ marginTop: '16px', textAlign: 'center' }}>
          <Text type="secondary">
            {t('purchase.currentProgress')}：{progress.step}/{progress.total} {t('purchase.steps')} ({progress.percentage}%)
          </Text>
        </div>
      </Card>

      {/* 流转历史 */}
      <Card title={t('purchase.flowHistory')} style={{ marginBottom: '16px' }}>
        <Timeline>
          {flowHistory.map((item, index) => (
            <Timeline.Item
              key={item.id}
              dot={getActionIcon(item.action)}
              color={
                item.action.includes('reject') ? 'red' :
                item.action.includes('approve') ? 'green' :
                item.action.includes('withdraw') || item.action.includes('return') ? 'orange' : 'blue'
              }
            >
              <div style={{ marginBottom: '8px' }}>
                <Space>
                  <Text strong>{item.action_display}</Text>
                  {item.approval_level_display && (
                    <Tag color="blue">{item.approval_level_display}</Tag>
                  )}
                  <Text type="secondary">{t('purchase.operator')}：{item.operator_name}</Text>
                </Space>
              </div>
              
              {item.from_status && item.to_status && (
                <div style={{ marginBottom: '8px' }}>
                  <Text type="secondary">
                    {t('purchase.statusChange')}：{item.from_status_display || item.from_status} → {item.to_status_display}
                  </Text>
                </div>
              )}
              
              {item.comments && (
                <div style={{ marginBottom: '8px' }}>
                  <Text type="secondary">{t('purchase.approvalComments')}：{item.comments}</Text>
                </div>
              )}
              
              <div>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {dayjs(item.created_at).format(DATE_FORMATS.DATE_TIME)}
                </Text>
              </div>
            </Timeline.Item>
          ))}
        </Timeline>
        
        {flowHistory.length === 0 && (
          <div style={{ textAlign: 'center', padding: '32px', color: '#999' }}>
            {t('purchase.noFlowHistory')}
          </div>
        )}
      </Card>

      {/* 允许的操作 */}
      {allowedActions.length > 0 && (
        <Card title={t('purchase.allowedActions')}>
          <Space wrap>
            {allowedActions.map((action, index) => (
              <Tag key={index} color="blue">
                {action}
              </Tag>
            ))}
          </Space>
        </Card>
      )}
    </div>
  );
};

export default RequestProgress;
