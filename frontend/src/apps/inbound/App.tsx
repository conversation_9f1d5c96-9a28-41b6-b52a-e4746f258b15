import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider, useAuth } from './contexts/AuthContext';

// 懒加载页面组件
const LoginPage = React.lazy(() => import('./pages/LoginPage'));
const InboundPage = React.lazy(() => import('./pages/InboundPage'));

// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading, user, token } = useAuth();
  
  console.log('[InboundProtectedRoute] 路由守卫检查:', {
    loading,
    isAuthenticated,
    hasUser: !!user,
    hasToken: !!token,
    username: user?.username,
    role: user?.role
  });
  
  // 如果还在加载认证状态，显示加载界面
  if (loading) {
    console.log('[InboundProtectedRoute] 认证状态加载中，显示加载界面');
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        fontSize: '16px',
        color: '#666'
      }}>
        正在验证登录状态...
      </div>
    );
  }
  
  if (!isAuthenticated) {
    console.log('[InboundProtectedRoute] 用户未认证，重定向到登录页');
    return <Navigate to="/inbound/login" replace />;
  }
  
  // 检查用户角色
  if (user?.role_code !== 'warehouse_keeper') {
    console.log('[InboundProtectedRoute] 用户角色不符合要求，重定向到登录页');
    return <Navigate to="/inbound/login" replace />;
  }
  
  console.log('[InboundProtectedRoute] 用户已认证且角色正确，允许访问');
  return <>{children}</>;
};

// 主应用组件
const InboundAppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<div>加载中...</div>}>
      <Routes>
        <Route path="login" element={<LoginPage />} />
        {/* 修复：空路径应该重定向到 inbound 页面 */}
        <Route path="" element={<Navigate to="inbound" replace />} />
        <Route path="inbound" element={
          <ProtectedRoute>
            <InboundPage />
          </ProtectedRoute>
        } />
        {/* TODO: 后续添加入库相关页面 */}
        {/* <Route path="receipts" element={<InboundReceipts />} /> */}
        {/* <Route path="receipts/:id" element={<InboundReceiptDetail />} /> */}
        {/* <Route path="putaway" element={<InboundPutaway />} /> */}
        {/* <Route path="inventory" element={<InboundInventory />} /> */}
        {/* 修复：通配符路径重定向到 inbound 页面 */}
        <Route path="*" element={<Navigate to="inbound" replace />} />
      </Routes>
    </Suspense>
  );
};

// 根应用组件
export default function InboundApp() {
  return (
    <ConfigProvider locale={zhCN}>
      <AuthProvider>
        <InboundAppRoutes />
      </AuthProvider>
    </ConfigProvider>
  );
}
