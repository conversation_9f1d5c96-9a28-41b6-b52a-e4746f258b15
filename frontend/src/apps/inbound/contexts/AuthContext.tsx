import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI } from '../services/api';

interface User {
  user_id: number;
  username: string;
  full_name: string;
  department_id: number;
  department_name: string;
  role: string;
  role_code: string;  // 添加role_code字段
  permissions: Array<{
    code: string;
    name: string;
    module: string;
  }>;
}

interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  hasPermission: (permissionCode: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // 检查本地存储的认证信息
  useEffect(() => {
    const initAuth = async () => {
      console.log('[InboundAuthContext] 初始化认证状态');
      const storedToken = localStorage.getItem('inbound_token');
      const storedUser = localStorage.getItem('inbound_user');
      
      if (storedToken && storedUser) {
        try {
          const userData = JSON.parse(storedUser);
          console.log('[InboundAuthContext] 发现本地存储的认证信息', { token: !!storedToken, user: userData.username });
          
          setToken(storedToken);
          setUser(userData);
          setIsAuthenticated(true);
          
          // 验证token是否有效
          try {
            await authAPI.verify();
            console.log('[InboundAuthContext] Token验证成功，用户认证状态有效');
          } catch (error) {
            console.warn('[InboundAuthContext] Token验证失败，清除认证状态', error);
            // 401错误时清除认证状态，要求用户重新登录
            handleLogout('token_verification_failed');
          }
        } catch (error) {
          console.error('[InboundAuthContext] 解析存储的用户信息失败:', error);
          // 清除损坏的数据
          handleLogout('data_corrupted');
        }
      } else {
        console.log('[InboundAuthContext] 未发现本地认证信息');
      }
      
      setLoading(false);
      console.log('[InboundAuthContext] 认证状态初始化完成');
    };

    initAuth();
  }, []);

  // 监听全局登出事件
  useEffect(() => {
    const handleGlobalLogout = (event: CustomEvent) => {
      console.log('[InboundAuthContext] 收到全局登出事件:', event.detail);
      handleLogout(event.detail.reason);
    };

    window.addEventListener('auth:logout', handleGlobalLogout as EventListener);
    
    return () => {
      window.removeEventListener('auth:logout', handleGlobalLogout as EventListener);
    };
  }, []);

  // 登录处理
  const login = async (username: string, password: string) => {
    try {
      const response = await authAPI.login(username, password);
      const { access_token, user: userData } = response as unknown as LoginResponse;
      
      console.log('[InboundAuthContext] 登录成功', { username: userData.username, role: userData.role_code });
      
      // 检查用户角色是否为warehouse_keeper
      if (userData.role_code !== 'warehouse_keeper') {
        throw new Error('只有仓库管理员可以访问入库管理系统');
      }
      
      // 保存认证信息到本地存储
      localStorage.setItem('inbound_token', access_token);
      localStorage.setItem('inbound_user', JSON.stringify(userData));
      
      // 更新状态
      setToken(access_token);
      setUser(userData);
      setIsAuthenticated(true);
      
      console.log('[InboundAuthContext] 用户认证状态已更新');
      
    } catch (error: any) {
      console.error('[InboundAuthContext] 登录失败:', error);
      throw error;
    }
  };

  // 登出处理
  const handleLogout = (reason: string = 'user_logout') => {
    console.log('[InboundAuthContext] 执行登出操作:', reason);
    
    // 清除本地存储
    localStorage.removeItem('inbound_token');
    localStorage.removeItem('inbound_user');
    
    // 清除状态
    setToken(null);
    setUser(null);
    setIsAuthenticated(false);
    
    console.log('[InboundAuthContext] 认证状态已清除');
  };

  // 权限检查
  const hasPermission = (permissionCode: string): boolean => {
    if (!user || !user.permissions) {
      return false;
    }
    
    return user.permissions.some(permission => permission.code === permissionCode);
  };

  const contextValue: AuthContextType = {
    user,
    token,
    isAuthenticated,
    loading,
    login,
    logout: handleLogout,
    hasPermission,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
