import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  InputNumber, 
  Button, 
  Space, 
  Typography, 
  message, 
  Row, 
  Col,
  Divider,
  Tag,
  Input
} from 'antd';
import { ArrowLeftOutlined, CheckOutlined, DeleteOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { InboundRequest, InboundItemRequest } from '../../../shared/types/inbound';

const { Title, Text } = Typography;

interface InboundItemListProps {
  scanResult: any;
  onInbound: (request: InboundRequest) => void;
  onBack: () => void;
  loading: boolean;
}

interface ItemRow {
  key: string;
  id: number;
  item_id: number;
  item_code: string;
  item_name: string;
  spq_quantity: number;
  spq_count: number;
  spq_unit: string;
  requested_quantity: number;
  inbound_quantity: number;
  notes?: string;
}

const InboundItemList: React.FC<InboundItemListProps> = ({
  scanResult,
  onInbound,
  onBack,
  loading
}) => {
  const { t } = useTranslation('inbound');
  const [items, setItems] = useState<ItemRow[]>([]);
  const [notes, setNotes] = useState<string>('');
  
  // 初始化物品列表
  useEffect(() => {
    if (scanResult?.items) {
      const initialItems = scanResult.items.map((item: any, index: number) => ({
        key: `item-${index}`,
        id: item.id,
        item_id: item.item_id,
        item_code: item.item_code,
        item_name: item.item_name,
        spq_quantity: parseFloat(item.spq_quantity),
        spq_count: item.spq_count,
        spq_unit: item.spq_unit,
        requested_quantity: parseFloat(item.requested_quantity),
        inbound_quantity: Math.round(parseFloat(item.requested_quantity)), // 默认等于申请数量，转为整数
        notes: item.notes
      }));
      setItems(initialItems);
    }
  }, [scanResult]);
  
  // 更新入库数量
  const handleQuantityChange = (key: string, value: number | null) => {
    if (value === null || value < 1) return;
    
    setItems(prev => prev.map(item => 
      item.key === key 
        ? { ...item, inbound_quantity: Math.round(value) }
        : item
    ));
  };
  
  // 移除物品
  const handleRemoveItem = (key: string) => {
    setItems(prev => prev.filter(item => item.key !== key));
  };
  
  // 执行入库
  const handleSubmit = () => {
    if (items.length === 0) {
      message.error(t('请至少选择一个物品进行入库'));
      return;
    }
    
    // 验证数量
    const invalidItems = items.filter(item => item.inbound_quantity < 1);
    if (invalidItems.length > 0) {
      message.error(t('请检查入库数量，所有数量必须大于等于1'));
      return;
    }
    
    // 构建入库请求
    const inboundItems: InboundItemRequest[] = items.map(item => ({
      item_id: item.item_id,
      inbound_quantity: item.inbound_quantity,
      notes: item.notes
    }));
    
    const request: InboundRequest = {
      purchase_request_id: scanResult.purchase_request.id,
      department_id: scanResult.purchase_request.department_id,
      items: inboundItems,
      notes: notes.trim() || undefined
    };
    
    onInbound(request);
  };
  
  // 表格列定义
  const columns = [
    {
      title: t('物品信息'),
      key: 'item',
      render: (record: ItemRow) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>
            {record.item_name}
          </div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {record.item_code}
          </div>
        </div>
      )
    },
    {
      title: t('申请数量'),
      key: 'requested',
      render: (record: ItemRow) => (
        <div>
          <div>{record.requested_quantity + ' x ' + record.spq_quantity + record.spq_unit}</div>
        </div>
      )
    },
    {
      title: t('入库数量'),
      key: 'inbound',
      render: (record: ItemRow) => (
        <div>
          <InputNumber
            min={1}
            step={1}
            precision={0}
            value={record.inbound_quantity}
            onChange={(value) => handleQuantityChange(record.key, value)}
            style={{ width: '150px' }}
            addonAfter={' x ' + record.spq_quantity + ' ' + record.spq_unit}
          />
        </div>
      )
    },
    {
      title: t('操作'),
      key: 'action',
      render: (record: ItemRow) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveItem(record.key)}
        >
          {t('移除')}
        </Button>
      )
    }
  ];
  
  // 计算统计信息
  const totalItems = items.length;
  const totalQuantity = items.reduce((sum, item) => sum + item.inbound_quantity, 0);
  
  return (
    <div>
      {/* 头部信息 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button icon={<ArrowLeftOutlined />} onClick={onBack}>
                {t('返回扫码')}
              </Button>
              <Title level={4} style={{ margin: 0 }}>
                {t('入库物品确认')}
              </Title>
            </Space>
          </Col>
          <Col>
            <Space>
              <Text>{t('申请单号')}: {scanResult?.purchase_request?.request_no}</Text>
              <Text>{t('申请部门')}: {scanResult?.purchase_request?.department_name || '未知'}</Text>
            </Space>
          </Col>
        </Row>
      </Card>
      
      {/* 物品列表 */}
      <Card title={t('物品列表')} style={{ marginBottom: '16px' }}>
        <Table
          columns={columns}
          dataSource={items}
          pagination={false}
          size="middle"
          rowKey="key"
        />
        
        <Divider />
        
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Text>{t('物品总数')}: {totalItems}</Text>
              <Text>{t('入库总数量')}: {Math.round(totalQuantity)}</Text>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button onClick={onBack}>
                {t('取消')}
              </Button>
              <Button 
                type="primary" 
                icon={<CheckOutlined />}
                onClick={handleSubmit}
                loading={loading}
                disabled={items.length === 0}
              >
                {t('确认入库')}
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>
      
      {/* 备注信息 */}
      <Card title={t('备注信息')}>
        <Input.TextArea
          rows={3}
          placeholder={t('请输入入库备注信息（可选）')}
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
        />
      </Card>
    </div>
  );
};

export default InboundItemList;
