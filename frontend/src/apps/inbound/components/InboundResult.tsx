import React from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Typography, 
  Row, 
  Col, 
  Divider,
  Tag,
  Result
} from 'antd';
import { CheckCircleOutlined, ReloadOutlined, PrinterOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { useSearchParams } from 'react-router-dom';
import { InboundResponse } from '../../../shared/types/inbound';

const { Title, Text } = Typography;

interface InboundResultProps {
  qrCode: string;
  onReset: () => void;
  inboundResult?: InboundResponse;  // 添加真实的入库结果数据
}

const InboundResult: React.FC<InboundResultProps> = ({ qrCode, onReset, inboundResult }) => {
  const { t } = useTranslation('inbound');
  const [searchParams] = useSearchParams();
  const inboundId = searchParams.get('inboundId');
  
  // 使用真实的入库结果数据，如果没有则显示默认值
  const result = inboundResult || {
    success: true,
    message: t('入库操作完成'),
    inbound_id: inboundId || 'INB202412011234567890ABCDEF',
    total_items: 0,
    success_items: 0,
    failed_items: 0,
    created_at: dayjs().format(DATE_FORMATS.DATE_TIME),
    results: []
  };
  
  // 打印结果
  const handlePrint = () => {
    window.print();
  };
  
  return (
    <div>
      {/* 成功结果 */}
      <Result
        status={result.success && result.success_items > 0 ? "success" : "warning"}
        icon={<CheckCircleOutlined />}
        title={result.success && result.success_items > 0 ? t('入库操作完成') : t('入库操作完成')}
        subTitle={
          result.success && result.success_items > 0 
            ? t('所有物品已成功入库到系统')
            : result.failed_items === result.total_items
              ? t('入库失败，所有物品都无法入库')
              : t('入库部分成功')
        }
        extra={[
          <Button key="print" icon={<PrinterOutlined />} onClick={handlePrint}>
            {t('打印结果')}
          </Button>,
          <Button key="reset" type="primary" icon={<ReloadOutlined />} onClick={onReset}>
            {t('继续入库')}
          </Button>
        ]}
      />
      
      {/* 入库详情 */}
      <Card title={t('入库详情')} style={{ marginBottom: '16px' }}>
        <Row gutter={16}>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{ color: '#52c41a', margin: '8px 0' }}>
                {result.total_items}
              </Title>
              <Text type="secondary">{t('物品总数')}</Text>
            </div>
          </Col>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{ color: '#52c41a', margin: '8px 0' }}>
                {result.success_items}
              </Title>
              <Text type="secondary">{t('成功入库')}</Text>
            </div>
          </Col>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{ color: '#ff4d4f', margin: '8px 0' }}>
                {result.failed_items}
              </Title>
              <Text type="secondary">{t('入库失败')}</Text>
            </div>
          </Col>
        </Row>
        
        <Divider />
        
        <Row gutter={16}>
          <Col span={12}>
            <Text strong>{t('入库单号')}: </Text>
            <Text copyable>{result.inbound_id}</Text>
          </Col>
          <Col span={12}>
            <Text strong>{t('操作时间')}: </Text>
            <Text>{result.created_at}</Text>
          </Col>
        </Row>
        
        <Row gutter={16} style={{ marginTop: '8px' }}>
          <Col span={12}>
            <Text strong>{t('二维码内容')}: </Text>
            <Text copyable>{qrCode}</Text>
          </Col>
          <Col span={12}>
            <Text strong>{t('操作状态')}: </Text>
            <Tag color={result.success && result.success_items > 0 ? "success" : "warning"}>
              {result.success && result.success_items > 0 ? t('完成') : t('部分完成')}
            </Tag>
          </Col>
        </Row>
      </Card>
      
      {/* 物品结果列表 */}
      <Card title={t('物品入库结果')} style={{ marginBottom: '16px' }}>
        <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
          {result.results && result.results.length > 0 ? (
            result.results.map((item, index) => (
              <Card 
                key={`${item.item_id}-${index}`} 
                size="small" 
                style={{ marginBottom: '8px' }}
                bodyStyle={{ padding: '12px' }}
              >
                <Row justify="space-between" align="middle">
                  <Col span={8}>
                    <div>
                      <div style={{ fontWeight: 'bold' }}>
                        {item.item_name || t('未知物品')}
                      </div>
                      <div style={{ color: '#666', fontSize: '12px' }}>
                        {item.item_code || t('无编码')}
                      </div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '16px', fontWeight: 'bold' }}>
                        {item.inbound_quantity}
                      </div>
                      <div style={{ color: '#666', fontSize: '12px' }}>
                        {t('入库数量')}
                      </div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '14px' }}>
                        {item.before_quantity} → {item.after_quantity}
                      </div>
                      <div style={{ color: '#666', fontSize: '12px' }}>
                        {t('库存变化')}
                      </div>
                    </div>
                  </Col>
                  <Col span={4}>
                    <div style={{ textAlign: 'center' }}>
                      {item.success ? (
                        <Tag color="success">{t('成功')}</Tag>
                      ) : (
                        <Tag color="error">{t('失败')}</Tag>
                      )}
                    </div>
                  </Col>
                </Row>
                {!item.success && item.message && (
                  <div style={{ marginTop: '8px', color: '#ff4d4f', fontSize: '12px' }}>
                    {t('失败原因')}: {item.message}
                  </div>
                )}
              </Card>
            ))
          ) : (
            <div style={{ textAlign: 'center', padding: '20px', color: '#666' }}>
              {t('暂无入库结果数据')}
            </div>
          )}
        </div>
      </Card>
      
      {/* 操作按钮 */}
      <Card>
        <div style={{ textAlign: 'center' }}>
          <Space size="large">
            <Button size="large" onClick={onReset}>
              {t('继续入库')}
            </Button>
            <Button size="large" type="primary" icon={<PrinterOutlined />} onClick={handlePrint}>
              {t('打印入库单')}
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default InboundResult;
