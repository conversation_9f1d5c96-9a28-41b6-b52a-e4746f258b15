export default {
  // Common
  '扫码入库': 'Scan & Inbound',
  '入库管理': 'Inbound Management',
  '操作成功': 'Operation Successful',
  '操作失败': 'Operation Failed',
  '确认': 'Confirm',
  '取消': 'Cancel',
  '返回': 'Back',
  '继续': 'Continue',
  '完成': 'Complete',
  '成功': 'Success',
  '失败': 'Failed',
  '错误': 'Error',
  '警告': 'Warning',
  '信息': 'Information',
  
  // QR Code Scanning
  '请输入二维码内容': 'Please enter QR code content',
  '请扫描采购申请单上的二维码开始入库操作': 'Please scan the QR code on the purchase request to start inbound operation',
  '支持手动输入二维码内容或使用扫码枪扫描': 'Support manual input or barcode scanner',
  '扫码': 'Scan',
  '扫描中...': 'Scanning...',
  '扫码成功': 'QR Code scanned successfully',
  '扫码失败': 'QR Code scan failed',
  '扫码失败，请重试': 'Scan failed, please retry',
  
  // Item Management
  '入库物品确认': 'Confirm Inbound Items',
  '物品列表': 'Item List',
  '物品信息': 'Item Information',
  '申请数量': 'Requested Quantity',
  '入库数量': 'Inbound Quantity',
  '操作': 'Action',
  '移除': 'Remove',
  '物品总数': 'Total Items',
  '入库总数量': 'Total Inbound Quantity',
  '请至少选择一个物品进行入库': 'Please select at least one item for inbound',
  '请检查入库数量，所有数量必须大于0': 'Please check inbound quantities, all must be greater than 0',
  '确认入库': 'Confirm Inbound',
  '入库中...': 'Processing...',
  
  // Inbound Operations
  '入库操作': 'Inbound Operation',
  '入库成功': 'Inbound successful',
  '入库失败': 'Inbound failed',
  '入库失败，请重试': 'Inbound failed, please retry',
  '入库操作完成': 'Inbound operation completed',
  '所有物品已成功入库到系统': 'All items have been successfully inbound to the system',
  '入库部分成功': 'Inbound partially successful',
  '入库失败，所有物品都无法入库': 'Inbound failed, all items cannot be inbound',
  '部分完成': 'Partially Complete',
  '失败原因': 'Failure Reason',
  '暂无入库结果数据': 'No inbound result data',
  '未知物品': 'Unknown Item',
  '无编码': 'No Code',
  
  // Result Display
  '入库详情': 'Inbound Details',
  '入库单号': 'Inbound ID',
  '操作时间': 'Operation Time',
  '二维码内容': 'QR Code Content',
  '操作状态': 'Operation Status',
  '物品入库结果': 'Item Inbound Results',
  '库存变化': 'Inventory Change',
  '成功入库': 'Successfully Inbound',

  
  // Purchase Request Information
  '申请单号': 'Request Number',
  '申请部门': 'Requesting Department',
  '申请状态': 'Request Status',
  '提交时间': 'Submission Time',
  '备注': 'Notes',
  
  // Notes
  '备注信息': 'Notes',
  '请输入入库备注信息（可选）': 'Please enter inbound notes (optional)',
  
  // Action Buttons
  '重新开始': 'Restart',
  '返回扫码': 'Back to Scan',
  '继续入库': 'Continue Inbound',
  '打印结果': 'Print Results',
  '打印入库单': 'Print Inbound Receipt',
  
  // Statistics
  '总数': 'Total',
  '成功数': 'Success Count',
  '失败数': 'Failed Count',
  
  // Error Messages
  '网络错误': 'Network Error',
  '服务器错误': 'Server Error',
  '权限不足': 'Insufficient Permissions',
  '数据验证失败': 'Data Validation Failed',
  '未知错误': 'Unknown Error'
};
