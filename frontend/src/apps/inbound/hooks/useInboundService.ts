import { useState } from 'react';
import { message } from 'antd';
import { ScanQRCodeRequest, ScanQRCodeResponse, InboundRequest, InboundResponse } from '../../../shared/types/inbound';
import { inboundAPI } from '../services/api';

export const useInboundService = () => {
  const [loading, setLoading] = useState(false);
  
  // 扫码二维码
  const scanQRCode = async (request: ScanQRCodeRequest): Promise<ScanQRCodeResponse> => {
    setLoading(true);
    try {
      const result = await inboundAPI.scanQRCode(request) as unknown as ScanQRCodeResponse;
      return result;
    } catch (error) {
      console.error('Scan QR code error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // 执行入库
  const executeInbound = async (request: InboundRequest): Promise<InboundResponse> => {
    setLoading(true);
    try {
      const result = await inboundAPI.executeInbound(request) as unknown as InboundResponse;
      return result;
    } catch (error) {
      console.error('Execute inbound error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  return {
    loading,
    scanQRCode,
    executeInbound
  };
};
