import React, { useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, Input, Button, message, Space, Typography, Divider } from 'antd';
import { ScanOutlined, InboxOutlined, ReloadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

import InboundItemList from '../components/InboundItemList';
import InboundResult from '../components/InboundResult';
import { useInboundService } from '../hooks/useInboundService';
import { ScanQRCodeRequest, InboundRequest } from '../../../shared/types/inbound';

const { Title, Text } = Typography;
const { Search } = Input;

const InboundPage: React.FC = () => {
  const { t } = useTranslation('inbound');
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  
  // URL状态管理
  const qrCode = searchParams.get('qrCode') || '';
  const step = searchParams.get('step') || 'scan';
  
  // 本地状态
  const [scanning, setScanning] = useState(false);
  const [inbounding, setInbounding] = useState(false);
  
  // 服务调用
  const { scanQRCode, executeInbound } = useInboundService();
  
  // 扫码结果
  const [scanResult, setScanResult] = useState<any>(null);
  
  // 入库结果
  const [inboundResult, setInboundResult] = useState<any>(null);
  
  // 处理扫码
  const handleScan = async (value: string) => {
    if (!value.trim()) {
      message.error(t('请输入二维码内容'));
      return;
    }
    
    setScanning(true);
    try {
      const request: ScanQRCodeRequest = { qr_code: value.trim() };
      const result = await scanQRCode(request);
      
      if (result.success) {
        setScanResult(result);
        setSearchParams({ qrCode: value.trim(), step: 'items' });
        message.success(t('扫码成功'));
      } else {
        message.error(result.message || t('扫码失败'));
      }
    } catch (error) {
      message.error(t('扫码失败，请重试'));
      console.error('Scan error:', error);
    } finally {
      setScanning(false);
    }
  };
  
  // 处理入库
  const handleInbound = async (inboundRequest: InboundRequest) => {
    setInbounding(true);
    try {
      const result = await executeInbound(inboundRequest);
      
      // 保存入库结果
      setInboundResult(result);
      
      // 检查入库结果
      if (result.success && result.success_items > 0) {
        setSearchParams({ 
          qrCode, 
          step: 'result',
          inboundId: result.inbound_id || ''
        });
        message.success(t('入库成功'));
      } else {
        // 即使API返回success=true，但如果所有物品都失败，仍然显示失败
        if (result.failed_items === result.total_items) {
          message.error(t('入库失败，所有物品都无法入库'));
        } else if (result.success_items > 0) {
          // 部分成功
          message.warning(t(`入库部分成功：${result.success_items}/${result.total_items} piece 物品入库成功`));
          setSearchParams({ 
            qrCode, 
            step: 'result',
            inboundId: result.inbound_id || ''
          });
        } else {
          message.error(result.message || t('入库失败'));
        }
      }
    } catch (error) {
      message.error(t('入库失败，请重试'));
      console.error('Inbound error:', error);
    } finally {
      setInbounding(false);
    }
  };
  
  // 重置操作
  const handleReset = () => {
    setScanResult(null);
    setInboundResult(null);
    setSearchParams({});
  };
  
  // 渲染不同步骤
  const renderStep = () => {
    switch (step) {
      case 'items':
        return (
          <InboundItemList
            scanResult={scanResult}
            onInbound={handleInbound}
            onBack={() => setSearchParams({ qrCode, step: 'scan' })}
            loading={inbounding}
          />
        );
      case 'result':
        return (
          <InboundResult
            qrCode={qrCode}
            onReset={handleReset}
            inboundResult={inboundResult}
          />
        );
      default:
        return (
          <Card>
            <div style={{ textAlign: 'center', padding: '40px 20px' }}>
              <InboxOutlined style={{ fontSize: '64px', color: '#1890ff', marginBottom: '24px' }} />
              <Title level={3}>{t('扫码入库')}</Title>
              <Text type="secondary">{t('请扫描采购申请单上的二维码开始入库操作')}</Text>
              
              <Divider />
              
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Search
                  placeholder={t('请输入或扫描二维码内容')}
                  enterButton={
                    <Button 
                      type="primary" 
                      icon={<ScanOutlined />}
                      loading={scanning}
                    >
                      {scanning ? t('扫描中...') : t('扫描')}
                    </Button>
                  }
                  size="large"
                  value={qrCode}
                  onChange={(e) => setSearchParams({ qrCode: e.target.value })}
                  onSearch={handleScan}
                  style={{ maxWidth: '500px' }}
                />
                
                <Text type="secondary">
                  {t('支持手动输入二维码内容或使用扫码枪扫描')}
                </Text>
              </Space>
            </div>
          </Card>
        );
    }
  };
  
  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleReset}
            disabled={step === 'scan'}
          >
            {t('重新开始')}
          </Button>
        </Space>
      </div>
      
      {renderStep()}
    </div>
  );
};

export default InboundPage;
