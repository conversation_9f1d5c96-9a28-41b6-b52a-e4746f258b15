import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: '/api/inbound',
  timeout: 10000,
});

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('inbound_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      console.log('API请求401错误，需要退出登录状态');
      
      // 清除本地存储的认证信息
      localStorage.removeItem('inbound_token');
      localStorage.removeItem('inbound_user');
      
      // 触发全局事件，通知AuthContext更新状态
      window.dispatchEvent(new CustomEvent('auth:logout', {
        detail: { reason: 'token_expired' }
      }));
      
      // 跳转到登录页面，让用户重新登录
      window.location.replace('/inbound/login');
    }
    return Promise.reject(error);
  }
);

// 认证相关API
export const authAPI = {
  // 登录
  login: (username: string, password: string) =>
    api.post('/auth/login', { username, password }),
  
  // 验证token
  verify: () => api.get('/auth/verify'),
};

// 入库相关API
export const inboundAPI = {
  // 扫描二维码
  scanQRCode: (data: { qr_code: string }) =>
    api.post('/inventory/scan-qr', data),
  
  // 执行入库
  executeInbound: (data: any) =>
    api.post('/inventory/inbound', data),
  
  // 获取库存列表
  getInventoryList: (params?: any) =>
    api.get('/inventory', { params }),
  
  // 获取指定物品库存
  getItemInventory: (itemId: number) =>
    api.get(`/inventory/${itemId}`),
};

export default api;
