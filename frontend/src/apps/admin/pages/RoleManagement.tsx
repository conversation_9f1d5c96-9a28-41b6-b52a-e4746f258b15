import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import {
  Table,
  Card,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Row,
  Col,
  Tabs,
  Descriptions,
  Badge,
  Checkbox,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SettingOutlined,
  ReloadOutlined,
  SearchOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { roleService, Role, Permission, PermissionNode } from '@admin/services/roleService';

// 通用错误处理函数
const handleApiError = (error: any, defaultMessage: string = '操作失败') => {
  if (error.response?.data?.detail) {
    const detail = error.response.data.detail;
    if (Array.isArray(detail)) {
      // 处理字段验证错误
      const errorMessages = detail.map((err: any) => {
        if (err.loc && err.msg) {
          const field = err.loc[err.loc.length - 1];
          return `${field}: ${err.msg}`;
        }
        return err.msg;
      });
      return errorMessages.join(', ');
    } else {
      return detail;
    }
  }
  return defaultMessage;
};

const { Option } = Select;
const { Search } = Input;
const { TabPane } = Tabs;
const { TextArea } = Input;

interface RoleManagementProps {}

const RoleManagement: React.FC<RoleManagementProps> = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissionGroups, setPermissionGroups] = useState<PermissionNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [roleModalVisible, setRoleModalVisible] = useState(false);
  const [permissionModalVisible, setPermissionModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [permissionForm] = Form.useForm();
  
  // 筛选状态
  const [filters, setFilters] = useState({
    search: '',
    is_active: true, // 默认筛选有效状态
  });

  // 加载角色列表
  const loadRoles = useCallback(async () => {
    setLoading(true);
    try {
      const roles = await roleService.getRoles(filters);
      setRoles(roles);
    } catch (error) {
      message.error(t('messages.loadRoleListFailed'));
    } finally {
      setLoading(false);
    }
  }, [filters, t]);

  // 加载权限列表
  const loadPermissions = async () => {
    try {
      const groupsResponse = await roleService.getPermissionGroups();
      setPermissionGroups(Array.isArray(groupsResponse) ? groupsResponse : []);
    } catch (error: any) {
      message.error(t('messages.loadPermissionListFailed'));
      setPermissionGroups([]);
    }
  };



  useEffect(() => {
    loadRoles();
  }, [filters, loadRoles]);

  useEffect(() => {
    loadPermissions();
  }, []);

  // 处理搜索
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
  };

  // 显示新增角色模态框
  const showCreateModal = () => {
    setSelectedRole(null);
    form.resetFields();
    setRoleModalVisible(true);
  };

  // 显示编辑角色模态框
  const showEditModal = (role: Role) => {
    setSelectedRole(role);
    form.setFieldsValue({
      ...role,
      permission_codes: role.permissions?.map(p => p.code) || [],
    });
    setRoleModalVisible(true);
  };

  // 显示角色详情
  const showRoleDetail = (role: Role) => {
    setSelectedRole(role);
    setDetailModalVisible(true);
  };

  // 显示权限配置模态框
  const showPermissionModal = (role: Role) => {
    setSelectedRole(role);
    permissionForm.setFieldsValue({
      permission_codes: role.permissions?.map(p => p.code) || [],
    });
    setPermissionModalVisible(true);
  };

  // 保存角色
  const handleSaveRole = async (values: any) => {
    try {
      if (selectedRole) {
        // 更新角色
        await roleService.updateRole(selectedRole.id, values);
        message.success(t('messages.roleUpdateSuccess'));
      } else {
        // 创建角色
        await roleService.createRole(values);
        message.success(t('messages.roleCreateSuccess'));
      }
      setRoleModalVisible(false);
      loadRoles();
    } catch (error: any) {
      message.error(handleApiError(error, t('messages.operationFailed')));
    }
  };

  // 配置权限
  const handleConfigPermissions = async (values: any) => {
    if (!selectedRole) return;
    
    try {
      await roleService.updateRole(selectedRole.id, {
        permission_codes: values.permission_codes,
      });
      message.success(t('messages.permissionConfigSuccess'));
      setPermissionModalVisible(false);
      loadRoles();
    } catch (error: any) {
      message.error(handleApiError(error, t('messages.permissionConfigFailed')));
    }
  };

  // 删除角色
  const handleDeleteRole = async (role: Role) => {
    try {
      await roleService.deleteRole(role.id);
      message.success(t('messages.roleDeleteSuccess'));
      loadRoles();
    } catch (error: any) {
      message.error(handleApiError(error, t('messages.deleteFailed')));
    }
  };







  // 表格列定义
  const columns: ColumnsType<Role> = [
    {
      title: t('roleManagement.roleCode'),
      dataIndex: 'code',
      key: 'code',
      width: 150,
      render: (code: string, record: Role) => (
        <Button 
          type="link" 
          onClick={() => navigate(`/admin/roles/${record.id}`)}
          style={{ padding: 0, height: 'auto' }}
        >
          {code}
        </Button>
      ),
    },
    {
      title: t('roleManagement.roleName'),
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (text: string, record: Role) => (
        <Space>
          {text}
          {record.is_system && <Tag color="gold">{t('roleManagement.systemRole')}</Tag>}
        </Space>
      ),
    },
    {
      title: t('roleManagement.description'),
      dataIndex: 'description',
      key: 'description',
      width: 250,
      ellipsis: true,
    },

    {
      title: t('roleManagement.permissionCount'),
      dataIndex: 'permissions',
      key: 'permissions',
      width: 100,
      render: (permissions: Permission[]) => (
        <Badge count={permissions.length} showZero color="#52c41a" />
      ),
    },
    {
      title: t('roleManagement.status'),
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (is_active) => (
        <Tag color={is_active ? 'green' : 'red'}>
          {is_active ? t('roleManagement.active') : t('roleManagement.inactive')}
        </Tag>
      ),
    },
    {
      title: t('roleManagement.createdAt'),
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time) => dayjs(time).format(DATE_FORMATS.DATE_TIME),
    },
    {
      title: t('roleManagement.actions'),
      key: 'actions',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title={t('roleManagement.viewDetail')}>
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => showRoleDetail(record)}
            />
          </Tooltip>
          <Tooltip title={t('roleManagement.editRole')}>
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => showEditModal(record)}
              disabled={record.is_system}
            />
          </Tooltip>
          <Tooltip title={t('roleManagement.configurePermissions')}>
            <Button
              type="text"
              size="small"
              icon={<SettingOutlined />}
              onClick={() => showPermissionModal(record)}
            />
          </Tooltip>
          {!record.is_system && (
            <Popconfirm
              title={t('roleManagement.confirmDeleteRole')}
              onConfirm={() => handleDeleteRole(record)}
              okText={t('roleManagement.confirm')}
              cancelText={t('roleManagement.cancel')}
            >
              <Tooltip title={t('roleManagement.deleteRole')}>
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Search
              placeholder={t('roleManagement.searchRoleNameOrCode')}
              onSearch={handleSearch}
              enterButton={<SearchOutlined />}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder={t('roleManagement.status')}
              allowClear
              style={{ width: '100%' }}
              value={filters.is_active}
              onChange={(value) => setFilters(prev => ({ ...prev, is_active: value }))}
            >
              <Option value={true}>{t('roleManagement.active')}</Option>
              <Option value={false}>{t('roleManagement.inactive')}</Option>
            </Select>
          </Col>
          <Col span={12}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={showCreateModal}
              >
                {t('roleManagement.addRole')}
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadRoles}
              >
                {t('roleManagement.refresh')}
              </Button>

            </Space>
          </Col>
        </Row>



        <Table
          columns={columns}
          dataSource={roles}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新增/编辑角色模态框 */}
      <Modal
        title={selectedRole ? t('roleManagement.editRole') : t('roleManagement.addRole')}
        open={roleModalVisible}
        onCancel={() => setRoleModalVisible(false)}
        onOk={() => form.submit()}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveRole}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label={t('roleManagement.roleCode')}
                rules={[
                  { required: true, message: t('roleManagement.roleCodeRequired') },
                  { pattern: /^[a-z_]+$/, message: t('roleManagement.roleCodePattern') },
                ]}
              >
                <Input placeholder={t('roleManagement.roleCode')} disabled={!!selectedRole} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label={t('roleManagement.roleName')}
                rules={[{ required: true, message: t('roleManagement.roleNameRequired') }]}
              >
                <Input placeholder={t('roleManagement.roleName')} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label={t('roleManagement.roleDescription')}>
            <TextArea placeholder={t('roleManagement.roleDescription')} rows={3} />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="priority" label={t('roleManagement.priority')}>
                <Select placeholder={t('roleManagement.selectPriority')}>
                  <Option value={100}>{t('roleManagement.low')}</Option>
                  <Option value={500}>{t('roleManagement.medium')}</Option>
                  <Option value={800}>{t('roleManagement.high')}</Option>
                  <Option value={900}>{t('roleManagement.highest')}</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="permission_codes" label={t('roleManagement.permissionConfig')}>
            <div style={{ maxHeight: 300, overflow: 'auto', border: '1px solid #d9d9d9', borderRadius: 6, padding: 16 }}>
              {permissionGroups.map(group => (
                <div key={group.code} style={{ marginBottom: 16 }}>
                  <div style={{ fontWeight: 'bold', marginBottom: 8, color: '#1890ff' }}>
                    {group.name}
                  </div>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                    {group.permissions?.map(permission => (
                      <Checkbox
                        key={permission.code}
                        checked={form.getFieldValue('permission_codes')?.includes(permission.code)}
                        onChange={(e) => {
                          const currentCodes = form.getFieldValue('permission_codes') || [];
                          if (e.target.checked) {
                            form.setFieldsValue({ permission_codes: [...currentCodes, permission.code] });
                          } else {
                            form.setFieldsValue({ permission_codes: currentCodes.filter((code: string) => code !== permission.code) });
                          }
                        }}
                      >
                        {permission.name}
                      </Checkbox>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 权限配置模态框 */}
      <Modal
        title={t('roleManagement.configurePermissions')}
        open={permissionModalVisible}
        onCancel={() => setPermissionModalVisible(false)}
        onOk={() => permissionForm.submit()}
        width={800}
      >
        <Form
          form={permissionForm}
          layout="vertical"
          onFinish={handleConfigPermissions}
        >
          <Form.Item name="permission_codes" label={t('roleManagement.selectPermission')}>
            <div style={{ maxHeight: 400, overflow: 'auto', border: '1px solid #d9d9d9', borderRadius: 6, padding: 16 }}>
              {permissionGroups.map(group => (
                <div key={group.code} style={{ marginBottom: 16 }}>
                  <div style={{ fontWeight: 'bold', marginBottom: 8, color: '#1890ff' }}>
                    {group.name}
                  </div>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                    {group.permissions?.map(permission => (
                      <Checkbox
                        key={permission.code}
                        checked={permissionForm.getFieldValue('permission_codes')?.includes(permission.code)}
                        onChange={(e) => {
                          const currentCodes = permissionForm.getFieldValue('permission_codes') || [];
                          if (e.target.checked) {
                            permissionForm.setFieldsValue({ permission_codes: [...currentCodes, permission.code] });
                          } else {
                            permissionForm.setFieldsValue({ permission_codes: currentCodes.filter((code: string) => code !== permission.code) });
                          }
                        }}
                      >
                        {permission.name}
                      </Checkbox>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 角色详情模态框 */}
      <Modal
        title={t('roleManagement.roleDetail')}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedRole && (
          <Tabs defaultActiveKey="1">
            <TabPane tab={t('roleManagement.basicInfo')} key="1">
              <Descriptions column={2} bordered>
                <Descriptions.Item label={t('roleManagement.roleCode')}>{selectedRole.code}</Descriptions.Item>
                <Descriptions.Item label={t('roleManagement.roleName')}>{selectedRole.name}</Descriptions.Item>

                <Descriptions.Item label={t('roleManagement.status')}>
                  <Tag color={selectedRole.is_active ? 'green' : 'red'}>
                    {selectedRole.is_active ? t('roleManagement.active') : t('roleManagement.inactive')}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label={t('roleManagement.systemRole')}>
                  <Tag color={selectedRole.is_system ? 'gold' : 'blue'}>
                    {selectedRole.is_system ? t('roleManagement.yes') : t('roleManagement.no')}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label={t('roleManagement.createdAt')}>
                  {dayjs(selectedRole.created_at).format(DATE_FORMATS.DATE_TIME)}
                </Descriptions.Item>
                <Descriptions.Item label={t('roleManagement.description')} span={2}>
                  {selectedRole.description || '-'}
                </Descriptions.Item>
              </Descriptions>
            </TabPane>
            <TabPane tab={`${t('roleManagement.permissionList')} (${selectedRole.permissions?.length || 0})`} key="2">
              <div style={{ maxHeight: 400, overflow: 'auto' }}>
                {selectedRole.permissions?.map(permission => (
                  <Tag
                    key={permission.code}
                    color="blue"
                    style={{ marginBottom: 8, marginRight: 8 }}
                  >
                    {permission.name} ({permission.code})
                  </Tag>
                )) || <span style={{ color: '#999' }}>{t('roleManagement.noPermissions')}</span>}
              </div>
            </TabPane>
          </Tabs>
        )}
      </Modal>


    </div>
  );
};

export default RoleManagement; 