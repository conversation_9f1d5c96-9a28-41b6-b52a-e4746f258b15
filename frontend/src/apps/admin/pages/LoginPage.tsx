import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, Layout, Space } from 'antd';
import { UserOutlined, LockOutlined, ShopOutlined } from '@ant-design/icons';
import { useNavigate, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { Content } = Layout;

interface LoginFormValues {
  username: string;
  password: string;
}

const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const navigate = useNavigate();
  const { isAuthenticated, login } = useAuth();
  const { t } = useTranslation();

  // 如果已经登录，重定向到主页
  if (isAuthenticated) {
    return <Navigate to="/items" replace />;
  }

  const onFinish = async (values: LoginFormValues) => {
    setLoading(true);
    setErrorMessage(''); // 清除之前的错误信息
    
    try {
      const success = await login(values.username, values.password);
      if (success) {
        navigate('/admin/items');
      } else {
        // 登录失败，错误信息已经在AuthContext中显示
        // 这里设置一个通用的错误信息，让用户知道需要重试
        setErrorMessage('登录失败，请检查用户名和密码后重试');
        // 不清空表单，保留用户名和密码
      }
    } catch (error: any) {
      console.error('Login error in LoginPage:', error);
      
      // 根据错误类型设置具体的错误信息
      let errorMsg = '登录过程中发生错误，请重试';
      
      if (error.response?.status >= 500) {
        errorMsg = '服务器暂时不可用，请稍后重试';
      } else if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
        errorMsg = '网络连接失败，请检查网络设置';
      } else if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
        errorMsg = '请求超时，请检查网络连接';
      } else if (error.response?.status === 0) {
        errorMsg = '无法连接到服务器，请检查服务器状态';
      }
      
      setErrorMessage(errorMsg);
      // 不清空表单，保留用户名和密码
    } finally {
      setLoading(false);
    }
  };

  const onValuesChange = (changedValues: any, allValues: any) => {
    // 当用户开始输入时，不清除错误信息
    // 这样用户可以修改用户名或密码后直接重试，无需重新输入
    // 错误信息会在下次提交时自动清除
  };

  const clearError = () => {
    setErrorMessage('');
  };

  return (
    <Layout style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
      <Content style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '50px' }}>
        <Card 
          style={{ 
            width: 400, 
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
            borderRadius: '12px',
            border: 'none'
          }}
        >
          <Space direction="vertical" size="large" style={{ width: '100%', textAlign: 'center' }}>
            {/* Logo和标题 */}
            <div>
              <ShopOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
              <Title level={2} style={{ margin: 0, color: '#1f1f1f' }}>
                {t('system.systemName')}
              </Title>
              <Text type="secondary">BizLinkSpeedy IDM</Text>
            </div>

            {/* 登录表单 */}
            <Form
              name="login"
              size="large"
              onFinish={onFinish}
              onValuesChange={onValuesChange}
              autoComplete="off"
              style={{ width: '100%' }}
            >
              {/* 错误信息显示 */}
              {errorMessage && (
                <div style={{ 
                  marginBottom: '16px', 
                  padding: '12px 16px', 
                  backgroundColor: '#fff2f0', 
                  border: '1px solid #ffccc7', 
                  borderRadius: '8px',
                  color: '#cf1322',
                  fontSize: '14px',
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}>
                  <span style={{ flex: 1 }}>
                    <span style={{ marginRight: '8px' }}>⚠️</span>
                    {errorMessage}
                  </span>
                  {/* 关闭按钮 */}
                  <button
                    onClick={clearError}
                    style={{
                      background: 'none',
                      border: 'none',
                      color: '#cf1322',
                      cursor: 'pointer',
                      fontSize: '16px',
                      padding: '0',
                      marginLeft: '4px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '20px',
                      height: '20px',
                      borderRadius: '50%',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'rgba(207, 19, 34, 0.1)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                    title="关闭提示"
                  >
                    ✕
                  </button>
                </div>
              )}
              
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: t('login.usernameRequired') },
                  { min: 3, message: t('login.usernameMinLength') }
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder={t('user.username')}
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: t('login.passwordRequired') },
                  { min: 6, message: t('login.passwordMinLength') }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder={t('user.password')}
                  autoComplete="current-password"
                />
              </Form.Item>

              <Form.Item style={{ marginBottom: 0 }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  style={{ 
                    width: '100%', 
                    height: '44px',
                    fontSize: '16px',
                    fontWeight: '500'
                  }}
                >
                  {loading ? t('login.loggingIn') : t('system.login')}
                </Button>
              </Form.Item>
              
            </Form>
          </Space>
        </Card>
      </Content>
    </Layout>
  );
};

export default LoginPage; 