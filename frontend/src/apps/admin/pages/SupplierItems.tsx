import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import {
  Card,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Empty,
  Spin,
  message,
  Popconfirm,
  Tag,
  Image,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { 
  supplierService, 
  Supplier, 
  ItemSupplier,
  SupplierPrice,
  supplierPriceService
} from '@admin/services/supplierService';
import PermissionGuard from '@shared/components/PermissionGuard';
import { PERMISSIONS } from '@shared/config/permissions';
import ItemSelector from '@admin/components/ItemSelector';
import ItemConfiguration from '@admin/components/ItemConfiguration';
import PriceManagement from '@admin/components/PriceManagement';

const { Title, Text } = Typography;

interface SupplierItemsProps {}

interface ItemWithUSDPrices extends ItemSupplier {
  usd_prices?: Array<{
    original_price: number;
    original_currency: string;
    usd_price: number;
    min_quantity: number;
    max_quantity?: number;
    valid_from: string;
    valid_to?: string;
    remarks?: string;
  }>;
  price_range?: {
    min_usd_price: number;
    max_usd_price: number;
    has_multiple_prices: boolean;
  };
}

const SupplierItems: React.FC<SupplierItemsProps> = () => {
  const { t } = useTranslation();
  const { id, itemId } = useParams<{ id: string; itemId?: string }>();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [itemSuppliers, setItemSuppliers] = useState<ItemWithUSDPrices[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedItemSupplier, setSelectedItemSupplier] = useState<ItemWithUSDPrices | null>(null);
  const [itemSelectorVisible, setItemSelectorVisible] = useState(false);
  const [supplier, setSupplier] = useState<Supplier | null>(null);

  // 从URL路径参数获取选中的物品ID（优先使用路径参数）
  const selectedItemId = itemId || searchParams.get('selectedItem');

  // 获取供应商信息
  const fetchSupplier = useCallback(async (supplierId: number) => {
    try {
      const response = await supplierService.getSupplier(supplierId);
      setSupplier(response);
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('messages.getSupplierInfoFailed'));
    }
  }, [t]);

  // 获取供应商的物品列表（包含USD价格）
  const fetchItemSuppliers = useCallback(async (supplierId: number) => {
    setLoading(true);
    try {
      // 使用新的API端点获取包含USD价格的物品列表
      const response = await supplierService.getSupplierItemsWithUSDPrices(supplierId);
      
      setItemSuppliers(response);
      
      // 如果有URL参数指定的选中物品，自动选中
      if (selectedItemId) {
        const itemToSelect = response.find(item => item.item_id.toString() === selectedItemId);
        if (itemToSelect) {
          setSelectedItemSupplier(itemToSelect);
          // 如果是从路径参数来的，清除URL中的selectedItem参数
          if (itemId && searchParams.get('selectedItem')) {
            const newParams = new URLSearchParams(searchParams);
            newParams.delete('selectedItem');
            setSearchParams(newParams);
          }
        }
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('messages.getSupplierItemsFailed'));
      setItemSuppliers([]);
    } finally {
      setLoading(false);
    }
  }, [selectedItemId, itemId, searchParams, setSearchParams, t]);

  useEffect(() => {
    let isMounted = true;
    
    if (id) {
      const supplierId = parseInt(id);
      // 获取供应商信息
      fetchSupplier(supplierId);
      // 获取供应商物品列表
      fetchItemSuppliers(supplierId).then(() => {
        if (!isMounted) {
          return;
        }
      });
    }

    return () => {
      isMounted = false;
    };
  }, [id, fetchSupplier, fetchItemSuppliers]); // 移除fetchItemSuppliers依赖项

  // 处理添加物品
  const handleAddItem = async (values: any) => {
    if (!id) return;
    try {
      await supplierService.addSupplierItem(parseInt(id), values);
      message.success(t('messages.itemAddedSuccessfully'));
      setItemSelectorVisible(false);
      // 添加物品后需要重新加载列表
      fetchItemSuppliers(parseInt(id));
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('messages.addFailed'));
    }
  };

  // 处理物品选择
  const handleItemSelect = (item: any) => {
    handleAddItem({
      item_id: item.id,
      supplier_id: parseInt(id!)
    });
  };

  // 处理删除物品
  const handleDeleteItem = async (itemSupplier: ItemWithUSDPrices) => {
    try {
      await supplierService.deleteItemSupplier(itemSupplier.id);
      message.success(t('messages.itemDeletedSuccessfully'));
      // 如果删除的是当前选中的物品，清空选中状态
      if (selectedItemSupplier?.id === itemSupplier.id) {
        setSelectedItemSupplier(null);
      }
      // 重新加载列表
      fetchItemSuppliers(parseInt(id!));
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('messages.deleteFailed'));
    }
  };

  // 处理物品点击
  const handleItemClick = (itemSupplier: ItemWithUSDPrices) => {
    setSelectedItemSupplier(itemSupplier);
    // 使用新的URL格式：/suppliers/:id/items/:itemId
    navigate(`/admin/suppliers/${id}/items/${itemSupplier.item_id}`, {replace: true});
  };

  // 处理图片点击（跳转到物品详情）
  const handleImageClick = (e: React.MouseEvent, itemSupplier: ItemWithUSDPrices) => {
    e.stopPropagation();
    if (itemSupplier.item?.id) {
      navigate(`/admin/items/${itemSupplier.item.id}`);
    }
  };

  // 获取USD价格范围显示
  const getUSDPriceRange = (usdPrices: any[] = [], purchaseUnit?: string) => {
    if (usdPrices.length === 0) return t('messages.noPrice');
    
    const minPrice = Math.min(...usdPrices.map(p => p.usd_price));
    const maxPrice = Math.max(...usdPrices.map(p => p.usd_price));
    
    let priceText = '';
    if (minPrice === maxPrice) {
      priceText = `$${minPrice.toFixed(2)}`;
    } else {
      priceText = `$${minPrice.toFixed(2)} - $${maxPrice.toFixed(2)}`;
    }
    
    // 如果有采购单位，则显示单位
    if (purchaseUnit) {
      return `${priceText} / ${purchaseUnit}`;
    }
    
    return priceText;
  };

  // 渲染物品卡片
  const renderItemCard = (itemSupplier: ItemWithUSDPrices) => {
    const isSelected = selectedItemSupplier?.id === itemSupplier.id;
    
    return (
      <Card
        key={itemSupplier.id}
        style={{
          marginBottom: '8px',
          cursor: 'pointer',
          border: isSelected ? '2px solid #1890ff' : '1px solid #d9d9d9',
          backgroundColor: isSelected ? '#f0f8ff' : 'white'
        }}
        onClick={() => handleItemClick(itemSupplier)}
        bodyStyle={{ padding: '12px' }}
      >
        <Row gutter={12} align="middle">
          <Col span={4}>
            <div style={{ textAlign: 'center' }}>
              {itemSupplier.item?.image_url ? (
                <Image
                  src={itemSupplier.item.image_url}
                  alt={itemSupplier.item.name}
                  width={60}
                  height={60}
                  style={{ objectFit: 'cover', borderRadius: '6px' }}
                  onClick={(e) => handleImageClick(e, itemSupplier)}
                  preview={{
                    mask: <div style={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '16px'
                    }}>
                      <EyeOutlined />
                    </div>
                  }}
                />
              ) : (
                <div
                  style={{
                    width: 60,
                    height: 60,
                    backgroundColor: '#f5f5f5',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '6px',
                    color: '#999',
                    fontSize: '12px'
                  }}
                >
                  {t('messages.noImage')}
                </div>
              )}
            </div>
          </Col>
          
          <Col span={10}>
            <div>
              <Text strong style={{ 
                fontSize: '14px', 
                display: 'block', 
                marginBottom: '4px',
                lineHeight: '1.4',
                textAlign: 'left'
              }}>
                {itemSupplier.item?.name || t('messages.unknownItem')}
              </Text>
              <div style={{ 
                marginTop: '4px', 
                textAlign: 'left',
                overflow: 'hidden',
                whiteSpace: 'nowrap'
              }}>
                <Tag color="blue" style={{ 
                  fontSize: '10px',
                  fontWeight: '500',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  marginRight: '4px'
                }}>
                  {itemSupplier.item?.category?.name || t('messages.uncategorized')}
                </Tag>
                {itemSupplier.item?.brand && (
                  <Tag color="purple" style={{ 
                    fontSize: '10px',
                    fontWeight: '500',
                    padding: '2px 6px',
                    borderRadius: '4px',
                    marginRight: '4px'
                  }}>
                    {itemSupplier.item.brand}
                  </Tag>
                )}
                {itemSupplier.item?.spec_material && (
                  <Tag color="cyan" style={{ 
                    fontSize: '10px',
                    fontWeight: '500',
                    padding: '2px 6px',
                    borderRadius: '4px',
                    marginRight: '4px'
                  }}>
                    {itemSupplier.item.spec_material}
                  </Tag>
                )}
                {itemSupplier.item?.size_dimension && (
                  <Tag color="blue" style={{ 
                    fontSize: '10px',
                    fontWeight: '500',
                    padding: '2px 6px',
                    borderRadius: '4px',
                    marginRight: '4px'
                  }}>
                    {itemSupplier.item.size_dimension}
                  </Tag>
                )}
              </div>
            </div>
          </Col>
          
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <Text strong style={{ 
                fontSize: '14px', 
                color: '#1890ff',
                lineHeight: '1.2'
              }}>
                {getUSDPriceRange(itemSupplier.usd_prices, itemSupplier.item?.purchase_unit)}
              </Text>
            </div>
          </Col>
          
          <Col span={4}>
            <div style={{ textAlign: 'right' }}>
              <Space size="small">
                <PermissionGuard permission={PERMISSIONS.SUPPLIER.UPDATE}>
                  <Tooltip title={t('messages.edit')}>
                    <Button
                      type="text"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleItemClick(itemSupplier);
                      }}
                      style={{
                        color: '#1890ff'
                      }}
                    />
                  </Tooltip>
                </PermissionGuard>
                <PermissionGuard permission={PERMISSIONS.SUPPLIER.DELETE}>
                  <Popconfirm
                    title={t('messages.confirmDeleteItem')}
                    onConfirm={(e) => {
                      e?.stopPropagation();
                      handleDeleteItem(itemSupplier);
                    }}
                    okText={t('messages.confirm')}
                    cancelText={t('messages.cancel')}
                  >
                    <Button
                      type="text"
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </Popconfirm>
                </PermissionGuard>
              </Space>
            </div>
          </Col>
        </Row>
      </Card>
    );
  };

  return (
    <div style={{ height: 'calc(100vh - 64px)', padding: '12px 16px', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      <div style={{ marginBottom: '8px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Text strong style={{ margin: 0, fontSize: 16, lineHeight: 1.2 }}>
              {supplier ? `${supplier.name_en || supplier.name_cn || t('messages.unknownSupplier')} ${t('messages.itemManagement')}` : t('messages.supplierItemManagement')}
            </Text>
          </Col>
        </Row>
      </div>

      <Row gutter={16} style={{ flex: 1, minHeight: 0, overflow: 'hidden', height: '100%' }}>
        {/* 左侧：物品列表 - 占1/3 */}
        <Col span={8} style={{ display: 'flex', flexDirection: 'column', height: '100%', overflow: 'hidden' }}>
          <Card
            title={
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                width: '100%'
              }}>
                <span>{t('messages.itemList')}</span>
                <PermissionGuard permission={PERMISSIONS.SUPPLIER.CREATE}>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => setItemSelectorVisible(true)}
                    size="small"
                  >
                    {t('messages.addItem')}
                  </Button>
                </PermissionGuard>
              </div>
            }
            style={{ height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}
            headStyle={{ padding: '8px 12px' }}
            bodyStyle={{ flex: 1, padding: '12px', overflow: 'hidden', minHeight: 0, display: 'flex', flexDirection: 'column' }}
          >
            <div style={{ flex: 1, minHeight: 0, overflowY: 'auto' }}>
              {loading ? (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <Spin size="large" />
                </div>
              ) : itemSuppliers.length === 0 ? (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <Empty 
                    description={t('messages.noSupplierItems')}
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                </div>
              ) : (
                <div>
                  {itemSuppliers.map(itemSupplier => (
                    <div key={itemSupplier.id}>
                      {renderItemCard(itemSupplier)}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>
        </Col>

        {/* 右侧：分为上下两部分 - 占2/3 */}
        <Col span={16} style={{ display: 'flex', flexDirection: 'column', height: '100%', overflow: 'hidden' }}>
          <div style={{ 
            flex: 1, 
            display: 'flex', 
            flexDirection: 'column', 
            minHeight: 0,
            height: '100%'
          }}>
            {/* 上方：物品配置 */}
            <div style={{ marginBottom: '12px', flexShrink: 0 }}>
              <ItemConfiguration 
                selectedItemSupplier={selectedItemSupplier}
                onRefresh={async () => {
                  // 更新配置后，重新获取当前物品的完整信息
                  if (selectedItemSupplier && id) {
                    try {
                      // 重新获取物品列表（跳过价格获取以提高性能）
                      await fetchItemSuppliers(parseInt(id));
                      // 更新选中的物品
                      const updatedItem = itemSuppliers.find(item => item.id === selectedItemSupplier.id);
                      if (updatedItem) {
                        setSelectedItemSupplier(updatedItem);
                      }
                    } catch (error) {
                      console.error(t('messages.updateItemInfoFailed'), error);
                    }
                  }
                }}
              />
            </div>

            {/* 下方：阶梯价格管理 */}
            <div style={{ flex: 1, minHeight: 0, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
              <PriceManagement 
                selectedItemSupplier={selectedItemSupplier}
                onRefresh={async () => {
                  // 价格更新后，重新获取当前物品的价格信息
                  if (selectedItemSupplier && id) {
                    try {
                      // 重新获取物品列表（包含价格信息）
                      await fetchItemSuppliers(parseInt(id));
                      // 更新选中的物品
                      const updatedItem = itemSuppliers.find(item => item.id === selectedItemSupplier.id);
                      if (updatedItem) {
                        setSelectedItemSupplier(updatedItem);
                      }
                    } catch (error) {
                      console.error(t('messages.updatePriceInfoFailed'), error);
                    }
                  }
                }}
              />
            </div>
          </div>
        </Col>
      </Row>

      {/* 物品选择器 */}
      <ItemSelector
        visible={itemSelectorVisible}
        onCancel={() => setItemSelectorVisible(false)}
        onSelect={handleItemSelect}
      />
    </div>
  );
};

export default SupplierItems; 