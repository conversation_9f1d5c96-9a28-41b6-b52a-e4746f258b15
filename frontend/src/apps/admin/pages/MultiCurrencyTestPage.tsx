import React from 'react';
import { Card, Row, Col, Typography, Divider } from 'antd';
import MultiCurrencyPriceDisplay from '@shared/components/MultiCurrencyPriceDisplay';
import MultiCurrencyTotalDisplay from '@shared/components/MultiCurrencyTotalDisplay';

const { Title, Text } = Typography;

/**
 * 多货币价格显示组件测试页面
 * 用于验证组件的各种显示状态
 */
const MultiCurrencyTestPage: React.FC = () => {
  // 模拟汇率信息
  const mockExchangeRateInfo = {
    currency_code: 'CNY',
    original_unit_price: 6.85,
    original_total_price: 68.50,
    exchange_rate: 7.2,
    usd_unit_price: 0.9514,
    usd_total_price: 9.51,
    rate_type: 'current_month' as const,
    effective_month: '2025-01-01',
    is_valid: true,
    warning: undefined,
    error: undefined
  };

  const mockHistoricalRateInfo = {
    ...mockExchangeRateInfo,
    rate_type: 'historical' as const,
    effective_month: '2024-12-01',
    warning: '使用历史汇率，建议更新当月汇率'
  };

  const mockInvalidRateInfo = {
    ...mockExchangeRateInfo,
    is_valid: false,
    error: '汇率数据无效'
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>多货币价格显示组件测试</Title>
      <Text type="secondary">
        此页面用于测试多货币价格显示组件的各种状态和显示效果
      </Text>

      <Divider />

      {/* 单价显示测试 */}
      <Card title="单价显示测试" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card size="small" title="USD单价">
              <MultiCurrencyPriceDisplay
                originalPrice={1.25}
                currencyCode="USD"
                displayType="unit"
                size="medium"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title="CNY单价 - 有效汇率">
              <MultiCurrencyPriceDisplay
                originalPrice={6.85}
                currencyCode="CNY"
                exchangeRateInfo={mockExchangeRateInfo}
                displayType="unit"
                size="medium"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title="CNY单价 - 历史汇率">
              <MultiCurrencyPriceDisplay
                originalPrice={6.85}
                currencyCode="CNY"
                exchangeRateInfo={mockHistoricalRateInfo}
                displayType="unit"
                size="medium"
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={8}>
            <Card size="small" title="CNY单价 - 无效汇率">
              <MultiCurrencyPriceDisplay
                originalPrice={6.85}
                currencyCode="CNY"
                exchangeRateInfo={mockInvalidRateInfo}
                displayType="unit"
                size="medium"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title="CNY单价 - 无汇率">
              <MultiCurrencyPriceDisplay
                originalPrice={6.85}
                currencyCode="CNY"
                displayType="unit"
                size="medium"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title="EUR单价 - 有效汇率">
              <MultiCurrencyPriceDisplay
                originalPrice={0.85}
                currencyCode="EUR"
                exchangeRateInfo={{
                  ...mockExchangeRateInfo,
                  currency_code: 'EUR',
                  original_unit_price: 0.85,
                  original_total_price: 8.50,
                  usd_unit_price: 0.1181,
                  usd_total_price: 1.18,
                  exchange_rate: 7.2
                }}
                displayType="unit"
                size="medium"
              />
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 总价显示测试 */}
      <Card title="总价显示测试" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card size="small" title="USD总价">
              <MultiCurrencyPriceDisplay
                originalPrice={125.50}
                currencyCode="USD"
                displayType="total"
                size="medium"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title="CNY总价 - 有效汇率">
              <MultiCurrencyPriceDisplay
                originalPrice={685.00}
                currencyCode="CNY"
                exchangeRateInfo={mockExchangeRateInfo}
                displayType="total"
                size="medium"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title="CNY总价 - 历史汇率">
              <MultiCurrencyPriceDisplay
                originalPrice={685.00}
                currencyCode="CNY"
                exchangeRateInfo={mockHistoricalRateInfo}
                displayType="total"
                size="medium"
              />
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 总金额显示测试 */}
      <Card title="总金额显示测试" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card size="small" title="USD总金额">
              <MultiCurrencyTotalDisplay
                totalAmount={1250.75}
                currencyCode="USD"
                size="medium"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title="CNY总金额 - 有效汇率">
              <MultiCurrencyTotalDisplay
                totalAmount={6850.00}
                currencyCode="CNY"
                exchangeRateInfo={{
                  currency_code: 'CNY',
                  exchange_rate: 7.2,
                  usd_total_price: 951.39,
                  rate_type: 'current_month',
                  effective_month: '2025-01-01',
                  is_valid: true
                }}
                size="medium"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title="CNY总金额 - 无汇率">
              <MultiCurrencyTotalDisplay
                totalAmount={6850.00}
                currencyCode="CNY"
                size="medium"
              />
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 不同尺寸测试 */}
      <Card title="不同尺寸测试" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card size="small" title="小尺寸">
              <MultiCurrencyPriceDisplay
                originalPrice={6.85}
                currencyCode="CNY"
                exchangeRateInfo={mockExchangeRateInfo}
                displayType="unit"
                size="small"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title="中等尺寸">
              <MultiCurrencyPriceDisplay
                originalPrice={6.85}
                currencyCode="CNY"
                exchangeRateInfo={mockExchangeRateInfo}
                displayType="unit"
                size="medium"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title="大尺寸">
              <MultiCurrencyPriceDisplay
                originalPrice={6.85}
                currencyCode="CNY"
                exchangeRateInfo={mockExchangeRateInfo}
                displayType="unit"
                size="large"
              />
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 配置选项测试 */}
      <Card title="配置选项测试" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card size="small" title="隐藏货币标签">
              <MultiCurrencyPriceDisplay
                originalPrice={6.85}
                currencyCode="CNY"
                exchangeRateInfo={mockExchangeRateInfo}
                displayType="unit"
                showCurrencyTag={false}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title="隐藏汇率信息">
              <MultiCurrencyPriceDisplay
                originalPrice={6.85}
                currencyCode="CNY"
                exchangeRateInfo={mockExchangeRateInfo}
                displayType="unit"
                showExchangeRate={false}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title="隐藏警告信息">
              <MultiCurrencyPriceDisplay
                originalPrice={6.85}
                currencyCode="CNY"
                exchangeRateInfo={mockHistoricalRateInfo}
                displayType="unit"
                showWarning={false}
              />
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 统计数字样式测试 */}
      <Card title="统计数字样式测试">
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Card size="small" title="普通样式">
              <MultiCurrencyTotalDisplay
                totalAmount={6850.00}
                currencyCode="CNY"
                exchangeRateInfo={{
                  currency_code: 'CNY',
                  exchange_rate: 7.2,
                  usd_total_price: 951.39,
                  rate_type: 'current_month',
                  effective_month: '2025-01-01',
                  is_valid: true
                }}
                size="large"
                isStatistic={false}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card size="small" title="统计数字样式">
              <MultiCurrencyTotalDisplay
                totalAmount={6850.00}
                currencyCode="CNY"
                exchangeRateInfo={{
                  currency_code: 'CNY',
                  exchange_rate: 7.2,
                  usd_total_price: 951.39,
                  rate_type: 'current_month',
                  effective_month: '2025-01-01',
                  is_valid: true
                }}
                size="large"
                isStatistic={true}
              />
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default MultiCurrencyTestPage;
