import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Spin, message } from 'antd';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { purchaseRequestService, PurchaseRequest, PurchaseRequestItem } from '@admin/services/purchaseRequestService';
import { apiClient } from '@admin/services/authService';
import QRCode from 'react-qr-code';
import { getStatusConfig } from '@admin/utils/statusConfig';

const PurchaseRequestExportPDF: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const [request, setRequest] = useState<PurchaseRequest | null>(null);
  const [qrContent, setQrContent] = useState<string>('');
  const [loading, setLoading] = useState(true);

  // 辅助函数：格式化价格显示
  const formatPrice = (price: string | number | undefined): string => {
    if (price === undefined || price === null) return '0.00';
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return isNaN(numPrice) ? '0.00' : numPrice.toFixed(2);
  };

  // 辅助函数：计算数量
  const calculateQuantity = (item: PurchaseRequestItem): number => {
    if (item.final_total_price && item.final_unit_price) {
      const total = parseFloat(String(item.final_total_price));
      const unit = parseFloat(String(item.final_unit_price));
      return unit > 0 ? total / unit : 0;
    } else if (item.estimated_total_price && item.estimated_unit_price) {
      const total = parseFloat(String(item.estimated_total_price));
      const unit = parseFloat(String(item.estimated_unit_price));
      return unit > 0 ? total / unit : 0;
    } else {
      const spqQty = parseFloat(String(item.spq_quantity));
      return spqQty * item.spq_count;
    }
  };

  // 组件加载时获取二维码
  useEffect(() => {
    if (id) {
      fetchRequestData();
    }
  }, [id]);

  // 自动打印功能
  useEffect(() => {
    if (!loading && request && qrContent) {
      // 延迟一点时间确保页面完全渲染，包括二维码
      const timer = setTimeout(() => {
        handleAutoPrint();
      }, 1500);
      
      return () => clearTimeout(timer);
    }
  }, [loading, request, qrContent]);

  // 自动打印处理
  const handleAutoPrint = () => {
    
    // 延迟一点时间让用户看到提示
    setTimeout(() => {
      try {
        window.print();
      } catch (error) {
        console.error('自动打印失败:', error);
        message.error(t('purchase.autoPrintFailed'));
      }
    }, 800);
  };

  const fetchRequestData = async () => {
    try {
      setLoading(true);
      
      // 获取申请详情
      const requestData = await purchaseRequestService.getPurchaseRequest(parseInt(id!));
      setRequest(requestData);
      
      // 获取二维码内容
      const qrResponse = await apiClient.get(`/purchase/requests/${id}/qr-code`);
      setQrContent(qrResponse.data.qr_content);
      
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error(t('purchase.getDataFailed'));
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ 
        textAlign: 'center', 
        padding: '50px',
        backgroundColor: 'white',
        minHeight: '100vh'
      }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>{t('purchase.loading')}</div>
      </div>
    );
  }

  if (!request) {
    return (
      <div style={{ 
        textAlign: 'center', 
        padding: '50px',
        backgroundColor: 'white',
        minHeight: '100vh'
      }}>
        <p>{t('purchase.requestNotFound')}</p>
      </div>
    );
  }

  // 计算总金额
  const totalAmount = request.items?.reduce((sum, item) => {
    const unitPrice = item.final_unit_price || item.estimated_unit_price || 0;
    const quantity = calculateQuantity(item);
    
    const numUnitPrice = typeof unitPrice === 'string' ? parseFloat(unitPrice) : unitPrice;
    const itemTotal = numUnitPrice * quantity;
    return sum + itemTotal;
  }, 0) || 0;

  return (
    <div style={{ 
      backgroundColor: 'white',
      minHeight: '100vh',
      fontFamily: 'SimSun, serif',
      fontSize: '14px',
      lineHeight: '1.6'
    }}>
      {/* 页面样式 */}
      <style>
        {`
          /* 隐藏DashboardLayout的菜单和头部 */
          .ant-layout-sider,
          .ant-layout-header,
          .ant-layout-footer {
            display: none !important;
          }
          
          .ant-layout-content {
            margin-left: 0 !important;
            padding: 0 !important;
          }
          
          @media print {
            body { margin: 0; }
            .no-print { display: none !important; }
            .page-break { page-break-before: always; }
            
            /* 隐藏页眉和页脚 */
            @page {
              margin: 0.5in;
              size: A4;
            }
            
            /* 确保内容不被页眉页脚遮挡 */
            .print-content {
              margin-top: 0;
              margin-bottom: 0;
            }
          }
          
          .print-content {
            padding: 16px;
            max-width: 800px;
            margin: 0 auto;
          }
          
          .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 12px;
            margin-bottom: 20px;
          }
          
          .title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 6px;
          }
          
          .subtitle {
            font-size: 14px;
            color: #666;
          }
          
          .info-section {
            margin-bottom: 20px;
          }
          
          .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
          }
          
          .info-item {
            display: flex;
            margin-bottom: 6px;
            font-size: 12px;
          }
          
          .info-label {
            font-weight: bold;
            min-width: 80px;
            margin-right: 8px;
            flex-shrink: 0;
          }
          
          .info-value {
            flex: 1;
            word-break: break-word;
          }
          
          .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
          }
          
          .items-table th,
          .items-table td {
            border: 1px solid #000;
            padding: 4px 6px;
            text-align: left;
          }
          
          .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            font-size: 11px;
            text-align: center;
          }
          
          .total-section {
            text-align: right;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #000;
          }
          
          .qr-section {
            text-align: center;
            margin-top: 20px;
            padding: 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
          }
          
          .footer {
            margin-top: 20px;
            text-align: center;
            color: #666;
            font-size: 10px;
          }
        `}
      </style>

      {/* 主要内容 */}
      <div className="print-content">
        {/* 页面头部 */}
        <div className="header">
          <div className="title">{t('purchase.purchaseRequestForm')}</div>
          <div className="subtitle">PURCHASE REQUEST FORM</div>
        </div>

        {/* 基本信息 */}
        <div className="info-section">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <div style={{ flex: 1 }}>
              <div className="info-grid">
                <div className="info-item">
                  <span className="info-label">申请单号:</span>
                  <span className="info-value">{request.request_no}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">申请部门:</span>
                  <span className="info-value">{request.department_name || `部门${request.department_id}`}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">{t('purchase.applicant')}:</span>
                  <span className="info-value">{request.submitter_name || `用户${request.submitter_id}`}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">{t('purchase.applicationStatus')}:</span>
                  <span className="info-value">{getStatusConfig(request.status).text}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">创建时间:</span>
                  <span className="info-value">
                    {request.created_at ? dayjs(request.created_at).format(DATE_FORMATS.DATE_TIME) : '-'}
                  </span>
                </div>
                {request.submitted_at && (
                  <div className="info-item">
                    <span className="info-label">提交时间:</span>
                    <span className="info-value">
                      {dayjs(request.submitted_at).format(DATE_FORMATS.DATE_TIME)}
                    </span>
                  </div>
                )}
              </div>
              
              {request.notes && (
                <div style={{ marginTop: '12px', fontSize: '12px' }}>
                  <span className="info-label">{t('purchase.remarks')}:</span>
                  <span className="info-value">{request.notes}</span>
                </div>
              )}
            </div>
            
            {/* 二维码区域 - 右上角 */}
            <div style={{ 
              marginLeft: '20px', 
              textAlign: 'center',
              flexShrink: 0,
              width: '120px'
            }}>
              {qrContent ? (
                <div style={{ display: 'inline-block' }}>
                  <QRCode 
                    value={qrContent} 
                    size={100} 
                    fgColor="#000000" 
                    bgColor="#ffffff"
                    level="M"
                  />
                </div>
              ) : (
                <div style={{ color: '#999', fontSize: '10px' }}>二维码加载失败</div>
              )}
            </div>
          </div>
        </div>

        {/* 物品明细 */}
        <div className="info-section">
          <h3 style={{ marginBottom: '12px', borderBottom: '1px solid #ddd', paddingBottom: '4px', fontSize: '14px' }}>
            物品明细
          </h3>
          <table className="items-table">
            <thead>
              <tr>
                <th>序号</th>
                <th>{t('purchase.itemName')}</th>
                <th>{t('purchase.itemCode')}</th>
                <th>{t('purchase.applicationQuantity')}</th>
                <th>单位</th>
                <th>{t('purchase.unitPriceLabel')}</th>
                <th>小计</th>
              </tr>
            </thead>
            <tbody>
              {request.items?.map((item, index) => (
                <tr key={item.id}>
                  <td>{index + 1}</td>
                  <td>{item.item_name || `物品${item.item_id}`}</td>
                  <td>{item.item_code}</td>
                  <td>{calculateQuantity(item).toFixed(2)}</td>
                  <td>{item.spq_unit}</td>
                  <td>¥{formatPrice(item.final_unit_price || item.estimated_unit_price)}</td>
                  <td>¥{formatPrice(item.final_total_price || item.estimated_total_price)}</td>
                </tr>
              ))}
            </tbody>
          </table>
          
          <div className="total-section">
            <div style={{ fontSize: '16px', fontWeight: 'bold' }}>
              {t('purchase.totalAmountLabel')}: ¥{totalAmount.toFixed(2)}
            </div>
          </div>
        </div>

        {/* 页脚 */}
        <div className="footer">
                          <p>本申请单由系统自动生成，打印时间: {dayjs().format(DATE_FORMATS.DATE_TIME)}</p>
          <p>请妥善保管，如有疑问请联系系统管理员</p>
        </div>
      </div>
    </div>
  );
};

export default PurchaseRequestExportPDF;
