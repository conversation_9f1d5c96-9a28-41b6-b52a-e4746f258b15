import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  Button,
  Space,
  Descriptions,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Divider,
  Alert,
  Spin,
  Breadcrumb,
  message
} from 'antd';
import {
  ArrowLeftOutlined,
  FileExcelOutlined,
  ShopOutlined,
  InboxOutlined,
  ClockCircleOutlined,
  UserOutlined,
  DollarOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import {
  purchaseExecutionService,
  ExecutionBatch,
  ExecutionItem
} from '../services/purchaseExecutionService';

const { Title, Text } = Typography;

const PurchaseExecutionBatchDetail: React.FC = () => {
  const { t } = useTranslation();
  const { batchId } = useParams<{ batchId: string }>();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(false);
  const [batch, setBatch] = useState<ExecutionBatch | null>(null);
  const [supplierSummary, setSupplierSummary] = useState<any[]>([]);

  // 加载批次详情
  const loadBatchDetail = async () => {
    if (!batchId) return;
    
    setLoading(true);
    try {
      const batchData = await purchaseExecutionService.getExecutionBatchDetail(parseInt(batchId));
      setBatch(batchData);
      
      // 计算供应商汇总
      if (batchData.execution_items) {
        const summary = calculateSupplierSummary(batchData.execution_items);
        setSupplierSummary(summary);
      }
    } catch (error) {
      message.error(t('purchase.getBatchDetailsFailed'));
      console.error('获取批次详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 计算供应商汇总
  const calculateSupplierSummary = (items: ExecutionItem[]) => {
    const summary: { [key: number]: any } = {};
    
    items.forEach(item => {
      if (!summary[item.supplier_id]) {
        summary[item.supplier_id] = {
          supplier_id: item.supplier_id,
          supplier_name: item.supplier_name,
          items_count: 0,
          total_amount: 0,
          items: []
        };
      }
      
      summary[item.supplier_id].items_count += 1;
      summary[item.supplier_id].total_amount += item.total_price;
      summary[item.supplier_id].items.push(item);
    });
    
    return Object.values(summary);
  };

  // 导出批次数据
  const handleExport = async () => {
    if (!batch) return;
    
    try {
      await purchaseExecutionService.exportExecutionData([batch.id]);
      message.success(t('purchase.exportRequested'));
    } catch (error) {
      message.error(t('purchase.exportFailed'));
      console.error('导出失败:', error);
    }
  };

  useEffect(() => {
    loadBatchDetail();
  }, [batchId]);

  if (loading || !batch) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  // 执行明细表格列
  const executionItemColumns = [
    {
      title: '物品信息',
      key: 'item_info',
      render: (record: ExecutionItem) => (
        <div>
          <div>
            <Button 
              type="link" 
              style={{ padding: 0, height: 'auto' }}
              onClick={() => navigate(`/admin/items/${record.item_id}`)}
            >
              {record.item_name}
            </Button>
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.item_code}
          </div>
        </div>
      )
    },
    {
      title: 'SPQ信息',
      key: 'spq_info',
      render: (record: ExecutionItem) => 
        `${record.spq_count} × ${typeof record.spq_quantity === 'string' ? parseFloat(record.spq_quantity) : record.spq_quantity} ${record.spq_unit}`
    },
    {
      title: '单价',
      dataIndex: 'unit_price',
      key: 'unit_price',
      render: (value: any) => {
        const numValue = typeof value === 'number' ? value : Number(value || 0);
        return `$${!isNaN(numValue) ? numValue.toFixed(4) : '0.0000'}`;
      }
    },
    {
      title: '总价',
      dataIndex: 'total_price',
      key: 'total_price',
      render: (value: any) => {
        const numValue = typeof value === 'number' ? value : Number(value || 0);
        return `$${!isNaN(numValue) ? numValue.toFixed(2) : '0.00'}`;
      }
    },
    {
      title: '供应商',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
      render: (supplierName: string, record: ExecutionItem) => (
        <Button 
          type="link" 
          style={{ padding: 0, height: 'auto' }}
          onClick={() => navigate(`/admin/suppliers/${record.supplier_id}/items/${record.item_id}`)}
        >
          {supplierName}
        </Button>
      )
    }
  ];

  // 供应商汇总表格列
  const supplierSummaryColumns = [
    {
      title: '供应商',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
    },
    {
      title: '物品数量',
      dataIndex: 'items_count',
      key: 'items_count',
    },
    {
      title: '总金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (value: any) => {
        const numValue = typeof value === 'number' ? value : Number(value || 0);
        return `$${!isNaN(numValue) ? numValue.toFixed(2) : '0.00'}`;
      }
    },
    {
      title: '占比',
      key: 'percentage',
      render: (record: any) => {
        const totalAmount = typeof batch.total_amount === 'number' ? batch.total_amount : Number(batch.total_amount || 0);
        const recordAmount = typeof record.total_amount === 'number' ? record.total_amount : Number(record.total_amount || 0);
        if (totalAmount > 0 && !isNaN(totalAmount) && !isNaN(recordAmount)) {
          const percentage = (recordAmount / totalAmount * 100).toFixed(1);
          return `${percentage}%`;
        }
        return '0.0%';
      }
    }
  ];

  return (
    <div className="purchase-execution-batch-detail">
      {/* 面包屑导航 */}
      <Breadcrumb style={{ marginBottom: 16 }}>
        <Breadcrumb.Item>
          <Button 
            type="link" 
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/admin/purchase-execution')}
          >
            {t('purchase.returnToExecutionList')}
          </Button>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{t('purchase.executionBatchDetails')}</Breadcrumb.Item>
        <Breadcrumb.Item>{batch.batch_no}</Breadcrumb.Item>
      </Breadcrumb>

      {/* 批次基本信息 */}
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={3}>
            {t('purchase.executionBatchDetails')}
          </Title>
          <Space>
            <Button 
              type="primary" 
              icon={<FileExcelOutlined />}
              onClick={handleExport}
            >
              {t('purchase.exportData')}
            </Button>
          </Space>
        </div>

        <Descriptions column={3} bordered>
          <Descriptions.Item label="批次编号">
            <Text strong>{batch.batch_no}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="批次名称">
            {batch.batch_name}
          </Descriptions.Item>
          <Descriptions.Item label="执行状态">
            <Tag color="green">
              <ClockCircleOutlined /> {batch.status === 'active' ? '已执行' : batch.status}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="执行人">
            <UserOutlined /> {batch.executor_name}
          </Descriptions.Item>
          <Descriptions.Item label="执行时间">
            <ClockCircleOutlined /> {dayjs(batch.executed_at).format(DATE_FORMATS.DATE_TIME)}
          </Descriptions.Item>
                      <Descriptions.Item label="申请数量">
              <InboxOutlined /> {batch.request_count} 个申请
            </Descriptions.Item>
          <Descriptions.Item label="总金额" span={2}>
            <Text strong style={{ fontSize: '16px', color: '#1890ff' }}>
              <DollarOutlined /> ${typeof batch.total_amount === 'number' && !isNaN(batch.total_amount) ? batch.total_amount.toFixed(2) : '0.00'}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="备注说明" span={3}>
            {batch.notes || '无'}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 统计概览 */}
      <Card title="执行统计" style={{ marginTop: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="申请数量"
              value={batch.request_count}
              prefix={<InboxOutlined />}
              suffix="piece"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="物品种类"
              value={batch.execution_items?.length || 0}
              prefix={<ShopOutlined />}
              suffix="种"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="供应商数量"
              value={supplierSummary.length}
              prefix={<UserOutlined />}
              suffix="piece"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="总金额"
              value={batch.total_amount}
              prefix={<DollarOutlined />}
              precision={2}
            />
          </Col>
        </Row>
      </Card>

      {/* 供应商汇总 */}
      {supplierSummary.length > 0 && (
        <Card title="供应商汇总" style={{ marginTop: 16 }}>
          <Table
            columns={supplierSummaryColumns}
            dataSource={supplierSummary}
            rowKey="supplier_id"
            pagination={false}
            summary={() => (
              <Table.Summary fixed>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0}>
                    <Text strong>总计</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={1}>
                    <Text strong>{batch.execution_items?.length || 0}</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={2}>
                    <Text strong>${typeof batch.total_amount === 'number' && !isNaN(batch.total_amount) ? batch.total_amount.toFixed(2) : '0.00'}</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={3}>
                    <Text strong>100%</Text>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            )}
          />
        </Card>
      )}

      {/* 执行明细 */}
      {batch.execution_items && (
        <Card title="执行明细" style={{ marginTop: 16 }}>
          <Table
            columns={executionItemColumns}
            dataSource={batch.execution_items}
            rowKey="id"
            pagination={false}
            summary={() => (
              <Table.Summary fixed>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={2}>
                    <Text strong>总计</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={2}>
                    <Text strong>${typeof batch.total_amount === 'number' && !isNaN(batch.total_amount) ? batch.total_amount.toFixed(2) : '0.00'}</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={3}>
                    <Text strong>{batch.execution_items?.length || 0} piece</Text>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            )}
          />
        </Card>
      )}

      {/* 关联申请列表 */}
      {batch.execution_items && (
        <Card title="关联申请列表" style={{ marginTop: 16 }}>
          <Table
            columns={[
              {
                title: '申请单号',
                dataIndex: 'request_id',
                key: 'request_id',
                render: (requestId: number) => (
                  <Button 
                    type="link" 
                    onClick={() => navigate(`/admin/purchase-requests/${requestId}`)}
                  >
                    {`PR${requestId.toString().padStart(6, '0')}`}
                  </Button>
                )
              },
              {
                title: '物品数量',
                key: 'items_count',
                render: (record: ExecutionItem) => {
                  const itemsInRequest = batch.execution_items?.filter(item => item.request_id === record.request_id) || [];
                  return itemsInRequest.length;
                }
              },
              {
                title: '申请金额',
                key: 'request_amount',
                render: (record: ExecutionItem) => {
                  const itemsInRequest = batch.execution_items?.filter(item => item.request_id === record.request_id) || [];
                  const totalAmount = itemsInRequest.reduce((sum, item) => {
                    const amount = typeof item.total_price === 'number' ? item.total_price : Number(item.total_price || 0);
                    return sum + (isNaN(amount) ? 0 : amount);
                  }, 0);
                  return `$${totalAmount.toFixed(2)}`;
                }
              }
            ]}
            dataSource={Array.from(new Set(batch.execution_items.map(item => item.request_id))).map(requestId => ({
              request_id: requestId,
              key: requestId
            }))}
            rowKey="request_id"
            pagination={false}
            size="small"
          />
        </Card>
      )}
    </div>
  );
};

export default PurchaseExecutionBatchDetail;
