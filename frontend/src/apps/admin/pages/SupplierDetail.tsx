import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import {
  Row,
  Col,
  Descriptions,
  Tag,
  Button,
  Space,
  message,
  Typography,
  Spin,
  Rate,
  Popconfirm,
  Modal
} from 'antd';
import {
  EditOutlined,
  ArrowLeftOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  supplierService, 
  Supplier
} from '@admin/services/supplierService';
import PermissionGuard from '@shared/components/PermissionGuard';
import { PERMISSIONS } from '@shared/config/permissions';

const { Title, Text } = Typography;

interface SupplierDetailProps {}

const SupplierDetail: React.FC<SupplierDetailProps> = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [loading, setLoading] = useState(false);

  // 获取供应商详情
  const fetchSupplier = useCallback(async () => {
    if (!id) return;
    setLoading(true);
    try {
      const supplierData = await supplierService.getSupplier(parseInt(id));
      setSupplier(supplierData);
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('messages.getSupplierDetailFailed'));
    } finally {
      setLoading(false);
    }
  }, [id, t]);

  useEffect(() => {
    fetchSupplier();
  }, [fetchSupplier]);

  // 处理删除供应商
  const handleDelete = async () => {
    try {
      console.log(t('messages.startDeleteSupplier'));
      const result = await supplierService.deleteSupplier(parseInt(id!));
      console.log(t('messages.deleteApiResponse'), result);
      
      // 检查是否有关联物品
      if (result.has_related_items) {
        console.log(t('messages.foundRelatedItems'));
        // 显示确认对话框
        const confirmed = await new Promise<boolean>((resolve) => {
          const itemList = result.item_details.map((item: any) => 
            `${item.name} (${item.code})`
          ).join('、');
          
          const content = (
            <div>
              <p>{result.message}</p>
              <p>{t('messages.relatedItems')}: {itemList}</p>
              {result.item_count > 3 && (
                <p>{t('messages.totalItemsCount', { count: result.item_count })}</p>
              )}
            </div>
          );
          
          // 使用Modal.confirm来显示确认对话框
          Modal.confirm({
            title: t('messages.confirmDelete'),
            content: content,
            okText: t('messages.confirmDelete'),
            cancelText: t('messages.cancel'),
            onOk: () => resolve(true),
            onCancel: () => resolve(false),
          });
        });
        
        if (confirmed) {
          console.log(t('messages.userConfirmedDelete'));
          // 强制删除
          await supplierService.deleteSupplier(parseInt(id!), true);
          message.success(t('messages.deleteSuccess'));
          navigate('/admin/suppliers');
        } else {
          console.log(t('messages.userCancelledDelete'));
        }
      } else {
        // 无关联物品，直接删除
        message.success(t('messages.deleteSuccess'));
        navigate('/admin/suppliers');
      }
    } catch (error: any) {
      console.error(t('messages.deleteSupplierFailed'), error);
      message.error(error.response?.data?.detail || t('messages.deleteSupplierFailed'));
    }
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>{t('messages.loading')}</div>
      </div>
    );
  }

  if (!supplier) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Text type="secondary">{t('messages.supplierNotFound')}</Text>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '16px',
        padding: '8px 0'
      }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate('/admin/suppliers')}
        >
          {t('messages.backToList')}
        </Button>
        
        <div style={{ flex: 1, textAlign: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>
            {supplier.name_en || supplier.name_cn || t('messages.unnamedSupplier')}
          </Title>
        </div>
        
        <Space>
          <PermissionGuard permission={PERMISSIONS.SUPPLIER.UPDATE}>
            <Button 
              type="primary" 
              icon={<EditOutlined />} 
              onClick={() => navigate(`/admin/suppliers/${id}/edit`)}
            >
              {t('messages.edit')}
            </Button>
          </PermissionGuard>
          <PermissionGuard permission={PERMISSIONS.SUPPLIER.DELETE}>
            <Button 
              danger 
              icon={<DeleteOutlined />}
              onClick={handleDelete}
            >
              {t('messages.delete')}
            </Button>
          </PermissionGuard>
        </Space>
      </div>

      <Descriptions title={t('messages.supplierInfo')} bordered column={2}>
        <Descriptions.Item label={t('messages.supplierNameCn')}>
          {supplier.name_cn || '-'}
        </Descriptions.Item>
        <Descriptions.Item label={t('messages.supplierNameEn')}>
          {supplier.name_en || '-'}
        </Descriptions.Item>
        <Descriptions.Item label={t('messages.supplierCode')}>
          {supplier.code}
        </Descriptions.Item>
        <Descriptions.Item label={t('messages.status')}>
          <Tag color={supplier.status === 'active' ? 'green' : 'orange'}>
            {supplier.status === 'active' ? t('messages.active') : t('messages.inactive')}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label={t('messages.contactPerson')}>
          {supplier.contact_person || '-'}
        </Descriptions.Item>
        <Descriptions.Item label={t('messages.phone')}>
          {supplier.phone || '-'}
        </Descriptions.Item>
        <Descriptions.Item label={t('messages.email')}>
          {supplier.email || '-'}
        </Descriptions.Item>
        <Descriptions.Item label={t('messages.rating')}>
          <Rate disabled defaultValue={supplier.rating} />
          <span style={{ marginLeft: 8 }}>{supplier.rating}/5</span>
        </Descriptions.Item>
        <Descriptions.Item label={t('messages.companyAddress')} span={2}>
          {supplier.company_address || '-'}
        </Descriptions.Item>
        <Descriptions.Item label={t('messages.createdAt')}>
                        {dayjs(supplier.created_at).format(DATE_FORMATS.DATE_TIME)}
        </Descriptions.Item>
        <Descriptions.Item label={t('messages.updatedAt')}>
                      {supplier.updated_at ? dayjs(supplier.updated_at).format(DATE_FORMATS.DATE_TIME) : '-'}
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

export default SupplierDetail; 