import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Typography, Tag, Space, Button, Spin, message, Descriptions, Divider } from 'antd';
import { 
  ArrowLeftOutlined, 
  CheckOutlined, 
  DeleteOutlined, 
  BellOutlined,
  LinkOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { notificationService } from '../services/notificationService';
import { Notification } from '../types/notification';
import { formatDate } from '@shared/utils/dateUtils';

const { Title, Text, Paragraph } = Typography;

const NotificationDetail: React.FC = () => {
  const { t } = useTranslation();
  const { i18n } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [notification, setNotification] = useState<Notification | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  // 加载通知详情
  useEffect(() => {
    if (id) {
      loadNotification(parseInt(id));
    }
  }, [id]);

  const loadNotification = async (notificationId: number) => {
    try {
      setLoading(true);
      const data = await notificationService.getNotification(notificationId);
      setNotification(data);
      
      // 如果通知未读，自动标记为已读
      if (data.status === 'unread') {
        await markAsRead(notificationId);
      }
    } catch (error) {
      console.error('Failed to load notification:', error);
      message.error(t('notifications.load_detail_error'));
      navigate('/admin/notifications');
    } finally {
      setLoading(false);
    }
  };

  // 标记已读
  const markAsRead = async (notificationId: number) => {
    try {
      await notificationService.markNotificationRead(notificationId);
      if (notification) {
        setNotification({
          ...notification,
          status: 'read',
          read_at: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  // 删除通知
  const handleDelete = async () => {
    if (!notification) return;
    
    try {
      setActionLoading(true);
      await notificationService.deleteNotification(notification.id);
      message.success(t('notifications.delete_success'));
      navigate('/admin/notifications');
    } catch (error) {
      console.error('Failed to delete notification:', error);
      message.error(t('notifications.delete_error'));
    } finally {
      setActionLoading(false);
    }
  };

  // 返回列表
  const handleBack = () => {
    navigate('/admin/notifications');
  };

  // 跳转到相关业务页面
  const handleActionClick = () => {
    if (notification?.action_url) {
      window.open(notification.action_url, '_blank');
    }
  };

  // 获取通知类型颜色
  const getNotificationTypeColor = (type: string) => {
    switch (type) {
      case 'inventory_alert':
        return 'red';
      case 'approval_flow':
        return 'blue';
      case 'system':
        return 'green';
      default:
        return 'default';
    }
  };

  // 获取通知类型文本
  const getNotificationTypeText = (type: string) => {
    switch (type) {
      case 'inventory_alert':
        return t('notifications.type.inventory_alert');
      case 'approval_flow':
        return t('notifications.type.approval_flow');
      case 'system':
        return t('notifications.type.system');
      default:
        return t('notifications.type.other');
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    return status === 'unread' ? 'blue' : 'default';
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    return status === 'unread' ? t('notifications.status.unread') : t('notifications.status.read');
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>{t('common.loading')}</div>
      </div>
    );
  }

  if (!notification) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Text type="secondary">{t('notifications.not_found')}</Text>
      </div>
    );
  }

  return (
    <div>
      {/* 页面头部 */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={handleBack}
              type="link"
            >
              {t('common.back')}
            </Button>
            <BellOutlined style={{ fontSize: 20, color: '#1890ff' }} />
            <Title level={3} style={{ margin: 0 }}>
              {t('notifications.detail.title')}
            </Title>
          </Space>
          
          <Space>
            {notification.action_url && (
              <Button
                type="primary"
                icon={<LinkOutlined />}
                onClick={handleActionClick}
              >
                {t('notifications.detail.view_related')}
              </Button>
            )}
            
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleDelete}
              loading={actionLoading}
            >
              {t('notifications.detail.delete')}
            </Button>
          </Space>
        </div>
      </Card>

      {/* 通知内容 */}
      <Card>
        <div style={{ marginBottom: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 16 }}>
            <Title level={4} style={{ margin: 0, flex: 1 }}>
              {notification.title}
            </Title>
            <Space size={8}>
              <Tag color={getNotificationTypeColor(notification.notification_type)}>
                {getNotificationTypeText(notification.notification_type)}
              </Tag>
              <Tag color={getStatusColor(notification.status)}>
                {getStatusText(notification.status)}
              </Tag>
            </Space>
          </div>
          
          <Paragraph style={{ fontSize: 16, lineHeight: 1.6, marginBottom: 24 }}>
            {notification.content}
          </Paragraph>
        </div>

        <Divider />

        {/* 通知信息 */}
        <Descriptions title={t('notifications.detail.info')} column={2} bordered>
          <Descriptions.Item label={t('notifications.detail.id')}>
            {notification.id}
          </Descriptions.Item>
          <Descriptions.Item label={t('notifications.detail.type')}>
            {getNotificationTypeText(notification.notification_type)}
          </Descriptions.Item>
          <Descriptions.Item label={t('notifications.detail.status')}>
            <Tag color={getStatusColor(notification.status)}>
              {getStatusText(notification.status)}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label={t('notifications.detail.created_at')}>
            {formatDate(notification.created_at, i18n.language === 'zh' ? 'DD-MM-YYYY HH:mm:ss' : 'MM-DD-YYYY HH:mm:ss')}
          </Descriptions.Item>
          {notification.read_at && (
            <Descriptions.Item label={t('notifications.detail.read_at')}>
              {formatDate(notification.read_at, i18n.language === 'zh' ? 'DD-MM-YYYY HH:mm:ss' : 'MM-DD-YYYY HH:mm:ss')}
            </Descriptions.Item>
          )}
          {notification.action_url && (
            <Descriptions.Item label={t('notifications.detail.action_url')} span={2}>
              <a href={notification.action_url} target="_blank" rel="noopener noreferrer">
                {notification.action_url}
              </a>
            </Descriptions.Item>
          )}
        </Descriptions>

        {/* 业务数据 */}
        {notification.business_data && Object.keys(notification.business_data).length > 0 && (
          <>
            <Divider />
            <div>
              <Title level={5}>{t('notifications.detail.business_data')}</Title>
              <pre style={{ 
                backgroundColor: '#f5f5f5', 
                padding: 16, 
                borderRadius: 6,
                overflow: 'auto',
                fontSize: 12
              }}>
                {JSON.stringify(notification.business_data, null, 2)}
              </pre>
            </div>
          </>
        )}
      </Card>
    </div>
  );
};

export default NotificationDetail;
