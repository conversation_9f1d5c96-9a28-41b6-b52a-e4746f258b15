import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  Space,
  Descriptions,
  Tag,
  Table,
  message,
  Spin,
  Row,
  Col,
  Statistic,
  Tabs,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { departmentService, Department } from '@admin/services/departmentService';
import { userService, User } from '@admin/services/userService';
import { handleApiError } from '@shared/utils/errorHandler';

interface DepartmentDetailProps {}

const DepartmentDetail: React.FC<DepartmentDetailProps> = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [department, setDepartment] = useState<Department | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [usersLoading, setUsersLoading] = useState(false);

  // 获取部门详情
  const fetchDepartmentDetail = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const response = await departmentService.getDepartment(parseInt(id));
      setDepartment(response);
    } catch (error: any) {
      message.error(handleApiError(error, t('messages.getDepartmentFailed')));
    } finally {
      setLoading(false);
    }
  };

  // 获取部门用户列表
  const fetchDepartmentUsers = async () => {
    if (!id) return;
    
    try {
      setUsersLoading(true);
      const response = await userService.getUsers({
        department_id: parseInt(id),
        page_size: 100, // 获取所有用户
      });
      setUsers(response.items);
    } catch (error: any) {
      message.error(handleApiError(error, t('messages.getDepartmentUsersFailed')));
    } finally {
      setUsersLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchDepartmentDetail();
      fetchDepartmentUsers();
    }
  }, [id]);

  const handleEdit = () => {
    navigate(`/admin/departments/${id}/edit`);
  };

  const handleBack = () => {
    navigate(-1);
  };

  const { TabPane } = Tabs;

  // 用户表格列定义
  const userColumns: ColumnsType<User> = [
    {
      title: t('messages.username'),
      dataIndex: 'username',
      key: 'username',
      width: 120,
    },
    {
      title: t('messages.fullName'),
      dataIndex: 'full_name',
      key: 'full_name',
      width: 100,
    },
    {
      title: t('messages.email'),
      dataIndex: 'email',
      key: 'email',
      width: 180,
      render: (email: string) => (
        <Button 
          type="link" 
          onClick={() => window.open(`mailto:${email}`, '_blank')}
          style={{ padding: 0, height: 'auto' }}
        >
          {email}
        </Button>
      ),
    },
    {
      title: t('messages.employeeId'),
      dataIndex: 'employee_id',
      key: 'employee_id',
      width: 100,
    },
    {
      title: t('messages.status'),
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (is_active: boolean) => (
        <Tag color={is_active ? 'green' : 'red'}>
          {is_active ? t('messages.normal') : t('messages.disabled')}
        </Tag>
      ),
    },
    {
      title: t('messages.lastLogin'),
      dataIndex: 'last_login_at',
      key: 'last_login_at',
      width: 150,
      render: (time) => time ? dayjs(time).format(DATE_FORMATS.DATE_TIME) : '-',
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!department) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <p>{t('messages.departmentNotFound')}</p>
        <Button onClick={handleBack}>{t('messages.back')}</Button>
      </div>
    );
  }

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
              {t('messages.back')}
            </Button>
            <Button 
              type="primary" 
              icon={<EditOutlined />} 
              onClick={handleEdit}
            >
              {t('messages.editDepartment')}
            </Button>
          </Space>
        </div>

        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Statistic
              title={t('messages.departmentUserCount')}
              value={users.length}
              prefix={<TeamOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('messages.status')}
              value={department.is_active ? t('messages.normal') : t('messages.disabled')}
              valueStyle={{ color: department.is_active ? '#3f8600' : '#cf1322' }}
            />
          </Col>
        </Row>

        <Tabs defaultActiveKey="1">
          <TabPane tab={t('messages.basicInfo')} key="1">
            <Descriptions bordered column={2}>
              <Descriptions.Item label={t('messages.departmentName')}>{department.name}</Descriptions.Item>
              <Descriptions.Item label={t('messages.departmentCode')}>{department.code}</Descriptions.Item>
              <Descriptions.Item label={t('messages.status')}>
                <Tag color={department.is_active ? 'green' : 'red'}>
                  {department.is_active ? t('messages.normal') : t('messages.disabled')}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label={t('messages.createdAt')}>
                {dayjs(department.created_at).format(DATE_FORMATS.DATE_TIME)}
              </Descriptions.Item>
              <Descriptions.Item label={t('messages.description')} span={2}>
                {department.description || '-'}
              </Descriptions.Item>
            </Descriptions>
          </TabPane>

          <TabPane tab={t('messages.userList')} key="2">
            <Table
              columns={userColumns}
              dataSource={users}
              rowKey="id"
              loading={usersLoading}
              pagination={false}
              scroll={{ x: 800 }}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default DepartmentDetail;
