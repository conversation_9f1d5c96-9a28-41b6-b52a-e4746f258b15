import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  DatePicker,
  Select,
  message,
  Popconfirm,
  Tag,
  Row,
  Col,
  Statistic,
  Divider,
  Typography,
  Alert
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { exchangeRateService, ExchangeRate } from '@admin/services/exchangeRateService';
import { formatPriceDisplay, convertUsdToCurrency, convertPriceToUsd } from '@shared/utils/exchangeRateUtils';

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;

interface ExchangeRateFormData {
  currency_code: string;
  rate: number;
  effective_month: string;
  status: string;
}

const ExchangeRateManagement: React.FC = () => {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const [exchangeRates, setExchangeRates] = useState<ExchangeRate[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  
  // 表单相关状态
  const [form] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingRate, setEditingRate] = useState<ExchangeRate | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  
  // 汇率示例相关状态
  const [currentRate, setCurrentRate] = useState<number>(0);
  const [selectedCurrency, setSelectedCurrency] = useState<string>('');
  
  // 筛选条件
  const [currencyFilter, setCurrencyFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [monthFilter, setMonthFilter] = useState<string>('');

  // 支持的货币列表
  const supportedCurrencies = [
    { code: 'USD', name: t('exchangeRate.currencies.USD'), symbol: '$' },
    { code: 'CNY', name: t('exchangeRate.currencies.CNY'), symbol: '¥' },
    { code: 'EUR', name: t('exchangeRate.currencies.EUR'), symbol: '€' },
    { code: 'JPY', name: t('exchangeRate.currencies.JPY'), symbol: '¥' },
    { code: 'GBP', name: t('exchangeRate.currencies.GBP'), symbol: '£' },
    { code: 'KRW', name: t('exchangeRate.currencies.KRW'), symbol: '₩' },
    { code: 'SGD', name: t('exchangeRate.currencies.SGD'), symbol: 'S$' },
    { code: 'HKD', name: t('exchangeRate.currencies.HKD'), symbol: 'HK$' },
  ];

  // 从URL参数初始化状态
  useEffect(() => {
    const page = parseInt(searchParams.get('page') || '1');
    const size = parseInt(searchParams.get('size') || '20');
    const currency = searchParams.get('currency') || '';
    const status = searchParams.get('status') || '';
    const month = searchParams.get('month') || '';

    setCurrentPage(page);
    setPageSize(size);
    setCurrencyFilter(currency);
    setStatusFilter(status);
    setMonthFilter(month);
  }, [searchParams]);

  // 加载汇率数据
  const loadExchangeRates = async () => {
    setLoading(true);
    try {
      const params = {
        page: currentPage,
        size: pageSize,
        currency_code: currencyFilter || undefined,
        status: statusFilter || undefined,
        effective_month: monthFilter || undefined,
      };

      const response = await exchangeRateService.getExchangeRates(params);
      console.log('收到响应:', response);
      
      // 修复：axios响应结构是 response.data，而后端数据在 response.data.data
      // 由于axios会自动包装响应，所以实际数据在response.data中
      if (response && response.data && Array.isArray(response.data)) {
        console.log('设置汇率数据:', response.data);
        setExchangeRates(response.data);
        setTotal(response.total || 0);
      } else if (response && response.data && response.data.data && Array.isArray(response.data.data)) {
        // 如果数据在response.data.data中（双重包装的情况）
        console.log('设置汇率数据（双重包装）:', response.data.data);
        setExchangeRates(response.data.data);
        setTotal(response.data.total || 0);
      } else {
        console.log('响应数据格式不正确:', response);
        console.log('response存在:', !!response);
        console.log('response.data存在:', !!(response && response.data));
        console.log('response.data是数组:', Array.isArray(response?.data));
        console.log('response.data.data存在:', !!(response && response.data && response.data.data));
        console.log('response.data.data是数组:', Array.isArray(response?.data?.data));
        setExchangeRates([]);
        setTotal(0);
      }
    } catch (error) {
      message.error(t('exchangeRate.loadingExchangeRatesFailed'));
      console.error('加载汇率数据失败:', error);
      setExchangeRates([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 更新URL参数
  const updateURLParams = (updates: Record<string, string | number>) => {
    const newParams = new URLSearchParams(searchParams);
    Object.entries(updates).forEach(([key, value]) => {
      if (value) {
        newParams.set(key, String(value));
      } else {
        newParams.delete(key);
      }
    });
    setSearchParams(newParams);
  };

  // 处理分页变化
  const handlePageChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
    updateURLParams({ page, size });
  };

  // 处理筛选变化
  const handleFilterChange = (type: string, value: string) => {
    switch (type) {
      case 'currency':
        setCurrencyFilter(value);
        updateURLParams({ currency: value, page: 1 });
        break;
      case 'status':
        setStatusFilter(value);
        updateURLParams({ status: value, page: 1 });
        break;
      case 'month':
        setMonthFilter(value);
        updateURLParams({ month: value, page: 1 });
        break;
    }
  };

  // 重置筛选
  const resetFilters = () => {
    setCurrencyFilter('');
    setStatusFilter('');
    setMonthFilter('');
    setCurrentPage(1);
    updateURLParams({ currency: '', status: '', month: '', page: 1 });
  };

  // 打开创建模态框
  const showCreateModal = () => {
    setIsEditMode(false);
    setEditingRate(null);
    setCurrentRate(0);
    setSelectedCurrency('');
    form.resetFields();
    form.setFieldsValue({
      status: 'active',
      effective_month: dayjs().startOf('month'),
    });
    setIsModalVisible(true);
  };

  // 打开编辑模态框
  const showEditModal = (rate: ExchangeRate) => {
    setIsEditMode(true);
    setEditingRate(rate);
    setCurrentRate(parseFloat(rate.rate));
    setSelectedCurrency(rate.currency_code);
    form.setFieldsValue({
      currency_code: rate.currency_code,
      rate: parseFloat(rate.rate), // 将string类型的rate转换为number
      effective_month: dayjs(rate.effective_month),
      status: rate.status,
    });
    setIsModalVisible(true);
  };

  // 处理货币选择变化
  const handleCurrencyChange = (value: string) => {
    setSelectedCurrency(value);
  };

  // 处理汇率值变化
  const handleRateChange = (value: string) => {
    const rate = parseFloat(value) || 0;
    setCurrentRate(rate);
  };

  // 关闭模态框
  const handleCancel = () => {
    setIsModalVisible(false);
    setEditingRate(null);
    setCurrentRate(0);
    setSelectedCurrency('');
    form.resetFields();
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const formData: ExchangeRateFormData = {
        ...values,
        effective_month: values.effective_month.format('YYYY-MM-DD'),
      };

      if (isEditMode && editingRate) {
        // 编辑模式：从表单获取修改原因
        await exchangeRateService.updateExchangeRate(editingRate.id, {
          ...formData,
          change_reason: values.change_reason,
        });
        message.success(t('exchangeRate.exchangeRateUpdated'));
      } else {
        // 创建模式
        await exchangeRateService.createExchangeRate(formData);
        message.success(t('exchangeRate.exchangeRateCreated'));
      }

      handleCancel();
      loadExchangeRates();
    } catch (error) {
      message.error(t('exchangeRate.operationFailed'));
      console.error('操作失败:', error);
    }
  };

  // 删除汇率
  const handleDelete = async (id: number) => {
    try {
      await exchangeRateService.deleteExchangeRate(id);
      message.success(t('exchangeRate.deleteSuccess'));
      loadExchangeRates();
    } catch (error) {
      message.error(t('exchangeRate.deleteFailed'));
      console.error('删除失败:', error);
    }
  };

  // 生成汇率转换示例
  const generateExchangeExamples = (currencyCode: string, rate: number) => {
    if (currencyCode === 'USD' || !rate || rate <= 0) {
      return null;
    }

    const currency = supportedCurrencies.find(c => c.code === currencyCode);
    if (!currency) return null;

    // 计算 1 单位外币等于多少美元
    const usdAmount = convertPriceToUsd(1, currencyCode as any, rate);

    return (
      <div style={{ marginTop: '12px', padding: '8px 12px', background: '#f0f9ff', borderRadius: '6px', border: '1px solid #bae6fd' }}>
        <Text style={{ fontSize: '12px', color: '#0369a1' }}>
          💱 {t('exchangeRate.conversionExample')}: 1 {currency.symbol}{currencyCode} → ${usdAmount.toFixed(4)} USD
        </Text>
      </div>
    );
  };

  // 查看详情
  const showDetail = (rate: ExchangeRate) => {
    Modal.info({
      title: t('exchangeRate.exchangeRateDetails'),
      width: 600,
      content: (
        <div>
          <Row gutter={16}>
            <Col span={12}>
              <p><strong>货币代码:</strong> {rate.currency_code}</p>
              <p><strong>汇率值:</strong> {rate.rate}</p>
              <p><strong>生效月份:</strong> {rate.effective_month}</p>
            </Col>
            <Col span={12}>
              <p><strong>状态:</strong> <Tag color={rate.status === 'active' ? 'green' : 'red'}>{rate.status}</Tag></p>
              <p><strong>创建时间:</strong> {dayjs(rate.created_at).format('YYYY-MM-DD HH:mm:ss')}</p>
              {rate.updated_at && (
                <p><strong>更新时间:</strong> {dayjs(rate.updated_at).format('YYYY-MM-DD HH:mm:ss')}</p>
              )}
            </Col>
          </Row>
        </div>
      ),
    });
  };

  // 表格列定义
  const columns = [
    {
      title: t('exchangeRate.currencyCode'),
      dataIndex: 'currency_code',
      key: 'currency_code',
      render: (code: string) => {
        const currency = supportedCurrencies.find(c => c.code === code);
        return (
          <Space>
            <span>{currency?.symbol}</span>
            <span>{code}</span>
            <span>({currency?.name})</span>
          </Space>
        );
      },
    },
    {
      title: t('exchangeRate.rateValue'),
      dataIndex: 'rate',
      key: 'rate',
      render: (rate: string) => `1 USD = ${rate} 外币`,
    },
    {
      title: t('exchangeRate.effectiveDate'),
      dataIndex: 'effective_month',
      key: 'effective_month',
      render: (month: string) => dayjs(month).format('YYYY年MM月'),
    },
    {
      title: t('exchangeRate.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? t('exchangeRate.active') : t('exchangeRate.inactive')}
        </Tag>
      ),
    },
    {
      title: t('exchangeRate.createdAt'),
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: t('exchangeRate.actions'),
      key: 'action',
      render: (_: any, record: ExchangeRate) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => showDetail(record)}
          >
            {t('exchangeRate.view')}
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => showEditModal(record)}
          >
            {t('exchangeRate.edit')}
          </Button>
          <Popconfirm
            title={t('exchangeRate.confirmDeleteRate')}
            onConfirm={() => handleDelete(record.id)}
            okText={t('exchangeRate.confirm')}
            cancelText={t('exchangeRate.cancel')}
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              {t('exchangeRate.delete')}
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];



  // 加载数据
  useEffect(() => {
    loadExchangeRates();
  }, [currentPage, pageSize, currencyFilter, statusFilter, monthFilter]);

  return (
    <div className="exchange-rate-management">
      <Card title={t('exchangeRate.exchangeRateManagement')} extra={
        <Button type="primary" icon={<PlusOutlined />} onClick={showCreateModal}>
          {t('exchangeRate.newExchangeRate')}
        </Button>
      }>
        {/* 筛选区域 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col span={6}>
              <Select
                placeholder={t('exchangeRate.selectCurrency')}
                value={currencyFilter}
                onChange={(value) => handleFilterChange('currency', value)}
                allowClear
                style={{ width: '100%' }}
              >
                {supportedCurrencies.map(currency => (
                  <Option key={currency.code} value={currency.code}>
                    {currency.symbol} {currency.code} ({currency.name})
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Select
                placeholder={t('exchangeRate.selectStatus')}
                value={statusFilter}
                onChange={(value) => handleFilterChange('status', value)}
                allowClear
                style={{ width: '100%' }}
              >
                <Option value="active">{t('exchangeRate.active')}</Option>
                <Option value="inactive">{t('exchangeRate.inactive')}</Option>
              </Select>
            </Col>
            <Col span={6}>
              <DatePicker
                placeholder={t('exchangeRate.selectMonth')}
                value={monthFilter ? dayjs(monthFilter) : null}
                onChange={(date) => handleFilterChange('month', date ? date.format('YYYY-MM-DD') : '')}
                picker="month"
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={6}>
              <Space>
                <Button onClick={resetFilters}>{t('exchangeRate.reset')}</Button>
                <Button type="primary" onClick={loadExchangeRates}>
                  {t('exchangeRate.search')}
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 统计信息 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Statistic title={t('exchangeRate.totalRecords')} value={total} />
          </Col>
          <Col span={6}>
            <Statistic title={t('exchangeRate.activeRates')} value={exchangeRates.filter(r => r.status === 'active').length} />
          </Col>
          <Col span={6}>
            <Statistic title={t('exchangeRate.supportedCurrencies')} value={supportedCurrencies.length} />
          </Col>
        </Row>

        {/* 汇率列表 */}
        <Table
          columns={columns}
          dataSource={exchangeRates}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: handlePageChange,
            onShowSizeChange: handlePageChange,
          }}
        />
      </Card>

      {/* 创建/编辑模态框 */}
      <Modal
        title={isEditMode ? t('exchangeRate.editExchangeRate') : t('exchangeRate.newExchangeRate')}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        width={600}
        okText={isEditMode ? t('exchangeRate.update') : t('exchangeRate.create')}
        cancelText={t('exchangeRate.cancel')}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'active',
            effective_month: dayjs().startOf('month'),
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="currency_code"
                label={t('exchangeRate.currencyCode')}
                rules={[{ required: true, message: t('exchangeRate.selectCurrencyCode') }]}
              >
                <Select placeholder={t('exchangeRate.selectCurrency')} onChange={handleCurrencyChange}>
                  {supportedCurrencies.map(currency => (
                    <Option key={currency.code} value={currency.code}>
                      {currency.symbol} {currency.code} ({currency.name})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="rate"
                label={t('exchangeRate.rateValue')}
                rules={[
                  { required: true, message: t('exchangeRate.enterRateValue') },
                  { type: 'number', min: 0.000001, message: t('exchangeRate.rateValueMustBePositive') },
                ]}
              >
                <InputNumber
                  placeholder={t('exchangeRate.rateInputPlaceholder')}
                  step={0.000001}
                  min={0.000001}
                  precision={6}
                  style={{ width: '100%' }}
                  onChange={(value) => handleRateChange(String(value || 0))}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="effective_month"
                label={t('exchangeRate.effectiveDate')}
                rules={[{ required: true, message: t('exchangeRate.selectEffectiveMonth') }]}
              >
                <DatePicker
                  picker="month"
                  placeholder={t('exchangeRate.selectEffectiveMonthPlaceholder')}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label={t('exchangeRate.status')}
                rules={[{ required: true, message: t('exchangeRate.selectStatus') }]}
              >
                <Select placeholder={t('exchangeRate.selectStatusPlaceholder')}>
                  <Option value="active">{t('exchangeRate.active')}</Option>
                  <Option value="inactive">{t('exchangeRate.inactive')}</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* 汇率转换示例 */}
          {selectedCurrency && (
            <div>
              {selectedCurrency === 'USD' ? (
                <div style={{ marginTop: '12px', padding: '8px 12px', background: '#f6ffed', borderRadius: '6px', border: '1px solid #b7eb8f' }}>
                  <Text style={{ fontSize: '12px', color: '#389e0d' }}>
                    ℹ️ {t('exchangeRate.baseCurrencyNote')}
                  </Text>
                </div>
              ) : currentRate > 0 ? (
                generateExchangeExamples(selectedCurrency, currentRate)
              ) : null}
            </div>
          )}

          {isEditMode && (
            <Form.Item
              name="change_reason"
              label={t('exchangeRate.changeReason')}
              rules={[{ required: true, message: t('exchangeRate.enterChangeReason') }]}
              style={{ marginTop: '16px' }}
            >
              <TextArea
                rows={3}
                placeholder={t('exchangeRate.changeReasonPlaceholder')}
              />
            </Form.Item>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default ExchangeRateManagement;
