import React, { useState, useEffect, useCallback } from 'react';
import { Card, Tabs, Typography, Space, Button, message, Spin, Row, Col, Statistic } from 'antd';
import { 
  BellOutlined, 
  MailOutlined, 
  BarChartOutlined, 
  ReloadOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { notificationService } from '../services/notificationService';
import { NotificationStats, EmailStats } from '../types/notification';
import NotificationList from './NotificationList';
import EmailStatusChart from '../components/notifications/EmailStatusChart';
import NotificationTypeChart from '../components/notifications/NotificationTypeChart';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const NotificationManagement: React.FC = () => {
  const { t } = useTranslation();
  
  const [loading, setLoading] = useState(false);
  const [notificationStats, setNotificationStats] = useState<NotificationStats | null>(null);
  const [emailStats, setEmailStats] = useState<EmailStats | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // 加载统计数据
  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      const [notifStats, emailStatsData] = await Promise.all([
        notificationService.getNotificationStats(),
        notificationService.getEmailStats()
      ]);
      
      setNotificationStats(notifStats);
      setEmailStats(emailStatsData);
    } catch (error) {
      console.error('Failed to load stats:', error);
      message.error(t('notifications.management.load_stats_error'));
    } finally {
      setLoading(false);
    }
  };

  // 手动触发邮件发送
  const handleTriggerEmail = async () => {
    try {
      await notificationService.triggerEmailSend();
      message.success(t('notifications.management.trigger_email_success'));
      
      // 重新加载统计数据
      loadStats();
    } catch (error) {
      console.error('Failed to trigger email send:', error);
      message.error(t('notifications.management.trigger_email_error'));
    }
  };

  // 刷新数据
  const handleRefresh = () => {
    loadStats();
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  if (loading && !notificationStats) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>{t('common.loading')}</div>
      </div>
    );
  }

  return (
    <div>
      {/* 页面头部 */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <BellOutlined style={{ fontSize: 24, color: '#1890ff' }} />
            <Title level={3} style={{ margin: 0 }}>
              {t('notifications.management.title')}
            </Title>
          </Space>
          
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={handleTriggerEmail}
              loading={loading}
            >
              {t('notifications.management.trigger_email')}
            </Button>
            
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              {t('notifications.management.refresh')}
            </Button>
          </Space>
        </div>
        <Text type="secondary">
          {t('notifications.management.description')}
        </Text>
      </Card>

      {/* 统计概览 */}
      {notificationStats && emailStats && (
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title={t('notifications.management.total_notifications')}
                value={notificationStats.total}
                prefix={<BellOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title={t('notifications.management.unread_count')}
                value={notificationStats.unread}
                prefix={<BellOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title={t('notifications.management.total_emails')}
                value={emailStats?.total || 0}
                prefix={<MailOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title={t('notifications.management.email_success_rate')}
                value={emailStats?.success_rate || 0}
                suffix="%"
                prefix={<MailOutlined />}
                valueStyle={{ color: (emailStats?.success_rate || 0) >= 90 ? '#52c41a' : '#faad14' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 主要内容区域 */}
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane
          tab={
            <span>
              <BarChartOutlined />
              {t('notifications.management.overview')}
            </span>
          }
          key="overview"
        >
          <Row gutter={16}>
            <Col xs={24} lg={12}>
              <Card title={t('notifications.management.notification_type_chart')}>
                {notificationStats && (
                  <NotificationTypeChart data={notificationStats.by_type} />
                )}
              </Card>
            </Col>
            
            <Col xs={24} lg={12}>
              <Card title={t('notifications.management.email_status_chart')}>
                {emailStats ? (
                  <EmailStatusChart data={{
                    pending: emailStats.pending || 0,
                    sent: emailStats.sent || 0,
                    failed: emailStats.failed || 0
                  }} />
                ) : (
                  <div style={{ 
                    textAlign: 'center', 
                    padding: '40px 20px',
                    color: '#8c8c8c'
                  }}>
                    {t('notifications.charts.no_data')}
                  </div>
                )}
              </Card>
            </Col>
          </Row>
          
          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col xs={24}>
              <Card title={t('notifications.management.notification_status_chart')}>
                {notificationStats && (
                  <NotificationTypeChart data={{
                    unread: notificationStats.unread || 0,
                    read: notificationStats.read || 0
                  }} />
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>
        
        <TabPane
          tab={
            <span>
              <BellOutlined />
              {t('notifications.management.all_notifications')}
            </span>
          }
          key="notifications"
        >
          <NotificationList />
        </TabPane>
        
        <TabPane
          tab={
            <span>
              <MailOutlined />
              {t('notifications.management.email_logs')}
            </span>
          }
          key="emails"
        >
          <Card>
            <div style={{ textAlign: 'center', padding: '50px' }}>
              <MailOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
              <div style={{ marginTop: 16 }}>
                <Text type="secondary">
                  {t('notifications.management.email_logs_coming_soon')}
                </Text>
              </div>
            </div>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default NotificationManagement;
