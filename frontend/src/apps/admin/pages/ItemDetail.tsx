import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { 
  Card, 
  Button, 
  Space, 
  Row, 
  Col, 
  Image, 
  Typography, 
  Tag, 
  Descriptions, 
  Popconfirm, 
  message,
  Timeline,
  Spin,
  Divider,
  Avatar,
  Table,
  Tooltip,
  Select,
  InputNumber
} from 'antd';
import { 
  ArrowLeftOutlined, 
  EditOutlined, 
  DeleteOutlined,
  UserOutlined,
  ClockCircleOutlined,
  PlusOutlined,
  ShoppingCartOutlined
} from '@ant-design/icons';
import { apiClient } from '../services/authService';
import { useAuth } from '../contexts/AuthContext';
import PermissionGuard from '@shared/components/PermissionGuard';
import { PERMISSIONS } from '@shared/config/permissions';
import SupplierSelector from '@admin/components/SupplierSelector';
import { supplierService } from '@admin/services/supplierService';
import { purchaseCartService } from '@admin/services/purchaseCartService';
import QRCode from 'react-qr-code';
import { QRCodeService } from '@shared/services/qrCodeService';

const { Title, Text } = Typography;

interface PrimaryCategory {
  id: number;
  name: string;
  description?: string;
  code_prefix: string;
  is_active: boolean;
}

interface Category {
  id: number;
  name: string;
  description?: string;
  primary_category_id: number;
  is_active: boolean;
  primary_category?: PrimaryCategory;
}

interface Item {
  id: number;
  name: string;
  code: string;
  description?: string;
  category_id: number;
  image_url?: string;
  purchase_unit: string;
  inventory_unit: string;
  qty_per_up: number;
  is_purchasable: boolean;
  is_active: boolean;
  brand?: string;
  spec_material?: string;
  size_dimension?: string;
  created_at: string;
  updated_at?: string;
  category?: Category;
}

interface TieredPrice {
  min_quantity: number;
  max_quantity: number | null;
  unit_price: number;  // USD价格
  original_price?: number;  // 原始价格
  currency_code?: string;   // 原始货币
  remarks?: string;
}

interface Supplier {
  id: number;
  supplier_id: number;
  supplier_name: string;
  priority: number | null;
  spq: number;
  moq: number;
  delivery_days: number;
  quality_rating: number;
  tiered_prices: TieredPrice[];
}

interface PriceRange {
  min_price: number;
  max_price: number;
}

interface ItemDetailResponse {
  item: Item;
  suppliers: Supplier[];
  price_range: PriceRange | null;
}

interface ItemChangeHistory {
  id: number;
  item_id: number;
  user_id: number;
  action: string;
  field_name?: string;
  old_value?: string;
  new_value?: string;
  created_at: string;
  user?: {
    username: string;
  };
}

const ItemDetail: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user, hasPermission } = useAuth();
  const [itemDetail, setItemDetail] = useState<ItemDetailResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [changeHistory, setChangeHistory] = useState<ItemChangeHistory[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [supplierSelectorVisible, setSupplierSelectorVisible] = useState(false);
  const [addingSupplier, setAddingSupplier] = useState(false);
  const [updatingPriority, setUpdatingPriority] = useState<number | null>(null);
  
  // 购物车相关状态
  const [cartQuantity, setCartQuantity] = useState(1);
  const [addingToCart, setAddingToCart] = useState(false);
  
  // 当前用户部门ID - 这里应该从用户上下文获取
  const currentDepartmentId = user?.department_id || 0;

  // 获取物品详情
  const fetchItemDetail = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get(`/items/${id}/detail`);
      setItemDetail(response.data);
    } catch (error: any) {
      console.error(t('messages.getItemDetailFailed'), error);
      message.error(t('messages.getItemDetailFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 获取变更历史
  const fetchChangeHistory = async () => {
    if (!id) return;
    
    try {
      setHistoryLoading(true);
      const response = await apiClient.get(`/items/${id}/change-history`);
      setChangeHistory(response.data);
    } catch (error: any) {
      console.error(t('messages.getChangeHistoryFailed'), error);
    } finally {
      setHistoryLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    if (id) {
      fetchItemDetail();
      fetchChangeHistory();
    }
  }, [id]);

  const handleEdit = () => {
    navigate(`/admin/items/${id}/edit`);
  };

  const handleDelete = async () => {
    try {
      await apiClient.delete(`/items/${id}`);
      message.success(t('messages.itemDeleteSuccess'));
      navigate('/admin/items');
    } catch (error: any) {
      console.error(t('messages.itemDeleteFailed'), error);
      message.error(t('messages.itemDeleteFailed'));
    }
  };

  // 处理加入购物车
  const handleAddToCart = async () => {
    if (!itemDetail?.item) return;
    
    // 检查用户是否有部门
    if (!currentDepartmentId) {
      message.error(t('purchase.noDepartmentAssigned'));
      return;
    }
    
    try {
      setAddingToCart(true);
      
      const cartItemData = {
        item_id: itemDetail.item.id,
        spq_count: cartQuantity,
        requirement_notes: undefined,
        expected_delivery: undefined
      };

      await purchaseCartService.addItemToCart(currentDepartmentId, cartItemData);
      
      message.success(t('messages.itemAddedToCart'));
      setCartQuantity(1); // 重置数量
      
    } catch (error: any) {
      console.error(t('messages.addToCartFailed'), error);
      
      // 根据错误类型显示不同的提示信息
      if (error.response?.data?.detail) {
        const errorDetail = error.response.data.detail;
        if (errorDetail.includes('只能为自己部门添加购物车物品')) {
          message.error(t('purchase.canOnlyAddToOwnDepartment'));
        } else if (errorDetail.includes('用户没有部门')) {
          message.error(t('purchase.noDepartmentAssigned'));
        } else {
          message.error(errorDetail);
        }
      } else {
        message.error(t('messages.addToCartFailed'));
      }
    } finally {
      setAddingToCart(false);
    }
  };

  // 处理供应商选择
  const handleSupplierSelect = async (supplier: any) => {
    if (!id) return;
    
    try {
      setAddingSupplier(true);
      
      // 获取当前物品的供应商优先级信息
      const priorityInfo = await apiClient.get(`/suppliers/items/${id}/supplier-priority-info`);
      const suppliers = priorityInfo.data?.current_suppliers ?? [];
      // get max priority and default 0
      const maxPriority = Math.max(...suppliers.map((supplier: any) => supplier.priority), -1);
            
      // 为物品添加供应商
      await supplierService.addSupplierItem(supplier.id, {
        item_id: parseInt(id),
        priority: maxPriority + 1,
        status: 'active',
        // delivery_days: 7, // 默认交货天数
        // quality_rating: 3, // 默认质量评级
        // spq: 100, // 默认标准包装数量
        // moq: 10   // 默认最小订购数量
      });
      
      message.success(t('messages.supplierAddedSuccess', { supplierName: supplier.name_en || supplier.name_cn }));
      setSupplierSelectorVisible(false);
      
      // 刷新物品详情
      await fetchItemDetail();
    } catch (error: any) {
      console.error(t('messages.addSupplierFailed'), error);
      message.error(t('messages.addSupplierFailed'));
    } finally {
      setAddingSupplier(false);
    }
  };

  // 处理供应商优先级更新
  const handlePriorityUpdate = async (supplierId: number, newPriority: number) => {
    if (!id) return;
    
    try {
      setUpdatingPriority(supplierId);
      
      const response = await apiClient.put(`/suppliers/items/${id}/supplier-priority`, null, {
        params: {
          supplier_id: supplierId,
          new_priority: newPriority
        }
      });
      
      if (response.data.success) {
        message.success(t('messages.priorityUpdatedSuccess'));
        
        // 重新获取供应商优先级信息，但保持原有的供应商顺序
        try {
          const priorityInfo = await apiClient.get(`/suppliers/items/${id}/supplier-priority-info`);
          if (itemDetail && priorityInfo.data && itemDetail.suppliers) {
            // 创建优先级映射表
            const priorityMap = new Map();
            priorityInfo.data.current_suppliers.forEach((supplier: any) => {
              priorityMap.set(supplier.supplier_id, supplier.priority);
            });
            
            // 保持原有供应商顺序，只更新优先级
            const updatedSuppliers = itemDetail.suppliers.map(supplier => ({
              ...supplier,
              priority: priorityMap.get(supplier.supplier_id) ?? supplier.priority
            }));
            
            setItemDetail({
              ...itemDetail,
              suppliers: updatedSuppliers
            });
          }
        } catch (error) {
          console.error(t('messages.getUpdatedSupplierInfoFailed'), error);
          // 如果获取失败，回退到刷新整个页面
          await fetchItemDetail();
        }
      }
    } catch (error: any) {
      console.error(t('messages.updatePriorityFailed'), error);
      message.error(t('messages.updatePriorityFailed'));
    } finally {
      setUpdatingPriority(null);
    }
  };



  // 格式化USD价格（按照要求显示精度）
  const formatUSDPrice = (price?: number | string | null) => {
    if (price === null || price === undefined || price === '') {
      return t('item.noPrice');
    }
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    // 按照要求，大于1美元显示2位小数，小于1美元显示4位小数
    if (numPrice >= 1) {
      return `$${numPrice.toFixed(2)}`;
    } else {
      return `$${numPrice.toFixed(4)}`;
    }
  };

  // 格式化原始价格（用于采购申请显示）
  const formatOriginalPrice = (price?: number, currencyCode?: string) => {
    if (price === null || price === undefined) {
      return t('item.noPrice');
    }
    const symbol = currencyCode === 'CNY' ? '￥' : 
                   currencyCode === 'EUR' ? '€' : 
                   currencyCode === 'JPY' ? '￥' : 
                   currencyCode === 'USD' ? '$' : currencyCode || '$';
    return `${symbol}${price.toFixed(2)}`;
  };

  const getImageUrl = () => {
    if (!itemDetail?.item?.image_url) {
      return '/static/images/admin/items/default-item.jpg';
    }
    
    // 直接返回后端返回的URL，不做额外处理
    return itemDetail.item.image_url;
  };

  // 渲染阶梯价格为可换行的标签样式，视觉更紧凑
  const renderTierChips = (tiers: TieredPrice[]) => {
    if (!tiers || tiers.length === 0) return <Text type="secondary">{t('item.noPrice')}</Text>;
    if (!itemDetail?.item) return <Text type="secondary">{t('item.noPrice')}</Text>;
    
    return (
      <Space size={[6, 6]} wrap>
        {tiers.map((tp, idx) => {
          const label = `${tp.min_quantity}-${tp.max_quantity || '∞'}: ${formatUSDPrice(tp.unit_price)}/${itemDetail.item.purchase_unit}`;
          const tip = (
            <div>
              <div>{t('item.priceRange')}: {tp.min_quantity}-{tp.max_quantity || '∞'}</div>
              <div>{t('item.unitPrice')}: {formatUSDPrice(tp.unit_price)} / {itemDetail.item.purchase_unit}</div>
              {tp.remarks && <div>{t('item.remarks')}: {tp.remarks}</div>}
            </div>
          );
          return (
            <Tooltip title={tip} key={idx}>
              <Tag color="blue" style={{ marginInlineEnd: 0 }}>
                {label}
              </Tag>
            </Tooltip>
          );
        })}
      </Space>
    );
  };

  // 垂直列表展示阶梯价格：一行一个区间，左侧区间范围，右侧价格（蓝色）
  const renderTierList = (tiers: TieredPrice[]) => {
    if (!tiers || tiers.length === 0) return <Text type="secondary">{t('item.noPrice')}</Text>;
    if (!itemDetail?.item) return <Text type="secondary">{t('item.noPrice')}</Text>;
    
    const qtyColWidth = 140; // 固定宽度，避免数量过长挤压价格
    return (
      <div style={{ display: 'grid', gridTemplateColumns: `${qtyColWidth}px auto`, rowGap: 6, columnGap: 8, alignItems: 'center' }}>
        {tiers.map((tp, idx) => (
          <React.Fragment key={idx}>
            <Tooltip title={`${tp.min_quantity}-${tp.max_quantity || '∞'}`}>
              <Text type="secondary" style={{ width: qtyColWidth, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'inline-block' }}>
                {tp.min_quantity}-{tp.max_quantity || '∞'}
              </Text>
            </Tooltip>
            <span>
              <Tooltip
                title={
                  <div>
                    <div>{t('item.priceRange')}: {tp.min_quantity}-{tp.max_quantity || '∞'}</div>
                    <div>{t('item.unitPrice')}: {formatUSDPrice(tp.unit_price)} / {itemDetail.item.purchase_unit}</div>
                    {tp.remarks && <div>{t('item.remarks')}: {tp.remarks}</div>}
                  </div>
                }
              >
                <Text strong style={{ color: '#1890ff' }}>{formatUSDPrice(tp.unit_price)}</Text>
              </Tooltip>
              <Text type="secondary"> / {itemDetail.item.purchase_unit}</Text>
            </span>
          </React.Fragment>
        ))}
      </div>
    );
  };

  const formatAction = (action: string) => {
    const actionMap: { [key: string]: string } = {
      'create': t('item.actionCreate'),
      'update': t('item.actionUpdate'),
      'delete': t('item.actionDelete')
    };
    return actionMap[action] || action;
  };

  const formatFieldName = (fieldName: string) => {
    const fieldMap: { [key: string]: string } = {
      'name': t('item.fieldNameName'),
      'code': t('item.fieldNameCode'),
      'description': t('item.fieldNameDescription'),
      'category_id': t('item.fieldNameCategory'),
      'image_url': t('item.fieldNameImage'),
      'unit': t('item.fieldNameUnit'),
      'moq': t('item.fieldNameMoq'),
      'spq': t('item.fieldNameSpq'),
      'qty_per_up': t('item.fieldNameQtyPerUp'),
      'is_purchasable': t('item.fieldNameIsPurchasable'),
      'is_active': t('item.fieldNameIsActive'),

      'brand': t('item.fieldNameBrand'),
      'spec_material': t('item.fieldNameSpecMaterial'),
      'size_dimension': t('item.fieldNameSizeDimension')
    };
    return fieldMap[fieldName] || fieldName;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!itemDetail || !itemDetail.item) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Text>{t('item.itemNotFound')}</Text>
      </div>
    );
  }

  const item = itemDetail.item;

  const qrValue = itemDetail?.item
    ? QRCodeService.generateItemQRCode({
        code: itemDetail.item.code,
        name: itemDetail.item.name,
        brand: itemDetail.item.brand,
        spec_material: itemDetail.item.spec_material,
        size_dimension: itemDetail.item.size_dimension
      })
    : '';

  return (
    <PermissionGuard permission={PERMISSIONS.ITEM.READ}>
      <div style={{ padding: '24px' }}>
        {/* 页面头部 */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '16px',
          padding: '8px 0'
        }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate(-1)}
          >
            {t('item.back')}
          </Button>
          
          <div style={{ flex: 1 }} />
          
          <Space>
            <PermissionGuard permission={PERMISSIONS.ITEM.UPDATE}>
              <Button 
                type="primary" 
                icon={<EditOutlined />} 
                onClick={handleEdit}
              >
                {t('item.edit')}
              </Button>
            </PermissionGuard>
            <PermissionGuard permission={PERMISSIONS.ITEM.DELETE}>
              <Popconfirm
                title={t('item.confirmDeleteItem')}
                description={t('item.deleteItemDescription')}
                onConfirm={handleDelete}
                okText={t('item.confirm')}
                cancelText={t('item.cancel')}
              >
                <Button 
                  danger 
                  icon={<DeleteOutlined />}
                >
                  {t('item.delete')}
                </Button>
              </Popconfirm>
            </PermissionGuard>
          </Space>
        </div>

        {/* 顶部大图 + 右侧信息区（仿电商详情布局） */}
        <div style={{ marginBottom: '24px' }}>
          <Row gutter={24}>
            {/* 左侧大图 */}
            <Col span={10}>
              <Card bodyStyle={{ padding: 0 }} bordered>
                <Image
                  src={getImageUrl()}
                  alt={item.name}
                  style={{ width: '100%', height: 420, objectFit: 'contain', background: '#fff' }}
                  fallback="/static/images/admin/items/default-item.jpg"
                />
              </Card>
            </Col>

            {/* 右侧标题、价格、基本信息 + 右侧二维码 */}
            <Col span={14}>
              <div style={{ display: 'flex', gap: 16, alignItems: 'flex-start' }}>
                <div style={{ flex: 1, textAlign: 'left' }}>
                  {/* 标题与状态标签 */}
                  <Title level={3} style={{ marginTop: 0, marginBottom: 8 }}>{item.name}</Title>
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary" style={{ fontSize: 14 }}>{t('item.code')}: {item.code}</Text>
                  </div>
                  <div style={{ marginBottom: 12 }}>
                    <Space wrap>
                      <Tag color={item.is_active ? 'green' : 'red'}>
                        {item.is_active ? t('item.active') : t('item.inactive')}
                      </Tag>
                      <Tag color={item.is_purchasable ? 'blue' : 'orange'}>
                        {item.is_purchasable ? t('item.purchasable') : t('item.unpurchasable')}
                      </Tag>
                    </Space>
                  </div>

                  {/* 优先供应商价格范围与阶梯价（左右布局） */}
                  {(() => {
                    const preferred = itemDetail.suppliers?.find(s => s.priority === 0);
                    if (!preferred || !preferred.tiered_prices || preferred.tiered_prices.length === 0) {
                      return (
                        <div style={{ padding: '12px 16px', background: '#fafafa', borderRadius: 6, marginBottom: 16 }}>
                          <Text type="secondary">{t('item.noPreferredSupplierPrice')}</Text>
                        </div>
                      );
                    }
                    const prices = preferred.tiered_prices.map(p => p.unit_price);
                    const minPrice = Math.min(...prices);
                    const maxPrice = Math.max(...prices);
                    return (
                      <div style={{ padding: '12px 16px', background: '#e6f4ff', borderRadius: 6, border: '1px solid #bae7ff', marginBottom: 16 }}>
                        <div style={{ display: 'flex', gap: 24, alignItems: 'flex-start', flexWrap: 'wrap' }}>
                          {/* 左侧：供应商名 + 价格范围 */}
                          <div style={{ flex: '1 1 280px', minWidth: 260 }}>
                            <div style={{ marginBottom: 6 }}>
                              <Text
                                strong
                                style={{ color: '#1677ff', cursor: 'pointer' }}
                                onClick={() => navigate(`/admin/suppliers/${preferred.supplier_id}/items/${id}`)}
                              >
                                {preferred.supplier_name}
                              </Text>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'baseline', gap: 8 }}>
                              <Title level={3} style={{ color: '#1890ff', margin: 0 }}>
                                {minPrice === maxPrice ? `${formatUSDPrice(minPrice)}` : `${formatUSDPrice(minPrice)} - ${formatUSDPrice(maxPrice)}`}
                              </Title>
                              <Text type="secondary">{t('item.unit')}: {item.purchase_unit}</Text>
                            </div>
                          </div>

                          {/* 右侧：阶梯价格 */}
                          <div style={{ flex: '1 1 280px', minWidth: 260 }}>
                            {renderTierList(preferred.tiered_prices)}
                          </div>
                        </div>
                      </div>
                    );
                  })()}

                  {/* 基本信息 + 属性 */}
                  <Descriptions column={2} size="small" style={{ textAlign: 'left' }}>
                    <Descriptions.Item label={t('item.category')}>
                      {item.category?.primary_category?.name && item.category?.name 
                        ? `${item.category.primary_category.name} / ${item.category.name}`
                        : item.category?.primary_category?.name || item.category?.name || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label={t('item.purchaseUnit')}>{item.purchase_unit}</Descriptions.Item>
                    <Descriptions.Item label={t('item.inventoryUnit')}>{item.inventory_unit}</Descriptions.Item>
                    <Descriptions.Item label={t('item.packageQuantity')}>{item.qty_per_up}</Descriptions.Item>
                    <Descriptions.Item label={t('item.brand')}>{item.brand || '-'}</Descriptions.Item>
                    <Descriptions.Item label={t('item.specMaterial')}>{item.spec_material || '-'}</Descriptions.Item>
                    <Descriptions.Item label={t('item.sizeDimension')}>{item.size_dimension || '-'}</Descriptions.Item>
                  </Descriptions>

                  {/* 购物车功能区域 */}
                  {item.is_purchasable && (
                    <PermissionGuard permission={PERMISSIONS.CART.ADD_ITEM}>
                      <div style={{ 
                        marginTop: '20px', 
                        padding: '16px', 
                        background: '#f8f9fa', 
                        borderRadius: '8px',
                        border: '1px solid #e9ecef',
                        minWidth: '300px'
                      }}>
                        <div style={{ marginBottom: '12px' }}>
                          <Text strong style={{ fontSize: '16px' }}>{t('item.addToCart')}</Text>
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flexWrap: 'nowrap' }}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', flexWrap: 'nowrap' }}>
                            <Text style={{ minWidth: '40px', flexShrink: 0 }}>{t('item.quantity')}:</Text>
                            <InputNumber
                              min={1}
                              precision={0}
                              value={cartQuantity}
                              onChange={(value) => setCartQuantity(value || 1)}
                              style={{ width: '120px', flexShrink: 0 }}
                              addonAfter={`${t('item.x')} ${item.qty_per_up || 1} ${item.purchase_unit || t('item.unit')}`}
                            />
                          </div>
                          <Button
                            type="primary"
                            icon={<ShoppingCartOutlined />}
                            onClick={handleAddToCart}
                            loading={addingToCart}
                            disabled={!item.is_purchasable}
                          >
                            {t('item.addToCart')}
                          </Button>
                        </div>
                      </div>
                    </PermissionGuard>
                  )}
                </div>

                <div style={{ width: 200 }}>
                  <Card size="small" bordered>
                    <div style={{ display: 'flex', justifyContent: 'center' }}>
                      <div
                        style={{
                          position: 'relative',
                          background: '#ffffff',
                          padding: 16,
                          border: '1px solid #e5e7eb',
                          borderRadius: 12,
                          boxShadow: '0 2px 6px rgba(0,0,0,0.04)'
                        }}
                      >
                        <span style={{ position: 'absolute', top: 6, left: 6, width: 18, height: 18, borderTop: '3px solid #1677ff', borderLeft: '3px solid #1677ff', borderRadius: 4 }} />
                        <span style={{ position: 'absolute', top: 6, right: 6, width: 18, height: 18, borderTop: '3px solid #1677ff', borderRight: '3px solid #1677ff', borderRadius: 4 }} />
                        <span style={{ position: 'absolute', bottom: 6, left: 6, width: 18, height: 18, borderBottom: '3px solid #1677ff', borderLeft: '3px solid #1677ff', borderRadius: 4 }} />
                        <span style={{ position: 'absolute', bottom: 6, right: 6, width: 18, height: 18, borderBottom: '3px solid #1677ff', borderRight: '3px solid #1677ff', borderRadius: 4 }} />
                        
                        {/* 物品二维码 */}
                        <QRCode 
                          value={qrValue} 
                          size={160} 
                          fgColor="#1f2937" 
                          bgColor="#ffffff"
                          level="M"
                          title="物品二维码"
                        />
                      </div>
                    </div>
                  </Card>
                </div>
              </div>
            </Col>
          </Row>
        </div>

        <Divider style={{ margin: '24px 0' }} />
        
        {/* 供应商信息板块 */}
        <PermissionGuard permissions={[PERMISSIONS.SUPPLIER.READ]}>
          <div style={{ marginBottom: '24px' }}>
            <div style={{ padding: '8px 0' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                <Text strong style={{ fontSize: '16px' }}>{t('item.supplierInfo')}</Text>
                <PermissionGuard permissions={[PERMISSIONS.SUPPLIER.UPDATE]}>
                  <Button 
                    type="primary" 
                    icon={<PlusOutlined />}
                    onClick={() => setSupplierSelectorVisible(true)}
                    loading={addingSupplier}
                  >
                    {t('item.addSupplier')}
                  </Button>
                </PermissionGuard>
              </div>
              {itemDetail.suppliers && itemDetail.suppliers.length > 0 ? (
                <div>
                  <Table 
                    dataSource={itemDetail.suppliers} 
                    columns={[
                      {
                        title: t('item.supplier'),
                        key: 'supplier_name',
                        render: (_: any, record: Supplier) => {
                          const isPreferred = record.priority === 0;
                          
                          // 生成优先级选项 - 允许选择任何优先级，后端会自动重排序
                          const priorityOptions = [];
                          const maxPriority = Math.max(...(itemDetail.suppliers?.map(s => s.priority || 0) || [0]), 0);
                          
                          // 生成所有可能的优先级选项（0到maxPriority）
                          for (let i = 0; i <= maxPriority; i++) {
                            const isCurrentPriority = record.priority === i;
                            
                            priorityOptions.push({
                              value: i,
                              label: i === 0 ? t('item.preferred') : `${t('item.alternative')} ${i}`,
                              disabled: false, // 所有优先级都可以选择
                              isCurrent: isCurrentPriority
                            });
                          }
                          
                          return (
                            <div>
                              <div style={{ marginBottom: '4px' }}>
                                <PermissionGuard permission={PERMISSIONS.SUPPLIER.UPDATE}>
                                  <Select
                                    value={record.priority}
                                    style={{ minWidth: '120px', marginRight: '8px' }}
                                    size="small"
                                    onChange={(value) => handlePriorityUpdate(record.supplier_id, value)}
                                    loading={updatingPriority === record.supplier_id}
                                    disabled={updatingPriority === record.supplier_id}
                                  >
                                    {priorityOptions.map(option => (
                                      <Select.Option 
                                        key={option.value} 
                                        value={option.value}
                                        disabled={option.disabled}
                                      >
                                        <Tag 
                                          color={option.value === 0 ? 'green' : 'blue'} 
                                          style={{ 
                                            margin: 0, 
                                            fontSize: '11px'
                                          }}
                                        >
                                          {option.label}
                                        </Tag>
                                      </Select.Option>
                                    ))}
                                  </Select>
                                </PermissionGuard>
                                {!user || !user.permissions?.includes(PERMISSIONS.SUPPLIER.UPDATE) ? (
                                  <Tag color={isPreferred ? 'green' : 'blue'} style={{ marginRight: '8px' }}>
                                    {isPreferred ? t('item.preferred') : `${t('item.alternative')} ${record.priority || 'N'}`}
                                  </Tag>
                                ) : null}
                              </div>
                              <div>
                                <Text 
                                  strong 
                                  style={{ 
                                    cursor: 'pointer', 
                                    color: '#1890ff',
                                    display: 'inline-block',
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    maxWidth: '200px'
                                  }}
                                  onClick={() => navigate(`/admin/suppliers/${record.supplier_id}/items/${id}`)}
                                  title={record.supplier_name}
                                >
                                  {record.supplier_name}
                                </Text>
                              </div>
                            </div>
                          );
                        }
                      },
                      {
                        title: t('item.spq'),
                        dataIndex: 'spq',
                        key: 'spq',
                        render: (value: any) => <Text>{value}</Text>
                      },
                      {
                        title: t('item.moq'),
                        dataIndex: 'moq',
                        key: 'moq',
                        render: (value: any) => <Text>{value}</Text>
                      },
                      {
                        title: t('item.deliveryDays'),
                        dataIndex: 'delivery_days',
                        key: 'delivery_days',
                        render: (value: any) => <Text>{value} {t('item.days')}</Text>
                      },
                      {
                        title: t('item.qualityRating'),
                        dataIndex: 'quality_rating',
                        key: 'quality_rating',
                        render: (value: any) => <Text>{value}/5</Text>
                      },
                      {
                        title: t('item.priceRange'),
                        key: 'price_range',
                        render: (_: any, record: Supplier) => {
                          if (!record.tiered_prices || record.tiered_prices.length === 0) {
                            return <Text type="secondary">{t('item.noPrice')}</Text>;
                          }
                          
                          const prices = record.tiered_prices.map(p => p.unit_price);
                          const minPrice = Math.min(...prices);
                          const maxPrice = Math.max(...prices);
                          
                          return (
                            <Text strong style={{ color: '#1890ff' }}>
                              {minPrice === maxPrice ? 
                                `${formatUSDPrice(minPrice)}` : 
                                `${formatUSDPrice(minPrice)} - ${formatUSDPrice(maxPrice)}`
                              }
                            </Text>
                          );
                        }
                      },
                      {
                        title: t('item.tieredPrice'),
                        key: 'tiered_prices',
                        render: (_: any, record: Supplier) => {
                          if (!record.tiered_prices || record.tiered_prices.length === 0) {
                            return <Text type="secondary">{t('item.noPrice')}</Text>;
                          }
                          
                          return renderTierList(record.tiered_prices);
                        }
                      }
                    ]}
                    pagination={false}
                    size="small"
                    style={{ marginBottom: '16px' }}
                  />
                </div>
              ) : (
                <Row gutter={16}>
                  <Col span={24}>
                    <div style={{ textAlign: 'left' }}>
                      <Text type="secondary">{t('item.noSupplierConfig')}</Text>
                    </div>
                  </Col>
                </Row>
              )}
              
              {itemDetail.price_range && (
                <Row gutter={16} style={{ marginTop: '16px' }}>
                  <Col span={6}>
                    <div style={{ textAlign: 'left' }}>
                      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                        <Text strong style={{ width: '100px', marginRight: '8px' }}>{t('item.priceRange')}:</Text>
                        <Text strong style={{ color: '#1890ff' }}>
                          {itemDetail.price_range ? 
                            (itemDetail.price_range.min_price === itemDetail.price_range.max_price ? 
                              `${formatUSDPrice(itemDetail.price_range.min_price)}` : 
                              `${formatUSDPrice(itemDetail.price_range.min_price)} - ${formatUSDPrice(itemDetail.price_range.max_price)}`
                            ) : 
                            t('item.noPriceInfo')
                          }
                        </Text>
                      </div>
                    </div>
                  </Col>
                  <Col span={18}>
                    {/* 占位，保持四列对齐 */}
                  </Col>
                </Row>
              )}
            </div>
          </div>
        </PermissionGuard>

        <Divider style={{ margin: '24px 0' }} />
        


        {/* 变更历史 */}
        <Divider style={{ margin: '24px 0' }} />
        <div style={{ marginBottom: '24px' }}>
          {historyLoading ? (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Spin />
            </div>
          ) : (
            <div style={{ minHeight: '120px' }}>
              {changeHistory.length > 0 ? (
                <Timeline>
                  {changeHistory.map((change, index) => (
                    <Timeline.Item
                      key={change.id}
                      dot={
                        <Avatar 
                          size="small" 
                          icon={<UserOutlined />} 
                          style={{ backgroundColor: '#1890ff' }}
                        />
                      }
                    >
                      <div className="timeline-item">
                        <div className="timeline-header">
                          <Text strong>{change.user?.username || t('item.unknownUser')}</Text>
                          <Text type="secondary" style={{ marginLeft: '8px' }}>
                            {formatAction(change.action)}
                          </Text>
                          <Text type="secondary" style={{ marginLeft: '8px' }}>
                            {dayjs(change.created_at).format(DATE_FORMATS.DATE_TIME)}
                          </Text>
                        </div>
                        {change.field_name && (
                          <div className="timeline-details">
                            <Text type="secondary">
                              {t('item.field')}: {formatFieldName(change.field_name)}
                            </Text>
                            {change.old_value && (
                              <Text delete style={{ marginLeft: '8px' }}>
                                {t('item.oldValue')}: {change.old_value}
                              </Text>
                            )}
                            {change.new_value && (
                              <Text type="success" style={{ marginLeft: '8px' }}>
                                {t('item.newValue')}: {change.new_value}
                              </Text>
                            )}
                          </div>
                        )}
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              ) : (
                <div style={{ 
                  textAlign: 'center', 
                  padding: '40px 20px', 
                  color: '#999',
                  backgroundColor: '#fafafa',
                  borderRadius: '6px',
                  margin: '20px 0'
                }}>
                  <ClockCircleOutlined style={{ fontSize: '24px', marginBottom: '8px', color: '#d9d9d9' }} />
                  <div>{t('item.noChangeHistory')}</div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {t('item.itemCreatedNoChanges')}
                  </Text>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      
      {/* 供应商选择器 */}
      <PermissionGuard permissions={[PERMISSIONS.SUPPLIER.READ]}>
        <SupplierSelector
          visible={supplierSelectorVisible}
          onCancel={() => setSupplierSelectorVisible(false)}
          onSelect={handleSupplierSelect}
          loading={addingSupplier}
        />
      </PermissionGuard>
    </PermissionGuard>
  );
};

export default ItemDetail; 