import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Table, 
  Button, 
  Space, 
  Typography, 
  Card,
  message,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Popconfirm,
  Row,
  Col,
  Divider,
  List
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SettingOutlined } from '@ant-design/icons';
import PermissionGuard from '@shared/components/PermissionGuard';
import { PERMISSIONS } from '@shared/config/permissions';
import { apiClient } from '../services/authService';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface PrimaryCategory {
  id: number;
  name: string;
  description?: string;
  code_prefix: string;
  is_active: boolean;
}

interface Category {
  id: number;
  name: string;
  description?: string;
  primary_category_id: number;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
  primary_category?: PrimaryCategory;
}

interface PropertyConfig {
  id: number;
  category_id: number;
  attribute_name: string;
  input_type: 'select' | 'text';
  options?: string;
}

interface CategoryFormData {
  name: string;
  description?: string;
  primary_category_id: number;
  is_active: boolean;
  // 直接定义三个属性的配置
  brand_config: {
    input_type: 'select' | 'text';
    options?: string;
  };
  spec_material_config: {
    input_type: 'select' | 'text';
    options?: string;
  };
  size_dimension_config: {
    input_type: 'select' | 'text';
    options?: string;
  };
}

const CategoryManagement: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [primaryCategories, setPrimaryCategories] = useState<PrimaryCategory[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [form] = Form.useForm();
  
  // 添加状态来跟踪开关状态
  const [brandOptionsEnabled, setBrandOptionsEnabled] = useState(false);
  const [specMaterialOptionsEnabled, setSpecMaterialOptionsEnabled] = useState(false);
  const [sizeDimensionOptionsEnabled, setSizeDimensionOptionsEnabled] = useState(false);

  useEffect(() => {
    fetchPrimaryCategories();
    fetchCategories();
  }, []);

  // 监听表单值变化来更新选项输入框状态
  useEffect(() => {
    if (modalVisible) {
      const brandInputType = form.getFieldValue(['brand_config', 'input_type']);
      const specMaterialInputType = form.getFieldValue(['spec_material_config', 'input_type']);
      const sizeDimensionInputType = form.getFieldValue(['size_dimension_config', 'input_type']);
      
      setBrandOptionsEnabled(brandInputType === 'select');
      setSpecMaterialOptionsEnabled(specMaterialInputType === 'select');
      setSizeDimensionOptionsEnabled(sizeDimensionInputType === 'select');
    }
  }, [modalVisible, form]);

  const fetchPrimaryCategories = async () => {
    try {
      const response = await apiClient.get('/items/primary-categories');
      setPrimaryCategories(response.data);
    } catch (error) {
      message.error(t('messages.getPrimaryCategoriesFailed'));
    }
  };

  const fetchCategories = async () => {
    setLoading(true);
    try {
      const response = await apiClient.get('/items/categories');
      setCategories(response.data);
    } catch (error) {
      message.error(t('messages.getCategoriesFailed'));
    } finally {
      setLoading(false);
    }
  };

  const fetchPropertyConfigs = async (categoryId: number): Promise<PropertyConfig[]> => {
    try {
      const response = await apiClient.get(`/items/categories/${categoryId}/property-configs`);
      return response.data;
    } catch (error) {
      console.error(t('messages.getPropertyConfigsFailed'), error);
      return [];
    }
  };

  const handleSubmit = async (values: CategoryFormData) => {
    try {
      const { brand_config, spec_material_config, size_dimension_config, ...categoryData } = values;
      
      if (editingCategory) {
        // 更新分类
        await apiClient.put(`/items/categories/${editingCategory.id}`, categoryData);
        message.success(t('messages.categoryUpdateSuccess'));
        
        // 处理属性配置
        await handlePropertyConfigs(editingCategory.id, {
          brand_config,
          spec_material_config,
          size_dimension_config
        });
      } else {
        // 创建分类
        const response = await apiClient.post('/items/categories', categoryData);
        message.success(t('messages.categoryCreateSuccess'));
        
        // 处理属性配置
        const newCategoryId = response.data.id;
        await handlePropertyConfigs(newCategoryId, {
          brand_config,
          spec_material_config,
          size_dimension_config
        });
      }
      
      setModalVisible(false);
      setEditingCategory(null);
      form.resetFields();
      fetchCategories();
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('messages.operationFailed'));
    }
  };

  const handlePropertyConfigs = async (categoryId: number, configs: {
    brand_config: { input_type: 'select' | 'text'; options?: string };
    spec_material_config: { input_type: 'select' | 'text'; options?: string };
    size_dimension_config: { input_type: 'select' | 'text'; options?: string };
  }) => {
    try {
      // 先删除现有的属性配置
      const existingConfigs = await fetchPropertyConfigs(categoryId);
      for (const config of existingConfigs) {
        await apiClient.delete(`/items/categories/${categoryId}/property-configs/${config.id}`);
      }
      
      // 处理三个属性的配置
      const attributeConfigs = [
        { attribute_name: 'brand', ...configs.brand_config },
        { attribute_name: 'spec_material', ...configs.spec_material_config },
        { attribute_name: 'size_dimension', ...configs.size_dimension_config }
      ];
      
      for (const config of attributeConfigs) {
        let processedConfig = { ...config, category_id: categoryId };
        
        if (config.input_type === 'select' && config.options) {
          // 将文本转换为JSON数组
          const optionsList = config.options
            .split('\n')
            .map(option => option.trim())
            .filter(option => option.length > 0);
          processedConfig.options = JSON.stringify(optionsList);
        } else if (config.input_type === 'text') {
          processedConfig.options = undefined;
        }
        
        // 如果是text类型，不保存配置
        if (config.input_type === 'text') {
          continue;
        }
        
        // 如果是select类型，保存配置
        if (config.input_type === 'select') {
          await apiClient.post(
            `/items/categories/${categoryId}/property-configs`,
            processedConfig
          );
        }
      }
      
      message.success(t('messages.propertyConfigsUpdateSuccess'));
    } catch (error: any) {
      message.error(t('messages.propertyConfigsUpdateFailed') + (error.response?.data?.detail || error.message));
    }
  };

  const handleDelete = async (category: Category) => {
    try {
      await apiClient.delete(`/items/categories/${category.id}`);
      message.success(t('messages.categoryDeleteSuccess'));
      fetchCategories();
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('messages.deleteFailed'));
    }
  };

  const handleEdit = async (category: Category) => {
    setEditingCategory(category);
    
    // 获取属性配置
    const propertyConfigs = await fetchPropertyConfigs(category.id);
    
    // 转换属性配置格式
    const brandConfig = propertyConfigs.find(c => c.attribute_name === 'brand');
    const specMaterialConfig = propertyConfigs.find(c => c.attribute_name === 'spec_material');
    const sizeDimensionConfig = propertyConfigs.find(c => c.attribute_name === 'size_dimension');
    
    const brandInputType = brandConfig?.input_type || 'text';
    const specMaterialInputType = specMaterialConfig?.input_type || 'text';
    const sizeDimensionInputType = sizeDimensionConfig?.input_type || 'text';
    
    // 设置开关状态
    setBrandOptionsEnabled(brandInputType === 'select');
    setSpecMaterialOptionsEnabled(specMaterialInputType === 'select');
    setSizeDimensionOptionsEnabled(sizeDimensionInputType === 'select');
    
    form.setFieldsValue({
      ...category,
      brand_config: {
        input_type: brandInputType,
        options: brandConfig?.options ? JSON.parse(brandConfig.options).join('\n') : ''
      },
      spec_material_config: {
        input_type: specMaterialInputType,
        options: specMaterialConfig?.options ? JSON.parse(specMaterialConfig.options).join('\n') : ''
      },
      size_dimension_config: {
        input_type: sizeDimensionInputType,
        options: sizeDimensionConfig?.options ? JSON.parse(sizeDimensionConfig.options).join('\n') : ''
      }
    });
    setModalVisible(true);
  };

  const handleAdd = () => {
    setEditingCategory(null);
    form.resetFields();
    
    // 重置开关状态
    setBrandOptionsEnabled(false);
    setSpecMaterialOptionsEnabled(false);
    setSizeDimensionOptionsEnabled(false);
    
    form.setFieldsValue({
      is_active: true,
      brand_config: { input_type: 'text', options: '' },
      spec_material_config: { input_type: 'text', options: '' },
      size_dimension_config: { input_type: 'text', options: '' }
    });
    setModalVisible(true);
  };

  const handleCancel = () => {
    setModalVisible(false);
    setEditingCategory(null);
    form.resetFields();
  };

  const columns = [
    {
      title: t('categoryManagement.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('categoryManagement.description'),
      dataIndex: 'description',
      key: 'description',
      render: (description: string) => description || '-',
    },
    {
      title: t('categoryManagement.primaryCategory'),
      dataIndex: 'primary_category',
      key: 'primary_category',
      render: (primaryCategory: PrimaryCategory) => primaryCategory?.name || '-',
    },
    {
      title: t('categoryManagement.status'),
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? t('categoryManagement.active') : t('categoryManagement.inactive')}
        </Tag>
      ),
    },
    {
      title: t('categoryManagement.actions'),
      key: 'action',
      render: (_: any, record: Category) => (
        <Space size="small">
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEdit(record)}
          >
            {t('categoryManagement.edit')}
          </Button>
          <Popconfirm
            title={t('categoryManagement.confirmDelete')}
            description={t('categoryManagement.deleteDescription')}
            onConfirm={() => handleDelete(record)}
            okText={t('categoryManagement.confirm')}
            cancelText={t('categoryManagement.cancel')}
          >
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small"
              danger
            >
              {t('categoryManagement.delete')}
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <PermissionGuard permission={PERMISSIONS.ITEM.CATEGORY_MANAGE}>
      <div style={{ padding: '24px' }}>
        <Card>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
            <Title level={3}>{t('categoryManagement.categoryManagement')}</Title>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              {t('categoryManagement.addCategory')}
            </Button>
          </div>

          <Table
            columns={columns}
            dataSource={categories}
            rowKey="id"
            loading={loading}
            pagination={false}
          />
        </Card>

        {/* 分类编辑模态框 */}
        <Modal
          title={editingCategory ? t('categoryManagement.editCategory') : t('categoryManagement.addCategory')}
          open={modalVisible}
          onCancel={handleCancel}
          footer={null}
          width={1000}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
          >
            <Row gutter={24}>
              {/* 左侧：基本信息 */}
              <Col span={12}>
                <div style={{ fontWeight: 'bold', marginBottom: 16, fontSize: '16px' }}>{t('categoryManagement.basicInfo')}</div>
                
                <Form.Item
                  name="name"
                  label={t('categoryManagement.name')}
                  rules={[
                    { required: true, message: t('categoryManagement.nameRequired') },
                    { max: 100, message: t('categoryManagement.nameMaxLength') }
                  ]}
                >
                  <Input placeholder={t('categoryManagement.enterName')} />
                </Form.Item>

                <Form.Item
                  name="primary_category_id"
                  label={t('categoryManagement.primaryCategory')}
                  rules={[
                    { required: true, message: t('categoryManagement.primaryCategoryRequired') }
                  ]}
                >
                  <Select placeholder={t('categoryManagement.selectPrimaryCategory')}>
                    {primaryCategories
                      .filter(category => category.is_active)
                      .map(category => (
                        <Option key={category.id} value={category.id}>
                          {category.name}
                        </Option>
                      ))
                    }
                  </Select>
                </Form.Item>

                <Form.Item
                  name="description"
                  label={t('categoryManagement.description')}
                >
                  <TextArea 
                    rows={4} 
                    placeholder={t('categoryManagement.enterDescription')}
                    maxLength={500}
                    showCount
                  />
                </Form.Item>

                <Form.Item
                  name="is_active"
                  label={t('categoryManagement.status')}
                  valuePropName="checked"
                  initialValue={true}
                >
                  <Switch checkedChildren={t('categoryManagement.active')} unCheckedChildren={t('categoryManagement.inactive')} />
                </Form.Item>
              </Col>

              {/* 右侧：属性配置 */}
              <Col span={12}>
                <div style={{ fontWeight: 'bold', marginBottom: 16, fontSize: '16px' }}>{t('categoryManagement.attributeConfigs')}</div>
                
                {/* 品牌属性配置 */}
                <Card size="small" style={{ marginBottom: 12 }}>
                  <div style={{ fontWeight: 'bold', marginBottom: 8, color: '#1890ff' }}>{t('categoryManagement.brand')}</div>
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item
                        name={['brand_config', 'input_type']}
                        label={t('categoryManagement.optionMode')}
                        valuePropName="checked"
                        getValueFromEvent={(checked) => checked ? 'select' : 'text'}
                        getValueProps={(value) => ({ checked: value === 'select' })}
                      >
                        <Switch 
                          checkedChildren={t('categoryManagement.options')} 
                          unCheckedChildren={t('categoryManagement.text')} 
                          size="small"
                          onChange={(checked) => setBrandOptionsEnabled(checked)}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={16}>
                      <Form.Item
                        name={['brand_config', 'options']}
                        label={t('categoryManagement.optionValues')}
                        tooltip={t('categoryManagement.optionValuesTooltip')}
                      >
                        <TextArea 
                          rows={3} 
                          placeholder={t('categoryManagement.enterOptionValues')}
                          size="small"
                          disabled={!brandOptionsEnabled}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>

                {/* 规格/材质属性配置 */}
                <Card size="small" style={{ marginBottom: 12 }}>
                  <div style={{ fontWeight: 'bold', marginBottom: 8, color: '#52c41a' }}>{t('categoryManagement.specMaterial')}</div>
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item
                        name={['spec_material_config', 'input_type']}
                        label={t('categoryManagement.optionMode')}
                        valuePropName="checked"
                        getValueFromEvent={(checked) => checked ? 'select' : 'text'}
                        getValueProps={(value) => ({ checked: value === 'select' })}
                      >
                        <Switch 
                          checkedChildren={t('categoryManagement.options')} 
                          unCheckedChildren={t('categoryManagement.text')} 
                          size="small"
                          onChange={(checked) => setSpecMaterialOptionsEnabled(checked)}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={16}>
                      <Form.Item
                        name={['spec_material_config', 'options']}
                        label={t('categoryManagement.optionValues')}
                        tooltip={t('categoryManagement.optionValuesTooltip')}
                      >
                        <TextArea 
                          rows={3} 
                          placeholder={t('categoryManagement.enterOptionValues')}
                          size="small"
                          disabled={!specMaterialOptionsEnabled}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>

                {/* 尺寸/规格属性配置 */}
                <Card size="small" style={{ marginBottom: 12 }}>
                  <div style={{ fontWeight: 'bold', marginBottom: 8, color: '#fa8c16' }}>{t('categoryManagement.sizeDimension')}</div>
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item
                        name={['size_dimension_config', 'input_type']}
                        label={t('categoryManagement.optionMode')}
                        valuePropName="checked"
                        getValueFromEvent={(checked) => checked ? 'select' : 'text'}
                        getValueProps={(value) => ({ checked: value === 'select' })}
                      >
                        <Switch 
                          checkedChildren={t('categoryManagement.options')} 
                          unCheckedChildren={t('categoryManagement.text')} 
                          size="small"
                          onChange={(checked) => setSizeDimensionOptionsEnabled(checked)}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={16}>
                      <Form.Item
                        name={['size_dimension_config', 'options']}
                        label={t('categoryManagement.optionValues')}
                        tooltip={t('categoryManagement.optionValuesTooltip')}
                      >
                        <TextArea 
                          rows={3} 
                          placeholder={t('categoryManagement.enterOptionValues')}
                          size="small"
                          disabled={!sizeDimensionOptionsEnabled}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>

            <Divider />

            <Form.Item style={{ marginBottom: 0, textAlign: 'center' }}>
              <Space>
                <Button type="primary" htmlType="submit">
                  {editingCategory ? t('categoryManagement.update') : t('categoryManagement.create')}
                </Button>
                <Button onClick={handleCancel}>
                  {t('categoryManagement.cancel')}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </PermissionGuard>
  );
};

export default CategoryManagement; 