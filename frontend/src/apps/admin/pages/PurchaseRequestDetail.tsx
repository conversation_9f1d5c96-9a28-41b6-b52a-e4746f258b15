import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Row, 
  Col, 
  Modal, 
  message,
  Spin
} from 'antd';
import { purchaseRequestService, PurchaseRequest, PurchaseRequestItem } from '@admin/services/purchaseRequestService';
import RequestStatusSteps from '@admin/components/purchase/RequestStatusSteps';
import ApprovalForm from '@admin/components/purchase/ApprovalForm';
import ItemRequestHistoryChart from '@admin/components/purchase/ItemRequestHistoryChart';
import ItemPriceTrendChart from '@admin/components/purchase/ItemPriceTrendChart';
import RequestDetailHeader from '@admin/components/purchase/RequestDetailHeader';
import RequestBasicInfo from '@admin/components/purchase/RequestBasicInfo';
import RequestItemsTable from '@admin/components/purchase/RequestItemsTable';
import RequestActionButtons from '@admin/components/purchase/RequestActionButtons';
import RequestFlowHistory from '@admin/components/purchase/RequestFlowHistory';
import { apiClient } from '@admin/services/authService';


const PurchaseRequestDetail: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [request, setRequest] = useState<PurchaseRequest | null>(null);
  const [loading, setLoading] = useState(true);
  const [preferredSuppliers, setPreferredSuppliers] = useState<{[itemId: number]: any}>({});
  
  // 审批相关状态
  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [currentRequest, setCurrentRequest] = useState<PurchaseRequest | null>(null);
  const [approvalLevel, setApprovalLevel] = useState<string>('');

  // 显示审批窗口
  const showApprovalModal = (level: string) => {
    setApprovalLevel(level);
    setCurrentRequest(request);
    setApprovalModalVisible(true);
  };

  // 隐藏审批窗口
  const hideApprovalModal = () => {
    setApprovalModalVisible(false);
    setCurrentRequest(null);
    setApprovalLevel('');
  };

  useEffect(() => {
    if (id) {
      fetchRequestDetail();
    }
  }, [id]);

  const fetchRequestDetail = async () => {
    try {
      setLoading(true);
      const data = await purchaseRequestService.getPurchaseRequest(parseInt(id!));
      setRequest(data);
      
      // 获取每个物品的最优先供应商
      if (data.items && data.items.length > 0) {
        await fetchPreferredSuppliers(data.items);
      }
    } catch (error) {
      console.error(t('messages.getPurchaseRequestDetailFailed'), error);
      message.error(t('messages.getPurchaseRequestDetailFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 获取最优先供应商信息（暂时禁用，申请详情数据已足够）
  const fetchPreferredSuppliers = async (items: PurchaseRequestItem[]) => {
    // 注释掉不必要的物品详情接口调用
    // 申请详情中已包含物品的基本信息，供应商信息可在需要时单独获取
    
    // 如果确实需要供应商信息，可以考虑以下优化方案：
    // 1. 后端在申请详情接口中直接包含供应商信息
    // 2. 批量获取供应商信息的接口
    // 3. 只在用户明确需要查看供应商时才获取
    
    try {
      const suppliersMap: {[itemId: number]: any} = {};
      
      // 临时解决方案：避免重复调用，设置空的供应商映射
      setPreferredSuppliers(suppliersMap);
      
      // 如果后续需要恢复供应商信息获取，请优化为：
      // 1. 防止重复请求的机制
      // 2. 批量请求接口
      // 3. 缓存机制
      
    } catch (error) {
      console.error(t('messages.getSupplierInfoFailed'), error);
    }
  };

  // 处理返回
  const handleBack = () => {
    navigate(-1);
  };

  // 处理编辑
  const handleEdit = () => {
    navigate(`/admin/purchase-requests/${request?.id}/edit`);
  };

  // 处理删除
  const handleDelete = async () => {
    Modal.confirm({
      title: t('messages.confirmDeleteTitle'),
      content: (
        <div>
          <p>{t('messages.confirmDeleteContent', { requestNo: request?.request_no })}</p>
          <div style={{ marginTop: 16 }}>
            <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
              <input
                type="checkbox"
                id="returnToCart"
                style={{ marginRight: 8 }}
                defaultChecked={true}
              />
              <span style={{ color: '#666' }}>
                {t('messages.returnToCart')}
              </span>
            </label>
          </div>
        </div>
      ),
      okText: t('messages.confirmDelete'),
      okType: 'danger',
      cancelText: t('messages.cancel'),
      onOk: async () => {
        try {
          const returnToCart = (document.getElementById('returnToCart') as HTMLInputElement)?.checked;
          await purchaseRequestService.deleteRequest(request!.id, returnToCart);
          
          if (returnToCart) {
            message.success(t('messages.requestDeletedWithItemsReturned'));
          } else {
            message.success(t('messages.requestDeleted'));
          }
          
          navigate(-1);
        } catch (error) {
          console.error(t('messages.deleteRequestFailed'), error);
          message.error(t('messages.deleteRequestFailed'));
        }
      },
    });
  };

  // 处理撤回
  const handleWithdraw = async () => {
    Modal.confirm({
      title: t('messages.confirmWithdrawTitle'),
      content: t('messages.confirmWithdrawContent'),
      okText: t('messages.confirm'),
      okType: 'default',
      cancelText: t('messages.cancel'),
      onOk: async () => {
        try {
          await purchaseRequestService.withdrawToPending(request!.id);
          message.success(t('messages.requestWithdrawn'));
          fetchRequestDetail(); // 刷新数据
        } catch (error) {
          console.error(t('messages.withdrawRequestFailed'), error);
          message.error(t('messages.withdrawRequestFailed'));
        }
      },
    });
  };

  // 处理提交申请
  const handleSubmit = async () => {
    Modal.confirm({
      title: t('messages.confirmSubmitTitle'),
      content: t('messages.confirmSubmitContent'),
      okText: t('messages.confirm'),
      okType: 'primary',
      cancelText: t('messages.cancel'),
      onOk: async () => {
        try {
          await purchaseRequestService.submitRequest(request!.id);
          message.success(t('messages.requestSubmitted'));
          fetchRequestDetail(); // 刷新数据
        } catch (error) {
          console.error(t('messages.submitRequestFailed'), error);
          message.error(t('messages.submitRequestFailed'));
        }
      },
    });
  };

  // 处理审批
  const handleApproval = (level: string) => {
    showApprovalModal(level);
  };

  // 执行审批操作
  const executeApproval = async (id: number, action: 'approve' | 'reject', comments: string) => {
    try {
      // 调用审批API
      await purchaseRequestService.approveRequest(id, action, comments);
      
      // 审批成功后刷新数据
      await fetchRequestDetail();
      
      return Promise.resolve();
    } catch (error) {
      console.error('审批失败:', error);
      message.error(t('messages.approvalFailed'));
      return Promise.reject(error);
    }
  };

  // 权限检查
  const canEdit = request?.status === 'pending_submission' || request?.status === 'rejected';
  const canDelete = request?.status === 'pending_submission';
  const canApprove = ['under_review', 'under_principle_approval', 'under_final_approval'].includes(request?.status || '');

  // 使用 useMemo 优化图表组件的 props，避免重复渲染
  const chartProps = useMemo(() => {
    if (!request?.items || request.items.length === 0) return null;
    
    return {
      departmentId: request.department_id,
      itemIds: request.items.map(item => item.item_id),
      itemNames: request.items.reduce((acc, item) => {
        acc[item.item_id] = item.item_name;
        return acc;
      }, {} as { [key: number]: string }),
      requestId: request.id
    };
  }, [request?.id, request?.department_id, request?.items]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>{t('messages.loading')}</div>
      </div>
    );
  }

  if (!request) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <p>{t('messages.purchaseRequestNotFound')}</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 自定义样式 */}
      <style>
        {`
          .custom-mask {
            background-color: rgba(0, 0, 0, 0.6) !important;
            color: white !important;
            font-size: 12px !important;
            border-radius: 4px !important;
          }
          
          /* 流转历史表格行样式 */
          .flow-history-submit-row {
            background-color: #f6ffed !important;
          }
          
          .flow-history-approve-row {
            background-color: #f6ffed !important;
          }
          
          .flow-history-reject-row {
            background-color: #fff2f0 !important;
          }
          
          .flow-history-return-row {
            background-color: #fff7e6 !important;
          }
          
          .flow-history-withdraw-row {
            background-color: #f0f5ff !important;
          }
          
          /* 表格行悬停效果 */
          .ant-table-tbody > tr:hover > td {
            background-color: #fafafa !important;
          }
          
          /* 状态变更列的样式 */
          .status-change-arrow {
            color: #1890ff;
            font-size: 12px;
            text-align: center;
            margin: 2px 0;
          }
        `}
      </style>
      
      {/* 页面头部 */}
      <RequestDetailHeader 
        request={request} 
        onBack={handleBack}
      >
        <RequestActionButtons
          request={request}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onWithdraw={handleWithdraw}
          onApprove={handleApproval}
          onSubmit={handleSubmit}
          canEdit={canEdit}
          canDelete={canDelete}
          canApprove={canApprove}
        />
      </RequestDetailHeader>

      <Row gutter={24}>
        {/* 左侧：基本信息和物品明细 */}
        <Col span={16}>
          {/* 基本信息 */}
          <RequestBasicInfo request={request} />
          
          {/* 物品明细 */}
          <RequestItemsTable items={request.items || []} />
          
          {/* 物品申请历史趋势图表 */}
          {chartProps && (
            <ItemRequestHistoryChart
              departmentId={chartProps.departmentId}
              itemIds={chartProps.itemIds}
              itemNames={chartProps.itemNames}
              requestId={chartProps.requestId}
            />
          )}
          
          {/* 物品价格趋势分析 */}
          <ItemPriceTrendChart items={request.items || []} />
        </Col>

        {/* 右侧：状态流程和流转历史 */}
        <Col span={8}>
          {/* 状态流程 */}
          <RequestStatusSteps 
            request={request}
          />
          
          {/* 流转历史 */}
          <RequestFlowHistory request={request} />
        </Col>
      </Row>

      {/* 审批弹窗 */}
      <ApprovalForm
        visible={approvalModalVisible}
        request={currentRequest}
        approvalLevel={approvalLevel}
        onCancel={hideApprovalModal}
        onSuccess={() => {
          hideApprovalModal();
          message.success(t('messages.approvalSuccess'));
          fetchRequestDetail(); // 刷新数据
        }}
        onApprove={executeApproval}
      />
    </div>
  );
};

export default PurchaseRequestDetail;
