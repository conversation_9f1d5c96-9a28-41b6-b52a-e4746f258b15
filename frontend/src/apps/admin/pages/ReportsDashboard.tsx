import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  DatePicker,
  Select,
  Button,
  Space,
  Typography,
  Tabs,
  Spin,
  Alert,
  message
} from 'antd';
import {
  <PERSON>boardOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  DownloadOutlined,
  ReloadOutlined,
  TrophyOutlined,
  DollarOutlined,
  ShoppingOutlined,
  WarningOutlined
} from '@ant-design/icons';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import type { ColumnsType } from 'antd/es/table';
import axios from 'axios';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { apiClient } from '../services/authService';
import MultiCurrencyTotalDisplay from '@shared/components/MultiCurrencyTotalDisplay';

// 通用错误处理函数
const handleApiError = (error: any, defaultMessage: string = '操作失败') => {
  if (error.response?.data?.detail) {
    const detail = error.response.data.detail;
    if (Array.isArray(detail)) {
      // 处理字段验证错误
      const errorMessages = detail.map((err: any) => {
        if (err.loc && err.msg) {
          const field = err.loc[err.loc.length - 1];
          return `${field}: ${err.msg}`;
        }
        return err.msg;
      });
      return errorMessages.join(', ');
    } else {
      return detail;
    }
  }
  return defaultMessage;
};

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

interface DashboardOverview {
  total_items: number;
  total_departments: number;
  total_suppliers: number;
  today_usages: number;
  period_usages: number;
  pending_usages: number;
  total_cost: number;
  active_users: number;
  low_stock_items: number;
  period_days: number;
}

interface ConsumptionReport {
  item_id: number;
  item_name: string;
  item_code: string;
  purchase_unit: string;
  inventory_unit: string;
  qty_per_up: number;
  total_quantity: number;
  total_amount: number;
  usage_count: number;
  avg_quantity: number;
}

interface DepartmentUsageReport {
  department_id: number;
  department_name: string;
  usage_count: number;
  total_quantity: number;
  total_amount: number;
  active_users: number;
  item_types: number;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

const ReportsDashboard: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [overview, setOverview] = useState<DashboardOverview | null>(null);
  const [consumptionData, setConsumptionData] = useState<ConsumptionReport[]>([]);
  const [departmentData, setDepartmentData] = useState<DepartmentUsageReport[]>([]);
  const [trendData, setTrendData] = useState<any[]>([]);
  const [topItemsData, setTopItemsData] = useState<any[]>([]);
  
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs()
  ]);
  const [reportPeriod, setReportPeriod] = useState(30);

  // 获取仪表板概览数据
  const fetchOverview = useCallback(async () => {
    try {
      const response = await apiClient.get(`/reports/dashboard/overview?days=${reportPeriod}`);
      setOverview(response.data);
    } catch (error) {
      message.error(handleApiError(error, t('messages.getOverviewDataFailed')));
    }
  }, [reportPeriod, t]);

  // 获取消耗报表
  const fetchConsumptionReport = useCallback(async () => {
    try {
      const response = await apiClient.get('/reports/consumption', {
        params: {
          start_date: dateRange[0].format(DATE_FORMATS.API_DATE),
          end_date: dateRange[1].format(DATE_FORMATS.API_DATE)
        }
      });
      setConsumptionData(response.data);
    } catch (error) {
      message.error(handleApiError(error, t('messages.getConsumptionReportFailed')));
    }
  }, [dateRange, t]);

  // 获取部门使用情况
  const fetchDepartmentReport = useCallback(async () => {
    try {
      const response = await apiClient.get('/reports/department-usage', {
        params: {
          start_date: dateRange[0].format(DATE_FORMATS.API_DATE),
          end_date: dateRange[1].format(DATE_FORMATS.API_DATE)
        }
      });
      setDepartmentData(response.data);
    } catch (error) {
      message.error(handleApiError(error, t('messages.getDepartmentReportFailed')));
    }
  }, [dateRange, t]);

  // 获取趋势分析
  const fetchTrendAnalysis = useCallback(async () => {
    try {
      const response = await apiClient.get('/reports/trend-analysis', {
        params: {
          days: reportPeriod,
          granularity: 'daily'
        }
      });
      setTrendData(response.data.trend_data || []);
    } catch (error) {
      message.error(handleApiError(error, t('messages.getTrendAnalysisFailed')));
    }
  }, [reportPeriod, t]);

  // 获取热门物品
  const fetchTopItems = useCallback(async () => {
    try {
      const response = await apiClient.get('/reports/top-items', {
        params: {
          days: reportPeriod,
          limit: 10,
          sort_by: 'quantity'
        }
      });
      setTopItemsData(response.data.items || []);
    } catch (error) {
      message.error(handleApiError(error, t('messages.getTopItemsFailed')));
    }
  }, [reportPeriod, t]);

  // 刷新所有数据
  const refreshData = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchOverview(),
        fetchConsumptionReport(),
        fetchDepartmentReport(),
        fetchTrendAnalysis(),
        fetchTopItems()
      ]);
    } finally {
      setLoading(false);
    }
  }, [fetchOverview, fetchConsumptionReport, fetchDepartmentReport, fetchTrendAnalysis, fetchTopItems]);

  useEffect(() => {
    refreshData();
  }, [reportPeriod, dateRange, refreshData]);

  // 消耗报表表格列
  const consumptionColumns: ColumnsType<ConsumptionReport> = [
    {
      title: t('reports.itemName'),
      dataIndex: 'item_name',
      key: 'item_name',
      fixed: 'left',
      width: 150,
    },
    {
      title: t('reports.itemCode'),
      dataIndex: 'item_code',
      key: 'item_code',
      width: 120,
    },
    {
      title: t('reports.totalQuantity'),
      dataIndex: 'total_quantity',
      key: 'total_quantity',
      render: (value, record) => `${value} ${record.inventory_unit}`,
      sorter: (a, b) => a.total_quantity - b.total_quantity,
    },
    {
      title: t('reports.totalAmount'),
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (value) => {
        if (!value) return '暂无数据';
        const numValue = typeof value === 'string' ? parseFloat(value) : value;
        if (isNaN(numValue)) return '暂无数据';
        
        // 使用多货币总金额显示组件
        return (
          <MultiCurrencyTotalDisplay
            totalAmount={numValue}
            currencyCode="USD"
            showCurrencyTag={false}
            showExchangeRate={false}
            showWarning={false}
            size="small"
          />
        );
      },
      sorter: (a, b) => a.total_amount - b.total_amount,
    },
    {
      title: t('reports.usageCount'),
      dataIndex: 'usage_count',
      key: 'usage_count',
      sorter: (a, b) => a.usage_count - b.usage_count,
    },
    {
      title: t('reports.avgQuantity'),
      dataIndex: 'avg_quantity',
      key: 'avg_quantity',
      render: (value, record) => {
        if (!value) return '暂无数据';
        const numValue = typeof value === 'string' ? parseFloat(value) : value;
        if (isNaN(numValue)) return '暂无数据';
        return `${numValue.toFixed(1)} ${record.inventory_unit}`;
      },
    },
  ];

  // 部门使用情况表格列
  const departmentColumns: ColumnsType<DepartmentUsageReport> = [
    {
      title: t('reports.departmentName'),
      dataIndex: 'department_name',
      key: 'department_name',
      fixed: 'left',
      width: 150,
    },
    {
      title: t('reports.usageCount'),
      dataIndex: 'usage_count',
      key: 'usage_count',
      sorter: (a, b) => a.usage_count - b.usage_count,
    },
    {
      title: t('reports.totalQuantity'),
      dataIndex: 'total_quantity',
      key: 'total_quantity',
      sorter: (a, b) => a.total_quantity - b.total_quantity,
    },
    {
      title: t('reports.totalAmount'),
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (value) => {
        if (!value) return '暂无数据';
        const numValue = typeof value === 'string' ? parseFloat(value) : value;
        if (isNaN(numValue)) return '暂无数据';
        
        // 使用多货币总金额显示组件
        return (
          <MultiCurrencyTotalDisplay
            totalAmount={numValue}
            currencyCode="USD"
            showCurrencyTag={false}
            showExchangeRate={false}
            showWarning={false}
            size="small"
          />
        );
      },
      sorter: (a, b) => a.total_amount - b.total_amount,
    },
    {
      title: t('reports.activeUsers'),
      dataIndex: 'active_users',
      key: 'active_users',
    },
    {
      title: t('reports.itemTypes'),
      dataIndex: 'item_types',
      key: 'item_types',
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>
          <DashboardOutlined /> {t('reports.reportsDashboard')}
        </Title>
        <Space>
          <Select
            value={reportPeriod}
            onChange={setReportPeriod}
            style={{ width: 120 }}
          >
            <Option value={7}>{t('reports.last7Days')}</Option>
            <Option value={30}>{t('reports.last30Days')}</Option>
            <Option value={90}>{t('reports.last90Days')}</Option>
            <Option value={365}>{t('reports.lastYear')}</Option>
          </Select>
          <RangePicker
            value={dateRange}
            onChange={(dates) => dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
          />
          <Button icon={<ReloadOutlined />} onClick={refreshData} loading={loading}>
            {t('reports.refresh')}
          </Button>
          <Button icon={<DownloadOutlined />} type="primary">
            {t('reports.exportReport')}
          </Button>
        </Space>
      </div>

      <Spin spinning={loading}>
        {/* 概览统计卡片 */}
        {overview && (
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title={t('reports.todayUsage')}
                  value={overview.today_usages}
                  prefix={<ShoppingOutlined />}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title={`${overview.period_days} ${t('reports.daysUsage')}`}
                  value={overview.period_usages}
                  prefix={<BarChartOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title={t('reports.pendingUsage')}
                  value={overview.pending_usages}
                  prefix={<WarningOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title={t('reports.totalCost')}
                  value={overview.total_cost}
                  prefix={<DollarOutlined />}
                  precision={2}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>
        )}

        {/* 低库存预警 */}
        {overview && overview.low_stock_items > 0 && (
          <Alert
            message={`${t('reports.stockWarning')}: ${overview.low_stock_items} ${t('reports.itemsLowStock')}`}
            type="warning"
            showIcon
            style={{ marginBottom: 24 }}
            action={
              <Button size="small" type="primary">
                {t('reports.viewDetails')}
              </Button>
            }
          />
        )}

        <Tabs defaultActiveKey="trend">
          {/* 趋势分析 */}
          <TabPane tab={<><LineChartOutlined /> {t('reports.trendAnalysis')}</>} key="trend">
            <Card>
              <Title level={4}>{t('reports.usageTrend')}</Title>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <RechartsTooltip />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="usage_count" 
                    stroke="#8884d8" 
                    name={t('reports.usageCount')}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="total_quantity" 
                    stroke="#82ca9d" 
                    name={t('reports.totalQuantity')}
                  />
                </LineChart>
              </ResponsiveContainer>
            </Card>
          </TabPane>

          {/* 消耗分析 */}
          <TabPane tab={<><BarChartOutlined /> {t('reports.consumptionAnalysis')}</>} key="consumption">
            <Card>
              <Title level={4}>{t('reports.itemConsumptionRanking')}</Title>
              <Table
                columns={consumptionColumns}
                dataSource={consumptionData}
                rowKey="item_id"
                size="small"
                scroll={{ x: 800 }}
                pagination={{ pageSize: 20 }}
              />
            </Card>
          </TabPane>

          {/* 部门分析 */}
          <TabPane tab={<><PieChartOutlined /> {t('reports.departmentAnalysis')}</>} key="department">
            <Row gutter={16}>
              <Col xs={24} lg={12}>
                <Card>
                  <Title level={4}>{t('reports.departmentCostDistribution')}</Title>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={departmentData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ department_name, total_amount }) => {
                          const numAmount = typeof total_amount === 'number' ? total_amount : parseFloat(total_amount) || 0;
                          return `${department_name}: $${numAmount.toFixed(0)}`;
                        }}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="total_amount"
                      >
                        {departmentData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <RechartsTooltip formatter={(value) => [`$${value}`, t('reports.cost')]} />
                    </PieChart>
                  </ResponsiveContainer>
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card>
                  <Title level={4}>{t('reports.departmentUsageDetails')}</Title>
                  <Table
                    columns={departmentColumns}
                    dataSource={departmentData}
                    rowKey="department_id"
                    size="small"
                    pagination={false}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>

          {/* 热门物品 */}
          <TabPane tab={<><TrophyOutlined /> {t('reports.popularItems')}</>} key="popular">
            <Card>
              <Title level={4}>{t('reports.popularItemsRanking')}</Title>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={topItemsData.slice(0, 10)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="item_name" 
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    interval={0}
                  />
                  <YAxis />
                  <RechartsTooltip />
                  <Bar dataKey="total_quantity" fill="#8884d8" name={t('reports.totalConsumption')} />
                </BarChart>
              </ResponsiveContainer>
            </Card>
          </TabPane>
        </Tabs>
      </Spin>
    </div>
  );
};

export default ReportsDashboard; 