import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Table,
  Tag,
  Space,
  Modal,
  message,
  Tabs,
  Image,
  Popconfirm,
  Spin,
  Alert,
  Typography,
  Divider
} from 'antd';
import {
  FileImageOutlined,
  DeleteOutlined,
  ReloadOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { apiClient } from '../services/authService';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface FileInfo {
  filename: string;
  size: number;
  created_time: string;
  modified_time: string;
  path: string;
  url: string;
}

interface MatchedItem {
  item_id: number;
  item_name: string;
  item_code: string;
  image_filename: string;
  image_url: string;
  image_info: FileInfo;
}

interface MissingImage {
  item_id: number;
  item_name: string;
  item_code: string;
  image_url: string;
  missing_filename: string;
}

interface MatchResult {
  directory_exists: boolean;
  matched_items: MatchedItem[];
  orphaned_images: FileInfo[];
  missing_images: MissingImage[];
  statistics: {
    total_items: number;
    total_images: number;
    matched_count: number;
    orphaned_count: number;
    missing_count: number;
    total_orphaned_size: number;
  };
}

interface DirectoryStats {
  directory: string;
  exists: boolean;
  images: {
    count: number;
    total_size: number;
  };
  total: {
    files: number;
    size: number;
  };
  match_statistics?: {
    total_items: number;
    total_images: number;
    matched_count: number;
    orphaned_count: number;
    missing_count: number;
    total_orphaned_size: number;
  };
}

const ImageManagement: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [overview, setOverview] = useState<{ directories: { [key: string]: DirectoryStats } } | null>(null);
  const [matchResult, setMatchResult] = useState<MatchResult | null>(null);
  const [selectedOrphanedImages, setSelectedOrphanedImages] = useState<string[]>([]);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化日期
  const formatDate = (dateString: string): string => {
    return dayjs(dateString).format(DATE_FORMATS.DATE_TIME);
  };

  // 加载概览信息
  const loadOverview = async () => {
    setLoading(true);
    try {
      const response = await apiClient.get('/image-management/overview');
      if (response.data.success) {
        setOverview(response.data.data);
      } else {
        message.error(t('messages.getImageOverviewFailed'));
      }
    } catch (error) {
      console.error('获取概览信息失败:', error);
      message.error(t('messages.getImageOverviewFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 加载匹配结果
  const loadMatchResult = async (subDir: string = 'items') => {
    setLoading(true);
    try {
      const response = await apiClient.get(`/image-management/directories/${subDir}/match`);
      if (response.data.success) {
        setMatchResult(response.data.data);
      } else {
        message.error(t('messages.getImageMatchingResultFailed'));
      }
    } catch (error) {
      console.error('获取匹配结果失败:', error);
      message.error(t('messages.getImageMatchingResultFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 删除孤立图片
  const deleteOrphanedImages = async () => {
    if (selectedOrphanedImages.length === 0) {
      message.warning(t('messages.noImagesSelected'));
      return;
    }

    setLoading(true);
    try {
      const response = await apiClient.post('/image-management/orphaned/delete', {
        filenames: selectedOrphanedImages,
        sub_dir: 'items'
      });
      
      if (response.data.success) {
        message.success(response.data.message);
        setSelectedOrphanedImages([]);
        setDeleteModalVisible(false);
        // 重新加载数据
        await Promise.all([loadOverview(), loadMatchResult()]);
      } else {
        message.error(t('messages.deleteOrphanedImagesFailed'));
      }
    } catch (error) {
      console.error('删除孤立图片失败:', error);
      message.error(t('messages.deleteOrphanedImagesFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 刷新数据
  const refreshData = async () => {
    await Promise.all([loadOverview(), loadMatchResult()]);
  };

  useEffect(() => {
    loadOverview();
    loadMatchResult();
  }, []);

  // 孤立图片表格列定义
  const orphanedColumns = [
    {
      title: '预览',
      dataIndex: 'url',
      key: 'preview',
      width: 80,
      render: (url: string) => (
        <Image
          width={50}
          height={50}
          src={url}
          style={{ objectFit: 'cover' }}
          placeholder={<div style={{ width: 50, height: 50, backgroundColor: '#f0f0f0' }} />}
        />
      ),
    },
    {
      title: '文件名',
      dataIndex: 'filename',
      key: 'filename',
      ellipsis: true,
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      key: 'size',
      render: (size: number) => formatFileSize(size),
      sorter: (a: FileInfo, b: FileInfo) => a.size - b.size,
    },
    {
      title: '创建时间',
      dataIndex: 'created_time',
      key: 'created_time',
      render: (time: string) => formatDate(time),
      sorter: (a: FileInfo, b: FileInfo) => new Date(a.created_time).getTime() - new Date(b.created_time).getTime(),
    },
    {
      title: '修改时间',
      dataIndex: 'modified_time',
      key: 'modified_time',
      render: (time: string) => formatDate(time),
      sorter: (a: FileInfo, b: FileInfo) => new Date(a.modified_time).getTime() - new Date(b.modified_time).getTime(),
    },
  ];

  // 匹配物品表格列定义
  const matchedColumns = [
    {
      title: '预览',
      dataIndex: ['image_info', 'url'],
      key: 'preview',
      width: 80,
      render: (url: string) => (
        <Image
          width={50}
          height={50}
          src={url}
          style={{ objectFit: 'cover' }}
          placeholder={<div style={{ width: 50, height: 50, backgroundColor: '#f0f0f0' }} />}
        />
      ),
    },
    {
      title: '物品编码',
      dataIndex: 'item_code',
      key: 'item_code',
    },
    {
      title: '物品名称',
      dataIndex: 'item_name',
      key: 'item_name',
      ellipsis: true,
    },
    {
      title: '图片文件',
      dataIndex: 'image_filename',
      key: 'image_filename',
      ellipsis: true,
    },
    {
      title: '文件大小',
      dataIndex: ['image_info', 'size'],
      key: 'size',
      render: (size: number) => formatFileSize(size),
    },
  ];

  // 缺失图片表格列定义
  const missingColumns = [
    {
      title: '物品编码',
      dataIndex: 'item_code',
      key: 'item_code',
    },
    {
      title: '物品名称',
      dataIndex: 'item_name',
      key: 'item_name',
      ellipsis: true,
    },
    {
      title: '缺失文件',
      dataIndex: 'missing_filename',
      key: 'missing_filename',
      ellipsis: true,
    },
    {
      title: '状态',
      key: 'status',
      render: () => <Tag color="red" icon={<WarningOutlined />}>文件缺失</Tag>,
    },
  ];

  const rowSelection = {
    selectedRowKeys: selectedOrphanedImages,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedOrphanedImages(selectedRowKeys as string[]);
    },
    getCheckboxProps: (record: FileInfo) => ({
      name: record.filename,
    }),
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>
          <FileImageOutlined /> {t('imageManagement.title')}
        </Title>
        <Button 
          type="primary" 
          icon={<ReloadOutlined />} 
          onClick={refreshData}
          loading={loading}
        >
          {t('imageManagement.refreshData')}
        </Button>
      </div>

      <Spin spinning={loading}>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={t('imageManagement.overviewTab')} key="overview">
            {overview && (
              <div>
                <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
                  {Object.entries(overview.directories).map(([subDir, stats]) => (
                    <Col span={24} key={subDir}>
                      <Card title={`${subDir.toUpperCase()} ${t('imageManagement.directory')}`} size="small">
                        {stats.exists ? (
                          <Row gutter={16}>
                            <Col span={8}>
                              <Statistic
                                title={t('imageManagement.imageFiles')}
                                value={stats.images.count}
                                suffix={t('imageManagement.countSuffix')}
                                prefix={<FileImageOutlined />}
                              />
                            </Col>
                            <Col span={8}>
                              <Statistic
                                title={t('imageManagement.totalSize')}
                                value={formatFileSize(stats.total.size)}
                                prefix={<FileImageOutlined />}
                              />
                            </Col>
                            {stats.match_statistics && (
                              <Col span={8}>
                                <Statistic
                                  title={t('imageManagement.orphanedFiles')}
                                  value={stats.match_statistics.orphaned_count}
                                  suffix={t('imageManagement.countSuffix')}
                                  prefix={<WarningOutlined />}
                                  valueStyle={{ color: stats.match_statistics.orphaned_count > 0 ? '#ff4d4f' : '#52c41a' }}
                                />
                              </Col>
                            )}
                          </Row>
                        ) : (
                          <Alert message={t('imageManagement.directoryNotFound')} type="warning" />
                        )}
                      </Card>
                    </Col>
                  ))}
                </Row>
              </div>
            )}
          </TabPane>

          <TabPane tab={t('imageManagement.analysisTab')} key="analysis">
            {matchResult && (
              <div>
                {/* 统计卡片 */}
                <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title={t('imageManagement.totalItems')}
                        value={matchResult.statistics.total_items}
                        prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title={t('imageManagement.totalImages')}
                        value={matchResult.statistics.total_images}
                        prefix={<FileImageOutlined style={{ color: '#1890ff' }} />}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title={t('imageManagement.matchedSuccess')}
                        value={matchResult.statistics.matched_count}
                        prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title={t('imageManagement.orphanedFiles')}
                        value={matchResult.statistics.orphaned_count}
                        prefix={<WarningOutlined style={{ color: '#ff4d4f' }} />}
                        valueStyle={{ color: matchResult.statistics.orphaned_count > 0 ? '#ff4d4f' : '#52c41a' }}
                      />
                    </Card>
                  </Col>
                </Row>

                {/* 孤立图片处理 */}
                {matchResult.orphaned_images.length > 0 && (
                  <Card 
                    title={
                      <Space>
                        <WarningOutlined style={{ color: '#ff4d4f' }} />
                        {t('imageManagement.orphanedImages', { count: matchResult.orphaned_images.length })}
                      </Space>
                    }
                    extra={
                      <Space>
                        <Text type="secondary">
                          {t('imageManagement.totalOrphanedSize')}: {formatFileSize(matchResult.statistics.total_orphaned_size)}
                        </Text>
                        <Popconfirm
                          title={t('imageManagement.confirmDeleteOrphanedImages')}
                          description={t('imageManagement.deleteOrphanedImagesDescription')}
                          onConfirm={() => setDeleteModalVisible(true)}
                          disabled={selectedOrphanedImages.length === 0}
                        >
                          <Button 
                            danger 
                            icon={<DeleteOutlined />}
                            disabled={selectedOrphanedImages.length === 0}
                          >
                            {t('imageManagement.deleteSelected', { count: selectedOrphanedImages.length })}
                          </Button>
                        </Popconfirm>
                      </Space>
                    }
                    style={{ marginBottom: '24px' }}
                  >
                    <Table
                      dataSource={matchResult.orphaned_images}
                      columns={orphanedColumns}
                      rowKey="filename"
                      size="small"
                      rowSelection={rowSelection}
                      pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `${t('imageManagement.pageRange', { start: range[0], end: range[1], total: total })}`,
                      }}
                    />
                  </Card>
                )}

                {/* 缺失图片 */}
                {matchResult.missing_images.length > 0 && (
                  <Card 
                    title={
                      <Space>
                        <ExclamationCircleOutlined style={{ color: '#faad14' }} />
                        {t('imageManagement.missingImages', { count: matchResult.missing_images.length })}
                      </Space>
                    }
                    style={{ marginBottom: '24px' }}
                  >
                    <Table
                      dataSource={matchResult.missing_images}
                      columns={missingColumns}
                      rowKey="item_id"
                      size="small"
                      pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `${t('imageManagement.pageRange', { start: range[0], end: range[1], total: total })}`,
                      }}
                    />
                  </Card>
                )}

                {/* 匹配成功的物品 */}
                <Card 
                  title={
                    <Space>
                      <CheckCircleOutlined style={{ color: '#52c41a' }} />
                      {t('imageManagement.matchedItems', { count: matchResult.matched_items.length })}
                    </Space>
                  }
                >
                  <Table
                    dataSource={matchResult.matched_items}
                    columns={matchedColumns}
                    rowKey="item_id"
                    size="small"
                    pagination={{
                      pageSize: 10,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total, range) => `${t('imageManagement.pageRange', { start: range[0], end: range[1], total: total })}`,
                    }}
                  />
                </Card>
              </div>
            )}
          </TabPane>
        </Tabs>
      </Spin>

      {/* 删除确认模态框 */}
      <Modal
        title={t('imageManagement.confirmDeleteOrphanedImagesTitle')}
        open={deleteModalVisible}
        onOk={deleteOrphanedImages}
        onCancel={() => setDeleteModalVisible(false)}
        okText={t('imageManagement.confirmDelete')}
        cancelText={t('imageManagement.cancel')}
        okButtonProps={{ danger: true, loading }}
        width={600}
      >
        <div>
          <Alert
            message={t('imageManagement.warning')}
            description={t('imageManagement.deleteOrphanedImagesWarning')}
            type="warning"
            showIcon
            style={{ marginBottom: '16px' }}
          />
          <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
            {selectedOrphanedImages.map((filename, index) => {
              const fileInfo = matchResult?.orphaned_images.find(img => img.filename === filename);
              return (
                <div key={filename} style={{ 
                  padding: '8px', 
                  borderBottom: index < selectedOrphanedImages.length - 1 ? '1px solid #f0f0f0' : 'none',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <Text ellipsis style={{ flex: 1 }}>{filename}</Text>
                  {fileInfo && (
                    <Text type="secondary">{formatFileSize(fileInfo.size)}</Text>
                  )}
                </div>
              );
            })}
          </div>
          <Divider />
          <Text strong>
            {t('imageManagement.totalCount', { count: selectedOrphanedImages.length })}: {selectedOrphanedImages.length} {t('imageManagement.files')},
            {matchResult && formatFileSize(
              selectedOrphanedImages.reduce((total, filename) => {
                const fileInfo = matchResult.orphaned_images.find(img => img.filename === filename);
                return total + (fileInfo?.size || 0);
              }, 0)
            )}
          </Text>
        </div>
      </Modal>
    </div>
  );
};

export default ImageManagement;
