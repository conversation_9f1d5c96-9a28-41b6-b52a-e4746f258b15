import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import {
  Table,
  Card,
  Button,
  Space,
  Modal,
  Form,
  Input,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Row,
  Col,
  Statistic,
  Select,
  Switch,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { departmentService, Department } from '@admin/services/departmentService';
import { userService, User } from '@admin/services/userService';

// 通用错误处理函数
const handleApiError = (error: any, defaultMessage: string = '操作失败') => {
  if (error.response?.data?.detail) {
    const detail = error.response.data.detail;
    if (Array.isArray(detail)) {
      // 处理字段验证错误
      const errorMessages = detail.map((err: any) => {
        if (err.loc && err.msg) {
          const field = err.loc[err.loc.length - 1];
          return `${field}: ${err.msg}`;
        }
        return err.msg;
      });
      return errorMessages.join(', ');
    } else {
      return detail;
    }
  }
  return defaultMessage;
};

const { Option } = Select;
const { Search } = Input;
const { TextArea } = Input;

interface DepartmentManagementProps {}

const DepartmentManagement: React.FC<DepartmentManagementProps> = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [departments, setDepartments] = useState<Department[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [detailForm] = Form.useForm();

  // 筛选状态
  const [filters, setFilters] = useState({
    search: '',
    is_active: true, // 默认筛选有效状态
  });

  // 加载部门列表
  const loadDepartments = useCallback(async () => {
    setLoading(true);
    try {
      const departments = await departmentService.getDepartments(filters);
      setDepartments(departments);
    } catch (error) {
      message.error(t('messages.loadDepartmentListFailed'));
    } finally {
      setLoading(false);
    }
  }, [filters, t]);

  // 加载用户列表（用于选择部门经理）
  const loadUsers = async () => {
    try {
      const response = await userService.getUsers();
      setUsers(response.items);
    } catch (error) {
      message.error(t('messages.loadUserListFailed'));
    }
  };

  useEffect(() => {
    loadDepartments();
  }, [loadDepartments]);

  useEffect(() => {
    loadUsers();
  }, []);

  // 处理搜索
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
  };

  // 显示创建模态框
  const showCreateModal = () => {
    setSelectedDepartment(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 显示编辑模态框
  const showEditModal = (department: Department) => {
    setSelectedDepartment(department);
    form.setFieldsValue({
      name: department.name,
      code: department.code,
      description: department.description,
      is_active: department.is_active,
    });
    setModalVisible(true);
  };

  // 显示详情模态框
  const showDetailModal = (department: Department) => {
    setSelectedDepartment(department);
    detailForm.setFieldsValue({
      name: department.name,
      code: department.code,
      description: department.description,
      is_active: department.is_active,
    });
    setDetailModalVisible(true);
  };

  // 保存部门
  const handleSaveDepartment = async (values: any) => {
    try {
      if (selectedDepartment) {
        await departmentService.updateDepartment(selectedDepartment.id, values);
        message.success(t('messages.departmentUpdateSuccess'));
      } else {
        await departmentService.createDepartment(values);
        message.success(t('messages.departmentCreateSuccess'));
      }
      setModalVisible(false);
      loadDepartments();
    } catch (error: any) {
      message.error(handleApiError(error, t('messages.operationFailed')));
    }
  };

  // 删除部门
  const handleDeleteDepartment = async (department: Department) => {
    try {
      await departmentService.deleteDepartment(department.id);
      message.success(t('messages.departmentDeleteSuccess'));
      loadDepartments();
    } catch (error: any) {
      message.error(handleApiError(error, t('messages.deleteFailed')));
    }
  };

  // 切换部门状态
  const handleToggleStatus = async (department: Department) => {
    try {
      await departmentService.toggleDepartmentStatus(department.id);
      message.success(t('messages.statusUpdateSuccess'));
      loadDepartments();
    } catch (error: any) {
      message.error(t('messages.statusUpdateFailed'));
    }
  };

  // 获取状态标签
  const getStatusTag = (isActive: boolean) => (
    <Tag color={isActive ? 'green' : 'red'}>
      {isActive ? t('messages.enabled') : t('messages.disabled')}
    </Tag>
  );

  // 表格列定义
  const columns: ColumnsType<Department> = [
    {
      title: t('messages.departmentCode'),
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: t('messages.departmentName'),
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (name: string, record: Department) => (
        <Button
          type="link"
          onClick={() => navigate(`/admin/departments/${record.id}`)}
          style={{ padding: 0, height: 'auto' }}
        >
          {name}
        </Button>
      ),
    },
    {
      title: t('messages.description'),
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },

    {
      title: t('messages.status'),
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (isActive) => getStatusTag(isActive),
    },
    {
      title: t('messages.createdAt'),
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (date) => dayjs(date).format(DATE_FORMATS.DATE_TIME),
    },
    {
      title: t('messages.actions'),
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title={t('messages.viewDetail')}>
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => showDetailModal(record)}
            />
          </Tooltip>
          <Tooltip title={t('messages.edit')}>
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => showEditModal(record)}
            />
          </Tooltip>
          <Tooltip title={record.is_active ? t('messages.disable') : t('messages.enable')}>
            <Button
              type="text"
              icon={<Switch size="small" checked={record.is_active} />}
              onClick={() => handleToggleStatus(record)}
            />
          </Tooltip>
          <Popconfirm
            title={t('messages.confirmDeleteDepartment')}
            onConfirm={() => handleDeleteDepartment(record)}
            okText={t('messages.confirm')}
            cancelText={t('messages.cancel')}
          >
            <Tooltip title={t('messages.delete')}>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 统计信息
  const totalDepartments = departments.length;
  const activeDepartments = departments.filter(d => d.is_active).length;
  const inactiveDepartments = totalDepartments - activeDepartments;

  return (
    <div style={{ padding: 24 }}>
      <Card>
        {/* 页面标题和操作 */}
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h2 style={{ margin: 0 }}>
              <TeamOutlined style={{ marginRight: 8 }} />
              {t('messages.departments')}
            </h2>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadDepartments}
              loading={loading}
            >
              {t('messages.refresh')}
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showCreateModal}
            >
              {t('messages.createDepartment')}
            </Button>
          </Space>
        </div>

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('messages.totalDepartments')}
                value={totalDepartments}
                prefix={<TeamOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('messages.activeDepartments')}
                value={activeDepartments}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('messages.inactiveDepartments')}
                value={inactiveDepartments}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 搜索和筛选 */}
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={8}>
              <Search
                placeholder={t('messages.searchDepartmentNameOrCode')}
                allowClear
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={6}>
              <Select
                placeholder={t('messages.statusFilter')}
                allowClear
                style={{ width: '100%' }}
                value={filters.is_active}
                onChange={(value) => setFilters(prev => ({ ...prev, is_active: value }))}
              >
                <Option value={true}>{t('messages.enabled')}</Option>
                <Option value={false}>{t('messages.disabled')}</Option>
              </Select>
            </Col>
          </Row>
        </div>

        {/* 部门表格 */}
        <Table
          columns={columns}
          dataSource={departments}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${t('messages.showing')} ${range[0]}-${range[1]} ${t('messages.of')} ${total} ${t('messages.total')}`,
          }}
        />
      </Card>

      {/* 创建/编辑模态框 */}
      <Modal
        title={selectedDepartment ? t('messages.editDepartment') : t('messages.createDepartment')}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveDepartment}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label={t('messages.departmentName')}
                rules={[{ required: true, message: t('messages.pleaseEnterDepartmentName') }]}
              >
                <Input placeholder={t('messages.pleaseEnterDepartmentName')} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label={t('messages.departmentCode')}
                rules={[{ required: true, message: t('messages.pleaseEnterDepartmentCode') }]}
              >
                <Input placeholder={t('messages.pleaseEnterDepartmentCode')} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label={t('messages.departmentDescription')}
          >
            <TextArea rows={3} placeholder={t('messages.pleaseEnterDepartmentDescription')} />
          </Form.Item>

          <Form.Item
            name="is_active"
            label={t('messages.status')}
            valuePropName="checked"
          >
            <Switch checkedChildren={t('messages.enabled')} unCheckedChildren={t('messages.disabled')} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 详情模态框 */}
      <Modal
        title={t('messages.departmentDetail')}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedDepartment && (
          <Form
            form={detailForm}
            layout="vertical"
            disabled
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="name" label={t('messages.departmentName')}>
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="code" label={t('messages.departmentCode')}>
                  <Input />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item name="description" label={t('messages.departmentDescription')}>
              <TextArea rows={3} />
            </Form.Item>

            <Form.Item name="is_active" label={t('messages.status')} valuePropName="checked">
              <Switch />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label={t('messages.createdAt')}>
                  <Input value={dayjs(selectedDepartment.created_at).format(DATE_FORMATS.DATE_TIME)} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label={t('messages.updatedAt')}>
                  <Input value={dayjs(selectedDepartment.updated_at).format(DATE_FORMATS.DATE_TIME)} />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default DepartmentManagement; 