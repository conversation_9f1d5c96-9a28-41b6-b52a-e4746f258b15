import React, { useState, useEffect, useCallback } from 'react';
import { Card, List, Pagination, Empty, Spin, message, Typography, Space } from 'antd';
import { BellOutlined, ReloadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { notificationService } from '../services/notificationService';
import { Notification, NotificationListParams } from '../types/notification';
import NotificationFilter from '../components/notifications/NotificationFilter';
import NotificationCard from '../components/notifications/NotificationCard';
import NotificationBatchActions from '../components/notifications/NotificationBatchActions';

const { Title } = Typography;

const NotificationList: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  // 状态管理
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [batchLoading, setBatchLoading] = useState(false);
  
  // 从URL获取筛选参数
  const getFilterParams = (): NotificationListParams => {
    return {
      page: parseInt(searchParams.get('page') || '1'),
      size: parseInt(searchParams.get('size') || '20'),
      notification_type: searchParams.get('type') || undefined,
      status: searchParams.get('status') || undefined,
      search: searchParams.get('search') || undefined,
      start_date: searchParams.get('start_date') || undefined,
      end_date: searchParams.get('end_date') || undefined,
    };
  };

  // 更新URL参数
  const updateSearchParams = (params: Partial<NotificationListParams>) => {
    const currentParams = new URLSearchParams(searchParams);
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        currentParams.set(key, String(value));
      } else {
        currentParams.delete(key);
      }
    });
    
    // 重置到第一页
    if (Object.keys(params).some(key => key !== 'page' && key !== 'size')) {
      currentParams.set('page', '1');
    }
    
    setSearchParams(currentParams);
  };

  // 加载通知列表
  const loadNotifications = useCallback(async (params: NotificationListParams = {}) => {
    try {
      setLoading(true);
      const response = await notificationService.getNotifications(params);
      setNotifications(response.data);
      setTotal(response.total);
    } catch (error) {
      console.error('Failed to load notifications:', error);
      message.error(t('notifications.load_error'));
    } finally {
      setLoading(false);
    }
  }, [t]);

  // 初始加载
  useEffect(() => {
    const params = getFilterParams();
    loadNotifications(params);
  }, [loadNotifications]);

  // 处理筛选
  const handleFilter = (params: NotificationListParams) => {
    updateSearchParams(params);
    loadNotifications(params);
  };

  // 处理重置
  const handleReset = () => {
    setSearchParams({ page: '1', size: '20' });
    loadNotifications({ page: 1, size: 20 });
  };

  // 处理分页
  const handlePageChange = (page: number, size: number) => {
    updateSearchParams({ page, size });
    loadNotifications({ ...getFilterParams(), page, size });
  };

  // 标记已读
  const handleMarkRead = async (id: number) => {
    try {
      await notificationService.markNotificationRead(id);
      message.success(t('notifications.mark_read_success'));
      
      // 更新本地状态
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, status: 'read' as const, read_at: new Date().toISOString() }
            : notification
        )
      );
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      message.error(t('notifications.mark_read_error'));
    }
  };

  // 删除通知
  const handleDelete = async (id: number) => {
    try {
      await notificationService.deleteNotification(id);
      message.success(t('notifications.delete_success'));
      
      // 更新本地状态
      setNotifications(prev => prev.filter(notification => notification.id !== id));
      setSelectedIds(prev => prev.filter(selectedId => selectedId !== id));
      
      // 如果当前页没有数据了，跳转到上一页
      const currentPage = getFilterParams().page;
      if (notifications.length === 1 && currentPage && currentPage > 1) {
        const newPage = currentPage - 1;
        updateSearchParams({ page: newPage });
        loadNotifications({ ...getFilterParams(), page: newPage });
      }
    } catch (error) {
      console.error('Failed to delete notification:', error);
      message.error(t('notifications.delete_error'));
    }
  };

  // 查看通知详情
  const handleView = (id: number) => {
    navigate(`/admin/notifications/${id}`);
  };

  // 选择通知
  const handleSelect = (id: number, selected: boolean) => {
    if (selected) {
      setSelectedIds(prev => [...prev, id]);
    } else {
      setSelectedIds(prev => prev.filter(selectedId => selectedId !== id));
    }
  };

  // 批量标记已读
  const handleBatchMarkRead = async () => {
    try {
      setBatchLoading(true);
      await notificationService.batchMarkRead({ notification_ids: selectedIds });
      message.success(t('notifications.batch.mark_read_success'));
      
      // 更新本地状态
      setNotifications(prev => 
        prev.map(notification => 
          selectedIds.includes(notification.id)
            ? { ...notification, status: 'read' as const, read_at: new Date().toISOString() }
            : notification
        )
      );
      
      setSelectedIds([]);
    } catch (error) {
      console.error('Failed to batch mark notifications as read:', error);
      message.error(t('notifications.batch.mark_read_error'));
    } finally {
      setBatchLoading(false);
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    try {
      setBatchLoading(true);
      await notificationService.batchDeleteNotifications({ notification_ids: selectedIds });
      message.success(t('notifications.batch.delete_success'));
      
      // 更新本地状态
      setNotifications(prev => prev.filter(notification => !selectedIds.includes(notification.id)));
      setSelectedIds([]);
      
      // 重新加载数据
      const params = getFilterParams();
      loadNotifications(params);
    } catch (error) {
      console.error('Failed to batch delete notifications:', error);
      message.error(t('notifications.batch.delete_error'));
    } finally {
      setBatchLoading(false);
    }
  };

  // 清除选择
  const handleClearSelection = () => {
    setSelectedIds([]);
  };

  // 刷新数据
  const handleRefresh = () => {
    const params = getFilterParams();
    loadNotifications(params);
  };

  return (
    <div>
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <BellOutlined style={{ fontSize: 20, color: '#1890ff' }} />
            <Title level={3} style={{ margin: 0 }}>
              {t('notifications.title')}
            </Title>
          </Space>
          <button
            onClick={handleRefresh}
            disabled={loading}
            style={{ 
              border: 'none', 
              background: 'none', 
              cursor: 'pointer',
              fontSize: '16px',
              color: '#1890ff'
            }}
          >
            <ReloadOutlined spin={loading} />
          </button>
        </div>
      </Card>

      <NotificationFilter
        onFilter={handleFilter}
        onReset={handleReset}
        loading={loading}
      />

      <NotificationBatchActions
        selectedIds={selectedIds}
        onBatchMarkRead={handleBatchMarkRead}
        onBatchDelete={handleBatchDelete}
        onClearSelection={handleClearSelection}
        loading={batchLoading}
      />

      <Card>
        <Spin spinning={loading}>
          {notifications.length > 0 ? (
            <>
              <List
                dataSource={notifications}
                renderItem={(notification) => (
                  <List.Item key={notification.id} style={{ padding: 0 }}>
                    <NotificationCard
                      notification={notification}
                      onMarkRead={handleMarkRead}
                      onDelete={handleDelete}
                      onView={handleView}
                      selected={selectedIds.includes(notification.id)}
                      onSelect={handleSelect}
                    />
                  </List.Item>
                )}
                pagination={false}
              />
              
              <div style={{ 
                display: 'flex', 
                justifyContent: 'center', 
                marginTop: 24 
              }}>
                <Pagination
                  current={getFilterParams().page}
                  pageSize={getFilterParams().size}
                  total={total}
                  showSizeChanger
                  showQuickJumper
                  showTotal={(total, range) => 
                    t('common.pagination_total', { 
                      start: range[0], 
                      end: range[1], 
                      total 
                    })
                  }
                  onChange={handlePageChange}
                  onShowSizeChange={handlePageChange}
                  pageSizeOptions={['10', '20', '50', '100']}
                />
              </div>
            </>
          ) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={t('notifications.no_data')}
            />
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default NotificationList;
