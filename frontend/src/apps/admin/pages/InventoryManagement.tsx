import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Tabs,
  message,
  Modal,
} from 'antd';
import { useAuth } from '../contexts/AuthContext';
import { inventoryApi } from '@admin/services/inventoryApi';
import { purchaseCartService } from '@admin/services/purchaseCartService';
import {
  InventoryOverview,
  InventoryList,
  InventoryChanges,
  InventoryAlerts,
  InventoryStatistics,
  InventoryDetail,
  InventoryForms
} from '@admin/components/inventory';
import { apiClient } from '@admin/services/authService';

const { TabPane } = Tabs;

interface InventoryItem {
  id: number;
  department_id: number;
  department_name: string;
  item_id: number;
  item_name: string;
  item_code: string;
  current_quantity: number;
  min_quantity: number;
  max_quantity?: number;
  status: string;
  storage_location?: string;
  rack_number?: string;
  purchase_unit: string;
  inventory_unit: string;
  qty_per_up: number;
  purchase_unit_quantity?: number;
  last_purchase_price?: number;
  average_cost?: number;
  total_value?: number;
  last_updated: string;
  created_at: string;
  updated_at: string;
  item_image_url?: string;
  has_inventory?: boolean;
  is_active?: boolean;
}

interface InventoryChangeRecord {
  id: number;
  department_id: number;
  department_name: string;
  item_id: number;
  item_name: string;
  item_code: string;
  before_quantity: number;
  after_quantity: number;
  change_quantity: number;
  change_type: string;
  change_reason?: string;
  operator_id: number;
  operator_name: string;
  change_date: string;
  remarks?: string;
  created_at: string;
}

interface InventoryAlert {
  id: number;
  department_id: number;
  department_name: string;
  item_id: number;
  item_name: string;
  item_code: string;
  alert_type: string;
  alert_level: string;
  message: string;
  current_stock: number;
  threshold_value: number;
  is_active: boolean;
  is_resolved: boolean;
  resolved_by?: number;
  resolved_at?: string;
  resolver_name?: string;
  created_at: string;
  updated_at: string;
}

interface UsageStatisticsItem {
  item_id: number;
  department_id: number;
  item_name: string;
  item_code: string;
  department_name: string;
  total_usage: number;
  usage_count: number;
  avg_unit_price: number;
  first_usage: string;
  last_usage: string;
  unit: string;
}

const InventoryManagement: React.FC = () => {
  const { t } = useTranslation();
  
  // 获取当前用户信息
  const { user: currentUser } = useAuth();
  
  // URL状态管理
  const [searchParams, setSearchParams] = useSearchParams();
  
  // 详情抽屉 - 从URL同步状态
  const detailVisible = searchParams.get('detail') === 'true';
  const selectedRecordId = searchParams.get('recordId');
  const [selectedRecord, setSelectedRecord] = useState<InventoryItem | null>(null);
  
  // 手工入库抽屉 - 从URL同步状态
  const manualInVisible = searchParams.get('manualIn') === 'true';
  const manualInRecordId = searchParams.get('manualInRecordId');
  
  // 库存调整抽屉 - 从URL同步状态
  const adjustVisible = searchParams.get('adjust') === 'true';
  const adjustRecordId = searchParams.get('adjustRecordId');
  
  // 编辑库存设置抽屉 - 从URL同步状态
  const editInventoryVisible = searchParams.get('editInventory') === 'true';
  const editInventoryRecordId = searchParams.get('editInventoryRecordId');
  
  // 标签页状态 - 从URL同步
  const activeTab = searchParams.get('tab') || 'overview';
  
  // 状态筛选 - 从URL同步，如果没有则默认为"已启用"
  const statusFilter = searchParams.get('status') || 'enabled';
  
  // 管理标签页状态
  const setActiveTab = useCallback((tab: string) => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('tab', tab);
      return newParams;
    });
  }, [setSearchParams]);
  
  const [loading, setLoading] = useState(false);
  
  // 库存概览数据
  const [overviewData, setOverviewData] = useState({
    total_items: 0,
    total_departments: 0,
    low_stock_items: 0,
    out_of_stock_items: 0,
    overstock_items: 0,
    normal_stock_items: 0,
    total_value: 0,
    total_value_usd: 0,
    active_alerts: 0
  });
  
  // 库存列表数据
  const [inventoryList, setInventoryList] = useState<InventoryItem[]>([]);
  const [inventoryPagination, setInventoryPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [inventoryFilters, setInventoryFilters] = useState({
    item_id: undefined as number | undefined,
    status: 'enabled' as string | undefined, // 默认选中"已启用"
    search: '',
    include_all_items: true,
  });
  // Keep latest inventory filters in a ref to avoid stale closures and unnecessary callback recreation
  const inventoryFiltersRef = React.useRef(inventoryFilters);
  useEffect(() => {
    inventoryFiltersRef.current = inventoryFilters;
  }, [inventoryFilters]);

  // Dedup key for last inventory request to avoid duplicate refetches with the same params
  const lastInventoryQueryKeyRef = React.useRef<string | null>(null);
 
  // 变更记录数据
  const [changeRecords, setChangeRecords] = useState<InventoryChangeRecord[]>([]);
  const [changePagination, setChangePagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [changeFilters, setChangeFilters] = useState({
    item_id: undefined as number | undefined,
    change_type: undefined as string | undefined,
    operator_id: undefined as number | undefined,
    start_date: undefined as string | undefined,
    end_date: undefined as string | undefined
  });
  
  // 预警数据
  const [alerts, setAlerts] = useState<InventoryAlert[]>([]);
  const [alertPagination, setAlertPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [alertFilters, setAlertFilters] = useState({
    item_id: undefined as number | undefined,
    alert_type: undefined as string | undefined,
    alert_level: undefined as string | undefined,
    is_resolved: undefined as boolean | undefined,
    is_active: true
  });
  
  // 使用统计数据
  const [usageStatistics, setUsageStatistics] = useState<UsageStatisticsItem[]>([]);
  const [usagePagination, setUsagePagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [usageFilters, setUsageFilters] = useState({
    item_id: undefined as number | undefined,
    year: undefined as number | undefined,
    month: undefined as number | undefined
  });

  // 手工入库相关状态
  const [manualInLoading, setManualInLoading] = useState(false);
  const [selectedItemForManualIn, setSelectedItemForManualIn] = useState<InventoryItem | null>(null);

  // 库存调整
  const [adjustLoading, setAdjustLoading] = useState(false);

  // 编辑库存设置
  const [editInventoryLoading, setEditInventoryLoading] = useState(false);
  
  // 管理详情抽屉状态
  const setDetailVisible = useCallback((visible: boolean, record?: any) => {
    if (visible && record) {
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.set('detail', 'true');
        newParams.set('recordId', record.id.toString());
        return newParams;
      });
      setSelectedRecord(record);
    } else {
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.delete('detail');
        newParams.delete('recordId');
        return newParams;
      });
      setSelectedRecord(null);
    }
  }, [setSearchParams]);

  // 管理手工入库抽屉状态
  const setManualInVisible = useCallback((visible: boolean, record?: InventoryItem) => {
    if (visible && record) {
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.set('manualIn', 'true');
        newParams.set('manualInRecordId', record.id.toString());
        return newParams;
      });
      setSelectedItemForManualIn(record);
    } else {
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.delete('manualIn');
        newParams.delete('manualInRecordId');
        return newParams;
      });
      setSelectedItemForManualIn(null);
    }
  }, [setSearchParams]);

  // 管理库存调整抽屉状态
  const setAdjustVisible = useCallback((visible: boolean, record?: InventoryItem) => {
    if (visible && record) {
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.set('adjust', 'true');
        newParams.set('adjustRecordId', record.id.toString());
        return newParams;
      });
      setSelectedItemForManualIn(record);
    } else {
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.delete('adjust');
        newParams.delete('adjustRecordId');
        return newParams;
      });
      setSelectedItemForManualIn(null);
    }
  }, [setSearchParams]);

  // 管理编辑库存设置抽屉状态
  const setEditInventoryVisible = useCallback((visible: boolean, record?: InventoryItem) => {
    if (visible && record) {
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.set('editInventory', 'true');
        newParams.set('editInventoryRecordId', record.id.toString());
        return newParams;
      });
      setSelectedItemForManualIn(record);
    } else {
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.delete('editInventory');
        newParams.delete('editInventoryRecordId');
        return newParams;
      });
      setSelectedItemForManualIn(null);
    }
  }, [setSearchParams]);
  
  // 根据URL状态加载选中的记录
  useEffect(() => {
    if (detailVisible && selectedRecordId && inventoryList.length > 0) {
      const record = inventoryList.find(item => item.id.toString() === selectedRecordId);
      if (record) {
        setSelectedRecord(record);
      }
    }
  }, [detailVisible, selectedRecordId, inventoryList]);

  // 根据URL状态恢复手工入库抽屉状态
  useEffect(() => {
    if (manualInVisible && manualInRecordId && inventoryList.length > 0) {
      const record = inventoryList.find(item => item.id.toString() === manualInRecordId);
      if (record) {
        setSelectedItemForManualIn(record);
      }
    }
  }, [manualInVisible, manualInRecordId, inventoryList]);

  // 根据URL状态恢复库存调整抽屉状态
  useEffect(() => {
    if (adjustVisible && adjustRecordId && inventoryList.length > 0) {
      const record = inventoryList.find(item => item.id.toString() === adjustRecordId);
      if (record) {
        setSelectedItemForManualIn(record);
      }
    }
  }, [adjustVisible, adjustRecordId, inventoryList]);

  // 根据URL状态恢复编辑库存设置抽屉状态
  useEffect(() => {
    if (editInventoryVisible && editInventoryRecordId && inventoryList.length > 0) {
      const record = inventoryList.find(item => item.id.toString() === editInventoryRecordId);
      if (record) {
        setSelectedItemForManualIn(record);
      }
    }
  }, [editInventoryVisible, editInventoryRecordId, inventoryList]);

  // 加载库存概览数据
  const fetchOverview = useCallback(async () => {
    try {
      setLoading(true);
      const response = await inventoryApi.getOverview();
      setOverviewData(response);
    } catch (error) {
      message.error('获取库存概览失败');
    } finally {
      setLoading(false);
    }
  }, []);

  // 加载库存列表数据
  const fetchInventoryList = useCallback(async (page = 1, filters?: any, forceRefresh: boolean = false) => {
    try {
      setLoading(true);
      const currentFilters = filters || inventoryFiltersRef.current;
      const params = {
        skip: (page - 1) * inventoryPagination.pageSize,
        limit: inventoryPagination.pageSize,
        ...currentFilters,
      };
      
      // 当状态为"所有物品"时，设置include_all_items为true，并移除status参数
      if (currentFilters.status === 'all_items') {
        params.include_all_items = true;
        delete params.status;
      }
      // Build a query key and short-circuit if identical to last one
      const queryKey = JSON.stringify({ page, params });
      if (!forceRefresh && lastInventoryQueryKeyRef.current === queryKey) {
        return;
      }
      lastInventoryQueryKeyRef.current = queryKey;
      const response = await inventoryApi.getDepartmentInventories(params);
      
      // 处理新的响应格式
      const items = response?.items || [];
      const total = response?.total || 0;
      
      const processedData = items.map((item: InventoryItem) => ({
        ...item,
        has_inventory: item.has_inventory ?? false
      }));
      
      setInventoryList(processedData);
      setInventoryPagination({
        current: page,
        pageSize: inventoryPagination.pageSize,
        total: total
      });
    } catch (error) {
      message.error('获取库存列表失败');
    } finally {
      setLoading(false);
    }
  }, [inventoryPagination.pageSize]);

  // 加载变更记录数据
  const fetchChangeRecords = useCallback(async (page = 1, filters?: any) => {
    try {
      setLoading(true);
      const currentFilters = filters || changeFilters;
      const params = {
        skip: (page - 1) * changePagination.pageSize,
        limit: changePagination.pageSize,
        ...currentFilters
      };
      const response = await inventoryApi.getInventoryChangeRecords(params);
      setChangeRecords(response || []);
      setChangePagination({
        current: page,
        pageSize: 20,
        total: response?.length || 0
      });
    } catch (error) {
      message.error('获取变更记录失败');
    } finally {
      setLoading(false);
    }
  }, [changePagination.pageSize, changeFilters]);

  // 加载预警数据
  const fetchAlerts = useCallback(async (page = 1, filters?: any) => {
    try {
      setLoading(true);
      const currentFilters = filters || alertFilters;
      const params = {
        skip: (page - 1) * alertPagination.pageSize,
        limit: alertPagination.pageSize,
        ...currentFilters
      };
      const response = await inventoryApi.getInventoryAlerts(params);
      setAlerts(response || []);
      setAlertPagination({
        current: page,
        pageSize: 20,
        total: response?.length || 0
      });
    } catch (error) {
      message.error('获取预警信息失败');
    } finally {
      setLoading(false);
    }
  }, [alertPagination.pageSize, alertFilters]);

  // 加载使用统计数据
  const fetchUsageStatistics = useCallback(async (page = 1, filters?: any) => {
    try {
      setLoading(true);
      const currentFilters = filters || usageFilters;
      const params = {
        skip: (page - 1) * usagePagination.pageSize,
        limit: usagePagination.pageSize,
        ...currentFilters
      };
      const response = await inventoryApi.getUsageStatistics(params);
      setUsageStatistics(response || []);
      setUsagePagination({
        current: page,
        pageSize: 20,
        total: response?.length || 0
      });
    } catch (error) {
      message.error('获取使用统计失败');
    } finally {
      setLoading(false);
    }
  }, [usagePagination.pageSize, usageFilters]);

  // 打开手工入库窗口
  const openManualIn = (record?: InventoryItem) => {
    if (record) {
      setManualInVisible(true, record);
    } else {
      setManualInVisible(true);
    }
  };

  // 处理手工入库
  const handleManualIn = async (values: any) => {
    try {
      setManualInLoading(true);
      
      // 确保有选中的物品和当前用户
      if (!selectedItemForManualIn || !currentUser) {
        message.error('未找到选中的物品或用户信息');
        return;
      }
      
      const requestData: any = {
        ...values,
        department_id: currentUser.department_id,
        // 当 qty_per_up=1 时，quantity_unit 固定为 inventory
        quantity_unit: selectedItemForManualIn.qty_per_up === 1 ? 'inventory' : (values.quantity_unit || 'purchase')
      };
      
      const response = await inventoryApi.processManualIn(requestData);
      
      if (response.success) {
        message.success('手工入库成功');
        setManualInVisible(false);
        fetchInventoryList(1, undefined, true);
      } else {
        message.error(response.message || '手工入库失败');
      }
    } catch (error) {
      message.error('手工入库失败');
    } finally {
      setManualInLoading(false);
    }
  };

  // 打开库存调整窗口
  const openAdjust = (record?: InventoryItem) => {
    if (record) {
      setAdjustVisible(true, record);
    } else {
      setAdjustVisible(true);
    }
  };

  // 处理库存调整
  const handleAdjust = async (values: any) => {
    try {
      setAdjustLoading(true);
      
      // 确保有选中的物品和当前用户
      const record = selectedItemForManualIn;
      if (!record || !currentUser) {
        message.error('未找到选中的物品或用户信息');
        return;
      }
      
      const requestData: any = {
        ...values,
        item_id: record.item_id,
        department_id: currentUser.department_id,
        // 库存调整默认使用库存单位，当 qty_per_up=1 时固定为 inventory
        quantity_unit: 'inventory'
      };
      
      const response = await inventoryApi.adjustStock(requestData);
      
      if (response.success) {
        message.success('库存调整成功');
        setAdjustVisible(false);
        fetchInventoryList(inventoryPagination.current || 1, undefined, true);
      } else {
        message.error(response.message || '库存调整失败');
      }
    } catch (error) {
      message.error('库存调整失败');
    } finally {
      setAdjustLoading(false);
    }
  };

  // 打开编辑库存设置窗口
  const openEditInventory = (record: InventoryItem) => {
    setEditInventoryVisible(true, record);
  };

  // 处理编辑库存设置
  const handleEditInventory = async (values: any) => {
    try {
      setEditInventoryLoading(true);
      
      // 检查是否有库存记录
      const record = selectedItemForManualIn;
      if (!record) {
        message.error('未找到选中的物品');
        return;
      }
      
      let response;
      if (record.has_inventory) {
        // 有库存记录，更新设置
        response = await inventoryApi.updateInventorySettings({
          id: record.id,
          item_id: record.item_id,
          min_quantity: values.min_quantity,
          max_quantity: values.max_quantity,
          storage_location: values.storage_location,
          rack_number: values.rack_number,
          notes: values.notes,
          is_active: values.is_active
        });
      } else {
        // 无库存记录，检查是否启用库存管理
        if (values.is_active) {
          // 启用库存管理，启动库存
          response = await inventoryApi.startInventory({
            item_id: record.item_id,
            min_quantity: values.min_quantity,
            max_quantity: values.max_quantity,
            storage_location: values.storage_location,
            rack_number: values.rack_number,
            notes: values.notes
          });
        } else {
          // 不启用库存管理，不创建记录，直接返回成功
          response = { success: true, message: '设置已保存' };
        }
      }
      
      if (response.success) {
        const messageText = record.has_inventory 
          ? '库存设置更新成功' 
          : (values.is_active ? '库存启动成功' : '设置已保存');
        message.success(messageText);
        setEditInventoryVisible(false);
        fetchInventoryList(inventoryPagination.current || 1, undefined, true);
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      message.error('操作失败');
    } finally {
      setEditInventoryLoading(false);
    }
  };

  // 一键添加缺货物品到购物车
  const handleAddOutOfStockToCart = async () => {
    try {
      setLoading(true);
      
      // 获取当前部门的购物车
      const currentDepartmentId = currentUser?.department_id;
      if (!currentDepartmentId) {
        message.error('用户未分配部门');
        return;
      }
      
      // 获取购物车中的现有物品
      const existingCartItems = await purchaseCartService.getDepartmentCartItems(currentDepartmentId);
      const cartItemMap = new Map();
      existingCartItems.forEach(item => {
        cartItemMap.set(item.item_id, item);
      });
      
      // 找出所有缺货物品（库存小于最小库存）
      const outOfStockItems = inventoryList.filter(inventory => 
        inventory.current_quantity < inventory.min_quantity
      );
      
      if (outOfStockItems.length === 0) {
        message.info('当前没有缺货物品');
        return;
      }
      
      let addedCount = 0;
      let updatedCount = 0;
      const results = [];
      
      // 处理每个缺货物品
      for (const inventory of outOfStockItems) {
        try {
          // 计算需要补充的库存数量
          const targetQuantity = inventory.max_quantity || (inventory.min_quantity * 2);
          const inventoryQuantityNeeded = Math.max(0, targetQuantity - inventory.current_quantity);
          
          if (inventoryQuantityNeeded <= 0) {
            continue; // 库存充足，跳过
          }
          
          // 将库存数量转换为采购单位数量
          // 使用 qty_per_up 进行单位转换
          const qtyPerUp = inventory.qty_per_up || 1;
          const purchaseUnitQuantityNeeded = Math.ceil(inventoryQuantityNeeded / qtyPerUp);
          
          // 确保采购单位数量至少为1
          if (purchaseUnitQuantityNeeded <= 0) {
            continue;
          }
          
          // 由于采购必须以SPQ的整数倍进行，需要向上取整到最近的SPQ倍数
          // 但是不能超过最大库存限制
          // 从物品的优先供应商配置中获取SPQ值
          let spq = 1; // 默认值
          try {
            // 获取物品详情，包含供应商信息
            const itemDetailResponse = await apiClient.get(`/items/${inventory.item_id}/detail`);
            const itemDetail = itemDetailResponse.data;
            
            // 找到优先级最高的供应商（priority = 0）
            if (itemDetail.suppliers && itemDetail.suppliers.length > 0) {
              const preferredSupplier = itemDetail.suppliers.find((s: any) => s.priority === 0) || itemDetail.suppliers[0];
              if (preferredSupplier && preferredSupplier.spq_quantity) {
                spq = preferredSupplier.spq_quantity;
              }
            }
          } catch (error) {
            console.warn(`获取物品 ${inventory.item_name} 的SPQ信息失败，使用默认值:`, error);
            // 如果获取失败，继续使用默认值
          }
          
          // 计算需要采购的SPQ数量，但不能超过目标库存限制
          // 关键逻辑：采购后总库存不能超过目标库存
          const currentPurchaseUnitQuantity = inventory.current_quantity / qtyPerUp; // 当前库存（采购单位）
          const targetPurchaseUnitQuantity = targetQuantity / qtyPerUp; // 目标库存（采购单位）
          const maxAdditionalPurchaseQuantity = targetPurchaseUnitQuantity - currentPurchaseUnitQuantity; // 最大可采购数量
          
          // 计算SPQ数量，既要满足需求，又不能超过最大可采购数量
          // 使用 Math.floor 确保采购后不超过目标库存
          const spqQuantityNeeded = Math.min(
            Math.ceil(purchaseUnitQuantityNeeded / spq) * spq,
            Math.floor(maxAdditionalPurchaseQuantity / spq) * spq
          );
          
          // 如果计算出的SPQ数量为0，说明无法在SPQ限制和目标库存限制下进行采购
          if (spqQuantityNeeded <= 0) {
            results.push({
              itemId: inventory.item_id,
              itemName: inventory.item_name,
              status: 'error',
              message: `无法采购：需要${purchaseUnitQuantityNeeded}${inventory.purchase_unit}，但受SPQ(${spq}${inventory.purchase_unit})和目标库存(${targetQuantity}${inventory.inventory_unit})限制`
            });
            continue;
          }
          
          if (cartItemMap.has(inventory.item_id)) {
            // 购物车已有该物品，更新数量
            const existingItem = cartItemMap.get(inventory.item_id);
            const newSpqCount = existingItem.spq_count + spqQuantityNeeded;
            
            await purchaseCartService.updateCartItem(existingItem.id, {
              spq_count: newSpqCount,
              notes: existingItem.notes ? `${existingItem.notes}; 补充缺货: ${spqQuantityNeeded} ${inventory.purchase_unit}` : `补充缺货: ${spqQuantityNeeded} ${inventory.purchase_unit}`
            });
            
            updatedCount++;
            results.push({
              item_name: inventory.item_name,
              action: 'updated',
              inventory_quantity_needed: inventoryQuantityNeeded,
              spq_quantity_added: spqQuantityNeeded,
              purchase_unit: inventory.purchase_unit,
              inventory_unit: inventory.inventory_unit,
              new_total: newSpqCount
            });
          } else {
            // 购物车没有该物品，添加新项目
            await purchaseCartService.addItemToCart(currentDepartmentId, {
              item_id: inventory.item_id,
              spq_count: spqQuantityNeeded,
              notes: `一键添加缺货物品，补充: ${spqQuantityNeeded} ${inventory.purchase_unit}`
            });
            
            addedCount++;
            results.push({
              item_name: inventory.item_name,
              action: 'added',
              inventory_quantity_needed: inventoryQuantityNeeded,
              spq_quantity_added: spqQuantityNeeded,
              purchase_unit: inventory.purchase_unit,
              inventory_unit: inventory.inventory_unit,
              new_total: spqQuantityNeeded
            });
          }
        } catch (error: any) {
          console.error(`处理物品 ${inventory.item_name} 时出错:`, error);
          results.push({
            item_name: inventory.item_name,
            action: 'error',
            error: error?.message || '添加失败'
          });
        }
      }
      
      // 显示结果
      if (addedCount > 0 || updatedCount > 0) {
        message.success(`成功处理 ${outOfStockItems.length} piece 缺货物品：新增 ${addedCount} piece，更新 ${updatedCount} piece`);
        
        // 显示详细结果
        const successResults = results.filter(r => r.action !== 'error');
        if (successResults.length > 0) {
          const resultText = successResults.map(r => {
            if (r.action === 'added' || r.action === 'updated') {
              return `${r.item_name}: ${r.action === 'added' ? t('inventory.added') : t('inventory.updated')} ${r.spq_quantity_added} ${r.purchase_unit} (${t('inventory.inventory')}: ${r.inventory_quantity_needed} ${r.inventory_unit})`;
            }
            return `${r.item_name}: ${t('inventory.processingFailed')}`;
          }).join('\n');
          
          Modal.info({
            title: t('inventory.addResultDetails'),
            content: (
              <div>
                <p>{t('inventory.successfullyProcessedItems')}:</p>
                <pre style={{ maxHeight: '200px', overflow: 'auto' }}>{resultText}</pre>
              </div>
            ),
            width: 500
          });
        }
      } else {
        message.warning(t('inventory.noItemsSuccessfullyAddedToCart'));
      }
      
    } catch (error: any) {
      console.error(t('inventory.addOutOfStockItemsToCartFailed'), error);
      message.error(error.response?.data?.detail || t('inventory.addFailedPleaseRetry'));
    } finally {
      setLoading(false);
    }
  };

  // 处理解决预警
  const handleResolveAlert = async (alertId: number) => {
    try {
      await inventoryApi.resolveAlert(alertId);
      message.success(t('inventory.alertResolved'));
      fetchAlerts();
    } catch (error) {
      message.error(t('inventory.resolveAlertFailed'));
    }
  };

  // 查看详情
  const handleViewDetail = (record: any) => {
    setDetailVisible(true, record);
  };

  // 初始化URL参数和默认筛选状态
  useEffect(() => {
    // 如果没有状态筛选参数，不自动设置，让用户自己选择
    // 这样可以避免URL被自动修改，提供更好的用户体验
  }, []);

  // 初始化数据加载（仅概览）
  useEffect(() => {
    if (activeTab === 'overview') {
      fetchOverview();
    }
  }, [activeTab]);

  // 处理URL状态筛选变化
  useEffect(() => {
    if (activeTab === 'inventory' && statusFilter) {
      // 只在URL状态筛选变化时更新本地筛选状态，避免循环
      setInventoryFilters(prev => {
        if (prev.status !== statusFilter) {
          return { ...prev, status: statusFilter };
        }
        return prev;
      });
    }
  }, [statusFilter, activeTab]);

  // 处理筛选条件变化后的数据加载（库存）
  useEffect(() => {
    if (activeTab === 'inventory') {
      fetchInventoryList(1, inventoryFiltersRef.current);
    }
  }, [inventoryFilters, activeTab]);

  // Stable page change handler to avoid creating a new function on each render
  const handleInventoryPageChange = useCallback((page: number, pageSize: number) => {
    // Update page size if changed
    setInventoryPagination(prev => ({ ...prev, pageSize }));
    // Explicitly fetch for the selected page with latest filters
    fetchInventoryList(page, inventoryFiltersRef.current);
  }, [fetchInventoryList]);

  // 处理变更记录筛选条件变化后的数据加载
  useEffect(() => {
    if (activeTab === 'changes') {
      fetchChangeRecords(1, changeFilters);
    }
  }, [changeFilters, activeTab]);

  // 处理预警筛选条件变化后的数据加载
  useEffect(() => {
    if (activeTab === 'alerts') {
      fetchAlerts(1, alertFilters);
    }
  }, [alertFilters, activeTab]);

  // 处理使用统计筛选条件变化后的数据加载
  useEffect(() => {
    if (activeTab === 'statistics') {
      fetchUsageStatistics(1, usageFilters);
    }
  }, [usageFilters, activeTab]);

  // 加载物品和部门选项
  useEffect(() => {
    // 这个功能已经不再需要，因为调整功能直接使用列表项的物品
  }, []);

  return (
    <div style={{ padding: '8px 24px 24px 24px' }}>
      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        style={{ marginTop: '-8px' }}
      >
        {/* 库存概览标签页 */}
        <TabPane tab={t('inventory.overviewTab')} key="overview">
          <InventoryOverview
            overviewData={overviewData}
            onRefresh={fetchOverview}
          />
        </TabPane>

        {/* 库存列表标签页 */}
        <TabPane tab={t('inventory.inventoryListTab')} key="inventory">
          <InventoryList
            inventoryList={inventoryList}
            loading={loading}
            pagination={inventoryPagination}
            filters={inventoryFilters}
            currentUser={currentUser}
            onSearch={(value) => {
              setInventoryFilters(prev => {
                const newFilters = { ...prev, search: value };
                return newFilters;
              });
            }}
            onStatusChange={(value) => {
              setInventoryFilters(prev => {
                let newFilters;
                if (value === 'all_items') {
                  // 选择"所有物品"时，设置特殊状态值，显示所有物品
                  newFilters = { ...prev, status: 'all_items' };
                  // 同步状态筛选到URL
                  setSearchParams(prevParams => {
                    const newParams = new URLSearchParams(prevParams);
                    newParams.set('status', 'all_items');
                    return newParams;
                  });
                } else {
                  // 其他状态筛选
                  newFilters = { ...prev, status: value };
                  // 同步状态筛选到URL
                  setSearchParams(prevParams => {
                    const newParams = new URLSearchParams(prevParams);
                    if (value) {
                      newParams.set('status', value);
                    } else {
                      newParams.delete('status');
                    }
                    return newParams;
                  });
                }
                return newFilters;
              });
            }}
            onViewDetail={handleViewDetail}
            onManualIn={openManualIn}
            onAdjust={openAdjust}
            onEditInventory={openEditInventory}
            onPageChange={handleInventoryPageChange}
            onAddOutOfStockToCart={handleAddOutOfStockToCart}
          />
        </TabPane>

        {/* 变更记录标签页 */}
        <TabPane tab={t('inventory.changesTab')} key="changes">
          <InventoryChanges
            changeRecords={changeRecords}
            loading={loading}
            pagination={changePagination}
            filters={changeFilters}
            onSearch={(value) => {
              // 这里需要根据实际API调整搜索逻辑
            }}
            onChangeTypeChange={(value) => {
              setChangeFilters(prev => {
                const newFilters = { ...prev, change_type: value };
                return newFilters;
              });
            }}
            onDateRangeChange={(dates) => {
              if (dates) {
                setChangeFilters(prev => {
                  const newFilters = {
                    ...prev,
                    start_date: dates[0]?.toISOString(),
                    end_date: dates[1]?.toISOString()
                  };
                  return newFilters;
                });
              }
            }}
            onRefresh={() => fetchChangeRecords()}
            onViewDetail={handleViewDetail}
          />
        </TabPane>

        {/* 预警标签页 */}
        <TabPane tab={t('inventory.alertsTab')} key="alerts">
          <InventoryAlerts
            alerts={alerts}
            loading={loading}
            pagination={alertPagination}
            filters={alertFilters}
            onSearch={(value) => {
              // 这里需要根据实际API调整搜索逻辑
            }}
            onAlertLevelChange={(value) => {
              setAlertFilters(prev => {
                const newFilters = { ...prev, alert_level: value };
                return newFilters;
              });
            }}
            onResolvedStatusChange={(value) => {
              setAlertFilters(prev => {
                const newFilters = { ...prev, is_resolved: value };
                return newFilters;
              });
            }}
            onRefresh={() => fetchAlerts()}
            onViewDetail={handleViewDetail}
            onResolveAlert={handleResolveAlert}
          />
        </TabPane>

        {/* 使用统计标签页 */}
        <TabPane tab={t('inventory.statisticsTab')} key="statistics">
          <InventoryStatistics
            usageStatistics={usageStatistics}
            loading={loading}
            pagination={usagePagination}
            filters={usageFilters}
            onSearch={(value) => {
              // 这里需要根据实际API调整搜索逻辑
            }}
            onYearChange={(value) => {
              setUsageFilters(prev => {
                const newFilters = { ...prev, year: value };
                return newFilters;
              });
            }}
            onMonthChange={(value) => {
              setUsageFilters(prev => {
                const newFilters = { ...prev, month: value };
                return newFilters;
              });
            }}
            onRefresh={() => fetchUsageStatistics()}
            onExport={() => message.info(t('inventory.exportFeatureInDevelopment'))}
          />
        </TabPane>
      </Tabs>

      {/* 库存详情抽屉 */}
      <InventoryDetail
        visible={detailVisible}
        selectedRecord={selectedRecord}
        onClose={() => setDetailVisible(false)}
      />

      {/* 库存相关表单 */}
      <InventoryForms
        manualInVisible={manualInVisible}
        manualInLoading={manualInLoading}
        selectedItemForManualIn={selectedItemForManualIn}
        onManualInClose={() => setManualInVisible(false)}
        onManualInSubmit={handleManualIn}
        adjustVisible={adjustVisible}
        adjustLoading={adjustLoading}
        onAdjustClose={() => setAdjustVisible(false)}
        onAdjustSubmit={handleAdjust}
        editInventoryVisible={editInventoryVisible}
        editInventoryLoading={editInventoryLoading}
        onEditInventoryClose={() => setEditInventoryVisible(false)}
        onEditInventorySubmit={handleEditInventory}
      />
    </div>
  );
};

export default InventoryManagement;