import React from 'react';
import { Result, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { LockOutlined } from '@ant-design/icons';

const NoPermission: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <Result
      icon={<LockOutlined style={{ color: '#ff4d4f' }} />}
      status="403"
      title={t('messages.noPermissionTitle')}
      subTitle={t('messages.noPermissionSubtitle')}
      extra={[
        <Button type="primary" key="home" onClick={() => navigate('/admin/')}>
          {t('messages.returnHome')}
        </Button>,
        <Button key="back" onClick={() => navigate(-1)}>
          {t('messages.returnPrevious')}
        </Button>,
      ]}
    />
  );
};

export default NoPermission; 