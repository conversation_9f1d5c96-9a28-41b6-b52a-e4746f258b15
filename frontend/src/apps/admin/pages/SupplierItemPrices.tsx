import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Row,
  Col,
  Button,
  Table,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  Space,
  message,
  Typography,
  Tag,
  Popconfirm,
  Spin,
  Alert
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import type { ColumnsType } from 'antd/es/table';
import { 
  supplierService, 
  supplierPriceService,
  ItemSupplier,
  SupplierPrice,
  SupplierPriceCreate
} from '@admin/services/supplierService';
import { exchangeRateService } from '@admin/services/exchangeRateService';
import { formatPriceDisplay, SUPPORTED_CURRENCIES } from '@shared/utils/exchangeRateUtils';
import PermissionGuard from '@shared/components/PermissionGuard';
import { PERMISSIONS } from '@shared/config/permissions';
import MultiCurrencyPriceDisplay from '@shared/components/MultiCurrencyPriceDisplay';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface SupplierItemPricesProps {}

const SupplierItemPrices: React.FC<SupplierItemPricesProps> = () => {
  const { t } = useTranslation();
  const { id, itemId } = useParams<{ id: string; itemId: string }>();
  const navigate = useNavigate();
  const [itemSupplier, setItemSupplier] = useState<ItemSupplier | null>(null);
  const [prices, setPrices] = useState<SupplierPrice[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedPrice, setSelectedPrice] = useState<SupplierPrice | null>(null);
  const [form] = Form.useForm();
  const [exchangeRates, setExchangeRates] = useState<{[key: string]: number}>({});

  // 获取物品供应商关联信息
  const fetchItemSupplier = useCallback(async () => {
    if (!itemId) return;
    try {
      const response = await supplierService.getSupplierItems(parseInt(id!));
      const item = response.find(is => is.id === parseInt(itemId));
      setItemSupplier(item || null);
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('messages.getItemInfoFailed'));
    }
  }, [id, itemId, t]);

  // 获取汇率信息
  const fetchExchangeRates = useCallback(async () => {
    try {
      const response = await exchangeRateService.getExchangeRateSummary();
      const rates: {[key: string]: number} = {};
      response.forEach(rate => {
        if (rate.current_rate && rate.is_valid) {
          rates[rate.currency_code] = rate.current_rate;
        }
      });
      setExchangeRates(rates);
    } catch (error) {
      console.error('获取汇率失败:', error);
    }
  }, []);

  // 获取价格列表
  const fetchPrices = useCallback(async () => {
    if (!itemId) return;
    setLoading(true);
    try {
      const response = await supplierPriceService.getPrices({
        item_supplier_id: parseInt(itemId),
        status: 'active'
      });
      setPrices(response.items);
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('messages.getPriceListFailed'));
    } finally {
      setLoading(false);
    }
  }, [itemId, t]);

  useEffect(() => {
    fetchItemSupplier();
    fetchPrices();
    fetchExchangeRates();
  }, [fetchItemSupplier, fetchPrices, fetchExchangeRates]);

  // 处理添加价格
  const handleAddPrice = async (values: any) => {
    try {
      const priceData = {
        ...values,
        item_supplier_id: parseInt(itemId!),
        valid_from: values.valid_from?.toISOString(),
        valid_to: values.valid_to?.toISOString(),
        status: values.status || 'active'
      };

      await supplierPriceService.createPrice(priceData);
      message.success(t('messages.priceAddedSuccessfully'));
      setModalVisible(false);
      form.resetFields();
      fetchPrices();
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('messages.addFailed'));
    }
  };

  // 处理编辑价格
  const handleEditPrice = async (values: any) => {
    if (!selectedPrice) return;
    try {
      const priceData = {
        ...values,
        valid_from: values.valid_from?.toISOString(),
        valid_to: values.valid_to?.toISOString(),
        status: values.status || 'active'
      };

      await supplierPriceService.updatePrice(selectedPrice.id, priceData);
      message.success(t('messages.priceUpdatedSuccessfully'));
      setModalVisible(false);
      form.resetFields();
      fetchPrices();
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('messages.updateFailed'));
    }
  };

  // 处理删除价格
  const handleDeletePrice = async (price: SupplierPrice) => {
    try {
      await supplierPriceService.deletePrice(price.id);
      message.success(t('messages.priceDeletedSuccessfully'));
      fetchPrices();
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('messages.deleteFailed'));
    }
  };

  // 打开添加价格模态框
  const openAddPrice = () => {
    setSelectedPrice(null);
    setEditMode(false);
    form.resetFields();
    form.setFieldsValue({
      item_supplier_id: parseInt(itemId!),
      currency_code: 'USD',
      status: 'active'
    });
    setModalVisible(true);
  };

  // 打开编辑价格模态框
  const openEditPrice = (price: SupplierPrice) => {
    setSelectedPrice(price);
    setEditMode(true);
    form.setFieldsValue({
      ...price,
      valid_from: price.valid_from ? dayjs(price.valid_from) : undefined,
      valid_to: price.valid_to ? dayjs(price.valid_to) : undefined
    });
    setModalVisible(true);
  };

  // 获取货币符号
  const getCurrencySymbol = (currencyCode: string) => {
    const currency = SUPPORTED_CURRENCIES[currencyCode as keyof typeof SUPPORTED_CURRENCIES];
    return currency?.symbol || currencyCode;
  };

  // 转换价格到美元
  const convertToUsd = (price: string | number, currencyCode: string) => {
    if (currencyCode === 'USD') return Number(price);
    const rate = exchangeRates[currencyCode];
    if (!rate) return null;
    return Number(price) / rate;
  };

  // 表格列定义
  const columns: ColumnsType<SupplierPrice> = [
    {
      title: t('messages.unitPrice'),
      key: 'unit_price',
      width: 150,
      render: (_, record) => {
        // 使用多货币价格显示组件
        return (
          <MultiCurrencyPriceDisplay
            originalPrice={record.unit_price}
            currencyCode={record.currency_code}
            exchangeRateInfo={exchangeRates[record.currency_code] ? {
              currency_code: record.currency_code,
              original_unit_price: Number(record.unit_price),
              original_total_price: Number(record.unit_price),
              exchange_rate: exchangeRates[record.currency_code],
              usd_unit_price: Number(record.unit_price) / exchangeRates[record.currency_code],
              usd_total_price: Number(record.unit_price) / exchangeRates[record.currency_code],
              rate_type: 'current_month',
              effective_month: new Date().toISOString().slice(0, 7) + '-01',
              is_valid: true
            } : undefined}
            displayType="unit"
            size="medium"
            showCurrencyTag={true}
            showExchangeRate={false}
            showWarning={false}
          />
        );
      }
    },
    {
      title: t('messages.currency'),
      dataIndex: 'currency_code',
      key: 'currency_code',
      width: 80,
      render: (currencyCode) => {
        const currency = SUPPORTED_CURRENCIES[currencyCode as keyof typeof SUPPORTED_CURRENCIES];
        return (
          <Tag color={currencyCode === 'USD' ? 'green' : 'blue'}>
            {currency?.name || currencyCode}
          </Tag>
        );
      }
    },
    {
      title: t('messages.quantityRange'),
      key: 'quantity_range',
      width: 150,
      render: (_, record) => (
        <div>
          <div>{t('messages.minQuantity')}: {record.min_quantity}</div>
          {record.max_quantity && <div>{t('messages.maxQuantity')}: {record.max_quantity}</div>}
        </div>
      )
    },
    {
      title: t('messages.validPeriod'),
      key: 'valid_period',
      width: 200,
      render: (_, record) => (
        <div>
          <div>{t('messages.startDate')}: {dayjs(record.valid_from).format(DATE_FORMATS.DATE_ONLY)}</div>
          {record.valid_to && (
            <div>{t('messages.endDate')}: {dayjs(record.valid_to).format(DATE_FORMATS.DATE_ONLY)}</div>
          )}
        </div>
      )
    },
    {
      title: t('messages.status'),
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'orange'}>
          {status === 'active' ? t('messages.active') : t('messages.inactive')}
        </Tag>
      )
    },
    {
      title: t('messages.updatedAt'),
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 140,
      render: (updated_at) => (
        <span>
          {updated_at ? dayjs(updated_at).format(DATE_FORMATS.DATE_TIME_MINUTES) : '-'}
        </span>
      )
    },
    {
      title: t('messages.remarks'),
      dataIndex: 'remarks',
      key: 'remarks',
      width: 150,
      ellipsis: true
    },
    {
      title: t('messages.actions'),
      key: 'actions',
      width: 80,
      render: (_, record) => (
        <Space>
          <PermissionGuard permission={PERMISSIONS.SUPPLIER.PRICE_MANAGE}>
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => openEditPrice(record)}
              title={t('messages.edit')}
            />
          </PermissionGuard>
          <PermissionGuard permission={PERMISSIONS.SUPPLIER.PRICE_MANAGE}>
            <Popconfirm
              title={t('messages.confirmDeletePrice')}
              onConfirm={() => handleDeletePrice(record)}
              okText={t('messages.confirm')}
              cancelText={t('messages.cancel')}
            >
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
                title={t('messages.remove')}
              />
            </Popconfirm>
          </PermissionGuard>
        </Space>
      )
    }
  ];

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>{t('messages.loading')}</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: 16 }}>
        <Button onClick={() => navigate(`/admin/suppliers/${id}/items`)} style={{ marginRight: 16 }}>
          <ArrowLeftOutlined /> {t('messages.backToItemList')}
        </Button>
        <PermissionGuard permission={PERMISSIONS.SUPPLIER.PRICE_MANAGE}>
          <Button type="primary" icon={<PlusOutlined />} onClick={openAddPrice}>
            {t('messages.addPrice')}
          </Button>
        </PermissionGuard>
      </div>

      <Card>
        <Title level={3}>{t('messages.priceManagement')}</Title>
        {itemSupplier && (
          <Alert
            message={`${t('messages.itemId')}: ${itemSupplier.item_id}`}
            description={`${t('messages.supplierId')}: ${itemSupplier.supplier_id} | ${t('messages.spq')}: ${itemSupplier.spq} | ${t('messages.moq')}: ${itemSupplier.moq}`}
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}
        <Table
          columns={columns}
          dataSource={prices}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 添加/编辑价格模态框 */}
      <Modal
        title={editMode ? t('messages.editPrice') : t('messages.addPrice')}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={editMode ? handleEditPrice : handleAddPrice}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="unit_price"
                label={t('messages.unitPrice')}
                rules={[{ required: true, message: t('messages.pleaseEnterUnitPrice') }]}
              >
                <InputNumber
                  placeholder={t('messages.pleaseEnterUnitPrice')}
                  min={0}
                  precision={4}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="currency_code"
                label={t('messages.currency')}
                rules={[{ required: true, message: t('messages.pleaseSelectCurrency') }]}
                initialValue="USD"
              >
                <Select 
                  placeholder={t('messages.pleaseSelectCurrency')}
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) => {
                    if (!option?.children) return false;
                    
                    // Convert children to string for filtering
                    let text = '';
                    if (typeof option.children === 'string') {
                      text = option.children;
                    } else if (option.children && typeof option.children === 'object') {
                      // Extract text from React elements
                      const extractText = (node: any): string => {
                        if (typeof node === 'string') return node;
                        if (typeof node === 'number') return String(node);
                        if (node && typeof node === 'object' && node.props) {
                          if (Array.isArray(node.props.children)) {
                            return node.props.children.map(extractText).join(' ');
                          }
                          return extractText(node.props.children);
                        }
                        return '';
                      };
                      text = extractText(option.children);
                    }
                    
                    return text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                  }}
                >
                  {Object.entries(SUPPORTED_CURRENCIES).map(([code, currency]) => (
                    <Option key={code} value={code}>
                      <Space>
                        <Tag color={code === 'USD' ? 'green' : 'blue'} style={{ margin: 0 }}>
                          {currency.symbol}
                        </Tag>
                        <span>{code}</span>
                        <span style={{ color: '#666' }}>({currency.name})</span>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="min_quantity"
                label={t('messages.minQuantityLabel')}
                rules={[{ required: true, message: t('messages.pleaseEnterMinQuantity') }]}
              >
                <InputNumber
                  placeholder={t('messages.pleaseEnterMinQuantity')}
                  min={1}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="max_quantity"
                label={t('messages.maxQuantityLabel')}
              >
                <InputNumber
                  placeholder={t('messages.maxQuantityOptional')}
                  min={1}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="valid_from"
                label={t('messages.validFrom')}
                rules={[{ required: true, message: t('messages.pleaseSelectValidFrom') }]}
              >
                <DatePicker
                  placeholder={t('messages.pleaseSelectValidFrom')}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="valid_to"
                label={t('messages.validTo')}
              >
                <DatePicker
                  placeholder={t('messages.validToOptional')}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label={t('messages.statusLabel')}
                initialValue="active"
              >
                <Select>
                  <Option value="active">{t('messages.active')}</Option>
                  <Option value="inactive">{t('messages.inactive')}</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="remarks"
            label={t('messages.remarksLabel')}
          >
            <TextArea
              rows={3}
              placeholder={t('messages.pleaseEnterRemarks')}
            />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editMode ? t('messages.update') : t('messages.add')}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                {t('messages.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SupplierItemPrices; 