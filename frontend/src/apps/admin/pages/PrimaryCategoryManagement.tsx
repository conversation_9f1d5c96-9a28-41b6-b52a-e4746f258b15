import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { 
  Table, 
  Button, 
  Space, 
  Typography, 
  Card,
  message,
  Tag,
  Modal,
  Form,
  Input,
  Switch,
  Popconfirm,
  Row,
  Col
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import PermissionGuard from '@shared/components/PermissionGuard';
import { PERMISSIONS } from '@shared/config/permissions';
import { apiClient } from '../services/authService';

const { Title } = Typography;
const { TextArea } = Input;

interface PrimaryCategory {
  id: number;
  name: string;
  description?: string;
  code_prefix: string;
  code_format: string;
  current_sequence: number;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

interface PrimaryCategoryFormData {
  name: string;
  description?: string;
  code_prefix: string;
  code_format: string;
  current_sequence: number;
  is_active: boolean;
}

const PrimaryCategoryManagement: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<PrimaryCategory[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<PrimaryCategory | null>(null);
  const [form] = Form.useForm();

  // 获取一级分类列表
  const fetchCategories = async () => {
    setLoading(true);
    try {
              const response = await apiClient.get('/items/primary-categories');
      setCategories(response.data);
    } catch (error) {
      console.error('获取一级分类列表错误:', error);
      message.error(t('categoryManagement.getPrimaryCategoryListFailed'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  // 处理添加/编辑
  const handleSubmit = async (values: PrimaryCategoryFormData) => {
    try {
      if (editingCategory) {
        await apiClient.put(`/items/primary-categories/${editingCategory.id}`, values);
        message.success(t('categoryManagement.primaryCategoryUpdateSuccess'));
      } else {
        await apiClient.post('/items/primary-categories', values);
        message.success(t('categoryManagement.primaryCategoryCreateSuccess'));
      }
      
      setModalVisible(false);
      setEditingCategory(null);
      form.resetFields();
      fetchCategories();
    } catch (error: any) {
      console.error('提交错误:', error);
      message.error(error.response?.data?.detail || t('categoryManagement.operationFailed'));
    }
  };

  // 处理删除
  const handleDelete = async (category: PrimaryCategory) => {
    try {
              await apiClient.delete(`/items/primary-categories/${category.id}`);
      message.success(t('categoryManagement.primaryCategoryDeleteSuccess'));
      fetchCategories();
    } catch (error: any) {
      console.error('删除错误:', error);
      message.error(error.response?.data?.detail || t('categoryManagement.deleteFailed'));
    }
  };

  // 打开编辑模态框
  const handleEdit = (category: PrimaryCategory) => {
    setEditingCategory(category);
    form.setFieldsValue({
      name: category.name,
      description: category.description,
      code_prefix: category.code_prefix,
      code_format: category.code_format,
      current_sequence: category.current_sequence,
      is_active: category.is_active,
    });
    setModalVisible(true);
  };

  // 打开添加模态框
  const handleAdd = () => {
    setEditingCategory(null);
    form.resetFields();
    form.setFieldsValue({
      code_format: '0000',
      current_sequence: 1,
      is_active: true,
    });
    setModalVisible(true);
  };

  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
    setEditingCategory(null);
    form.resetFields();
  };

  const columns = [
    {
      title: t('categoryManagement.primaryCategoryName'),
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: t('messages.description'),
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (text: string) => text || '-',
    },
    {
      title: t('categoryManagement.codePrefix'),
      dataIndex: 'code_prefix',
      key: 'code_prefix',
      width: 100,
    },
    {
      title: t('categoryManagement.codeFormat'),
      dataIndex: 'code_format',
      key: 'code_format',
      width: 100,
    },
    {
      title: t('categoryManagement.currentSequence'),
      dataIndex: 'current_sequence',
      key: 'current_sequence',
      width: 100,
    },
    {
      title: t('messages.status'),
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? t('categoryManagement.enabled') : t('categoryManagement.disabled')}
        </Tag>
      ),
    },
    {
      title: t('messages.createdAt'),
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (date: string) => dayjs(date).format(DATE_FORMATS.DATE_ONLY),
    },
    {
      title: t('messages.actions'),
      key: 'action',
      width: 150,
      render: (_: any, record: PrimaryCategory) => (
        <Space size="small">
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEdit(record)}
          >
            {t('messages.edit')}
          </Button>
          <Popconfirm
            title={t('categoryManagement.confirmDeletePrimaryCategory')}
            description={t('categoryManagement.confirmDeletePrimaryCategoryDescription')}
            onConfirm={() => handleDelete(record)}
            okText={t('messages.confirm')}
            cancelText={t('messages.cancel')}
          >
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small"
              danger
            >
              {t('messages.delete')}
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <PermissionGuard permission={PERMISSIONS.ITEM.CATEGORY_MANAGE}>
      <div>
        <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            {t('categoryManagement.addPrimaryCategory')}
          </Button>
        </div>

        <Card>
          <Table
            columns={columns}
            dataSource={categories}
            rowKey="id"
            loading={loading}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                t('common.showing', { start: range[0], end: range[1], total }),
            }}
          />
        </Card>

        <Modal
          title={editingCategory ? t('categoryManagement.editPrimaryCategory') : t('categoryManagement.addPrimaryCategory')}
          open={modalVisible}
          onCancel={handleCancel}
          footer={null}
          width={600}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              code_format: '0000',
              current_sequence: 1,
              is_active: true,
            }}
          >
            <Form.Item
              name="name"
              label={t('categoryManagement.primaryCategoryName')}
              rules={[
                { required: true, message: t('categoryManagement.primaryCategoryNameRequired') },
                { max: 100, message: t('categoryManagement.primaryCategoryNameMaxLength') }
              ]}
            >
              <Input placeholder={t('categoryManagement.primaryCategoryNameRequired')} />
            </Form.Item>

            <Form.Item
              name="description"
              label={t('messages.description')}
            >
              <TextArea 
                rows={3} 
                placeholder={t('categoryManagement.enterDescription')}
                maxLength={500}
                showCount
              />
            </Form.Item>

            <Form.Item
              name="code_prefix"
              label={t('categoryManagement.codePrefix')}
              rules={[
                { required: true, message: t('categoryManagement.codePrefixRequired') },
                { max: 10, message: t('categoryManagement.codePrefixMaxLength') },
                { pattern: /^[A-Z0-9]+$/, message: t('categoryManagement.codePrefixPattern') }
              ]}
            >
              <Input placeholder={t('categoryManagement.codePrefixPlaceholder')} />
            </Form.Item>

            <Form.Item
              name="code_format"
              label={t('categoryManagement.codeFormat')}
              rules={[
                { required: true, message: t('categoryManagement.codeFormatRequired') },
                { max: 20, message: t('categoryManagement.codeFormatMaxLength') }
              ]}
            >
              <Input placeholder={t('categoryManagement.codeFormatPlaceholder')} />
            </Form.Item>

            <Form.Item
              name="current_sequence"
              label={t('categoryManagement.currentSequence')}
              rules={[
                { required: true, message: t('categoryManagement.currentSequenceRequired') },
                { type: 'number', min: 1, message: t('categoryManagement.currentSequenceMin') }
              ]}
            >
              <Input type="number" min={1} />
            </Form.Item>

            <Form.Item
              name="is_active"
              label={t('messages.status')}
              valuePropName="checked"
            >
              <Switch checkedChildren={t('categoryManagement.enabled')} unCheckedChildren={t('categoryManagement.disabled')} />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  {editingCategory ? t('messages.update') : t('common.buttons.create')}
                </Button>
                <Button onClick={handleCancel}>
                  {t('messages.cancel')}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </PermissionGuard>
  );
};

export default PrimaryCategoryManagement; 