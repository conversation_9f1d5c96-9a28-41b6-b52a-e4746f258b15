import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  Space,
  Descriptions,
  Tag,
  Table,
  message,
  Spin,
  Row,
  Col,
  Statistic,
  Divider,
  Tabs,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  SafetyOutlined,
  UserOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { roleService, Role, Permission } from '@admin/services/roleService';
import { userService, User } from '@admin/services/userService';
import { handleApiError } from '@shared/utils/errorHandler';

const { TabPane } = Tabs;

interface RoleDetailProps {}

const RoleDetail: React.FC<RoleDetailProps> = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [role, setRole] = useState<Role | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [usersLoading, setUsersLoading] = useState(false);

  // 获取角色详情
  const fetchRoleDetail = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const response = await roleService.getRoleById(parseInt(id));
      setRole(response);
    } catch (error: any) {
      message.error(handleApiError(error, t('messages.getRoleDetailFailed')));
    } finally {
      setLoading(false);
    }
  };

  // 获取拥有该角色的用户列表
  const fetchRoleUsers = async () => {
    if (!id || !role?.code) return;
    
    try {
      setUsersLoading(true);
      const response = await userService.getUsers({
        role: role.code, // 使用role字段，避免数组参数格式问题
        page_size: 100, // 获取所有用户
      });
      setUsers(response.items);
    } catch (error: any) {
      message.error(handleApiError(error, t('messages.getRoleUsersFailed')));
    } finally {
      setUsersLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchRoleDetail();
    }
  }, [id]);

  useEffect(() => {
    if (role) {
      fetchRoleUsers();
    }
  }, [role]);

  const handleEdit = () => {
    navigate(`/admin/roles/${id}/edit`);
  };

  const handleBack = () => {
    navigate(-1);
  };

  // 权限表格列定义
  const permissionColumns: ColumnsType<Permission> = [
    {
      title: t('messages.permissionCode'),
      dataIndex: 'code',
      key: 'code',
      width: 150,
    },
    {
      title: t('messages.permissionName'),
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: t('messages.permissionDescription'),
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: t('messages.permissionStatus'),
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? t('messages.active') : t('messages.inactive')}
        </Tag>
      ),
    },
  ];

  // 用户表格列定义
  const userColumns: ColumnsType<User> = [
    {
      title: t('messages.userAvatar'),
      dataIndex: 'avatar',
      key: 'avatar',
      width: 80,
      render: (avatar: string, record: User) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div
            style={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              backgroundColor: '#1890ff',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '16px',
              fontWeight: 'bold',
            }}
          >
            {record.full_name ? record.full_name.charAt(0).toUpperCase() : 'U'}
          </div>
        </div>
      ),
    },
    {
      title: t('messages.userName'),
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: t('messages.userEmail'),
      dataIndex: 'email',
      key: 'email',
      width: 200,
    },
    {
      title: t('messages.userDepartment'),
      dataIndex: 'department_name',
      key: 'department_name',
      width: 150,
    },
    {
      title: t('messages.userStatus'),
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? t('messages.active') : t('messages.inactive')}
        </Tag>
      ),
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>{t('messages.loading')}</div>
      </div>
    );
  }

  if (!role) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <p>{t('messages.roleNotFound')}</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '24px' 
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Button 
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
          >
            {t('messages.back')}
          </Button>
          <h2 style={{ margin: 0 }}>
            {t('messages.roleDetailTitle', { roleName: role.name })}
          </h2>
        </div>
        
        <Space>
          <Button 
            type="primary" 
            icon={<EditOutlined />}
            onClick={handleEdit}
          >
            {t('messages.edit')}
          </Button>
        </Space>
      </div>

      {/* 角色基本信息 */}
      <Card style={{ marginBottom: '24px' }}>
        <Descriptions title={t('messages.basicInfo')} bordered column={2}>
          <Descriptions.Item label={t('messages.roleName')}>
            {role.name}
          </Descriptions.Item>
          <Descriptions.Item label={t('messages.roleCode')}>
            {role.code}
          </Descriptions.Item>
          <Descriptions.Item label={t('messages.roleDescription')}>
            {role.description || t('messages.noDescription')}
          </Descriptions.Item>
          <Descriptions.Item label={t('messages.roleStatus')}>
            <Tag color={role.is_active ? 'green' : 'red'}>
              {role.is_active ? t('messages.active') : t('messages.inactive')}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label={t('messages.createdAt')}>
                          {dayjs(role.created_at).format(DATE_FORMATS.DATE_TIME)}
          </Descriptions.Item>
          {/* 暂时移除 updatedAt 字段，因为 Role 接口中没有这个属性 */}
          {/* <Descriptions.Item label={t('messages.updatedAt')}>
                          {dayjs(role.updated_time).format(DATE_FORMATS.DATE_TIME)}
          </Descriptions.Item> */}
        </Descriptions>
      </Card>

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={8}>
          <Card>
            <Statistic
              title={t('messages.totalPermissions')}
              value={role.permissions?.length || 0}
              prefix={<SafetyOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title={t('messages.totalUsers')}
              value={users.length}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title={t('messages.activeUsers')}
              value={users.filter(user => user.is_active).length}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细信息标签页 */}
      <Card>
        <Tabs defaultActiveKey="permissions">
          <TabPane tab={t('messages.permissions')} key="permissions">
            <Table
              columns={permissionColumns}
              dataSource={role.permissions || []}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </TabPane>
          <TabPane tab={t('messages.users')} key="users">
            <Table
              columns={userColumns}
              dataSource={users}
              rowKey="id"
              loading={usersLoading}
              pagination={false}
              size="small"
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default RoleDetail;
