import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  Input,
  Button,
  Row,
  Col,
  Select,
  Rate,
  Space,
  message,
  Typography,
  Spin,
  Alert
} from 'antd';
import {
  SaveOutlined,
  ArrowLeftOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { supplierService, Supplier } from '@admin/services/supplierService';
import PermissionGuard from '@shared/components/PermissionGuard';
import { PERMISSIONS } from '@shared/config/permissions';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface SupplierEditProps {}

const SupplierEdit: React.FC<SupplierEditProps> = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [form] = Form.useForm();
  const isEdit = !!id;

  // 获取供应商详情
  const fetchSupplier = useCallback(async () => {
    if (!id) return;
    setLoading(true);
    try {
      const supplierData = await supplierService.getSupplier(parseInt(id));
      setSupplier(supplierData);
      
      // 处理供应商名称字段
      const formData = {
        ...supplierData,
        name_cn: supplierData.name_cn || '',
        name_en: supplierData.name_en || ''
      };
      
      form.setFieldsValue(formData);
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('messages.getSupplierDetailFailed'));
    } finally {
      setLoading(false);
    }
  }, [id, form, t]);

  useEffect(() => {
    if (isEdit) {
      fetchSupplier();
    }
  }, [isEdit, fetchSupplier]);

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    setSaving(true);
    try {
      // 处理供应商名称：检查是否至少提供了一个名称
      const name_cn = values.name_cn?.trim();
      const name_en = values.name_en?.trim();
      
      if (!name_cn && !name_en) {
        message.error(t('messages.atLeastOneNameRequired'));
        setSaving(false);
        return;
      }
      
      // 构建提交数据
      const submitData = {
        ...values,
        name_cn: name_cn || null,
        name_en: name_en || null
      };
      
      if (isEdit) {
        await supplierService.updateSupplier(parseInt(id), submitData);
        message.success(t('messages.supplierUpdateSuccess'));
      } else {
        await supplierService.createSupplier(submitData);
        message.success(t('messages.supplierCreateSuccess'));
      }
      navigate('/admin/suppliers');
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('messages.operationFailed'));
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>{t('messages.loading')}</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          {isEdit ? t('supplier.editSupplier') : t('supplier.addSupplier')}
        </Title>
      </div>

      {/* 页面头部 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '16px',
        padding: '8px 0'
      }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate('/admin/suppliers')}
        >
          {t('buttons.back')}
        </Button>
        
        <Space>
          <PermissionGuard permission={isEdit ? PERMISSIONS.SUPPLIER.UPDATE : PERMISSIONS.SUPPLIER.CREATE}>
            <Button 
              type="primary" 
              icon={<SaveOutlined />} 
              loading={saving}
              onClick={() => form.submit()}
            >
              {isEdit ? t('buttons.update') : t('buttons.save')}
            </Button>
          </PermissionGuard>
          <Button 
            icon={<CloseOutlined />} 
            onClick={() => navigate('/admin/suppliers')}
          >
            {t('buttons.cancel')}
          </Button>
        </Space>
      </div>

      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        {/* 供应商基本信息 */}
        <div style={{ marginBottom: '24px' }}>
          <Title level={4}>{t('supplier.basicInfo')}</Title>
          <Row gutter={16}>
            <Col span={9}>
              <Form.Item
                name="name_cn"
                label={t('supplier.nameCn')}
              >
                <Input placeholder={t('supplier.nameCnPlaceholder')} />
              </Form.Item>
            </Col>
            <Col span={9}>
              <Form.Item
                name="name_en"
                label={t('supplier.nameEn')}
              >
                <Input placeholder={t('supplier.nameEnPlaceholder')} />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item
                name="code"
                label={t('supplier.code')}
              >
                <Input 
                  placeholder={isEdit ? t('supplier.codeImmutable') : t('supplier.autoGenerate')} 
                  disabled={isEdit}
                  style={{ backgroundColor: '#f5f5f5' }}
                />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item
                name="status"
                label={t('supplier.status')}
                initialValue="active"
              >
                <Select>
                  <Option value="active">{t('supplier.active')}</Option>
                  <Option value="inactive">{t('supplier.inactive')}</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </div>

        {/* 联系信息 */}
        <div style={{ marginBottom: '24px' }}>
          <Title level={4}>{t('supplier.contactInfo')}</Title>
          <Row gutter={16}>
            <Col span={12}>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="contact_person"
                    label={t('supplier.contactPerson')}
                  >
                    <Input placeholder={t('supplier.contactPersonPlaceholder')} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="phone"
                    label={t('supplier.phone')}
                  >
                    <Input placeholder={t('supplier.phonePlaceholder')} />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="email"
                    label={t('supplier.email')}
                    rules={[
                      { type: 'email', message: t('supplier.emailFormatError') }
                    ]}
                  >
                    <Input placeholder={t('supplier.emailPlaceholder')} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="rating"
                    label={t('supplier.rating')}
                    initialValue={0}
                  >
                    <Select placeholder={t('supplier.selectRating')}>
                      <Option value={0}>{t('supplier.noRating')}</Option>
                      <Option value={1}>{t('supplier.oneStar')}</Option>
                      <Option value={2}>{t('supplier.twoStars')}</Option>
                      <Option value={3}>{t('supplier.threeStars')}</Option>
                      <Option value={4}>{t('supplier.fourStars')}</Option>
                      <Option value={5}>{t('supplier.fiveStars')}</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            </Col>
            <Col span={12}>
              <Form.Item
                name="company_address"
                label={t('supplier.companyAddress')}
              >
                <TextArea rows={6} placeholder={t('supplier.companyAddressPlaceholder')} />
              </Form.Item>
            </Col>
          </Row>
        </div>

        {/* 其它信息 */}
        <div style={{ marginBottom: '24px' }}>
          {/* 目前没有其它信息字段 */}
        </div>
        </Form>
    </div>
  );
};

export default SupplierEdit; 