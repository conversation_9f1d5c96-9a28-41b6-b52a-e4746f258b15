import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  message,
  Popconfirm,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Spin
} from 'antd';
import {
  DeleteOutlined,
  ShoppingCartOutlined,
  FileTextOutlined,
  CheckOutlined,
  CloseOutlined,
  EditOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

import { useNavigate } from 'react-router-dom';
import { purchaseCartService, CartItem, CartSummary } from '@admin/services/purchaseCartService';
import { purchaseRequestService, CreateRequestRequest } from '@admin/services/purchaseRequestService';
import { useAuth } from '../contexts/AuthContext';
import PermissionGuard from '@shared/components/PermissionGuard';


const { Title, Text } = Typography;
const { Option } = Select;



const PurchaseCart: React.FC = () => {
  const { t } = useTranslation();
  const { user, hasPermission } = useAuth();
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitModalVisible, setSubmitModalVisible] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [submitForm] = Form.useForm();
  const navigate = useNavigate();

  // 权限检查
  const canViewCart = hasPermission('cart.view');
  const canAddItem = hasPermission('cart.add_item');
  const canUpdateItem = hasPermission('cart.update_item');
  const canRemoveItem = hasPermission('cart.remove_item');
  const canSubmitCart = hasPermission('cart.submit');

  // 编辑状态管理
  const [editingItemId, setEditingItemId] = useState<number | null>(null);
  const [editValues, setEditValues] = useState<Record<number, number | string>>({});
  


  // 当前用户部门ID - 从用户上下文获取
  const currentDepartmentId = user?.department_id || 0;

  // 获取购物车数据
  const fetchCartItems = async () => {
    setLoading(true);
    try {
      const items = await purchaseCartService.getDepartmentCartItems(currentDepartmentId);
      setCartItems(items);
    } catch (error) {
      console.error(t('purchase.getCartDataFailed'), error);
      message.error(t('purchase.getCartDataFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 获取购物车数据
  useEffect(() => {
    fetchCartItems();
  }, []);

  // 如果没有查看权限，显示无权限提示
  if (!canViewCart) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <p>{t('purchase.noPermissionToViewCart')}</p>
      </div>
    );
  }

  // 如果用户没有部门，显示提示
  if (!currentDepartmentId) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <p>{t('purchase.noDepartmentAssigned')}</p>
      </div>
    );
  }





  const handleDeleteItem = async (itemId: number) => {
    try {
      await purchaseCartService.removeCartItem(itemId);
      setCartItems(prev => prev.filter(item => item.id !== itemId));
      message.success(t('purchase.itemRemovedFromCart'));
    } catch (error) {
      console.error(t('purchase.removeItemFailed'), error);
      message.error(t('purchase.removeItemFailed'));
    }
  };

  const handleSubmitCart = () => {
    if (cartItems.length === 0) {
      message.warning(t('purchase.cartEmptyCannotSubmit'));
      return;
    }
    
    // 显示提交申请模态框
    setSubmitModalVisible(true);
  };

  const handleSubmitRequest = async (values: any) => {
    try {
      setSubmitLoading(true);
      
      // 准备申请数据
      const requestData: CreateRequestRequest = {
        department_id: currentDepartmentId,
        notes: values.notes,
        cart_item_ids: cartItems.map(item => item.id)
      };

      // 创建采购申请
      const newRequest = await purchaseRequestService.createRequestFromCart(requestData);
      
      // 清空购物车
      await purchaseCartService.clearDepartmentCart(currentDepartmentId);
      setCartItems([]);
      
      // 创建成功后跳转到采购申请管理页面
      message.success(t('purchase.purchaseRequestCreatedSuccessfully'));
      setSubmitModalVisible(false);
      submitForm.resetFields();
      
      // 跳转到采购工作台
      navigate('/admin/purchase-requests');
      
    } catch (error) {
      console.error(t('purchase.createPurchaseRequestFailed'), error);
      message.error(t('purchase.createPurchaseRequestFailed'));
    } finally {
      setSubmitLoading(false);
    }
  };

  const handleClearCart = () => {
    Modal.confirm({
      title: t('purchase.confirmClearCartTitle'),
      content: t('purchase.confirmClearCartContent'),
      onOk: async () => {
        try {
          await purchaseCartService.clearDepartmentCart(currentDepartmentId);
          setCartItems([]);
          message.success(t('purchase.cartCleared'));
        } catch (error) {
          console.error(t('purchase.clearCartFailed'), error);
          message.error(t('purchase.clearCartFailed'));
        }
      }
    });
  };

  // 开始编辑数量
  const handleStartEdit = (itemId: number, currentQuantity: number | string) => {
    setEditingItemId(itemId);
    setEditValues(prev => ({ ...prev, [itemId]: currentQuantity }));
  };

  // 保存编辑的数量
  const handleSaveEdit = async (itemId: number) => {
    const newQuantity = editValues[itemId];
    const currentItem = cartItems.find(item => item.id === itemId);
    
    if (!currentItem || newQuantity === currentItem.spq_count) {
      setEditingItemId(null);
      return;
    }

    try {
      await purchaseCartService.updateCartItem(itemId, {
        spq_count: typeof newQuantity === 'number' ? newQuantity : parseFloat(newQuantity as string)
      });
      
      // 重新获取完整的购物车数据，确保包含所有物品信息
      await fetchCartItems();
      
      setEditingItemId(null);
      message.success(t('purchase.quantityUpdated'));
    } catch (error) {
      console.error(t('purchase.updateQuantityFailed'), error);
      message.error(t('purchase.updateQuantityFailed'));
      // 恢复原值
      setEditValues(prev => ({ ...prev, [itemId]: currentItem.spq_count }));
    }
  };

  // 取消编辑
  const handleCancelEdit = (itemId: number) => {
    setEditingItemId(null);
    setEditValues(prev => ({ ...prev, [itemId]: cartItems.find(item => item.id === itemId)?.spq_count || 1 }));
  };

  // 更新编辑值
  const handleEditValueChange = (itemId: number, value: number | string) => {
    setEditValues(prev => ({ ...prev, [itemId]: value }));
  };

  const columns = [
    {
      title: t('purchase.itemIcon'),
      key: 'item_image',
      width: 80,
      render: (_: any, record: CartItem) => (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          {record.item_image_url ? (
            <img
              src={record.item_image_url}
              alt={record.item_name}
              style={{
                width: 40,
                height: 40,
                objectFit: 'cover',
                borderRadius: 4,
                border: '1px solid #f0f0f0'
              }}
            />
          ) : (
            <div
              style={{
                width: 40,
                height: 40,
                backgroundColor: '#f5f5f5',
                borderRadius: 4,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#999',
                fontSize: '12px'
              }}
            >
              {t('purchase.noImage')}
            </div>
          )}
        </div>
      ),
    },
    {
      title: t('purchase.itemInfo'),
      key: 'item_info',
      width: 250,
      render: (_: any, record: CartItem) => (
        <div>
          <div style={{ fontSize: '14px', fontWeight: 500, marginBottom: '4px' }}>
            <Button
              type="link"
              style={{ padding: 0, height: 'auto', fontSize: '14px', fontWeight: 500 }}
              onClick={() => navigate(`/admin/items/${record.item_id}`)}
            >
              {record.item_name}
            </Button>
            <span style={{ marginLeft: '8px', fontSize: '12px', color: '#666' }}>
              ({record.item_code})
            </span>
            {record.is_overstock && (
              <Tag color="red" style={{ marginLeft: '8px' }}>
                {t('purchase.overstockWarning')}
              </Tag>
            )}
          </div>
        </div>
      ),
    },
    {
      title: t('purchase.quantity'),
      dataIndex: 'spq_count',
      key: 'spq_count',
      width: 120,
      render: (spq_count: number, record: CartItem) => {
        const isEditing = editingItemId === record.id;
        const editValue = editValues[record.id] || spq_count;

        if (isEditing) {
          return (
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <InputNumber
                size="small"
                value={editValue}
                onChange={(value) => handleEditValueChange(record.id, value || 1)}
                min={1}
                precision={0}
                style={{ width: 60 }}
                autoFocus
              />
              <Button
                type="link"
                size="small"
                icon={<CheckOutlined />}
                onClick={() => handleSaveEdit(record.id)}
                style={{ padding: '0 4px' }}
              />
              <Button
                type="link"
                size="small"
                icon={<CloseOutlined />}
                onClick={() => handleCancelEdit(record.id)}
                style={{ padding: '0 4px' }}
              />
            </div>
          );
        }

        return (
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span>{spq_count} × {record.spq_quantity} {record.spq_unit}</span>
            <PermissionGuard permission="cart.update_item">
              <Button
                type="link"
                size="small"
                icon={<EditOutlined />}
                onClick={() => handleStartEdit(record.id, spq_count)}
                style={{ padding: '0 4px' }}
              />
            </PermissionGuard>
          </div>
        );
      },
    },
    {
      title: t('purchase.inventoryStatus'),
      key: 'inventory_status',
      width: 150,
      render: (_: any, record: CartItem) => {
        const currentQty = record.current_quantity || 0;
        const maxQty = record.max_quantity;
        const cartQty = record.cart_quantity || 0;
        const totalAfter = record.total_after_purchase || 0;
        const isOverstock = record.is_overstock;
        
        return (
          <div style={{ fontSize: '12px' }}>
            <div style={{ marginBottom: '4px' }}>
              <span>{t('purchase.currentQuantity')}: {currentQty.toFixed(2)}</span>
            </div>
            {maxQty && (
              <div style={{ marginBottom: '4px' }}>
                <span>{t('purchase.maxQuantity')}: {maxQty.toFixed(2)}</span>
              </div>
            )}
            <div style={{ marginBottom: '4px' }}>
              <span style={{ color: isOverstock ? '#ff4d4f' : 'inherit' }}>
                {t('purchase.quantityAfterPurchase')}: {totalAfter.toFixed(2)}
              </span>
            </div>

          </div>
        );
      },
    },
    {
      title: t('purchase.priceInfo'),
      key: 'price_info',
      width: 150,
      render: (_: any, record: CartItem) => {
        const unitPrice = record.estimated_unit_price;
        const totalPrice = record.total_price;
        
        return (
          <div style={{ fontSize: '12px' }}>
            <div style={{ marginBottom: '4px' }}>
              <span>{t('purchase.unitPrice')}: {unitPrice ? `$${unitPrice.toFixed(2)}` : '-'}</span>
            </div>
            <div>
              <span>{t('purchase.totalPrice')}: {totalPrice ? `$${totalPrice.toFixed(2)}` : '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      title: t('purchase.action'),
      key: 'action',
      width: 100,
      render: (_: any, record: CartItem) => (
        <PermissionGuard permission="cart.remove_item">
          <Popconfirm
            title={t('purchase.confirmRemoveTitle')}
            description={t('purchase.confirmRemoveDescription')}
            onConfirm={() => handleDeleteItem(record.id)}
            okText={t('purchase.confirm')}
            cancelText={t('purchase.cancel')}
          >
            <Button type="link" danger icon={<DeleteOutlined />} />
          </Popconfirm>
        </PermissionGuard>
      ),
    },
  ];

  const cartSummary = {
    total_items: cartItems.length,
    total_quantity: cartItems.reduce((sum, item) => sum + (item.spq_count * (typeof item.spq_quantity === 'string' ? parseFloat(item.spq_quantity) : item.spq_quantity)), 0),
    total_amount: cartItems.reduce((sum, item) => sum + (item.total_price || 0), 0)
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: 16 }}>
        <Title level={3}>{t('purchase.purchaseCartManagement')}</Title>
        <Text type="secondary">{t('purchase.managePurchaseItemsInDepartment')}</Text>
      </div>

      {/* 购物车统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('purchase.cartItemCount')}
              value={cartSummary.total_items}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('purchase.totalQuantity')}
              value={cartSummary.total_quantity}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('purchase.estimatedTotalAmount')}
              value={cartSummary.total_amount}
              prefix="$"
              precision={2}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center', padding: '16px 0' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>{t('purchase.actions')}</div>
              <PermissionGuard permission="cart.submit">
                <Button
                  type="primary"
                  icon={<FileTextOutlined />}
                  onClick={handleSubmitCart}
                  disabled={cartItems.length === 0}
                  size="large"
                >
                  {t('purchase.submitApplication')}
                </Button>
              </PermissionGuard>
            </div>
          </Card>
        </Col>
      </Row>





      {/* 购物车物品列表 */}
      <Card
        title={t('purchase.purchaseCartItems')}
        extra={
          <Space>

            <PermissionGuard permission="cart.remove_item">
              <Button onClick={handleClearCart} disabled={cartItems.length === 0}>
                {t('purchase.clearCart')}
              </Button>
            </PermissionGuard>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={cartItems}
          rowKey="id"
          loading={loading}
          pagination={false}
          size="middle"
        />
      </Card>



      {/* 编辑物品模态框 */}
      {/* Removed */}

      {/* 提交申请模态框 */}
      <Modal
        title={t('purchase.submitPurchaseApplication')}
        open={submitModalVisible}
        onCancel={() => {
          setSubmitModalVisible(false);
          submitForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={submitForm}
          layout="vertical"
          onFinish={handleSubmitRequest}
        >
          <div style={{ marginBottom: 16, padding: 16, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
            <Text strong>{t('purchase.applicationSummary')}</Text>
            <div style={{ marginTop: 8 }}>
              <div>{t('purchase.itemQuantity')}: {cartItems.length} {t('purchase.items')}</div>
              <div>{t('purchase.totalQuantity')}: {cartItems.reduce((sum, item) => sum + (item.spq_count * (typeof item.spq_quantity === 'string' ? parseFloat(item.spq_quantity) : item.spq_quantity)), 0)} {cartItems[0]?.spq_unit || t('purchase.items')}</div>
              <div>{t('purchase.estimatedTotalAmount')}: ${cartItems.reduce((sum, item) => sum + (item.total_price || 0), 0).toFixed(2)}</div>
            </div>
          </div>

          <Form.Item
            name="notes"
            label={t('purchase.notes')}
          >
            <Input.TextArea rows={4} placeholder={t('purchase.optionalNotes')} />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setSubmitModalVisible(false);
                submitForm.resetFields();
              }}>
                {t('purchase.cancel')}
              </Button>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={submitLoading}
                disabled={cartItems.length === 0}
              >
                {t('purchase.submitApplication')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PurchaseCart;
