import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  Button, 
  Space, 
  Row, 
  Col, 
  Typography, 
  message,
  Spin,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Divider
} from 'antd';
import { 
  ArrowLeftOutlined, 
  SaveOutlined
} from '@ant-design/icons';
import { apiClient } from '../services/authService';
import { useAuth } from '../contexts/AuthContext';
import ImageUpload from '@admin/components/ImageUpload';
import CategorySelect from '@admin/components/CategorySelect';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface PrimaryCategory {
  id: number;
  name: string;
  description?: string;
  code_prefix: string;
  is_active: boolean;
}

interface Category {
  id: number;
  name: string;
  description?: string;
  primary_category_id: number;
  is_active: boolean;
  primary_category?: PrimaryCategory;
}

interface Item {
  id: number;
  name: string;
  code: string;
  description?: string;
  category_id: number;
  image_url?: string;
  purchase_unit: string;
  inventory_unit: string;
  qty_per_up: number;
  is_purchasable: boolean;
  is_active: boolean;
  brand?: string;
  spec_material?: string;
  size_dimension?: string;
  created_at: string;
  updated_at?: string;
  category?: Category;
}

const ItemEdit: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  
  // 判断是添加还是编辑模式
  // 使用路径来判断更准确
  const isAddMode = location.pathname === '/admin/items/new';
  const itemId = isAddMode ? null : id;
  

  const [item, setItem] = useState<Item | null>(null);
  const [loading, setLoading] = useState(true);
  const [editLoading, setEditLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [form] = Form.useForm();

  // 获取物品详情（仅编辑模式）
  const fetchItemDetail = async () => {
    if (isAddMode) {
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      const response = await apiClient.get(`/items/${itemId}`);
      setItem(response.data);
    } catch (error: any) {
      console.error(t('messages.getItemDetailFailed'), error);
      message.error(error.response?.data?.detail || t('messages.getItemDetailFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      // 并行获取一级分类和二级分类
      const [primaryResponse, categoriesResponse] = await Promise.all([
        apiClient.get('/items/primary-categories'),
        apiClient.get('/items/categories')
      ]);
      
      const primaryCategories = primaryResponse.data.filter((cat: PrimaryCategory) => cat.is_active);
      const categories = categoriesResponse.data.filter((cat: Category) => cat.is_active);
      
      // 为每个二级分类添加一级分类信息
      const categoriesWithPrimary = categories.map((category: Category) => {
        const primaryCategory = primaryCategories.find((pc: PrimaryCategory) => pc.id === category.primary_category_id);
        return {
          ...category,
          primary_category: primaryCategory
        };
      });
      
      // 按一级分类 -> 二级分类进行排序
      const sortedCategories = categoriesWithPrimary.sort((a: Category & { primary_category?: PrimaryCategory }, b: Category & { primary_category?: PrimaryCategory }) => {
        // 首先按一级分类名称排序
        const primaryA = a.primary_category?.name || '';
        const primaryB = b.primary_category?.name || '';
        const primaryCompare = primaryA.localeCompare(primaryB, 'zh-CN');
        
        if (primaryCompare !== 0) {
          return primaryCompare;
        }
        
        // 一级分类相同时，按二级分类名称排序
        return a.name.localeCompare(b.name, 'zh-CN');
      });
      
      setCategories(sortedCategories);
      return sortedCategories;
    } catch (error: any) {
      console.error(t('messages.getCategoriesFailed'), error);
      message.error(t('messages.getCategoriesFailed'));
      throw error;
    }
  };

  // 初始化数据
  useEffect(() => {
    const initializeData = async () => {
      setLoading(true);
      
      try {
        // 始终获取分类列表
        await fetchCategories();
        
        if (isAddMode) {
          // 添加模式设置默认值（不设置code，让后端自动生成）
          form.setFieldsValue({
            purchase_unit: 'piece',
            inventory_unit: 'piece',
            qty_per_up: 1,
            is_purchasable: true,
            is_active: true
          });
        } else {
          // 编辑模式获取物品详情
          await fetchItemDetail();
        }
      } finally {
        setLoading(false);
      }
    };
    
    initializeData();
  }, [id, isAddMode, form]);

  // 初始化表单数据
  useEffect(() => {
    if (item) {
      form.setFieldsValue({
        name: item.name,
        code: item.code,
        description: item.description,
        category_id: item.category_id,
        image_url: item.image_url,
        purchase_unit: item.purchase_unit,
        inventory_unit: item.inventory_unit,
        qty_per_up: item.qty_per_up,
        brand: item.brand,
        spec_material: item.spec_material,
        size_dimension: item.size_dimension,
        is_active: item.is_active,
        is_purchasable: item.is_purchasable,
      });
    }
  }, [item, form]);

  const handleSave = async () => {
    try {
      setEditLoading(true);
      const values = await form.validateFields();
      
      let response;
      if (isAddMode) {
        // 添加模式使用POST，移除code字段让后端自动生成
        const { code, ...rawValues } = values;
        
        // 清理undefined值，转换为null或移除
        const submitValues = Object.fromEntries(
          Object.entries(rawValues).map(([key, value]) => [
            key, 
            value === undefined ? null : value
          ])
        );
        
        response = await apiClient.post('/items', submitValues);
        if (response.status === 200 || response.status === 201) {
          message.success(t('messages.addSuccess'));
          navigate(-1);
        }
      } else {
        // 编辑模式使用PUT，但不允许修改code
        const { code, ...rawValues } = values;
        
        // 清理undefined值，转换为null或移除
        const submitValues = Object.fromEntries(
          Object.entries(rawValues).map(([key, value]) => [
            key, 
            value === undefined ? null : value
          ])
        );
        
        response = await apiClient.put(`/items/${itemId}`, submitValues);
        if (response.status === 200) {
          message.success(t('messages.saveSuccess'));
          setItem(response.data);
          navigate(-1);
        }
      }
    } catch (error: any) {
      console.error(t('messages.saveFailed'), error);
      if (error.response?.data?.detail) {
        message.error(error.response.data.detail);
      } else if (error.response?.data?.message) {
        message.error(error.response.data.message);
      } else if (error.message) {
        message.error(error.message);
      } else {
        message.error(t('messages.saveFailed'));
      }
    } finally {
      setEditLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(-1);
  };

  const formatPrice = (price?: number | string | null) => {
    if (price === null || price === undefined || price === '') {
      return t('messages.noPrice');
    }
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return `${t('messages.currencySymbol')}${numPrice.toFixed(2)}`;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  // 编辑模式下检查物品是否存在
  if (!isAddMode && !item) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Text>{t('messages.itemNotFound')}</Text>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>{isAddMode ? t('messages.addItem') : `${t('messages.editItem')}${item ? ` - ${item.name}` : ''}`}</Title>
      </div>

      {/* 页面头部 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '24px',
        padding: '16px 0'
      }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={handleCancel}
        >
          {t('messages.cancel')}
        </Button>
        
        <Button 
          type="primary" 
          icon={<SaveOutlined />} 
          onClick={handleSave}
          loading={editLoading}
        >
          {t('messages.save')}
        </Button>
      </div>

      <Form form={form} layout="vertical">
        {/* 基本信息板块 */}
        <div style={{ marginBottom: '24px' }}>
          <Title level={4}>{t('messages.basicInfo')}</Title>
          <Row gutter={24}>
            <Col xs={24} sm={24} md={8} lg={6} xl={6}>
              <Form.Item
                name="image_url"
                label={t('messages.itemImage')}
              >
                <ImageUpload 
                  maxFileSize={500}
                  maxDimension={2048}
                  itemCode={isAddMode ? 'new-item' : form.getFieldValue('code')}
                />
              </Form.Item>
            </Col>
            
            <Col xs={24} sm={24} md={16} lg={18} xl={18}>
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="name"
                    label={t('messages.itemName')}
                    rules={[{ required: true, message: t('messages.itemNameRequired') }]}
                  >
                    <Input placeholder={t('messages.itemNamePlaceholder')} />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="code"
                    label={t('messages.itemCode')}
                  >
                    <Input 
                      placeholder={isAddMode ? t('messages.codeAutoGenerated') : t('messages.itemCodePlaceholder')} 
                      disabled 
                      style={{ backgroundColor: '#f5f5f5' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="category_id"
                    label={t('messages.category')}
                    rules={[{ required: true, message: t('messages.categoryRequired') }]}
                  >
                    <CategorySelect />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="purchase_unit"
                    label={t('messages.purchaseUnit')}
                    rules={[{ required: true, message: t('messages.purchaseUnitRequired') }]}
                  >
                    <Input placeholder={t('messages.purchaseUnitPlaceholder')} />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="inventory_unit"
                    label={t('messages.inventoryUnit')}
                    rules={[{ required: true, message: t('messages.inventoryUnitRequired') }]}
                  >
                    <Input placeholder={t('messages.inventoryUnitPlaceholder')} />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="qty_per_up"
                    label={t('messages.qtyPerUp')}
                    rules={[
                      { required: true, message: t('messages.qtyPerUpRequired') },
                      { type: 'number', min: 1, message: t('messages.qtyPerUpMin') }
                    ]}
                    tooltip={t('messages.qtyPerUpTooltip')}
                  >
                    <InputNumber 
                      placeholder={t('messages.qtyPerUpPlaceholder')} 
                      min={1}
                      step={1}
                      precision={0}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item
                name="description"
                label={t('messages.itemDescription')}
              >
                <TextArea rows={2} placeholder={t('messages.itemDescriptionPlaceholder')} />
              </Form.Item>
            </Col>
          </Row>
        </div>

        <Divider style={{ margin: '24px 0' }} />
        
        {/* 属性板块 */}
        <div style={{ marginBottom: '24px' }}>
          <Title level={4}>{t('messages.itemAttributes')}</Title>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                name="brand"
                label={t('messages.brand')}
              >
                <Input placeholder={t('messages.brandPlaceholder')} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="spec_material"
                label={t('messages.specMaterial')}
              >
                <Input placeholder={t('messages.specMaterialPlaceholder')} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="size_dimension"
                label={t('messages.sizeDimension')}
              >
                <Input placeholder={t('messages.sizeDimensionPlaceholder')} />
              </Form.Item>
            </Col>
            <Col span={6}>
              {/* 占位，保持四列对齐 */}
            </Col>
          </Row>
        </div>

        <Divider style={{ margin: '24px 0' }} />
        
        {/* 状态板块 */}
        <div style={{ marginBottom: '24px' }}>
          <Title level={4}>{t('messages.statusSettings')}</Title>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                name="is_active"
                label={t('messages.isActive')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="is_purchasable"
                label={t('messages.isPurchasable')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={6}>
              {/* 占位，保持四列对齐 */}
            </Col>
            <Col span={6}>
              {/* 占位，保持四列对齐 */}
            </Col>
          </Row>
        </div>

        <Divider style={{ margin: '24px 0' }} />
        



      </Form>
    </div>
  );
};

export default ItemEdit; 