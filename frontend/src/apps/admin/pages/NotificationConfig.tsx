import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Switch, Button, Space, Typography, message, Spin, Divider, Row, Col, InputNumber } from 'antd';
import { 
  SettingOutlined, 
  MailOutlined, 
  SaveOutlined, 
  ExperimentOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { notificationService } from '../services/notificationService';
import { SmtpConfig, TestEmailRequest } from '../types/notification';

const { Title, Text } = Typography;
const { TextArea } = Input;

const NotificationConfig: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [config, setConfig] = useState<Partial<SmtpConfig>>({});

  // 加载配置
  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      setLoading(true);
      const configs = await notificationService.getSystemConfigs();
      
      // 将配置数组转换为对象
      const configObj: Partial<SmtpConfig> = {};
      configs.forEach(config => {
        if (config.config_key === 'smtp_host') configObj.smtp_host = config.config_value;
        if (config.config_key === 'smtp_port') configObj.smtp_port = parseInt(config.config_value);
        if (config.config_key === 'smtp_username') configObj.smtp_username = config.config_value;
        if (config.config_key === 'smtp_password') configObj.smtp_password = config.config_value;
        if (config.config_key === 'smtp_use_tls') configObj.smtp_use_tls = config.config_value === 'true';
        if (config.config_key === 'notification_enabled') configObj.notification_enabled = config.config_value === 'true';
        if (config.config_key === 'email_send_interval') configObj.email_send_interval = parseInt(config.config_value);
      });
      
      setConfig(configObj);
      form.setFieldsValue(configObj);
    } catch (error) {
      console.error('Failed to load config:', error);
      message.error(t('notifications.config.load_error'));
    } finally {
      setLoading(false);
    }
  };

  // 保存配置
  const handleSave = async (values: any) => {
    try {
      setSaving(true);
      
      // 转换表单值为配置格式
      const configData: Partial<SmtpConfig> = {
        smtp_host: values.smtp_host,
        smtp_port: values.smtp_port,
        smtp_username: values.smtp_username,
        smtp_password: values.smtp_password,
        smtp_use_tls: values.smtp_use_tls,
        notification_enabled: values.notification_enabled,
        email_send_interval: values.email_send_interval,
      };
      
      await notificationService.updateSystemConfigs(configData);
      message.success(t('notifications.config.save_success'));
      
      // 更新本地状态
      setConfig(configData);
    } catch (error) {
      console.error('Failed to save config:', error);
      message.error(t('notifications.config.save_error'));
    } finally {
      setSaving(false);
    }
  };

  // 发送测试邮件
  const handleTestEmail = async () => {
    try {
      const values = await form.validateFields();
      setTesting(true);
      
      const testData: TestEmailRequest = {
        to_email: values.test_email || '<EMAIL>',
        subject: t('notifications.config.test_email_subject'),
        content: t('notifications.config.test_email_content')
      };
      
      await notificationService.sendTestEmail(testData);
      message.success(t('notifications.config.test_email_success'));
    } catch (error: any) {
      if (error?.errorFields) {
        // 表单验证错误
        message.error(t('notifications.config.test_email_validation_error'));
      } else {
        console.error('Failed to send test email:', error);
        message.error(t('notifications.config.test_email_error'));
      }
    } finally {
      setTesting(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.setFieldsValue(config);
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>{t('common.loading')}</div>
      </div>
    );
  }

  return (
    <div>
      {/* 页面头部 */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <SettingOutlined style={{ fontSize: 24, color: '#1890ff' }} />
          <Title level={3} style={{ margin: 0 }}>
            {t('notifications.config.title')}
          </Title>
        </div>
        <Text type="secondary">
          {t('notifications.config.description')}
        </Text>
      </Card>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        initialValues={{
          smtp_use_tls: true,
          notification_enabled: true,
          email_send_interval: 5,
          ...config
        }}
      >
        {/* SMTP配置 */}
        <Card 
          title={
            <Space>
              <MailOutlined />
              {t('notifications.config.smtp_title')}
            </Space>
          }
          style={{ marginBottom: 16 }}
        >
          <Row gutter={16}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="smtp_host"
                label={t('notifications.config.smtp_host')}
                rules={[
                  { required: true, message: t('notifications.config.smtp_host_required') }
                ]}
              >
                <Input placeholder={t('notifications.config.smtp_host_placeholder')} />
              </Form.Item>
            </Col>
            
            <Col xs={24} sm={12}>
              <Form.Item
                name="smtp_port"
                label={t('notifications.config.smtp_port')}
                rules={[
                  { required: true, message: t('notifications.config.smtp_port_required') }
                ]}
              >
                <InputNumber 
                  placeholder={t('notifications.config.smtp_port_placeholder')}
                  min={1}
                  max={65535}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="smtp_username"
                label={t('notifications.config.smtp_username')}
                rules={[
                  { required: true, message: t('notifications.config.smtp_username_required') }
                ]}
              >
                <Input placeholder={t('notifications.config.smtp_username_placeholder')} />
              </Form.Item>
            </Col>
            
            <Col xs={24} sm={12}>
              <Form.Item
                name="smtp_password"
                label={t('notifications.config.smtp_password')}
                rules={[
                  { required: true, message: t('notifications.config.smtp_password_required') }
                ]}
              >
                <Input.Password placeholder={t('notifications.config.smtp_password_placeholder')} />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="smtp_use_tls"
                label={t('notifications.config.smtp_use_tls')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col xs={24} sm={12}>
              <Form.Item
                name="email_send_interval"
                label={t('notifications.config.email_send_interval')}
                rules={[
                  { required: true, message: t('notifications.config.email_send_interval_required') }
                ]}
              >
                <InputNumber 
                  placeholder={t('notifications.config.email_send_interval_placeholder')}
                  min={1}
                  max={60}
                  addonAfter={t('notifications.config.email_send_interval_unit')}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 通知开关设置 */}
        <Card 
          title={
            <Space>
              <CheckCircleOutlined />
              {t('notifications.config.notification_title')}
            </Space>
          }
          style={{ marginBottom: 16 }}
        >
          <Row gutter={16}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="notification_enabled"
                label={t('notifications.config.notification_enabled')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              <Text type="secondary">
                {t('notifications.config.notification_enabled_description')}
              </Text>
            </Col>
          </Row>
        </Card>

        {/* 测试邮件 */}
        <Card 
          title={
                    <Space>
          <ExperimentOutlined />
          {t('notifications.config.test_email_title')}
        </Space>
          }
          style={{ marginBottom: 16 }}
        >
          <Row gutter={16}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="test_email"
                label={t('notifications.config.test_email')}
                rules={[
                  { required: false, type: 'email', message: t('notifications.config.test_email_invalid') }
                ]}
              >
                <Input placeholder={t('notifications.config.test_email_placeholder')} />
              </Form.Item>
            </Col>
            
            <Col xs={24} sm={12}>
              <Form.Item label=" " style={{ marginTop: 32 }}>
                <Button
                  type="primary"
                  icon={<ExperimentOutlined />}
                  onClick={handleTestEmail}
                  loading={testing}
                  disabled={!form.getFieldValue('notification_enabled')}
                >
                  {t('notifications.config.send_test_email')}
                </Button>
              </Form.Item>
            </Col>
          </Row>
          
          <Text type="secondary">
            {t('notifications.config.test_email_description')}
          </Text>
        </Card>

        {/* 操作按钮 */}
        <Card>
          <div style={{ display: 'flex', justifyContent: 'center', gap: 16 }}>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              htmlType="submit"
              loading={saving}
              size="large"
            >
              {t('notifications.config.save')}
            </Button>
            
            <Button
              icon={<ExperimentOutlined />}
              onClick={handleTestEmail}
              loading={testing}
              disabled={!form.getFieldValue('notification_enabled')}
              size="large"
            >
              {t('notifications.config.test_email')}
            </Button>
            
            <Button
              onClick={handleReset}
              disabled={saving}
              size="large"
            >
              {t('notifications.config.reset')}
            </Button>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default NotificationConfig;
