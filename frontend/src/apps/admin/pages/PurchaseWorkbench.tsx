import React from 'react';
import { Card } from 'antd';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import UnifiedPurchaseManagement from '@admin/components/purchase/UnifiedPurchaseManagement';
import { useNavigate } from 'react-router-dom';

const PurchaseWorkbench: React.FC = () => {
  const { t } = useTranslation();
  const { user, hasPermission } = useAuth();
  const navigate = useNavigate();

  // 权限检查
  const canApprove = hasPermission('purchase.approve');
  const canManageRequests = hasPermission('purchase.update') || hasPermission('purchase.delete');
  const canViewRequests = hasPermission('purchase.read');
  const canExecute = hasPermission('purchase.execute');

  // 如果没有查看权限，显示无权限提示
  if (!canViewRequests) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <p>{t('messages.noAccessPermission')}</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <UnifiedPurchaseManagement 
          canCreate={false} // 采购申请只能由购物车发起
          canEdit={canManageRequests}
          canDelete={hasPermission('purchase.delete')}
          canApprove={canApprove}
          canExecute={canExecute}
          onNavigateToExecution={() => navigate('/admin/purchase-execution')}
        />
      </Card>
    </div>
  );
};

export default PurchaseWorkbench;
