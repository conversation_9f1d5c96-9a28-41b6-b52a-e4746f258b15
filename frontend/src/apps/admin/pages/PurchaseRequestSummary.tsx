import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Button,
  message,
  Spin,
  Typography,
  Divider,
  Progress,
  Select,
  Space,
  DatePicker
} from 'antd';
import {
  ArrowLeftOutlined,
  ShoppingCartOutlined,
  TeamOutlined,
  ShopOutlined,
  DollarOutlined,
  LinkOutlined,
  FileExcelOutlined,
  DownloadOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { 
  purchaseRequestService, 
  RequestSummaryAnalysis,
  ItemSummary,
  DepartmentSummary,
  SupplierSummary
} from '@admin/services/purchaseRequestService';
import { apiClient } from '@admin/services/authService';
import ItemPurchaseHistory<PERSON>hart from '@admin/components/purchase/ItemPurchaseHistoryChart';
import DepartmentAmountHistoryChart from '@admin/components/purchase/DepartmentAmountHistoryChart';
import SupplierAmountHistoryChart from '@admin/components/purchase/SupplierAmountHistoryChart';
import MultiCurrencyTotalDisplay from '@shared/components/MultiCurrencyTotalDisplay';

const { Title, Text } = Typography;

// 申请状态配置将在组件内部定义以使用 t 函数

const PurchaseRequestSummary: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [summaryData, setSummaryData] = useState<RequestSummaryAnalysis | null>(null);


  // 从URL参数获取申请ID列表
  const requestIds = searchParams.get('requestIds')?.split(',').map(id => parseInt(id)) || [];

  // 申请状态配置
  const statusConfig: { [key: string]: { color: string; text: string } } = {
    'draft': { color: 'default', text: t('purchase.statusDraft') },
    'pending_submission': { color: 'processing', text: t('purchase.statusPendingSubmission') },
    'under_review': { color: 'processing', text: t('purchase.statusUnderReview') },
    'under_principle_approval': { color: 'processing', text: t('purchase.statusUnderPrincipleApproval') },
    'under_final_approval': { color: 'processing', text: t('purchase.statusUnderFinalApproval') },
    'approved': { color: 'success', text: t('purchase.statusApproved') },
    'rejected': { color: 'error', text: t('purchase.statusRejected') },
    'withdrawn': { color: 'warning', text: t('purchase.statusWithdrawn') },
    'executed': { color: 'success', text: t('purchase.statusExecuted') },
  };

  useEffect(() => {
    console.log('PurchaseRequestSummary useEffect triggered, requestIds:', requestIds);
    if (requestIds.length > 0) {
      fetchSummaryAnalysis();
    } else {
      message.error(t('purchase.noRequestsSpecified'));
      navigate('/admin/purchase-workbench');
    }
  }, []); // 只在组件挂载时执行一次

  const fetchSummaryAnalysis = async () => {
    try {
      console.log('开始获取汇总分析，requestIds:', requestIds);
      setLoading(true);
      const data = await purchaseRequestService.getRequestsSummaryAnalysis({
        request_ids: requestIds
      });
      console.log('获取汇总分析成功:', data);
      setSummaryData(data);
    } catch (error: any) {
      console.error('获取汇总分析失败:', error);
      console.error('错误详情:', error.response?.data);
      message.error(error.response?.data?.detail || t('purchase.getSummaryAnalysisFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  // 在新标签页中打开物品详情页面
  const handleItemClick = (itemId: number) => {
    window.open(`/admin/items/${itemId}`, '_blank');
  };

  // 状态标签配置已在组件顶部定义

  // 物品汇总表格列定义
  const itemColumns: ColumnsType<ItemSummary> = [
    {
      title: t('purchase.itemImage'),
      key: 'item_image',
      width: 80,
      render: (_, record) => (
        <div style={{ textAlign: 'center' }}>
          {record.image_url ? (
            <img
              src={record.image_url}
              alt={record.item_name}
              style={{
                width: '50px',
                height: '50px',
                objectFit: 'cover',
                borderRadius: '4px',
                border: '1px solid #d9d9d9'
              }}
              onError={(e) => {
                // 图片加载失败时显示默认图标
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.nextElementSibling?.setAttribute('style', 'display: block');
              }}
            />
          ) : (
            <div
              style={{
                width: '50px',
                height: '50px',
                backgroundColor: '#f5f5f5',
                borderRadius: '4px',
                border: '1px solid #d9d9d9',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#999',
                fontSize: '12px'
              }}
            >
              {t('purchase.noImage')}
            </div>
          )}
        </div>
      ),
    },
    {
      title: t('purchase.itemInfo'),
      key: 'item_info',
      width: 200,
      render: (_, record) => (
        <div>
          <div 
            style={{ 
              fontWeight: 'bold', 
              color: '#1890ff', 
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '4px'
            }}
            onClick={() => handleItemClick(record.item_id)}
            onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'underline'}
            onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
          >
            {record.item_name}
          </div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {t('purchase.itemCode')}: {record.item_code}
          </div>
          {record.category_name && (
            <div style={{ color: '#666', fontSize: '12px' }}>
              {t('purchase.category')}: {record.category_name}
            </div>
          )}
        </div>
      ),
    },
    {
      title: t('purchase.summaryQuantity'),
      key: 'quantity',
      width: 150,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
            {record.total_spq_count} {record.spq_unit}
          </div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {record.total_spq_count} × {Number(record.single_spq_quantity).toFixed(3)} {record.spq_unit}/SPQ
          </div>
        </div>
      ),
    },
    {
      title: t('purchase.requestCount'),
      dataIndex: 'request_count',
      width: 100,
      render: (count: number) => (
        <Text strong>{count} {t('purchase.requests')}</Text>
      ),
    },
    {
      title: t('purchase.averagePrice'),
      dataIndex: 'average_price',
      width: 120,
      render: (price: number) => (
        <Text>${Number(price).toFixed(4)}</Text>
      ),
    },
    {
      title: t('purchase.supplier'),
      key: 'supplier',
      width: 200,
      render: (_, record) => {
        if (record.preferred_supplier_id && record.preferred_supplier_name) {
          return (
            <Button
              type="link"
              style={{ padding: 0, height: 'auto', fontSize: '14px' }}
              onClick={() => window.open(`/admin/suppliers/${record.preferred_supplier_id}/items/${record.item_id}`, '_blank')}
              icon={<LinkOutlined />}
            >
              {record.preferred_supplier_name}
            </Button>
          );
        }
        return <span style={{ color: '#999' }}>{t('purchase.noSupplier')}</span>;
      },
    },
    {
      title: '总金额',
      dataIndex: 'total_amount',
      width: 120,
      render: (amount: number) => (
        <Text strong style={{ color: '#1890ff' }}>
          ${Number(amount).toFixed(2)}
        </Text>
      ),
    },

  ];

  // 申请单列表表格列定义
  const requestColumns: ColumnsType<any> = [
    {
      title: '申请单号',
      dataIndex: 'request_no',
      key: 'request_no',
      width: 200,
      render: (requestNo: string, record: any) => (
        <Button
          type="link"
          style={{ padding: 0, height: 'auto', fontSize: '14px' }}
          onClick={() => window.open(`/admin/purchase-requests/${record.request_id}`, '_blank')}
          icon={<LinkOutlined />}
        >
          {requestNo}
        </Button>
      ),
    },
    {
      title: '申请部门',
      dataIndex: 'department_name',
      key: 'department_name',
      width: 150,
    },
    {
      title: '申请人',
      dataIndex: 'submitter_name',
      key: 'submitter_name',
      width: 120,
    },
    {
      title: '申请状态',
      key: 'status',
      width: 120,
      render: (_, record) => {
        const config = statusConfig[record.status] || { color: 'default', text: record.status };
        return (
          <Tag color={config.color} style={{ padding: '2px 8px', fontSize: '12px' }}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '申请金额',
      dataIndex: 'final_total',
      key: 'final_total',
      width: 120,
      render: (amount: number) => (
        <Text strong style={{ color: '#1890ff' }}>
          ${Number(amount || 0).toFixed(2)}
        </Text>
      ),
    },
    {
      title: '申请时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (date: string) => dayjs(date).format(DATE_FORMATS.DATE_TIME),
    },
  ];

  // 部门汇总表格列定义
  const departmentColumns: ColumnsType<DepartmentSummary> = [
    {
      title: '部门名称',
      dataIndex: 'department_name',
      width: 200,
    },
    {
      title: '申请数量',
      dataIndex: 'request_count',
      width: 120,
      render: (count: number) => (
        <Text strong>{count} piece</Text>
      ),
    },
    {
      title: '总金额',
      dataIndex: 'total_amount',
      width: 150,
      render: (amount: number) => (
        <Text strong>${Number(amount).toFixed(2)}</Text>
      ),
    },
    {
      title: '占比',
      key: 'percentage',
      width: 150,
      render: (_, record) => (
        <div>
          <Progress 
            percent={Number(record.percentage)} 
            size="small" 
            format={(percent) => `${percent}%`}
          />
        </div>
      ),
    },
  ];

  // 供应商汇总表格列定义
  const supplierColumns: ColumnsType<SupplierSummary> = [
    {
      title: '供应商名称',
      dataIndex: 'supplier_name',
      width: 200,
      render: (text: string, record: SupplierSummary) => (
        <Button
          type="link"
          style={{ padding: 0, height: 'auto', fontSize: '14px' }}
          onClick={() => window.open(`/admin/suppliers/${record.supplier_id}/items`, '_blank')}
          icon={<LinkOutlined />}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '物品种类',
      dataIndex: 'item_count',
      width: 120,
      render: (count: number) => (
        <Text>{count} 种</Text>
      ),
    },
    {
      title: '平均价格',
      dataIndex: 'average_price',
      width: 120,
      render: (price: number) => (
        <Text>${Number(price).toFixed(2)}</Text>
      ),
    },
    {
      title: '总金额',
      dataIndex: 'total_amount',
      width: 150,
      render: (amount: number) => (
        <Text strong style={{ color: '#1890ff' }}>
          ${Number(amount).toFixed(2)}
        </Text>
      ),
    },
    {
      title: '占比',
      key: 'percentage',
      width: 120,
      render: (_, record) => (
        <div>
          <Progress 
            percent={Number(record.percentage)} 
            size="small" 
            format={(percent) => `${percent}%`}
            strokeColor="#52c41a"
          />
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>
          <Text>{t('purchase.generatingSummaryAnalysis')}</Text>
        </div>
      </div>
    );
  }

  if (!summaryData) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <Text>{t('purchase.noData')}</Text>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={handleBack}
          style={{ marginRight: '16px' }}
        >
          {t('messages.back')}
        </Button>
        <Title level={2} style={{ display: 'inline', margin: 0 }}>
          {t('purchase.purchaseRequestSummary')}
        </Title>
        <Text type="secondary" style={{ marginLeft: '16px' }}>
                      {t('purchase.analysisTime')}: {dayjs(summaryData.analysis_time).format(DATE_FORMATS.DATE_TIME)}
        </Text>
      </div>

      {/* 汇总统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('purchase.totalRequests')}
              value={summaryData.total_requests}
              prefix={<ShoppingCartOutlined />}
              suffix={t('purchase.items')}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('purchase.involvedDepartments')}
              value={summaryData.total_departments}
              prefix={<TeamOutlined />}
              suffix="piece"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('purchase.itemCategories')}
              value={summaryData.item_summaries.length}
              prefix={<ShopOutlined />}
              suffix="种"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('purchase.totalAmount')}
              value={Number(summaryData.total_amount).toFixed(2)}
              prefix={<DollarOutlined />}
              precision={2}
              valueStyle={{ color: '#1890ff' }}
              formatter={(value) => (
                <MultiCurrencyTotalDisplay
                  totalAmount={value}
                  currencyCode="USD"
                  showCurrencyTag={false}
                  showExchangeRate={false}
                  showWarning={false}
                  size="large"
                  isStatistic={true}
                />
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 申请单列表 */}
      <Card style={{ marginBottom: '24px' }}>
        <Title level={4}>{t('purchase.requestList')}</Title>
        <Table
          columns={requestColumns}
          dataSource={summaryData.requests || []}
          rowKey="id"
          pagination={false}
          scroll={{ x: 900 }}
        />
      </Card>

      {/* 物品汇总分析 */}
      <Card style={{ marginBottom: '24px' }}>
        <Title level={4}>{t('purchase.itemSummaryAnalysis')}</Title>
        <Table
          columns={itemColumns}
          dataSource={summaryData.item_summaries}
          rowKey="item_id"
          pagination={false}
                      scroll={{ x: 1100 }}
        />
      </Card>

      {/* 物品历史采购量趋势 */}
      <ItemPurchaseHistoryChart
        itemIds={summaryData.item_summaries.map(item => item.item_id)}
        itemNames={summaryData.item_summaries.reduce((acc, item) => {
          acc[item.item_id] = item.item_name;
          return acc;
        }, {} as { [key: number]: string })}
        requestIds={requestIds}
      />

      {/* 部门汇总分析 */}
      <Card style={{ marginBottom: '24px' }}>
        <Title level={4}>{t('purchase.departmentSummaryAnalysis')}</Title>
        <Table
          columns={departmentColumns}
          dataSource={summaryData.department_summaries}
          rowKey="department_id"
          pagination={false}
          scroll={{ x: 600 }}
        />
      </Card>

      {/* 部门采购金额历史趋势 */}
      <DepartmentAmountHistoryChart
        departmentIds={summaryData.department_summaries.map(dept => dept.department_id)}
        departmentNames={summaryData.department_summaries.reduce((acc, dept) => {
          acc[dept.department_id] = dept.department_name;
          return acc;
        }, {} as { [key: number]: string })}
        requestIds={requestIds}
      />

      {/* 供应商汇总分析 */}
      {summaryData.supplier_summaries.length > 0 && (
        <Card>
          <Title level={4}>{t('purchase.supplierSummaryAnalysis')}</Title>
          <Table
            columns={supplierColumns}
            dataSource={summaryData.supplier_summaries}
            rowKey="supplier_id"
            pagination={false}
            scroll={{ x: 750 }}
          />
        </Card>
      )}

      {/* 供应商采购金额历史趋势 */}
      {summaryData.supplier_summaries.length > 0 && (
        <SupplierAmountHistoryChart
          supplierIds={summaryData.supplier_summaries.map(supplier => supplier.supplier_id)}
          supplierNames={summaryData.supplier_summaries.reduce((acc, supplier) => {
            acc[supplier.supplier_id] = supplier.supplier_name;
            return acc;
          }, {} as { [key: number]: string })}
          requestIds={requestIds}
        />
      )}


    </div>
  );
};

export default PurchaseRequestSummary;
