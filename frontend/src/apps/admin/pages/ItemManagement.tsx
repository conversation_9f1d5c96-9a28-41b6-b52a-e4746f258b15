import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { 
  Button, 
  Space, 
  Input, 
  Select, 
  Card,
  message,
  Pagination,
  Empty,
  Spin
} from 'antd';
import { 
  PlusOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import PermissionGuard from '@shared/components/PermissionGuard';
import ItemCard from '@admin/components/ItemCard';
import CategoryFilter from '@admin/components/CategoryFilter';
import ItemFilterPanel from '@admin/components/ItemFilterPanel';
import AddToCartModal from '@admin/components/purchase/AddToCartModal';
import { useAuth } from '../contexts/AuthContext';

import { PERMISSIONS } from '@shared/config/permissions';
import { apiClient } from '../services/authService';
import { itemService, ItemPreferredPrice } from '@admin/services/itemService';

const { Search } = Input;
const { Option } = Select;

interface PrimaryCategory {
  id: number;
  name: string;
  description?: string;
  code_prefix: string;
  is_active: boolean;
}

interface Category {
  id: number;
  name: string;
  description?: string;
  primary_category_id: number;
  is_active: boolean;
  primary_category?: PrimaryCategory;
}

interface Item {
  id: number;
  name: string;
  code: string;
  description?: string;
  category_id: number;
  image_url?: string;
  purchase_unit: string;
  inventory_unit: string;
  qty_per_up: number;
  is_purchasable: boolean;
  is_active: boolean;
  brand?: string;
  spec_material?: string;
  size_dimension?: string;
  created_at: string;
  updated_at?: string;
  category?: Category;
}

const ItemManagement: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { t } = useTranslation();
  const { user, hasPermission } = useAuth();
  
  // 仅保留必要的UI状态
  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState<Item[]>([]);
  const [preferredPrices, setPreferredPrices] = useState<Record<number, ItemPreferredPrice>>({});
  const [totalItems, setTotalItems] = useState(0);
  const [searchInputValue, setSearchInputValue] = useState('');
  
  // 购物车相关状态
  const [addToCartModalVisible, setAddToCartModalVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);

  // 当前用户部门ID - 从用户上下文获取
  const currentDepartmentId = user?.department_id || 0;

  // 从URL参数读取所有筛选条件
  const getFilterFromURL = useCallback(() => {
    return {
      search: searchParams.get('search') || '',
      primaryCategoryId: searchParams.get('primary_category_id') ? parseInt(searchParams.get('primary_category_id')!) : undefined,
      categoryId: searchParams.get('category_id') ? parseInt(searchParams.get('category_id')!) : undefined,
      status: (searchParams.get('status') as 'all' | 'active' | 'inactive') || 'all',
      purchasable: (searchParams.get('purchasable') as 'all' | 'purchasable' | 'not_purchasable') || 'all',
      brand: searchParams.get('brand') || '',
      specMaterial: searchParams.get('spec_material') || '',
      sizeDimension: searchParams.get('size_dimension') || '',
      page: parseInt(searchParams.get('page') || '1'),
      pageSize: parseInt(searchParams.get('size') || '20'),
    };
  }, [searchParams]);

  // 初始化搜索输入框的值
  useEffect(() => {
    setSearchInputValue(searchParams.get('search') || '');
  }, [searchParams]);

  // 更新URL参数的函数
  const updateURLParams = useCallback((newParams: Record<string, string | number | undefined>) => {
    const currentParams = new URLSearchParams(searchParams);
    
    // 更新参数
    Object.entries(newParams).forEach(([key, value]) => {
      if (value !== undefined && value !== '' && value !== 'all') {
        currentParams.set(key, value.toString());
      } else {
        currentParams.delete(key);
      }
    });
    
    // 如果页码为1，则从URL中移除page参数
    if (newParams.page === 1) {
      currentParams.delete('page');
    }
    
    // 更新URL
    setSearchParams(currentParams, { replace: true });
  }, [searchParams, setSearchParams]);
  
  // 获取物品列表
  const fetchItems = useCallback(async () => {
    setLoading(true);
    try {
      const filters = getFilterFromURL();
      const params = new URLSearchParams();
      
      if (filters.search) params.append('search', filters.search);
      
      // 优先使用二级分类ID，如果选择了二级分类，则不传递一级分类ID
      if (filters.categoryId) {
        params.append('category_id', filters.categoryId.toString());
      } else if (filters.primaryCategoryId) {
        params.append('primary_category_id', filters.primaryCategoryId.toString());
      }
      
      // 物品状态筛选
      if (filters.status && filters.status !== 'all') {
        params.append('is_active', filters.status === 'active' ? 'true' : 'false');
      }
      
      // 可购买状态筛选
      if (filters.purchasable && filters.purchasable !== 'all') {
        params.append('is_purchasable', filters.purchasable === 'purchasable' ? 'true' : 'false');
      }
      
      if (filters.brand) {
        params.append('brand', filters.brand);
      }
      if (filters.specMaterial) {
        params.append('spec_material', filters.specMaterial);
      }
      if (filters.sizeDimension) {
        params.append('size_dimension', filters.sizeDimension);
      }
      
      params.append('page', filters.page.toString());
      params.append('size', filters.pageSize.toString());
      
      const response = await apiClient.get(`/items?${params.toString()}`);
      
      if (response.status === 200) {
        const itemsData = response.data.items;
        setItems(itemsData);
        setTotalItems(response.data.total); // 从API响应中获取总数
        
        // 获取每个物品的首选供应商价格
        if (itemsData.length > 0) {
          const itemIds = itemsData.map((item: Item) => item.id);
          try {
            const prices = await itemService.getPreferredPrices(itemIds);
            setPreferredPrices(prices);
          } catch (error) {
            console.error(t('messages.getPreferredPricesFailed'), error);
          }
        }
      }
    } catch (error) {
      console.error(t('messages.getItemListFailed'), error);
      message.error(t('messages.getItemListFailed'));
    } finally {
      setLoading(false);
    }
  }, [getFilterFromURL, t]);

  // 初始化数据
  useEffect(() => {
    fetchItems();
  }, [fetchItems]);

  // 处理编辑
  const handleEdit = (item: Item) => {
    navigate(`/admin/items/${item.id}/edit`);
  };

  // 处理加入购物车
  const handleAddToCart = (item: Item) => {
    setSelectedItem(item);
    setAddToCartModalVisible(true);
  };

  // 处理分类变化
  const handleCategoryChange = (categoryId: number | undefined) => {
    updateURLParams({ 
      category_id: categoryId,
      primary_category_id: undefined,
      page: undefined,
      brand: undefined,
      spec_material: undefined,
      size_dimension: undefined
    });
  };

  // 处理属性筛选变化
  const handleAttributeFilterChange = (attributes: {
    brands: string[];
    specs: string[];
    sizes: string[];
  }) => {
    updateURLParams({ 
      brand: attributes.brands.length > 0 ? attributes.brands[0] : undefined,
      spec_material: attributes.specs.length > 0 ? attributes.specs[0] : undefined,
      size_dimension: attributes.sizes.length > 0 ? attributes.sizes[0] : undefined,
      page: 1 
    });
  };

  // 处理状态筛选变化
  const handleStatusFilterChange = (value: 'all' | 'active' | 'inactive') => {
    updateURLParams({ 
      status: value === 'all' ? undefined : value,
      page: 1 
    });
  };

  // 处理购买状态筛选变化
  const handlePurchasableFilterChange = (value: 'all' | 'purchasable' | 'not_purchasable') => {
    updateURLParams({ 
      purchasable: value === 'all' ? undefined : value,
      page: 1 
    });
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    updateURLParams({ search: value || undefined, page: 1 });
    setSearchInputValue(value);
  };

  // 处理分页变化
  const handlePaginationChange = (page: number, pageSize: number) => {
    updateURLParams({ 
      page: page === 1 ? undefined : page,
      size: pageSize
    });
  };

  // 获取当前筛选条件
  const filters = getFilterFromURL();

  return (
    <PermissionGuard permission={PERMISSIONS.ITEM.READ}>
      <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* 主内容区域 - 使用flex布局 */}
        <div style={{ 
          display: 'flex', 
          gap: '16px', 
          flex: 1,
          minHeight: 0,
          overflow: 'hidden'
        }}>
          {/* 左侧分类筛选器 - 固定 */}
          <div style={{ 
            width: '200px', 
            flexShrink: 0,
            background: '#fff',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            overflow: 'hidden',
            position: 'relative',
            zIndex: 10,
            height: 'fit-content',
            maxHeight: 'calc(100vh - 200px)'
          }}>
            <CategoryFilter
              selectedPrimaryCategory={filters.primaryCategoryId}
              selectedCategory={filters.categoryId}
              onPrimaryCategorySelect={(primaryCategoryId) => {
                updateURLParams({ 
                  primary_category_id: primaryCategoryId,
                  category_id: undefined,
                  page: 1 
                });
              }}
              onCategorySelect={handleCategoryChange}
            />
          </div>

          {/* 右侧内容区域 - 可滚动 */}
          <div style={{ 
            flex: 1, 
            minWidth: 0,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            position: 'relative',
            zIndex: 1
          }}>
            {/* 平级分类快速切换和属性筛选面板 */}
            <ItemFilterPanel
              selectedCategoryId={filters.categoryId}
              onCategoryChange={handleCategoryChange}
              onAttributeFilterChange={handleAttributeFilterChange}
              visible={!!filters.categoryId}
              currentBrands={filters.brand ? [filters.brand] : []}
              currentSpecs={filters.specMaterial ? [filters.specMaterial] : []}
              currentSizes={filters.sizeDimension ? [filters.sizeDimension] : []}
            />

            {/* 商品列表区域 - 可滚动 */}
            <div style={{ 
              flex: 1, 
              overflow: 'auto',
              position: 'relative',
              zIndex: 1
            }}>
              <Card style={{ marginTop: 0, paddingTop: 0, height: '100%' }}>
                <div style={{ marginBottom: 12 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
                    <Space wrap>
                      <Search
                        placeholder={t('item.searchPlaceholder')}
                        allowClear
                        style={{ width: 300 }}
                        value={searchInputValue}
                        onChange={(e) => setSearchInputValue(e.target.value)}
                        onSearch={handleSearch}
                        onClear={() => {
                          setSearchInputValue('');
                          handleSearch('');
                        }}
                      />
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <span style={{ fontSize: '14px', color: '#666' }}>{t('common.status')}:</span>
                        <Select
                          placeholder={t('item.statusPlaceholder')}
                          style={{ width: 140 }}
                          value={filters.status}
                          onChange={handleStatusFilterChange}
                          allowClear
                        >
                          <Option value="all">{t('common.all')}</Option>
                          <Option value="active">{t('common.active')}</Option>
                          <Option value="inactive">{t('common.inactive')}</Option>
                        </Select>
                      </div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <span style={{ fontSize: '14px', color: '#666' }}>{t('item.purchasable')}:</span>
                        <Select
                          placeholder={t('item.purchasablePlaceholder')}
                          style={{ width: 160 }}
                          value={filters.purchasable}
                          onChange={handlePurchasableFilterChange}
                          allowClear
                        >
                          <Option value="all">{t('common.all')}</Option>
                          <Option value="purchasable">{t('item.purchasable')}</Option>
                          <Option value="not_purchasable">{t('item.notPurchasable')}</Option>
                        </Select>
                      </div>
                    </Space>
                    
                    <Space>
                      <PermissionGuard permission={PERMISSIONS.ITEM.CREATE}>
                        <Button type="primary" icon={<PlusOutlined />} onClick={() => navigate('/admin/items/new')}>
                          {t('item.addItem')}
                        </Button>
                      </PermissionGuard>
                    </Space>
                  </div>

                  <div style={{ position: 'relative' }}>
                    {loading ? (
                      <div style={{ textAlign: 'center', padding: '50px' }}>
                        <Spin size="large" />
                      </div>
                    ) : items.length === 0 ? (
                      <Empty description={t('item.noItemsData')} />
                    ) : (
                      <>
                        <div className="item-cards-grid">
                          {items.map(item => (
                            <div key={item.id} className="item-card-wrapper">
                              <ItemCard
                                item={item}
                                preferredPrice={preferredPrices[item.id]}
                                showActions={true}
                                showPrice={true}
                                showEdit={true}
                                showAddToCart={hasPermission('cart.add_item')}
                                onClick={() => navigate(`/admin/items/${item.id}`)}
                                onEdit={handleEdit}
                                onAddToCart={handleAddToCart}
                              />
                            </div>
                          ))}
                        </div>
                        
                        <div style={{ marginTop: 16, textAlign: 'center' }}>
                          <Pagination
                            current={filters.page}
                            pageSize={filters.pageSize}
                            total={totalItems}
                            showSizeChanger={true}
                            showQuickJumper={true}
                            showTotal={(total, range) => 
                              t('common.paginationInfo', { 
                                start: range[0], 
                                end: range[1], 
                                total 
                              })
                            }
                            onChange={handlePaginationChange}
                          />
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* 加入购物车模态框 */}
      <AddToCartModal
        visible={addToCartModalVisible}
        item={selectedItem}
        onCancel={() => {
          setAddToCartModalVisible(false);
          setSelectedItem(null);
        }}
        onSuccess={() => {
          setAddToCartModalVisible(false);
          setSelectedItem(null);
        }}
        currentDepartmentId={currentDepartmentId}
      />
    </PermissionGuard>
  );
};

export default ItemManagement;