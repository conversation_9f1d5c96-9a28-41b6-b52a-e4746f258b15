import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  Button,
  Space,
  DatePicker,
  Input,
  message,
  Row,
  Col,
  Typography,
  Badge
} from 'antd';
import {
  PlayCircleOutlined,
  FileExcelOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useSearchParams, useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import {
  purchaseExecutionService,
  ExecutionBatch,
  ExecutionFilters
} from '../services/purchaseExecutionService';
import MultiCurrencyTotalDisplay from '@shared/components/MultiCurrencyTotalDisplay';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

const PurchaseExecution: React.FC = () => {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  
  // URL状态管理
  const page = parseInt(searchParams.get('page') || '1');
  const size = parseInt(searchParams.get('size') || '20');

  // 数据状态
  const [loading, setLoading] = useState(false);
  const [executionBatches, setExecutionBatches] = useState<ExecutionBatch[]>([]);
  const [total, setTotal] = useState(0);
  
  // 筛选状态
  const [filters, setFilters] = useState<ExecutionFilters>({});

  // 加载执行批次列表
  const loadExecutionBatches = async () => {
    setLoading(true);
    try {
      const response = await purchaseExecutionService.getExecutionBatches(filters, page, size);
      setExecutionBatches(response.batches);
      setTotal(response.total);
    } catch (error) {
      message.error(t('messages.getExecutionBatchesFailed'));
      console.error(t('messages.getExecutionBatchesFailed'), error);
    } finally {
      setLoading(false);
    }
  };

  // 更新URL参数
  const updateSearchParams = (updates: Record<string, string>) => {
    const newParams = new URLSearchParams(searchParams);
    Object.entries(updates).forEach(([key, value]) => {
      if (value) {
        newParams.set(key, value);
      } else {
        newParams.delete(key);
      }
    });
    setSearchParams(newParams);
  };

  // 筛选条件变更
  const handleFilterChange = (newFilters: Partial<ExecutionFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    updateSearchParams({ page: '1' });
  };

  // 清空筛选条件
  const handleClearFilters = () => {
    setFilters({});
    updateSearchParams({ page: '1' });
  };

  // 初始化和数据更新
  useEffect(() => {
    loadExecutionBatches();
  }, [filters, page, size]);

  // 执行批次表格列
  const executionBatchColumns = [
    {
      title: t('purchase.batchNumber'),
      dataIndex: 'batch_no',
      key: 'batch_no',
      render: (text: string, record: ExecutionBatch) => (
        <Button 
          type="link" 
          onClick={() => navigate(`/admin/purchase-execution/${record.id}`)}
          style={{ padding: 0, height: 'auto' }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: t('purchase.executionDate'),
      dataIndex: 'execution_date',
      key: 'execution_date',
      render: (date: string) => dayjs(date).format(DATE_FORMATS.DATE_ONLY),
    },
    {
      title: t('purchase.totalAmount'),
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (amount: any) => {
        const numAmount = typeof amount === 'number' ? amount : parseFloat(amount) || 0;
        
        // 使用多货币总金额显示组件
        return (
          <MultiCurrencyTotalDisplay
            totalAmount={numAmount}
            currencyCode="USD"
            showCurrencyTag={false}
            showExchangeRate={false}
            showWarning={false}
            size="small"
          />
        );
      },
    },
    {
      title: t('purchase.itemCount'),
      dataIndex: 'item_count',
      key: 'item_count',
    },
    {
      title: t('purchase.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          'pending': { text: t('purchase.statusPending'), color: 'orange' },
          'in_progress': { text: t('purchase.statusInProgress'), color: 'blue' },
          'completed': { text: t('purchase.statusCompleted'), color: 'green' },
          'failed': { text: t('purchase.statusFailed'), color: 'red' },
        };
        const config = statusConfig[status as keyof typeof statusConfig] || { text: status, color: 'default' };
        return <Badge status={config.color as any} text={config.text} />;
      },
    },
    {
      title: t('purchase.actions'),
      key: 'actions',
      render: (_: any, record: ExecutionBatch) => (
        <Space size="small">
          <Button 
            type="text" 
            icon={<PlayCircleOutlined />}
            onClick={() => navigate(`/admin/purchase-execution/${record.id}`)}
          >
            {t('purchase.viewDetails')}
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="purchase-execution">
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={3}>
            <PlayCircleOutlined /> {t('purchase.purchaseExecutionRecords')}
          </Title>
          <Text type="secondary">
            {t('purchase.viewPurchaseExecutionRecordsAndBatchInfo')}
          </Text>
        </div>

        {/* 执行批次列表 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={6}>
              <Input.Search
                placeholder={t('purchase.searchBatchNumber')}
                allowClear
                onSearch={(value) => handleFilterChange({ 
                  batch_no: value || undefined 
                })}
              />
            </Col>
            <Col span={6}>
              <RangePicker
                placeholder={[t('purchase.startDate'), t('purchase.endDate')]}
                onChange={(dates) => {
                  if (dates && dates[0] && dates[1]) {
                    handleFilterChange({
                      start_date: dates[0].toISOString(),
                      end_date: dates[1].toISOString()
                    });
                  } else {
                    handleFilterChange({
                      start_date: undefined,
                      end_date: undefined
                    });
                  }
                }}
              />
            </Col>
            <Col span={4}>
              <Button onClick={handleClearFilters}>
                {t('purchase.clearFilters')}
              </Button>
            </Col>
            <Col span={8} style={{ textAlign: 'right' }}>
              <Space>
                <Button 
                  icon={<FileExcelOutlined />}
                  disabled={executionBatches.length === 0}
                >
                  {t('purchase.exportData')}
                </Button>
                <Button 
                  icon={<ReloadOutlined />} 
                  onClick={loadExecutionBatches}
                >
                  {t('purchase.refresh')}
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 执行批次表格 */}
        <Table
          columns={executionBatchColumns}
          dataSource={executionBatches}
          rowKey="id"
          loading={loading}
          pagination={{
            current: page,
            pageSize: size,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${t('purchase.pageRange', { start: range[0], end: range[1], total: total })}`,
            onChange: (newPage, newSize) => {
              updateSearchParams({ 
                page: newPage.toString(), 
                size: newSize?.toString() || size.toString() 
              });
            }
          }}
        />
      </Card>
    </div>
  );
};

export default PurchaseExecution;
