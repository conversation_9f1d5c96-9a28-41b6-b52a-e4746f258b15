import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Card, 
  Form, 
  Button, 
  Space, 
  message, 
  Spin,
  Typography,
  Divider,
  Alert,
  DatePicker
} from 'antd';
import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { apiClient } from '@admin/services/authService';
import { PurchaseRequest } from '@admin/services/purchaseRequestService';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface TimeManipulationProps {}

const TimeManipulation: React.FC<TimeManipulationProps> = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  const [loading, setLoading] = useState(false);
  const [request, setRequest] = useState<PurchaseRequest | null>(null);
  const [originalTimes, setOriginalTimes] = useState<{[key: string]: string}>({});
  const [modifiedTimes, setModifiedTimes] = useState<{[key: string]: string}>({});

  useEffect(() => {
    if (id) {
      fetchRequestDetail();
    }
  }, [id]);

  const fetchRequestDetail = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get(`/purchase/requests/${id}`);
      const data = response.data;
      setRequest(data);
      
      // 设置原始时间
      const times = {
        created_at: data.created_at,
        updated_at: data.updated_at,
        submitted_at: data.submitted_at
      };
      setOriginalTimes(times);
      setModifiedTimes(times);
      
      // 转换时间格式为dayjs对象，用于DatePicker
      const formTimes = {
        created_at: data.created_at ? dayjs(data.created_at) : null,
        updated_at: data.updated_at ? dayjs(data.updated_at) : null,
        submitted_at: data.submitted_at ? dayjs(data.submitted_at) : null
      };
      
      // 设置表单初始值
      form.setFieldsValue(formTimes);
    } catch (error) {
      console.error('获取申请单详情失败:', error);
      message.error('获取申请单详情失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      
      // 准备时间数据，将dayjs对象转换为ISO字符串
      const timeData: {[key: string]: string} = {};
      Object.keys(values).forEach(key => {
        if (values[key] && values[key] !== originalTimes[key]) {
          // 将dayjs对象转换为ISO字符串
          const timeString = values[key].toISOString();
          timeData[key] = timeString;
        }
      });
      
      if (Object.keys(timeData).length === 0) {
        message.info('没有时间字段被修改');
        return;
      }
      
      // 调用API
      await apiClient.put(`/purchase/requests/${id}/time-manipulation`, timeData);
      
      message.success('申请单时间修改成功');
      
      // 重新获取数据
      await fetchRequestDetail();
      
    } catch (error: any) {
      console.error('修改时间失败:', error);
      const errorMessage = error.response?.data?.detail || '修改时间失败';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return '-';
    const date = new Date(timeString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    
    return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
  };

  if (loading && !request) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!request) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Text>申请单不存在</Text>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '24px' }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate(`/admin/purchase-requests/${id}`)}
          style={{ marginBottom: '16px' }}
        >
          返回申请单详情
        </Button>
        <Title level={3}>申请单时间修改</Title>
        <Text type="secondary">
          申请单号: {request.request_no} | 仅限超级管理员使用
        </Text>
      </div>

      <Alert
        message="⚠️ 警告"
        description="此功能仅用于测试阶段，可以修改申请单的创建时间、更新时间和提交时间。修改后的时间将影响相关的统计报表和历史数据分析。"
        type="warning"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          {/* 创建时间 */}
          <Form.Item
            label="创建时间 (created_at)"
            name="created_at"
            rules={[{ required: true, message: '请输入创建时间' }]}
          >
            <DatePicker 
              showTime={{ format: 'HH:mm:ss' }}
              format="DD-MM-YYYY HH:mm:ss"
              style={{ width: '100%' }}
              placeholder="选择创建时间"
            />
          </Form.Item>

          <Divider />

          {/* 更新时间 */}
          <Form.Item
            label="更新时间 (updated_at)"
            name="updated_at"
            rules={[{ required: true, message: '请输入更新时间' }]}
          >
            <DatePicker 
              showTime={{ format: 'HH:mm:ss' }}
              format="DD-MM-YYYY HH:mm:ss"
              style={{ width: '100%' }}
              placeholder="选择更新时间"
            />
          </Form.Item>

          <Divider />

          {/* 提交时间 */}
          <Form.Item
            label="提交时间 (submitted_at)"
            name="submitted_at"
            rules={[{ required: true, message: '请输入提交时间' }]}
          >
            <DatePicker 
              showTime={{ format: 'HH:mm:ss' }}
              format="DD-MM-YYYY HH:mm:ss"
              style={{ width: '100%' }}
              placeholder="选择提交时间"
            />
          </Form.Item>

          <Divider />

          {/* 时间对比 */}
          <div style={{ marginBottom: '24px' }}>
            <Title level={5}>时间对比</Title>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
              <div>
                <Text strong>原始时间:</Text>
                <div style={{ marginTop: '8px' }}>
                  <div>创建时间: {formatTime(originalTimes.created_at)}</div>
                  <div>更新时间: {formatTime(originalTimes.updated_at)}</div>
                  <div>提交时间: {formatTime(originalTimes.submitted_at)}</div>
                </div>
              </div>
              <div>
                <Text strong>修改后时间:</Text>
                <div style={{ marginTop: '8px' }}>
                  <div>创建时间: {formatTime(modifiedTimes.created_at)}</div>
                  <div>更新时间: {formatTime(modifiedTimes.updated_at)}</div>
                  <div>提交时间: {formatTime(modifiedTimes.submitted_at)}</div>
                </div>
              </div>
            </div>
          </div>

          {/* 提交按钮 */}
          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit" 
                icon={<SaveOutlined />}
                loading={loading}
              >
                保存修改
              </Button>
              <Button onClick={() => navigate(`/admin/purchase-requests/${id}`)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default TimeManipulation;
