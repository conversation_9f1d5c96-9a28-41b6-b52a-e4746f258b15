import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import {
  Table,
  Button,
  Input,
  Space,
  Tag,
  Rate,
  Select,
  Modal,
  Card,
  Row,
  Col,
  Statistic,
  message,
  Tooltip,
  Badge,
  Descriptions,
  Typography,
  Spin
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  PhoneOutlined,
  MailOutlined,
  ShopOutlined,
  DollarOutlined,
  StarOutlined,
  EnvironmentOutlined,
  ShoppingOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useAuth } from '../contexts/AuthContext';
import PermissionGuard from '@shared/components/PermissionGuard';
import { PERMISSIONS } from '@shared/config/permissions';
import { apiClient } from '../services/authService';
import { supplierService } from '@admin/services/supplierService';

// 通用错误处理函数
const handleApiError = (error: any, defaultMessage: string = '操作失败') => {
  if (error.response?.data?.detail) {
    const detail = error.response.data.detail;
    if (Array.isArray(detail)) {
      // 处理字段验证错误
      const errorMessages = detail.map((err: any) => {
        if (err.loc && err.msg) {
          const field = err.loc[err.loc.length - 1];
          return `${field}: ${err.msg}`;
        }
        return err.msg;
      });
      return errorMessages.join(', ');
    } else {
      return detail;
    }
  }
  return defaultMessage;
};

const { Search } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

// 供应商状态配置
const getSupplierStatusConfig = (t: any) => ({
  active: { color: 'green', text: t('supplier.statusActive') },
  inactive: { color: 'orange', text: t('supplier.statusInactive') }
});

interface Supplier {
  id: number;
  name_cn?: string;
  name_en?: string;
  code: string;
  company_address?: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  rating: number;
  status: string;
  created_at: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
  itemCount?: number;
  priceRange?: string;
}

interface SupplierListResponse {
  items: Supplier[];
  total: number;
  page: number;
  size: number;
  pages: number;
  stats?: {
    active_suppliers: number;
    avg_rating: number;
    new_this_month: number;
  };
}

const SupplierManagement: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { t } = useTranslation();
  
  // 仅保留必要的UI状态
  const [loading, setLoading] = useState(false);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [totalSuppliers, setTotalSuppliers] = useState(0);
  const [stats, setStats] = useState<{
    active_suppliers: number;
    avg_rating: number;
    new_this_month: number;
  }>({
    active_suppliers: 0,
    avg_rating: 0,
    new_this_month: 0
  });
  const [searchInputValue, setSearchInputValue] = useState('');

  const { user } = useAuth();

  // 从URL参数读取所有筛选条件
  const getFilterFromURL = useCallback(() => {
    return {
      search: searchParams.get('search') || '',
      status: searchParams.get('status') || '',
      ratingMin: searchParams.get('rating_min') ? parseInt(searchParams.get('rating_min')!) : undefined,
      sortBy: searchParams.get('sort_by') || 'created_at',
      sortOrder: (searchParams.get('sort_order') as 'asc' | 'desc') || 'desc',
      page: parseInt(searchParams.get('page') || '1'),
      pageSize: parseInt(searchParams.get('size') || '10'),
    };
  }, [searchParams]);

  // 初始化搜索输入框的值
  useEffect(() => {
    setSearchInputValue(searchParams.get('search') || '');
  }, [searchParams]);

  // 更新URL参数的函数
  const updateURLParams = useCallback((newParams: Record<string, string | number | undefined>) => {
    const currentParams = new URLSearchParams(searchParams);
    
    // 更新参数
    Object.entries(newParams).forEach(([key, value]) => {
      if (value !== undefined && value !== '' && value !== 'all') {
        currentParams.set(key, value.toString());
      } else {
        currentParams.delete(key);
      }
    });
    
    // 如果页码为1，则从URL中移除page参数
    if (newParams.page === 1) {
      currentParams.delete('page');
    }
    
    // 更新URL
    setSearchParams(currentParams, { replace: true });
  }, [searchParams, setSearchParams]);

  // 获取供应商的物品统计信息 - 已由后端API提供，此函数不再需要
  // const fetchSupplierStats = async (supplierId: number) => { ... }

  // 获取供应商列表
  const fetchSuppliers = useCallback(async () => {
    setLoading(true);
    try {
      const filters = getFilterFromURL();
      const params = new URLSearchParams();
      
      if (filters.search) params.append('search', filters.search);
      if (filters.status) params.append('status', filters.status);
      if (filters.ratingMin) params.append('rating_min', filters.ratingMin.toString());
      if (filters.sortBy) params.append('sort_by', filters.sortBy);
      if (filters.sortOrder) params.append('sort_order', filters.sortOrder);
      params.append('page', filters.page.toString());
      params.append('size', filters.pageSize.toString());
      
      const response = await apiClient.get(`/suppliers?${params.toString()}`);
      const data: SupplierListResponse = response.data;

      // 后端API已经提供了统计信息，直接使用
      setSuppliers(data.items);
      setTotalSuppliers(data.total);
      setStats(data.stats || {
        active_suppliers: 0,
        avg_rating: 0,
        new_this_month: 0
      });
    } catch (error: any) {
      message.error(handleApiError(error, t('messages.getSupplierListFailed')));
    } finally {
      setLoading(false);
    }
  }, [getFilterFromURL, t]);

  // 初始化数据
  useEffect(() => {
    fetchSuppliers();
  }, [fetchSuppliers]);

  // 表格列定义
  const columns: ColumnsType<Supplier> = [
    {
      title: t('supplier.basicInfo'),
      key: 'basic_info',
      width: 280,
      render: (_, record) => {
        // 获取显示名称：优先显示英文名，否则显示中文名
        const displayName = record.name_en || record.name_cn || t('supplier.unnamedSupplier');
        
        return (
          <div>
            <div 
              style={{ 
                fontWeight: 'bold', 
                marginBottom: 4,
                cursor: 'pointer',
                color: '#1890ff',
                textDecoration: 'underline',
                transition: 'color 0.3s',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: '100%'
              }}
              onClick={() => handleViewDetail(record)}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = '#40a9ff';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = '#1890ff';
              }}
              title={displayName}
            >
              {displayName}
            </div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              {t('supplier.code')}: {record.code || '-'}
            </div>
            {record.contact_person && (
              <div style={{ color: '#666', fontSize: '12px' }}>
                {t('supplier.contactPerson')}: {record.contact_person}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: t('supplier.supplyItems'),
      key: 'items',
      width: 150,
      render: (_, record) => (
        <div>
          <div 
            style={{ 
              marginBottom: 4,
              cursor: 'pointer',
              transition: 'opacity 0.3s'
            }}
            onClick={() => handleViewItems(record)}
            onMouseEnter={(e) => {
              e.currentTarget.style.opacity = '0.7';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = '1';
            }}
          >
            <Tag color="blue" style={{ fontSize: '12px' }}>
              {record.itemCount || 0} {t('supplier.itemTypes')}
            </Tag>
          </div>
          {record.priceRange && (
            <div 
              style={{ 
                fontSize: '12px', 
                color: '#666',
                cursor: 'pointer',
                transition: 'color 0.3s'
              }}
              onClick={() => handleViewItems(record)}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = '#40a9ff';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = '#666';
              }}
            >
              {t('supplier.price')}: {record.priceRange}
            </div>
          )}
        </div>
      ),
    },
    {
      title: t('supplier.contactInfo'),
      key: 'contact',
      width: 200,
      render: (_, record) => (
        <div>
          {record.phone && (
            <div style={{ marginBottom: 4 }}>
              <PhoneOutlined style={{ marginRight: 4 }} />
              {record.phone}
            </div>
          )}
          {record.email && (
            <div>
              <MailOutlined style={{ marginRight: 4 }} />
              <a 
                href={`mailto:${record.email}`}
                style={{ 
                  color: '#1890ff',
                  textDecoration: 'none',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.textDecoration = 'underline';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.textDecoration = 'none';
                }}
              >
                {record.email}
              </a>
            </div>
          )}
        </div>
      ),
    },
    {
      title: t('supplier.rating'),
      dataIndex: 'rating',
      key: 'rating',
      width: 120,
      render: (rating) => (
        <div>
          <Rate disabled defaultValue={rating || 0} />
          <div style={{ fontSize: '12px', color: '#666' }}>
            {rating || 0}/5
          </div>
        </div>
      ),
    },
    {
      title: t('common.status'),
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const statusConfig = getSupplierStatusConfig(t)[status as keyof ReturnType<typeof getSupplierStatusConfig>];
        if (!statusConfig) {
          return <Tag color="default">{t('supplier.unknownStatus')}</Tag>;
        }
        return (
          <Tag color={statusConfig.color}>
            {statusConfig.text}
          </Tag>
        );
      },
    },
    {
      title: t('common.createdAt'),
      dataIndex: 'created_at',
      width: 120,
      render: (date: string) => date ? dayjs(date).format(DATE_FORMATS.DATE_ONLY) : '-',
    },
    {
      title: t('common.operation'),
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title={t('supplier.viewDetails')}>
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <PermissionGuard permission={PERMISSIONS.SUPPLIER.READ}>
            <Tooltip title={t('supplier.supplyItems')}>
              <Button
                type="text"
                icon={<ShoppingOutlined />}
                onClick={() => handleViewItems(record)}
              />
            </Tooltip>
          </PermissionGuard>
          <PermissionGuard permission={PERMISSIONS.SUPPLIER.UPDATE}>
            <Tooltip title={t('common.edit')}>
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
          </PermissionGuard>
          <PermissionGuard permission={PERMISSIONS.SUPPLIER.DELETE}>
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(record)}
              />
            </Tooltip>
          </PermissionGuard>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (supplier: Supplier) => {
    navigate(`/admin/suppliers/${supplier.id}`);
  };

  const handleViewItems = (supplier: Supplier) => {
    navigate(`/admin/suppliers/${supplier.id}/items`);
  };

  const handleEdit = (supplier: Supplier) => {
    navigate(`/admin/suppliers/${supplier.id}/edit`);
  };

  const handleDelete = async (supplier: Supplier) => {
    try {
      console.log('开始删除供应商...');
      const result = await supplierService.deleteSupplier(supplier.id);
      console.log('删除API响应:', result);
      
      // 检查是否有关联物品
      if (result.has_related_items) {
        console.log('发现关联物品，显示确认对话框');
        // 显示确认对话框
        const confirmed = await new Promise<boolean>((resolve) => {
          const itemList = result.item_details.map((item: any) => 
            `${item.name} (${item.code})`
          ).join('、');
          
          const content = (
            <div>
              <p>{result.message}</p>
              <p>{t('supplier.relatedItems')}: {itemList}</p>
              {result.item_count > 3 && (
                <p>{t('supplier.totalItems', { count: result.item_count })}</p>
              )}
            </div>
          );
          
          Modal.confirm({
            title: t('supplier.confirmDelete'),
            content: content,
            okText: t('supplier.confirmDelete'),
            cancelText: t('common.cancel'),
            onOk: () => resolve(true),
            onCancel: () => resolve(false),
          });
        });
        
        if (confirmed) {
          console.log('用户确认删除，执行强制删除...');
          // 强制删除
          await supplierService.deleteSupplier(supplier.id, true);
          message.success(t('supplier.deleteSuccess'));
          fetchSuppliers();
        } else {
          console.log('用户取消删除');
        }
      } else {
        console.log('没有关联物品，直接删除');
        message.success(t('supplier.deleteSuccess'));
        fetchSuppliers();
      }
    } catch (error: any) {
      console.error('删除失败:', error);
      message.error(handleApiError(error, t('supplier.deleteFailed')));
    }
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    updateURLParams({ search: value || undefined, page: 1 });
    setSearchInputValue(value);
  };

  // 处理状态筛选变化
  const handleStatusFilterChange = (value: string) => {
    updateURLParams({ status: value || undefined, page: 1 });
  };

  // 处理评级筛选变化
  const handleRatingFilterChange = (value: number | null) => {
    updateURLParams({ rating_min: value || undefined, page: 1 });
  };

  // 处理排序变化
  const handleSortChange = (value: string) => {
    const [field, order] = value.split('-');
    updateURLParams({ sort_by: field, sort_order: order, page: 1 });
  };

  // 处理分页变化
  const handlePaginationChange = (page: number, pageSize: number) => {
    updateURLParams({ 
      page: page === 1 ? undefined : page,
      size: pageSize
    });
  };

  // 处理表格变化（排序和分页）
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // 处理排序变化
    if (sorter.field) {
      const sortOrder = sorter.order === 'descend' ? 'desc' : 'asc';
      updateURLParams({ 
        sort_by: sorter.field, 
        sort_order: sortOrder,
        page: 1 
      });
    }
  };

  // 重置筛选
  const handleResetFilters = () => {
    updateURLParams({
      search: undefined,
      status: undefined,
      rating_min: undefined,
      sort_by: undefined,
      sort_order: undefined,
      page: 1
    });
  };

  // 获取当前筛选条件
  const filters = getFilterFromURL();

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Statistic
              title={t('supplier.totalSuppliers')}
              value={totalSuppliers}
              prefix={<ShopOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('supplier.activeSuppliers')}
              value={stats.active_suppliers}
              valueStyle={{ color: '#3f8600' }}
              prefix={<ShopOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('supplier.averageRating')}
              value={stats.avg_rating.toFixed(1)}
              suffix="/5"
              prefix={<StarOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('supplier.newThisMonth')}
              value={stats.new_this_month}
              prefix={<PlusOutlined />}
            />
          </Col>
        </Row>

        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Search
              placeholder={t('supplier.searchPlaceholder')}
              value={searchInputValue}
              onChange={(e) => setSearchInputValue(e.target.value)}
              onSearch={handleSearch}
              onClear={() => {
                setSearchInputValue('');
                handleSearch('');
              }}
              style={{ width: '100%' }}
              enterButton
            />
          </Col>
          <Col span={3}>
            <Select
              placeholder={t('supplier.statusFilterPlaceholder')}
              value={filters.status || undefined}
              onChange={handleStatusFilterChange}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="active">{t('supplier.statusActive')}</Option>
              <Option value="inactive">{t('supplier.statusInactive')}</Option>
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder={t('supplier.ratingFilterPlaceholder')}
              value={filters.ratingMin || undefined}
              onChange={handleRatingFilterChange}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value={1}>{t('supplier.rating1Star')}</Option>
              <Option value={2}>{t('supplier.rating2Star')}</Option>
              <Option value={3}>{t('supplier.rating3Star')}</Option>
              <Option value={4}>{t('supplier.rating4Star')}</Option>
              <Option value={5}>{t('supplier.rating5Star')}</Option>
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder={t('supplier.sortByPlaceholder')}
              value={`${filters.sortBy}-${filters.sortOrder}`}
              onChange={handleSortChange}
              style={{ width: '100%' }}
            >
              <Option value="name-asc">{t('supplier.sortByNameAsc')}</Option>
              <Option value="name-desc">{t('supplier.sortByNameDesc')}</Option>
              <Option value="code-asc">{t('supplier.sortByCodeAsc')}</Option>
              <Option value="code-desc">{t('supplier.sortByCodeDesc')}</Option>
              <Option value="rating-asc">{t('supplier.sortByRatingAsc')}</Option>
              <Option value="rating-desc">{t('supplier.sortByRatingDesc')}</Option>
              <Option value="created_at-asc">{t('supplier.sortByCreatedAtAsc')}</Option>
              <Option value="created_at-desc">{t('supplier.sortByCreatedAtDesc')}</Option>
            </Select>
          </Col>
          <Col span={3}>
            <Space>
              <Button onClick={handleResetFilters}>
                {t('common.reset')}
              </Button>
            </Space>
          </Col>
          <Col span={6}>
            <PermissionGuard permission={PERMISSIONS.SUPPLIER.CREATE}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => navigate('/admin/suppliers/new')}
                style={{ width: '100%' }}
              >
                {t('supplier.addSupplier')}
              </Button>
            </PermissionGuard>
          </Col>
        </Row>

        <div style={{ marginBottom: 8, fontSize: '12px', color: '#666' }}>
          <InfoCircleOutlined style={{ marginRight: 4 }} />
          {t('supplier.hint')}
        </div>

        <Table
          columns={columns}
          dataSource={suppliers}
          rowKey="id"
          loading={loading}
          pagination={{
            current: filters.page,
            pageSize: filters.pageSize,
            total: totalSuppliers,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${t('common.pageRange', { start: range[0], end: range[1], total: total })}`,
            onChange: handlePaginationChange
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default SupplierManagement; 