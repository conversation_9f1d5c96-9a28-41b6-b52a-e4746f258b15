import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Table,
  Space,
  message,
  Typography,
  Divider,
  Row,
  Col,
  Tag,
  Image,
  Descriptions,
  Spin,
  Modal
} from 'antd';
import dayjs from 'dayjs';
import {
  ArrowLeftOutlined,
  PlusOutlined,
  DeleteOutlined,
  SaveOutlined,
  ShoppingCartOutlined
} from '@ant-design/icons';
import { PurchaseRequest, PurchaseRequestItem, purchaseRequestService } from '@admin/services/purchaseRequestService';
import { purchaseCartService, CartItem } from '@admin/services/purchaseCartService';
import { itemService } from '@admin/services/itemService';
import { apiClient } from '../services/authService';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;



const EditPurchaseRequest: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  const [request, setRequest] = useState<PurchaseRequest | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  // 基础数据状态
  const [cartLoading, setCartLoading] = useState(false);
  
  // 初始状态（用于变更比较）
  const [initialRequestItems, setInitialRequestItems] = useState<PurchaseRequestItem[]>([]);
  const [initialCartItems, setInitialCartItems] = useState<CartItem[]>([]);
  
  // 当前状态（用户操作的实时状态）
  const [currentRequestItems, setCurrentRequestItems] = useState<PurchaseRequestItem[]>([]);
  const [currentCartItems, setCurrentCartItems] = useState<CartItem[]>([]);
  
  // URL状态管理
  const [urlState, setUrlState] = useState({
    activeTab: 'items',
    editMode: false
  });


  useEffect(() => {
    if (id) {
      initializePage();
    }
  }, [id]);

  // URL状态管理
  useEffect(() => {
    restoreStateFromURL();
  }, []);

  useEffect(() => {
    saveStateToURL(urlState);
  }, [urlState]);



  // 页面初始化：获取所有基础数据并设置初始状态
  const initializePage = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      // 1. 获取申请详情（已包含明细）
      const requestData = await purchaseRequestService.getPurchaseRequest(parseInt(id));
      const requestItems = requestData.items || []; // 直接使用申请详情中的items数组
      
      console.log('获取申请数据:', requestData);
      console.log('获取申请明细:', requestItems);
      
      // 2. 获取部门购物车
      setCartLoading(true);
      const cartItems = await purchaseCartService.getDepartmentCartItems(requestData.department_id);
      console.log('获取购物车数据:', cartItems);
      
      // 3. 设置基础数据
      setRequest(requestData);
      
      // 4. 保存初始状态（用于最终比较）
      setInitialRequestItems([...requestItems]);
      setInitialCartItems([...cartItems]);
      
      // 5. 设置当前状态（用于实时操作）
      setCurrentRequestItems([...requestItems]);
      setCurrentCartItems([...cartItems]);
      
      // 6. 设置表单值
      form.setFieldsValue({
        notes: requestData.notes
      });
      
      console.log('页面初始化完成');
      
    } catch (error) {
      console.error(t('messages.pageInitializationFailed'), error);
      message.error(t('messages.pageLoadFailed'));
    } finally {
      setLoading(false);
      setCartLoading(false);
    }
  };

  // URL状态管理方法
  const saveStateToURL = (state: typeof urlState) => {
    const url = new URL(window.location.href);
    if (state.activeTab !== 'items') url.searchParams.set('tab', state.activeTab);
    else url.searchParams.delete('tab');
    
    if (state.editMode) url.searchParams.set('edit', 'true');
    else url.searchParams.delete('edit');
    
    window.history.replaceState({}, '', url.toString());
  };

  const restoreStateFromURL = () => {
    const params = new URLSearchParams(window.location.search);
    setUrlState({
      activeTab: params.get('tab') || 'items',
      editMode: params.get('edit') === 'true'
    });
  };

  // 辅助方法：创建申请明细项
  const createRequestItemFromCart = (cartItem: CartItem): PurchaseRequestItem => {
    return {
      id: Date.now() + Math.random(), // 临时ID
      request_id: parseInt(id!),
      item_id: cartItem.item_id,
      item_code: cartItem.item_code,
      item_name: cartItem.item_name,
      item_image_url: cartItem.item_image_url,
      spq_quantity: Number(cartItem.spq_quantity),
      spq_count: cartItem.spq_count,
      spq_unit: cartItem.spq_unit,
      estimated_unit_price: cartItem.estimated_unit_price,
      estimated_total_price: cartItem.total_price,
      final_unit_price: 0,
      final_total_price: 0,
      final_supplier_id: undefined,
      notes: cartItem.notes,
      _markedForRemoval: false // 确保新添加的物品不被标记为删除
    };
  };

  // 辅助方法：创建购物车项
  const createCartItemFromRequest = (requestItem: PurchaseRequestItem): CartItem => {
    return {
      id: Date.now() + Math.random(), // 临时ID
      item_id: requestItem.item_id,
      item_code: requestItem.item_code,
      item_name: requestItem.item_name,
      item_image_url: requestItem.item_image_url,
      spq_quantity: Number(requestItem.spq_quantity),
      spq_count: requestItem.spq_count,
      spq_unit: requestItem.spq_unit,
      estimated_unit_price: typeof requestItem.estimated_unit_price === 'number' ? requestItem.estimated_unit_price : 0,
      total_price: typeof requestItem.estimated_total_price === 'number' ? requestItem.estimated_total_price : 0,
      notes: requestItem.notes,
      created_at: new Date().toISOString()
    };
  };

  // 辅助方法：检查物品是否有变化
  const hasItemChanged = (initial: PurchaseRequestItem, current: PurchaseRequestItem): boolean => {
    return initial.spq_count !== current.spq_count || 
           initial.notes !== current.notes;
  };

  // 计算申请明细变更（独立计算）
  const calculateRequestChanges = () => {
    const changes = {
      itemsToAdd: [] as any[],
      itemsToUpdate: [] as any[],
      itemsToDelete: [] as number[]
    };

    console.log('计算申请明细变更 - 初始状态:', initialRequestItems);
    console.log('计算申请明细变更 - 当前状态:', currentRequestItems);
    
    // A. 找出新增的申请物品（初始无，现在有）
    for (const currentItem of currentRequestItems) {
      const initialItem = initialRequestItems.find(item => item.item_id === currentItem.item_id);
      if (!initialItem) {
        console.log('发现新增申请物品:', currentItem.item_name);
        changes.itemsToAdd.push({
          item_id: currentItem.item_id,
          spq_count: currentItem.spq_count,
          spq_quantity: currentItem.spq_quantity,
          spq_unit: currentItem.spq_unit,
          item_code: currentItem.item_code,
          item_name: currentItem.item_name,
          notes: currentItem.notes
        });
      }
    }
    
    // B. 找出删除的申请物品（初始有，现在无 或 数量为0/标记为删除）
    for (const initialItem of initialRequestItems) {
      const currentItem = currentRequestItems.find(item => item.item_id === initialItem.item_id);
      if (!currentItem || 
          currentItem._markedForRemoval === true || 
          currentItem.spq_count === 0) {
        console.log('发现删除申请物品:', initialItem.item_name);
        changes.itemsToDelete.push(initialItem.id);
      }
    }
    
    // C. 找出修改的申请物品（数量或备注变化，且数量大于0）
    for (const currentItem of currentRequestItems) {
      const initialItem = initialRequestItems.find(item => item.item_id === currentItem.item_id);
      if (initialItem && 
          currentItem.spq_count > 0 && 
          currentItem._markedForRemoval !== true &&
          hasItemChanged(initialItem, currentItem)) {
        console.log('发现修改申请物品:', currentItem.item_name);
        changes.itemsToUpdate.push({
          id: currentItem.id,
          spq_count: currentItem.spq_count,
          notes: currentItem.notes
        });
      }
    }
    
    console.log('申请明细变更结果:', changes);
    return changes;
  };

  // 计算购物车变更（独立计算）
  const calculateCartChanges = () => {
    const changes = {
      cartItemsToAdd: [] as any[],
      cartItemsToRemove: [] as any[]
    };

    console.log('计算购物车变更 - 初始状态:', initialCartItems);
    console.log('计算购物车变更 - 当前状态:', currentCartItems);
    
    // A. 找出需要从购物车删除的物品（初始有，现在无）
    for (const initialCartItem of initialCartItems) {
      const currentCartItem = currentCartItems.find(item => item.item_id === initialCartItem.item_id);
      if (!currentCartItem) {
        console.log('发现需要从购物车删除的物品:', initialCartItem.item_name);
        changes.cartItemsToRemove.push(initialCartItem);
      }
    }
    
    // B. 找出需要添加到购物车的物品（初始无，现在有）
    for (const currentCartItem of currentCartItems) {
      const initialCartItem = initialCartItems.find(item => item.item_id === currentCartItem.item_id);
      if (!initialCartItem) {
        console.log('发现需要添加到购物车的物品:', currentCartItem.item_name);
        changes.cartItemsToAdd.push({
          item_id: currentCartItem.item_id,
          spq_count: currentCartItem.spq_count,
          notes: currentCartItem.notes
        });
      }
    }
    
    // C. 找出需要更新数量的购物车物品（数量变化）
    for (const currentCartItem of currentCartItems) {
      const initialCartItem = initialCartItems.find(item => item.item_id === currentCartItem.item_id);
      if (initialCartItem && initialCartItem.spq_count !== currentCartItem.spq_count) {
        console.log('发现购物车物品数量变化:', currentCartItem.item_name, 
          `${initialCartItem.spq_count} -> ${currentCartItem.spq_count}`);
        // 注意：这里需要特殊处理，因为购物车API可能不支持直接更新数量
        // 我们选择删除旧的数量，添加新的数量
        changes.cartItemsToRemove.push(initialCartItem);
        changes.cartItemsToAdd.push({
          item_id: currentCartItem.item_id,
          spq_count: currentCartItem.spq_count,
          notes: currentCartItem.notes
        });
      }
    }
    
    console.log('购物车变更结果:', changes);
    return changes;
  };

  // 计算所有变更（分别计算申请明细和购物车）
  const calculateAllChanges = () => {
    const requestChanges = calculateRequestChanges();
    const cartChanges = calculateCartChanges();
    
    return {
      request: requestChanges,
      cart: cartChanges
    };
  };

  // 检查是否有申请变更
  const hasRequestChanges = (changes: ReturnType<typeof calculateRequestChanges>): boolean => {
    // 检查物品变更
    const hasItemChanges = changes.itemsToAdd.length > 0 || 
                          changes.itemsToUpdate.length > 0 || 
                          changes.itemsToDelete.length > 0;
    
    // 检查申请单本身变更（如notes字段）
    const formValues = form.getFieldsValue();
    const hasNotesChanges = request?.notes !== formValues.notes;
    
    return hasItemChanges || hasNotesChanges;
  };

  // 购物车更新方法
  const updateCartItems = async (changes: ReturnType<typeof calculateCartChanges>) => {
    if (!request) return;
    
    // 删除购物车物品（对应新增的申请物品）
    for (const cartItem of changes.cartItemsToRemove) {
      try {
        await purchaseCartService.removeCartItem(cartItem.id);
        console.log(`成功从购物车删除物品: ${cartItem.item_name}`);
      } catch (error) {
        console.warn(`删除购物车物品失败: ${cartItem.item_name}`, error);
        // 不抛出错误，因为申请更新已经成功
      }
    }
    
    // 添加购物车物品（对应删除的申请物品）
    for (const item of changes.cartItemsToAdd) {
      try {
        await purchaseCartService.addItemToCart(request.department_id, {
          item_id: item.item_id,
          spq_count: item.spq_count,
          notes: item.notes
        });
        console.log(`成功将物品添加到购物车: ${item.item_name || `物品${item.item_id}`}`);
    } catch (error) {
        console.warn(`添加购物车物品失败: ${item.item_name || `物品${item.item_id}`}`, error);
        // 不抛出错误，继续处理其他物品
      }
    }
  };

  // 主保存方法
  const handleSave = async () => {
    try {
      setSaving(true);
      
      // 1. 计算所有变更
      const allChanges = calculateAllChanges();
      const hasChanges = hasRequestChanges(allChanges.request) || 
                        allChanges.cart.cartItemsToAdd.length > 0 || 
                        allChanges.cart.cartItemsToRemove.length > 0;
      
      if (!hasChanges) {
        message.info(t('messages.noChangesToSave'));
        return;
      }

      // 2. 显示确认对话框
        const changeSummary: string[] = [];
      
      // 检查申请单本身的变更
      const formValues = form.getFieldsValue();
      const hasNotesChanges = request?.notes !== formValues.notes;
      if (hasNotesChanges) {
        changeSummary.push(t('messages.updateNotes'));
      }
      
      // 检查物品变更
      if (allChanges.request.itemsToAdd.length > 0) changeSummary.push(t('messages.addItems', { count: allChanges.request.itemsToAdd.length }));
      if (allChanges.request.itemsToUpdate.length > 0) changeSummary.push(t('messages.updateItems', { count: allChanges.request.itemsToUpdate.length }));
      if (allChanges.request.itemsToDelete.length > 0) changeSummary.push(t('messages.deleteItems', { count: allChanges.request.itemsToDelete.length }));
        
        const confirmed = await new Promise<boolean>((resolve) => {
          Modal.confirm({
            title: t('messages.confirmSaveChangesTitle'),
            content: (
              <div>
                <p>{t('messages.youWillPerformTheFollowingActions')}:</p>
                <ul>
                  {changeSummary.map((summary, index) => (
                    <li key={index}>{summary}</li>
                  ))}
                </ul>
                <p>{t('messages.areYouSureYouWantToSaveTheseChanges')}</p>
              </div>
            ),
            onOk: () => resolve(true),
            onCancel: () => resolve(false),
          });
        });
        
        if (!confirmed) {
          return;
      }
      
      // 3. 获取表单数据
      const values = await form.validateFields();
      
      // 4. 更新申请明细
      if (hasRequestChanges(allChanges.request)) {
        console.log('开始更新申请明细...');
        await purchaseRequestService.updateRequest(parseInt(id!), {
          notes: values.notes,
          items_to_add: allChanges.request.itemsToAdd,
          items_to_update: allChanges.request.itemsToUpdate,
          items_to_delete: allChanges.request.itemsToDelete
        });
        console.log('申请明细更新成功');
      } else {
        // 检查是否有申请单本身的变更（如notes字段）
        const formValues = form.getFieldsValue();
        const hasNotesChanges = request?.notes !== formValues.notes;
        
        if (hasNotesChanges) {
          // 只更新基本信息
          console.log('只更新申请基本信息...');
          await purchaseRequestService.updateRequest(parseInt(id!), {
            notes: values.notes
          });
        } else {
          console.log('没有检测到任何变更');
        }
      }
      
      // 5. 更新购物车
      console.log('开始更新购物车...');
      await updateCartItems(allChanges.cart);
      console.log('购物车更新完成');
      
      // 6. 成功反馈
      const wasRejectedOrWithdrawn = request?.status === 'rejected' || request?.status === 'withdrawn';
      if (wasRejectedOrWithdrawn) {
        message.success(t('messages.purchaseRequestUpdatedAndReadyForResubmission'));
      } else {
        message.success(t('messages.purchaseRequestUpdatedSuccessfully'));
      }
      
      // 7. 导航（延迟确保所有操作完成）
      setTimeout(() => {
        navigate('/admin/purchase-requests');
      }, 100);
      
    } catch (error) {
      console.error(t('messages.saveFailed'), error);
      message.error(t('messages.saveFailed'));
    } finally {
      setSaving(false);
    }
  };

  // 用户操作方法：修改申请明细中的物品数量
  const updateItemQuantity = (itemId: number, newQuantity: number | null) => {
    // 如果数量为0或null，标记为待删除
    if (newQuantity === 0 || newQuantity === null) {
      setCurrentRequestItems(prev => prev.map(item => 
        item.id === itemId ? { ...item, spq_count: 0, _markedForRemoval: true } : item
      ));
      console.log(`标记物品为待删除: ID=${itemId}, 数量=${newQuantity}`);
    } else {
      // 正常更新数量
      setCurrentRequestItems(prev => prev.map(item => 
        item.id === itemId ? { ...item, spq_count: newQuantity, _markedForRemoval: false } : item
      ));
      console.log(`更新物品数量: ID=${itemId}, 新数量=${newQuantity}`);
    }
  };

  // 用户操作方法：修改申请明细中的物品备注
  const updateItemNotes = (itemId: number, newNotes: string) => {
    setCurrentRequestItems(prev => prev.map(item => 
      item.id === itemId ? { ...item, notes: newNotes } : item
    ));
    console.log(`更新物品备注: ID=${itemId}, 新备注=${newNotes}`);
  };

  // 用户操作方法：从申请明细中删除物品
  const removeItemFromRequest = (itemId: number) => {
    const item = currentRequestItems.find(item => item.id === itemId);
    if (!item) {
      console.warn(`要删除的物品不存在: ID=${itemId}`);
      return;
    }

    console.log(`删除申请明细物品: ${item.item_name}`);
    
    // 1. 从当前申请明细中移除
    setCurrentRequestItems(prev => prev.filter(item => item.id !== itemId));
    
    // 2. 添加到当前购物车（智能合并数量）
    const existingCartItem = currentCartItems.find(cartItem => cartItem.item_id === item.item_id);
    
    if (existingCartItem) {
      // 2a. 购物车中已存在同样物品：增加数量
      const newQuantity = existingCartItem.spq_count + item.spq_count;
      setCurrentCartItems(prev => prev.map(cartItem => 
        cartItem.item_id === item.item_id
          ? { ...cartItem, spq_count: newQuantity }
          : cartItem
      ));
      
      message.success(t('messages.itemRemovedAndQuantityIncreased', { itemName: item.item_name, newQuantity }));
    } else {
      // 2b. 购物车中不存在：创建新的购物车项
      const newCartItem = createCartItemFromRequest(item);
      setCurrentCartItems(prev => [...prev, newCartItem]);
      
      message.success(t('messages.itemRemovedAndAddedToCart', { itemName: item.item_name }));
    }
  };
  
  // 辅助函数
  const getStatusColor = (status: string) => {
    const statusColors: Record<string, string> = {
      'pending_submission': 'orange',
      'under_review': 'blue',
      'under_principle_approval': 'purple',
      'under_final_approval': 'cyan',
      'approved': 'green',
      'rejected': 'red',
      'withdrawn': 'gray'
    };
    return statusColors[status] || 'default';
  };

  const getStatusText = (status: string) => {
    const statusTexts: Record<string, string> = {
      'pending_submission': t('messages.pendingSubmission'),
      'under_review': t('messages.underReview'),
      'under_principle_approval': t('messages.underPrincipleApproval'),
      'under_final_approval': t('messages.underFinalApproval'),
      'approved': t('messages.approved'),
      'rejected': t('messages.rejected'),
      'withdrawn': t('messages.withdrawn')
    };
    return statusTexts[status] || status;
  };

  // 用户操作方法：从购物车添加物品到申请明细
  const addItemFromCart = (cartItem: CartItem) => {
    console.log(`从购物车添加物品: ${cartItem.item_name}`);
    
    // 1. 检查申请明细中是否已存在同样物品
    const existingItem = currentRequestItems.find(item => item.item_id === cartItem.item_id);
    
    if (existingItem) {
      // 1a. 已存在：增加数量并确保不被标记为删除
      const newQuantity = existingItem.spq_count + cartItem.spq_count;
      setCurrentRequestItems(prev => prev.map(item => 
        item.id === existingItem.id 
          ? { ...item, spq_count: newQuantity, _markedForRemoval: false }
          : item
      ));
      
      message.success(t('messages.itemQuantityIncreased', { itemName: cartItem.item_name, newQuantity }));
    } else {
      // 1b. 不存在：创建新的申请明细项
      const newRequestItem = createRequestItemFromCart(cartItem);
      // 确保新添加的物品不被标记为删除
      newRequestItem._markedForRemoval = false;
      setCurrentRequestItems(prev => [...prev, newRequestItem]);      
    }
    
    // 2. 从当前购物车中移除
    setCurrentCartItems(prev => prev.filter(item => item.id !== cartItem.id));
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>{t('messages.loading')}</div>
      </div>
    );
  }

  if (!request) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Text>{t('messages.purchaseRequestNotFound')}</Text>
      </div>
    );
  }

  // 申请明细表格列定义 - 参考库存列表的详细信息展示
  const requestItemColumns = [
    {
      title: t('messages.image'),
      key: 'image',
      width: 100,
      render: (_: any, record: PurchaseRequestItem) => (
        <div
          style={{ cursor: 'pointer', display: 'inline-block' }}
          onClick={() => navigate(`/admin/items/${record.item_id}`)}
        >
          <Image
            src={record.item_image_url}
            alt={record.item_name}
            width={64}
            height={64}
            style={{ objectFit: 'cover', borderRadius: '6px', background: '#f5f5f5' }}
            fallback="/static/images/admin/items/default-item.jpg"
            preview={false}
          />
        </div>
      ),
    },
    {
      title: t('messages.itemInfo'),
      key: 'item_info',
      width: 250,
      render: (_: any, record: PurchaseRequestItem) => (
        <div>
          <div
            style={{ cursor: 'pointer' }}
            onClick={() => navigate(`/admin/items/${record.item_id}`)}
          >
            <div style={{ fontWeight: 'bold', fontSize: '14px', color: '#1677ff', marginBottom: '4px' }}>
              {record.item_name}
            </div>
            <div style={{ color: '#666', fontSize: '12px', marginBottom: '2px' }}>
              {t('messages.itemCode')}: {record.item_code}
            </div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              SPQ: {typeof record.spq_quantity === 'string' ? parseFloat(record.spq_quantity) : record.spq_quantity} {record.spq_unit}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: t('messages.quantity'),
      key: 'quantity',
      width: 200,
      render: (_: any, record: PurchaseRequestItem) => {
        const isMarkedForRemoval = record._markedForRemoval === true;
        const isZeroQuantity = record.spq_count === 0;
        
        return (
          <div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
              <Input
                type="number"
                value={record.spq_count || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '') {
                    updateItemQuantity(record.id, null);
                  } else {
                    const numValue = parseInt(value);
                    updateItemQuantity(record.id, isNaN(numValue) ? 0 : numValue);
                  }
                }}
                style={{ 
                  width: 60,
                  borderColor: isMarkedForRemoval || isZeroQuantity ? '#ff4d4f' : undefined
                }}
                min={0}
                placeholder="0"
                disabled={!['pending_submission', 'rejected'].includes(request.status)}
              />
              <span style={{ 
                fontSize: '14px', 
                color: isMarkedForRemoval || isZeroQuantity ? '#ff4d4f' : '#666'
              }}>
                × {typeof record.spq_quantity === 'string' ? parseFloat(record.spq_quantity) : record.spq_quantity} {record.spq_unit}
              </span>
            </div>
            <div style={{ 
              fontSize: '12px', 
              color: isMarkedForRemoval || isZeroQuantity ? '#ff4d4f' : '#999'
            }}>
              {t('messages.total')}: {(record.spq_count * Number(record.spq_quantity)).toFixed(2)} {record.spq_unit}
            </div>
          </div>
        );
      },
    },
    {
      title: t('messages.estimatedPrice'),
      key: 'estimated_price',
      width: 120,
      render: (_: any, record: PurchaseRequestItem) => (
        <div>
          <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
            {record.estimated_unit_price && Number(record.estimated_unit_price) > 0 ? `$${Number(record.estimated_unit_price).toFixed(2)}` : '-'}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {t('messages.unitPrice')} / {record.spq_unit}
          </div>
          <div style={{ fontSize: '14px', color: '#1890ff', fontWeight: 'bold', marginTop: '4px' }}>
            {record.estimated_total_price && Number(record.estimated_total_price) > 0 ? `$${Number(record.estimated_total_price).toFixed(2)}` : '-'}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {t('messages.totalPrice')}
          </div>
        </div>
      ),
    },
    {
      title: t('messages.finalPrice'),
      key: 'final_price',
      width: 120,
      render: (_: any, record: PurchaseRequestItem) => (
        <div>
          <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
            {record.final_unit_price && Number(record.final_unit_price) > 0 ? `$${Number(record.final_unit_price).toFixed(2)}` : '-'}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {t('messages.unitPrice')} / {record.spq_unit}
          </div>
          <div style={{ fontSize: '14px', color: '#1890ff', fontWeight: 'bold', marginTop: '4px' }}>
            {record.final_total_price && Number(record.final_total_price) > 0 ? `$${Number(record.final_total_price).toFixed(2)}` : '-'}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {t('messages.totalPrice')}
          </div>
        </div>
      ),
    },
    {
      title: t('messages.supplier'),
      key: 'supplier',
      width: 120,
      render: (_: any, record: PurchaseRequestItem) => (
        <div>
          {record.final_supplier_id ? (
            <Tag color="blue">{t('messages.supplierConfirmed')}</Tag>
          ) : (
            <Tag color="default">{t('messages.supplierPending')}</Tag>
          )}
        </div>
      ),
    },
    {
      title: t('messages.notes'),
      key: 'notes',
      width: 150,
      render: (_: any, record: PurchaseRequestItem) => (
        <Input
          value={record.notes || ''}
          placeholder={t('messages.pleaseEnterNotes')}
          onChange={(e) => updateItemNotes(record.id, e.target.value)}
          disabled={!['pending_submission', 'rejected'].includes(request.status)}
          style={{ fontSize: '12px' }}
        />
      ),
    },
    {
      title: t('messages.action'),
      key: 'action',
      width: 100,
      fixed: 'right' as const,
      render: (_: any, record: PurchaseRequestItem) => {
        const isMarkedForRemoval = record._markedForRemoval === true;
        const isZeroQuantity = record.spq_count === 0;
        
        if (isMarkedForRemoval || isZeroQuantity) {
          // 显示恢复按钮
          return (
            <Button
              type="text"
              size="small"
              icon={<PlusOutlined />}
              onClick={() => {
                // 恢复物品，设置默认数量为1
                setCurrentRequestItems(prev => prev.map(item => 
                  item.id === record.id 
                    ? { ...item, spq_count: 1, _markedForRemoval: false }
                    : item
                ));
              }}
              disabled={!['pending_submission', 'rejected'].includes(request.status)}
            >
              {t('messages.restore')}
            </Button>
          );
        } else {
          // 显示移除按钮
          return (
            <Button
              type="text"
              danger
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => removeItemFromRequest(record.id)}
              disabled={!['pending_submission', 'rejected'].includes(request.status)}
            >
              {t('messages.remove')}
            </Button>
          );
        }
      },
    },
  ];

  // 购物车物品表格列定义
  const cartItemColumns = [
    {
      title: t('messages.image'),
      key: 'image',
      width: 80,
      render: (_: any, record: CartItem) => (
        <div
          style={{ cursor: 'pointer', display: 'inline-block' }}
          onClick={() => navigate(`/admin/items/${record.item_id}`)}
        >
          <Image
            src={record.item_image_url}
            alt={record.item_name}
            width={50}
            height={50}
            style={{ objectFit: 'cover', borderRadius: '4px', background: '#f5f5f5' }}
            fallback="/static/images/admin/items/default-item.jpg"
            preview={false}
          />
        </div>
      ),
    },
    {
      title: t('messages.itemInfo'),
      key: 'item_info',
      render: (_: any, record: CartItem) => (
        <div>
          <div
            style={{ cursor: 'pointer' }}
            onClick={() => navigate(`/admin/items/${record.item_id}`)}
          >
            <div style={{ fontWeight: 'bold', fontSize: '14px', color: '#1677ff', marginBottom: '2px' }}>
              {record.item_name}
            </div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              {t('messages.itemCode')}: {record.item_code}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: t('messages.quantity'),
      key: 'quantity',
      render: (_: any, record: CartItem) => (
        <div>
          <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
            {record.spq_count} × {typeof record.spq_quantity === 'string' ? parseFloat(record.spq_quantity) : record.spq_quantity} {record.spq_unit}
          </div>
          <div style={{ fontSize: '12px', color: '#999' }}>
            {t('messages.total')}: {(record.spq_count * Number(record.spq_quantity)).toFixed(2)} {record.spq_unit}
          </div>
        </div>
      ),
    },
    {
      title: t('messages.estimatedPrice'),
      key: 'price',
      render: (_: any, record: CartItem) => (
        <div>
          <div style={{ fontSize: '14px' }}>
            {record.estimated_unit_price && Number(record.estimated_unit_price) > 0 ? `$${Number(record.estimated_unit_price).toFixed(2)}` : '-'}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>{t('messages.unitPrice')}</div>
          <div style={{ fontSize: '14px', color: '#1890ff', marginTop: '2px' }}>
            {record.total_price && Number(record.total_price) > 0 ? `$${Number(record.total_price).toFixed(2)}` : '-'}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>{t('messages.totalPrice')}</div>
        </div>
      ),
    },
    {
      title: t('messages.action'),
      key: 'action',
      width: 80,
      render: (_: any, record: CartItem) => (
        <Button
          type="primary"
          size="small"
          icon={<PlusOutlined />}
          onClick={() => addItemFromCart(record)}
          disabled={!['pending_submission', 'rejected'].includes(request.status)}
        >
          {t('messages.add')}
        </Button>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px', width: '100%' }}>
      {/* 页面头部 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/admin/purchase-requests')}
          >
            {t('messages.back')}
          </Button>
          <Title level={3} style={{ margin: 0 }}>
            {t('messages.editPurchaseRequestTitle', { requestNo: request.request_no })}
          </Title>
          <Tag color={getStatusColor(request.status)}>
            {getStatusText(request.status)}
          </Tag>
        </div>
        
        <Space>
          <Button 
            type="primary" 
            icon={<SaveOutlined />}
            onClick={handleSave}
            loading={saving}
            disabled={!['pending_submission', 'rejected'].includes(request.status)}
          >
            {t('messages.saveChanges')}
          </Button>
        </Space>
      </div>

      <Row gutter={24}>
        {/* 左侧：申请信息和明细 */}
        <Col span={18}>
          {/* 申请基本信息 */}
          <Card title={t('messages.purchaseRequestInfo')} style={{ marginBottom: '16px' }}>
            <Form form={form} layout="vertical">
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item
                    name="notes"
                    label={t('messages.notes')}
                  >
                    <TextArea 
                      rows={4} 
                      placeholder={t('messages.pleaseEnterNotes')}
                      disabled={!['pending_submission', 'rejected'].includes(request.status)}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Card>

          {/* 申请明细 */}
          <Card title={`${t('messages.purchaseRequestItems')} (${currentRequestItems.length} ${t('messages.items')})`}>
            <Table
              columns={requestItemColumns}
              dataSource={currentRequestItems}
              rowKey="id"
              pagination={false}
              scroll={{ x: 1600 }}
              size="small"
            />
          </Card>
        </Col>

        {/* 右侧：购物车物品 */}
        <Col span={6}>
          <Card title={t('messages.cartItems')} style={{ height: 'fit-content' }}>
            <Table
              columns={cartItemColumns}
              dataSource={currentCartItems}
              rowKey="id"
              pagination={false}
              size="small"
              loading={cartLoading}
              locale={{ emptyText: t('messages.cartIsEmpty') }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};


export default EditPurchaseRequest;
