import React, { useState, useEffect, useCallback } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Row,
  Col,
  Typography,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  KeyOutlined,
  UserOutlined,
  ReloadOutlined,
  SearchOutlined,
  EyeOutlined,
  LockOutlined,
  UnlockOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { userService, User } from '@admin/services/userService';
import { roleService, Role } from '@admin/services/roleService';
import { departmentService, Department } from '@admin/services/departmentService';
import { useAuth } from '../contexts/AuthContext';
import { handleApiError } from '@shared/utils/errorHandler';
import QRCode from 'react-qr-code';
import { QRCodeService } from '@shared/services/qrCodeService';

const { Option } = Select;
const { Search } = Input;
const { Text } = Typography;

// 生成用户二维码内容
const generateUserQRCode = (user: User) => {
  return QRCodeService.generateEmployeeQRCode({
    employee_id: user.employee_id || user.username,
    full_name: user.full_name,
    username: user.username
  });
};

interface UserManagementProps {}

const UserManagement: React.FC<UserManagementProps> = () => {
  const { user: currentUser } = useAuth();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { t } = useTranslation();
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalUsers, setTotalUsers] = useState(0);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userModalVisible, setUserModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [roleModalVisible, setRoleModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [roleForm] = Form.useForm();

  // 从URL参数读取筛选条件
  const getFiltersFromURL = useCallback(() => {
    return {
      search: searchParams.get('search') || '',
      department_id: searchParams.get('department_id') ? parseInt(searchParams.get('department_id')!) : undefined,
      role: searchParams.get('role') || undefined,
      account_status: (searchParams.get('account_status') as 'active' | 'inactive') || 'active',
      page: parseInt(searchParams.get('page') || '1'),
      pageSize: parseInt(searchParams.get('pageSize') || '10'),
    };
  }, [searchParams]);

  // 更新URL参数
  const updateURLParams = useCallback((newParams: Record<string, string | number | undefined>) => {
    const currentParams = new URLSearchParams(searchParams);
    
    Object.entries(newParams).forEach(([key, value]) => {
      if (value !== undefined && value !== '' && value !== 'active') {
        currentParams.set(key, value.toString());
      } else {
        currentParams.delete(key);
      }
    });
    
    // 如果页码为1，则从URL中移除page参数
    if (newParams.page === 1) {
      currentParams.delete('page');
    }
    
    setSearchParams(currentParams, { replace: true });
  }, [searchParams, setSearchParams]);

  // 加载用户列表
  const loadUsers = useCallback(async () => {
    setLoading(true);
    try {
      const filters = getFiltersFromURL();
      
      // 将角色ID转换为角色代码
      let roleCode: string | undefined;
      if (filters.role && filters.role !== '') {
        const roleId = parseInt(filters.role);
        if (!isNaN(roleId)) {
          const selectedRole = roles.find(r => r.id === roleId);
          roleCode = selectedRole?.code;
        }
      }
      
      const response = await userService.getUsers({
        page: filters.page,
        page_size: filters.pageSize,
        search: filters.search,
        department_id: filters.department_id,
        roles: roleCode ? [roleCode] : undefined,
        account_status: filters.account_status,
      });
      setUsers(response.items);
      setTotalUsers(response.total); // 更新总数
    } catch (error) {
      message.error(t('messages.loadUserListFailed'));
    } finally {
      setLoading(false);
    }
  }, [getFiltersFromURL, roles, t]);

  // 加载角色列表
  const loadRoles = useCallback(async () => {
    try {
      const roles = await roleService.getRoles();
      setRoles(roles);
    } catch (error) {
      message.error(t('messages.loadRoleListFailed'));
    }
  }, [t]);

  // 加载部门列表
  const loadDepartments = useCallback(async () => {
    try {
      const departments = await departmentService.getDepartments();
      setDepartments(departments);
    } catch (error) {
      message.error(t('messages.loadDepartmentListFailed'));
    }
  }, [t]);

  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  useEffect(() => {
    loadRoles();
    loadDepartments();
  }, [loadRoles, loadDepartments]);

  // 处理表格变化
  const handleTableChange = (paginationInfo: any) => {
    updateURLParams({
      page: paginationInfo.current,
      pageSize: paginationInfo.pageSize,
    });
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    updateURLParams({ search: value });
  };

  // 处理筛选
  const handleFilter = (key: string, value: any) => {
    updateURLParams({ [key]: value });
  };

  // 显示新增用户模态框
  const showCreateModal = () => {
    setSelectedUser(null);
    form.resetFields();
    setUserModalVisible(true);
  };

  // 显示编辑用户模态框
  const showEditModal = (user: User) => {
    setSelectedUser(user);
    form.setFieldsValue({
      ...user,
      
    });
    setUserModalVisible(true);
  };

  // 显示用户详情
  const showUserDetail = (user: User) => {
    setSelectedUser(user);
    setDetailModalVisible(true);
  };

  // 显示密码重置模态框
  const showPasswordModal = (user: User) => {
    setSelectedUser(user);
    passwordForm.resetFields();
    setPasswordModalVisible(true);
  };

  // 显示角色分配模态框
  const showRoleModal = (user: User) => {
    // 安全检查：禁止给自己分配角色
    if (currentUser && user.id === currentUser.id) {
      message.error(t('user.cannotAssignRoleToSelf'));
      return;
    }
    
    setSelectedUser(user);
    roleForm.setFieldsValue({
      role_id: user.role_id,
    });
    setRoleModalVisible(true);
  };

  // 保存用户
  const handleSaveUser = async (values: any) => {
    try {
      if (selectedUser) {
        // 更新用户
        await userService.updateUser(selectedUser.id, values);
        message.success(t('messages.userUpdateSuccess'));
      } else {
        // 创建用户
        await userService.createUser(values);
        message.success(t('messages.userCreateSuccess'));
      }
      setUserModalVisible(false);
      loadUsers();
    } catch (error: any) {
      message.error(handleApiError(error, t('messages.operationFailed')));
    }
  };

  // 重置密码
  const handleResetPassword = async (values: any) => {
    if (!selectedUser) return;
    
    try {
      await userService.resetPassword(selectedUser.id, values);
      message.success(t('messages.passwordResetSuccess'));
      setPasswordModalVisible(false);
    } catch (error: any) {
      message.error(handleApiError(error, t('messages.passwordResetFailed')));
    }
  };

  // 分配角色
  const handleAssignRoles = async (values: any) => {
    if (!selectedUser) return;
    
    // 安全检查：禁止给自己分配角色
    if (currentUser && selectedUser.id === currentUser.id) {
      message.error(t('user.cannotAssignRoleToSelf'));
      return;
    }
    
    try {
      await userService.updateUser(selectedUser.id, {
        role_id: values.role_id,
      });
      message.success(t('messages.roleAssignSuccess'));
      setRoleModalVisible(false);
      loadUsers();
    } catch (error: any) {
      message.error(handleApiError(error, t('messages.roleAssignFailed')));
    }
  };

  // 切换用户状态
  const handleToggleStatus = async (user: User) => {
    try {
      await userService.toggleUserStatus(user.id);
      message.success(t('messages.userStatusUpdateSuccess'));
      loadUsers();
    } catch (error: any) {
      message.error(handleApiError(error, t('messages.statusUpdateFailed')));
    }
  };

  // 删除功能已移除，仅保留禁用功能
  // 用户管理通过状态切换进行管理

  // 获取账户状态标签
  const getAccountStatusTag = (status: string) => {
    const statusMap = {
      active: { color: 'green', text: t('user.statusActive') },
      disabled: { color: 'red', text: t('user.statusDisabled') },
      locked: { color: 'orange', text: t('user.statusLocked') },
      pending: { color: 'blue', text: t('user.statusPending') },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取密码状态标签
  const getPasswordStatusTag = (status: string) => {
    const statusMap = {
      normal: { color: 'green', text: t('user.passwordStatusNormal') },
      need_reset: { color: 'orange', text: t('user.passwordStatusNeedReset') },
      temporary: { color: 'blue', text: t('user.passwordStatusTemporary') },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns: ColumnsType<User> = [
    {
      title: t('user.username'),
      dataIndex: 'username',
      key: 'username',
      width: 120,
      render: (text, record) => (
        <Space>
          <Button type="link" onClick={() => showUserDetail(record)}>
            {text}
          </Button>
          {record.is_superuser && <Tag color="red">{t('user.superAdmin')}</Tag>}
        </Space>
      ),
    },
    {
      title: t('user.fullName'),
      dataIndex: 'full_name',
      key: 'full_name',
      width: 100,
    },
    {
      title: t('user.displayName'),
      dataIndex: 'display_name',
      key: 'display_name',
      width: 120,
      render: (display_name: string | undefined) => display_name || '-',
    },
    {
      title: t('user.email'),
      dataIndex: 'email',
      key: 'email',
      width: 180,
      render: (email: string | undefined) => {
        if (!email) return '-';
        return (
          <Button 
            type="link" 
            onClick={() => window.open(`mailto:${email}`, '_blank')}
            style={{ padding: 0, height: 'auto' }}
          >
            {email}
          </Button>
        );
      },
    },
    {
      title: t('user.phone'),
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
      render: (phone: string | undefined) => phone || '-',
    },
    {
      title: t('user.employeeId'),
      dataIndex: 'employee_id',
      key: 'employee_id',
      width: 100,
    },
    {
      title: t('user.position'),
      dataIndex: 'position',
      key: 'position',
      width: 120,
      render: (position: string | undefined) => position || '-',
    },
    {
      title: t('user.department'),
      dataIndex: 'department_name',
      key: 'department_name',
      width: 120,
      render: (department_name: string | undefined, record: User) => (
        <Button 
          type="link" 
          onClick={() => navigate(`/admin/departments/${record.department_id}`)}
          style={{ padding: 0, height: 'auto' }}
          disabled={!record.department_id}
        >
          {department_name || '-'}
        </Button>
      ),
    },
    {
      title: t('user.role'),
      dataIndex: 'role_id',
      key: 'role_id',
      width: 150,
      render: (role_id: number | undefined) => {
        if (!role_id) return '-';
        const role = roles?.find(r => r.id === role_id);
        return role ? (
          <Button 
            type="link" 
            onClick={() => navigate(`/admin/roles/${role_id}`)}
            style={{ padding: 0, height: 'auto' }}
          >
            <Tag color="blue">
              {role.name}
            </Tag>
          </Button>
        ) : '-';
      },
    },
    {
      title: t('user.accountStatus'),
      dataIndex: 'account_status',
      key: 'account_status',
      width: 100,
      render: (status: string | undefined) => status ? getAccountStatusTag(status) : '-',
    },
    {
      title: t('user.passwordStatus'),
      dataIndex: 'password_status',
      key: 'password_status',
      width: 100,
      render: (status: string | undefined) => status ? getPasswordStatusTag(status) : '-',
    },
    {
      title: t('user.lastLogin'),
      dataIndex: 'last_login_at',
      key: 'last_login_at',
      width: 150,
      render: (time: string | undefined) => time ? dayjs(time).format(DATE_FORMATS.DATE_TIME) : '-',
    },
    {
      title: t('common.operation'),
      key: 'actions',
      width: 250,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title={t('user.viewDetails')}>
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => showUserDetail(record)}
            />
          </Tooltip>
          <Tooltip title={t('user.editUser')}>
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => showEditModal(record)}
            />
          </Tooltip>
          {currentUser && record.id !== currentUser.id && (
            <Tooltip title={t('user.assignRole')}>
              <Button
                type="text"
                size="small"
                icon={<UserOutlined />}
                onClick={() => showRoleModal(record)}
              />
            </Tooltip>
          )}
          <Tooltip title={t('user.resetPassword')}>
            <Button
              type="text"
              size="small"
              icon={<KeyOutlined />}
              onClick={() => showPasswordModal(record)}
            />
          </Tooltip>
          <Tooltip title={record.account_status === 'active' ? t('user.disable') : t('user.enable')}>
            <Button
              type="text"
              size="small"
              icon={record.account_status === 'active' ? <LockOutlined /> : <UnlockOutlined />}
              onClick={() => handleToggleStatus(record)}
            />
          </Tooltip>
          {/* 删除功能已移除，仅保留禁用功能 */}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Search
              placeholder={t('user.searchPlaceholder')}
              value={getFiltersFromURL().search}
              onSearch={handleSearch}
              onChange={(e) => {
                if (e.target.value === '') {
                  handleSearch('');
                }
              }}
              enterButton={<SearchOutlined />}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder={t('user.selectDepartment')}
              allowClear
              style={{ width: '100%' }}
              value={getFiltersFromURL().department_id}
              onChange={(value) => handleFilter('department_id', value)}
            >
              {departments?.map(dept => (
                <Option key={dept.id} value={dept.id}>
                  {dept.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder={t('user.selectRole')}
              allowClear
              style={{ width: '100%' }}
              value={getFiltersFromURL().role}
              onChange={(value) => handleFilter('role', value)}
            >
              {roles?.map(role => (
                <Option key={role.id} value={role.id}>
                  {role.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder={t('user.accountStatus')}
              allowClear
              style={{ width: '100%' }}
              value={getFiltersFromURL().account_status}
              onChange={(value) => handleFilter('account_status', value)}
            >
              <Option value="active">{t('user.statusActive')}</Option>
              <Option value="disabled">{t('user.statusDisabled')}</Option>
              <Option value="locked">{t('user.statusLocked')}</Option>
              <Option value="pending">{t('user.statusPending')}</Option>
            </Select>
          </Col>
          <Col span={6}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={showCreateModal}
              >
                {t('user.addUser')}
              </Button>
              {/* 批量导入功能暂未实现
              <Button
                icon={<UploadOutlined />}
                onClick={() => {}}
              >
                批量导入
              </Button>
              */}
              <Button
                icon={<ReloadOutlined />}
                onClick={loadUsers}
              >
                {t('common.refresh')}
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            current: getFiltersFromURL().page,
            pageSize: getFiltersFromURL().pageSize,
            total: totalUsers, // 使用 totalUsers 作为总数
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => t('common.totalRecords', { total }),
          }}
          onChange={handleTableChange}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 新增/编辑用户模态框 */}
      <Modal
        title={selectedUser ? t('user.editUser') : t('user.addUser')}
        open={userModalVisible}
        onCancel={() => setUserModalVisible(false)}
        onOk={() => form.submit()}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveUser}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="username"
                label={t('user.username')}
                rules={[
                  { required: true, message: t('user.usernameRequired') },
                  { min: 3, max: 50, message: t('user.usernameLengthRule') },
                ]}
              >
                <Input placeholder={t('user.username')} disabled={!!selectedUser} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label={t('user.email')}
                rules={[
                  { required: true, message: t('user.emailRequired') },
                  { type: 'email', message: t('user.emailValid') },
                ]}
              >
                <Input placeholder={t('user.email')} />
              </Form.Item>
            </Col>
          </Row>

          {!selectedUser && (
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="password"
                  label={t('user.password')}
                  rules={[
                    { required: true, message: t('user.passwordRequired') },
                    { min: 6, message: t('user.passwordMinLength') }
                  ]}
                >
                  <Input.Password placeholder={t('user.password')} />
                </Form.Item>
              </Col>
            </Row>
          )}

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="full_name" label={t('user.fullName')}>
                <Input placeholder={t('user.fullName')} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="phone" label={t('user.phone')}>
                <Input placeholder={t('user.phone')} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="employee_id" label={t('user.employeeId')}>
                <Input placeholder={t('user.employeeId')} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="position" label={t('user.position')}>
                <Input placeholder={t('user.position')} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="department_id" label={t('user.department')}>
                <Select placeholder={t('user.selectDepartment')} allowClear>
                  {departments.map(department => (
                    <Option key={department.id} value={department.id}>
                      {department.name} ({department.code})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

          </Row>

          <Form.Item name="role_id" label={t('user.role')}>
            <Select placeholder={t('user.selectRole')}>
              {roles?.map(role => (
                <Option key={role.id} value={role.id}>
                  {role.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 密码重置模态框 */}
      <Modal
        title={t('user.resetPassword')}
        open={passwordModalVisible}
        onCancel={() => setPasswordModalVisible(false)}
        onOk={() => passwordForm.submit()}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleResetPassword}
        >
          <Form.Item
            name="new_password"
            label={t('user.newPassword')}
            rules={[
              { required: true, message: t('user.newPasswordRequired') },
              { min: 6, message: t('user.passwordMinLength') }
            ]}
          >
            <Input.Password placeholder={t('user.newPassword')} />
          </Form.Item>
          <Form.Item name="temporary" valuePropName="checked">
            <Switch /> {t('user.setAsTemporaryPassword')}
          </Form.Item>
        </Form>
      </Modal>

      {/* 角色分配模态框 */}
      <Modal
        title={t('user.assignRole')}
        open={roleModalVisible}
        onCancel={() => setRoleModalVisible(false)}
        onOk={() => roleForm.submit()}
      >
        <Form
          form={roleForm}
          layout="vertical"
          onFinish={handleAssignRoles}
        >
          <Form.Item
            name="role_id"
            label={t('user.selectRole')}
            rules={[{ required: true, message: t('user.roleRequired') }]}
          >
            <Select placeholder={t('user.selectRole')}>
              {roles?.map(role => (
                <Option key={role.id} value={role.id}>
                  <Space>
                    <span>{role.name}</span>
                    <span style={{ color: '#666' }}>({role.description})</span>
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 用户详情模态框 */}
      <Modal
        title={t('user.userDetails')}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedUser && (
          <div>
            <Row gutter={[16, 16]}>
              {/* 左侧用户信息 */}
              <Col span={16}>
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <strong>{t('user.username')}:</strong> {selectedUser.username}
                  </Col>
                  <Col span={12}>
                    <strong>{t('user.fullName')}:</strong> {selectedUser.full_name || '-'}
                  </Col>
                  <Col span={12}>
                    <strong>{t('user.email')}:</strong> {selectedUser.email}
                  </Col>
                  <Col span={12}>
                    <strong>{t('user.phone')}:</strong> {selectedUser.phone || '-'}
                  </Col>
                  <Col span={12}>
                    <strong>{t('user.employeeId')}:</strong> {selectedUser.employee_id || '-'}
                  </Col>
                  <Col span={12}>
                    <strong>{t('user.position')}:</strong> {selectedUser.position || '-'}
                  </Col>
                  <Col span={12}>
                    <strong>{t('user.department')}:</strong> {selectedUser.department_name || '-'}
                  </Col>

                  <Col span={12}>
                    <strong>{t('user.accountStatus')}:</strong> {getAccountStatusTag(selectedUser.account_status)}
                  </Col>
                  <Col span={12}>
                    <strong>{t('user.passwordStatus')}:</strong> {getPasswordStatusTag(selectedUser.password_status)}
                  </Col>
                  <Col span={12}>
                    <strong>{t('user.lastLogin')}:</strong> {selectedUser.last_login_at ? dayjs(selectedUser.last_login_at).format(DATE_FORMATS.DATE_TIME) : '-'}
                  </Col>
                  <Col span={12}>
                    <strong>{t('common.createdAt')}:</strong> {dayjs(selectedUser.created_at).format(DATE_FORMATS.DATE_TIME)}
                  </Col>
                  <Col span={24}>
                    <strong>{t('user.role')}:</strong>
                    <div style={{ marginTop: 8 }}>
                      {selectedUser.role_id ? (
                        <Tag color="blue">
                          {roles?.find(r => r.id === selectedUser.role_id)?.name || t('user.unknownRole')}
                        </Tag>
                      ) : (
                        <span style={{ color: '#999' }}>{t('user.noRole')}</span>
                      )}
                    </div>
                  </Col>
                </Row>
              </Col>

              {/* 右侧用户二维码 */}
              <Col span={8}>
                <Card size="small" bordered>
                  <div style={{ display: 'flex', justifyContent: 'center' }}>
                    <div
                      style={{
                        position: 'relative',
                        background: '#ffffff',
                        padding: 16,
                        border: '1px solid #e5e7eb',
                        borderRadius: 12,
                        boxShadow: '0 2px 6px rgba(0,0,0,0.04)'
                      }}
                    >
                      <span style={{ position: 'absolute', top: 6, left: 6, width: 18, height: 18, borderTop: '3px solid #1677ff', borderLeft: '3px solid #1677ff', borderRadius: 4 }} />
                      <span style={{ position: 'absolute', top: 6, right: 6, width: 18, height: 18, borderTop: '3px solid #1677ff', borderRight: '3px solid #1677ff', borderRadius: 4 }} />
                      <span style={{ position: 'absolute', bottom: 6, left: 6, width: 18, height: 18, borderBottom: '3px solid #1677ff', borderLeft: '3px solid #1677ff', borderRadius: 4 }} />
                      <span style={{ position: 'absolute', bottom: 6, right: 6, width: 18, height: 18, borderBottom: '3px solid #1677ff', borderRight: '3px solid #1677ff', borderRadius: 4 }} />
                      
                      {/* 二维码 */}
                      <QRCode 
                        value={generateUserQRCode(selectedUser)} 
                        size={160} 
                        fgColor="#1f2937" 
                        bgColor="#ffffff"
                        level="M"
                        title={t('user.employeeQRCode')}
                      />
                    </div>
                  </div>
                  <div style={{ textAlign: 'center', marginTop: 8 }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>{t('user.userQRCode')}</Text>
                  </div>
                </Card>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default UserManagement; 