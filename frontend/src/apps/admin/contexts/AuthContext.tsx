import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { message } from 'antd';
import { authService } from '../services/authService';

interface User {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  department?: string;
  department_id?: number;
  role: string;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  // 权限相关字段
  role_id?: number;
  role_name?: string;
  role_code?: string;
  permissions: string[];
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  updateUser: (user: User) => void;
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // 检查本地存储的token并验证
  useEffect(() => {
    let isMounted = true;
    
    const checkAuth = async () => {
      const token = localStorage.getItem('access_token');
      if (token) {
        try {
          const currentUser = await authService.getCurrentUser();
          if (isMounted) {
            setUser(currentUser);
          }
        } catch (error) {
          // Token可能已过期，清除本地存储
          if (isMounted) {
            localStorage.removeItem('access_token');
            localStorage.removeItem('user');
          }
        }
      }
      if (isMounted) {
        setLoading(false);
      }
    };

    checkAuth();

    return () => {
      isMounted = false;
    };
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      const response = await authService.login(username, password);
      
      // 保存token和用户信息
      localStorage.setItem('access_token', response.access_token);
      localStorage.setItem('user', JSON.stringify(response.user));
      
      setUser(response.user);
      return true;
    } catch (error: any) {
      console.error('Login error:', error);
      
      // 根据不同的错误类型显示不同的提示信息
      let errorMessage = '登录失败，请稍后重试';
      
      if (error.response?.status === 401) {
        // 认证失败，显示具体的错误原因
        errorMessage = error.response?.data?.detail || '用户名或密码错误';
      } else if (error.response?.status === 403) {
        errorMessage = '账号已被禁用或锁定，请联系管理员';
      } else if (error.response?.status === 404) {
        errorMessage = '登录服务不可用，请联系管理员';
      } else if (error.response?.status === 429) {
        errorMessage = '登录尝试次数过多，请稍后再试';
      } else if (error.response?.status >= 500) {
        // 服务器错误
        if (error.response?.status === 500) {
          errorMessage = '服务器内部错误，请联系管理员';
        } else if (error.response?.status === 502) {
          errorMessage = '网关错误，服务器暂时不可用';
        } else if (error.response?.status === 503) {
          errorMessage = '服务暂时不可用，请稍后重试';
        } else if (error.response?.status === 504) {
          errorMessage = '网关超时，请稍后重试';
        } else {
          errorMessage = `服务器错误 (${error.response?.status})，请联系管理员`;
        }
      } else if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
        errorMessage = '网络连接失败，请检查网络设置';
      } else if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
        errorMessage = '请求超时，请检查网络连接';
      } else if (error.response?.status === 0) {
        errorMessage = '无法连接到服务器，请检查服务器状态';
      } else if (error.message?.includes('Failed to fetch')) {
        errorMessage = '无法连接到服务器，请检查网络连接';
      }
      
      message.error(errorMessage, 8); // 显示8秒
      return false;
    }
  };

  const logout = async () => {
    try {
      await authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // 清除本地存储
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
      setUser(null);
    }
  };

  const updateUser = (newUser: User) => {
    setUser(newUser);
    localStorage.setItem('user', JSON.stringify(newUser));
  };

  // 权限检查函数
  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    if (user.is_superuser) return true;
    return user.permissions.includes(permission);
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    if (!user) return false;
    if (user.is_superuser) return true;
    return permissions.some(permission => user.permissions.includes(permission));
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    if (!user) return false;
    if (user.is_superuser) return true;
    return permissions.every(permission => user.permissions.includes(permission));
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    loading,
    login,
    logout,
    updateUser,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
