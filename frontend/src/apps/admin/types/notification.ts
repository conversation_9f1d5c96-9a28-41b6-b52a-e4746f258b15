// 通知相关类型定义

export interface Notification {
  id: number;
  user_id: number;
  title: string;
  content: string;
  notification_type: 'inventory_alert' | 'approval_flow' | 'system' | 'other';
  status: 'unread' | 'read';
  business_data?: Record<string, any>;
  action_url?: string;
  created_at: string;
  read_at?: string;
}

export interface NotificationEmail {
  id: number;
  notification_id: number;
  user_id: number;
  email_status: 'pending' | 'sent' | 'failed';
  failure_reason?: string;
  sent_at?: string;
  created_at: string;
}

export interface SystemConfig {
  id: number;
  config_key: string;
  config_value: string;
  description: string;
  updated_at: string;
}

export interface NotificationStats {
  total: number;
  unread: number;
  read: number;
  by_type: {
    inventory_alert: number;
    approval_flow: number;
    system?: number;
    other?: number;
  };
}

export interface EmailStats {
  total: number;
  pending: number;
  sent: number;
  failed: number;
  today_sent: number;
  success_rate: number;
}

export interface NotificationListParams {
  page?: number;
  size?: number;
  notification_type?: string;
  status?: string;
  user_id?: number;
  start_date?: string;
  end_date?: string;
  search?: string;
  date_range?: any; // 用于表单处理，实际发送时会被转换为 start_date 和 end_date
}

export interface NotificationListResponse {
  data: Notification[];
  total: number;
  page: number;
  size: number;
}

export interface EmailListParams {
  page?: number;
  size?: number;
  email_status?: string;
  user_id?: number;
  start_date?: string;
  end_date?: string;
}

export interface EmailListResponse {
  data: NotificationEmail[];
  total: number;
  page: number;
  size: number;
}

export interface BatchReadRequest {
  notification_ids: number[];
}

export interface BatchDeleteRequest {
  notification_ids: number[];
}

export interface TestEmailRequest {
  to_email: string;
  subject: string;
  content: string;
}

export interface SmtpConfig {
  smtp_host: string;
  smtp_port: number;
  smtp_username: string;
  smtp_password: string;
  smtp_use_tls: boolean;
  notification_enabled: boolean;
  email_send_interval: number;
}
