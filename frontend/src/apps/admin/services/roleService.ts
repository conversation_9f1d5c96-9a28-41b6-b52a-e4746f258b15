import { apiClient } from './authService';

export interface Permission {
  id: number;
  code: string;
  name: string;
  description?: string;
  module: string;
  is_active: boolean;
  is_system: boolean;
  created_at: string;
}

export interface Role {
  id: number;
  code: string;
  name: string;
  description?: string;
  is_active: boolean;
  is_system: boolean;
  created_at: string;
  permissions: Permission[];
}

export interface CreateRoleRequest {
  code: string;
  name: string;
  description?: string;
  permission_codes?: string[];
}

export interface UpdateRoleRequest {
  name?: string;
  description?: string;
  permission_codes?: string[];
}

export interface RoleQuery {
  search?: string;
  is_active?: boolean;
}

export interface PermissionQuery {
  page?: number;
  page_size?: number;
  module?: string;
  search?: string;
}

export interface PermissionListResponse {
  items: Permission[];
  total: number;
  page: number;
  page_size: number;
  pages: number;
}

export interface UserRoleAssignRequest {
  user_id: number;
  role_codes: string[];
}

export interface UserPermissions {
  user_id: number;
  roles: string[];
  permissions: string[];
  effective_permissions: Permission[];
}

export interface RolePermissionMatrix {
  [roleCode: string]: {
    role_name: string;
    permissions: string[];
  };
}

export interface PermissionNode {
  id: number;
  code: string;
  name: string;
  description?: string;
  module: string;
  is_active: boolean;
  permissions: Permission[];
}

class RoleService {
  /**
   * 获取所有权限
   */
  async getPermissions(query?: PermissionQuery): Promise<Permission[]> {
    const response = await apiClient.get<Permission[]>('/roles/permissions', {
      params: query,
    });
    return response.data;
  }

  /**
   * 获取权限分组列表
   */
  async getPermissionGroups(): Promise<PermissionNode[]> {
    const response = await apiClient.get<PermissionNode[]>('/roles/permissions/groups');
    return response.data;
  }

  /**
   * 获取角色列表
   */
  async getRoles(query?: RoleQuery): Promise<Role[]> {
    const response = await apiClient.get<Role[]>('/roles', {
      params: query,
    });
    return response.data;
  }

  /**
   * 根据ID获取角色详情
   */
  async getRoleById(id: number): Promise<Role> {
    const response = await apiClient.get<Role>(`/roles/${id}`);
    return response.data;
  }

  /**
   * 创建新角色
   */
  async createRole(data: CreateRoleRequest): Promise<Role> {
    const response = await apiClient.post<Role>('/roles', data);
    return response.data;
  }

  /**
   * 更新角色
   */
  async updateRole(id: number, data: UpdateRoleRequest): Promise<Role> {
    const response = await apiClient.put<Role>(`/roles/${id}`, data);
    return response.data;
  }

  /**
   * 删除角色
   */
  async deleteRole(id: number): Promise<void> {
    await apiClient.delete(`/roles/${id}`);
  }

  /**
   * 为用户分配角色
   */
  async assignRoles(data: UserRoleAssignRequest): Promise<void> {
    await apiClient.post('/roles/assign', data);
  }

  /**
   * 获取用户的有效权限
   */
  async getUserPermissions(userId: number): Promise<UserPermissions> {
    const response = await apiClient.get<UserPermissions>(`/roles/users/${userId}/permissions`);
    return response.data;
  }

  /**
   * 获取角色权限矩阵
   */
  async getRolePermissionMatrix(): Promise<RolePermissionMatrix> {
    const response = await apiClient.get<RolePermissionMatrix>('/roles/matrix');
    return response.data;
  }

  /**
   * 初始化系统数据（权限和角色）
   */
  async initSystemData(): Promise<{ permissions_created: number; roles_created: number }> {
    const response = await apiClient.post('/roles/init-system-data');
    return response.data;
  }

  /**
   * 获取系统信息（预定义权限和角色）
   */
  async getSystemInfo(): Promise<{
    permission_modules: string[];
    predefined_roles: Array<{
      code: string;
      name: string;
      description: string;
      permissions: string[];
    }>;
  }> {
    const response = await apiClient.get('/roles/system-info');
    return response.data;
  }
}

export const roleService = new RoleService(); 
