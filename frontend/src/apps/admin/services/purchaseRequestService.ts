import { apiClient } from './authService';

export interface RequestFlowHistory {
  id: number;
  request_id: number;
  action: string;
  from_status?: string;
  to_status: string;
  operator_id: number;
  operator_name: string;
  approval_level?: string;
  comments?: string;
  created_at: string;
}

export interface PurchaseRequest {
  id: number;
  request_no: string;
  department_id: number;
  submitter_id: number;
  // 添加名称字段
  department_name?: string;
  submitter_name?: string;
  status: string;
  final_total: number | string; // 可能是Numeric类型，需要处理
  notes?: string;
  created_at: string;
  updated_at: string;
  submitted_at?: string;
  items: PurchaseRequestItem[];
  // 添加流转历史
  flow_history?: RequestFlowHistory[];
}

export interface CreateRequestRequest {
  department_id: number;
  notes?: string;
  cart_item_ids: number[]; // 购物车项目ID列表
}

export interface PurchaseRequestItem {
  id: number;
  request_id: number;
  item_id: number;
  item_code: string;
  item_name: string;
  item_image_url?: string; // 物品图片URL
  spq_quantity: number | string; // 可能是Numeric类型，需要处理
  spq_count: number;
  spq_unit: string;
  
  // 最终价格（审批后锁定）
  final_unit_price?: number | string;
  final_total_price?: number | string;
  final_supplier_id?: number;
  price_locked_at?: string;
  
  // 预估价格（申请阶段）
  estimated_unit_price?: number | string;
  estimated_total_price?: number | string;
  
  notes?: string;
  
  // 汇率信息（后端动态添加）
  exchange_rate_info?: {
    currency_code: string;
    original_unit_price: number;
    original_total_price: number;
    exchange_rate: number;
    usd_unit_price: number;
    usd_total_price: number;
    rate_type: 'current_month' | 'historical';
    effective_month: string;
    is_valid: boolean;
    warning?: string;
    error?: string;
  };
  
  // 供应商信息（后端动态添加）
  preferred_supplier?: {
    id: number;
    supplier_id: number;
    item_id: number;
    priority: number;
    status: string;
    supplier_name: string;
  };
  available_suppliers?: Array<{
    id: number;
    supplier_id: number;
    item_id: number;
    priority: number;
    status: string;
  }>;
  
  // 编辑状态标记（前端使用）
  _markedForRemoval?: boolean;
}

export interface RequestSummary {
  total_requests: number;
  pending_submission: number;
  under_review: number;
  under_principle_approval: number;
  under_final_approval: number;
  approved: number;
  rejected: number;
}

// 新增汇总分析相关接口
export interface SummaryAnalysisRequest {
  request_ids: number[];
  analysis_name?: string;
}

export interface ItemSummary {
  item_id: number;
  item_code: string;
  item_name: string;
  category_name?: string;
  image_url?: string;
  preferred_supplier_id?: number;
  preferred_supplier_name?: string;
  total_spq_quantity: number;
  total_spq_count: number;
  total_quantity?: number;      // 总数量（SPQ数量 × SPQ个数）
  spq_unit: string;
  single_spq_quantity: number;  // 单个物品的SPQ数量
  single_spq_count: number;     // 单个物品的SPQ包装数量
  total_amount: number;
  request_count: number;
  request_ids: string[];  // 申请单ID列表
  average_price: number;
}

export interface DepartmentSummary {
  department_id: number;
  department_name: string;
  request_count: number;
  total_amount: number;
  percentage: number;
}

export interface SupplierSummary {
  supplier_id: number;
  supplier_name: string;
  item_count: number;
  total_amount: number;
  average_price: number;
  percentage: number;
}

export interface RequestSummaryAnalysis {
  total_requests: number;
  total_departments: number;
  total_amount: number;
  analysis_time: string;
  item_summaries: ItemSummary[];
  department_summaries: DepartmentSummary[];
  supplier_summaries: SupplierSummary[];
  status_distribution: Record<string, number>;
  requests: Array<{
    id: number;
    request_id: string;
    request_no: string;
    department_name: string;
    submitter_name: string;
    status: string;
    final_total: number;
    created_at: string;
  }>;
}

class PurchaseRequestService {
  // 从购物车创建采购申请
  async createRequestFromCart(requestData: CreateRequestRequest): Promise<PurchaseRequest> {
    const response = await apiClient.post('/purchase/requests', requestData);
    return response.data;
  }

  // 获取采购申请列表
  async getPurchaseRequests(filters?: any): Promise<PurchaseRequest[]> {
    const response = await apiClient.get('/purchase/requests', { params: filters });
    return response.data;
  }

  // 获取采购申请详情
  async getPurchaseRequest(requestId: number): Promise<PurchaseRequest> {
    const response = await apiClient.get(`/purchase/requests/${requestId}`);
    return response.data;
  }

  // 获取申请明细
  async getRequestItems(requestId: number): Promise<PurchaseRequestItem[]> {
    const response = await apiClient.get(`/purchase/requests/${requestId}/items`);
    return response.data;
  }

  // 获取申请流转历史
  async getRequestFlowHistory(requestId: number): Promise<RequestFlowHistory[]> {
    const response = await apiClient.get(`/purchase/approval/requests/${requestId}/history`);
    return response.data;
  }

  // 获取申请汇总统计
  async getRequestsSummary(departmentId?: number): Promise<RequestSummary> {
    const params = departmentId ? { department_id: departmentId } : {};
    const response = await apiClient.get('/purchase/requests/summary', { params });
    return response.data;
  }

  // 提交申请
  async submitRequest(requestId: number): Promise<PurchaseRequest> {
    const response = await apiClient.post(`/purchase/requests/${requestId}/submit`);
    return response.data;
  }

  // 撤回申请
  async withdrawRequest(requestId: number): Promise<PurchaseRequest> {
    const response = await apiClient.post(`/purchase/requests/${requestId}/withdraw`);
    return response.data;
  }

  // 撤销申请回到待提交状态
  async withdrawToPending(requestId: number): Promise<PurchaseRequest> {
    const response = await apiClient.post(`/purchase/requests/${requestId}/withdraw-to-pending`);
    return response.data;
  }

  // 更新申请状态
  async updateRequestStatus(requestId: number, newStatus: string, comments?: string): Promise<PurchaseRequest> {
    const response = await apiClient.put(`/purchase/requests/${requestId}/status`, {
      new_status: newStatus,
      comments: comments
    });
    return response.data;
  }

  // 审批申请
  async approveRequest(requestId: number, action: 'approve' | 'reject' | 'return', comments: string): Promise<PurchaseRequest> {
    // 首先获取申请详情，了解当前状态
    const request = await this.getPurchaseRequest(requestId);
    
    let response;
    
    // 根据当前状态和操作类型调用不同的审批接口
    if (request.status === 'under_review') {
      // 部门经理复核
      response = await apiClient.post(`/purchase/approval/requests/${requestId}/review`, {
        approved: action === 'approve',
        comments: comments
      });
    } else if (request.status === 'under_principle_approval') {
      // 物品管理员主管审批
      response = await apiClient.post(`/purchase/approval/requests/${requestId}/principle-approval`, {
        approved: action === 'approve',
        comments: comments
      });
    } else if (request.status === 'under_final_approval') {
      // 公司主管最终审批
      response = await apiClient.post(`/purchase/approval/requests/${requestId}/final-approval`, {
        approved: action === 'approve',
        comments: comments
      });
    } else {
      // 如果状态不匹配，回退到统一的状态更新接口
      response = await apiClient.put(`/purchase/requests/${requestId}/status`, {
        action: action,
        comments: comments
      });
    }
    
    // 审批成功后，重新获取完整的申请信息
    if (response.status === 200) {
      return await this.getPurchaseRequest(requestId);
    }
    
    // 如果审批失败，返回原始申请信息
    return request;
  }

  // 更新申请信息
  async updateRequest(requestId: number, data: any): Promise<PurchaseRequest> {
    const response = await apiClient.put(`/purchase/requests/${requestId}`, data);
    return response.data;
  }

  // 删除采购申请
  async deleteRequest(requestId: number, returnToCart: boolean = false): Promise<{ message: string }> {
    const response = await apiClient.delete(`/purchase/requests/${requestId}`, {
      params: { return_to_cart: returnToCart }
    });
    return response.data;
  }

  // 添加物品到申请
  async addItemToRequest(requestId: number, itemData: { item_id: number; spq_count: number }): Promise<PurchaseRequestItem> {
    const response = await apiClient.post(`/purchase/requests/${requestId}/items`, itemData);
    return response.data;
  }

  // 更新申请物品
  async updateRequestItem(itemId: number, updates: { spq_count?: number }): Promise<PurchaseRequestItem> {
    const response = await apiClient.put(`/purchase/requests/items/${itemId}`, updates);
    return response.data;
  }

  // 删除申请物品
  async deleteRequestItem(itemId: number): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.delete(`/purchase/requests/items/${itemId}`);
    return response.data;
  }

  // 获取采购申请汇总分析
  async getRequestsSummaryAnalysis(requestData: SummaryAnalysisRequest): Promise<RequestSummaryAnalysis> {
    const response = await apiClient.post('/purchase/requests/summary-analysis', requestData);
    return response.data;
  }

  // 获取汇总分析历史数据
  async getSummaryAnalysisHistory(requestIds: number[]): Promise<{
    item_history: Array<{ item_id: number; month: string; count: number }>;
    department_history: Array<{ department_id: number; month: string; amount: number }>;
    supplier_history: Array<{ supplier_id: number; month: string; amount: number }>;
  }> {
    const response = await apiClient.post('/purchase/requests/summary-analysis/history', {
      request_ids: requestIds
    });
    return response.data;
  }
}

export const purchaseRequestService = new PurchaseRequestService();
export default purchaseRequestService;
