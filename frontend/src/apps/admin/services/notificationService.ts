import { apiClient } from './authService';
import {
  Notification,
  NotificationEmail,
  SystemConfig,
  NotificationStats,
  EmailStats,
  NotificationListParams,
  NotificationListResponse,
  EmailListParams,
  EmailListResponse,
  BatchReadRequest,
  BatchDeleteRequest,
  TestEmailRequest,
  SmtpConfig
} from '../types/notification';

class NotificationService {
  // 通知管理API
  async getNotifications(params: NotificationListParams = {}): Promise<NotificationListResponse> {
    const response = await apiClient.get<NotificationListResponse>('/notifications', { params });
    return response.data;
  }

  async getNotification(id: number): Promise<Notification> {
    const response = await apiClient.get<Notification>(`/notifications/${id}`);
    return response.data;
  }

  async markNotificationRead(id: number): Promise<Notification> {
    const response = await apiClient.put<Notification>(`/notifications/${id}/read`);
    return response.data;
  }

  async batchMarkRead(data: BatchReadRequest): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.put<{ success: boolean; message: string }>('/notifications/batch-read', data);
    return response.data;
  }

  async deleteNotification(id: number): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.delete<{ success: boolean; message: string }>(`/notifications/${id}`);
    return response.data;
  }

  async batchDeleteNotifications(data: BatchDeleteRequest): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.delete<{ success: boolean; message: string }>('/notifications/batch', { data });
    return response.data;
  }

  // 邮件管理API
  async getNotificationEmails(params: EmailListParams = {}): Promise<EmailListResponse> {
    const response = await apiClient.get<EmailListResponse>('/notification-emails', { params });
    return response.data;
  }

  async triggerEmailSend(): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.post<{ success: boolean; message: string }>('/notification-emails/trigger');
    return response.data;
  }

  async getEmailLogs(params: EmailListParams = {}): Promise<EmailListResponse> {
    const response = await apiClient.get<EmailListResponse>('/notification-emails/logs', { params });
    return response.data;
  }

  // 系统配置API
  async getSystemConfigs(): Promise<SystemConfig[]> {
    const response = await apiClient.get<SystemConfig[]>('/system-configs');
    return response.data;
  }

  async updateSystemConfigs(configs: Partial<SmtpConfig>): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.put<{ success: boolean; message: string }>('/system-configs', configs);
    return response.data;
  }

  async sendTestEmail(data: TestEmailRequest): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.post<{ success: boolean; message: string }>('/system-configs/test-email', data);
    return response.data;
  }

  // 统计查询API
  async getNotificationStats(): Promise<NotificationStats> {
    const response = await apiClient.get<NotificationStats>('/notifications/stats');
    return response.data;
  }

  async getEmailStats(): Promise<EmailStats> {
    const response = await apiClient.get<EmailStats>('/notification-emails/stats');
    return response.data;
  }
}

export const notificationService = new NotificationService();
export default notificationService;
