import { apiClient } from './authService';

export interface ExchangeRate {
  id: number;
  currency_code: string;
  rate: string; // 后端返回的是Decimal字符串，如"148.500000"
  effective_month: string; // 后端返回的是ISO日期字符串，如"2024-01-01"
  status: string;
  created_at: string; // ISO日期时间字符串
  updated_at?: string; // ISO日期时间字符串
  created_by?: number;
  updated_by?: number;
}

export interface ExchangeRateCreate {
  currency_code: string;
  rate: number;
  effective_month: string;
  status: string;
}

export interface ExchangeRateUpdate {
  rate?: number;
  status?: string;
  change_reason: string;
}

export interface ExchangeRateQuery {
  currency_code?: string;
  effective_month?: string;
  status?: string;
  page: number;
  size: number;
}

export interface ExchangeRateHistoryQuery {
  currency_code: string;
  start_date?: string;
  end_date?: string;
  days?: number;
}

export interface ExchangeRateSummary {
  currency_code: string;
  current_rate?: number;
  last_updated?: string;
  status: string;
  is_valid: boolean;
}

export interface CurrencyInfo {
  code: string;
  name: string;
  symbol: string;
  is_base: boolean;
}

export interface CurrencyConversionResult {
  original_amount: number;
  original_currency: string;
  usd_amount: number;
  usd_formatted: string;
  conversion_date: string;
}

export interface ExchangeRateValidationResult {
  currency_code: string;
  is_valid: boolean;
  current_rate?: number;
  effective_month?: string;
}

export interface ExchangeRateResponse {
  data: ExchangeRate[];
  total: number;
  page: number;
  size: number;
}

class ExchangeRateService {
  private baseUrl = '/exchange-rates';

  /**
   * 创建汇率记录
   */
  async createExchangeRate(data: ExchangeRateCreate): Promise<ExchangeRate> {
    return apiClient.post(this.baseUrl, data);
  }

  /**
   * 更新汇率记录
   */
  async updateExchangeRate(id: number, data: ExchangeRateUpdate): Promise<ExchangeRate> {
    return apiClient.put(`${this.baseUrl}/${id}`, data);
  }

  /**
   * 获取汇率记录详情
   */
  async getExchangeRate(id: number): Promise<ExchangeRate> {
    return apiClient.get(`${this.baseUrl}/${id}`);
  }

  /**
   * 获取汇率记录详情（包含修改日志）
   */
  async getExchangeRateWithLogs(id: number): Promise<ExchangeRate & { logs: any[] }> {
    return apiClient.get(`${this.baseUrl}/${id}/with-logs`);
  }

  /**
   * 查询汇率记录列表
   */
  async getExchangeRates(params: ExchangeRateQuery): Promise<any> {
    return apiClient.get(this.baseUrl, { params });
  }

  /**
   * 获取汇率历史记录
   */
  async getExchangeRateHistory(params: ExchangeRateHistoryQuery): Promise<ExchangeRate[]> {
    return apiClient.get(`${this.baseUrl}/history`, { params });
  }

  /**
   * 获取汇率汇总信息
   */
  async getExchangeRateSummary(): Promise<ExchangeRateSummary[]> {
    return apiClient.get(`${this.baseUrl}/summary`);
  }

  /**
   * 获取支持的货币列表
   */
  async getSupportedCurrencies(): Promise<CurrencyInfo[]> {
    return apiClient.get(`${this.baseUrl}/currencies`);
  }

  /**
   * 货币转换（转换为美元）
   */
  async convertCurrency(
    currencyCode: string,
    amount: number,
    targetDate?: string
  ): Promise<CurrencyConversionResult> {
    const params: any = { amount };
    if (targetDate) {
      params.target_date = targetDate;
    }
    return apiClient.get(`${this.baseUrl}/convert/${currencyCode}`, { params });
  }

  /**
   * 验证汇率有效性
   */
  async validateExchangeRate(
    currencyCode: string,
    targetDate?: string
  ): Promise<ExchangeRateValidationResult> {
    const params: any = {};
    if (targetDate) {
      params.target_date = targetDate;
    }
    return apiClient.get(`${this.baseUrl}/validate/${currencyCode}`, { params });
  }

  /**
   * 删除汇率记录
   */
  async deleteExchangeRate(id: number): Promise<void> {
    return apiClient.delete(`${this.baseUrl}/${id}`);
  }

  /**
   * 批量删除汇率记录
   */
  async batchDeleteExchangeRates(ids: number[]): Promise<void> {
    return apiClient.delete(`${this.baseUrl}/batch`, { data: { ids } });
  }

  /**
   * 导入汇率数据
   */
  async importExchangeRates(file: File): Promise<{ success: number; failed: number; errors: string[] }> {
    const formData = new FormData();
    formData.append('file', file);
    return apiClient.post(`${this.baseUrl}/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * 导出汇率数据
   */
  async exportExchangeRates(params?: Partial<ExchangeRateQuery>): Promise<Blob> {
    return apiClient.get(`${this.baseUrl}/export`, {
      params,
      responseType: 'blob',
    });
  }

  /**
   * 获取汇率趋势图表数据
   */
  async getExchangeRateTrend(
    currencyCode: string,
    days: number = 180
  ): Promise<{ date: string; rate: number }[]> {
    return apiClient.get(`${this.baseUrl}/trend/${currencyCode}`, {
      params: { days },
    });
  }

  /**
   * 同步外部汇率数据
   */
  async syncExternalRates(): Promise<{ synced: number; updated: number }> {
    return apiClient.post(`${this.baseUrl}/sync`);
  }

  /**
   * 获取汇率统计信息
   */
  async getExchangeRateStats(): Promise<{
    total_currencies: number;
    active_rates: number;
    last_updated: string;
    monthly_changes: { month: string; changes: number }[];
  }> {
    return apiClient.get(`${this.baseUrl}/stats`);
  }
}

export const exchangeRateService = new ExchangeRateService();
