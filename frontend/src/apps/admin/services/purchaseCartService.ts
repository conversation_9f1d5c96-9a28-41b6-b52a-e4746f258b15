import { apiClient } from './authService';

export interface CartItem {
  id: number;
  item_id: number;
  item_code: string;
  item_name: string;
  item_image_url?: string; // 物品图片URL
  spq_quantity: number | string; // SPQ数量（标准包装数量）
  spq_count: number; // SPQ个数（需要多少个标准包装）
  spq_unit: string; // SPQ单位
  notes?: string;
  estimated_unit_price?: number;
  total_price?: number;
  created_at: string;
  
  // 库存信息
  current_quantity?: number;
  max_quantity?: number;
  cart_quantity?: number;
  total_after_purchase?: number;
  is_overstock?: boolean;
  overstock_message?: string;
  

}

export interface AddCartItemRequest {
  item_id: number;
  spq_count: number; // SPQ个数（需要多少个标准包装）
  notes?: string;
}

export interface UpdateCartItemRequest {
  spq_count?: number; // SPQ个数（需要多少个标准包装）
  notes?: string;
}

export interface CartSummary {
  department_id: number;
  total_items: number;
  total_quantity: number; // 总数量（SPQ数量 × SPQ个数的总和）
  items: CartItem[];
}

export interface InventoryCheckResult {
  is_overstock: boolean;
  current_quantity: number;
  max_quantity?: number;
  cart_quantity: number;
  total_after_purchase: number;
  alert_message: string;
  item_name?: string;
  item_code?: string;
}

export interface CartInventoryCheckResponse {
  department_id: number;
  total_items: number;
  overstock_items: InventoryCheckResult[];
  overstock_count: number;
  alert_file_path?: string;
  alert_generated: boolean;
  check_timestamp: string;
}

class PurchaseCartService {
  // 获取部门购物车中的所有物品
  async getDepartmentCartItems(departmentId: number): Promise<CartItem[]> {
    const response = await apiClient.get(`/purchase/cart/department/${departmentId}/items`);
    return response.data;
  }

  // 添加物品到部门购物车
  async addItemToCart(departmentId: number, itemData: AddCartItemRequest): Promise<CartItem> {
    const response = await apiClient.post(`/purchase/cart/department/${departmentId}/items`, itemData);
    return response.data;
  }

  // 更新购物车项目
  async updateCartItem(cartItemId: number, updates: UpdateCartItemRequest): Promise<CartItem> {
    const response = await apiClient.put(`/purchase/cart/items/${cartItemId}`, updates);
    return response.data;
  }

  // 从购物车移除物品
  async removeCartItem(cartItemId: number): Promise<void> {
    await apiClient.delete(`/purchase/cart/items/${cartItemId}`);
  }

  // 清空部门购物车
  async clearDepartmentCart(departmentId: number): Promise<void> {
    await apiClient.delete(`/purchase/cart/department/${departmentId}/clear`);
  }

  // 获取部门购物车摘要信息
  async getCartSummary(departmentId: number): Promise<CartSummary> {
    const response = await apiClient.get(`/purchase/cart/department/${departmentId}/summary`);
    return response.data;
  }

  // 根据ID获取购物车项目
  async getCartItemById(cartItemId: number): Promise<CartItem> {
    const response = await apiClient.get(`/purchase/cart/items/${cartItemId}`);
    return response.data;
  }

  // 检查购物车库存
  async checkCartInventory(departmentId: number): Promise<CartInventoryCheckResponse> {
    const response = await apiClient.get(`/purchase/cart/department/${departmentId}/inventory-check`);
    return response.data;
  }

  // 获取库存预警文件列表
  async getInventoryAlertFiles(limit: number = 10): Promise<any> {
    const response = await apiClient.get(`/purchase/cart/inventory-alerts/files?limit=${limit}`);
    return response.data;
  }

  // 删除库存预警文件
  async deleteInventoryAlertFile(filename: string): Promise<void> {
    await apiClient.delete(`/purchase/cart/inventory-alerts/files/${filename}`);
  }
}

export const purchaseCartService = new PurchaseCartService();
export default purchaseCartService;
