import { apiClient } from './authService';

export interface User {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  display_name?: string;
  phone?: string;
  employee_id?: string;
  position?: string;
  avatar?: string;
  department_id?: number;
  is_active: boolean;
  is_superuser: boolean;
  account_status: 'active' | 'disabled' | 'locked' | 'pending';
  password_status: 'normal' | 'need_reset' | 'temporary';
  last_login_at?: string;
  created_at: string;
  role_id?: number;
  department_name?: string;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  full_name?: string;
  phone?: string;
  employee_id?: string;
  position?: string;
  department_id?: number;
  role_id?: number;
}

export interface UpdateUserRequest {
  email?: string;
  full_name?: string;
  phone?: string;
  employee_id?: string;
  position?: string;
  department_id?: number;
  role_id?: number;
}

export interface UserListQuery {
  page?: number;
  page_size?: number;
  department_id?: number;
  role?: string; // 保持向后兼容
  roles?: string[]; // 新增支持roles数组参数
  account_status?: string;
  search?: string;
}

export interface UserListResponse {
  items: User[];
  total: number;
  page: number;
  page_size: number;
  pages: number;
}

export interface PasswordResetRequest {
  new_password: string;
  temporary?: boolean;
}

export interface BatchImportRequest {
  file: File;
}

class UserService {
  /**
   * 获取用户列表
   */
  async getUsers(query?: UserListQuery): Promise<UserListResponse> {
    const response = await apiClient.get<UserListResponse>('/users', {
      params: query,
    });
    return response.data;
  }

  /**
   * 根据ID获取用户详情
   */
  async getUserById(id: number): Promise<User> {
    const response = await apiClient.get<User>(`/users/${id}`);
    return response.data;
  }

  /**
   * 创建新用户
   */
  async createUser(data: CreateUserRequest): Promise<User> {
    const response = await apiClient.post<User>('/users', data);
    return response.data;
  }

  /**
   * 更新用户信息
   */
  async updateUser(id: number, data: UpdateUserRequest): Promise<User> {
    const response = await apiClient.put<User>(`/users/${id}`, data);
    return response.data;
  }

  /**
   * 删除功能已移除，仅保留禁用功能
   * 用户管理通过 toggleUserStatus 方法进行状态切换
   */

  /**
   * 重置用户密码
   */
  async resetPassword(id: number, data: PasswordResetRequest): Promise<void> {
    await apiClient.post(`/users/${id}/reset-password`, data);
  }

  /**
   * 切换用户状态（激活/禁用）
   */
  async toggleUserStatus(id: number): Promise<User> {
    const response = await apiClient.post<User>(`/users/${id}/toggle-status`);
    return response.data;
  }

  /**
   * 批量导入用户
   */
  async batchImport(file: File): Promise<{ success_count: number; error_count: number; errors: string[] }> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await apiClient.post('/users/batch-import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * 获取当前用户资料
   */
  async getProfile(): Promise<User> {
    const response = await apiClient.get<User>('/users/me/profile');
    return response.data;
  }

  /**
   * 更新当前用户资料
   */
  async updateProfile(data: UpdateUserRequest): Promise<User> {
    const response = await apiClient.put<User>('/users/me/profile', data);
    return response.data;
  }

  /**
   * 修改当前用户密码
   */
  async changePassword(old_password: string, new_password: string): Promise<void> {
    await apiClient.post('/users/me/change-password', {
      old_password,
      new_password,
    });
  }
}

export const userService = new UserService(); 