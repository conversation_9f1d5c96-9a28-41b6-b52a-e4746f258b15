import { apiClient } from './authService';

export interface ExecutionBatch {
  id: number;
  batch_no: string;
  batch_name: string;
  executor_id: number;
  executor_name: string;
  executed_at: string;
  request_count: number;
  total_amount: number;
  status: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  execution_items?: ExecutionItem[];
}

export interface ExecutionItem {
  id: number;
  batch_id: number;
  request_id: number;
  request_item_id: number;
  item_id: number;
  item_code: string;
  item_name: string;
  spq_quantity: number;
  spq_count: number;
  spq_unit: string;
  unit_price: number;
  total_price: number;
  supplier_id: number;
  supplier_name: string;
  price_locked_at?: string;
  created_at: string;
}

export interface ApprovedRequest {
  id: number;
  request_no: string;
  department_id: number;
  submitter_id: number;
  status: string;
  final_total: number;
  submitted_at?: string;
  created_at: string;
  items: ApprovedRequestItem[];
}

export interface ApprovedRequestItem {
  id: number;
  item_id: number;
  item_code: string;
  item_name: string;
  spq_quantity: number;
  spq_count: number;
  spq_unit: string;
  final_unit_price?: number;
  final_total_price?: number;
  final_supplier_id?: number;
}

export interface BatchExecutionPreview {
  request_count: number;
  total_amount: number;
  estimated_batch_no: string;
  requests_preview: any[];
  supplier_summary: any[];
  warnings: string[];
}

export interface ExecutionSummary {
  supplier_summary: any[];
  category_summary: any[];
  time_summary: any;
  total_requests: number;
  total_amount: number;
  unique_suppliers: number;
  unique_items: number;
}

export interface ExecutionStatistics {
  period_days: number;
  total_batches: number;
  total_requests: number;
  total_amount: number;
  average_amount_per_batch: number;
  average_requests_per_batch: number;
  executor_statistics: any[];
}

export interface ExecutionFilters {
  department_id?: number;
  submitter_id?: number;
  start_date?: string;
  end_date?: string;
  executor_id?: number;
  status?: string;
  batch_no?: string;
}

export interface BatchExecutionRequest {
  request_ids: number[];
  batch_name?: string;
  notes?: string;
  confirm: boolean;
}

class PurchaseExecutionService {
  private baseURL = '/purchase/execution';

  // 获取已批准的采购申请列表
  async getApprovedRequests(filters: ExecutionFilters = {}, page = 1, size = 20) {
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
      ...Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== undefined)
      )
    });

    const response = await apiClient.get(`${this.baseURL}/approved-requests?${params}`);
    return response.data;
  }

  // 预览批量执行
  async previewBatchExecution(requestIds: number[], batchName?: string, notes?: string) {
    const response = await apiClient.post(`${this.baseURL}/preview`, {
      request_ids: requestIds,
      batch_name: batchName,
      notes: notes,
      confirm: false
    });
    return response.data as BatchExecutionPreview;
  }

  // 执行批量采购申请
  async executeBatch(requestIds: number[], batchName?: string, notes?: string) {
    const response = await apiClient.post(`${this.baseURL}/execute`, {
      request_ids: requestIds,
      batch_name: batchName,
      notes: notes,
      confirm: true
    });
    return response.data as ExecutionBatch;
  }

  // 执行单个采购申请
  async executeSingleRequest(requestId: number) {
    const response = await apiClient.post(`${this.baseURL}/execute`, {
      request_ids: [requestId],
      batch_name: `单申请执行_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}`,
      notes: '单个申请执行',
      confirm: true
    });
    return response.data as ExecutionBatch;
  }

  // 获取执行批次列表
  async getExecutionBatches(filters: ExecutionFilters = {}, page = 1, size = 20) {
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
      ...Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== undefined)
      )
    });

    const response = await apiClient.get(`${this.baseURL}/batches?${params}`);
    return response.data;
  }

  // 获取执行批次详情
  async getExecutionBatchDetail(batchId: number) {
    const response = await apiClient.get(`${this.baseURL}/batches/${batchId}`);
    return response.data as ExecutionBatch;
  }

  // 获取执行汇总数据
  async getExecutionSummary(batchIds?: number[]) {
    const params = batchIds ? `?batch_ids=${batchIds.join(',')}` : '';
    const response = await apiClient.get(`${this.baseURL}/summary${params}`);
    return response.data as ExecutionSummary;
  }

  // 获取执行统计数据
  async getExecutionStatistics(days = 30) {
    const response = await apiClient.get(`${this.baseURL}/statistics?days=${days}`);
    return response.data as ExecutionStatistics;
  }

  // 导出执行数据
  async exportExecutionData(batchIds: number[], format = 'xlsx', includeDetails = true) {
    const response = await apiClient.post(`${this.baseURL}/export`, {
      batch_ids: batchIds,
      export_format: format,
      include_details: includeDetails
    });
    return response.data;
  }
}

export const purchaseExecutionService = new PurchaseExecutionService();
