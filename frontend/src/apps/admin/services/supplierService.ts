import { apiClient } from './authService';

// 供应商相关接口
export interface Supplier {
  id: number;
  name_cn?: string;
  name_en?: string;
  code: string;
  company_address?: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  rating: number;
  status: string;
  created_at: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
}

export interface SupplierCreate {
  name_cn?: string;
  name_en?: string;
  company_address?: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  rating?: number;
  status?: string;
}

export interface SupplierUpdate {
  name_cn?: string;
  name_en?: string;
  company_address?: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  rating?: number;
  status?: string;
}

// 获取供应商显示名称的工具函数
export function getSupplierDisplayName(supplier: Supplier): string {
  // 优先显示英文名，如果没有则显示中文名
  return supplier.name_en || supplier.name_cn || '未命名供应商';
}

export interface SupplierListResponse {
  items: Supplier[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 物品供应商关联接口
export interface ItemSupplier {
  id: number;
  item_id: number;
  supplier_id: number;
  priority: number | null;
  status: string;
  delivery_days: number;
  quality_rating: number;
  spq: number;
  moq: number;
  created_at: string;
  updated_at?: string;
  supplier?: Supplier;
  item?: {
    id: number;
    name: string;
    code: string;
    description?: string;
    category?: {
      id: number;
      name: string;
    };
    image_url?: string;
    spec_material?: string;
    size_dimension?: string;
    purchase_unit?: string;
    inventory_unit?: string;
    qty_per_up?: number;
    brand?: string;
  };
}

export interface ItemSupplierCreate {
  item_id: number;
  priority?: number;
  status?: string;
  delivery_days?: number;
  quality_rating?: number;
  spq?: number;
  moq?: number;
}

export interface ItemSupplierUpdate {
  priority?: number;
  status?: string;
  delivery_days?: number;
  quality_rating?: number;
  spq?: number;
  moq?: number;
}

// 供应商价格接口
export interface SupplierPrice {
  id: number;
  item_supplier_id: number;
  unit_price: string;
  currency_code: string;
  min_quantity: number;
  max_quantity?: number;
  valid_from: string;
  valid_to?: string;
  status: string;
  remarks?: string;
  created_at: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
}

export interface SupplierPriceCreate {
  item_supplier_id: number;
  unit_price: string;
  currency_code: string;
  min_quantity: number;
  max_quantity?: number;
  valid_from: string;
  valid_to?: string;
  status?: string;
  remarks?: string;
}

export interface SupplierPriceUpdate {
  unit_price?: string;
  currency_code?: string;
  min_quantity?: number;
  max_quantity?: number;
  valid_from?: string;
  valid_to?: string;
  status?: string;
  remarks?: string;
}

export interface SupplierPriceListResponse {
  items: SupplierPrice[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface PriceCalculationResult {
  supplier_id: number;
  item_id: number;
  quantity: number;
  unit_price: number;
  total_amount: number;
  valid_from: string;
  valid_to?: string;
  remarks?: string;
}

// 供应商基础管理API
export const supplierService = {
  // 获取供应商列表
  getSuppliers: async (params: {
    page?: number;
    size?: number;
    search?: string;
    status?: string;
    rating_min?: number;
    sort_by?: string;
    sort_order?: string;
  }): Promise<SupplierListResponse> => {
    const response = await apiClient.get('/suppliers', { params });
    return response.data;
  },

  // 获取单个供应商
  getSupplier: async (id: number): Promise<Supplier> => {
    const response = await apiClient.get(`/suppliers/${id}`);
    return response.data;
  },

  // 创建供应商
  createSupplier: async (data: SupplierCreate): Promise<Supplier> => {
    const response = await apiClient.post('/suppliers', data);
    return response.data;
  },

  // 更新供应商
  updateSupplier: async (id: number, data: SupplierUpdate): Promise<Supplier> => {
    const response = await apiClient.put(`/suppliers/${id}`, data);
    return response.data;
  },

  // 删除供应商
  deleteSupplier: async (id: number, forceDelete: boolean = false): Promise<any> => {
    const response = await apiClient.delete(`/suppliers/${id}`, {
      params: { force_delete: forceDelete }
    });
    return response.data;
  },

  // 获取供应商的物品列表
  getSupplierItems: async (supplierId: number): Promise<ItemSupplier[]> => {
    const response = await apiClient.get(`/suppliers/${supplierId}/items`);
    return response.data;
  },

  // 获取供应商的物品列表（包含USD价格计算）
  getSupplierItemsWithUSDPrices: async (supplierId: number): Promise<any[]> => {
    const response = await apiClient.get(`/suppliers/${supplierId}/items-with-usd-prices`);
    return response.data;
  },

  // 为供应商添加物品
  addSupplierItem: async (supplierId: number, data: ItemSupplierCreate): Promise<Supplier> => {
    const response = await apiClient.post(`/suppliers/${supplierId}/items`, data);
    return response.data;
  },

  // 更新物品供应商关联
  updateItemSupplier: async (itemSupplierId: number, data: ItemSupplierUpdate): Promise<ItemSupplier> => {
    const response = await apiClient.put(`/item-suppliers/${itemSupplierId}`, data);
    return response.data;
  },

  // 删除物品供应商关联
  deleteItemSupplier: async (itemSupplierId: number): Promise<void> => {
    await apiClient.delete(`/item-suppliers/${itemSupplierId}`);
  },

  // 获取物品的供应商列表
  getItemSuppliers: async (itemId: number): Promise<ItemSupplier[]> => {
            const response = await apiClient.get(`/items/${itemId}/suppliers`);
    return response.data;
  },

  // 获取物品列表用于选择
  getItemsForSelection: async (params?: {
    search?: string;
    page?: number;
    size?: number;
  }): Promise<Array<{
    id: number;
    name: string;
    code: string;
    description?: string;
    category_name?: string;
    image_url?: string;
    spec_material?: string;
    size_dimension?: string;
    unit?: string;
    brand?: string;
  }>> => {
    const response = await apiClient.get('/items', { params });
    // 后端返回的是分页格式，需要提取 items 数组并转换格式
    const items = response.data.items || [];
    return items.map((item: any) => ({
      id: item.id,
      name: item.name,
      code: item.code,
      description: item.description,
      category_name: item.category?.name,
      image_url: item.image_url,
      spec_material: item.spec_material,
      size_dimension: item.size_dimension,
      unit: item.unit,
      brand: item.brand
    }));
  },
};

// 供应商价格管理API
export const supplierPriceService = {
  // 获取价格列表
  getPrices: async (params: {
    page?: number;
    size?: number;
    supplier_id?: number;
    item_id?: number;
    item_supplier_id?: number;
    status?: string | null;
  }): Promise<SupplierPriceListResponse> => {
            const response = await apiClient.get('/suppliers/prices', { params });
    return response.data;
  },

  // 批量获取多个物品供应商关联的价格信息
  getPricesBatch: async (itemSupplierIds: number[], status?: string | null): Promise<Record<number, SupplierPrice[]>> => {
    if (itemSupplierIds.length === 0) {
      return {};
    }
    
    try {
      const params = status ? { status } : {};
              const response = await apiClient.post('/suppliers/prices/batch', itemSupplierIds, { params });
      return response.data;
    } catch (error) {
      console.error('批量获取价格失败:', error);
      // 如果批量查询失败，返回空对象
      return {};
    }
  },

  // 创建价格
  createPrice: async (data: SupplierPriceCreate): Promise<SupplierPrice> => {
            const response = await apiClient.post('/suppliers/prices', data);
    return response.data;
  },

  // 更新价格
  updatePrice: async (priceId: number, data: SupplierPriceUpdate): Promise<SupplierPrice> => {
            const response = await apiClient.put(`/suppliers/prices/${priceId}`, data);
    return response.data;
  },

  // 删除价格
  deletePrice: async (priceId: number): Promise<void> => {
            await apiClient.delete(`/suppliers/prices/${priceId}`);
  },

  // 计算价格
  calculatePrice: async (
    supplierId: number, 
    itemId: number, 
    quantity: number
  ): Promise<PriceCalculationResult> => {
    const response = await apiClient.get(`/suppliers/${supplierId}/items/${itemId}/price`, {
      params: { quantity }
    });
    return response.data;
  },
};

// 价格走势相关接口
export interface PricePoint {
  date: string;              // 日期
  unit_price: number;        // 单价
  total_price: number;       // 总价
  is_valid: boolean;         // 是否有效
  remarks?: string;          // 价格备注
  currency_code: string;     // 货币代码
  // USD价格字段
  usd_unit_price?: number;   // USD单价
  usd_total_price?: number;  // USD总价
  exchange_rate?: number;    // 汇率
}

export interface PriceTrendData {
  tier_id: number;           // 阶梯ID（基于min_quantity）
  min_quantity: number;      // 最小数量
  max_quantity?: number;     // 最大数量
  tier_name: string;         // 阶梯名称（如"1-100", "101-500"等）
  price_points: PricePoint[];
}

export interface PriceTrendResponse {
  item_supplier_id: number;
  item_name: string;
  supplier_name: string;
  price_trends: PriceTrendData[];
  date_range: {
    start_date: string;
    end_date: string;
  };
}

// 价格走势服务
export const priceTrendService = {
  // 获取价格走势数据
  getPriceTrend: async (
    itemSupplierId: number,
    startDate?: string,
    endDate?: string
  ): Promise<PriceTrendResponse> => {
    const params: any = {};
    if (startDate) params.start_date = startDate;
    if (endDate) params.end_date = endDate;
    
            const response = await apiClient.get(`/suppliers/item-suppliers/${itemSupplierId}/price-trend`, { params });
    return response.data;
  },
}; 