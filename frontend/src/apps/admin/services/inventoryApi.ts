import { apiClient } from './authService';

export interface InventoryOverview {
  total_items: number;
  total_departments: number;
  low_stock_items: number;
  out_of_stock_items: number;
  overstock_items: number;
  normal_stock_items: number;
  total_value: number;
  total_value_usd: number;
  active_alerts: number;
}

export interface InventoryItem {
  id: number;
  department_id: number;
  department_name: string;
  item_id: number;
  item_name: string;
  item_code: string;
  current_quantity: number;
  min_quantity: number;
  max_quantity?: number;
  status: string;
  storage_location?: string;
  rack_number?: string;
  purchase_unit: string;
  inventory_unit: string;
  qty_per_up: number;
  purchase_unit_quantity?: number;
  last_purchase_price?: number;
  average_cost?: number;
  total_value?: number;
  calculated_usd_price?: number;
  original_currency_price?: number;
  currency_code?: string;
  exchange_rate?: number;
  last_updated: string;
  created_at: string;
  updated_at: string;
  item_image_url?: string;
  has_inventory?: boolean;
  is_active?: boolean;
}

export interface InventoryChangeRecord {
  id: number;
  department_id: number;
  department_name: string;
  item_id: number;
  item_name: string;
  item_code: string;
  before_quantity: number;
  after_quantity: number;
  change_quantity: number;
  change_type: string;
  change_reason?: string;
  operator_id: number;
  operator_name: string;
  change_date: string;
  remarks?: string;
  created_at: string;
}

export interface InventoryAlert {
  id: number;
  department_id: number;
  department_name: string;
  item_id: number;
  item_name: string;
  item_code: string;
  alert_type: string;
  alert_level: string;
  message: string;
  current_stock: number;
  threshold_value: number;
  is_active: boolean;
  is_resolved: boolean;
  resolved_by?: number;
  resolved_at?: string;
  resolver_name?: string;
  created_at: string;
  updated_at: string;
}

export interface InventoryUsageStatistics {
  id: number;
  department_id: number;
  department_name: string;
  item_id: number;
  item_name: string;
  item_code: string;
  unit: string;
  year: number;
  month: number;
  total_usage: number;
  total_value: number;
  avg_unit_price: number;
  created_at: string;
  updated_at: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  skip: number;
  limit: number;
}

export interface InventoryFilters {
  item_id?: number;
  status?: string;
  low_stock_only?: boolean;
  out_of_stock_only?: boolean;
  search?: string;
  skip?: number;
  limit?: number;
  include_all_items?: boolean;
}

export interface ChangeRecordFilters {
  item_id?: number;
  change_type?: string;
  operator_id?: number;
  start_date?: string;
  end_date?: string;
  skip?: number;
  limit?: number;
}

export interface AlertFilters {
  item_id?: number;
  alert_type?: string;
  alert_level?: string;
  is_resolved?: boolean;
  is_active?: boolean;
  skip?: number;
  limit?: number;
}

export interface UsageStatisticsFilters {
  item_id?: number;
  year?: number;
  month?: number;
  start_date?: string;
  end_date?: string;
  skip?: number;
  limit?: number;
}

export interface UsageStatisticsItem {
  item_id: number;
  department_id: number;
  item_name: string;
  item_code: string;
  department_name: string;
  total_usage: number;
  usage_count: number;
  avg_unit_price: number;
  first_usage: string;
  last_usage: string;
  unit: string;
}

export interface TopUsedItem {
  item_id: number;
  item_name: string;
  item_code: string;
  total_usage: number;
  usage_count: number;
  unit: string;
}

export const inventoryApi = {
  // 获取库存概览
  getOverview: async (): Promise<InventoryOverview> => {
    const response = await apiClient.get('/inventory/overview');
    return response.data;
  },

  // 获取部门库存列表
  getDepartmentInventories: async (params: InventoryFilters): Promise<PaginatedResponse<InventoryItem>> => {
    const response = await apiClient.get('/inventory/department-inventories', { params });
    return response.data;
  },

  // 获取库存变更记录
  getInventoryChangeRecords: async (params: ChangeRecordFilters): Promise<InventoryChangeRecord[]> => {
    const response = await apiClient.get('/inventory/inventory-change-records', { params });
    return response.data;
  },

  // 获取库存预警列表
  getInventoryAlerts: async (params: AlertFilters): Promise<InventoryAlert[]> => {
    const response = await apiClient.get('/inventory/alerts', { params });
    return response.data;
  },

  // 获取使用统计
  getUsageStatistics: async (params: UsageStatisticsFilters): Promise<UsageStatisticsItem[]> => {
    const response = await apiClient.get('/inventory/usage-statistics', { params });
    return response.data;
  },

  // 获取使用统计摘要
  getUsageStatisticsSummary: async (params?: { year?: number; month?: number }): Promise<any> => {
    const response = await apiClient.get('/inventory/usage-statistics-summary', { params });
    return response.data;
  },

  // 获取最常用物品排行
  getTopUsedItems: async (params?: { limit?: number; days?: number }): Promise<TopUsedItem[]> => {
    const response = await apiClient.get('/inventory/top-used-items', { params });
    return response.data;
  },

  // 手工入库
  processManualIn: async (data: {
    item_id: number;
    quantity: number;
    quantity_unit: string;
    department_id: number;
    remarks?: string;
  }): Promise<{
    success: boolean;
    message: string;
    change_record_id?: number;
    new_quantity?: number;
  }> => {
    const response = await apiClient.post('/inventory/manual-in', data);
    return response.data;
  },

  // 库存调整
  adjustStock: async (data: {
    item_id: number;
    adjust_quantity: number;
    quantity_unit: string;
    department_id: number;
    adjust_reason: string;
    remarks?: string;
  }): Promise<{
    success: boolean;
    message: string;
    change_record_id?: number;
    new_quantity?: number;
  }> => {
    const response = await apiClient.post('/inventory/adjust-stock', data);
    return response.data;
  },

  // 解决预警
  resolveAlert: async (alertId: number): Promise<InventoryAlert> => {
    const response = await apiClient.post(`/inventory/alerts/${alertId}/resolve`);
    return response.data;
  },

  // 更新库存设置
  updateInventorySettings: async (data: {
    id: number;
    item_id: number;
    min_quantity?: number;
    max_quantity?: number;
    storage_location?: string;
    rack_number?: string;
    notes?: string;
    is_active?: boolean;
  }): Promise<{
    success: boolean;
    message: string;
  }> => {
    const response = await apiClient.put(`/inventory/department-inventories/${data.id}`, data);
    return response.data;
  },

  // 启动库存
  startInventory: async (data: {
    item_id: number;
    min_quantity?: number;
    max_quantity?: number;
    storage_location?: string;
    rack_number?: string;
    notes?: string;
  }): Promise<{
    success: boolean;
    message: string;
    inventory_id?: number;
  }> => {
    const response = await apiClient.post('/inventory/department-inventories/start', data);
    return response.data;
  },

  // 获取物品列表（用于下拉选择）
  getItems: async (params?: { search?: string; page?: number; size?: number }): Promise<any[]> => {
            const response = await apiClient.get('/items', { params: { page: 1, size: 20, ...(params || {}) } });
    return response.data?.items ?? [];
  },

  // 获取部门列表（用于下拉选择）
  getDepartments: async (params?: { search?: string }): Promise<any[]> => {
    const response = await apiClient.get('/departments', { params });
    return response.data;
  },
};
