import axios from 'axios';

const API_BASE_URL = '/api/admin';

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10秒超时
});

// 请求拦截器：自动添加token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器：处理token过期
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    // 如果是网络错误或超时，尝试重试
    if ((error.code === 'ECONNABORTED' || error.message?.includes('timeout')) && 
        error.config && !error.config._retry) {
      error.config._retry = true;
      
      // 延迟重试
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      try {
        return await apiClient.request(error.config);
      } catch (retryError) {
        return Promise.reject(retryError);
      }
    }
    
    if (error.response?.status === 401) {
      // Token过期，清除本地存储并重定向到登录页
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
      window.location.href = '/admin/login';
    }
    return Promise.reject(error);
  }
);

export interface LoginRequest {
  username: string;
  password: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  department?: string;
  department_id?: number;
  role: string;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  // 权限相关字段
  role_id?: number;
  role_name?: string;
  role_code?: string;
  permissions: string[];
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  user: User;
}

class AuthService {
  /**
   * 用户登录
   */
  async login(username: string, password: string): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>('/auth/login', {
      username,
      password,
    });
    return response.data;
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    await apiClient.post('/auth/logout');
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get<User>('/auth/me');
    return response.data;
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated(): boolean {
    const token = localStorage.getItem('access_token');
    return !!token;
  }

  /**
   * 获取存储的token
   */
  getToken(): string | null {
    return localStorage.getItem('access_token');
  }

  /**
   * 获取存储的用户信息
   */
  getStoredUser(): User | null {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch (error) {
        console.error('Failed to parse stored user:', error);
        return null;
      }
    }
    return null;
  }
}

export const authService = new AuthService();
export { apiClient };
