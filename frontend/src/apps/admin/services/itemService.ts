import { apiClient } from './authService';

export interface ItemPreferredPrice {
  price_range: {
    min_price: number;
    max_price: number;
  } | null;
  min_price?: number;
  max_price?: number;
  supplier: {
    id: number;
    name_cn?: string;
    name_en?: string;
    code: string;
  } | null;
  original_prices?: {
    unit_price: number;
    currency_code: string;
    min_quantity: number;
    max_quantity: number | null;
  }[];
}

export interface ItemTierPrice {
  min_quantity: number;
  max_quantity: number | null;
  unit_price: number;
  spq_quantity: number;
  spq_unit: string;
}

export interface ItemTierPriceResponse {
  tier_prices: ItemTierPrice[];
  supplier: {
    id: number;
    name: string;
  } | null;
  spq_info: {
    spq_quantity: number;
    spq_unit: string;
  } | null;
}

export const itemService = {
  // 获取物品的首选供应商价格
  getPreferredPrice: async (itemId: number): Promise<ItemPreferredPrice | null> => {
    try {
      const response = await apiClient.get(`/items/${itemId}/preferred-price`);
      return response.data;
    } catch (error) {
      console.error('获取物品价格失败:', error);
      return null;
    }
  },

  // 批量获取多个物品的首选供应商价格
  getPreferredPrices: async (itemIds: number[]): Promise<Record<number, ItemPreferredPrice>> => {
    if (itemIds.length === 0) {
      return {};
    }
    
    try {
      const response = await apiClient.post('/items/preferred-prices/batch', itemIds);
      return response.data;
    } catch (error) {
      console.error('批量获取物品价格失败:', error);
      // 如果批量查询失败，返回空对象
      return {};
    }
  },

  // 获取物品的阶梯价格信息
  getTierPrices: async (itemId: number): Promise<ItemTierPriceResponse | null> => {
    try {
      const response = await apiClient.get(`/items/${itemId}/tier-prices`);
      return response.data;
    } catch (error) {
      console.error('获取物品阶梯价格失败:', error);
      return null;
    }
  }
}; 