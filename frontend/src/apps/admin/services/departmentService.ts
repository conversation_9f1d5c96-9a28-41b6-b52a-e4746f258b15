import { apiClient } from './authService';

export interface Department {
  id: number;
  name: string;
  code: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface DepartmentCreate {
  name: string;
  code: string;
  description?: string;
  is_active?: boolean;
}

export interface DepartmentUpdate {
  name?: string;
  code?: string;
  description?: string;
  is_active?: boolean;
}

export interface DepartmentQuery {
  search?: string;
  is_active?: boolean;
}

/**
 * 部门管理服务
 */
export const departmentService = {
  /**
   * 获取部门列表
   */
  async getDepartments(query?: DepartmentQuery): Promise<Department[]> {
    const response = await apiClient.get<Department[]>('/departments', {
      params: query,
    });
    return response.data;
  },

  /**
   * 获取部门详情
   */
  async getDepartment(id: number): Promise<Department> {
    const response = await apiClient.get<Department>(`/departments/${id}/`);
    return response.data;
  },

  /**
   * 获取部门详情
   */
  async createDepartment(data: DepartmentCreate): Promise<Department> {
    const response = await apiClient.post<Department>('/departments', data);
    return response.data;
  },

  /**
   * 更新部门
   */
  async updateDepartment(id: number, data: DepartmentUpdate): Promise<Department> {
    const response = await apiClient.put<Department>(`/departments/${id}/`, data);
    return response.data;
  },

  /**
   * 删除部门
   */
  async deleteDepartment(id: number): Promise<void> {
    await apiClient.delete(`/departments/${id}/`);
  },

  /**
   * 切换部门状态
   */
  async toggleDepartmentStatus(id: number): Promise<Department> {
    const department = await this.getDepartment(id);
    const response = await apiClient.put<Department>(`/suppliers/departments/${id}`, {
      is_active: !department.is_active,
    });
    return response.data;
  },
}; 