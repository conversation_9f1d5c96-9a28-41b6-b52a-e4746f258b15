import React, { useState, useCallback, useRef } from 'react';
import { Image, Button, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { UploadOutlined } from '@ant-design/icons';
import './ImageUpload.css';

interface ImageUploadProps {
  value?: string;
  onChange?: (url: string) => void;
  maxFileSize?: number;
  maxDimension?: number;
  accept?: string;
  itemCode?: string; // 物品编码，用于文件命名
}

interface UploadResult {
  success: boolean;
  message: string;
  data: {
    url: string;
  
    filename: string;
    file_size: number;
    content_type: string;
  };
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value,
  onChange,
  maxFileSize = 500,
  maxDimension = 2048,
  accept = 'image/*',
  itemCode // 物品编码
}) => {
  const { t } = useTranslation();
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = useCallback(async (file: File): Promise<UploadResult | false> => {
    // 验证文件格式
    if (!file.type.startsWith('image/')) {
      message.error(t('messages.pleaseSelectImage'));
      return false;
    }

    // 验证文件大小
    if (file.size > maxFileSize * 1024) {
      message.error(t('messages.fileSizeExceeded', { maxSize: maxFileSize }));
      return false;
    }

    // 验证图片尺寸
    return new Promise((resolve) => {
      const img = new window.Image();
      img.onload = async () => {
        if (img.width > maxDimension || img.height > maxDimension) {
          message.error(t('messages.imageDimensionExceeded', { maxDimension }));
          resolve(false);
          return;
        }

        try {
          setUploading(true);
          const result = await handleUpload(file);
          if (result && result.success) {
            onChange?.(result.data.url);
            message.success(t('messages.imageUploadSuccess'));
            resolve(result);
          } else {
            message.error(t('messages.imageUploadFailed'));
            resolve(false);
          }
        } catch (error) {
          console.error('上传失败:', error);
          message.error(t('messages.imageUploadFailed'));
          resolve(false);
        } finally {
          setUploading(false);
        }
      };
      img.onerror = () => {
        message.error('图片文件损坏');
        resolve(false);
      };
      img.src = URL.createObjectURL(file);
    });
  }, [maxFileSize, maxDimension, onChange, itemCode]);

  const handleUpload = useCallback(async (file: File): Promise<UploadResult> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('sub_dir', 'items');
    
    // 动态获取物品编码
    const currentItemCode = itemCode || 'ITEM';
    formData.append('item_code', currentItemCode);

    const token = localStorage.getItem('access_token');
    const response = await fetch('/api/admin/upload/images', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '上传失败');
    }

    return await response.json();
  }, [itemCode]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleDelete = async () => {
    if (!value) return;

    try {
      const filename = value.split('/').pop();
      if (!filename || !filename.startsWith('item-image-')) {
        console.warn('无法删除非标准格式的图片');
        return;
      }

      const token = localStorage.getItem('access_token');
      const response = await fetch(`/api/admin/upload/images/admin/items/${filename}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        onChange?.('');
        message.success('图片删除成功');
      } else {
        console.warn('删除图片失败:', response.status);
      }
    } catch (error) {
      console.error('删除图片失败:', error);
    }
  };

  return (
    <div className="image-upload-container">
      {value ? (
        <div className="image-display">
          <Image
            src={value}
            alt="物品图片"
            style={{ objectFit: 'cover', cursor: 'pointer', width: '100%', height: 'auto' }}
            onClick={handleClick}
            preview={false}
          />
          <div className="image-overlay">
            <Button
              type="text"
              icon={<UploadOutlined />}
              onClick={handleClick}
              className="upload-button"
              title="点击更换图片"
            />
          </div>
        </div>
      ) : (
        <div className="upload-area" onClick={handleClick}>
          <div className="upload-content">
            <UploadOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
            <div style={{ marginTop: '8px', color: '#666' }}>
              点击上传图片
            </div>
          </div>
        </div>
      )}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        style={{ display: 'none' }}
        onChange={handleFileChange}
      />
    </div>
  );
};

export default ImageUpload; 