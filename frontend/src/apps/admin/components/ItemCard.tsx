import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Button, Typography, message } from 'antd';
import { EditOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import PermissionGuard from '@shared/components/PermissionGuard';
import { PERMISSIONS } from '@shared/config/permissions';
import { getFallbackImageByCategory, DEFAULT_PRODUCT_IMAGE } from '@shared/utils/imageUtils';

const { Text, Paragraph } = Typography;

interface ItemCardProps {
  item: {
    id: number;
    name: string;
    code: string;
    description?: string;
    image_url?: string;
    purchase_unit?: string;
    inventory_unit?: string;
    qty_per_up?: number;
    is_active?: boolean;
    is_purchasable?: boolean;
    brand?: string;
    spec_material?: string;
    size_dimension?: string;
    category?: {
      name: string;
      primary_category?: {
        name: string;
      };
    };
  };
  preferredPrice?: {
    price_range: {
      min_price: number;
      max_price: number;
    } | null;
    min_price?: number;
    max_price?: number;
    supplier: {
      id: number;
      name_cn?: string;
      name_en?: string;
      code: string;
    } | null;
  };
  showActions?: boolean;
  showPrice?: boolean;
  showEdit?: boolean;
  showAddToCart?: boolean;
  size?: 'small' | 'default' | 'large';
  onClick?: (item: any) => void;
  onEdit?: (item: any) => void;
  onAddToCart?: (item: any) => void;
}

const ItemCard: React.FC<ItemCardProps> = ({
  item,
  preferredPrice,
  showActions = true,
  showPrice = true,
  showEdit = false,
  showAddToCart = false,
  size = 'default',
  onClick,
  onEdit,
  onAddToCart
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // 根据商品分类获取合适的fallback图片
  const getDefaultImageUrl = () => {
    const categoryName = item.category?.name || item.category?.primary_category?.name;
    return getFallbackImageByCategory(categoryName) || DEFAULT_PRODUCT_IMAGE;
  };
  
  // 固定卡片宽度
  const CARD_WIDTH = 220;
  const IMAGE_SIZE = CARD_WIDTH - 24; // 减去padding (12px * 2)

  // 格式化价格（USD）
  const formatUSDPrice = (price?: number | string | null) => {
    if (price === null || price === undefined || price === '') {
      return t('item.noPrice');
    }
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    // 按照要求，大于1美元显示2位小数，小于1美元显示4位小数
    if (numPrice >= 1) {
      return `$${numPrice.toFixed(2)}`;
    } else {
      return `$${numPrice.toFixed(4)}`;
    }
  };

  // 处理卡片点击
  const handleCardClick = () => {
    if (onClick) {
      onClick(item);
    }
  };

  // 处理编辑点击
  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit(item);
    } else {
      navigate(`/admin/items/${item.id}/edit`);
    }
  };

  // 处理加入购物车点击
  const handleAddToCartClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onAddToCart) {
      onAddToCart(item);
    }
  };

  return (
    <Card
      hoverable
      style={{ 
        width: CARD_WIDTH, 
        marginBottom: 0,
        height: 'auto',
        position: 'relative'
      }}
      onClick={handleCardClick}
      bodyStyle={{ padding: '12px' }}
    >
      {/* 图片区域 */}
      <div style={{ 
        width: IMAGE_SIZE,
        height: IMAGE_SIZE, 
        overflow: 'hidden', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        backgroundColor: '#f5f5f5',
        position: 'relative',
        borderRadius: '6px',
        marginBottom: '12px',
        margin: '0 auto'
      }}>
        <img
          src={item.image_url || getDefaultImageUrl()}
          alt={item.name}
          style={{ 
            width: '100%', 
            height: '100%', 
            objectFit: 'cover',
            borderRadius: '6px'
          }}
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = getDefaultImageUrl();
          }}
        />
        
        {/* 分类标签 - 左上角 */}
        {item.category && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            zIndex: 2
          }}>
            <div style={{
              background: '#1890ff',
              color: 'white',
              padding: '2px 6px',
              fontSize: '10px',
              fontWeight: '500',
              borderRadius: '0 0 8px 0',
              border: '1px solid rgba(255,255,255,0.2)',
              boxShadow: '0 2px 4px rgba(24,144,255,0.3)',
              maxWidth: '140px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              lineHeight: '1.2'
            }}>
              {item.category.primary_category?.name} / {item.category.name}
            </div>
          </div>
        )}
        
        {/* 状态标签 - 右上角 */}
        <div style={{
          position: 'absolute',
          top: 0,
          right: 0,
          zIndex: 2,
          display: 'flex',
          flexDirection: 'column',
          gap: '2px'
        }}>
          {!item.is_active && (
            <div style={{
              background: '#ff4d4f',
              color: 'white',
              padding: '2px 6px',
              fontSize: '10px',
              fontWeight: '500',
              borderRadius: '0 0 0 8px',
              border: '1px solid rgba(255,255,255,0.2)',
              boxShadow: '0 2px 4px rgba(255,77,79,0.3)',
              whiteSpace: 'nowrap'
            }}>
              禁用
            </div>
          )}
          {!item.is_purchasable && (
            <div style={{
              background: '#fa8c16',
              color: 'white',
              padding: '2px 6px',
              fontSize: '10px',
              fontWeight: '500',
              borderRadius: '0 0 0 8px',
              border: '1px solid rgba(255,255,255,0.2)',
              boxShadow: '0 2px 4px rgba(250,140,22,0.3)',
              whiteSpace: 'nowrap'
            }}>
              不可购买
            </div>
          )}
        </div>
        
        {/* 属性标签 - 左下角 */}
        {(item.brand || item.spec_material || item.size_dimension) && (
          <div style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            zIndex: 2,
            display: 'flex',
            flexWrap: 'wrap',
            gap: '2px',
            maxWidth: '80%'
          }}>
            {item.brand && (
              <div style={{
                background: '#722ed1',
                color: 'white',
                padding: '2px 6px',
                fontSize: '10px',
                fontWeight: '500',
                borderRadius: '0 8px 0 0',
                border: '1px solid rgba(255,255,255,0.2)',
                boxShadow: '0 2px 4px rgba(114,46,209,0.3)',
                whiteSpace: 'nowrap'
              }}>
                {item.brand}
              </div>
            )}
            {item.spec_material && (
              <div style={{
                background: '#13c2c2',
                color: 'white',
                padding: '2px 6px',
                fontSize: '10px',
                fontWeight: '500',
                borderRadius: '0 8px 0 0',
                border: '1px solid rgba(255,255,255,0.2)',
                boxShadow: '0 2px 4px rgba(19,194,194,0.3)',
                whiteSpace: 'nowrap'
              }}>
                {item.spec_material}
              </div>
            )}
            {item.size_dimension && (
              <div style={{
                background: '#1890ff',
                color: 'white',
                padding: '2px 6px',
                fontSize: '10px',
                fontWeight: '500',
                borderRadius: '0 8px 0 0',
                border: '1px solid rgba(255,255,255,0.2)',
                boxShadow: '0 2px 4px rgba(24,144,255,0.3)',
                whiteSpace: 'nowrap'
              }}>
                {item.size_dimension}
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* 内容区域 */}
      <div>
        {/* 物品名称和编码 */}
        <div style={{ 
          marginBottom: '8px',
          height: '40px', // 固定两行高度 (14px * 1.4 line-height * 2 lines)
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-start'
        }}>
          <div style={{
            textAlign: 'left',
            lineHeight: '1.4',
            height: '40px',
            overflow: 'hidden'
          }}>
            <Text strong style={{ 
              fontSize: '14px',
              wordBreak: 'break-word',
              lineHeight: '1.4'
            }}>
              {item.name}
            </Text>
            {item.code && (
              <Text type="secondary" style={{ 
                fontSize: '12px',
                lineHeight: '1.4',
                marginLeft: '8px'
              }}>
                ({item.code})
              </Text>
            )}
          </div>
        </div>

        {/* 价格信息 */}
        {showPrice && (
          <div style={{ marginTop: '8px' }}>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center',
              lineHeight: '1.2'
            }}>
              <Text strong style={{ color: '#1890ff', fontSize: '16px', lineHeight: '1.2' }}>
                {preferredPrice?.price_range ? 
                  (preferredPrice.price_range.min_price === preferredPrice.price_range.max_price ? 
                    formatUSDPrice(preferredPrice.price_range.min_price) : 
                    `${formatUSDPrice(preferredPrice.price_range.min_price)} - ${formatUSDPrice(preferredPrice.price_range.max_price)}`
                  ) : 
                  t('item.noPriceAvailable')
                }
              </Text>
              {item.purchase_unit && (
                <Text type="secondary" style={{ fontSize: '12px', marginLeft: '4px', lineHeight: '1.2' }}>
                  /{item.purchase_unit}
                </Text>
              )}
            </div>
            {/* 供应商信息 */}
            <div style={{ marginTop: '1px', textAlign: 'left' }}>
              {preferredPrice?.supplier ? (
                <Text 
                  type="secondary" 
                  style={{ 
                    fontSize: '13px', 
                    color: '#666',
                    cursor: 'pointer',
                    textDecoration: 'underline',
                    display: 'block',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    maxWidth: '100%'
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (preferredPrice?.supplier) {
                      navigate(`/admin/suppliers/${preferredPrice.supplier.id}/items/${item.id}`);
                    }
                  }}
                  title={preferredPrice.supplier.name_en || preferredPrice.supplier.name_cn || preferredPrice.supplier.code}
                >
                  {preferredPrice.supplier.name_en || preferredPrice.supplier.name_cn || preferredPrice.supplier.code}
                </Text>
              ) : (
                <Text type="secondary" style={{ fontSize: '13px', color: '#999' }}>
                  无供应商
                </Text>
              )}
            </div>
          </div>
        )}


      </div>
      
      {/* 右下角操作按钮 */}
      {showActions && (
        <div style={{
          position: 'absolute',
          bottom: '8px',
          right: '8px',
          display: 'flex',
          gap: '4px',
          zIndex: 3
        }}>
          {/* 加入购物车按钮 */}
          {showAddToCart && item.is_purchasable && (
            <PermissionGuard permission={PERMISSIONS.CART.ADD_ITEM}>
              <Button
                type="text"
                icon={<ShoppingCartOutlined />}
                onClick={handleAddToCartClick}
                title={t('item.addToCart')}
                style={{
                  width: '24px',
                  height: '24px',
                  minWidth: '24px',
                  padding: '0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#52c41a',
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  borderRadius: '50%',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}
              />
            </PermissionGuard>
          )}
          
          {/* 编辑按钮 */}
          {showEdit && (
            <PermissionGuard permission={PERMISSIONS.ITEM.UPDATE}>
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={handleEditClick}
                title={t('item.edit')}
                style={{
                  width: '24px',
                  height: '24px',
                  minWidth: '24px',
                  padding: '0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#1890ff',
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  borderRadius: '50%',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}
              />
            </PermissionGuard>
          )}
        </div>
      )}
    </Card>
  );
};

export default ItemCard; 