import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Tag, Space, Spin, Empty, message } from 'antd';
import { apiClient } from '../services/authService';

interface PeerCategory {
  id: number;
  name: string;
  item_count: number;
  is_selected: boolean;
}

interface CategoryAttributes {
  brand: string[];
  spec_material: string[];
  size_dimension: string[];
}

interface ItemFilterPanelProps {
  selectedCategoryId?: number;
  onCategoryChange: (categoryId: number | undefined) => void;
  onAttributeFilterChange: (attributes: {
    brands: string[];
    specs: string[];
    sizes: string[];
  }) => void;
  visible: boolean;
  currentBrands?: string[];
  currentSpecs?: string[];
  currentSizes?: string[];
}

const ItemFilterPanel: React.FC<ItemFilterPanelProps> = ({
  selectedCategoryId,
  onCategoryChange,
  onAttributeFilterChange,
  visible,
  currentBrands = [],
  currentSpecs = [],
  currentSizes = []
}) => {
  const { t } = useTranslation();
  const [peerCategories, setPeerCategories] = useState<PeerCategory[]>([]);
  const [attributes, setAttributes] = useState<CategoryAttributes>({
    brand: [],
    spec_material: [],
    size_dimension: []
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取平级分类
  const fetchPeerCategories = async (categoryId: number) => {
    setLoading(true);
    setError(null);
    try {
              const response = await apiClient.get(`/items/categories/${categoryId}/peers`);
      setPeerCategories(response.data.data);
    } catch (error: any) {
      console.error('获取平级分类失败:', error);
      setError(t('item.getPeerCategoriesFailed'));
      message.error(t('item.getPeerCategoriesFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 获取分类属性
  const fetchCategoryAttributes = async (categoryId: number) => {
    try {
              const response = await apiClient.get(`/items/categories/${categoryId}/attributes`);
      setAttributes(response.data.data);
    } catch (error: any) {
      console.error('获取分类属性失败:', error);
      message.error(t('item.getCategoryAttributesFailed'));
    }
  };

  // 当选中分类变化时
  useEffect(() => {
    if (selectedCategoryId && visible) {
      // 获取新分类的数据
      fetchPeerCategories(selectedCategoryId);
      fetchCategoryAttributes(selectedCategoryId);
    }
  }, [selectedCategoryId, visible]);

  // 处理分类切换
  const handleCategoryClick = (categoryId: number) => {
    onCategoryChange(categoryId);
  };

  // 处理属性筛选
  const handleAttributeToggle = (type: 'brand' | 'spec' | 'size', value: string) => {
    let newBrands = [...currentBrands];
    let newSpecs = [...currentSpecs];
    let newSizes = [...currentSizes];

    switch (type) {
      case 'brand':
        // 单选：如果已选中则取消，否则选中新的
        newBrands = currentBrands.includes(value) ? [] : [value];
        break;
      case 'spec':
        // 单选：如果已选中则取消，否则选中新的
        newSpecs = currentSpecs.includes(value) ? [] : [value];
        break;
      case 'size':
        // 单选：如果已选中则取消，否则选中新的
        newSizes = currentSizes.includes(value) ? [] : [value];
        break;
    }

    // 通知父组件属性筛选变化
    onAttributeFilterChange({
      brands: newBrands,
      specs: newSpecs,
      sizes: newSizes
    });
  };

  if (!visible || !selectedCategoryId) {
    return null;
  }

  return (
    <Card 
      size="small" 
      style={{ 
        marginBottom: 2, 
        backgroundColor: 'transparent',
        border: 'none',
        borderRadius: '0',
        padding: '2px 4px'
      }}
    >
      <Spin spinning={loading}>
        {error && (
          <div style={{ 
            color: '#ff4d4f', 
            fontSize: '11px', 
            marginBottom: '4px',
            textAlign: 'center'
          }}>
            {error}
          </div>
        )}
        
        {/* 平级分类快速切换 */}
        <div style={{ marginBottom: 8 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span style={{ 
              fontSize: '13px', 
              color: '#666', 
              fontWeight: 500,
              whiteSpace: 'nowrap'
            }}>
              Sub Category:
            </span>
            <Space wrap size={[4, 4]} style={{ flex: 1 }}>
              {peerCategories.map(category => (
                <Tag
                  key={category.id}
                  color={category.is_selected ? '#1890ff' : undefined}
                  style={{
                    cursor: 'pointer',
                    padding: '2px 8px',
                    fontSize: '12px',
                    border: category.is_selected ? '1px solid #1890ff' : '1px solid #d9d9d9',
                    backgroundColor: category.is_selected ? '#1890ff' : '#fff',
                    color: category.is_selected ? '#fff' : '#333',
                    transition: 'all 0.2s ease',
                    margin: 0
                  }}
                  onClick={() => handleCategoryClick(category.id)}
                >
                  {category.name} ({category.item_count})
                </Tag>
              ))}
            </Space>
          </div>
        </div>

        {/* 属性筛选 */}
        {(attributes.brand.length > 0 || attributes.spec_material.length > 0 || attributes.size_dimension.length > 0) && (
          <div>
            {/* 品牌筛选 */}
            {attributes.brand.length > 0 && (
              <div style={{ marginBottom: 6 }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                  <span style={{ fontSize: '12px', color: '#666', whiteSpace: 'nowrap' }}>{t('item.brand')}</span>
                  <Space wrap size={[2, 2]} style={{ flex: 1 }}>
                    {attributes.brand.map(brand => (
                      <Tag
                        key={brand}
                        color={currentBrands.includes(brand) ? '#52c41a' : undefined}
                        style={{
                          cursor: 'pointer',
                          padding: '1px 6px',
                          fontSize: '11px',
                          border: currentBrands.includes(brand) ? '1px solid #52c41a' : '1px solid #d9d9d9',
                          backgroundColor: currentBrands.includes(brand) ? '#52c41a' : '#fff',
                          color: currentBrands.includes(brand) ? '#fff' : '#333',
                          transition: 'all 0.2s ease',
                          margin: 0
                        }}
                        onClick={() => handleAttributeToggle('brand', brand)}
                      >
                        {brand}
                      </Tag>
                    ))}
                  </Space>
                </div>
              </div>
            )}

            {/* 规格/材质筛选 */}
            {attributes.spec_material.length > 0 && (
              <div style={{ marginBottom: 6 }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                  <span style={{ fontSize: '12px', color: '#666', whiteSpace: 'nowrap' }}>{t('item.spec')}</span>
                  <Space wrap size={[2, 2]} style={{ flex: 1 }}>
                    {attributes.spec_material.map(spec => (
                      <Tag
                        key={spec}
                        color={currentSpecs.includes(spec) ? '#52c41a' : undefined}
                        style={{
                          cursor: 'pointer',
                          padding: '1px 6px',
                          fontSize: '11px',
                          border: currentSpecs.includes(spec) ? '1px solid #52c41a' : '1px solid #d9d9d9',
                          backgroundColor: currentSpecs.includes(spec) ? '#52c41a' : '#fff',
                          color: currentSpecs.includes(spec) ? '#fff' : '#333',
                          transition: 'all 0.2s ease',
                          margin: 0
                        }}
                        onClick={() => handleAttributeToggle('spec', spec)}
                      >
                        {spec}
                      </Tag>
                    ))}
                  </Space>
                </div>
              </div>
            )}

            {/* 尺寸/规格筛选 */}
            {attributes.size_dimension.length > 0 && (
              <div style={{ marginBottom: 6 }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                  <span style={{ fontSize: '12px', color: '#666', whiteSpace: 'nowrap' }}>{t('item.size')}</span>
                  <Space wrap size={[2, 2]} style={{ flex: 1 }}>
                    {attributes.size_dimension.map(size => (
                      <Tag
                        key={size}
                        color={currentSizes.includes(size) ? '#52c41a' : undefined}
                        style={{
                          cursor: 'pointer',
                          padding: '1px 6px',
                          fontSize: '11px',
                          border: currentSizes.includes(size) ? '1px solid #52c41a' : '1px solid #d9d9d9',
                          backgroundColor: currentSizes.includes(size) ? '#52c41a' : '#fff',
                          color: currentSizes.includes(size) ? '#fff' : '#333',
                          transition: 'all 0.2s ease',
                          margin: 0
                        }}
                        onClick={() => handleAttributeToggle('size', size)}
                      >
                        {size}
                      </Tag>
                    ))}
                  </Space>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 无数据提示 */}
        {!loading && peerCategories.length === 0 && !error && (
          <Empty 
            description={t('item.noPeerCategories')} 
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            style={{ padding: '6px 0' }}
          />
        )}
      </Spin>
    </Card>
  );
};

export default ItemFilterPanel; 