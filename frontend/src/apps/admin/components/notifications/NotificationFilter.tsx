import React, { useState, useEffect } from 'react';
import { Form, Select, DatePicker, Input, Button, Space, Row, Col, Card } from 'antd';
import { SearchOutlined, ReloadOutlined, FilterOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { NotificationListParams } from '../../types/notification';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface NotificationFilterProps {
  onFilter: (params: NotificationListParams) => void;
  onReset: () => void;
  loading?: boolean;
}

const NotificationFilter: React.FC<NotificationFilterProps> = ({
  onFilter,
  onReset,
  loading = false
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [expanded, setExpanded] = useState(false);

  const handleFilter = () => {
    const values = form.getFieldsValue();
    const params: NotificationListParams = {
      page: 1, // 重置到第一页
      size: 20,
      ...values
    };

    // 处理日期范围
    if (values.date_range && values.date_range.length === 2) {
      params.start_date = values.date_range[0].format('YYYY-MM-DD');
      params.end_date = values.date_range[1].format('YYYY-MM-DD');
      delete params.date_range;
    }

    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key as keyof NotificationListParams] === undefined || 
          params[key as keyof NotificationListParams] === null ||
          params[key as keyof NotificationListParams] === '') {
        delete params[key as keyof NotificationListParams];
      }
    });

    onFilter(params);
  };

  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  return (
    <Card size="small" style={{ marginBottom: 16 }}>
      <Form
        form={form}
        layout="horizontal"
        onFinish={handleFilter}
        initialValues={{
          status: '',
          notification_type: '',
          date_range: undefined,
          search: ''
        }}
      >
        <Row gutter={16}>
          {/* 基础筛选 */}
          <Col xs={24} sm={12} md={6}>
            <Form.Item name="status" label={t('notifications.filter.status')}>
              <Select placeholder={t('notifications.filter.status_placeholder')} allowClear>
                <Option value="unread">{t('notifications.status.unread')}</Option>
                <Option value="read">{t('notifications.status.read')}</Option>
              </Select>
            </Form.Item>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Form.Item name="notification_type" label={t('notifications.filter.type')}>
              <Select placeholder={t('notifications.filter.type_placeholder')} allowClear>
                <Option value="inventory_alert">{t('notifications.type.inventory_alert')}</Option>
                <Option value="approval_flow">{t('notifications.type.approval_flow')}</Option>
                <Option value="system">{t('notifications.type.system')}</Option>
                <Option value="other">{t('notifications.type.other')}</Option>
              </Select>
            </Form.Item>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Form.Item name="search" label={t('notifications.filter.search')}>
              <Input 
                placeholder={t('notifications.filter.search_placeholder')}
                prefix={<SearchOutlined />}
              />
            </Form.Item>
          </Col>
          
          <Col xs={24} sm={12} md={6}>
            <Form.Item label=" " style={{ marginBottom: 0 }}>
              <Space>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  icon={<FilterOutlined />}
                  loading={loading}
                >
                  {t('common.filter')}
                </Button>
                <Button 
                  onClick={handleReset} 
                  icon={<ReloadOutlined />}
                >
                  {t('common.reset')}
                </Button>
                <Button 
                  type="link" 
                  onClick={toggleExpanded}
                  size="small"
                >
                  {expanded ? t('common.collapse') : t('common.expand')}
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        {/* 扩展筛选 */}
        {expanded && (
          <Row gutter={16}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="date_range" label={t('notifications.filter.date_range')}>
                <RangePicker 
                  style={{ width: '100%' }}
                  placeholder={[
                    t('notifications.filter.start_date'),
                    t('notifications.filter.end_date')
                  ]}
                />
              </Form.Item>
            </Col>
            
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="size" label={t('notifications.filter.page_size')}>
                <Select defaultValue={20}>
                  <Option value={10}>10</Option>
                  <Option value={20}>20</Option>
                  <Option value={50}>50</Option>
                  <Option value={100}>100</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        )}
      </Form>
    </Card>
  );
};

export default NotificationFilter;
