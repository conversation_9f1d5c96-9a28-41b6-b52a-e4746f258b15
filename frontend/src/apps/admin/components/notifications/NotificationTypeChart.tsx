import React from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import { useTranslation } from 'react-i18next';

interface NotificationTypeChartProps {
  data: Record<string, number>;
}

const NotificationTypeChart: React.FC<NotificationTypeChartProps> = ({ data }) => {
  const { t } = useTranslation();

  // 转换数据格式
  const chartData = Object.entries(data).map(([key, value]) => ({
    name: t(`notifications.${key}`),
    value,
    key
  })).filter(item => item.value > 0); // 只显示有数据的项

  // 如果没有数据，显示空状态
  if (chartData.length === 0) {
    return (
      <div style={{ 
        textAlign: 'center', 
        padding: '40px 20px',
        color: '#8c8c8c'
      }}>
        {t('notifications.charts.no_data')}
      </div>
    );
  }

  // 根据数据类型选择颜色
  const getBarColor = (key: string) => {
    switch (key) {
      case 'inventory_alert':
        return '#ff4d4f';
      case 'approval_flow':
        return '#1890ff';
      case 'system':
        return '#52c41a';
      case 'other':
        return '#8c8c8c';
      case 'unread':
        return '#faad14';
      case 'read':
        return '#52c41a';
      default:
        return '#1890ff';
    }
  };

  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis 
          dataKey="name" 
          angle={-45}
          textAnchor="end"
          height={80}
          interval={0}
        />
        <YAxis />
        <Tooltip 
          formatter={(value: number, name: string) => [
            value, 
            t('notifications.charts.count')
          ]}
        />
        <Bar 
          dataKey="value" 
          fill="#1890ff"
          radius={[4, 4, 0, 0]}
        >
          {chartData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={getBarColor(entry.key)} />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  );
};

export default NotificationTypeChart;
