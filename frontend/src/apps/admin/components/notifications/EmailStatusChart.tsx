import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveC<PERSON>r, <PERSON>, Tooltip } from 'recharts';
import { useTranslation } from 'react-i18next';

interface EmailStatusChartProps {
  data: {
    pending: number;
    sent: number;
    failed: number;
  };
}

const EmailStatusChart: React.FC<EmailStatusChartProps> = ({ data }) => {
  const { t } = useTranslation();

  // 转换数据格式
  const chartData = [
    {
      name: t('notifications.email_status.pending'),
      value: data.pending,
      color: '#faad14'
    },
    {
      name: t('notifications.email_status.sent'),
      value: data.sent,
      color: '#52c41a'
    },
    {
      name: t('notifications.email_status.failed'),
      value: data.failed,
      color: '#ff4d4f'
    }
  ].filter(item => item.value > 0); // 只显示有数据的项

  // 如果没有数据，显示空状态
  if (chartData.length === 0) {
    return (
      <div style={{ 
        textAlign: 'center', 
        padding: '40px 20px',
        color: '#8c8c8c'
      }}>
        {t('notifications.charts.no_data')}
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height={300}>
      <PieChart>
        <Pie
          data={chartData}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percent }) => `${name}: ${((percent || 0) * 100).toFixed(0)}%`}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
        >
          {chartData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip 
          formatter={(value: number, name: string) => [
            value, 
            t(`notifications.email_status.${name.toLowerCase()}`)
          ]}
        />
        <Legend />
      </PieChart>
    </ResponsiveContainer>
  );
};

export default EmailStatusChart;
