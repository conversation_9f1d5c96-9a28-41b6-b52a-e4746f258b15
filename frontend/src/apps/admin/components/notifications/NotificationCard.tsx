import React from 'react';
import { Card, Tag, Space, Typography, Button } from 'antd';
import { BellOutlined, CheckOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { Notification } from '../../types/notification';
import { formatDate } from '@shared/utils/dateUtils';

const { Text, Title } = Typography;

interface NotificationCardProps {
  notification: Notification;
  onMarkRead: (id: number) => void;
  onDelete: (id: number) => void;
  onView: (id: number) => void;
  selected: boolean;
  onSelect: (id: number, selected: boolean) => void;
}

const NotificationCard: React.FC<NotificationCardProps> = ({
  notification,
  onMarkRead,
  onDelete,
  onView,
  selected,
  onSelect
}) => {
  const { t } = useTranslation();
  const { i18n } = useTranslation();

  const getNotificationTypeColor = (type: string) => {
    switch (type) {
      case 'inventory_alert':
        return 'red';
      case 'approval_flow':
        return 'blue';
      case 'system':
        return 'green';
      default:
        return 'default';
    }
  };

  const getNotificationTypeText = (type: string) => {
    switch (type) {
      case 'inventory_alert':
        return t('notifications.type.inventory_alert');
      case 'approval_flow':
        return t('notifications.type.approval_flow');
      case 'system':
        return t('notifications.type.system');
      default:
        return t('notifications.type.other');
    }
  };

  const getStatusColor = (status: string) => {
    return status === 'unread' ? 'blue' : 'default';
  };

  const getStatusText = (status: string) => {
    return status === 'unread' ? t('notifications.status.unread') : t('notifications.status.read');
  };

  return (
    <Card
      size="small"
      style={{
        marginBottom: 8,
        border: selected ? '2px solid #1890ff' : undefined,
        backgroundColor: notification.status === 'unread' ? '#f0f8ff' : undefined
      }}
      bodyStyle={{ padding: 12 }}
    >
      <div style={{ display: 'flex', alignItems: 'flex-start', gap: 12 }}>
        {/* 选择框 */}
        <input
          type="checkbox"
          checked={selected}
          onChange={(e) => onSelect(notification.id, e.target.checked)}
          style={{ marginTop: 4 }}
        />
        
        {/* 通知图标 */}
        <BellOutlined 
          style={{ 
            fontSize: 20, 
            color: notification.status === 'unread' ? '#1890ff' : '#8c8c8c',
            marginTop: 2
          }} 
        />
        
        {/* 通知内容 */}
        <div style={{ flex: 1 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 8 }}>
            <Title level={5} style={{ margin: 0, color: notification.status === 'unread' ? '#000' : '#8c8c8c' }}>
              {notification.title}
            </Title>
            <Space size={8}>
              <Tag color={getNotificationTypeColor(notification.notification_type)}>
                {getNotificationTypeText(notification.notification_type)}
              </Tag>
              <Tag color={getStatusColor(notification.status)}>
                {getStatusText(notification.status)}
              </Tag>
            </Space>
          </div>
          
          <Text style={{ color: '#666', fontSize: 14 }}>
            {notification.content}
          </Text>
          
          <div style={{ marginTop: 8, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {formatDate(notification.created_at, i18n.language === 'zh' ? 'DD-MM-YYYY HH:mm' : 'MM-DD-YYYY HH:mm')}
            </Text>
            
            <Space size={8}>
              {notification.status === 'unread' && (
                <Button
                  size="small"
                  icon={<CheckOutlined />}
                  onClick={() => onMarkRead(notification.id)}
                >
                  {t('notifications.actions.mark_read')}
                </Button>
              )}
              
              {notification.action_url && (
                <Button
                  size="small"
                  type="primary"
                  icon={<EyeOutlined />}
                  onClick={() => onView(notification.id)}
                >
                  {t('notifications.actions.view')}
                </Button>
              )}
              
              <Button
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={() => onDelete(notification.id)}
              >
                {t('notifications.actions.delete')}
              </Button>
            </Space>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default NotificationCard;
