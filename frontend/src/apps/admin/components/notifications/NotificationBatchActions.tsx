import React from 'react';
import { But<PERSON>, Space, Typography, Popconfirm, message } from 'antd';
import { 
  CheckOutlined, 
  DeleteOutlined, 
  EyeOutlined, 
  ClearOutlined 
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Text } = Typography;

interface NotificationBatchActionsProps {
  selectedIds: number[];
  onBatchMarkRead: () => void;
  onBatchDelete: () => void;
  onClearSelection: () => void;
  loading?: boolean;
}

const NotificationBatchActions: React.FC<NotificationBatchActionsProps> = ({
  selectedIds,
  onBatchMarkRead,
  onBatchDelete,
  onClearSelection,
  loading = false
}) => {
  const { t } = useTranslation();

  const handleBatchMarkRead = () => {
    if (selectedIds.length === 0) {
      message.warning(t('notifications.batch.no_selection'));
      return;
    }
    onBatchMarkRead();
  };

  const handleBatchDelete = () => {
    if (selectedIds.length === 0) {
      message.warning(t('notifications.batch.no_selection'));
      return;
    }
    onBatchDelete();
  };

  const handleClearSelection = () => {
    onClearSelection();
  };

  if (selectedIds.length === 0) {
    return null;
  }

  return (
    <div style={{ 
      padding: '12px 16px', 
      backgroundColor: '#f5f5f5', 
      border: '1px solid #d9d9d9',
      borderRadius: '6px',
      marginBottom: 16
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <EyeOutlined style={{ color: '#1890ff' }} />
          <Text strong>
            {t('notifications.batch.selected_count', { count: selectedIds.length })}
          </Text>
          <Text type="secondary">
            {t('notifications.batch.actions_available')}
          </Text>
        </div>
        
        <Space>
          <Button
            type="primary"
            icon={<CheckOutlined />}
            onClick={handleBatchMarkRead}
            loading={loading}
          >
            {t('notifications.batch.mark_read')}
          </Button>
          
          <Popconfirm
            title={t('notifications.batch.delete_confirm_title')}
            description={t('notifications.batch.delete_confirm_description', { count: selectedIds.length })}
            onConfirm={handleBatchDelete}
            okText={t('common.confirm')}
            cancelText={t('common.cancel')}
            okType="danger"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              loading={loading}
            >
              {t('notifications.batch.delete')}
            </Button>
          </Popconfirm>
          
          <Button
            icon={<ClearOutlined />}
            onClick={handleClearSelection}
            disabled={loading}
          >
            {t('notifications.batch.clear_selection')}
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default NotificationBatchActions;
