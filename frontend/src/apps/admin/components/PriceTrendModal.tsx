import React, { useState, useEffect } from 'react';
import { Modal, Spin, message, DatePicker, Space, Button } from 'antd';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import PriceTrendChart from './PriceTrendChart';
import { priceTrendService, PriceTrendResponse } from '@admin/services/supplierService';

interface PriceTrendModalProps {
  visible: boolean;
  itemSupplierId: number;
  onCancel: () => void;
}

const PriceTrendModal: React.FC<PriceTrendModalProps> = ({
  visible,
  itemSupplierId,
  onCancel
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<PriceTrendResponse | null>(null);
  const [startDate, setStartDate] = useState<dayjs.Dayjs | null>(null);
  const [endDate, setEndDate] = useState<dayjs.Dayjs | null>(null);

  // 加载价格走势数据
  const loadPriceTrend = async () => {
    if (!itemSupplierId) return;

    setLoading(true);
    try {
      const response = await priceTrendService.getPriceTrend(
        itemSupplierId,
        startDate?.format(DATE_FORMATS.API_DATE),
        endDate?.format(DATE_FORMATS.API_DATE)
      );
      setData(response);
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('item.priceTrendDataFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 当模态框打开时加载数据
  useEffect(() => {
    if (visible && itemSupplierId) {
      loadPriceTrend();
    }
  }, [visible, itemSupplierId]);

  // 处理日期范围变化
  const handleDateRangeChange = () => {
    if (startDate && endDate && startDate.isAfter(endDate)) {
      message.error(t('item.startDateNotAfterEndDate'));
      return;
    }
    loadPriceTrend();
  };

  // 重置日期范围
  const handleResetDates = () => {
    setStartDate(null);
    setEndDate(null);
    // 重新加载数据（使用默认日期范围）
    setTimeout(() => {
      loadPriceTrend();
    }, 100);
  };

  return (
    <Modal
      title={t('item.priceTrendAnalysis')}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={1000}
      destroyOnClose
    >
      <div style={{ marginBottom: '16px' }}>
        <Space>
          <span>{t('item.dateRange')}：</span>
          <DatePicker
            placeholder="开始日期"
            value={startDate}
            onChange={setStartDate}
            format={DATE_FORMATS.DATE_ONLY}
          />
          <span>至</span>
          <DatePicker
            placeholder="结束日期"
            value={endDate}
            onChange={setEndDate}
            format={DATE_FORMATS.DATE_ONLY}
          />
          <Button type="primary" onClick={handleDateRangeChange}>
            查询
          </Button>
          <Button onClick={handleResetDates}>
            重置
          </Button>
        </Space>
      </div>

      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>加载价格走势数据中...</div>
        </div>
      ) : data ? (
        <div>
          <div style={{ marginBottom: '16px' }}>
            <p>
              <strong>物品：</strong>{data.item_name} | 
              <strong>供应商：</strong>{data.supplier_name} | 
              <strong>日期范围：</strong>{data.date_range.start_date} 至 {data.date_range.end_date}
            </p>
          </div>
          <PriceTrendChart
            data={data.price_trends}
            title="价格走势图"
          />
        </div>
      ) : (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <p>暂无价格走势数据</p>
        </div>
      )}
    </Modal>
  );
};

export default PriceTrendModal;
