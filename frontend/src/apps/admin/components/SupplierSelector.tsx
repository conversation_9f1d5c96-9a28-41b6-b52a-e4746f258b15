import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Modal,
  Input,
  List,
  Card,
  Row,
  Col,
  Typography,
  Tag,
  Spin,
  Empty,
  Space,
  Divider
} from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { supplierService, Supplier, getSupplierDisplayName } from '@admin/services/supplierService';

const { Text, Title } = Typography;

interface SupplierSelectorProps {
  visible: boolean;
  onCancel: () => void;
  onSelect: (supplier: Supplier) => void;
  loading?: boolean;
}

const SupplierSelector: React.FC<SupplierSelectorProps> = ({
  visible,
  onCancel,
  onSelect,
  loading = false
}) => {
  const { t } = useTranslation();
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [loadingSuppliers, setLoadingSuppliers] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement>(null);

  // 获取供应商列表
  const fetchSuppliers = useCallback(async (search?: string, pageNum: number = 1, append: boolean = false) => {
    if (pageNum === 1) {
      setLoadingSuppliers(true);
    } else {
      setLoadingMore(true);
    }
    
    try {
      const response = await supplierService.getSuppliers({ 
        search, 
        page: pageNum,
        size: 20,
        status: 'active'
      });
      
      const newSuppliers = response.items || [];
      
      if (append) {
        setSuppliers(prev => [...prev, ...newSuppliers]);
      } else {
        setSuppliers(newSuppliers);
      }
      
      // 判断是否还有更多数据
      setHasMore(newSuppliers.length === 20);
      setPage(pageNum);
    } catch (error: any) {
      console.error('Error fetching suppliers:', error);
      if (!append) {
        setSuppliers([]);
      }
    } finally {
      setLoadingSuppliers(false);
      setLoadingMore(false);
    }
  }, []);

  // 处理搜索
  const handleSearch = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return (value: string) => {
      clearTimeout(timeoutId);
      setSearchValue(value);
      setPage(1);
      timeoutId = setTimeout(() => {
        fetchSuppliers(value, 1, false);
      }, 500);
    };
  }, [fetchSuppliers]);

  // 处理供应商选择
  const handleSupplierSelect = (supplier: Supplier) => {
    onSelect(supplier);
  };

  // 无限滚动
  useEffect(() => {
    if (loadingRef.current) {
      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasMore && !loadingMore) {
            fetchSuppliers(searchValue, page + 1, true);
          }
        },
        { threshold: 0.1 }
      );

      observerRef.current.observe(loadingRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [fetchSuppliers, hasMore, loadingMore, searchValue, page]);

  // 初始化数据
  useEffect(() => {
    if (visible) {
      fetchSuppliers('', 1, false);
    }
  }, [visible, fetchSuppliers]);

  // 渲染供应商卡片
  const renderSupplierCard = (supplier: Supplier) => (
    <Card
      hoverable
      size="small"
      style={{ marginBottom: '8px', cursor: 'pointer' }}
      onClick={() => handleSupplierSelect(supplier)}
    >
      <Row gutter={16} align="middle">
        <Col flex="auto">
          <div>
            <Text 
              strong
              style={{
                display: 'block',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: '100%'
              }}
              title={getSupplierDisplayName(supplier)}
            >
              {getSupplierDisplayName(supplier)}
            </Text>
            <div style={{ marginTop: '4px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {t('supplier.code')}: {supplier.code}
              </Text>
            </div>
            {supplier.contact_person && (
              <div style={{ marginTop: '2px' }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {t('supplier.contactPerson')}: {supplier.contact_person}
                </Text>
              </div>
            )}
            {supplier.phone && (
              <div style={{ marginTop: '2px' }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  电话: {supplier.phone}
                </Text>
              </div>
            )}
          </div>
        </Col>
        <Col flex="none">
          <Space direction="vertical" align="end">
            <Tag color={supplier.status === 'active' ? 'green' : 'red'}>
              {supplier.status === 'active' ? '活跃' : '停用'}
            </Tag>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              评级: {supplier.rating}/5
            </Text>
          </Space>
        </Col>
      </Row>
    </Card>
  );

  return (
    <Modal
      title="选择供应商"
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
      destroyOnClose
    >
      <div style={{ marginBottom: '16px' }}>
        <Input
          placeholder="搜索供应商..."
          prefix={<SearchOutlined />}
          value={searchValue}
          onChange={(e) => handleSearch(e.target.value)}
          allowClear
        />
      </div>

      <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
        {loadingSuppliers ? (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Spin size="large" />
          </div>
        ) : suppliers.length === 0 ? (
          <Empty description="暂无供应商" />
        ) : (
          <List
            dataSource={suppliers}
            renderItem={renderSupplierCard}
            locale={{ emptyText: '暂无供应商' }}
          />
        )}

        {hasMore && (
          <div ref={loadingRef} style={{ textAlign: 'center', padding: '16px' }}>
            {loadingMore ? <Spin /> : <Text type="secondary">加载更多...</Text>}
          </div>
        )}
      </div>
    </Modal>
  );
};

export default SupplierSelector; 