import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import { apiClient } from '../services/authService';

const { Option } = Select;

interface PrimaryCategory {
  id: number;
  name: string;
  description?: string;
  code_prefix: string;
  is_active: boolean;
}

interface Category {
  id: number;
  name: string;
  description?: string;
  primary_category_id: number;
  is_active: boolean;
  primary_category?: PrimaryCategory;
}

interface CategorySelectProps {
  value?: number;
  onChange?: (value: number) => void;
  placeholder?: string;
  disabled?: boolean;
  size?: 'small' | 'middle' | 'large';
  style?: React.CSSProperties;
}

const CategorySelect: React.FC<CategorySelectProps> = ({
  value,
  onChange,
  placeholder,
  disabled = false,
  size = 'middle',
  style
}) => {
  const { t } = useTranslation();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取分类数据
  const fetchCategories = async () => {
    setLoading(true);
    try {
      // 并行获取一级分类和二级分类
      const [primaryResponse, categoriesResponse] = await Promise.all([
        apiClient.get('/items/primary-categories'),
        apiClient.get('/items/categories')
      ]);
      
      const primaryCategories = primaryResponse.data.filter((cat: PrimaryCategory) => cat.is_active);
      const categories = categoriesResponse.data.filter((cat: Category) => cat.is_active);
      
      // 为每个二级分类添加一级分类信息
      const categoriesWithPrimary = categories.map((category: Category) => {
        const primaryCategory = primaryCategories.find((pc: PrimaryCategory) => pc.id === category.primary_category_id);
        return {
          ...category,
          primary_category: primaryCategory
        };
      });
      
      // 按一级分类 -> 二级分类进行排序
      const sortedCategories = categoriesWithPrimary.sort((a: Category & { primary_category?: PrimaryCategory }, b: Category & { primary_category?: PrimaryCategory }) => {
        // 首先按一级分类名称排序
        const primaryA = a.primary_category?.name || '';
        const primaryB = b.primary_category?.name || '';
        const primaryCompare = primaryA.localeCompare(primaryB, 'zh-CN');
        
        if (primaryCompare !== 0) {
          return primaryCompare;
        }
        
        // 一级分类相同时，按二级分类名称排序
        return a.name.localeCompare(b.name, 'zh-CN');
      });
      
      setCategories(sortedCategories);
    } catch (error) {
      console.error('获取分类数据错误:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  // 自定义筛选函数
  const filterOption = (input: string, option: any) => {
    if (!option?.children) return false;
    const label = String(option.children);
    return label.toLowerCase().includes(input.toLowerCase());
  };

  // 渲染选项
  const renderOptions = useMemo(() => {
    return categories.map(category => (
      <Option key={category.id} value={category.id}>
        {category.primary_category?.name} / {category.name}
      </Option>
    ));
  }, [categories]);

  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={placeholder || t('messages.selectCategory')}
      disabled={disabled}
      size={size}
      style={style}
      showSearch
      filterOption={filterOption}
      optionFilterProp="children"
      notFoundContent={loading ? <Spin size="small" /> : t('messages.noMatchingCategories')}
      loading={loading}
    >
      {renderOptions}
    </Select>
  );
};

export default CategorySelect;
