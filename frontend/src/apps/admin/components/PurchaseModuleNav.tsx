import React from 'react';
import { Breadcrumb, <PERSON>, Button } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  ShoppingCartOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  CalendarOutlined,
  DashboardOutlined
} from '@ant-design/icons';

interface PurchaseModuleNavProps {
  currentPage: string;
  showBreadcrumb?: boolean;
}

const PurchaseModuleNav: React.FC<PurchaseModuleNavProps> = ({ 
  currentPage, 
  showBreadcrumb = true 
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const navItems = [
    {
      key: 'workbench',
      label: '采购工作台',
      icon: <ShoppingCartOutlined />,
      path: '/admin/purchase-requests'
    },

  ];

  const getBreadcrumbItems = () => {
    const items = [
      {
        title: (
          <Button 
            type="link" 
            onClick={() => navigate('/admin/purchase')}
            style={{ padding: 0, height: 'auto' }}
          >
            采购管理
          </Button>
        )
      }
    ];

    const currentItem = navItems.find(item => item.key === currentPage);
    if (currentItem) {
      items.push({
        title: currentItem.label as any
      });
    }

    return items;
  };

  return (
    <div style={{ marginBottom: '16px' }}>
      {showBreadcrumb && (
        <Breadcrumb 
          items={getBreadcrumbItems()} 
          style={{ marginBottom: '16px' }}
        />
      )}
      
      <Space wrap>
        {navItems.map(item => (
          <Button
            key={item.key}
            type={currentPage === item.key ? 'primary' : 'default'}
            icon={item.icon}
            onClick={() => navigate(item.path)}
            size="small"
          >
            {item.label}
          </Button>
        ))}
      </Space>
    </div>
  );
};

export default PurchaseModuleNav;
