import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { Card, Typography, Spin, Empty } from 'antd';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { PriceTrendData, PricePoint } from '@admin/services/supplierService';
import { getCurrencyInfo } from '@shared/utils/exchangeRateUtils';

const { Title } = Typography;

interface PriceTrendChartProps {
  data: PriceTrendData[];
  loading?: boolean;
  title?: string;
}

const PriceTrendChart: React.FC<PriceTrendChartProps> = ({
  data,
  loading = false,
  title
}) => {
  const { t } = useTranslation();
  const chartTitle = title || t('item.priceTrendChart');
  // 处理数据，过滤掉无效的价格点
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // 获取所有日期
    const allDates = new Set<string>();
    data.forEach(trend => {
      trend.price_points.forEach(point => {
        if (point.is_valid) {
          allDates.add(point.date);
        }
      });
    });

    // 按日期排序
    const sortedDates = Array.from(allDates).sort();

    // 构建图表数据
    return sortedDates.map(date => {
      const dataPoint: any = { date };
      
      data.forEach(trend => {
        const pricePoint = trend.price_points.find(p => p.date === date && p.is_valid);
        if (pricePoint) {
          dataPoint[trend.tier_name] = pricePoint.total_price;
          // 添加备注信息到数据点
          if (pricePoint.remarks) {
            dataPoint[`${trend.tier_name}_remarks`] = pricePoint.remarks;
          }
          // 添加货币信息到数据点
          dataPoint[`${trend.tier_name}_currency`] = pricePoint.currency_code;
        }
      });
      
      return dataPoint;
    });
  }, [data]);

  // 计算Y轴范围
  const yAxisDomain = useMemo(() => {
    if (!chartData || chartData.length === 0) return [0, 100];

    // 收集所有有效的价格数据
    const allPrices: number[] = [];
    chartData.forEach(dataPoint => {
      data.forEach(trend => {
        const price = dataPoint[trend.tier_name];
        if (price !== undefined && price > 0) {
          allPrices.push(price);
        }
      });
    });

    if (allPrices.length === 0) return [0, 100];

    const minPrice = Math.min(...allPrices);
    const maxPrice = Math.max(...allPrices);
    const priceRange = maxPrice - minPrice;

    // 如果价格范围很小（小于最小价格的10%），则扩大范围
    if (priceRange < minPrice * 0.1) {
      const padding = minPrice * 0.05; // 5%的边距
      return [Math.max(0, minPrice - padding), maxPrice + padding];
    }

    // 正常情况：添加10%的边距
    const padding = priceRange * 0.1;
    return [Math.max(0, minPrice - padding), maxPrice + padding];
  }, [chartData, data]);

  // 生成颜色数组
  const colors = [
    '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
    '#13c2c2', '#eb2f96', '#fa8c16', '#a0d911', '#2f54eb'
  ];

  // 自定义Tooltip组件
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: 'white',
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
        }}>
          <p style={{ margin: '0 0 4px 0', fontWeight: 'bold' }}>
            {dayjs(label).format(DATE_FORMATS.DATE_ONLY)}
          </p>
          {payload.map((entry: any, index: number) => {
            const remarks = entry.payload[`${entry.dataKey}_remarks`];
            const currencyCode = entry.payload[`${entry.dataKey}_currency`] || 'USD';
            const currencyInfo = getCurrencyInfo(currencyCode as any);
            return (
              <p key={index} style={{ 
                margin: '2px 0', 
                color: entry.color,
                fontSize: '12px'
              }}>
                <span style={{ fontWeight: 'bold' }}>{entry.dataKey}:</span> {currencyInfo.symbol}{entry.value.toFixed(2)}
                {remarks && (
                  <div style={{ 
                    marginTop: '2px', 
                    fontSize: '11px', 
                    color: '#666',
                    fontStyle: 'italic'
                  }}>
                    {remarks}
                  </div>
                )}
              </p>
            );
          })}
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>加载价格走势数据中...</div>
        </div>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card>
        <Empty description={t('item.noPriceTrendData')} />
      </Card>
    );
  }

  return (
    <Card>
      <Title level={4}>{chartTitle}</Title>
      <ResponsiveContainer width="100%" height={400}>
        <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="date"
            tickFormatter={(value) => dayjs(value).format(DATE_FORMATS.CHART_LABEL)}
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis
            domain={yAxisDomain}
            tickFormatter={(value) => `${value.toFixed(2)}`}
            label={{ value: t('item.priceLabel'), angle: -90, position: 'insideLeft' }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          {data.map((trend, index) => (
            <Line
              key={trend.tier_id}
              type="monotone"
              dataKey={trend.tier_name}
              stroke={colors[index % colors.length]}
              strokeWidth={2}
              dot={{ r: 3 }}
              activeDot={{ r: 6 }}
              connectNulls={false}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </Card>
  );
};

export default PriceTrendChart;
