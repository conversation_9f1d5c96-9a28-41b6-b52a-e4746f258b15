import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Space } from 'antd';
import { 
  EditOutlined, 
  DeleteOutlined, 
  RollbackOutlined,
  CheckCircleOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { PurchaseRequest } from '@admin/services/purchaseRequestService';
import { canApproveRequest, getApprovalLevelName } from '../../utils/approvalPermissionUtils';
import { useAuth } from '../../contexts/AuthContext';

interface RequestActionButtonsProps {
  request: PurchaseRequest;
  onEdit: () => void;
  onDelete: () => void;
  onWithdraw: () => void;
  onApprove: (level: string) => void;
  onSubmit: () => void; // 添加提交回调
  canEdit: boolean;
  canDelete: boolean;
  canApprove: boolean;
}

const RequestActionButtons: React.FC<RequestActionButtonsProps> = ({
  request,
  onEdit,
  onDelete,
  onWithdraw,
  onApprove,
  onSubmit, // 添加提交回调
  canEdit,
  canDelete,
  canApprove
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  return (
    <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
      {/* 编辑按钮 - 只有申请人可以编辑 */}
      {canEdit && request.status === 'pending_submission' && (
        <Button 
          type="primary" 
          onClick={onEdit}
        >
          {t('purchase.editApplication')}
        </Button>
      )}
      
      {/* 编辑按钮 - 已拒绝的申请也可以编辑 */}
      {canEdit && request.status === 'rejected' && (
        <Button 
          type="primary" 
          onClick={onEdit}
        >
          {t('purchase.editApplication')}
        </Button>
      )}
      
      {/* 提交按钮 - 只有申请人可以提交 */}
      {canEdit && request.status === 'pending_submission' && (
        <Button 
          type="primary" 
          onClick={onSubmit}
        >
          {t('purchase.submitApplication')}
        </Button>
      )}
      
      {/* 删除按钮 - 只有申请人可以删除 */}
      {canDelete && request.status === 'pending_submission' && (
        <Button 
          danger 
          onClick={onDelete}
        >
          {t('purchase.deleteApplication')}
        </Button>
      )}
      
      {/* 撤回按钮 - 只有申请人可以撤回，根据状态显示不同文本 */}
      {canEdit && ['under_review', 'under_principle_approval', 'under_final_approval', 'rejected'].includes(request.status) && (
        <Button 
          onClick={onWithdraw}
        >
          {request.status === 'under_review' ? t('purchase.withdrawApplication') : t('purchase.withdrawToPending')}
        </Button>
      )}
      
      {/* 审批按钮 - 根据当前状态和用户权限显示不同的审批按钮 */}
      {canApprove && request.status === 'under_review' && 
       canApproveRequest(request.status, user?.role_code, user?.permissions) && (
        <Button 
          type="primary"
          onClick={() => onApprove('review')}
        >
          {t('purchase.departmentManagerReview')}
        </Button>
      )}
      
      {canApprove && request.status === 'under_principle_approval' && 
       canApproveRequest(request.status, user?.role_code, user?.permissions) && (
        <Button 
          type="primary"
          onClick={() => onApprove('principle_approval')}
        >
          {t('purchase.itemManagerPrincipleApproval')}
        </Button>
      )}
      
      {canApprove && request.status === 'under_final_approval' && 
       canApproveRequest(request.status, user?.role_code, user?.permissions) && (
        <Button 
          type="primary"
          onClick={() => onApprove('final_approval')}
        >
          {t('purchase.companyManagerApproval')}
        </Button>
      )}
    </div>
  );
};

export default RequestActionButtons;
