import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { Card, Table, Tag, Space, Spin, message } from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  RollbackOutlined,
  UserOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { PurchaseRequest, purchaseRequestService } from '@admin/services/purchaseRequestService';
import type { RequestFlowHistory as RequestFlowHistoryType } from '@admin/services/purchaseRequestService';

interface RequestFlowHistoryProps {
  request: PurchaseRequest;
}

const RequestFlowHistory: React.FC<RequestFlowHistoryProps> = ({ request }) => {
  const { t } = useTranslation();
  const [flowHistoryData, setFlowHistoryData] = useState<RequestFlowHistoryType[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取流转历史数据
  const fetchFlowHistory = async () => {
    if (!request.id) return;
    
    setLoading(true);
    try {
      const history = await purchaseRequestService.getRequestFlowHistory(request.id);
      setFlowHistoryData(history);
    } catch (error) {
      console.error('获取流转历史失败:', error);
      message.error('获取流转历史失败');
      // 如果API调用失败，使用基础数据作为后备
      setFlowHistoryData(getBasicFlowHistory());
    } finally {
      setLoading(false);
    }
  };

  // 获取基础流转历史（作为API失败时的后备）
  const getBasicFlowHistory = (): RequestFlowHistoryType[] => {
    const basicHistory: RequestFlowHistoryType[] = [];
    
    // 添加创建记录
    if (request.created_at) {
      basicHistory.push({
        id: Date.now(),
        request_id: request.id,
        action: 'submit',
        from_status: undefined,
        to_status: 'pending_submission',
        operator_id: request.submitter_id,
        operator_name: request.submitter_name || `用户${request.submitter_id}` || '未知用户',
        approval_level: undefined,
        comments: t('purchase.applicationSubmitted'),
        created_at: request.created_at
      });
    }
    
    // 如果有提交时间，添加提交记录
    if (request.submitted_at && request.submitted_at !== request.created_at) {
      basicHistory.push({
        id: Date.now() + 1,
        request_id: request.id,
        action: 'submit',
        from_status: 'pending_submission',
        to_status: 'under_review',
        operator_id: request.submitter_id,
        operator_name: request.submitter_name || `用户${request.submitter_id}` || '未知用户',
        approval_level: undefined,
        comments: t('purchase.applicationFormallySubmitted'),
        created_at: request.submitted_at
      });
    }
    
    return basicHistory;
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchFlowHistory();
  }, [request.id]);
  // 流转历史表格列定义
  const flowHistoryColumns = [
    {
      title: t('purchase.operationTime'),
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text: any) => {
        if (text && typeof text === 'string') {
          const date = new Date(text);
          if (!isNaN(date.getTime())) {
            return dayjs(date).format(DATE_FORMATS.DATE_TIME);
          }
        }
        return '-';
      }
    },
    {
      title: t('purchase.operationType'),
      dataIndex: 'action',
      key: 'action',
      width: 120,
      render: (action: string) => {
        const actionConfig = {
          'submit': { text: t('purchase.submitApplication'), color: 'blue', icon: <ClockCircleOutlined /> },
          'approve': { text: t('purchase.approvalPassed'), color: 'green', icon: <CheckCircleOutlined /> },
          'reject': { text: t('purchase.approvalRejected'), color: 'red', icon: <CloseCircleOutlined /> },
          'return': { text: t('purchase.returnForModification'), color: 'orange', icon: <RollbackOutlined /> },
          'withdraw': { text: t('purchase.withdrawApplication'), color: 'default', icon: <RollbackOutlined /> }
        };
        
        const config = actionConfig[action as keyof typeof actionConfig] || { text: action, color: 'default', icon: null };
        
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      }
    },
    {
      title: t('purchase.operator'),
      dataIndex: 'operator_name',
      key: 'operator_name',
      width: 120,
      render: (text: string, record: any) => (
        <Space>
          <UserOutlined />
          {text || record.operator_id || t('purchase.system')}
        </Space>
      )
    },
    {
      title: t('purchase.approvalLevel'),
      dataIndex: 'approval_level',
      key: 'approval_level',
      width: 120,
      render: (level: string) => {
        const levelConfig = {
          'review': t('purchase.departmentManagerReview'),
          'principle_approval': t('purchase.itemManagerPrincipleApproval'),
          'final_approval': t('purchase.companyManagerApproval')
        };
        return levelConfig[level as keyof typeof levelConfig] || level;
      }
    },
    {
      title: t('purchase.remarks'),
      dataIndex: 'comments',
      key: 'comments',
      ellipsis: true,
      render: (text: string) => text || '-'
    }
  ];

  return (
    <Card title={t('purchase.flowHistory')} style={{ marginBottom: '24px' }}>
      <Spin spinning={loading}>
        <Table
          columns={flowHistoryColumns}
          dataSource={flowHistoryData}
          rowKey="id"
          pagination={false}
          size="small"
          rowClassName={(record) => {
            const classNameMap: { [key: string]: string } = {
              'submit': 'flow-history-submit-row',
              'approve': 'flow-history-approve-row',
              'reject': 'flow-history-reject-row',
              'return': 'flow-history-return-row',
              'withdraw': 'flow-history-withdraw-row'
            };
            return classNameMap[record.action] || '';
          }}
        />
      </Spin>
    </Card>
  );
};

export default RequestFlowHistory;
