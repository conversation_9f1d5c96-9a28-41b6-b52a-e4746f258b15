import React from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, Form, InputNumber, message } from 'antd';
import { purchaseCartService } from '@admin/services/purchaseCartService';

interface Item {
  id: number;
  name: string;
  code: string;
  description?: string;
  category_id: number;
  image_url?: string;
  purchase_unit: string;
  inventory_unit: string;
  qty_per_up: number;
  is_purchasable: boolean;
  is_active: boolean;
  brand?: string;
  spec_material?: string;
  size_dimension?: string;
  created_at: string;
  updated_at?: string;
}

interface AddToCartModalProps {
  visible: boolean;
  item: Item | null;
  onCancel: () => void;
  onSuccess: () => void;
  currentDepartmentId: number;
}

const AddToCartModal: React.FC<AddToCartModalProps> = ({
  visible,
  item,
  onCancel,
  onSuccess,
  currentDepartmentId
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);

  // 当模态框打开且选中物品时，设置表单初始值
  React.useEffect(() => {
    if (visible && item) {
      form.setFieldsValue({
        quantity: 1
      });
    }
  }, [visible, item, form]);

  // 提交加入购物车
  const handleSubmit = async () => {
    try {
      if (!item) return;
      
      const values = await form.validateFields();
      
      const cartItemData = {
        item_id: item.id,
        spq_count: values.quantity,
        requirement_notes: undefined,
        expected_delivery: undefined
      };

      setLoading(true);
      await purchaseCartService.addItemToCart(currentDepartmentId, cartItemData);
      
      message.success(t('purchase.itemAddedToCart'));
      form.resetFields();
      onSuccess();
      
    } catch (error: any) {
      console.error('添加购物车失败:', error);
      
      // 根据错误类型显示不同的提示信息
      if (error.response?.data?.detail) {
        const errorDetail = error.response.data.detail;
        if (errorDetail.includes('只能为自己部门添加购物车物品')) {
          message.error(t('purchase.canOnlyAddToOwnDepartment'));
        } else if (errorDetail.includes('用户没有部门')) {
          message.error(t('purchase.noDepartmentAssigned'));
        } else {
          message.error(errorDetail);
        }
      } else {
        message.error(t('purchase.addToCartFailed'));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  if (!item) return null;

  return (
    <Modal
      title={t('purchase.addToCart')}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      okText={t('purchase.addToCart')}
      cancelText={t('purchase.cancel')}
      width={500}
      destroyOnClose
    >
      <div style={{ display: 'flex', gap: '16px', marginBottom: '24px' }}>
        {/* 物品图片 */}
        <div style={{ width: '80px', height: '80px', flexShrink: 0 }}>
          <img
            src={item.image_url || '/placeholder-item.png'}
            alt={item.name}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              borderRadius: '8px'
            }}
          />
        </div>

        {/* 物品信息 */}
        <div style={{ flex: 1 }}>
          <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
            {item.name}
          </h3>
          {item.description && (
            <p style={{ margin: '0 0 4px 0', color: '#666', fontSize: '14px' }}>
              {item.description}
            </p>
          )}
          <p style={{ margin: '0 0 4px 0', color: '#666', fontSize: '14px' }}>
            {t('purchase.code')}: {item.code}
          </p>
        </div>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={{ quantity: 1 }}
      >
        <Form.Item
          label={t('purchase.quantity')}
          name="quantity"
          rules={[{ required: true, message: t('purchase.pleaseEnterQuantity') }]}
        >
          <InputNumber
            min={1}
            precision={0}
            style={{ width: '100%' }}
            addonAfter={`x ${item.qty_per_up || 1} ${item.purchase_unit || t('purchase.pieces')}`}
            placeholder={t('purchase.pleaseEnterQuantity')}
          />
        </Form.Item>
      </Form>

    </Modal>
  );
};

export default AddToCartModal;
