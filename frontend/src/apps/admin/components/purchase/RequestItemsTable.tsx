import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, Empty, Space, Image, Button } from 'antd';
import { ShoppingCartOutlined, LinkOutlined } from '@ant-design/icons';
import { PurchaseRequestItem } from '@admin/services/purchaseRequestService';
import { useNavigate } from 'react-router-dom';
import MultiCurrencyPriceDisplay from '@shared/components/MultiCurrencyPriceDisplay';

interface RequestItemsTableProps {
  items: PurchaseRequestItem[];
}

const RequestItemsTable: React.FC<RequestItemsTableProps> = ({ items }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  // 表格列定义
  const columns = [
    {
      title: t('purchase.itemInfo'),
      key: 'item_info',
      width: 180,
      render: (_: any, record: PurchaseRequestItem) => (
        <div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px' }}>
          {/* 物品图标 */}
          <div style={{ flexShrink: 0 }}>
            <Image
              src={record.item_image_url || '/static/images/admin/items/default-item.jpg'}
              alt={record.item_name}
              width={32}
              height={32}
              style={{ objectFit: 'cover', borderRadius: '4px', background: '#f5f5f5' }}
              fallback="/static/images/admin/items/default-item.jpg"
              preview={false}
            />
          </div>
          
          {/* 物品名称和编码 */}
          <div style={{ flex: 1, minWidth: 0 }}>
            <div
              style={{ 
                cursor: 'pointer',
                fontWeight: 'bold',
                fontSize: '12px',
                color: '#1677ff',
                marginBottom: '2px',
                wordWrap: 'break-word',
                lineHeight: '1.3',
                maxHeight: '3.9em',
                overflow: 'hidden'
              }}
              onClick={() => navigate(`/admin/items/${record.item_id}`)}
              title={t('purchase.clickToViewItemDetails', { itemName: record.item_name })}
            >
              {record.item_name}
            </div>
            <div style={{ 
              fontSize: '10px', 
              color: '#666',
              fontFamily: 'monospace'
            }}>
              {record.item_code}
            </div>
          </div>
        </div>
      )
    },
    {
      title: 'SPQ',
      dataIndex: 'spq_quantity',
      key: 'spq_quantity',
      width: 80,
      align: 'right' as const,
      render: (value: any) => {
        const numValue = typeof value === 'string' ? parseFloat(value) : value;
        if (typeof numValue === 'number' && !isNaN(numValue)) {
          return <span style={{ fontSize: '12px' }}>{numValue.toFixed(3)}</span>;
        }
        return <span style={{ fontSize: '12px' }}>0.000</span>;
      }
    },
    {
      title: 'Unit',
      dataIndex: 'spq_unit',
      key: 'spq_unit',
      width: 60,
      render: (value: any) => <span style={{ fontSize: '12px' }}>{value}</span>
    },
    {
      title: 'Count',
      dataIndex: 'spq_count',
      key: 'spq_count',
      width: 80,
      align: 'right' as const,
      render: (value: any) => <span style={{ fontSize: '12px' }}>{value}</span>
    },
    {
      title: 'Currency',
      key: 'currency',
      width: 80,
      align: 'center' as const,
      render: (_: any, record: PurchaseRequestItem) => {
        if (record.exchange_rate_info && record.exchange_rate_info.currency_code !== 'USD') {
          const currencyCode = record.exchange_rate_info.currency_code;
          const rateType = record.exchange_rate_info.rate_type;
          const isHistorical = rateType === 'historical';
          
          return (
            <div style={{ textAlign: 'center' }}>
              <div style={{ 
                fontSize: '11px', 
                fontWeight: 'bold',
                color: isHistorical ? '#faad14' : '#52c41a'
              }}>
                {currencyCode}
              </div>
              {isHistorical && (
                <div style={{ 
                  fontSize: '9px', 
                  color: '#faad14'
                }}>
                  历史
                </div>
              )}
            </div>
          );
        }
        return (
          <div style={{ 
            fontSize: '11px', 
            fontWeight: 'bold',
            color: '#52c41a'
          }}>
            USD
          </div>
        );
      }
    },
    {
      title: 'Unit Price',
      key: 'original_price',
      width: 100,
      align: 'right' as const,
      render: (_: any, record: PurchaseRequestItem) => {
        // 现在后端总是提供exchange_rate_info，包含供应商的原始价格
        if (record.exchange_rate_info) {
          const originalPrice = record.exchange_rate_info.original_unit_price;
          return (
            <div style={{ textAlign: 'right' }}>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {originalPrice.toFixed(4)}
              </div>
            </div>
          );
        }
        // 如果没有exchange_rate_info，显示默认值
        return <span style={{ fontSize: '12px', color: '#999' }}>-</span>;
      }
    },
    {
      title: 'Unit Price(USD)',
      key: 'usd_price',
      width: 100,
      align: 'right' as const,
      render: (_: any, record: PurchaseRequestItem) => {
        if (record.exchange_rate_info && record.exchange_rate_info.currency_code !== 'USD') {
          const usdPrice = record.exchange_rate_info.usd_unit_price;
          return (
            <div style={{ textAlign: 'right' }}>
              <div style={{ fontSize: '12px', fontWeight: 'bold', color: '#52c41a' }}>
                ${usdPrice.toFixed(2)}
              </div>
            </div>
          );
        } else {
          const numValue = typeof record.estimated_unit_price === 'string' ? 
            parseFloat(record.estimated_unit_price) : record.estimated_unit_price;
          if (typeof numValue === 'number' && !isNaN(numValue)) {
            return (
              <div style={{ textAlign: 'right' }}>
                <div style={{ fontSize: '12px', fontWeight: 'bold', color: '#52c41a' }}>
                  ${numValue.toFixed(2)}
                </div>
              </div>
            );
          }
        }
        return <span style={{ fontSize: '12px', color: '#999' }}>-</span>;
      }
    },
    {
      title: 'Total Price(USD)',
      key: 'estimated_total_price_usd',
      width: 140,
      align: 'right' as const,
      render: (_: any, record: PurchaseRequestItem) => {
        if (record.exchange_rate_info && record.exchange_rate_info.currency_code !== 'USD') {
          const usdTotalPrice = record.exchange_rate_info.usd_total_price;
          return (
            <div style={{ textAlign: 'right' }}>
              <div style={{ fontSize: '12px', fontWeight: 'bold', color: '#52c41a' }}>
                ${usdTotalPrice.toFixed(2)}
              </div>
            </div>
          );
        } else {
          const numValue = typeof record.estimated_total_price === 'string' ? 
            parseFloat(record.estimated_total_price) : record.estimated_total_price;
          if (typeof numValue === 'number' && !isNaN(numValue)) {
            return (
              <div style={{ textAlign: 'right' }}>
                <div style={{ fontSize: '12px', fontWeight: 'bold', color: '#52c41a' }}>
                  ${numValue.toFixed(2)}
                </div>
              </div>
            );
          }
        }
        return <span style={{ fontSize: '12px', color: '#999' }}>-</span>;
      }
    },
    {
      title: 'Supplier',
      key: 'supplier',
      width: 120,
      render: (_: any, record: PurchaseRequestItem) => {
        if (record.preferred_supplier?.supplier_id) {
          return (
            <Button
              type="link"
              style={{ padding: 0, height: 'auto', fontSize: '12px' }}
              onClick={() => navigate(`/admin/suppliers/${record.preferred_supplier!.supplier_id}/items/${record.item_id}`)}
              icon={<LinkOutlined />}
            >
              {record.preferred_supplier.supplier_name}
            </Button>
          );
        }
        return <span style={{ color: '#999', fontSize: '12px' }}>{t('purchase.noSupplier')}</span>;
      }
    }
  ];

  return (
    <>
      <style>
        {`
          .compact-table .ant-table-thead > tr > th {
            padding: 8px 4px !important;
            font-size: 11px !important;
            font-weight: 600 !important;
          }
          .compact-table .ant-table-tbody > tr > td {
            padding: 6px 4px !important;
            font-size: 12px !important;
          }
          .compact-table .ant-table-tbody > tr:hover > td {
            background-color: #f5f5f5 !important;
          }
        `}
      </style>
      <Card 
        title={
          <Space>
            <ShoppingCartOutlined />
            {t('purchase.requestItemDetails', { count: items?.length || 0 })}
          </Space>
        }
        style={{ marginBottom: '24px' }}
      >
      {items && items.length > 0 ? (
        <Table
          columns={columns}
          dataSource={items}
          rowKey="id"
          pagination={false}
          size="small"
          scroll={{ x: 1000 }}
          style={{ fontSize: '12px' }}
          className="compact-table"
        />
      ) : (
        <Empty description={t('purchase.noItemDetails')} />
      )}
      </Card>
    </>
  );
};

export default RequestItemsTable;
