import React from 'react';
import { useTranslation } from 'react-i18next';
import { Steps } from 'antd';
import { PurchaseRequest } from '@admin/services/purchaseRequestService';

const { Step } = Steps;

interface RequestStatusStepsProps {
  request: PurchaseRequest;
}

interface StepItem {
  title: string;
  status: 'wait' | 'process' | 'finish' | 'error';
  level: string;
}

const RequestStatusSteps: React.FC<RequestStatusStepsProps> = ({ request }) => {
  const { t } = useTranslation();
  // 获取状态步骤
  const getStatusStep = (status: string) => {
    const statusMap = {
      'draft': 0,
      'pending_submission': 0,
      'under_review': 1,           // 第一步：部门经理复核
      'under_principle_approval': 2, // 第二步：物品管理员原则审批
      'under_final_approval': 3,    // 第三步：公司主管最终审批
      'approved': 4,                // 第四步：采购执行
      'rejected': -1,               // 被拒绝，不显示当前步骤
      'returned': -1,               // 被退回，不显示当前步骤
      'withdrawn': 0,               // 已撤回，回到第一步
      'executed': 4                 // 已执行，显示在第四步
    };
    return statusMap[status as keyof typeof statusMap] || 0;
  };

  // 获取步骤状态
  const getStepStatus = (request: PurchaseRequest): StepItem[] => {
    const steps: StepItem[] = [
      { title: t('purchase.departmentManagerReview'), status: 'wait', level: 'review' },
      { title: t('purchase.itemManagerPrincipleApproval'), status: 'wait', level: 'principle_approval' },
      { title: t('purchase.companyManagerApproval'), status: 'wait', level: 'final_approval' },
      { title: t('purchase.purchaseExecution'), status: 'wait', level: 'execution' }
    ];

    // 根据当前状态直接设置步骤状态，这是最可靠的方法
    switch (request.status) {
      case 'draft':
      case 'pending_submission':
        // 草稿或待提交状态，所有步骤都是等待中
        steps.forEach(step => {
          step.status = 'wait';
        });
        break;
        
      case 'under_review':
        // 部门经理复核中
        steps[0].status = 'process';  // 当前进行中
        steps[1].status = 'wait';     // 等待中
        steps[2].status = 'wait';     // 等待中
        steps[3].status = 'wait';     // 等待中
        break;
        
      case 'under_principle_approval':
        // 物品管理员原则审批中
        steps[0].status = 'finish';   // 已完成
        steps[1].status = 'process';  // 当前进行中
        steps[2].status = 'wait';     // 等待中
        steps[3].status = 'wait';     // 等待中
        break;
        
      case 'under_final_approval':
        // 公司主管最终审批中
        steps[0].status = 'finish';   // 已完成
        steps[1].status = 'finish';   // 已完成
        steps[2].status = 'process';  // 当前进行中
        steps[3].status = 'wait';     // 等待中
        break;
        
      case 'approved':
        // 已批准
        steps[0].status = 'finish';   // 已完成
        steps[1].status = 'finish';   // 已完成
        steps[2].status = 'finish';   // 已完成
        steps[3].status = 'wait';     // 等待执行
        break;
        
      case 'executed':
        // 已执行
        steps[0].status = 'finish';   // 已完成
        steps[1].status = 'finish';   // 已完成
        steps[2].status = 'finish';   // 已完成
        steps[3].status = 'finish';   // 已完成
        break;
        
      case 'rejected':
        // 被拒绝，根据流转历史确定哪些步骤已完成
        if (request.flow_history && request.flow_history.length > 0) {
          // 检查是否有原则审批记录
          const hasPrincipleApproval = request.flow_history.some(record => 
            record.approval_level === 'principle_approval' && record.action === 'approve'
          );
          
          // 检查是否有最终审批记录
          const hasFinalApproval = request.flow_history.some(record => 
            record.approval_level === 'final_approval' && record.action === 'approve'
          );
          
          steps[0].status = 'finish';  // 部门经理复核通常已完成
          steps[1].status = hasPrincipleApproval ? 'finish' : 'error';
          steps[2].status = hasFinalApproval ? 'finish' : 'wait';
          steps[3].status = 'wait';
        } else {
          // 没有流转历史，假设只完成了第一步
          steps[0].status = 'error';
          steps[1].status = 'wait';
          steps[2].status = 'wait';
          steps[3].status = 'wait';
        }
        break;
        
      case 'returned':
        // 被退回，类似被拒绝的逻辑
        if (request.flow_history && request.flow_history.length > 0) {
          const hasPrincipleApproval = request.flow_history.some(record => 
            record.approval_level === 'principle_approval' && record.action === 'approve'
          );
          
          const hasFinalApproval = request.flow_history.some(record => 
            record.approval_level === 'final_approval' && record.action === 'approve'
          );
          
          steps[0].status = 'finish';
          steps[1].status = hasPrincipleApproval ? 'finish' : 'error';
          steps[2].status = hasFinalApproval ? 'finish' : 'wait';
          steps[3].status = 'wait';
        } else {
          steps[0].status = 'error';
          steps[1].status = 'wait';
          steps[2].status = 'wait';
          steps[3].status = 'wait';
        }
        break;
        
      case 'withdrawn':
        // 已撤回，重置为待提交状态
        steps.forEach(step => {
          step.status = 'wait';
        });
        break;
        
      default:
        // 其他状态，保持等待中
        steps.forEach(step => {
          step.status = 'wait';
        });
        break;
    }

    return steps;
  };

  const steps = getStepStatus(request);
  const currentStep = getStatusStep(request.status);

  return (
    <Steps 
      current={currentStep >= 0 ? currentStep : undefined} 
      direction="vertical" 
      size="small"
      status={request.status === 'rejected' || request.status === 'returned' ? 'error' : 'process'}
    >
      {steps.map((step, index) => (
        <Step 
          key={index} 
          title={step.title} 
          status={step.status}
          description={
            step.status === 'process' ? t('purchase.processing') :
            step.status === 'finish' ? t('purchase.approved') :
            step.status === 'error' ? t('purchase.error') : t('purchase.waiting')
          }
        />
      ))}
    </Steps>
  );
};

export default RequestStatusSteps;
