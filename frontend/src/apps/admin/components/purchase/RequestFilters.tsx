import React from 'react';
import { useTranslation } from 'react-i18next';
import { Row, Col, Input, Select, DatePicker, Space, Card, Button } from 'antd';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { SearchOutlined } from '@ant-design/icons';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface RequestFiltersProps {
  filters: {
    searchText: string;
    statusFilter: string;
    departmentFilter: string;
    levelFilter?: string;
    dateRange: any;
  };
  onFiltersChange: (filters: any) => void;
  departments: string[];
  statuses: Array<{ value: string; label: string }>;
  approvalLevels?: Array<{ value: string; label: string }>;
  showApprovalLevel?: boolean;
}

const RequestFilters: React.FC<RequestFiltersProps> = ({
  filters,
  onFiltersChange,
  departments,
  statuses,
  approvalLevels = [],
  showApprovalLevel = false
}) => {
  const { t } = useTranslation();
  const handleFilterChange = (key: string, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const handleSearch = (value: string) => {
    handleFilterChange('searchText', value);
  };

  const handleStatusChange = (value: string) => {
    handleFilterChange('statusFilter', value);
  };

  const handleDepartmentChange = (value: string) => {
    handleFilterChange('departmentFilter', value);
  };

  const handleLevelChange = (value: string) => {
    handleFilterChange('levelFilter', value);
  };

  const handleDateRangeChange = (dates: any) => {
    handleFilterChange('dateRange', dates);
  };

  const clearFilters = () => {
    onFiltersChange({
      searchText: '',
      statusFilter: '',
      departmentFilter: '',
      levelFilter: '',
      dateRange: null
    });
  };

  return (
    <Card size="small" style={{ marginBottom: '16px' }}>
      <Row gutter={16} align="middle">
        <Col span={6}>
          <Search
            placeholder={t('purchase.searchPlaceholder')}
            value={filters.searchText}
            onChange={(e) => handleFilterChange('searchText', e.target.value)}
            onSearch={handleSearch}
            prefix={<SearchOutlined />}
            allowClear
            style={{ width: '100%' }}
          />
        </Col>
        
        <Col span={4}>
          <Select
            placeholder={t('purchase.selectStatus')}
            value={filters.statusFilter}
            onChange={handleStatusChange}
            style={{ width: '100%' }}
            allowClear
          >
            {statuses.map(status => (
              <Option key={status.value} value={status.value}>
                {status.label}
              </Option>
            ))}
          </Select>
        </Col>
        
        <Col span={4}>
          <Select
            placeholder={t('purchase.selectDepartment')}
            value={filters.departmentFilter}
            onChange={handleDepartmentChange}
            style={{ width: '100%' }}
            allowClear
          >
            {departments.map(dept => (
              <Option key={dept} value={dept}>
                {dept}
              </Option>
            ))}
          </Select>
        </Col>
        
        {showApprovalLevel && (
          <Col span={4}>
            <Select
              placeholder={t('purchase.selectApprovalLevel')}
              value={filters.levelFilter}
              onChange={handleLevelChange}
              style={{ width: '100%' }}
              allowClear
            >
              {approvalLevels.map(level => (
                <Option key={level.value} value={level.value}>
                  {level.label}
                </Option>
              ))}
            </Select>
          </Col>
        )}
        
        <Col span={4}>
          <RangePicker
            placeholder={[t('purchase.startDate'), t('purchase.endDate')]}
            value={filters.dateRange}
            onChange={handleDateRangeChange}
            style={{ width: '100%' }}
            format={DATE_FORMATS.DATE_ONLY}
          />
        </Col>
        
        <Col span={2}>
          <Button onClick={clearFilters} size="small">
            {t('purchase.clear')}
          </Button>
        </Col>
      </Row>
    </Card>
  );
};

export default RequestFilters;
