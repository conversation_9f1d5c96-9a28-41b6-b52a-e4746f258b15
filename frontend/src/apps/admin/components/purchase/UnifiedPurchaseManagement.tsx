import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Card, Row, Col, Statistic, Button } from 'antd';
import { 
  FileTextOutlined, 
  ClockCircleOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  UserOutlined,
  EditOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { useSearchParams } from 'react-router-dom';
import { PurchaseRequest } from '@admin/services/purchaseRequestService';
import PurchaseRequestTable from './PurchaseRequestTable';
import { useAuth } from '../../contexts/AuthContext';
import { usePurchaseRequests } from '@admin/hooks/usePurchaseRequests';
import { purchaseExecutionService } from '@admin/services/purchaseExecutionService';
import { message } from 'antd';

const { Title } = Typography;

interface UnifiedPurchaseManagementProps {
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canApprove: boolean;
  canExecute?: boolean;
  onNavigateToExecution?: () => void;
}

const UnifiedPurchaseManagement: React.FC<UnifiedPurchaseManagementProps> = ({
  canCreate,
  canEdit,
  canDelete,
  canApprove,
  canExecute,
  onNavigateToExecution
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { requests, loading, deleteRequest, approveRequest, updateRequest, withdrawToPending, submitRequest, refreshRequests } = usePurchaseRequests();
  const [searchParams, setSearchParams] = useSearchParams();

  // 执行采购申请
  const handleExecute = async (requestId: number) => {
    try {
      // 调用采购执行服务
      await purchaseExecutionService.executeSingleRequest(requestId);
      message.success(t('purchase.executionSuccess'));
      // 刷新数据
      refreshRequests(); // 刷新采购申请列表
    } catch (error) {
      message.error(t('purchase.executionFailed'));
      console.error('执行失败:', error);
    }
  };

  // 批量执行采购申请
  const handleBatchExecute = async (requestIds: number[]) => {
    try {
      // 调用采购执行服务的批量执行功能
      await purchaseExecutionService.executeBatch(
        requestIds,
        `批量执行_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}`,
        '批量执行采购申请'
      );
      message.success(t('purchase.batchExecutionSuccess', { count: requestIds.length }));
      // 刷新数据
      refreshRequests(); // 刷新采购申请列表
    } catch (error) {
      message.error(t('purchase.batchExecutionFailed'));
      console.error('批量执行失败:', error);
    }
  };
  
  // 从URL读取当前标签页
  const activeTab = searchParams.get('tab') || 'all';

  // 更新URL参数
  const updateURLParams = useCallback((updates: Record<string, string>) => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      Object.entries(updates).forEach(([key, value]) => {
        if (value) {
          newParams.set(key, value);
        } else {
          newParams.delete(key);
        }
      });
      return newParams;
    });
  }, [setSearchParams]);

  // 设置标签页
  const setActiveTab = useCallback((tab: string) => {
    updateURLParams({ tab });
  }, [updateURLParams]);

  // 根据标签页过滤数据
  const getFilteredRequests = () => {
    let filtered = requests;

    // 根据标签页过滤
    switch (activeTab) {
      case 'my':
        // 我的申请 - 当前用户提交的申请
        filtered = filtered.filter(req => req.submitter_id === user?.id);
        break;
      case 'pending_submission':
        // 待提交 - 当前用户提交的待提交申请
        filtered = filtered.filter(req => 
          req.submitter_id === user?.id && req.status === 'pending_submission'
        );
        break;
      case 'pending':
        // 待处理 - 需要当前用户处理的申请
        filtered = filtered.filter(req => 
          ['under_review', 'under_principle_approval', 'under_final_approval'].includes(req.status)
        );
        break;
      case 'approved':
        // 已批准 - 当前用户能看到的所有已批准申请
        filtered = filtered.filter(req => req.status === 'approved');
        break;
      case 'executed':
        // 已执行 - 当前用户能看到的所有已执行申请
        filtered = filtered.filter(req => req.status === 'executed');
        break;
      case 'rejected':
        // 已拒绝 - 当前用户能看到的所有已拒绝申请
        filtered = filtered.filter(req => req.status === 'rejected');
        break;
      default:
        // 'all' 标签页显示当前用户能看到的所有申请
        break;
    }

    return filtered;
  };

  const filteredRequests = getFilteredRequests();

  // 计算统计数据
  const stats = {
    totalRequests: requests.length,
    pendingSubmission: requests.filter(req => req.status === 'pending_submission').length,
    pendingRequests: requests.filter(req => 
      ['under_review', 'under_principle_approval', 'under_final_approval'].includes(req.status)
    ).length,
    approvedRequests: requests.filter(req => req.status === 'approved').length,
    executedRequests: requests.filter(req => req.status === 'executed').length,
    rejectedRequests: requests.filter(req => req.status === 'rejected').length,
    myRequests: requests.filter(req => req.submitter_id === user?.id).length
  };

  return (
    <div>
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={4} style={{ margin: 0 }}>
          {t('purchase.purchaseRequestManagement')}
        </Title>
        {canExecute && onNavigateToExecution && (
          <Button 
            type="primary" 
            icon={<CalendarOutlined />}
            onClick={onNavigateToExecution}
          >
            {t('purchase.purchaseExecution')}
          </Button>
        )}
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={3}>
          <Card 
            hoverable 
            style={{ 
              cursor: 'pointer',
              border: activeTab === 'all' ? '2px solid #1890ff' : '1px solid #f0f0f0',
              boxShadow: activeTab === 'all' ? '0 4px 12px rgba(24, 144, 255, 0.15)' : 'none',
              transition: 'all 0.3s ease',
              backgroundColor: activeTab === 'all' ? '#f6ffed' : 'white'
            }}
            onClick={() => setActiveTab('all')}
            bodyStyle={{ 
              padding: '20px 24px',
              textAlign: 'center'
            }}
          >
            <Statistic 
              title={
                <span style={{ 
                  color: activeTab === 'all' ? '#1890ff' : '#666',
                  fontWeight: activeTab === 'all' ? '600' : 'normal'
                }}>
                  {t('purchase.allApplications')}
                </span>
              }
              value={stats.totalRequests} 
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={3}>
          <Card 
            hoverable 
            style={{ 
              cursor: 'pointer',
              border: activeTab === 'my' ? '2px solid #52c41a' : '1px solid #f0f0f0',
              boxShadow: activeTab === 'my' ? '0 4px 12px rgba(82, 196, 26, 0.15)' : 'none',
              transition: 'all 0.3s ease',
              backgroundColor: activeTab === 'my' ? '#f6ffed' : 'white'
            }}
            onClick={() => setActiveTab('my')}
            bodyStyle={{ 
              padding: '20px 24px',
              textAlign: 'center'
            }}
          >
            <Statistic 
              title={
                <span style={{ 
                  color: activeTab === 'my' ? '#52c41a' : '#666',
                  fontWeight: activeTab === 'my' ? '600' : 'normal'
                }}>
                  {t('purchase.myRequests')}
                </span>
              }
              value={stats.myRequests} 
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={3}>
          <Card 
            hoverable 
            style={{ 
              cursor: 'pointer',
              border: activeTab === 'pending_submission' ? '2px solid #1890ff' : '1px solid #f0f0f0',
              boxShadow: activeTab === 'pending_submission' ? '0 4px 12px rgba(24, 144, 255, 0.15)' : 'none',
              transition: 'all 0.3s ease',
              backgroundColor: activeTab === 'pending_submission' ? '#f0f8ff' : 'white'
            }}
            onClick={() => setActiveTab('pending_submission')}
            bodyStyle={{ 
              padding: '20px 24px',
              textAlign: 'center'
            }}
          >
            <Statistic 
              title={
                <span style={{ 
                  color: activeTab === 'pending_submission' ? '#1890ff' : '#666',
                  fontWeight: activeTab === 'pending_submission' ? '600' : 'normal'
                }}>
                  {t('purchase.pendingSubmission')}
                </span>
              }
              value={stats.pendingSubmission} 
              prefix={<EditOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={3}>
          <Card 
            hoverable 
            style={{ 
              cursor: 'pointer',
              border: activeTab === 'pending' ? '2px solid #faad14' : '1px solid #f0f0f0',
              boxShadow: activeTab === 'pending' ? '0 4px 12px rgba(250, 173, 20, 0.15)' : 'none',
              transition: 'all 0.3s ease',
              backgroundColor: activeTab === 'pending' ? '#fffbe6' : 'white'
            }}
            onClick={() => setActiveTab('pending')}
            bodyStyle={{ 
              padding: '20px 24px',
              textAlign: 'center'
            }}
          >
            <Statistic 
              title={
                <span style={{ 
                  color: activeTab === 'pending' ? '#faad14' : '#666',
                  fontWeight: activeTab === 'pending' ? '600' : 'normal'
                }}>
                  {t('purchase.pending')}
                </span>
              }
              value={stats.pendingRequests} 
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: stats.pendingRequests > 0 ? '#faad14' : '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={3}>
          <Card 
            hoverable 
            style={{ 
              cursor: 'pointer',
              border: activeTab === 'approved' ? '2px solid #52c41a' : '1px solid #f0f0f0',
              boxShadow: activeTab === 'approved' ? '0 4px 12px rgba(82, 196, 26, 0.15)' : 'none',
              transition: 'all 0.3s ease',
              backgroundColor: activeTab === 'approved' ? '#f6ffed' : 'white'
            }}
            onClick={() => setActiveTab('approved')}
            bodyStyle={{ 
              padding: '20px 24px',
              textAlign: 'center'
            }}
          >
            <Statistic 
              title={
                <span style={{ 
                  color: activeTab === 'approved' ? '#52c41a' : '#666',
                  fontWeight: activeTab === 'approved' ? '600' : 'normal'
                }}>
                  {t('purchase.approved')}
                </span>
              }
              value={stats.approvedRequests} 
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={3}>
          <Card 
            hoverable 
            style={{ 
              cursor: 'pointer',
              border: activeTab === 'executed' ? '2px solid #722ed1' : '1px solid #f0f0f0',
              boxShadow: activeTab === 'executed' ? '0 4px 12px rgba(114, 46, 209, 0.15)' : 'none',
              transition: 'all 0.3s ease',
              backgroundColor: activeTab === 'executed' ? '#f9f0ff' : 'white'
            }}
            onClick={() => setActiveTab('executed')}
            bodyStyle={{ 
              padding: '20px 24px',
              textAlign: 'center'
            }}
          >
            <Statistic 
              title={
                <span style={{ 
                  color: activeTab === 'executed' ? '#722ed1' : '#666',
                  fontWeight: activeTab === 'executed' ? '600' : 'normal'
                }}>
                  {t('purchase.executed')}
                </span>
              }
              value={stats.executedRequests} 
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={3}>
          <Card 
            hoverable 
            style={{ 
              cursor: 'pointer',
              border: activeTab === 'rejected' ? '2px solid #ff4d4f' : '1px solid #f0f0f0',
              boxShadow: activeTab === 'rejected' ? '0 4px 12px rgba(255, 77, 79, 0.15)' : 'none',
              transition: 'all 0.3s ease',
              backgroundColor: activeTab === 'rejected' ? '#fff2f0' : 'white'
            }}
            onClick={() => setActiveTab('rejected')}
            bodyStyle={{ 
              padding: '20px 24px',
              textAlign: 'center'
            }}
          >
            <Statistic 
              title={
                <span style={{ 
                  color: activeTab === 'rejected' ? '#ff4d4f' : '#666',
                  fontWeight: activeTab === 'rejected' ? '600' : 'normal'
                }}>
                  {t('purchase.rejected')}
                </span>
              }
              value={stats.rejectedRequests} 
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 申请列表 */}
      <div style={{ marginTop: '16px' }}>
        <PurchaseRequestTable
          requests={filteredRequests}
          loading={loading}
          canEdit={activeTab === 'my' || activeTab === 'pending_submission' || activeTab === 'rejected' || activeTab === 'all' ? canEdit : false}
          canDelete={canDelete}
          canApprove={activeTab === 'pending' || activeTab === 'all' ? canApprove : false}
          canExecute={canExecute}
          showApprovalActions={activeTab === 'pending' || activeTab === 'all'}
          showSummaryButton={activeTab === 'approved' || activeTab === 'pending' || activeTab === 'all'}
          showBatchExecuteButton={activeTab === 'approved'}
          showBatchApprovalButton={activeTab === 'pending'} // 只在pending标签页显示批量审批按钮
          onDelete={deleteRequest}
          onApprove={approveRequest}
          updateRequest={updateRequest}
          onWithdrawToPending={withdrawToPending}
          onSubmit={submitRequest}
          onExecute={handleExecute}
          onBatchExecute={handleBatchExecute}
        />
      </div>
    </div>
  );
};

export default UnifiedPurchaseManagement;
