import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  Modal,
  message,
  Typography,
  Alert
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  RollbackOutlined,
  HistoryOutlined,
  PlayCircleOutlined,
  BarChartOutlined,
  CheckOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { PurchaseRequest } from '@admin/services/purchaseRequestService';
import { purchaseRequestService } from '@admin/services/purchaseRequestService';
import ApprovalForm from './ApprovalForm';
import RequestStatusSteps from './RequestStatusSteps';
import BatchApprovalModal from './BatchApprovalModal';
import { useAuth } from '../../contexts/AuthContext';
import { canApproveRequest, getApprovalLevelName } from '../../utils/approvalPermissionUtils';

const { Text } = Typography;

interface PurchaseRequestTableProps {
  requests: PurchaseRequest[];
  loading: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canApprove: boolean;
  canExecute?: boolean;
  showApprovalActions?: boolean;
  showSummaryButton?: boolean; // 新增：是否显示汇总按钮
  showBatchExecuteButton?: boolean; // 新增：是否显示批量执行按钮
  showBatchApprovalButton?: boolean; // 新增：是否显示批量审批按钮
  onDelete?: (id: number, returnToCart?: boolean) => Promise<void>;
  onApprove?: (id: number, action: 'approve' | 'reject' | 'return', comments: string) => Promise<void>;
  onSubmit?: (id: number) => Promise<void>;
  updateRequest?: (id: number, data: any) => Promise<void>;
  onWithdrawToPending?: (id: number) => Promise<void>;
  onExecute?: (id: number) => Promise<void>;
  onBatchExecute?: (requestIds: number[]) => Promise<void>; // 新增：批量执行回调
}

// 采购申请状态配置 - 将在组件内部定义以使用 t 函数

// 优先级配置 - 将在组件内部定义以使用 t 函数

const PurchaseRequestTable: React.FC<PurchaseRequestTableProps> = ({
  requests,
  loading,
  canEdit,
  canDelete,
  canApprove,
  canExecute = false,
  showApprovalActions = false,
  showSummaryButton = false,
  showBatchExecuteButton = false,
  showBatchApprovalButton = false,
  onDelete,
  onApprove,
  updateRequest,
  onWithdrawToPending,
  onSubmit,
  onExecute,
  onBatchExecute
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [currentRequest, setCurrentRequest] = useState<PurchaseRequest | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [batchApprovalModalVisible, setBatchApprovalModalVisible] = useState(false);
  const { user } = useAuth();

  // 采购申请状态配置
  const requestStatusConfig = {
    draft: { color: 'default', text: t('purchase.draft'), icon: <EditOutlined /> },
    pending_submission: { color: 'blue', text: t('purchase.pendingSubmission'), icon: <EditOutlined /> },
    under_review: { color: 'orange', text: t('purchase.underReview'), icon: <CheckCircleOutlined /> },
    under_principle_approval: { color: 'orange', text: t('purchase.underPrincipleApproval'), icon: <CheckCircleOutlined /> },
    under_final_approval: { color: 'orange', text: t('purchase.underFinalApproval'), icon: <CheckCircleOutlined /> },
    approved: { color: 'green', text: t('purchase.approved'), icon: <CheckCircleOutlined /> },
    rejected: { color: 'red', text: t('purchase.rejected'), icon: <CloseCircleOutlined /> },
    executed: { color: 'purple', text: t('purchase.executed'), icon: <CheckCircleOutlined /> }
  };

  // 优先级配置
  const priorityConfig: Record<string, { color: string; text: string }> = {
    normal: { color: 'blue', text: t('purchase.normal') },
    urgent: { color: 'orange', text: t('purchase.urgent') },
    emergency: { color: 'red', text: t('purchase.emergency') }
  };

  // 获取状态步骤
  const getStatusStep = (status: string) => {
    const statusMap = {
      'draft': 0,
      'pending_submission': 1,
      'under_review': 2,
      'under_principle_approval': 2,
      'under_final_approval': 2,
      'approved': 3,
      'rejected': 1,
      'executed': 4
    };
    return statusMap[status as keyof typeof statusMap] || 0;
  };

  // 获取当前审批级别
  const getCurrentApprovalLevel = (status: string) => {
    switch (status) {
      case 'under_review':
      case 'under_principle_approval':
      case 'under_final_approval':
        return 'pending';
      default:
        return '';
    }
  };

  // 获取审批级别显示名称
  const getApprovalLevelName = (level: string) => {
    const levelNames = {
      'pending': t('purchase.pendingApproval')
    };
    return levelNames[level as keyof typeof levelNames] || level;
  };

  // 表格列定义
  const columns: ColumnsType<PurchaseRequest> = [
    {
      title: t('purchase.requestInfo'),
      key: 'request_info',
      width: 200,
      render: (_, record) => (
        <div>
          <div 
            style={{ 
              fontWeight: 'bold', 
              marginBottom: 4,
              color: '#1890ff',
              cursor: 'pointer',
              textDecoration: 'underline',
              textUnderlineOffset: '2px'
            }}
            onClick={() => handleViewDetail(record)}
            onMouseEnter={(e) => {
              e.currentTarget.style.color = '#40a9ff';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = '#1890ff';
            }}
          >
            {record.request_no}
          </div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {t('purchase.applicant')}: {record.submitter_name || record.submitter_id}
          </div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {t('purchase.requestDepartment')}: {record.department_name || record.department_id}
          </div>
          {record.notes && (
            <div style={{ marginTop: 4 }}>
              <strong>{t('purchase.remarks')}:</strong> {record.notes}
            </div>
          )}
        </div>
      ),
    },
    {
      title: t('purchase.applicationDate'),
      dataIndex: 'created_at',
      width: 120,
      render: (date: string, record: PurchaseRequest) => {
        // 优先显示提交日期，如果没有则显示创建日期
        const displayDate = record.submitted_at || date;
        const dateType = record.submitted_at ? t('purchase.submitted') : t('purchase.created');
        
        return (
          <div>
            {dayjs(displayDate).format(DATE_FORMATS.DATE_ONLY)}
          </div>
        );
      },
    },
    {
      title: t('purchase.status'),
      dataIndex: 'status',
      width: 150,
      render: (status: string) => {
        const config = requestStatusConfig[status as keyof typeof requestStatusConfig];
        return (
          <Tag color={config?.color || 'default'} icon={config?.icon}>
            {config?.text || status}
          </Tag>
        );
      },
    },
    {
      title: t('purchase.itemStatistics'),
      key: 'items_info',
      width: 120,
      render: (_, record) => {
        // 计算总金额：优先显示最终金额，如果没有则使用实时阶梯价格计算
        let totalAmount = 0;
        
        if (record.final_total && Number(record.final_total) > 0) {
          // 如果有最终金额，直接使用
          totalAmount = Number(record.final_total);
        } else if (record.items && record.items.length > 0) {
          // 否则使用实时阶梯价格计算
          totalAmount = record.items.reduce((sum, item) => {
            // 优先使用最终价格，如果没有则使用预估价格
            if (item.final_total_price && Number(item.final_total_price) > 0) {
              return sum + Number(item.final_total_price);
            } else if (item.estimated_total_price && Number(item.estimated_total_price) > 0) {
              return sum + Number(item.estimated_total_price);
            }
            return sum;
          }, 0);
        }
        
        return (
          <div>
            <div style={{ fontWeight: 'bold' }}>
              {record.items?.length || 0} {t('purchase.items')}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              ${typeof totalAmount === 'number' && !isNaN(totalAmount) ? totalAmount.toFixed(2) : '0.00'}
            </div>
          </div>
        );
      },
    },
    {
      title: t('purchase.remarks'),
      dataIndex: 'notes',
      width: 200,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <Text ellipsis>{text || '-'}</Text>
        </Tooltip>
      ),
    },
    {
      title: t('purchase.actions'),
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          {/* 编辑按钮 - 只在待提交、已拒绝状态下显示 */}
          {canEdit && ['pending_submission', 'rejected'].includes(record.status) && (
            <Tooltip title={t('purchase.editApplication')}>
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
          )}
          
          {/* 提交按钮 - 只在待提交状态下显示 */}
          {onSubmit && record.status === 'pending_submission' && (
            <Tooltip title={t('purchase.submitApplication')}>
              <Button
                type="text"
                size="small"
                icon={<CheckCircleOutlined />}
                onClick={() => handleSubmit(record)}
              />
            </Tooltip>
          )}
          
          {/* 删除按钮 - 只在草稿、待提交、已拒绝状态下显示 */}
          {canDelete && ['draft', 'pending_submission', 'rejected'].includes(record.status) && (
            <Tooltip title={t('purchase.deleteApplication')}>
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(record)}
              />
            </Tooltip>
          )}
          
          {/* 撤销按钮 - 只在待审批状态下显示，且是当前用户的申请 */}
          {onWithdrawToPending && 
           ['under_review', 'under_principle_approval', 'under_final_approval'].includes(record.status) && 
           record.submitter_id === user?.id && (
            <Tooltip title={t('purchase.withdrawToPending')}>
              <Button
                type="text"
                size="small"
                icon={<RollbackOutlined />}
                onClick={() => handleWithdrawToPending(record)}
              />
            </Tooltip>
          )}
          
          {/* 审批按钮 - 根据用户权限和申请状态决定是否显示 */}
          {showApprovalActions && 
           ['under_review', 'under_principle_approval', 'under_final_approval'].includes(record.status) && 
           canApproveRequest(record.status, user?.role_code, user?.permissions) && (
            <Tooltip title={`${t('purchase.approveApplication')} (${getApprovalLevelName(record.status)})`}>
              <Button
                type="text"
                size="small"
                icon={<CheckCircleOutlined />}
                onClick={() => handleApproval(record)}
              />
            </Tooltip>
          )}
          
          {/* 执行按钮 - 只在已批准状态下显示，且当前用户有执行权限 */}
          {canExecute && record.status === 'approved' && (
            <Tooltip title={t('purchase.executeApplication')}>
              <Button
                type="primary"
                size="small"
                icon={<PlayCircleOutlined />}
                onClick={() => handleExecute(record)}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  const handleViewDetail = (request: PurchaseRequest) => {
    navigate(`/admin/purchase-requests/${request.id}`);
  };

  const handleEdit = (request: PurchaseRequest) => {
    navigate(`/admin/purchase-requests/${request.id}/edit`);
  };

  const handleDelete = (request: PurchaseRequest) => {
    Modal.confirm({
      title: t('purchase.confirmDelete'),
      content: (
        <div>
          <p>{t('purchase.confirmDeleteContent', { requestNo: request.request_no })}</p>
          <div style={{ marginTop: 16 }}>
            <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
              <input
                type="checkbox"
                id="returnToCart"
                style={{ marginRight: 8 }}
                defaultChecked={true}
              />
              <span style={{ color: '#666' }}>
                {t('purchase.confirmDeleteOptions')}
              </span>
            </label>
          </div>
        </div>
      ),
      okText: t('purchase.confirmDelete'),
      cancelText: t('purchase.cancel'),
      onOk: async () => {
        try {
          if (onDelete) {
            const returnToCart = (document.getElementById('returnToCart') as HTMLInputElement)?.checked;
            await onDelete(request.id, returnToCart);
          }
        } catch (error) {
          // 错误已经在onDelete中处理了
        }
      },
    });
  };

  const handleApproval = (request: PurchaseRequest) => {
    setCurrentRequest(request);
    setApprovalModalVisible(true);
  };

  const handleViewHistory = (request: PurchaseRequest) => {
    // setSelectedRequest(request); // This state is removed
    // setDetailModalVisible(true); // This state is removed
    // The detail modal is now handled by a separate page, so this function is no longer needed here.
    // For now, we'll just show a message.
    message.info('详情请点击申请编号查看');
  };

  const handleWithdrawToPending = (request: PurchaseRequest) => {
    Modal.confirm({
      title: t('purchase.confirmWithdraw'),
      content: t('purchase.confirmWithdrawContent', { requestNo: request.request_no }),
      onOk: async () => {
        try {
          if (onWithdrawToPending) {
            await onWithdrawToPending(request.id);
          }
        } catch (error) {
          // 错误已经在onWithdrawToPending中处理了
        }
      },
    });
  };

  const handleSubmit = (request: PurchaseRequest) => {
    Modal.confirm({
      title: t('purchase.confirmSubmit'),
      content: t('purchase.confirmSubmitContent', { requestNo: request.request_no }),
      onOk: async () => {
        try {
          if (onSubmit) {
            await onSubmit(request.id);
          }
        } catch (error) {
          // 错误已经在onSubmit中处理了
        }
      },
    });
  };

  const handleExecute = (request: PurchaseRequest) => {
    Modal.confirm({
      title: t('purchase.confirmExecute'),
      content: t('purchase.confirmExecuteContent', { requestNo: request.request_no }),
      onOk: async () => {
        try {
          if (onExecute) {
            await onExecute(request.id);
          }
        } catch (error) {
          // 错误已经在onExecute中处理了
        }
      },
    });
  };

  // 处理批量选择
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // 处理查看汇总
  const handleViewSummary = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要汇总的申请');
      return;
    }

    // 验证选中的申请状态是否允许汇总
    const selectedRequests = requests.filter(req => selectedRowKeys.includes(req.id));
    const validStatuses = ['approved', 'under_review', 'under_principle_approval', 'under_final_approval'];
    const invalidRequests = selectedRequests.filter(req => !validStatuses.includes(req.status));
    
    if (invalidRequests.length > 0) {
      const invalidNos = invalidRequests.map(req => req.request_no).join(', ');
      message.error(`以下申请状态不允许汇总: ${invalidNos}`);
      return;
    }

    // 跳转到汇总分析页面，通过URL参数传递申请ID列表
    const requestIdsParam = selectedRowKeys.join(',');
    navigate(`/admin/purchase-requests/summary?requestIds=${requestIdsParam}`);
  };

  // 获取可汇总的申请（用于批量选择）
  const getSummaryEligibleRequests = () => {
    const validStatuses = ['approved', 'under_review', 'under_principle_approval', 'under_final_approval'];
    return requests.filter(req => validStatuses.includes(req.status));
  };

  // 获取可审批的申请（用于批量选择）
  const getApprovalEligibleRequests = () => {
    return requests.filter(req => {
      // 根据用户权限和申请状态判断是否可选
      if (canApproveRequest(req.status, user?.role_code, user?.permissions)) {
        return ['under_review', 'under_principle_approval', 'under_final_approval'].includes(req.status);
      }
      return false;
    });
  };

  // 获取可批量执行的申请（只有已批准状态）
  const getBatchExecuteEligibleRequests = () => {
    return requests.filter(req => req.status === 'approved');
  };

  // 处理批量执行
  const handleBatchExecute = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要执行的申请');
      return;
    }

    // 验证选中的申请状态是否允许执行
    const selectedRequests = requests.filter(req => selectedRowKeys.includes(req.id));
    const invalidRequests = selectedRequests.filter(req => req.status !== 'approved');
    
    if (invalidRequests.length > 0) {
      const invalidNos = invalidRequests.map(req => req.request_no).join(', ');
      message.error(`以下申请状态不允许执行: ${invalidNos}`);
      return;
    }

    Modal.confirm({
      title: t('purchase.confirmBatchExecute'),
      content: t('purchase.confirmBatchExecuteContent', { count: selectedRowKeys.length }),
      onOk: async () => {
        try {
          if (onBatchExecute) {
            await onBatchExecute(selectedRowKeys as number[]);
            setSelectedRowKeys([]); // 清除选择
          }
        } catch (error) {
          // 错误已经在onBatchExecute中处理了
        }
      },
    });
  };

  // 处理批量审批
  const handleBatchApproval = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要审批的申请');
      return;
    }

    // 验证选中的申请状态是否允许审批
    const selectedRequests = requests.filter(req => selectedRowKeys.includes(req.id));
    const invalidRequests = selectedRequests.filter(req => 
      !['under_review', 'under_principle_approval', 'under_final_approval'].includes(req.status)
    );
    
    if (invalidRequests.length > 0) {
      const invalidNos = invalidRequests.map(req => req.request_no).join(', ');
      message.error(`以下申请状态不允许审批: ${invalidNos}`);
      return;
    }

    setBatchApprovalModalVisible(true);
  };

  // 批量审批成功后的处理
  const handleBatchApprovalSuccess = () => {
    setBatchApprovalModalVisible(false);
    setSelectedRowKeys([]); // 清除选择
    message.success('批量审批完成');
    // 可以在这里添加刷新数据的逻辑
  };

  // 批量选择配置
  const getRowSelection = () => {
    if (showSummaryButton) {
      return {
        selectedRowKeys,
        onChange: onSelectChange,
        getCheckboxProps: (record: PurchaseRequest) => {
          const validStatuses = ['approved', 'under_review', 'under_principle_approval', 'under_final_approval'];
          return {
            disabled: !validStatuses.includes(record.status),
          };
        },
      };
    } else if (showBatchExecuteButton) {
      return {
        selectedRowKeys,
        onChange: onSelectChange,
        getCheckboxProps: (record: PurchaseRequest) => {
          return {
            disabled: record.status !== 'approved',
          };
        },
      };
    } else if (showApprovalActions) {
      // 审批功能的智能全选：只选中当前用户有权限审批的申请
      return {
        selectedRowKeys,
        onChange: onSelectChange,
        getCheckboxProps: (record: PurchaseRequest) => {
          return {
            disabled: !canApproveRequest(record.status, user?.role_code, user?.permissions),
          };
        },
      };
    }
    return undefined;
  };

  const rowSelection = getRowSelection();

  return (
    <>
      {/* 批量操作功能区域 */}
      {(showSummaryButton || showBatchExecuteButton || showBatchApprovalButton) && (
        <div style={{ marginBottom: '16px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
            <Space>
              {showSummaryButton && (
                <Button
                  type="primary"
                  icon={<BarChartOutlined />}
                  onClick={handleViewSummary}
                  disabled={selectedRowKeys.length === 0}
                >
                  {t('purchase.viewSummaryAnalysis')} {selectedRowKeys.length > 0 ? `(${selectedRowKeys.length})` : ''}
                </Button>
              )}
              {showBatchExecuteButton && (
                <Button
                  type="primary"
                  icon={<CheckCircleOutlined />}
                  onClick={handleBatchExecute}
                  disabled={selectedRowKeys.length === 0}
                  style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                >
                  {t('purchase.batchExecute')} {selectedRowKeys.length > 0 ? `(${selectedRowKeys.length})` : ''}
                </Button>
              )}
              {showBatchApprovalButton && (
                <Button
                  type="primary"
                  icon={<CheckOutlined />}
                  onClick={handleBatchApproval}
                  disabled={selectedRowKeys.length === 0}
                  style={{ backgroundColor: '#1890ff', borderColor: '#1890ff' }}
                >
                  {t('purchase.batchApproval')} {selectedRowKeys.length > 0 ? `(${selectedRowKeys.length})` : ''}
                </Button>
              )}
            </Space>
            
            <Space>
              {selectedRowKeys.length > 0 && (
                <Button 
                  size="small" 
                  onClick={() => setSelectedRowKeys([])}
                >
                  {t('purchase.clearSelection')}
                </Button>
              )}
            </Space>
          </div>          
        </div>
      )}

      <Table
        columns={columns}
        dataSource={requests}
        rowKey="id"
        loading={loading}
        rowSelection={rowSelection}
        pagination={{
          total: requests.length,
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => t('purchase.totalRecords', { total }),
        }}
        scroll={{ x: 1200 }}
        size="middle"
      />

      {/* 申请详情弹窗 */}
      {/* RequestDetailModal is removed as per edit hint */}

      {/* 审批弹窗 */}
      <ApprovalForm
        visible={approvalModalVisible}
        request={currentRequest}
        onCancel={() => {
          setApprovalModalVisible(false);
          setCurrentRequest(null);
        }}
        onSuccess={() => {
          setApprovalModalVisible(false);
          setCurrentRequest(null);
          message.success(t('purchase.approvalSuccess'));
        }}
        onApprove={onApprove || (() => Promise.resolve())}
      />

      {/* 批量审批弹窗 */}
      <BatchApprovalModal
        visible={batchApprovalModalVisible}
        selectedRequests={requests.filter(req => selectedRowKeys.includes(req.id))}
        onCancel={() => setBatchApprovalModalVisible(false)}
        onSuccess={handleBatchApprovalSuccess}
        onApprove={onApprove || (() => Promise.resolve())}
      />

    </>
  );
};

export default PurchaseRequestTable;
