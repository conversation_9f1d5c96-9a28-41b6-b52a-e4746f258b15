import React from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, Form, Select, Input, Card, Typography, Tag, Button } from 'antd';
import { PurchaseRequest } from '@admin/services/purchaseRequestService';
import { canApproveRequest, getApprovalLevelName } from '../../utils/approvalPermissionUtils';
import { useAuth } from '../../contexts/AuthContext';

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;

interface ApprovalFormProps {
  visible: boolean;
  request: PurchaseRequest | null;
  approvalLevel?: string; // 添加审批级别参数
  onCancel: () => void;
  onSuccess: () => void;
  onApprove: (id: number, action: 'approve' | 'reject', comments: string) => Promise<void>;
}

const ApprovalForm: React.FC<ApprovalFormProps> = ({
  visible,
  request,
  approvalLevel,
  onCancel,
  onSuccess,
  onApprove
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [form] = Form.useForm();

  if (!request) return null;

  // 检查用户是否有权限审批此申请
  const hasApprovalPermission = canApproveRequest(request.status, user?.role_code, user?.permissions);
  
  if (!hasApprovalPermission) {
    return (
      <Modal
        title={t('purchase.approvalPermissionDenied')}
        open={visible}
        onCancel={onCancel}
        footer={[
          <Button key="close" onClick={onCancel}>
            {t('common.close')}
          </Button>
        ]}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <p>{t('purchase.noApprovalPermission')}</p>
          <p style={{ color: '#666', fontSize: '14px' }}>
            {t('purchase.currentApprovalLevel')}: {getApprovalLevelName(request.status)}
          </p>
        </div>
      </Modal>
    );
  }

  // 获取当前审批级别
  const getCurrentApprovalLevel = (status: string) => {
    switch (status) {
      case 'under_review':
      case 'under_principle_approval':
      case 'under_final_approval':
        return 'pending';
      default:
        return '';
    }
  };

  // 获取审批级别显示名称
  const getApprovalLevelDisplayName = (level: string) => {
    const levelNames = {
      'pending': t('purchase.pendingApproval')
    };
    return levelNames[level as keyof typeof levelNames] || level;
  };

  // 获取状态显示名称
  const getStatusName = (status: string) => {
    const statusNames = {
      'under_review': t('purchase.departmentManagerReview'),
      'under_principle_approval': t('purchase.itemManagerPrincipleApproval'),
      'under_final_approval': t('purchase.companyManagerApproval')
    };
    return statusNames[status as keyof typeof statusNames] || status;
  };

  // 获取优先级显示名称
  const getPriorityName = (priority: string) => {
    const levelNames = {
      'normal': t('purchase.normal'),
      'urgent': t('purchase.urgent'),
      'emergency': t('purchase.emergency')
    };
    return levelNames[priority as keyof typeof levelNames] || priority;
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    const priorityColors = {
      'normal': 'blue',
      'urgent': 'orange',
      'emergency': 'red'
    };
    return priorityColors[priority as keyof typeof priorityColors] || 'default';
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // 调用实际的审批API
      await onApprove(request.id, values.action, values.comments);
      
      // 审批成功后调用成功回调
      onSuccess();
      form.resetFields();
    } catch (error) {
      console.error('审批失败:', error);
      // 错误已经在onApprove中处理了，这里不需要额外处理
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={`${t('purchase.approveApplication')} - ${request.request_no}`}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          {t('purchase.cancel')}
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          {t('purchase.submitApproval')}
        </Button>
      ]}
      width={600}
      destroyOnClose
    >
      <div>
        {/* 申请信息 */}
        <Card title={t('purchase.requestInfo')} size="small" style={{ marginBottom: 16 }}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
            <div><strong>{t('purchase.requestNumber')}:</strong> {request.request_no}</div>
            <div><strong>{t('purchase.requestDepartment')}:</strong> {request.department_id}</div>
            <div><strong>{t('purchase.applicant')}:</strong> {request.submitter_id}</div>
            <div><strong>{t('purchase.currentStatus')}:</strong> {getStatusName(request.status)}</div>
            <div><strong>{t('purchase.approvalLevel')}:</strong> {approvalLevel ? getApprovalLevelName(approvalLevel) : getApprovalLevelDisplayName(getCurrentApprovalLevel(request.status))}</div>
            {request.notes && (
              <div>
                <strong>{t('purchase.remarks')}:</strong> {request.notes}
              </div>
            )}
          </div>
        </Card>

        {/* 审批表单 */}
        <Form form={form} layout="vertical">
          <Form.Item
            name="action"
            label={t('purchase.approvalDecision')}
            rules={[{ required: true, message: t('purchase.pleaseSelectApprovalDecision') }]}
          >
            <Select placeholder={t('purchase.selectApprovalDecision')}>
              <Option value="approve">{t('purchase.approve')}</Option>
              <Option value="reject">{t('purchase.reject')}</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="comments"
            label={t('purchase.approvalComments')}
            rules={[{ required: false }]}
          >
            <TextArea 
              rows={4} 
              placeholder={t('purchase.enterApprovalComments')}
              maxLength={500}
              showCount
            />
          </Form.Item>
        </Form>

        {/* 审批说明 */}
        <Card size="small" style={{ backgroundColor: '#f6ffed', borderColor: '#b7eb8f' }}>
          <div style={{ fontSize: '12px', color: '#666' }}>
            <strong>{t('purchase.approvalInstructions')}:</strong>
            <ul style={{ margin: '8px 0 0 20px', padding: 0 }}>
              <li>{t('purchase.approveInstruction')}</li>
              <li>{t('purchase.rejectInstruction')}</li>
              <li>{t('purchase.returnInstruction')}</li>
            </ul>
          </div>
        </Card>
      </div>
    </Modal>
  );
};

export default ApprovalForm;
