import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { Card, Row, Col, Descriptions, Typography, Tag, Button, Space, message } from 'antd';
import { 
  EditOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  ExportOutlined,
  QrcodeOutlined
} from '@ant-design/icons';
import { PurchaseRequest } from '@admin/services/purchaseRequestService';
import { getStatusConfig } from '@admin/utils/statusConfig';
import { apiClient } from '@admin/services/authService';
import QRCode from 'react-qr-code';

const { Text } = Typography;

interface RequestBasicInfoProps {
  request: PurchaseRequest;
}

// 根据图标名称获取图标组件
const getIconComponent = (iconName: string) => {
  switch (iconName) {
    case 'EditOutlined':
      return <EditOutlined />;
    case 'CheckCircleOutlined':
      return <CheckCircleOutlined />;
    case 'CloseCircleOutlined':
      return <CloseCircleOutlined />;
    default:
      return <EditOutlined />;
  }
};

const RequestBasicInfo: React.FC<RequestBasicInfoProps> = ({ request }) => {
  const { t } = useTranslation();
  const [qrContent, setQrContent] = useState<string>('');
  const [qrLoading, setQrLoading] = useState(false);

  // 获取二维码内容
  const fetchQRCode = async () => {
    try {
      setQrLoading(true);
      const response = await apiClient.get(`/purchase/requests/${request.id}/qr-code`);
      setQrContent(response.data.qr_content);
    } catch (error) {
      console.error('获取二维码失败:', error);
      message.error(t('purchase.getQRCodeFailed'));
    } finally {
      setQrLoading(false);
    }
  };

  // 导出PDF
  const handleExportPDF = () => {
    const url = `/admin/purchase-requests/${request.id}/export-pdf`;
    window.open(url, '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');
  };

  // 组件加载时获取二维码
  useEffect(() => {
    if (request.id) {
      fetchQRCode();
    }
  }, [request.id]);

  return (
    <Card 
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>{t('purchase.basicInformation')}</span>
          <Button 
            type="primary" 
            icon={<ExportOutlined />}
            onClick={handleExportPDF}
          >
            {t('purchase.exportPDF')}
          </Button>
        </div>
      } 
      style={{ marginBottom: '24px' }}
    >
      <Row gutter={24}>
        <Col span={8}>
          <Descriptions column={1} size="small">
            <Descriptions.Item label={t('purchase.requestNumber')}>
              <Text strong>{request.request_no}</Text>
            </Descriptions.Item>
            <Descriptions.Item label={t('purchase.requestDepartment')}>
              <Text>{request.department_name || request.department_id}</Text>
            </Descriptions.Item>
            <Descriptions.Item label={t('purchase.applicant')}>
              <Text>{request.submitter_name || request.submitter_id}</Text>
            </Descriptions.Item>
          </Descriptions>
        </Col>
        <Col span={8}>
          <Descriptions column={1} size="small">
            <Descriptions.Item label={t('purchase.requestStatus')}>
              <Tag 
                color={getStatusConfig(request.status).color}
                icon={getIconComponent(getStatusConfig(request.status).iconName)}
              >
                {getStatusConfig(request.status).text}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label={t('purchase.createdTime')}>
              {request.created_at ? dayjs(request.created_at).format(DATE_FORMATS.DATE_TIME) : '-'}
            </Descriptions.Item>
            {request.submitted_at && (
              <Descriptions.Item label={t('purchase.submittedTime')}>
                {dayjs(request.submitted_at).format(DATE_FORMATS.DATE_TIME)}
              </Descriptions.Item>
            )}
          </Descriptions>
        </Col>
        <Col span={8}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ marginTop: '8px', padding: '16px', backgroundColor: '#fafafa', borderRadius: '8px' }}>
              {qrLoading ? (
                <div style={{ padding: '20px' }}>{t('purchase.loading')}</div>
              ) : qrContent ? (
                <div>
                  <QRCode 
                    value={qrContent} 
                    size={120} 
                    fgColor="#1f2937" 
                    bgColor="#ffffff"
                    level="M"
                    title={t('purchase.purchaseRequestQRCode')}
                  />
                </div>
              ) : (
                <div style={{ padding: '20px', color: '#999' }}>
                  <QrcodeOutlined style={{ fontSize: '24px' }} />
                  <div>{t('purchase.qrCodeLoadFailed')}</div>
                </div>
              )}
            </div>
          </div>
        </Col>
      </Row>
      
      {/* 备注信息 */}
      {request.notes && (
        <div style={{ marginTop: '16px' }}>
          <Text strong>{t('purchase.remarks')}:</Text>
          <div style={{ marginTop: '8px', padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
            {request.notes}
          </div>
        </div>
      )}
    </Card>
  );
};

export default RequestBasicInfo;
