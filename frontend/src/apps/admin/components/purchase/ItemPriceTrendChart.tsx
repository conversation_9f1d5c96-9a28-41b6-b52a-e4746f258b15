import React, { useState, useEffect, useMemo } from 'react';
import { Card, Spin, message, Typography, Space, Tag, Empty } from 'antd';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { LineChartOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { PurchaseRequestItem } from '@admin/services/purchaseRequestService';
import { priceTrendService, PriceTrendResponse } from '@admin/services/supplierService';
import { getCurrencyInfo } from '@shared/utils/exchangeRateUtils';
import { useTranslation } from 'react-i18next';

const { Text } = Typography;

interface ItemPriceTrendChartProps {
  items: PurchaseRequestItem[];
  title?: string;
}

interface ItemPriceTrendData {
  item: PurchaseRequestItem;
  data: PriceTrendResponse | null;
  loading: boolean;
  error: string | null;
}

// 主组件
const ItemPriceTrendChart: React.FC<ItemPriceTrendChartProps> = ({
  items,
  title
}) => {
  const { t } = useTranslation();
  const defaultTitle = title || t('purchase.itemPriceTrendAnalysis');
  const [itemTrendData, setItemTrendData] = useState<ItemPriceTrendData[]>([]);
  const [globalLoading, setGlobalLoading] = useState(true);

  // 过滤有供应商信息的物品
  const itemsWithSupplier = useMemo(() => 
    items.filter(item => item.preferred_supplier?.id), [items]);

  // 初始化数据
  useEffect(() => {
    const initialData = itemsWithSupplier.map(item => ({
      item,
      data: null,
      loading: true,
      error: null
    }));
    setItemTrendData(initialData);
    setGlobalLoading(itemsWithSupplier.length > 0);
  }, [itemsWithSupplier]);

  // 加载价格趋势数据
  useEffect(() => {
    const loadPriceTrends = async () => {
      if (itemsWithSupplier.length === 0) {
        setGlobalLoading(false);
        return;
      }

      // 设置前后180天的日期范围
      // API需要YYYY-MM-DD格式
      const endDate = dayjs().add(90, 'day').format(DATE_FORMATS.API_DATE);
      const startDate = dayjs().subtract(90, 'day').format(DATE_FORMATS.API_DATE);

      // 为每个物品加载价格数据
      const promises = itemsWithSupplier.map(async (item, index) => {
        try {
          const response = await priceTrendService.getPriceTrend(
            item.preferred_supplier!.id,
            startDate,
            endDate
          );

          // 更新对应物品的数据
          setItemTrendData(prev => prev.map((itemData, i) => 
            i === index ? { ...itemData, data: response, loading: false } : itemData
          ));
        } catch (error: any) {
          console.error(`${t('purchase.failedToLoadItemPriceTrend')} ${item.item_name}:`, error);
          
          // 更新对应物品的错误状态
          setItemTrendData(prev => prev.map((itemData, i) => 
            i === index ? { 
              ...itemData, 
              error: error.response?.data?.detail || t('purchase.loadingPriceDataFailed'),
              loading: false 
            } : itemData
          ));
        }
      });

      await Promise.all(promises);
      setGlobalLoading(false);
    };

    if (itemTrendData.some(item => item.loading)) {
      loadPriceTrends();
    }
  }, [itemTrendData, itemsWithSupplier]);

  // 合并所有物品的价格数据到一个图表
  const mergedChartData = useMemo(() => {
    // 获取所有有效数据的物品
    const validItems = itemTrendData.filter(item => item.data && !item.error);
    
    if (validItems.length === 0) return [];

    // 收集所有日期
    const allDates = new Set<string>();
    validItems.forEach(itemData => {
      if (itemData.data && itemData.data.price_trends) {
        itemData.data.price_trends.forEach(trend => {
          trend.price_points.forEach(point => {
            if (point.is_valid) {
              allDates.add(point.date);
            }
          });
        });
      }
    });

    // 按日期排序
    const sortedDates = Array.from(allDates).sort();

    // 构建合并的图表数据
    return sortedDates.map(date => {
      const dataPoint: any = { date };
      
      validItems.forEach(itemData => {
        const { item, data } = itemData;
        if (data && data.price_trends) {
          // 对于每个物品，我们取其当前采购数量对应的最优价格
          const currentQuantity = (typeof item.spq_quantity === 'string' ? parseFloat(item.spq_quantity) : item.spq_quantity) * item.spq_count;
          
          // 找到适合当前采购数量的阶梯价格
          let bestPrice: number | null = null;
          let bestTierName = '';
          let bestCurrencyCode = 'USD';
          
          data.price_trends.forEach(trend => {
            // 检查当前数量是否在这个阶梯范围内
            const inRange = currentQuantity >= trend.min_quantity && 
                           (trend.max_quantity === null || trend.max_quantity === undefined || currentQuantity <= trend.max_quantity);
            
            if (inRange) {
              const pricePoint = trend.price_points.find(p => p.date === date && p.is_valid);
              if (pricePoint && (bestPrice === null || (pricePoint.usd_total_price || pricePoint.total_price) < bestPrice)) {
                // 优先使用USD价格，如果没有则使用原始价格
                bestPrice = pricePoint.usd_total_price || pricePoint.total_price;
                bestTierName = trend.tier_name;
                bestCurrencyCode = 'USD'; // 现在显示的是USD价格
              }
            }
          });
          
          // 如果没找到合适的阶梯，取最小数量阶梯的价格
          if (bestPrice === null) {
            const minTier = data.price_trends.reduce((min, trend) => 
              trend.min_quantity < min.min_quantity ? trend : min
            );
            const pricePoint = minTier.price_points.find(p => p.date === date && p.is_valid);
            if (pricePoint) {
              // 优先使用USD价格，如果没有则使用原始价格
              bestPrice = pricePoint.usd_total_price || pricePoint.total_price;
              bestTierName = minTier.tier_name;
              bestCurrencyCode = 'USD'; // 现在显示的是USD价格
            }
          }
          
          if (bestPrice !== null) {
            const itemKey = `${item.item_name} (${(typeof item.spq_quantity === 'string' ? parseFloat(item.spq_quantity) : item.spq_quantity) * item.spq_count} ${item.spq_unit})`;
            dataPoint[itemKey] = bestPrice;
            dataPoint[`${itemKey}_tier`] = bestTierName;
            dataPoint[`${itemKey}_supplier`] = data.supplier_name;
            dataPoint[`${itemKey}_currency`] = bestCurrencyCode;
          }
        }
      });
      
      return dataPoint;
    });
  }, [itemTrendData]);

  // 生成图表线条配置
  const chartLines = useMemo(() => {
    const validItems = itemTrendData.filter(item => item.data && !item.error);
    const colors = [
      '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
      '#13c2c2', '#eb2f96', '#fa8c16', '#a0d911', '#2f54eb',
      '#096dd9', '#389e0d', '#d48806', '#cf1322', '#531dab',
      '#08979c', '#c41d7f', '#d46b08', '#7cb305', '#1d39c4'
    ];

    return validItems.map((itemData, index) => {
      const { item } = itemData;
      const itemKey = `${item.item_name} (${(typeof item.spq_quantity === 'string' ? parseFloat(item.spq_quantity) : item.spq_quantity) * item.spq_count} ${item.spq_unit})`;
      
      return {
        key: itemKey,
        color: colors[index % colors.length],
        name: item.item_name
      };
    });
  }, [itemTrendData]);

  // 计算Y轴范围
  const yAxisDomain = useMemo(() => {
    if (!mergedChartData || mergedChartData.length === 0) return [0, 100];

    // 收集所有有效的价格数据
    const allPrices: number[] = [];
    mergedChartData.forEach(dataPoint => {
      chartLines.forEach(line => {
        const price = dataPoint[line.key];
        if (price !== undefined && price > 0) {
          allPrices.push(price);
        }
      });
    });

    if (allPrices.length === 0) return [0, 100];

    const minPrice = Math.min(...allPrices);
    const maxPrice = Math.max(...allPrices);
    const priceRange = maxPrice - minPrice;

    // 如果价格范围很小，则扩大范围
    if (priceRange < minPrice * 0.1) {
      const padding = minPrice * 0.05;
      return [Math.max(0, minPrice - padding), maxPrice + padding];
    }

    // 正常情况：添加10%的边距
    const padding = priceRange * 0.1;
    return [Math.max(0, minPrice - padding), maxPrice + padding];
  }, [mergedChartData, chartLines]);

  // 自定义Tooltip组件
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: 'white',
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          maxWidth: '300px'
        }}>
          <p style={{ margin: '0 0 8px 0', fontWeight: 'bold' }}>
            {dayjs(label).format(DATE_FORMATS.DATE_ONLY)}
          </p>
          {payload.map((entry: any, index: number) => {
            const tierInfo = entry.payload[`${entry.dataKey}_tier`];
            const supplierInfo = entry.payload[`${entry.dataKey}_supplier`];
            const currencyCode = entry.payload[`${entry.dataKey}_currency`] || 'USD';
            const currencyInfo = getCurrencyInfo(currencyCode as any);
            return (
              <div key={index} style={{ 
                margin: '4px 0', 
                fontSize: '12px',
                borderLeft: `3px solid ${entry.color}`,
                paddingLeft: '8px'
              }}>
                <div style={{ fontWeight: 'bold', color: entry.color }}>
                  {entry.dataKey}
                </div>
                <div>{t('purchase.price')}: ${entry.value.toFixed(2)} USD</div>
                {tierInfo && <div style={{ color: '#666' }}>{t('purchase.tier')}: {tierInfo}</div>}
                {supplierInfo && <div style={{ color: '#666' }}>{t('purchase.supplier')}: {supplierInfo}</div>}
              </div>
            );
          })}
        </div>
      );
    }
    return null;
  };

  if (itemsWithSupplier.length === 0) {
    return null; // 如果没有有供应商的物品，不显示组件
  }

  if (globalLoading) {
    return (
      <Card
        title={
          <Space>
            <LineChartOutlined />
            <span>{title}</span>
          </Space>
        }
        style={{ marginBottom: 24 }}
      >
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>{t('purchase.loadingPriceTrendData')}</div>
        </div>
      </Card>
    );
  }

  const validItems = itemTrendData.filter(item => item.data && !item.error);
  const errorItems = itemTrendData.filter(item => item.error);

  if (validItems.length === 0) {
    return (
      <Card
        title={
          <Space>
            <LineChartOutlined />
            <span>{title}</span>
          </Space>
        }
        style={{ marginBottom: 24 }}
      >
        <Empty description={t('purchase.noPriceTrendData')} />
        {errorItems.length > 0 && (
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">{t('purchase.itemsLoadFailed')}</Text>
            {errorItems.map(item => (
              <div key={item.item.id} style={{ color: '#ff4d4f', fontSize: '12px' }}>
                {item.item.item_name}: {item.error}
              </div>
            ))}
          </div>
        )}
      </Card>
    );
  }

  return (
    <Card
      title={
        <Space>
          <LineChartOutlined />
          <span>{title}</span>
        </Space>
      }
      style={{ marginBottom: 24 }}
    >
      <div style={{ marginBottom: 16 }}>
        <Text type="secondary">
          {t('purchase.itemPriceTrendAnalysisDesc')}
        </Text>
        <div style={{ marginTop: 8 }}>
          <Space wrap>
            {validItems.map((itemData, index) => (
              <Tag key={itemData.item.id} color={chartLines[index]?.color}>
                {itemData.item.item_name}: {(typeof itemData.item.spq_quantity === 'string' ? parseFloat(itemData.item.spq_quantity) : itemData.item.spq_quantity) * itemData.item.spq_count} {itemData.item.spq_unit}
              </Tag>
            ))}
          </Space>
        </div>
      </div>
      
      <ResponsiveContainer width="100%" height={400}>
        <LineChart data={mergedChartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="date"
            tickFormatter={(value) => dayjs(value).format(DATE_FORMATS.CHART_LABEL)}
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis
            domain={yAxisDomain}
            tickFormatter={(value) => `${value.toFixed(2)}`}
            label={{ value: 'Price (USD)', angle: -90, position: 'insideLeft' }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          {chartLines.map((line, index) => (
            <Line
              key={line.key}
              type="monotone"
              dataKey={line.key}
              stroke={line.color}
              strokeWidth={2}
              dot={(props: any) => {
                // 突出显示今天的价格点
                // 使用API_DATE格式进行匹配，因为props.payload.date是YYYY-MM-DD格式
                const isToday = props.payload.date === dayjs().format(DATE_FORMATS.API_DATE);
                return (
                  <circle
                    cx={props.cx}
                    cy={props.cy}
                    r={isToday ? 6 : 3}
                    fill={isToday ? '#ff4d4f' : props.fill}
                    stroke={isToday ? '#fff' : 'none'}
                    strokeWidth={isToday ? 2 : 0}
                  />
                );
              }}
              activeDot={{ r: 5 }}
              connectNulls={false}
              name={line.name}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>

      {errorItems.length > 0 && (
        <div style={{ marginTop: 16, padding: 12, backgroundColor: '#fff2f0', borderRadius: 4 }}>
          <Text type="danger" style={{ fontSize: '12px' }}>
            {t('purchase.itemsLoadFailed')}
          </Text>
          {errorItems.map(item => (
            <div key={item.item.id} style={{ color: '#ff4d4f', fontSize: '12px', marginTop: 4 }}>
              • {item.item.item_name}: {item.error}
            </div>
          ))}
        </div>
      )}
    </Card>
  );
};

export default ItemPriceTrendChart;