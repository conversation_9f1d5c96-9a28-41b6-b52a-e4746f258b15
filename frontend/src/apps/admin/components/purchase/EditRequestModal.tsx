import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, DatePicker, Button, Table, Space, message, Typography, Card, Tag, Tooltip } from 'antd';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { PurchaseRequest, PurchaseRequestItem, purchaseRequestService } from '@admin/services/purchaseRequestService';
import { purchaseCartService, CartItem } from '@admin/services/purchaseCartService';
import { useTranslation } from 'react-i18next';

const { Option } = Select;
const { TextArea } = Input;
const { Title } = Typography;

interface EditRequestModalProps {
  visible: boolean;
  request: PurchaseRequest | null;
  onCancel: () => void;
  onSuccess: () => void;
  onUpdate: (id: number, data: any) => Promise<void>;
}

const EditRequestModal: React.FC<EditRequestModalProps> = ({
  visible,
  request,
  onCancel,
  onSuccess,
  onUpdate
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [cartLoading, setCartLoading] = useState(false);
  
  // 临时存储修改后的申请明细
  const [tempRequestItems, setTempRequestItems] = useState<PurchaseRequestItem[]>([]);
  // 记录要添加的物品
  const [itemsToAdd, setItemsToAdd] = useState<CartItem[]>([]);
  // 记录要删除的物品ID
  const [itemsToDelete, setItemsToDelete] = useState<number[]>([]);
  // 记录要更新的物品数量
  const [itemsToUpdate, setItemsToUpdate] = useState<Map<number, number>>(new Map());
  // 记录要返回购物车的物品
  const [itemsToReturnToCart, setItemsToReturnToCart] = useState<PurchaseRequestItem[]>([]);
  // 临时购物车状态，包含原有物品和要返回的物品
  const [tempCartItems, setTempCartItems] = useState<CartItem[]>([]);
  


  useEffect(() => {
    if (visible && request) {
      form.setFieldsValue({
        notes: request.notes
      });
      
      // 初始化临时申请明细
      setTempRequestItems([...request.items]);
      setItemsToAdd([]);
      setItemsToDelete([]);
      setItemsToUpdate(new Map());
      setItemsToReturnToCart([]);
      setTempCartItems([]);
      
      // 如果申请明细为空，尝试获取申请明细
      if (!request.items || request.items.length === 0) {
        fetchRequestItems();
      }
      
      fetchCartItems();
    }
  }, [visible, request, form]);

  const fetchRequestItems = async () => {
    if (!request) return;
    
    try {
      const items = await purchaseRequestService.getRequestItems(request.id);
      setTempRequestItems(items);
    } catch (error) {
      console.error('获取申请明细失败:', error);
      message.error('获取申请明细失败');
    }
  };

  const fetchCartItems = async () => {
    if (!request) return;
    
    setCartLoading(true);
    try {
      const items = await purchaseCartService.getDepartmentCartItems(request.department_id);
      setCartItems(items);
      setTempCartItems(items); // 同时更新临时购物车状态
    } catch (error) {
      console.error('获取购物车数据失败:', error);
      message.error('获取购物车数据失败');
    } finally {
      setCartLoading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      const updateData = {
        business_justification: values.business_justification,
        priority: values.priority,
        expected_delivery: values.expected_delivery?.format(DATE_FORMATS.API_DATE),
        // 包含所有修改的申请明细信息
        items_to_add: itemsToAdd.map(item => ({
          item_id: item.item_id,
          spq_quantity: Number(item.spq_quantity),
          spq_count: item.spq_count,
          spq_unit: item.spq_unit,
          notes: item.notes,
          item_code: item.item_code,
          item_name: item.item_name
        })),
        items_to_delete: itemsToDelete,
        items_to_update: Array.from(itemsToUpdate.entries()).map(([itemId, spq_count]) => ({
          item_id: itemId,
          spq_count: spq_count
        }))
      };

      if (request) {
        await onUpdate(request.id, updateData);
        
        // 将删除的物品添加到购物车
        if (itemsToReturnToCart.length > 0) {
          try {
            for (const item of itemsToReturnToCart) {
              await purchaseCartService.addItemToCart(request.department_id, {
                item_id: item.item_id,
                spq_count: item.spq_count,
                notes: item.notes
              });
            }
            message.success(`申请更新成功，${itemsToReturnToCart.length} piece 物品已返回购物车`);
          } catch (error) {
            console.error('将物品返回购物车失败:', error);
            message.warning('申请更新成功，但部分物品返回购物车失败');
          }
        } else {
          message.success('申请更新成功');
        }
        
        onSuccess();
        form.resetFields();
      }
    } catch (error) {
      console.error('更新申请失败:', error);
      message.error('更新申请失败');
    }
  };

  const handleCancel = () => {
    form.resetFields();
    // 重置所有临时修改
    setTempRequestItems(request ? [...request.items] : []);
    setItemsToAdd([]);
    setItemsToDelete([]);
    setItemsToUpdate(new Map());
    setItemsToReturnToCart([]);
    onCancel();
  };

  const addItemFromCart = (cartItem: CartItem) => {
    if (!request) return;
    
    // 检查是否已经存在于临时申请明细中
    const existingItem = tempRequestItems.find(item => item.item_id === cartItem.item_id);
    
    if (existingItem) {
      // 如果已存在，增加SPQ数量
      const updatedItems = tempRequestItems.map(item => 
        item.item_id === cartItem.item_id 
          ? { ...item, spq_count: Number(item.spq_count) + Number(cartItem.spq_count) }
          : item
      );
      setTempRequestItems(updatedItems);
      
      // 记录数量更新
      const currentQuantity = itemsToUpdate.get(existingItem.id) || Number(existingItem.spq_count);
      itemsToUpdate.set(existingItem.id, currentQuantity + Number(cartItem.spq_count));
      setItemsToUpdate(new Map(itemsToUpdate));
      
      // 从临时购物车中移除已添加的物品
      setTempCartItems(tempCartItems.filter(item => item.id !== cartItem.id));
    } else {
      // 创建新的临时申请明细
      const newRequestItem: PurchaseRequestItem = {
        id: Date.now(), // 临时ID
        request_id: request.id,
        item_id: cartItem.item_id,
        item_code: cartItem.item_code || "",
        item_name: cartItem.item_name || "",
        spq_quantity: Number(cartItem.spq_quantity),
        spq_count: cartItem.spq_count,
        spq_unit: cartItem.spq_unit,
        notes: cartItem.notes
      };
      
      setTempRequestItems([...tempRequestItems, newRequestItem]);
      
      // 记录要添加的物品
      setItemsToAdd([...itemsToAdd, cartItem]);
    }
    
    // 从临时购物车中移除已添加的物品
    setTempCartItems(tempCartItems.filter(item => item.id !== cartItem.id));    
  };

  const removeItem = (itemId: number) => {
    if (!request) return;
    
    // 找到要删除的物品
    const itemToRemove = tempRequestItems.find(item => item.id === itemId);
    if (!itemToRemove) return;
    
    // 从临时申请明细中移除
    const updatedItems = tempRequestItems.filter(item => item.id !== itemId);
    setTempRequestItems(updatedItems);
    
    // 记录要删除的物品
    setItemsToDelete([...itemsToDelete, itemId]);
    
    // 记录要返回购物车的物品
    setItemsToReturnToCart([...itemsToReturnToCart, itemToRemove]);
    
    // 将删除的物品添加到临时购物车中，让用户立即看到
    const cartItemToAdd: CartItem = {
      id: Date.now(), // 临时ID
      item_id: itemToRemove.item_id,
      item_code: itemToRemove.item_code,
      item_name: itemToRemove.item_name,
      spq_quantity: itemToRemove.spq_quantity,
      spq_count: itemToRemove.spq_count,
      spq_unit: itemToRemove.spq_unit,
              notes: itemToRemove.notes,
      created_at: new Date().toISOString()
    };
    
    // 检查购物车中是否已存在相同物品
    const existingCartItem = tempCartItems.find(item => item.item_id === itemToRemove.item_id);
    if (existingCartItem) {
      // 如果已存在，增加SPQ数量
      const updatedTempCart = tempCartItems.map(item => 
        item.item_id === itemToRemove.item_id 
          ? { ...item, spq_count: Number(item.spq_count) + Number(itemToRemove.spq_count) }
          : item
      );
      setTempCartItems(updatedTempCart);
    } else {
      // 如果不存在，添加新物品
      setTempCartItems([...tempCartItems, cartItemToAdd]);
    }
    
    // 从要添加和更新的列表中移除
    setItemsToAdd(itemsToAdd.filter(item => item.item_id !== itemId));
    const newItemsToUpdate = new Map(itemsToUpdate);
    newItemsToUpdate.delete(itemId);
    setItemsToUpdate(newItemsToUpdate);
    
    message.success('物品已从申请明细中移除，并添加到购物车');
  };

  const updateItemQuantity = (itemId: number, spq_count: number) => {
    if (!request) return;
    
    // 更新临时申请明细中的SPQ数量
    const updatedItems = tempRequestItems.map(item => 
      item.id === itemId ? { ...item, spq_count: spq_count } : item
    );
    setTempRequestItems(updatedItems);
    
    // 记录数量更新
    itemsToUpdate.set(itemId, spq_count);
    setItemsToUpdate(new Map(itemsToUpdate));
  };



  if (!request) return null;

  const columns = [
    {
      title: '物品信息',
      key: 'item_info',
      render: (_: any, record: PurchaseRequestItem) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.item_name}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>编码: {record.item_code}</div>
        </div>
      ),
    },
    {
      title: '数量',
      key: 'quantity_info',
      render: (_: any, record: PurchaseRequestItem) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Input
            type="number"
            value={record.spq_count}
            onChange={(e) => updateItemQuantity(record.id, parseInt(e.target.value))}
            style={{ width: 60 }}
          />
          <span style={{ fontSize: '14px', color: '#666' }}>
            × {record.spq_quantity} {record.spq_unit}
          </span>
        </div>
      ),
    },

    {
      title: '操作',
      key: 'action',
      render: (_: any, record: PurchaseRequestItem) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeItem(record.id)}
        >
          删除
        </Button>
      ),
    },
  ];

  const cartColumns = [
    {
      title: '物品信息',
      key: 'item_info',
      render: (_: any, record: CartItem) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.item_name || '未命名物品'}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>数量: {record.spq_count} × {typeof record.spq_quantity === 'string' ? parseFloat(record.spq_quantity) : record.spq_quantity} {record.spq_unit}</div>
        </div>
      ),
    },
    {
      title: t('purchase.operation'),
      key: 'action',
      render: (_: any, record: CartItem) => (
        <Button
          type="primary"
          size="small"
          icon={<PlusOutlined />}
          onClick={() => addItemFromCart(record)}
        >
          {t('purchase.addToRequest')}
        </Button>
      ),
    },
  ];

  return (
    <Modal
      title={`${t('purchase.editRequest')} - ${request.request_no}`}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          {t('purchase.cancel')}
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          {t('purchase.saveChanges')}
        </Button>,
      ]}
      width={1200}
      destroyOnClose
    >
      <div style={{ display: 'flex', gap: '16px' }}>
        {/* 左侧：申请信息编辑 */}
        <div style={{ flex: 1 }}>
          <Card title={t('purchase.applicationInfo')} size="small" style={{ marginBottom: 16 }}>
            <Form form={form} layout="vertical">
              <Form.Item
                name="business_justification"
                label={t('purchase.businessJustification')}
                rules={[{ required: true, message: t('purchase.pleaseEnterBusinessJustification') }]}
              >
                <TextArea rows={4} placeholder={t('purchase.enterBusinessJustification')} />
              </Form.Item>

              <Form.Item
                name="priority"
                label={t('purchase.priority')}
                rules={[{ required: true, message: t('purchase.pleaseSelectPriority') }]}
              >
                <Select placeholder={t('purchase.selectPriority')}>
                  <Option value="normal">{t('purchase.normal')}</Option>
                  <Option value="urgent">{t('purchase.urgent')}</Option>
                  <Option value="emergency">{t('purchase.emergency')}</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="expected_delivery"
                label={t('purchase.expectedDelivery')}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Form>
          </Card>

          {/* 申请明细 */}
          <Card 
            title={`${t('purchase.applicationDetails')} (${tempRequestItems.length} ${t('purchase.items')})`}
            size="small"
          >
            <Table
              columns={columns}
              dataSource={tempRequestItems}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </div>

        {/* 右侧：购物车物品 */}
        <div style={{ flex: 1 }}>
          <Card title={t('purchase.shoppingCartItems')} size="small">
            <Table
              columns={cartColumns}
              dataSource={tempCartItems}
              rowKey="id"
              pagination={false}
              size="small"
              loading={cartLoading}
            />
          </Card>
        </div>
      </div>
    </Modal>
  );
};

export default EditRequestModal;