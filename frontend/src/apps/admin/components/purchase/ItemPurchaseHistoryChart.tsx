import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Spin, Empty, Typography } from 'antd';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { purchaseRequestService } from '@admin/services/purchaseRequestService';

const { Title } = Typography;

// 预定义的颜色数组
const CHART_COLORS = [
  '#1890ff', '#52c41a', '#faad14', '#f5222d', 
  '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16',
  '#a0d911', '#fa541c', '#2f54eb', '#fa8c16',
  '#fadb14', '#a0d911', '#52c41a', '#13c2c2',
  '#1890ff', '#722ed1', '#eb2f96', '#fa8c16'
];

// 基于字符串生成颜色的函数
const generateColorFromString = (str: string): string => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  const index = Math.abs(hash) % CHART_COLORS.length;
  return CHART_COLORS[index];
};

interface ItemPurchaseHistoryChartProps {
  itemIds: number[];
  itemNames: { [key: number]: string };
  requestIds: number[];
}

interface ChartDataPoint {
  month: string;
  itemName: string;
  count: number;
}

const ItemPurchaseHistoryChart: React.FC<ItemPurchaseHistoryChartProps> = ({
  itemIds,
  itemNames,
  requestIds
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);

  useEffect(() => {
    if (itemIds.length > 0 && requestIds.length > 0) {
      fetchItemPurchaseHistory();
    }
  }, [itemIds, requestIds]);

  const fetchItemPurchaseHistory = async () => {
    try {
      setLoading(true);
      
      // 使用新的汇总分析历史数据接口
      const response = await purchaseRequestService.getSummaryAnalysisHistory(requestIds);

      if (response && response.item_history && response.item_history.length > 0) {
        const processedData: ChartDataPoint[] = [];
        
        // 后端已经返回完整数据，直接转换格式
        response.item_history.forEach((item: any) => {
          const itemName = itemNames[item.item_id] || `${t('purchase.item')}${item.item_id}`;
          processedData.push({
            month: item.month,
            itemName: itemName,
            count: item.count || 0
          });
        });
        
        setChartData(processedData);
      } else {
        setChartData([]);
      }
    } catch (error) {
      console.error(t('purchase.failedToGetItemPurchaseHistory'), error);
      setChartData([]);
    } finally {
      setLoading(false);
    }
  };

  // 为 Recharts 准备数据格式
  const prepareChartData = () => {
    if (!chartData.length) return [];
    
    // 按月份分组数据
    const monthData: { [key: string]: any } = {};
    
    chartData.forEach(item => {
      if (!monthData[item.month]) {
        monthData[item.month] = { month: item.month };
      }
      monthData[item.month][item.itemName] = item.count;
    });
    
    return Object.values(monthData);
  };

  const chartDataForRecharts = prepareChartData();
  
  // 生成颜色映射
  const colorMap = (() => {
    const colors: { [key: string]: string } = {};
    Object.values(itemNames).forEach(itemName => {
      colors[itemName] = generateColorFromString(itemName);
    });
    return colors;
  })();

  if (loading) {
    return (
      <Card title={t('purchase.itemPurchaseHistoryTrend')} style={{ marginBottom: '24px' }}>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px', color: '#666' }}>{t('purchase.loading')}</div>
        </div>
      </Card>
    );
  }

  if (chartData.length === 0) {
    return (
      <Card title={t('purchase.itemPurchaseHistoryTrend')} style={{ marginBottom: '24px' }}>
        <Empty 
          description={
            <div>
              <div>{t('purchase.noPurchaseHistoryData')}</div>
              <div style={{ fontSize: '12px', color: '#999', marginTop: '8px' }}>
                {t('purchase.showPurchaseTrendForLast12Months')}
              </div>
            </div>
          }
        />
      </Card>
    );
  }

  return (
    <Card 
      title={t('purchase.itemPurchaseHistoryTrend')} 
      style={{ marginBottom: 24 }}
      bodyStyle={{ padding: '20px 24px' }}
    >
      <div style={{ marginBottom: 16, height: 300 }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartDataForRecharts}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="month" 
              tickFormatter={(value) => {
                if (!value || !value.includes('-')) return value;
                const [year, month] = value.split('-');
                return `${parseInt(month)}月`;
              }}
              style={{ fontSize: 11 }}
            />
            <YAxis 
              style={{ fontSize: 11 }}
              tickFormatter={(value) => Math.round(value).toString()}
            />
            <Tooltip 
              formatter={(value, name) => [value, name]}
              labelFormatter={(label) => {
                if (!label || !label.includes('-')) return label;
                const [year, month] = label.split('-');
                return `${parseInt(month)}月`;
              }}
            />
            <Legend 
              wrapperStyle={{ paddingBottom: 10 }}
              formatter={(value) => value}
            />
            {Object.values(itemNames).map((itemName, index) => (
              <Line
                key={itemName}
                type="monotone"
                dataKey={itemName}
                stroke={colorMap[itemName]}
                strokeWidth={3}
                dot={{ fill: 'white', strokeWidth: 2, stroke: colorMap[itemName], r: 5 }}
                activeDot={{ r: 6, stroke: colorMap[itemName], strokeWidth: 2, fill: 'white' }}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>
      </div>
      <div style={{ 
        fontSize: 12, 
        color: '#666', 
        textAlign: 'center',
        borderTop: '1px solid #f0f0f0',
        paddingTop: 12
      }}>
        {t('purchase.showPurchaseTrendForLast12Months')}
      </div>
    </Card>
  );
};

export default ItemPurchaseHistoryChart;
