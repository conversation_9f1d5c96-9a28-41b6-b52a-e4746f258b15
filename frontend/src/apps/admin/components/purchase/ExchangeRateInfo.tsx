import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Space, Tag, Tooltip, Button, message } from 'antd';
import { InfoCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { apiClient } from '@admin/services/authService';

interface ExchangeRateInfoProps {
  requestId: number;
}

interface ExchangeRateValidation {
  request_id: number;
  overall_valid: boolean;
  items_validation: Array<{
    currency_code: string;
    is_valid: boolean;
    has_current_month_rate: boolean;
    message: string;
  }>;
  blocking_currencies: string[];
  warnings: string[];
  message?: string;
}

const ExchangeRateInfo: React.FC<ExchangeRateInfoProps> = ({ requestId }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [exchangeRateInfo, setExchangeRateInfo] = useState<ExchangeRateValidation | null>(null);

  useEffect(() => {
    fetchExchangeRateInfo();
  }, [requestId]);

  const fetchExchangeRateInfo = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get(`/purchase/requests/${requestId}/exchange-rate-validation`);
      setExchangeRateInfo(response.data);
    } catch (error) {
      console.error('获取汇率信息失败:', error);
      message.error(t('messages.getExchangeRateInfoFailed'));
    } finally {
      setLoading(false);
    }
  };

  const getCurrencyDisplayName = (currencyCode: string) => {
    const currencyNames: { [key: string]: string } = {
      'USD': '美元',
      'CNY': '人民币',
      'EUR': '欧元',
      'JPY': '日元',
      'GBP': '英镑',
      'KRW': '韩元',
      'SGD': '新加坡元',
      'HKD': '港币'
    };
    return currencyNames[currencyCode] || currencyCode;
  };

  const getStatusColor = (isValid: boolean, hasCurrentMonthRate: boolean) => {
    if (!isValid) return 'red';
    if (!hasCurrentMonthRate) return 'orange';
    return 'green';
  };

  const getStatusText = (isValid: boolean, hasCurrentMonthRate: boolean) => {
    if (!isValid) return t('exchangeRate.invalid');
    if (!hasCurrentMonthRate) return t('exchangeRate.usingHistoricalRate');
    return t('exchangeRate.valid');
  };

  if (!exchangeRateInfo) {
    return null;
  }

  // 如果没有需要汇率转换的物品，不显示组件
  if (exchangeRateInfo.message && exchangeRateInfo.message.includes('没有需要汇率转换的物品')) {
    return null;
  }

  return (
    <Card
      title={
        <Space>
          <InfoCircleOutlined />
          {t('exchangeRate.exchangeRateInfo')}
        </Space>
      }
      extra={
        <Button
          type="text"
          icon={<ReloadOutlined />}
          onClick={fetchExchangeRateInfo}
          loading={loading}
          size="small"
        >
          {t('common.refresh')}
        </Button>
      }
      style={{ marginBottom: '16px' }}
      size="small"
    >
      <div style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* 总体状态 */}
          <div>
            <Tag color={exchangeRateInfo.overall_valid ? 'green' : 'red'}>
              {exchangeRateInfo.overall_valid ? t('exchangeRate.allCurrenciesValid') : t('exchangeRate.someCurrenciesInvalid')}
            </Tag>
          </div>

          {/* 各货币状态 */}
          {exchangeRateInfo.items_validation.map((item, index) => (
            <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Tag color={getStatusColor(item.is_valid, item.has_current_month_rate)}>
                {getCurrencyDisplayName(item.currency_code)}
              </Tag>
              <span style={{ fontSize: '12px', color: '#666' }}>
                {getStatusText(item.is_valid, item.has_current_month_rate)}
              </span>
              {!item.has_current_month_rate && (
                <Tooltip title={item.message}>
                  <InfoCircleOutlined style={{ color: '#faad14', marginLeft: '4px' }} />
                </Tooltip>
              )}
            </div>
          ))}

          {/* 警告信息 */}
          {exchangeRateInfo.warnings.length > 0 && (
            <div style={{ marginTop: '8px' }}>
              {exchangeRateInfo.warnings.map((warning, index) => (
                <div key={index} style={{ fontSize: '12px', color: '#faad14' }}>
                  ⚠️ {warning}
                </div>
              ))}
            </div>
          )}

          {/* 阻止信息 */}
          {exchangeRateInfo.blocking_currencies.length > 0 && (
            <div style={{ marginTop: '8px' }}>
              <div style={{ fontSize: '12px', color: '#ff4d4f', fontWeight: 'bold' }}>
                🚫 {t('exchangeRate.blockingCurrencies')}:
              </div>
              {exchangeRateInfo.blocking_currencies.map((currency, index) => (
                <div key={index} style={{ fontSize: '12px', color: '#ff4d4f', marginLeft: '16px' }}>
                  {getCurrencyDisplayName(currency)}
                </div>
              ))}
            </div>
          )}
        </Space>
      </div>
    </Card>
  );
};

export default ExchangeRateInfo;
