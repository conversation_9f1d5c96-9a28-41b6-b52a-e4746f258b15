import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Space, Typography, Tabs, Empty } from 'antd';
import { ShoppingCartOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { PurchaseRequest } from '@admin/services/purchaseRequestService';
import PurchaseRequestTable from './PurchaseRequestTable';
import RequestFilters from './RequestFilters';


const { Title } = Typography;
const { TabPane } = Tabs;

interface RequestManagementProps {
  requests: PurchaseRequest[];
  loading: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

const RequestManagement: React.FC<RequestManagementProps> = ({
  requests,
  loading,
  canCreate,
  canEdit,
  canDelete
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('all');

  const [filters, setFilters] = useState({
    searchText: '',
    statusFilter: '',
    departmentFilter: '',
    dateRange: null
  });

  // 根据标签页过滤数据
  const getFilteredRequests = () => {
    let filtered = requests;

    // 应用搜索和筛选条件
    if (filters.searchText) {
      filtered = filtered.filter(req => 
        req.request_no.toLowerCase().includes(filters.searchText.toLowerCase()) ||
        req.submitter_id.toString().toLowerCase().includes(filters.searchText.toLowerCase()) ||
        (req.notes && req.notes.toLowerCase().includes(filters.searchText.toLowerCase()))
      );
    }

    if (filters.statusFilter) {
      filtered = filtered.filter(req => req.status === filters.statusFilter);
    }

    if (filters.departmentFilter) {
      filtered = filtered.filter(req => req.department_id.toString() === filters.departmentFilter);
    }

    // 根据标签页进一步过滤
    switch (activeTab) {
      case 'my':
        // 我的申请 - 根据当前用户过滤
        filtered = filtered.filter(req => req.submitter_id === 1); // 这里应该使用实际的用户信息
        break;
      case 'draft':
        filtered = filtered.filter(req => req.status === 'draft');
        break;
      case 'pending':
        filtered = filtered.filter(req => 
          ['under_review', 'under_principle_approval', 'under_final_approval'].includes(req.status)
        );
        break;
      case 'approved':
        filtered = filtered.filter(req => req.status === 'approved');
        break;
      case 'rejected':
        filtered = filtered.filter(req => req.status === 'rejected');
        break;
      default:
        // 'all' 标签页显示所有申请
        break;
    }

    return filtered;
  };

  const filteredRequests = getFilteredRequests();

  // 获取部门列表（用于筛选）
  const getDepartments = () => {
    const departments = Array.from(new Set(requests.map(req => req.department_id.toString())));
    return departments;
  };

  // 获取状态列表（用于筛选）
  const getStatuses = () => {
    const statuses = [
      { value: 'draft', label: t('purchase.draft') },
      { value: 'pending_submission', label: t('purchase.pendingSubmission') },
      { value: 'under_review', label: t('purchase.underReview') },
      { value: 'under_principle_approval', label: t('purchase.underPrincipleApproval') },
      { value: 'under_final_approval', label: t('purchase.underFinalApproval') },
      { value: 'approved', label: t('purchase.approved') },
      { value: 'rejected', label: t('purchase.rejected') }
    ];
    return statuses;
  };

  return (
    <div>
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={4} style={{ margin: 0 }}>
          {t('purchase.requestManagement')}
        </Title>
        {canCreate && (
          <Button 
            type="primary" 
            icon={<ShoppingCartOutlined />} 
            onClick={() => navigate('/admin/purchase-cart')}
          >
            {t('purchase.goToCart')}
          </Button>
        )}
      </div>

      {/* 筛选器 */}
      <RequestFilters
        filters={filters}
        onFiltersChange={setFilters}
        departments={getDepartments()}
        statuses={getStatuses()}
      />

      {/* 标签页 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab} style={{ marginTop: '16px' }}>
        <TabPane tab={t('purchase.allApplications')} key="all">
          <PurchaseRequestTable
            requests={filteredRequests}
            loading={loading}
            canEdit={canEdit}
            canDelete={canDelete}
            canApprove={false} // 申请管理页面不显示审批按钮
          />
        </TabPane>
        
        {canCreate && (
          <TabPane tab={t('purchase.myApplications')} key="my">
            <PurchaseRequestTable
              requests={filteredRequests}
              loading={loading}
              canEdit={canEdit}
              canDelete={canDelete}
              canApprove={false}
            />
          </TabPane>
        )}
        
        <TabPane tab={t('purchase.draftTab')} key="draft">
          <PurchaseRequestTable
            requests={filteredRequests}
            loading={loading}
            canEdit={canEdit}
            canDelete={canDelete}
            canApprove={false}
          />
        </TabPane>
        
        <TabPane tab={t('purchase.pendingTab')} key="pending">
          <PurchaseRequestTable
            requests={filteredRequests}
            loading={loading}
            canEdit={canEdit}
            canDelete={canDelete}
            canApprove={false}
          />
        </TabPane>
        
        <TabPane tab={t('purchase.approvedTab')} key="approved">
          <PurchaseRequestTable
            requests={filteredRequests}
            loading={loading}
            canEdit={false} // 已批准的申请不能编辑
            canDelete={canDelete}
            canApprove={false}
          />
        </TabPane>
        
        <TabPane tab={t('purchase.rejectedReturnedTab')} key="rejected">
          <PurchaseRequestTable
            requests={filteredRequests}
            loading={loading}
            canEdit={canEdit} // 被拒绝的申请可以重新编辑
            canDelete={canDelete}
            canApprove={false}
          />
        </TabPane>
      </Tabs>


    </div>
  );
};

export default RequestManagement;
