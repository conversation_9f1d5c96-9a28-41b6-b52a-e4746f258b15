import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Tag, Space, Typography, Button, Tooltip } from 'antd';
import { WarningOutlined, InfoCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { Decimal } from 'decimal.js';
import { useTranslation } from 'react-i18next';

const { Text, Title } = Typography;

interface RiskCheckResult {
  has_risk: boolean;
  risk_type?: string;
  risk_message?: string;
  item_name?: string;
  item_code?: string;
}

interface RiskAlertProps {
  riskResults: RiskCheckResult[];
  onViewDetails?: () => void;
}

const RiskAlert: React.FC<RiskAlertProps> = ({
  riskResults,
  onViewDetails
}) => {
  const { t } = useTranslation();
  if (!riskResults || riskResults.length === 0) {
    return null;
  }

  const riskItems = riskResults.filter(item => item.has_risk);
  const normalItems = riskResults.filter(item => !item.has_risk);

  const formatQuantity = (quantity: number) => {
    return new Decimal(quantity).toFixed(4).replace(/\.?0+$/, '');
  };

  const getRiskColor = (item: RiskCheckResult) => {
    if (item.risk_type === 'overstock') return 'error';
    if (item.risk_type === 'out_of_stock') return 'warning';
    return 'default';
  };

  const getRiskIcon = (item: RiskCheckResult) => {
    if (item.risk_type === 'overstock') return <ExclamationCircleOutlined />;
    if (item.risk_type === 'out_of_stock') return <WarningOutlined />;
    return <InfoCircleOutlined />;
  };

  return (
    <div style={{ marginBottom: '16px' }}>
      {/* 风险提醒 */}
      {riskItems.length > 0 && (
        <Alert
          message={
            <Space>
              <ExclamationCircleOutlined />
              <span>{t('purchase.shoppingCartRiskAlert')}</span>
              <Tag color="red">{riskItems.length} {t('purchase.items')}</Tag>
            </Space>
          }
          description={
            <div>
              <Text type="danger">
                {t('purchase.itemsExistRisk')}
              </Text>
              <div style={{ marginTop: '8px' }}>
                {riskItems.map((item, index) => (
                  <Card
                    key={index}
                    size="small"
                    style={{ marginBottom: '8px', borderColor: '#ff4d4f' }}
                    bodyStyle={{ padding: '8px 12px' }}
                  >
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Space>
                          <Text strong>{item.item_name || t('purchase.unknownItem')}</Text>
                          <Text code>{item.item_code || 'N/A'}</Text>
                        </Space>
                        <Tag color={getRiskColor(item)}>{item.risk_message || t('purchase.risk')}</Tag>
                      </div>
                      
                      <Text type="danger" style={{ fontSize: '12px' }}>
                        {item.risk_message}
                      </Text>
                    </Space>
                  </Card>
                ))}
              </div>
            </div>
          }
          type="warning"
          showIcon
          style={{ marginBottom: '16px' }}
          action={
            onViewDetails && (
              <Button size="small" type="primary" onClick={onViewDetails}>
                {t('purchase.viewDetails')}
              </Button>
            )
          }
        />
      )}
    </div>
  );
};

export default RiskAlert;
