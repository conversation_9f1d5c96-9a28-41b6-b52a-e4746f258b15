import React, { useState } from 'react';
import { Modal, Form, Input, Table, Tag, Typography, Alert, Space } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { PurchaseRequest } from '@admin/services/purchaseRequestService';

const { TextArea } = Input;
const { Text } = Typography;

interface BatchApprovalModalProps {
  visible: boolean;
  selectedRequests: PurchaseRequest[];
  onCancel: () => void;
  onSuccess: () => void;
  onApprove: (id: number, action: 'approve' | 'reject' | 'return', comments: string) => Promise<void>;
}

interface BatchApprovalFormData {
  action: 'approve' | 'reject';
  comments: string;
}

const BatchApprovalModal: React.FC<BatchApprovalModalProps> = ({
  visible,
  selectedRequests,
  onCancel,
  onSuccess,
  onApprove
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm<BatchApprovalFormData>();
  const [loading, setLoading] = useState(false);
  
  // 审批状态跟踪
  const [approvalStatuses, setApprovalStatuses] = useState<Map<number, 'pending' | 'success' | 'failed'>>(new Map());
  const [approvalResults, setApprovalResults] = useState<{
    successCount: number;
    failureCount: number;
    totalCount: number;
    failedRequests: Array<{request_no: string, reason: string}>;
  } | null>(null);

  // 获取审批级别
  const getApprovalLevel = (status: string) => {
    switch (status) {
      case 'under_review':
        return 'review';
      case 'under_principle_approval':
        return 'principle_approval';
      case 'under_final_approval':
        return 'final_approval';
      default:
        return 'review';
    }
  };

  // 获取审批级别显示名称
  const getApprovalLevelName = (level: string) => {
    switch (level) {
      case 'review':
        return t('purchase.review');
      case 'principle_approval':
        return t('purchase.principleApproval');
      case 'final_approval':
        return t('purchase.finalApproval');
      default:
        return level;
    }
  };

  // 获取状态显示名称
  const getStatusName = (status: string) => {
    const statusMap: Record<string, string> = {
      'under_review': t('purchase.underReview'),
      'under_principle_approval': t('purchase.underPrincipleApproval'),
      'under_final_approval': t('purchase.underFinalApproval')
    };
    return statusMap[status] || status;
  };

  // 检查所有申请是否处于同一审批级别
  const isSameApprovalLevel = () => {
    if (selectedRequests.length === 0) return true;
    const firstLevel = getApprovalLevel(selectedRequests[0].status);
    return selectedRequests.every(req => getApprovalLevel(req.status) === firstLevel);
  };

  // 获取当前审批级别
  const currentApprovalLevel = selectedRequests.length > 0 ? getApprovalLevel(selectedRequests[0].status) : 'review';

  // 表格列定义
  const columns = [
    {
      title: t('purchase.requestNo'),
      dataIndex: 'request_no',
      key: 'request_no',
      width: 150,
    },
    {
      title: t('purchase.status'),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color="orange">{getStatusName(status)}</Tag>
      ),
    },
    {
      title: t('purchase.applicant'),
      dataIndex: 'submitter_name',
      key: 'submitter_name',
      width: 120,
    },
    {
      title: t('purchase.department'),
      dataIndex: 'department_name',
      key: 'department_name',
      width: 120,
    },
    {
      title: t('purchase.items'),
      key: 'items',
      width: 80,
      render: (_: any, record: PurchaseRequest) => record.items?.length || 0,
    },
    {
      title: t('purchase.approvalStatus'),
      key: 'approval_status',
      width: 120,
      render: (_: any, record: PurchaseRequest) => {
        const status = approvalStatuses.get(record.id);
        if (!status) return null;
        
        switch (status) {
          case 'pending':
            return <LoadingOutlined style={{ color: '#1890ff', fontSize: '16px' }} />;
          case 'success':
            return <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} />;
          case 'failed':
            return <CloseCircleOutlined style={{ color: '#ff4d4f', fontSize: '16px' }} />;
          default:
            return null;
        }
      },
    },
  ];

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      // 重置状态
      setApprovalStatuses(new Map());
      setApprovalResults(null);

      const requestIds = selectedRequests.map(req => req.id);
      const comments = values.comments || ''; // 如果为空则使用空字符串

      let successCount = 0;
      let failureCount = 0;
      const failedRequests: Array<{request_no: string, reason: string}> = [];

      // 依次调用单个审批接口
      for (const requestId of requestIds) {
        try {
          // 设置当前请求为处理中状态
          setApprovalStatuses(prev => new Map(prev).set(requestId, 'pending'));
          
          // 使用传入的onApprove方法进行审批
          await onApprove(requestId, values.action, comments);
          
          // 设置成功状态
          setApprovalStatuses(prev => new Map(prev).set(requestId, 'success'));
          successCount++;
        } catch (error) {
          // 设置失败状态
          setApprovalStatuses(prev => new Map(prev).set(requestId, 'failed'));
          failureCount++;
          const request = selectedRequests.find(req => req.id === requestId);
          const requestNo = request?.request_no || `ID:${requestId}`;
          failedRequests.push({
            request_no: requestNo,
            reason: error instanceof Error ? error.message : '审批失败'
          });
        }
      }

      // 设置最终结果
      setApprovalResults({
        successCount,
        failureCount,
        totalCount: requestIds.length,
        failedRequests
      });

      // 所有请求处理完成后，调用onSuccess回调刷新列表
      // 这样可以确保状态一致性
      onSuccess();
    } catch (error) {
      console.error('批量审批失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setApprovalStatuses(new Map());
    setApprovalResults(null);
    onCancel();
  };

  // 处理弹窗关闭
  const handleModalClose = () => {
    form.resetFields();
    setApprovalStatuses(new Map());
    setApprovalResults(null);
  };

  return (
    <Modal
      title={t('purchase.batchApproval')}
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      afterClose={handleModalClose}
      confirmLoading={loading}
      width={800}
      okText={t('purchase.confirm')}
      cancelText={t('purchase.close')}
      okButtonProps={{
        disabled: !isSameApprovalLevel() || selectedRequests.length === 0 || loading
      }}
    >
      <div style={{ marginBottom: 16 }}>
        <Text strong>
          {t('purchase.selectedRequests')}: {selectedRequests.length} {t('purchase.items')}
        </Text>
        {!isSameApprovalLevel() && (
          <div style={{ marginTop: 8 }}>
            <Text type="danger">
              {t('purchase.differentApprovalLevels')}
            </Text>
          </div>
        )}
        

      </div>

      <Table
        columns={columns}
        dataSource={selectedRequests}
        rowKey="id"
        size="small"
        pagination={false}
        scroll={{ y: 200 }}
        style={{ marginBottom: 16 }}
      />

      {/* 审批结果统计 */}
      {approvalResults && (
        <div style={{ marginBottom: 16 }}>
          <Alert
            message={t('purchase.batchApprovalResults')}
            description={
              <div>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>{t('purchase.totalProcessed')}: {approvalResults.totalCount}</Text>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <Text type="success">{t('purchase.successCount')}: {approvalResults.successCount}</Text>
                </div>
                {approvalResults.failureCount > 0 && (
                  <div style={{ marginBottom: 8 }}>
                    <Text type="danger">{t('purchase.failureCount')}: {approvalResults.failureCount}</Text>
                  </div>
                )}
                {approvalResults.failedRequests.length > 0 && (
                  <div style={{ marginTop: 12 }}>
                    <Text strong>{t('purchase.failedRequests')}:</Text>
                    <ul style={{ margin: '8px 0 0 20px', padding: 0 }}>
                      {approvalResults.failedRequests.map((failed, index) => (
                        <li key={index} style={{ color: '#ff4d4f' }}>
                          {failed.request_no}: {failed.reason}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            }
            type={approvalResults.failureCount === 0 ? 'success' : 'warning'}
            showIcon
            style={{ marginBottom: 16 }}
          />
        </div>
      )}

      <Form form={form} layout="vertical">
        <Form.Item
          name="action"
          label={t('purchase.approvalAction')}
          initialValue="approve"
          rules={[{ required: true, message: t('purchase.pleaseSelectAction') }]}
        >
          <div>
            <label style={{ marginRight: 16 }}>
              <input
                type="radio"
                name="action"
                value="approve"
                defaultChecked
                style={{ marginRight: 4 }}
              />
              {t('purchase.approve')}
            </label>
            <label>
              <input
                type="radio"
                name="action"
                value="reject"
                style={{ marginRight: 4 }}
              />
              {t('purchase.reject')}
            </label>
          </div>
        </Form.Item>

        <Form.Item
          name="comments"
          label={t('purchase.approvalComments')}
        >
          <TextArea
            rows={4}
            placeholder={t('purchase.enterApprovalComments')}
          />
        </Form.Item>

        <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f6f6f6', borderRadius: 4 }}>
          <Text type="secondary">
            {t('purchase.batchApprovalInfo', {
              level: getApprovalLevelName(currentApprovalLevel),
              count: selectedRequests.length
            })}
          </Text>
        </div>
      </Form>
    </Modal>
  );
};

export default BatchApprovalModal;
