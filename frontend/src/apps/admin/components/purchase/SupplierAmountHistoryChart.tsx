import React, { useState, useEffect } from 'react';
import { Card, Spin, Empty, Typography } from 'antd';
import { LineChart, Line, XAxis, YA<PERSON>s, CartesianGrid, Toolt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { purchaseRequestService } from '@admin/services/purchaseRequestService';

const { Title } = Typography;

// 预定义的颜色数组
const CHART_COLORS = [
  '#1890ff', '#52c41a', '#faad14', '#f5222d', 
  '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16',
  '#a0d911', '#fa541c', '#2f54eb', '#fa8c16',
  '#fadb14', '#a0d911', '#52c41a', '#13c2c2',
  '#1890ff', '#722ed1', '#eb2f96', '#fa8c16'
];

// 基于字符串生成颜色的函数
const generateColorFromString = (str: string): string => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  const index = Math.abs(hash) % CHART_COLORS.length;
  return CHART_COLORS[index];
};

interface SupplierAmountHistoryChartProps {
  supplierIds: number[];
  supplierNames: { [key: number]: string };
  requestIds: number[];  // 添加requestIds参数
}

interface ChartDataPoint {
  month: string;
  supplierName: string;
  amount: number;
}

const SupplierAmountHistoryChart: React.FC<SupplierAmountHistoryChartProps> = ({
  supplierIds,
  supplierNames,
  requestIds  // 添加requestIds参数
}) => {
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);

  useEffect(() => {
    if (supplierIds.length > 0 && requestIds.length > 0) {  // 添加requestIds检查
      fetchSupplierAmountHistory();
    }
  }, [supplierIds, requestIds]);  // 添加requestIds依赖

  const fetchSupplierAmountHistory = async () => {
    try {
      setLoading(true);
      
      // 使用新的汇总分析历史数据接口
      const response = await purchaseRequestService.getSummaryAnalysisHistory(requestIds);

      if (response && response.supplier_history && response.supplier_history.length > 0) {
        const processedData: ChartDataPoint[] = [];
        
        // 后端已经返回完整数据，直接转换格式
        response.supplier_history.forEach((record: any) => {
          const supplierName = supplierNames[record.supplier_id] || `供应商${record.supplier_id}`;
          processedData.push({
            month: record.month,
            supplierName: supplierName,
            amount: record.amount || 0
          });
        });
        
        setChartData(processedData);
      } else {
        setChartData([]);
      }
    } catch (error) {
      console.error('获取供应商采购金额历史失败:', error);
      setChartData([]);
    } finally {
      setLoading(false);
    }
  };

  // 为 Recharts 准备数据格式
  const prepareChartData = () => {
    if (!chartData.length) return [];
    
    // 按月份分组数据
    const monthData: { [key: string]: any } = {};
    
    chartData.forEach(record => {
      if (!monthData[record.month]) {
        monthData[record.month] = { month: record.month };
      }
      monthData[record.month][record.supplierName] = record.amount;
    });
    
    return Object.values(monthData);
  };

  const chartDataForRecharts = prepareChartData();
  
  // 生成颜色映射
  const colorMap = (() => {
    const colors: { [key: string]: string } = {};
    Object.values(supplierNames).forEach(supplierName => {
      colors[supplierName] = generateColorFromString(supplierName);
    });
    return colors;
  })();

  if (loading) {
    return (
      <Card title="供应商采购金额历史趋势" style={{ marginBottom: '24px' }}>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px', color: '#666' }}>加载中...</div>
        </div>
      </Card>
    );
  }

  if (chartData.length === 0) {
    return (
      <Card title="供应商采购金额历史趋势" style={{ marginBottom: '24px' }}>
        <Empty 
          description={
            <div>
              <div>暂无供应商采购金额历史数据</div>
              <div style={{ fontSize: '12px', color: '#999', marginTop: '8px' }}>
                显示最近13个月各供应商的采购金额趋势
              </div>
            </div>
          }
        />
      </Card>
    );
  }

  return (
    <Card 
      title="供应商采购金额历史趋势（折线图）" 
      style={{ marginBottom: 24 }}
      bodyStyle={{ padding: '20px 24px' }}
    >
      <div style={{ marginBottom: 16, height: 300 }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartDataForRecharts}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="month" 
              tickFormatter={(value) => {
                if (!value || !value.includes('-')) return value;
                const [year, month] = value.split('-');
                return `${parseInt(month)}月`;
              }}
              style={{ fontSize: 11 }}
            />
            <YAxis 
              style={{ fontSize: 11 }}
              tickFormatter={(value) => `$${Math.round(value).toLocaleString()}`}
            />
            <Tooltip 
              formatter={(value, name) => [`$${Number(value).toFixed(2)}`, name]}
              labelFormatter={(label) => {
                if (!label || !label.includes('-')) return label;
                const [year, month] = label.split('-');
                return `${parseInt(month)}月`;
              }}
            />
            <Legend 
              wrapperStyle={{ paddingBottom: 10 }}
              formatter={(value) => value}
            />
            {Object.values(supplierNames).map((supplierName, index) => (
              <Line
                key={supplierName}
                type="monotone"
                dataKey={supplierName}
                stroke={colorMap[supplierName]}
                strokeWidth={3}
                dot={{ fill: 'white', strokeWidth: 2, stroke: colorMap[supplierName], r: 5 }}
                activeDot={{ r: 6, stroke: colorMap[supplierName], strokeWidth: 2, fill: 'white' }}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>
      </div>
      <div style={{ 
        fontSize: 12, 
        color: '#666', 
        textAlign: 'center',
        borderTop: '1px solid #f0f0f0',
        paddingTop: 12
      }}>
        显示最近13个月各供应商的采购金额趋势变化（折线图）
      </div>
    </Card>
  );
};

export default SupplierAmountHistoryChart;
