import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Typography, Space } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { PurchaseRequest } from '@admin/services/purchaseRequestService';

const { Title } = Typography;

interface RequestDetailHeaderProps {
  request: PurchaseRequest;
  onBack: () => void;
  children?: React.ReactNode; // 操作按钮区域
}

const RequestDetailHeader: React.FC<RequestDetailHeaderProps> = ({
  request,
  onBack,
  children
}) => {
  const { t } = useTranslation();
  
  return (
    <div style={{ marginBottom: '24px' }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px', flexWrap: 'wrap' }}>
        <Button icon={<ArrowLeftOutlined />} onClick={onBack}>
          {t('purchase.back')}
        </Button>
        <Title level={3} style={{ margin: 0, flex: 1, textAlign: 'center', minWidth: '300px' }}>
          {t('purchase.purchaseRequestDetails')} - {request.request_no}
        </Title>
        <div style={{ display: 'flex', justifyContent: 'flex-end', flexWrap: 'wrap' }}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default RequestDetailHeader;
