import React from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import {
  Modal,
  Card,
  Typography,
  Tag,
  Row,
  Col,
  Timeline,
  Descriptions,
  Space,
  Button,
  Table,
  Divider
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  RollbackOutlined
} from '@ant-design/icons';
import { PurchaseRequest } from '@admin/services/purchaseRequestService';
import RequestStatusSteps from './RequestStatusSteps';

const { Text, Title } = Typography;

interface RequestDetailModalProps {
  visible: boolean;
  request: PurchaseRequest | null;
  onCancel: () => void;
}

const RequestDetailModal: React.FC<RequestDetailModalProps> = ({
  visible,
  request,
  onCancel
}) => {
  const { t } = useTranslation();
  if (!request) return null;

  // 获取优先级显示名称
  const getPriorityName = (priority: string) => {
    const priorityNames = {
      'normal': t('purchase.normal'),
      'urgent': t('purchase.urgent'),
      'emergency': t('purchase.emergency')
    };
    return priorityNames[priority as keyof typeof priorityNames] || priority;
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    const priorityColors = {
      'normal': 'blue',
      'urgent': 'orange',
      'emergency': 'red'
    };
    return priorityColors[priority as keyof typeof priorityColors] || 'default';
  };

  // 获取审批级别显示名称
  const getApprovalLevelName = (level: string) => {
    const levelNames = {
      'review': t('purchase.departmentManagerReview'),
      'principle_approval': t('purchase.itemManagerPrincipleApproval'),
      'final_approval': t('purchase.companyManagerApproval')
    };
    return levelNames[level as keyof typeof levelNames] || level;
  };

  return (
    <Modal
      title={t('purchase.purchaseRequestDetails')}
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="close" onClick={onCancel}>
          {t('purchase.close')}
        </Button>
      ]}
      width={1000}
      destroyOnClose
    >
      <div>
        {/* 基本信息和审批进度 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Card title={t('purchase.basicInformation')} size="small">
              <p><strong>{t('purchase.requestNumber')}:</strong> {request.request_no}</p>
              <p><strong>{t('purchase.requestDepartment')}:</strong> {request.department_name || request.department_id}</p>
              <p><strong>{t('purchase.applicant')}:</strong> {request.submitter_name || request.submitter_id}</p>
              <p><strong>{t('purchase.applicationDate')}:</strong> {dayjs(request.created_at).format(DATE_FORMATS.DATE_ONLY)}</p>
              <p><strong>{t('purchase.remarks')}:</strong> {request.notes || '-'}</p>
              {request.submitted_at && (
                <p><strong>{t('purchase.submittedTime')}:</strong> {dayjs(request.submitted_at).format(DATE_FORMATS.DATE_ONLY)}</p>
              )}

            </Card>
          </Col>
          <Col span={12}>
            <Card title={t('purchase.approvalProgress')} size="small">
              <RequestStatusSteps request={request} />
            </Card>
          </Col>
        </Row>

        {/* 申请物品 */}
        <Card title={t('purchase.requestItems')} size="small" style={{ marginBottom: 16 }}>
          <Table
            dataSource={request.items || []}
            rowKey="id"
            pagination={false}
            size="small"
            columns={[
              { title: t('purchase.itemName'), dataIndex: 'item_name', width: 150 },
              { title: t('purchase.quantity'), dataIndex: 'spq_count', width: 80,
                render: (spq_count: any, record: any) => {
                  const count = typeof spq_count === 'string' ? parseInt(spq_count) : spq_count;
                  const quantity = typeof record.spq_quantity === 'string' ? parseFloat(record.spq_quantity) : record.spq_quantity;
                  return `${count} × ${quantity} ${record.spq_unit}`;
                } },
              { title: t('purchase.finalUnitPrice'), dataIndex: 'final_unit_price', width: 100,
                render: (price: any) => {
                  const numPrice = typeof price === 'string' ? parseFloat(price) : price;
                  return numPrice && !isNaN(numPrice) ? `$${numPrice.toFixed(2)}` : '-';
                } }
            ]}
          />
          <Divider />
          <div style={{ textAlign: 'right' }}>
            <Text strong style={{ fontSize: '16px' }}>
              {t('purchase.total')}: ${request.final_total ? (typeof request.final_total === 'string' ? parseFloat(request.final_total).toFixed(2) : request.final_total.toFixed(2)) : '0.00'}
            </Text>
          </div>
        </Card>

        {/* 申请说明 */}
        <Card title="申请说明" size="small" style={{ marginBottom: 16 }}>
          <p>{request.notes || '-'}</p>
        </Card>
      </div>
    </Modal>
  );
};

export default RequestDetailModal;
