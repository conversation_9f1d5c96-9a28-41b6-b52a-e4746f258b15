import React, { useEffect, useState } from 'react';
import { Drawer, Form, Input, InputNumber, Button, Space, Row, Col, Switch, Select, Typography, AutoComplete } from 'antd';

const { TextArea } = Input;
const { Text } = Typography;

interface InventoryItem {
  id: number;
  item_id: number;
  item_name: string;
  item_code: string;
  current_quantity?: number;
  min_quantity?: number;
  max_quantity?: number;
  storage_location?: string;
  rack_number?: string;
  notes?: string;
  purchase_unit: string;
  inventory_unit: string;
  qty_per_up: number;
  has_inventory?: boolean; // Added for new logic
  is_active?: boolean; // Added for inventory management status
}

interface InventoryFormsProps {
  // 手工入库相关
  manualInVisible: boolean;
  manualInLoading: boolean;
  selectedItemForManualIn: InventoryItem | null;
  onManualInClose: () => void;
  onManualInSubmit: (values: any) => void;
  
  // 库存调整相关
  adjustVisible: boolean;
  adjustLoading: boolean;
  onAdjustClose: () => void;
  onAdjustSubmit: (values: any) => void;
  
  // 编辑库存设置相关
  editInventoryVisible: boolean;
  editInventoryLoading: boolean;
  onEditInventoryClose: () => void;
  onEditInventorySubmit: (values: any) => void;
}

const InventoryForms: React.FC<InventoryFormsProps> = ({
  manualInVisible,
  manualInLoading,
  selectedItemForManualIn,
  onManualInClose,
  onManualInSubmit,
  adjustVisible,
  adjustLoading,
  onAdjustClose,
  onAdjustSubmit,
  editInventoryVisible,
  editInventoryLoading,
  onEditInventoryClose,
  onEditInventorySubmit,
}) => {
  const [manualInForm] = Form.useForm();
  const [adjustForm] = Form.useForm();
  const [editInventoryForm] = Form.useForm();
  const [isInventoryEnabled, setIsInventoryEnabled] = useState(false);
  const [hasFormChanges, setHasFormChanges] = useState(false);
  const [initialFormValues, setInitialFormValues] = useState<any>(null);
  
  // 调整原因历史记录
  const [adjustmentReasonHistory, setAdjustmentReasonHistory] = useState<string[]>([]);

  // 设置手工入库表单初始值
  useEffect(() => {
    if (manualInVisible && selectedItemForManualIn) {
      const initialValues: any = {
        item_id: selectedItemForManualIn.item_id,
        quantity: undefined,
        remarks: undefined,
        // 当 qty_per_up=1 时默认使用库存单位，否则使用采购单位
        quantity_unit: selectedItemForManualIn.qty_per_up === 1 ? 'inventory' : 'purchase'
      };
      
      manualInForm.setFieldsValue(initialValues);
    }
  }, [manualInVisible, selectedItemForManualIn, manualInForm]);

  // 设置库存调整表单初始值
  useEffect(() => {
    if (adjustVisible && selectedItemForManualIn) {
      const initialValues: any = {
        item_id: selectedItemForManualIn.item_id,
        adjust_quantity: undefined,
        adjust_reason: undefined,
        remarks: undefined,
        // 库存调整默认使用库存单位
        quantity_unit: 'inventory'
      };
      
      adjustForm.setFieldsValue(initialValues);
    }
  }, [adjustVisible, selectedItemForManualIn, adjustForm]);

  // 设置编辑库存设置表单初始值
  useEffect(() => {
    if (editInventoryVisible && selectedItemForManualIn) {
      const isActive = selectedItemForManualIn.has_inventory ? (selectedItemForManualIn.is_active ?? true) : false;
      setIsInventoryEnabled(isActive);
      
      const initialValues = {
        id: selectedItemForManualIn.id,
        item_id: selectedItemForManualIn.item_id,
        min_quantity: selectedItemForManualIn.min_quantity,
        max_quantity: selectedItemForManualIn.max_quantity,
        storage_location: selectedItemForManualIn.storage_location,
        rack_number: selectedItemForManualIn.rack_number,
        notes: selectedItemForManualIn.notes,
        is_active: isActive,
      };
      
      setInitialFormValues(initialValues);
      setHasFormChanges(false);
      
      editInventoryForm.setFieldsValue(initialValues);
    }
  }, [editInventoryVisible, selectedItemForManualIn, editInventoryForm]);

  // 加载调整原因历史记录
  useEffect(() => {
    const savedHistory = localStorage.getItem('adjustmentReasonHistory');
    if (savedHistory) {
      try {
        const history = JSON.parse(savedHistory);
        setAdjustmentReasonHistory(Array.isArray(history) ? history : []);
      } catch (error) {
        console.error('Failed to parse adjustment reason history:', error);
        setAdjustmentReasonHistory([]);
      }
    }
  }, []);

  // 保存调整原因到历史记录
  const saveAdjustmentReason = (reason: string) => {
    if (!reason || reason.trim() === '') return;
    
    const trimmedReason = reason.trim();
    setAdjustmentReasonHistory(prev => {
      // 移除重复项，将新项添加到开头
      const newHistory = [trimmedReason, ...prev.filter(item => item !== trimmedReason)];
      // 只保留最近10个
      const limitedHistory = newHistory.slice(0, 10);
      
      // 保存到 localStorage
      localStorage.setItem('adjustmentReasonHistory', JSON.stringify(limitedHistory));
      
      return limitedHistory;
    });
  };

  // 监听表单字段变化
  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    if (!initialFormValues) return;
    
    // 检查是否有实际变化
    const hasChanges = Object.keys(changedValues).some(key => {
      const currentValue = allValues[key];
      const initialValue = initialFormValues[key];
      
      // 处理null/undefined的情况
      if (currentValue === null || currentValue === undefined) {
        return initialValue !== null && initialValue !== undefined;
      }
      if (initialValue === null || initialValue === undefined) {
        return currentValue !== null && currentValue !== undefined;
      }
      
      return currentValue !== initialValue;
    });
    
    setHasFormChanges(hasChanges);
  };

  return (
    <>
      {/* 手工入库抽屉 */}
      <Drawer
        title={`手工入库 - ${selectedItemForManualIn ? selectedItemForManualIn.item_name : ''}`}
        open={manualInVisible}
        onClose={() => {
          onManualInClose();
          manualInForm.resetFields();
        }}
        width={500}
        maskClosable={false}
        destroyOnClose
        extra={
          <Space>
            <Button onClick={() => {
              onManualInClose();
              manualInForm.resetFields();
            }}>取消</Button>
            <Button 
              type="primary" 
              loading={manualInLoading}
              onClick={() => manualInForm.submit()}
            >
              确认入库
            </Button>
          </Space>
        }
      >
        <Form form={manualInForm} layout="vertical" onFinish={onManualInSubmit}>
          <Form.Item name="item_id" hidden>
            <Input />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={16}>
              <Form.Item name="quantity" label="入库数量" rules={[
                { required: true, message: '请输入入库数量' },
                { 
                  validator: (_, value) => {
                    if (value <= 0) {
                      return Promise.reject(new Error('入库数量必须大于0'));
                    }
                    return Promise.resolve();
                  }
                }
              ]}>
                <InputNumber precision={4} style={{ width: '100%' }} placeholder="请输入大于0的入库数量" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="quantity_unit" label="数量单位" initialValue="purchase">
                <Select disabled={selectedItemForManualIn?.qty_per_up === 1}>
                  <Select.Option value="purchase">{selectedItemForManualIn?.purchase_unit || '采购单位'}</Select.Option>
                  <Select.Option value="inventory">{selectedItemForManualIn?.inventory_unit || '库存单位'}</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          {selectedItemForManualIn && selectedItemForManualIn.qty_per_up !== 1 && (
            <div style={{ marginBottom: '16px', padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
              <Text type="secondary">
                单位换算：每{selectedItemForManualIn.purchase_unit}{selectedItemForManualIn.qty_per_up}{selectedItemForManualIn.inventory_unit}
              </Text>
            </div>
          )}
          
          <Form.Item name="notes" label="备注">
            <TextArea rows={3} placeholder="请输入备注信息（可选）" />
          </Form.Item>
        </Form>
      </Drawer>

      {/* 库存调整抽屉 */}
      <Drawer
        title={`调整 - ${selectedItemForManualIn ? selectedItemForManualIn.item_name : ''}`}
        open={adjustVisible}
        onClose={() => {
          onAdjustClose();
          adjustForm.resetFields();
        }}
        width={500}
        maskClosable={false}
        destroyOnClose
        extra={
          <Space>
            <Button onClick={() => {
              onAdjustClose();
              adjustForm.resetFields();
            }}>取消</Button>
            <Button 
              type="primary" 
              loading={adjustLoading}
              onClick={() => adjustForm.submit()}
            >
              确认调整
            </Button>
          </Space>
        }
      >
        <Form form={adjustForm} layout="vertical" onFinish={(values) => {
          // 保存调整原因到历史记录
          if (values.adjustment_reason) {
            saveAdjustmentReason(values.adjustment_reason);
          }
          // 调用父组件的提交处理
          onAdjustSubmit(values);
        }}>
          <Form.Item name="item_id" hidden>
            <Input />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={16}>
              <Form.Item name="quantity" label="调整数量" rules={[
                { required: true, message: '请输入调整数量' },
                { 
                  validator: (_, value) => {
                    if (value === 0) {
                      return Promise.reject(new Error('调整数量不能为0'));
                    }
                    return Promise.resolve();
                  }
                }
              ]}>
                <InputNumber precision={4} style={{ width: '100%' }} placeholder="可输入正数增加库存，负数减少库存" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="quantity_unit" label="数量单位" initialValue="inventory">
                <Select disabled={selectedItemForManualIn?.qty_per_up === 1}>
                  <Select.Option value="purchase">{selectedItemForManualIn?.purchase_unit || '采购单位'}</Select.Option>
                  <Select.Option value="inventory">{selectedItemForManualIn?.inventory_unit || '库存单位'}</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          {selectedItemForManualIn && selectedItemForManualIn.qty_per_up !== 1 && (
            <div style={{ marginBottom: '16px', padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
              <Text type="secondary">
                单位换算：每{selectedItemForManualIn.purchase_unit}{selectedItemForManualIn.qty_per_up}{selectedItemForManualIn.inventory_unit}
              </Text>
            </div>
          )}
          
          <Form.Item name="adjustment_reason" label="调整原因" rules={[{ required: true, message: '请输入调整原因' }]}>
            <AutoComplete
              placeholder="例如：盘点调整/损耗/质量问题等"
              options={adjustmentReasonHistory.map(reason => ({ value: reason, label: reason }))}
              onSelect={(value: string) => {
                // 当用户选择一个历史记录时，可以在这里做额外处理
                console.log('Selected adjustment reason:', value);
              }}
              onSearch={(value: string) => {
                // 可以在这里实现搜索过滤逻辑
                console.log('Searching for:', value);
              }}
              allowClear
              style={{ width: '100%' }}
            />
          </Form.Item>
          <Form.Item name="notes" label="备注">
            <TextArea rows={3} placeholder="请输入备注信息（可选）" />
          </Form.Item>
        </Form>
      </Drawer>

      {/* 编辑库存设置抽屉 */}
      <Drawer
        title={`设置 - ${selectedItemForManualIn ? selectedItemForManualIn.item_name : ''}`}
        open={editInventoryVisible}
        onClose={() => {
          onEditInventoryClose();
          editInventoryForm.resetFields();
          setIsInventoryEnabled(false);
          setHasFormChanges(false);
          setInitialFormValues(null);
        }}
        width={500}
        maskClosable={false}
        destroyOnClose
        extra={
          <Space>
            <Button onClick={() => {
              onEditInventoryClose();
              editInventoryForm.resetFields();
              setIsInventoryEnabled(false);
              setHasFormChanges(false);
              setInitialFormValues(null);
            }}>取消</Button>
            <Button 
              type="primary" 
              loading={editInventoryLoading}
              disabled={!hasFormChanges}
              onClick={() => editInventoryForm.submit()}
            >
              确认设置
            </Button>
          </Space>
        }
      >
        <Form 
          form={editInventoryForm} 
          layout="vertical" 
          onFinish={onEditInventorySubmit}
          onValuesChange={handleFormValuesChange}
        >
          <Form.Item name="id" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="item_id" hidden>
            <Input />
          </Form.Item>
          
          <Form.Item name="is_active" label="启用库存管理" valuePropName="checked">
            <Switch 
              checkedChildren="启用" 
              unCheckedChildren="禁用"
              defaultChecked={true}
              onChange={(checked) => {
                setIsInventoryEnabled(checked);
                // 手动触发表单变化检测
                const currentValues = editInventoryForm.getFieldsValue();
                const newValues = { ...currentValues, is_active: checked };
                handleFormValuesChange({ is_active: checked }, newValues);
              }}
            />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="min_quantity" label="最小库存量">
                <InputNumber 
                  min={0} 
                  precision={4} 
                  style={{ width: '100%' }} 
                  placeholder="设置库存阈值"
                  disabled={!isInventoryEnabled}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="max_quantity" label="最大库存量">
                <InputNumber 
                  min={0} 
                  precision={4} 
                  style={{ width: '100%' }} 
                  placeholder="设置最大库存预警阈值"
                  disabled={!isInventoryEnabled}
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="storage_location" label="存储位置">
                <Input 
                  placeholder="例如：A区-01货架"
                  disabled={!isInventoryEnabled}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="rack_number" label="货架号">
                <Input 
                  placeholder="例如：A-01-01"
                  disabled={!isInventoryEnabled}
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item name="notes" label="备注">
            <TextArea 
              rows={3} 
              placeholder="请输入备注信息（可选）"
              disabled={!isInventoryEnabled}
            />
          </Form.Item>
        </Form>
      </Drawer>
    </>
  );
};

export default InventoryForms;
