import React from 'react';
import { Tag } from 'antd';

interface InventoryProgressBarProps {
  currentQuantity: number;
  minQuantity: number;
  maxQuantity?: number;
  status: string;
  purchase_unit: string;
  inventory_unit: string;
  qty_per_up: number;
  purchase_unit_quantity?: number;
  hasInventory?: boolean;
}

const InventoryProgressBar: React.FC<InventoryProgressBarProps> = ({
  currentQuantity,
  minQuantity,
  maxQuantity,
  status,
  purchase_unit,
  inventory_unit,
  qty_per_up,
  purchase_unit_quantity,
  hasInventory = true
}) => {
  if (!hasInventory) {
    return <span style={{ color: '#999' }}>-</span>;
  }

  const getProgressMax = () => {
    const current = Number(currentQuantity);
    let progressMax = current;
    if (maxQuantity !== undefined && maxQuantity !== null) {
      // 有最大库存限制时，以max(最大库存, 当前库存)为上限
      progressMax = Math.max(Number(maxQuantity), current);
    }
    // else 
    if (minQuantity != undefined && minQuantity != null) {
      if (current < minQuantity) {
        progressMax = minQuantity;
      }
    }

    return progressMax;
  };

  const getProgressValue = () => {
    const max = getProgressMax();
    let progressValue = max;
    if (max > 0) {
      progressValue = Math.min(currentQuantity, max);
    }

    return progressValue;
  };

  const getProgressPercent = () => {
    const max = getProgressMax();
    if (max === 0) return 0;
    const percent = Math.round((currentQuantity / max) * 100);
    return Math.min(percent, 100); // 确保不超过100%
  };

  const getStatusConfig = () => {
    const configs = {
      normal: { color: 'green', text: '正常' },
      low: { color: 'orange', text: '低库存' },
      out_of_stock: { color: 'red', text: '缺货' },
      overstock: { color: 'blue', text: '超储' }
    };
    return configs[status as keyof typeof configs] || { color: 'default', text: status };
  };

  // 创建分段进度条数据
  const createSegmentedProgress = () => {
    const current = Number(currentQuantity);
    const min = Number(minQuantity);
    const max = maxQuantity !== undefined && maxQuantity !== null ? Number(maxQuantity) : null;
    const progressMax = getProgressMax();
    
    const segments = [];
    
    // 缺货状态
    if (current === 0) {
      return [{
        width: 0,
        color: '#ff4d4f',
        label: '缺货'
      }];
    }
    
    // 有库存状态 - 统一处理逻辑
    let remainingWidth = 100;
    
    // 低库存区间（0到min）
    if (min > 0) {
      const lowStockWidth = Math.min((min / progressMax) * 100, (current / progressMax) * 100);
      if (lowStockWidth > 0) {
        segments.push({
          width: lowStockWidth,
          color: '#fa8c16',
          label: '低库存'
        });
        remainingWidth -= lowStockWidth;
      }
    }
    
    // 正常区间（min到max或min到current）
    if (max !== null) {
      // 有最大库存限制
      const normalStart = Math.max(min, 0);
      const normalEnd = Math.min(current, max);
      const normalWidth = ((normalEnd - normalStart) / progressMax) * 100;
      if (normalWidth > 0) {
        segments.push({
          width: normalWidth,
          color: '#52c41a',
          label: '正常'
        });
        remainingWidth -= normalWidth;
      }
      
      // 超储区间（max到current）
      if (current > max) {
        const overstockWidth = ((current - max) / progressMax) * 100;
        if (overstockWidth > 0) {
          segments.push({
            width: overstockWidth,
            color: '#1890ff',
            label: '超储'
          });
          remainingWidth -= overstockWidth;
        }
      }
    } else {
      // 无最大库存限制 - 填充剩余部分
      const filledWidth = (current / progressMax) * 100;
      const totalSegmentsWidth = segments.reduce((sum, seg) => sum + seg.width, 0);
      const remainingSegmentWidth = Math.max(0, filledWidth - totalSegmentsWidth);
      
      if (remainingSegmentWidth > 0) {
        const color = current > min ? '#52c41a' : '#fa8c16';
        const label = current > min ? '正常' : '低库存';
        segments.push({
          width: remainingSegmentWidth,
          color,
          label
        });
        remainingWidth -= remainingSegmentWidth;
      }
    }
    
    // 不再将剩余宽度填充为已完成部分，保留灰色背景以体现实际进度百分比
    
    return segments;
  };

  const formatRangeText = () => {
    if (maxQuantity !== undefined && maxQuantity !== null) {
      return `${minQuantity}~${maxQuantity}`;
    }
    return `${minQuantity}~-`;
  };

  const progressMax = getProgressMax();
  const progressValue = getProgressValue();
  const progressPercent = getProgressPercent();
  const statusConfig = getStatusConfig();
  const rangeText = formatRangeText();
  const segments = createSegmentedProgress();

  return (
    <div style={{ width: '100%' }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: 4
      }}>
        <span style={{ 
          fontWeight: 'bold', 
          fontSize: '14px',
          color: '#666'
        }}>
          {currentQuantity} {inventory_unit}
          {purchase_unit_quantity && qty_per_up > 1 && (
            <span style={{ fontSize: '12px', color: '#999', marginLeft: '4px' }}>
              （{purchase_unit_quantity} {purchase_unit}）
            </span>
          )}
        </span>
        <Tag 
          color={statusConfig.color} 
          style={{ fontSize: '11px', margin: 0 }}
        >
          {statusConfig.text}
        </Tag>
      </div>

      <div style={{ marginBottom: 4 }}>
        <div style={{ 
          width: '100%', 
          height: '8px', 
          backgroundColor: '#f0f0f0', 
          borderRadius: '4px',
          overflow: 'hidden',
          position: 'relative'
        }}>
          {segments.map((segment, index) => (
            <div
              key={index}
              style={{
                position: 'absolute',
                left: index === 0 ? '0' : `${segments.slice(0, index).reduce((sum, s) => sum + s.width, 0)}%`,
                width: `${segment.width}%`,
                height: '100%',
                backgroundColor: segment.color,
                borderRadius: index === 0 ? '4px 0 0 4px' : 
                           index === segments.length - 1 ? '0 4px 4px 0' : '0'
              }}
            />
          ))}
        </div>
      </div>

      <div style={{ 
        fontSize: '11px',
        color: '#666'
      }}>
        <span>库存范围: {rangeText}</span>
      </div>
    </div>
  );
};

export default InventoryProgressBar;
