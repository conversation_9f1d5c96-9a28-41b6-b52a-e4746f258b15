import React from 'react';
import { useTranslation } from 'react-i18next';
import { Table, Button, Input, Space, Tag, Card, Row, Col, Select } from 'antd';
import { EyeOutlined, ReloadOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Search } = Input;
const { Option } = Select;

// 预警级别配置
const alertLevelConfig = {
  info: { color: 'blue', text: '信息' },
  warning: { color: 'orange', text: '警告' },
  critical: { color: 'red', text: '紧急' }
};

interface InventoryAlert {
  id: number;
  department_id: number;
  department_name: string;
  item_id: number;
  item_name: string;
  item_code: string;
  alert_type: string;
  alert_level: string;
  message: string;
  current_stock: number;
  threshold_value: number;
  is_active: boolean;
  is_resolved: boolean;
  resolved_by?: number;
  resolved_at?: string;
  resolver_name?: string;
  created_at: string;
  updated_at: string;
}

interface InventoryAlertsProps {
  alerts: InventoryAlert[];
  loading: boolean;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  filters: {
    item_id?: number;
    alert_type?: string;
    alert_level?: string;
    is_resolved?: boolean;
    is_active: boolean;
  };
  onSearch: (value: string) => void;
  onAlertLevelChange: (value: string) => void;
  onResolvedStatusChange: (value: boolean) => void;
  onRefresh: () => void;
  onViewDetail: (record: InventoryAlert) => void;
  onResolveAlert: (alertId: number) => void;
}

const InventoryAlerts: React.FC<InventoryAlertsProps> = ({
  alerts,
  loading,
  pagination,
  filters,
  onSearch,
  onAlertLevelChange,
  onResolvedStatusChange,
  onRefresh,
  onViewDetail,
  onResolveAlert
}) => {
  const { t } = useTranslation();
  
  const alertLevelConfig = {
    info: { color: 'blue', text: t('inventory.info') },
    warning: { color: 'orange', text: t('inventory.warning') },
    critical: { color: 'red', text: t('inventory.critical') }
  };
  
  const alertColumns: ColumnsType<InventoryAlert> = [
    {
      title: t('inventory.department'),
      dataIndex: 'department_name',
      key: 'department_name',
      width: 120,
    },
    {
      title: t('inventory.itemName'),
      dataIndex: 'item_name',
      key: 'item_name',
      width: 150,
    },
    {
      title: t('inventory.alertType'),
      dataIndex: 'alert_type',
      key: 'alert_type',
      width: 120,
      render: (type: string) => {
        const config = alertLevelConfig[type as keyof typeof alertLevelConfig];
        return config ? (
          <Tag color={config.color}>{config.text}</Tag>
        ) : (
          <Tag>{type}</Tag>
        );
      },
    },
    {
      title: t('inventory.alertLevel'),
      dataIndex: 'alert_level',
      key: 'alert_level',
      width: 100,
      render: (level: string) => {
        const config = alertLevelConfig[level as keyof typeof alertLevelConfig];
        return config ? (
          <Tag color={config.color}>{config.text}</Tag>
        ) : (
          <Tag>{level}</Tag>
        );
      },
    },
    {
      title: t('inventory.currentStock'),
      dataIndex: 'current_stock',
      key: 'current_stock',
      width: 100,
    },
    {
      title: t('inventory.threshold'),
      dataIndex: 'threshold_value',
      key: 'threshold_value',
      width: 100,
    },
    {
      title: t('inventory.status'),
      dataIndex: 'is_resolved',
      key: 'is_resolved',
      width: 100,
      render: (resolved: boolean) => (
        <Tag color={resolved ? 'green' : 'red'}>
          {resolved ? t('inventory.resolved') : t('inventory.unresolved')}
        </Tag>
      ),
    },
    {
      title: t('inventory.createTime'),
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
    },
    {
      title: t('inventory.actions'),
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space size="small">
          {!record.is_resolved && (
            <Button
              type="link"
              size="small"
              onClick={() => onResolveAlert(record.id)}
            >
              {t('inventory.resolve')}
            </Button>
          )}
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => onViewDetail(record)}
          >
            {t('inventory.details')}
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Search
            placeholder={t('inventory.searchItemPlaceholder')}
            allowClear
            onSearch={onSearch}
          />
        </Col>
        <Col span={4}>
          <Select
            placeholder={t('inventory.alertLevelPlaceholder')}
            allowClear
            style={{ width: '100%' }}
            onChange={onAlertLevelChange}
          >
                          <Option value="info">{t('inventory.info')}</Option>
              <Option value="warning">{t('inventory.warning')}</Option>
              <Option value="critical">{t('inventory.critical')}</Option>
          </Select>
        </Col>
        <Col span={4}>
          <Select
            placeholder={t('inventory.resolvedStatusPlaceholder')}
            allowClear
            style={{ width: '100%' }}
            onChange={onResolvedStatusChange}
          >
                          <Option value={false}>{t('inventory.unresolvedStatus')}</Option>
              <Option value={true}>{t('inventory.resolvedStatus')}</Option>
          </Select>
        </Col>
        <Col span={4}>
          <Button
            icon={<ReloadOutlined />}
            onClick={onRefresh}
          >
            {t('inventory.refresh')}
          </Button>
        </Col>
      </Row>

      <Table
        columns={alertColumns}
        dataSource={alerts}
        rowKey="id"
        loading={loading}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => t('inventory.paginationInfo', { start: range[0], end: range[1], total }),
        }}
        scroll={{ x: 1000 }}
      />
    </Card>
  );
};

export default InventoryAlerts;
