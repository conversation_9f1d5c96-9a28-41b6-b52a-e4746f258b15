import React from 'react';
import { Drawer, Card, Row, Col, Statistic, Tag, Descriptions, Typography } from 'antd';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';

const { Title, Text } = Typography;

interface InventoryItem {
  id: number;
  department_id: number;
  department_name: string;
  item_id: number;
  item_name: string;
  item_code: string;
  current_quantity: number;
  min_quantity: number;
  max_quantity?: number;
  status: string;
  storage_location?: string;
  rack_number?: string;
  purchase_unit: string;
  inventory_unit: string;
  qty_per_up: number;
  purchase_unit_quantity?: number;
  last_purchase_price?: number;
  average_cost?: number;
  total_value?: number;
  calculated_usd_price?: number;
  original_currency_price?: number;
  currency_code?: string;
  exchange_rate?: number;
  last_updated: string;
  created_at: string;
  updated_at: string;
  item_image_url?: string;
  has_inventory?: boolean;
  is_active?: boolean;
}

interface InventoryDetailProps {
  visible: boolean;
  selectedRecord: InventoryItem | null;
  onClose: () => void;
}

const InventoryDetail: React.FC<InventoryDetailProps> = ({
  visible,
  selectedRecord,
  onClose
}) => {
  if (!selectedRecord) return null;

  return (
    <Drawer
      title="库存详情"
      placement="right"
      width={800}
      onClose={onClose}
      open={visible}
    >
      <div>
        {/* 基本信息卡片 */}
        <Card title="基本信息" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                {selectedRecord.item_image_url ? (
                  <img
                    src={selectedRecord.item_image_url}
                    alt={selectedRecord.item_name}
                    style={{ width: 80, height: 80, objectFit: 'cover', borderRadius: 8, marginRight: 16 }}
                  />
                ) : (
                  <div style={{ width: 80, height: 80, backgroundColor: '#f5f5f5', borderRadius: 8, display: 'flex', alignItems: 'center', justifyContent: 'center', marginRight: 16 }}>
                    <Text type="secondary">无图</Text>
                  </div>
                )}
                <div>
                  <Title level={4} style={{ margin: 0 }}>{selectedRecord.item_name}</Title>
                  <Text type="secondary">编码：{selectedRecord.item_code}</Text>
                </div>
              </div>
            </Col>
            <Col span={12}>
                              <Statistic
                  title="当前库存"
                  value={selectedRecord.has_inventory ? (selectedRecord.current_quantity || 0) : '-'}
                  suffix={selectedRecord.has_inventory ? (selectedRecord.inventory_unit || 'piece') : ''}
                  valueStyle={{ color: selectedRecord.has_inventory ? (selectedRecord.status === 'normal' ? '#3f8600' : selectedRecord.status === 'low' ? '#faad14' : '#cf1322') : '#999' }}
                />
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={8}>
              <Statistic
                title="最小库存"
                value={selectedRecord.has_inventory ? (selectedRecord.min_quantity || 0) : '-'}
                suffix={selectedRecord.has_inventory ? (selectedRecord.inventory_unit || 'piece') : ''}
                valueStyle={{ color: selectedRecord.has_inventory ? '#1890ff' : '#999' }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="最大库存"
                value={selectedRecord.has_inventory ? (selectedRecord.max_quantity || '-') : ''}
                suffix={selectedRecord.has_inventory ? (selectedRecord.inventory_unit || 'piece') : ''}
                valueStyle={{ color: selectedRecord.has_inventory ? '#1890ff' : '#999' }}
              />
            </Col>
          </Row>
        </Card>

        {/* 状态和位置信息 */}
        <Card title="状态信息" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="库存状态">
                  {selectedRecord.has_inventory ? (
                    <Tag color={
                      selectedRecord.status === 'normal' ? 'green' :
                      selectedRecord.status === 'low' ? 'orange' :
                      selectedRecord.status === 'out_of_stock' ? 'red' :
                      selectedRecord.status === 'overstock' ? 'blue' : 'default'
                    }>
                      {selectedRecord.status === 'normal' ? '正常' :
                       selectedRecord.status === 'low' ? '低库存' :
                       selectedRecord.status === 'out_of_stock' ? '缺货' :
                       selectedRecord.status === 'overstock' ? '超储' : selectedRecord.status}
                    </Tag>
                  ) : (
                    <Tag color="default">无库存</Tag>
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="部门">
                  {selectedRecord.department_name}
                </Descriptions.Item>
                <Descriptions.Item label="存储位置">
                  {selectedRecord.has_inventory ? (selectedRecord.storage_location || '-') : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="货架号">
                  {selectedRecord.has_inventory ? (selectedRecord.rack_number || '-') : '-'}
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col span={12}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="最后更新">
                  {selectedRecord.has_inventory ? (selectedRecord.last_updated ? dayjs(selectedRecord.last_updated).format(DATE_FORMATS.DATE_TIME) : '-') : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="最后采购价">
                  {selectedRecord.has_inventory ? (selectedRecord.last_purchase_price ? `$${selectedRecord.last_purchase_price}` : '-') : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="计算后USD价格">
                  {selectedRecord.has_inventory ? (
                    selectedRecord.calculated_usd_price ? (
                      <div>
                        <div style={{ color: '#1890ff', fontWeight: 'bold' }}>
                          ${selectedRecord.calculated_usd_price >= 1 ? 
                            selectedRecord.calculated_usd_price.toFixed(2) : 
                            selectedRecord.calculated_usd_price.toFixed(4)}
                        </div>
                        {selectedRecord.currency_code && selectedRecord.currency_code !== 'USD' && selectedRecord.original_currency_price && (
                          <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                            原价: {selectedRecord.currency_code} {selectedRecord.original_currency_price.toFixed(2)}
                            {selectedRecord.exchange_rate && (
                              <span style={{ marginLeft: '8px', color: '#999' }}>
                                (汇率: {selectedRecord.exchange_rate.toFixed(6)})
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    ) : '-'
                  ) : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="平均成本">
                  {selectedRecord.has_inventory ? (selectedRecord.average_cost ? `$${selectedRecord.average_cost}` : '-') : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="库存价值">
                  {selectedRecord.has_inventory ? (selectedRecord.total_value ? `$${selectedRecord.total_value}` : '-') : '-'}
                </Descriptions.Item>
              </Descriptions>
            </Col>
          </Row>
        </Card>
      </div>
    </Drawer>
  );
};

export default InventoryDetail;
