import React from 'react';
import { useTranslation } from 'react-i18next';
import { Table, Button, Input, Tag, Card, Row, Col, DatePicker, Select } from 'antd';
import { EyeOutlined, ReloadOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 变更类型配置（将在组件内部使用 t() 函数）

interface InventoryChangeRecord {
  id: number;
  department_id: number;
  department_name: string;
  item_id: number;
  item_name: string;
  item_code: string;
  before_quantity: number;
  after_quantity: number;
  change_quantity: number;
  change_type: string;
  change_reason?: string;
  operator_id: number;
  operator_name: string;
  change_date: string;
  remarks?: string;
  created_at: string;
}

interface InventoryChangesProps {
  changeRecords: InventoryChangeRecord[];
  loading: boolean;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  filters: {
    item_id?: number;
    change_type?: string;
    operator_id?: number;
    start_date?: string;
    end_date?: string;
  };
  onSearch: (value: string) => void;
  onChangeTypeChange: (value: string) => void;
  onDateRangeChange: (dates: any) => void;
  onRefresh: () => void;
  onViewDetail: (record: InventoryChangeRecord) => void;
}

const InventoryChanges: React.FC<InventoryChangesProps> = ({
  changeRecords,
  loading,
  pagination,
  filters,
  onSearch,
  onChangeTypeChange,
  onDateRangeChange,
  onRefresh,
  onViewDetail
}) => {
  const { t } = useTranslation();
  
  const changeTypeConfig = {
    manual_in: { color: 'blue', text: t('inventory.manualIn') },
    pickup_out: { color: 'orange', text: t('inventory.pickupOut') },
    adjust: { color: 'purple', text: t('inventory.adjust') },
    transfer: { color: 'cyan', text: t('inventory.transfer') },
    return: { color: 'red', text: t('inventory.return') }
  };
  
  const changeColumns: ColumnsType<InventoryChangeRecord> = [
    {
      title: t('inventory.department'),
      dataIndex: 'department_name',
      key: 'department_name',
      width: 120,
    },
    {
      title: t('inventory.itemName'),
      dataIndex: 'item_name',
      key: 'item_name',
      width: 150,
    },
    {
      title: t('inventory.beforeChange'),
      dataIndex: 'before_quantity',
      key: 'before_quantity',
      width: 80,
    },
    {
      title: t('inventory.afterChange'),
      dataIndex: 'after_quantity',
      key: 'after_quantity',
      width: 80,
    },
    {
      title: t('inventory.changeQuantity'),
      dataIndex: 'change_quantity',
      key: 'change_quantity',
      width: 100,
      render: (value: number) => (
        <span style={{ color: value > 0 ? '#52c41a' : '#ff4d4f' }}>
          {value > 0 ? '+' : ''}{value}
        </span>
      ),
    },
    {
      title: t('inventory.changeType'),
      dataIndex: 'change_type',
      key: 'change_type',
      width: 120,
      render: (type: string) => {
        const config = changeTypeConfig[type as keyof typeof changeTypeConfig];
        return config ? (
          <Tag color={config.color}>{config.text}</Tag>
        ) : (
          <Tag>{type}</Tag>
        );
      },
    },
    {
      title: t('inventory.operator'),
      dataIndex: 'operator_name',
      key: 'operator_name',
      width: 100,
    },
    {
      title: t('inventory.changeTime'),
      dataIndex: 'change_date',
      key: 'change_date',
      width: 150,
    },
    {
      title: t('inventory.actions'),
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          type="link"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => onViewDetail(record)}
        >
          {t('inventory.details')}
        </Button>
      ),
    },
  ];

  return (
    <Card>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Search
            placeholder={t('inventory.searchItemPlaceholder')}
            allowClear
            onSearch={onSearch}
          />
        </Col>
        <Col span={4}>
          <Select
            placeholder={t('inventory.changeTypePlaceholder')}
            allowClear
            style={{ width: '100%' }}
            onChange={onChangeTypeChange}
          >
                          <Option value="manual_in">{t('inventory.manualIn')}</Option>
              <Option value="pickup_out">{t('inventory.pickupOut')}</Option>
              <Option value="adjust">{t('inventory.adjust')}</Option>
              <Option value="transfer">{t('inventory.transfer')}</Option>
              <Option value="return">{t('inventory.return')}</Option>
          </Select>
        </Col>
        <Col span={6}>
          <RangePicker
            style={{ width: '100%' }}
            onChange={onDateRangeChange}
          />
        </Col>
        <Col span={4}>
          <Button
            icon={<ReloadOutlined />}
            onClick={onRefresh}
          >
            {t('inventory.refresh')}
          </Button>
        </Col>
      </Row>

      <Table
        columns={changeColumns}
        dataSource={changeRecords}
        rowKey="id"
        loading={loading}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => t('inventory.paginationInfo', { start: range[0], end: range[1], total }),
        }}
        scroll={{ x: 1000 }}
      />
    </Card>
  );
};

export default InventoryChanges;
