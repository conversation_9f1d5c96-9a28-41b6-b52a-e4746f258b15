import React from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { Table, Button, Input, Space, Tag, Card, Row, Col, Typography, Select, Tooltip, message } from 'antd';
import { EyeOutlined, PlusOutlined, EditOutlined, SettingOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import PermissionGuard from '@shared/components/PermissionGuard';
import { PERMISSIONS } from '@shared/config/permissions';
import InventoryProgressBar from './InventoryProgressBar';
import { useNavigate } from 'react-router-dom';

const { Search } = Input;
const { Option } = Select;
const { Text } = Typography;

interface InventoryItem {
  id: number;
  department_id: number;
  department_name: string;
  item_id: number;
  item_name: string;
  item_code: string;
  current_quantity: number;
  min_quantity: number;
  max_quantity?: number;
  status: string;
  storage_location?: string;
  rack_number?: string;
  purchase_unit: string;
  inventory_unit: string;
  qty_per_up: number;
  purchase_unit_quantity?: number;
  last_purchase_price?: number;
  average_cost?: number;
  total_value?: number;
  calculated_usd_price?: number;
  original_currency_price?: number;
  currency_code?: string;
  exchange_rate?: number;
  last_updated: string;
  created_at: string;
  updated_at: string;
  item_image_url?: string;
  has_inventory?: boolean;
  is_active?: boolean;
}

interface InventoryListProps {
  inventoryList: InventoryItem[];
  loading: boolean;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  filters: {
    item_id?: number;
    status?: string;
    search: string;
    include_all_items: boolean;
  };
  currentUser: any;
  onSearch: (value: string) => void;
  onStatusChange: (value: string) => void;
  onViewDetail: (record: InventoryItem) => void;
  onManualIn: (record?: InventoryItem) => void;
  onAdjust: (record?: InventoryItem) => void;
  onEditInventory: (record: InventoryItem) => void;
  onPageChange?: (page: number, pageSize: number) => void;
  onAddOutOfStockToCart: () => void;
}

const InventoryList: React.FC<InventoryListProps> = ({
  inventoryList,
  loading,
  pagination,
  filters,
  currentUser,
  onSearch,
  onStatusChange,
  onViewDetail,
  onManualIn,
  onAdjust,
  onEditInventory,
  onPageChange,
  onAddOutOfStockToCart
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  // 数值格式化辅助，兼容字符串/空值
  const toNum = (v: any): number => {
    if (v === null || v === undefined) return 0;
    const n = typeof v === 'number' ? v : Number(v);
    return Number.isFinite(n) ? n : 0;
  };

  const inventoryColumns: ColumnsType<InventoryItem> = [
    {
      title: t('inventory.image'),
      dataIndex: 'item_image_url',
      key: 'item_image_url',
      width: 100,
      render: (_: any, record) => {
        const src = record.item_image_url;
        const goDetail = () => navigate(`/admin/items/${record.item_id}`);
        return (
          <div
            role="link"
            tabIndex={0}
            onClick={goDetail}
            onKeyDown={(e) => { if (e.key === 'Enter') goDetail(); }}
            style={{ cursor: 'pointer', display: 'inline-block' }}
          >
            {src ? (
              <img
                src={src}
                alt={record.item_name}
                style={{ width: 64, height: 64, objectFit: 'cover', borderRadius: 6, background: '#f5f5f5' }}
                onError={(e: any) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            ) : (
              <span style={{ color: '#999', fontSize: '12px' }}>{t('inventory.noImage')}</span>
            )}
          </div>
        );
      }
    },
    {
      title: t('inventory.itemInfo'),
      key: 'item_info',
      width: 200,
      render: (_, record) => {
        const goDetail = () => navigate(`/admin/items/${record.item_id}`);
        return (
          <div
            role="link"
            tabIndex={0}
            onClick={goDetail}
            onKeyDown={(e) => { if (e.key === 'Enter') goDetail(); }}
            style={{ cursor: 'pointer' }}
          >
            <div style={{ fontWeight: 'bold', fontSize: '14px', color: '#1677ff' }}>
              {record.item_name}
              <span style={{ color: '#666', fontSize: '12px', fontWeight: 'normal', marginLeft: 6 }}>
                {record.item_code}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      title: t('inventory.inventoryStatus'),
      key: 'inventory_status',
      width: 200,
      render: (_, record: InventoryItem) => {
        // 检查是否有库存记录
        if (!record.has_inventory) {
          return <Tag color="default" style={{ fontSize: '11px' }}>{t('inventory.noInventory')}</Tag>;
        }

        // 检查是否被禁用
        if (record.is_active === false) {
          return <Tag color="red" style={{ fontSize: '11px' }}>{t('inventory.disabled')}</Tag>;
        }        
        
        return (
          <InventoryProgressBar
            currentQuantity={record.current_quantity}
            minQuantity={record.min_quantity || 0}
            maxQuantity={record.max_quantity}
            status={record.status}
            purchase_unit={record.purchase_unit}
            inventory_unit={record.inventory_unit}
            qty_per_up={record.qty_per_up}
            purchase_unit_quantity={record.purchase_unit_quantity}
            hasInventory={record.has_inventory}
          />
        );
      },
    },
    {
      title: t('inventory.inventoryValue'),
      dataIndex: 'total_value',
      key: 'total_value',
      width: 100,
      render: (value: any, record: InventoryItem) => {
        // 检查是否被禁用或无库存记录
        if (record.is_active === false || !record.has_inventory) {
          return <span style={{ color: '#999' }}>-</span>;
        }
        // 无价格时显示 '-'
        if (value === null || value === undefined || value === '') {
          return <span style={{ color: '#999' }}>-</span>;
        }
        return <span style={{ fontSize: '12px' }}>${toNum(value).toFixed(2)}</span>;
      },
    },
    {
      title: t('inventory.calculatedUsdPrice'),
      key: 'calculated_usd_price',
      width: 120,
      render: (_: any, record: InventoryItem) => {
        // 检查是否被禁用或无库存记录
        if (record.is_active === false || !record.has_inventory) {
          return <span style={{ color: '#999' }}>-</span>;
        }
        
        // 如果没有计算后的USD价格，显示 '-'
        if (!record.calculated_usd_price) {
          return <span style={{ color: '#999' }}>-</span>;
        }
        
        // 显示计算后的USD价格
        const usdPrice = toNum(record.calculated_usd_price);
        const displayPrice = usdPrice >= 1 ? usdPrice.toFixed(2) : usdPrice.toFixed(4);
        
        return (
          <div style={{ fontSize: '12px' }}>
            <div style={{ color: '#1890ff', fontWeight: 'bold' }}>${displayPrice}</div>
            {record.currency_code && record.currency_code !== 'USD' && record.original_currency_price && (
              <div style={{ fontSize: '10px', color: '#666', marginTop: '2px' }}>
                {record.currency_code} {toNum(record.original_currency_price).toFixed(2)}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: t('inventory.lastUpdated'),
      dataIndex: 'last_updated',
      key: 'last_updated',
      width: 120,
      render: (value: string, record: InventoryItem) => {
        // 检查是否被禁用或无库存记录
        if (record.is_active === false || !record.has_inventory) {
          return <span style={{ color: '#999' }}>-</span>;
        }
        // 格式化时间显示
        try {
          const date = new Date(value);
          const now = new Date();
          const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
          
          let displayText = '';
          if (diffInHours < 24) {
            // 24小时内显示相对时间
            if (diffInHours < 1) {
              const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
              if (diffInMinutes < 1) {
                displayText = t('inventory.justNow');
              } else {
                displayText = `${diffInMinutes}${t('inventory.minutesAgo')}`;
              }
            } else {
              displayText = `${diffInHours}${t('inventory.hoursAgo')}`;
            }
          } else {
            // 超过24小时显示日期
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const hour = date.getHours().toString().padStart(2, '0');
            const minute = date.getMinutes().toString().padStart(2, '0');
            displayText = `${month}-${day} ${hour}:${minute}`;
          }
          
          // 格式化完整时间用于tooltip
          const fullTime = dayjs(date).format(DATE_FORMATS.DATE_TIME);
          
          return (
            <Tooltip title={fullTime} placement="top">
              <span style={{ fontSize: '12px', cursor: 'help' }}>{displayText}</span>
            </Tooltip>
          );
        } catch (error) {
          return <span style={{ fontSize: '12px', color: '#999' }}>-</span>;
        }
      },
    },
    {
      title: t('inventory.actions'),
      key: 'action',
      width: 160,
      render: (_, record) => (
        <Space size={4}>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            style={{ padding: '0 4px', fontSize: '12px', height: 'auto' }}
            onClick={() => onViewDetail(record)}
          >
            {t('inventory.details')}
          </Button>

          <PermissionGuard permission={PERMISSIONS.INVENTORY.UPDATE}>
            <Button
              type="link"
              size="small"
              icon={<PlusOutlined />}
              style={{ padding: '0 4px', fontSize: '12px', height: 'auto' }}
              onClick={() => onManualIn(record)}
            >
              {t('inventory.manualIn')}
            </Button>
          </PermissionGuard>
          <PermissionGuard permission={PERMISSIONS.INVENTORY.ADJUST}>
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              style={{ padding: '0 4px', fontSize: '12px', height: 'auto' }}
              onClick={() => onAdjust(record)}
            >
              {t('inventory.adjust')}
            </Button>
          </PermissionGuard>
          <PermissionGuard permission={PERMISSIONS.INVENTORY.UPDATE}>
            <Button
              type="link"
              size="small"
              icon={<SettingOutlined />}
              style={{ padding: '0 4px', fontSize: '12px', height: 'auto' }}
              onClick={() => onEditInventory(record)}
            >
              {t('inventory.settings')}
            </Button>
          </PermissionGuard>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Search
            placeholder={t('inventory.searchItemPlaceholder')}
            allowClear
            onSearch={onSearch}
          />
        </Col>
        <Col span={6}>
          <Select
            placeholder={t('inventory.inventoryStatusPlaceholder')}
            style={{ width: '100%' }}
            onChange={onStatusChange}
            value={filters.status || 'enabled'}
          >
            <Option value="enabled">{t('inventory.enabled')}</Option>
            <Option value="normal">{t('inventory.normal')}</Option>
            <Option value="low">{t('inventory.low')}</Option>
            <Option value="out_of_stock">{t('inventory.outOfStock')}</Option>
            <Option value="overstock">{t('inventory.overstock')}</Option>
            <Option value="all_items">{t('inventory.allItems')}</Option>
          </Select>
        </Col>
        <Col span={6}>
          <PermissionGuard permission={PERMISSIONS.CART.ADD_ITEM}>
            <Button
              type="primary"
              icon={<ShoppingCartOutlined />}
              onClick={onAddOutOfStockToCart}
              style={{ width: '100%' }}
            >
              {t('inventory.addOutOfStockToCart')}
            </Button>
          </PermissionGuard>
        </Col>
        <Col span={6}>
          <Text type="secondary">
            {t('inventory.currentDisplay', { department: currentUser?.department || t('inventory.currentDepartment') })}
          </Text>
        </Col>
      </Row>

      <Table
        columns={inventoryColumns}
        dataSource={inventoryList}
        rowKey="id"
        loading={loading}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => t('inventory.paginationInfo', { start: range[0], end: range[1], total }),
          onChange: onPageChange,
        }}
        scroll={{ x: 800 }}
      />
    </Card>
  );
};

export default InventoryList;
