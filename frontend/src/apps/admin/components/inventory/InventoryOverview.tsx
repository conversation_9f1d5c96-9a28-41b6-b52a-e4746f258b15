import React from 'react';
import { useTranslation } from 'react-i18next';
import { Row, Col, Card, Statistic, Alert } from 'antd';
import { InboxOutlined, WarningOutlined, ExclamationCircleOutlined, AlertOutlined, ReloadOutlined } from '@ant-design/icons';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from 'recharts';

interface InventoryOverviewProps {
  overviewData: {
    total_items: number;
    total_departments: number;
    low_stock_items: number;
    out_of_stock_items: number;
    overstock_items: number;
    normal_stock_items: number;
    total_value: number;
    total_value_usd: number;
    active_alerts: number;
  };
  onRefresh: () => void;
}

const InventoryOverview: React.FC<InventoryOverviewProps> = ({ overviewData, onRefresh }) => {
  const { t } = useTranslation();
  const pieData = [
    { name: t('inventory.normal'), value: overviewData.normal_stock_items, color: '#52c41a' },
    { name: t('inventory.lowStock'), value: overviewData.low_stock_items, color: '#faad14' },
    { name: t('inventory.outOfStock'), value: overviewData.out_of_stock_items, color: '#ff4d4f' },
    { name: t('inventory.overstock'), value: overviewData.overstock_items, color: '#1890ff' },
  ].filter(item => item.value > 0);

  return (
    <>
      <Row gutter={16}>
        <Col span={4}>
          <Card>
            <Statistic 
              title={t('inventory.totalInventory')} 
              value={overviewData.total_items} 
              prefix={<InboxOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic 
              title={t('inventory.lowStockCount')} 
              value={overviewData.low_stock_items} 
              prefix={<WarningOutlined />}
              valueStyle={{ color: overviewData.low_stock_items > 0 ? '#faad14' : '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic 
              title={t('inventory.outOfStockCount')} 
              value={overviewData.out_of_stock_items} 
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title={t('inventory.totalInventoryValue')} 
              value={overviewData.total_value} 
              prefix="$"
              precision={2}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title={t('inventory.totalInventoryValueUsd')} 
              value={overviewData.total_value_usd} 
              prefix="$"
              precision={2}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16} style={{ marginTop: 16 }}>
        <Col span={12}>
          <Card title={t('inventory.inventoryStatusDistribution')} extra={<ReloadOutlined onClick={onRefresh} />}>
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={(props: any) => {
                      const { name, percent, value } = props || {};
                      const pct = typeof percent === 'number' ? (percent * 100).toFixed(0) : '0';
                      return `${name}(${value}), ${pct}%`;
                    }}
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                </PieChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title={t('inventory.activeAlerts')} extra={<ReloadOutlined onClick={onRefresh} />}>
            <Statistic 
                              title={t('inventory.unresolvedAlerts')} 
              value={overviewData.active_alerts} 
              prefix={<AlertOutlined />}
              valueStyle={{ color: overviewData.active_alerts > 0 ? '#cf1322' : '#3f8600' }}
            />
            {overviewData.active_alerts > 0 && (
              <Alert
                        message={t('inventory.unresolvedAlertsMessage')}
        description={t('inventory.unresolvedAlertsDescription')}
                type="warning"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}
          </Card>
        </Col>
      </Row>
    </>
  );
};

export default InventoryOverview;
