import React from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import { Table, Button, Input, Card, Row, Col, Select } from 'antd';
import { ReloadOutlined, DownloadOutlined } from '@ant-design/icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, BarChart, Bar, Legend } from 'recharts';
import type { ColumnsType } from 'antd/es/table';

const { Search } = Input;
const { Option } = Select;

interface UsageStatisticsItem {
  item_id: number;
  department_id: number;
  item_name: string;
  item_code: string;
  department_name: string;
  total_usage: number;
  usage_count: number;
  avg_unit_price: number;
  first_usage: string;
  last_usage: string;
  unit: string;
}

interface InventoryStatisticsProps {
  usageStatistics: UsageStatisticsItem[];
  loading: boolean;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  filters: {
    item_id?: number;
    year?: number;
    month?: number;
  };
  onSearch: (value: string) => void;
  onYearChange: (value: number) => void;
  onMonthChange: (value: number) => void;
  onRefresh: () => void;
  onExport: () => void;
}

const InventoryStatistics: React.FC<InventoryStatisticsProps> = ({
  usageStatistics,
  loading,
  pagination,
  filters,
  onSearch,
  onYearChange,
  onMonthChange,
  onRefresh,
  onExport
}) => {
  const { t } = useTranslation();
  // 数值格式化辅助，兼容字符串/空值
  const toNum = (v: any): number => {
    if (v === null || v === undefined) return 0;
    const n = typeof v === 'number' ? v : Number(v);
    return Number.isFinite(n) ? n : 0;
  };

  const statisticsColumns: ColumnsType<UsageStatisticsItem> = [
    {
      title: t('inventory.department'),
      dataIndex: 'department_name',
      key: 'department_name',
      width: 120,
    },
    {
      title: t('inventory.itemName'),
      dataIndex: 'item_name',
      key: 'item_name',
      width: 150,
    },
    {
      title: t('inventory.itemCode'),
      dataIndex: 'item_code',
      key: 'item_code',
      width: 120,
    },
    {
      title: t('inventory.usage'),
      dataIndex: 'total_usage',
      key: 'total_usage',
      width: 100,
      render: (value: any, record) => `${toNum(value)} ${record.unit}`,
    },
    {
      title: t('inventory.usageCount'),
      dataIndex: 'usage_count',
      key: 'usage_count',
      width: 100,
    },
    {
      title: t('inventory.averagePrice'),
      dataIndex: 'avg_unit_price',
      key: 'avg_unit_price',
      width: 120,
      render: (value: any) => `$${toNum(value).toFixed(2)}`,
    },
    {
      title: t('inventory.firstUsage'),
      dataIndex: 'first_usage',
      key: 'first_usage',
      width: 150,
      render: (value: any) => value ? dayjs(value).format(DATE_FORMATS.DATE_ONLY) : '-',
    },
    {
      title: t('inventory.lastUsage'),
      dataIndex: 'last_usage',
      key: 'last_usage',
      width: 150,
      render: (value: any) => value ? dayjs(value).format(DATE_FORMATS.DATE_ONLY) : '-',
    },
  ];

  // 生成时间序列数据，用于趋势图 - 多曲线，每个物品一条线
  const generateTimeSeriesData = (data: UsageStatisticsItem[]) => {
    if (!data || data.length === 0) {
      return { timeSeries: [], topItems: [] };
    }

    // 按使用量排序，取前5个物品
    const topItems = data
      .sort((a, b) => b.total_usage - a.total_usage)
      .slice(0, 5);

    // 收集所有日期并去重
    const allDates = new Set<string>();
    topItems.forEach(item => {
      if (item.first_usage) {
        allDates.add(item.first_usage.split('T')[0]);
      }
      if (item.last_usage) {
        allDates.add(item.last_usage.split('T')[0]);
      }
    });

    // 按日期排序
    const sortedDates = Array.from(allDates).sort();

    // 生成时间序列数据
    const timeSeries = sortedDates.map(date => {
      const result: any = { date };
      
      // 为每个物品计算该日期的使用量
      topItems.forEach(item => {
        const firstDate = item.first_usage ? item.first_usage.split('T')[0] : '';
        const lastDate = item.last_usage ? item.last_usage.split('T')[0] : '';
        
        // 如果该日期在物品的使用时间范围内，则计入统计
        if (date >= firstDate && date <= lastDate) {
          result[`item_${item.item_id}`] = item.total_usage;
        } else {
          result[`item_${item.item_id}`] = 0;
        }
      });

      return result;
    });

    return { timeSeries, topItems };
  };

  return (
    <Card>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Search
            placeholder="搜索物品名称或编码"
            allowClear
            onSearch={onSearch}
          />
        </Col>
        <Col span={4}>
          <Select
            placeholder="年份"
            allowClear
            style={{ width: '100%' }}
            onChange={onYearChange}
          >
            {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map(year => (
              <Option key={year} value={year}>{year}</Option>
            ))}
          </Select>
        </Col>
        <Col span={4}>
          <Select
            placeholder="月份"
            allowClear
            style={{ width: '100%' }}
            onChange={onMonthChange}
          >
            {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
              <Option key={month} value={month}>{month}</Option>
            ))}
          </Select>
        </Col>
        <Col span={4}>
          <Button
            icon={<ReloadOutlined />}
            onClick={onRefresh}
          >
            刷新
          </Button>
        </Col>
        <Col span={6}>
          <Button
            icon={<DownloadOutlined />}
            onClick={onExport}
          >
            导出报表
          </Button>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={12}>
          {/* 使用量趋势图 - 按时间维度，多曲线 */}
          <Card title="使用量趋势" size="small">
            <div style={{ height: 300, position: 'relative' }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={(() => {
                  const result = generateTimeSeriesData(usageStatistics);
                  return result.timeSeries || [];
                })()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    type="category"
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis />
                  <RechartsTooltip 
                    labelFormatter={(value) => `日期: ${value}`}
                    formatter={(value, name) => {
                      const itemId = name?.toString().replace('item_', '');
                      const item = usageStatistics.find(item => item.item_id.toString() === itemId);
                      return [value, item?.item_name || name];
                    }}
                  />
                  {(() => {
                    const result = generateTimeSeriesData(usageStatistics);
                    return (result.topItems || []).map((item, index) => {
                      const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'];
                      return (
                        <Line 
                          key={item.item_id}
                          type="monotone" 
                          dataKey={`item_${item.item_id}`} 
                          stroke={colors[index % colors.length]} 
                          name={item.item_name}
                          strokeWidth={2}
                          dot={{ r: 4 }}
                        />
                      );
                    });
                  })()}
                </LineChart>
              </ResponsiveContainer>
              
              {/* 自定义图例 - 覆盖在图表上 */}
              <div style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                zIndex: 10,
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                padding: '8px',
                borderRadius: '4px',
                border: '1px solid #e8e8e8',
                fontSize: '12px',
                maxWidth: '200px'
              }}>
                {(() => {
                  const result = generateTimeSeriesData(usageStatistics);
                  return (result.topItems || []).map((item, index) => {
                    const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'];
                    return (
                      <div key={item.item_id} style={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        marginBottom: '4px',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                      }}>
                        <div style={{
                          width: '12px',
                          height: '2px',
                          backgroundColor: colors[index % colors.length],
                          marginRight: '6px',
                          flexShrink: 0
                        }} />
                        <span style={{ 
                          color: '#333',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis'
                        }}>
                          {item.item_name}
                        </span>
                      </div>
                    );
                  });
                })()}
              </div>
            </div>
          </Card>
        </Col>
        
        <Col span={12}>
          {/* 使用次数排行图 */}
          <Card title="使用次数排行" size="small">
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={usageStatistics.slice(0, 10)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="item_name" 
                    angle={-45}
                    textAnchor="end"
                    height={80}
                    interval={0}
                  />
                  <YAxis />
                  <RechartsTooltip />
                  <Bar 
                    dataKey="usage_count" 
                    fill="#82ca9d" 
                    name="使用次数"
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
      </Row>

      <Table
        columns={statisticsColumns}
        dataSource={usageStatistics}
        rowKey="id"
        loading={loading}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
        }}
        scroll={{ x: 800 }}
      />
    </Card>
  );
};

export default InventoryStatistics;
