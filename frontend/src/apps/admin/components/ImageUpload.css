.image-upload-container {
  position: relative;
  width: 100%;
}

.image-display {
  position: relative;
  display: inline-block;
  width: 100%;
}

.image-display .ant-image {
  width: 100% !important;
  height: auto !important;
}

.image-display .ant-image img {
  width: 100% !important;
  height: auto !important;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
}

.upload-button {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.upload-button:hover {
  background: rgba(0, 0, 0, 0.8);
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
  background-color: #fafafa;
  width: 100%;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  aspect-ratio: 1;
}

.upload-area:hover {
  border-color: #1890ff;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
} 