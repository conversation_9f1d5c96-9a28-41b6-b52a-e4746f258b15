import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Modal,
  Input,
  List,
  Card,
  Row,
  Col,
  Typography,
  Tag,
  Spin,
  Empty,
  Image,
  Space,
  Divider
} from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { supplierService } from '@admin/services/supplierService';

const { Text, Title } = Typography;

interface Item {
  id: number;
  name: string;
  code: string;
  description?: string;
  category_name?: string;
  image_url?: string;
  spec_material?: string;
  size_dimension?: string;
  purchase_unit?: string;
  inventory_unit?: string;
  qty_per_up?: number;
  brand?: string;
}

interface ItemSelectorProps {
  visible: boolean;
  onCancel: () => void;
  onSelect: (item: Item) => void;
  loading?: boolean;
}

const ItemSelector: React.FC<ItemSelectorProps> = ({
  visible,
  onCancel,
  onSelect,
  loading = false
}) => {
  const { t } = useTranslation();
  const [items, setItems] = useState<Item[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [loadingItems, setLoadingItems] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement>(null);

  // 获取物品列表
  const fetchItems = useCallback(async (search?: string, pageNum: number = 1, append: boolean = false) => {
    if (pageNum === 1) {
      setLoadingItems(true);
    } else {
      setLoadingMore(true);
    }
    
    try {
      const itemsData = await supplierService.getItemsForSelection({ 
        search, 
        page: pageNum,
        size: 20
      });
      
      const newItems = Array.isArray(itemsData) ? itemsData : [];
      
      if (append) {
        setItems(prev => [...prev, ...newItems]);
      } else {
        setItems(newItems);
      }
      
      // 判断是否还有更多数据
      setHasMore(newItems.length === 20);
      setPage(pageNum);
    } catch (error: any) {
      console.error('Error fetching items:', error);
      if (!append) {
        setItems([]);
      }
    } finally {
      setLoadingItems(false);
      setLoadingMore(false);
    }
  }, []);

  // 处理搜索
  const handleSearch = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return (value: string) => {
      clearTimeout(timeoutId);
      setSearchValue(value);
      setPage(1);
      setHasMore(true);
      timeoutId = setTimeout(() => {
        if (value.trim()) {
          fetchItems(value.trim(), 1, false);
        } else {
          fetchItems('', 1, false);
        }
      }, 300);
    };
  }, [fetchItems]);

  // 初始化加载
  useEffect(() => {
    if (visible) {
      setPage(1);
      setHasMore(true);
      setItems([]);
      fetchItems('', 1, false);
    }
  }, [visible, fetchItems]);

  // 处理物品选择
  const handleItemSelect = (item: Item) => {
    onSelect(item);
    onCancel();
  };

  // 无限滚动加载
  useEffect(() => {
    if (!loadingRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && hasMore && !loadingMore && !loadingItems) {
          fetchItems(searchValue, page + 1, true);
        }
      },
      { threshold: 0.1 }
    );

    observerRef.current = observer;
    observer.observe(loadingRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loadingMore, loadingItems, page, searchValue, fetchItems]);

  return (
    <Modal
      title={t('item.selectItem')}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={1000}
      bodyStyle={{ maxHeight: '70vh', overflow: 'hidden' }}
    >
      <div style={{ marginBottom: 16 }}>
        <Input
          placeholder={t('item.searchItemPlaceholder')}
          prefix={<SearchOutlined />}
          onChange={(e) => handleSearch(e.target.value)}
          allowClear
        />
      </div>

      <div style={{ height: 'calc(70vh - 120px)', overflow: 'auto', paddingRight: '8px' }}>
        {loadingItems ? (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>{t('item.searching')}</div>
          </div>
        ) : items.length === 0 ? (
          <Empty description={t('item.noMatchingItems')} />
        ) : (
          <>
            <List
              grid={{ gutter: 16, xs: 1, sm: 1, md: 1, lg: 1, xl: 1, xxl: 1 }}
              dataSource={items}
              renderItem={(item) => (
                <List.Item>
                  <Card
                    hoverable
                    style={{ cursor: 'pointer', width: '100%' }}
                    onClick={() => handleItemSelect(item)}
                    bodyStyle={{ padding: '16px', overflow: 'hidden' }}
                  >
                    <Row gutter={16} align="middle" style={{ width: '100%' }}>
                      <Col span={4} style={{ flexShrink: 0 }}>
                        {item.image_url ? (
                          <Image
                            src={item.image_url}
                            alt={item.name}
                            width={80}
                            height={80}
                            style={{ objectFit: 'cover' }}
                            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                          />
                        ) : (
                          <div
                            style={{
                              width: 80,
                              height: 80,
                              backgroundColor: '#f5f5f5',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              borderRadius: '4px'
                            }}
                          >
                            <Text type="secondary">无图片</Text>
                          </div>
                        )}
                      </Col>
                      <Col span={20} style={{ minWidth: 0 }}>
                        <div style={{ width: '100%', overflow: 'hidden' }}>
                          <div style={{ marginBottom: 8 }}>
                            <Title level={5} style={{ margin: 0, wordBreak: 'break-word' }}>
                              {item.code} - {item.name}
                            </Title>
                            {item.category_name && (
                              <Tag color="blue" style={{ marginTop: 4 }}>
                                {item.category_name}
                              </Tag>
                            )}
                          </div>
                          
                          <div style={{ marginBottom: 8 }}>
                            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                              {item.spec_material && (
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                  规格: {item.spec_material}
                                </Text>
                              )}
                              {item.size_dimension && (
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                  尺寸: {item.size_dimension}
                                </Text>
                              )}
                              {item.inventory_unit && (
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                  单位: {item.inventory_unit}
                                </Text>
                              )}
                              {item.brand && (
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                  品牌: {item.brand}
                                </Text>
                              )}
                            </div>
                          </div>
                          
                          {item.description && (
                            <Text 
                              type="secondary" 
                              style={{ 
                                fontSize: '12px',
                                wordBreak: 'break-word',
                                lineHeight: '1.4'
                              }}
                            >
                              {item.description}
                            </Text>
                          )}
                        </div>
                      </Col>
                    </Row>
                  </Card>
                </List.Item>
              )}
            />
            
            {/* 加载更多指示器 */}
            {hasMore && (
              <div 
                ref={loadingRef}
                style={{ 
                  textAlign: 'center', 
                  padding: '20px',
                  height: '60px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {loadingMore ? (
                  <Spin size="small" />
                ) : (
                  <Text type="secondary">滚动加载更多...</Text>
                )}
              </div>
            )}
          </>
        )}
      </div>
    </Modal>
  );
};

export default ItemSelector; 