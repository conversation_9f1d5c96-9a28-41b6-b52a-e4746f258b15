import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  Space,
  message,
  Popconfirm,
  Tag,
  Typography,
  Row,
  Col,
  Card,
  Empty,
  Checkbox,
  Switch
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  LineChartOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';
import type { ColumnsType } from 'antd/es/table';
import { 
  supplierPriceService,
  SupplierPrice,
  SupplierPriceCreate,
  ItemSupplier
} from '@admin/services/supplierService';
import PermissionGuard from '@shared/components/PermissionGuard';
import { PERMISSIONS } from '@shared/config/permissions';
import PriceTrendModal from './PriceTrendModal';
import { SUPPORTED_CURRENCIES } from '@shared/utils/exchangeRateUtils';

const { Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface PriceManagementProps {
  selectedItemSupplier: ItemSupplier | null;
  onRefresh: () => void;
}

const PriceManagement: React.FC<PriceManagementProps> = ({ selectedItemSupplier, onRefresh }) => {
  const { t } = useTranslation();
  const [prices, setPrices] = useState<SupplierPrice[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedPrice, setSelectedPrice] = useState<SupplierPrice | null>(null);
  const [form] = Form.useForm();
  const [showAllPrices, setShowAllPrices] = useState(false);
  const [priceTrendModalVisible, setPriceTrendModalVisible] = useState(false);
  const [isAddMode, setIsAddMode] = useState(false);
  const [defaultValidTo, setDefaultValidTo] = useState<dayjs.Dayjs | null>(null);
  const [selectedCurrency, setSelectedCurrency] = useState<string>('USD');

  // 获取价格列表
  const fetchPrices = useCallback(async () => {
    if (!selectedItemSupplier) return;
    
    setLoading(true);
    try {
      const response = await supplierPriceService.getPrices({
        item_supplier_id: selectedItemSupplier.id,
        status: showAllPrices ? null : 'active'
      });
      setPrices(response.items);
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('item.getPriceListFailed'));
    } finally {
      setLoading(false);
    }
  }, [selectedItemSupplier, showAllPrices]);

  useEffect(() => {
    fetchPrices();
  }, [fetchPrices]);

  // 监听模态框打开，设置默认值
  useEffect(() => {
    if (modalVisible && isAddMode && !editMode) {
      // 计算默认日期
      const today = dayjs();
      const threeYearsLater = today.add(3, 'year').subtract(1, 'day');
      
      console.log('设置默认日期:', {
        today: today.format(DATE_FORMATS.DATE_ONLY),
        threeYearsLater: threeYearsLater.format(DATE_FORMATS.DATE_ONLY)
      });
      
      // 设置默认值
      setTimeout(() => {
        form.setFieldsValue({
          status: true, // Switch组件使用boolean值

          valid_from: today, // 起始日期默认为今天
          valid_to: threeYearsLater // 截止日期默认为3年后前一天
        });
        
        // 设置状态
        setDefaultValidTo(threeYearsLater);
        
        console.log('默认值设置完成');
      }, 100);
    }
  }, [modalVisible, isAddMode, editMode, form]);

  // 处理添加价格
  const handleAddPrice = async (values: any) => {
    if (!selectedItemSupplier) return;
    
    try {
      const priceData = {
        ...values,
        item_supplier_id: selectedItemSupplier.id,
        valid_from: values.valid_from?.toISOString(),
        valid_to: values.valid_to?.toISOString(),

        status: values.status ? 'active' : 'inactive'
      };
      await supplierPriceService.createPrice(priceData);
              message.success(t('item.priceAddedSuccess'));
      setModalVisible(false);
      form.resetFields();
      form.setFieldsValue({ currency_code: 'USD', status: true });
      setSelectedCurrency('USD');
      fetchPrices();
      onRefresh();
    } catch (error: any) {
              message.error(error.response?.data?.detail || t('item.priceAddFailed'));
    }
  };

  // 处理编辑价格
  const handleEditPrice = async (values: any) => {
    if (!selectedPrice) return;
    
    try {
      const priceData = {
        ...values,
        valid_from: values.valid_from?.toISOString(),
        valid_to: values.valid_to?.toISOString(),

        status: values.status ? 'active' : 'inactive'
      };
      await supplierPriceService.updatePrice(selectedPrice.id, priceData);
              message.success(t('item.priceUpdatedSuccess'));
      setModalVisible(false);
      setEditMode(false);
      setSelectedPrice(null);
      form.resetFields();
      form.setFieldsValue({ currency_code: 'USD', status: true });
      setSelectedCurrency('USD');
      fetchPrices();
      onRefresh();
    } catch (error: any) {
              message.error(error.response?.data?.detail || t('item.priceUpdateFailed'));
    }
  };

  // 处理删除价格
  const handleDeletePrice = async (price: SupplierPrice) => {
    try {
      await supplierPriceService.deletePrice(price.id);
      message.success(t('item.priceDeletedSuccessfully'));
      fetchPrices();
      onRefresh();
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('item.priceDeleteFailed'));
    }
  };

  // 打开添加价格模态框
  const openAddPrice = () => {
    setEditMode(false);
    setSelectedPrice(null);
    setIsAddMode(true);
    
    // 重置表单
    form.resetFields();
    form.setFieldsValue({ currency_code: 'USD', status: true });
    setSelectedCurrency('USD');
    
    setModalVisible(true);
  };

  // 打开价格走势模态框
  const openPriceTrendModal = () => {
    setPriceTrendModalVisible(true);
  };

  // 设置最大范围截止日期
  const setMaxRangeDate = () => {
    const validFrom = form.getFieldValue('valid_from');
    console.log('当前起始日期:', validFrom);
    
    if (validFrom) {
      const maxDate = dayjs(validFrom).add(3, 'year').subtract(1, 'day');
      console.log('设置最大范围日期:', maxDate.format(DATE_FORMATS.DATE_ONLY));
      
      // 设置状态和表单值
      setDefaultValidTo(maxDate);
      form.setFieldsValue({ valid_to: maxDate });
      
              message.success(t('item.maxRangeEndDateSet'));
    } else {
              message.warning(t('item.pleaseSelectStartDateFirst'));
    }
  };

  // 检查价格是否过期
  const isPriceExpired = (price: SupplierPrice) => {
    const now = dayjs();
    const validTo = price.valid_to ? dayjs(price.valid_to) : null;
    
    if (validTo && validTo.isBefore(now)) {
      return true;
    }
    
    return false;
  };

  // 打开编辑价格模态框
  const openEditPrice = (price: SupplierPrice) => {
    setEditMode(true);
    setSelectedPrice(price);
    form.setFieldsValue({
      unit_price: price.unit_price,
      currency_code: price.currency_code || 'USD',
      min_quantity: price.min_quantity,
      max_quantity: price.max_quantity,

      valid_from: price.valid_from ? dayjs(price.valid_from) : null,
      valid_to: price.valid_to ? dayjs(price.valid_to) : null,
      status: price.status === 'active', // 转换为boolean值
      remarks: price.remarks
    });
    setSelectedCurrency(price.currency_code || 'USD');
    setModalVisible(true);
  };

  // 过滤和排序价格数据
  const filteredPrices = useMemo(() => {
    let filtered = prices.filter(price => {
      if (showAllPrices) {
        return true;
      }
      return price.status === 'active' && !isPriceExpired(price);
    });

    // 排序：有效的在前面，无效的在后面，然后按数量范围的起始值排序
    filtered.sort((a, b) => {
      // 当显示所有价格时，首先按状态排序：有效的在前面，无效的在后面
      if (showAllPrices) {
        const aIsValid = a.status === 'active' && !isPriceExpired(a);
        const bIsValid = b.status === 'active' && !isPriceExpired(b);
        
        if (aIsValid !== bIsValid) {
          return aIsValid ? -1 : 1;
        }
      }
      
      // 然后按数量范围的起始值排序
      return a.min_quantity - b.min_quantity;
    });

    return filtered;
  }, [prices, showAllPrices]);

  // 表格列定义
  const columns: ColumnsType<SupplierPrice> = [
    {
      title: t('item.unitPrice'),
      dataIndex: 'unit_price',
      key: 'unit_price',
      width: '12%',
      render: (price, record) => {
        const currency = SUPPORTED_CURRENCIES[record.currency_code as keyof typeof SUPPORTED_CURRENCIES];
        return `${currency?.symbol || record.currency_code} ${price}`;
      }
    },
    {
      title: t('item.currency'),
      dataIndex: 'currency_code',
      key: 'currency_code',
      width: '8%',
      render: (currencyCode) => {
        const currency = SUPPORTED_CURRENCIES[currencyCode as keyof typeof SUPPORTED_CURRENCIES];
        return (
          <Tag color={currencyCode === 'USD' ? 'green' : 'blue'}>
            {currency?.name || currencyCode}
          </Tag>
        );
      }
    },
    {
      title: t('item.quantityRange'),
      key: 'quantity_range',
      width: '12%',
      render: (_, record) => (
        <span>
          {record.min_quantity}
          {record.max_quantity && ` - ${record.max_quantity}`}
        </span>
      )
    },
    {
      title: t('item.validPeriod'),
      key: 'valid_period',
      width: '15%',
      render: (_, record) => (
        <div>
                                    <div>{t('item.startDate')}: {dayjs(record.valid_from).format(DATE_FORMATS.DATE_ONLY)}</div>
          {record.valid_to && (
            <div>{t('item.endDate')}: {dayjs(record.valid_to).format(DATE_FORMATS.DATE_ONLY)}</div>
          )}
        </div>
      )
    },
    {
      title: t('item.status'),
      dataIndex: 'status',
      key: 'status',
      width: '10%',
      render: (status, record) => {
        const isExpired = isPriceExpired(record);
        
        let displayStatus = '';
        let color = '';
        
        if (status === 'active') {
          if (isExpired) {
            displayStatus = t('item.expired');
            color = 'orange';
          } else {
            displayStatus = t('item.valid');
            color = 'green';
          }
        } else {
          displayStatus = t('item.invalid');
          color = 'red';
        }
        
        return (
          <Tag color={color}>
            {displayStatus}
          </Tag>
        );
      }
    },
    {
      title: t('item.updateTime'),
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: '12%',
      render: (updated_at) => (
        <span>
          {updated_at ? dayjs(updated_at).format(DATE_FORMATS.DATE_TIME_MINUTES) : '-'}
        </span>
      )
    },
    {
      title: t('item.operations'),
      key: 'actions',
      width: '10%',
      render: (_, record) => (
        <Space size="small">
          <PermissionGuard permission={PERMISSIONS.SUPPLIER.UPDATE}>
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => openEditPrice(record)}
                                  title={t('item.edit')}
            />
          </PermissionGuard>
          <PermissionGuard permission={PERMISSIONS.SUPPLIER.DELETE}>
            <Popconfirm
              title={t('item.confirmDeletePrice')}
              onConfirm={() => handleDeletePrice(record)}
              okText={t('messages.confirm')}
              cancelText={t('messages.cancel')}
            >
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
                                    title={t('item.delete')}
              />
            </Popconfirm>
          </PermissionGuard>
        </Space>
      )
    }
  ];

  return (
    <Card 
      title={t('item.priceManagement')} 
      style={{ 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column', 
        overflow: 'hidden'
      }}
      extra={
        selectedItemSupplier && (
          <Space>
            <Checkbox
              checked={showAllPrices}
              onChange={(e) => setShowAllPrices(e.target.checked)}
            >
              {t('item.showAllPrices')}
            </Checkbox>
            <PermissionGuard permission={PERMISSIONS.SUPPLIER.READ}>
              <Button 
                type="default" 
                icon={<LineChartOutlined />}
                size="small"
                onClick={openPriceTrendModal}
              >
                {t('item.showPriceTrend')}
              </Button>
            </PermissionGuard>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              size="small"
              onClick={openAddPrice}
            >
              {t('item.addPrice')}
            </Button>
          </Space>
        )
      }
      bodyStyle={{ flex: 1, padding: '12px 16px', overflow: 'hidden', display: 'flex', flexDirection: 'column', minHeight: 0 }}
    >
      {selectedItemSupplier ? (
        <div style={{ 
          height: '100%', 
          overflow: 'hidden', 
          display: 'flex', 
          flexDirection: 'column' 
        }}>
          <div style={{ flex: 1, overflow: 'hidden', minHeight: 0 }}>
            {filteredPrices.length === 0 ? (
              <div style={{ 
                height: '100%', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                color: '#999',
                fontSize: '14px'
              }}>
                {showAllPrices ? t('item.noPriceData') : t('item.noValidPriceData')}
              </div>
            ) : (
              <Table
                columns={columns}
                dataSource={filteredPrices}
                rowKey="id"
                loading={loading}
                pagination={false}
                scroll={{ y: 300, x: 800 }}
                size="small"
              />
            )}
          </div>
        </div>
      ) : (
        <div style={{ 
          textAlign: 'center', 
          padding: '40px 0',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%'
        }}>
          <Empty 
            description={
              <Text type="secondary">请从左侧选择一个物品管理价格</Text>
            }
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </div>
      )}

      {/* 价格编辑模态框 */}
      <Modal
        title={editMode ? t('item.editPrice') : t('item.addPrice')}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditMode(false);
          setSelectedPrice(null);
          setIsAddMode(false);
          setDefaultValidTo(null);
          setSelectedCurrency('USD');
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={editMode ? handleEditPrice : handleAddPrice}
          initialValues={{
            status: true,
            currency_code: 'USD'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="unit_price"
                label={t('item.unitPrice')}
                rules={[{ required: true, message: t('item.pleaseEnterUnitPrice') }]}
              >
                <InputNumber
                  placeholder={t('item.unitPrice')}
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  addonAfter={selectedCurrency ? SUPPORTED_CURRENCIES[selectedCurrency as keyof typeof SUPPORTED_CURRENCIES]?.symbol : '$'}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="currency_code"
                label={t('item.currency')}
                rules={[{ required: true, message: t('item.pleaseSelectCurrency') }]}
                initialValue="USD"
              >
                <Select 
                  placeholder={t('item.pleaseSelectCurrency')}
                  showSearch
                  optionFilterProp="children"
                  value={selectedCurrency}
                  onChange={(value) => {
                    setSelectedCurrency(value);
                    form.validateFields(['unit_price']);
                  }}
                >
                  {Object.entries(SUPPORTED_CURRENCIES).map(([code, currency]) => (
                    <Option key={code} value={code}>
                      <Space>
                        <Tag color={code === 'USD' ? 'green' : 'blue'} style={{ margin: 0 }}>
                          {currency.symbol}
                        </Tag>
                        <span>{code}</span>
                        <span style={{ color: '#666' }}>({currency.name})</span>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="min_quantity"
                label={t('item.minQuantity')}
                rules={[{ required: true, message: t('item.pleaseEnterMinQuantity') }]}
              >
                <InputNumber
                                      placeholder={t('item.minQuantity')}
                  style={{ width: '100%' }}
                  min={1}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="max_quantity"
                label={t('item.maxQuantity')}
              >
                <InputNumber
                                      placeholder={t('item.maxQuantity')}
                  style={{ width: '100%' }}
                  min={1}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="valid_from"
                label={t('item.startDate')}
                rules={[{ required: true, message: t('item.pleaseSelectStartDate') }]}
              >
                                <DatePicker 
                  style={{ width: '100%' }} 
                  format={DATE_FORMATS.DATE_ONLY}
                  placeholder={t('item.pleaseSelectStartDate')}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="valid_to"
                label={t('item.endDate')}
                rules={[{ required: true, message: t('item.pleaseSelectEndDate') }]}
              >
                <div style={{ display: 'flex', gap: '8px' }}>
                                    <DatePicker 
                    style={{ flex: 1 }} 
                    format={DATE_FORMATS.DATE_ONLY}
                    placeholder={t('item.pleaseSelectEndDate')}
                    allowClear={false}
                    value={defaultValidTo}
                    onChange={(date) => {
                      setDefaultValidTo(date);
                      form.setFieldsValue({ valid_to: date });
                    }}
                  />
                  <Button 
                    type="default" 
                    size="small" 
                    onClick={setMaxRangeDate}
                    style={{ whiteSpace: 'nowrap' }}
                  >
                    最大范围
                  </Button>
                </div>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="status"
                          label={t('item.status')}
                            rules={[{ required: true, message: t('item.pleaseSelectStatus') }]}
          >
            <Switch
                              checkedChildren={t('item.valid')}
                              unCheckedChildren={t('item.invalid')}
              defaultChecked={true}
            />
          </Form.Item>

          <Form.Item
            name="remarks"
                          label={t('item.remarks')}
          >
                          <TextArea rows={3} placeholder={t('item.pleaseEnterRemarks')} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editMode ? t('item.update') : t('item.add')}
              </Button>
              <Button onClick={() => {
                setModalVisible(false);
                setEditMode(false);
                setSelectedPrice(null);
                setIsAddMode(false);
                setDefaultValidTo(null);
                setSelectedCurrency('USD');
                form.resetFields();
                form.setFieldsValue({ currency_code: 'USD', status: true });
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 价格走势模态框 */}
      <PriceTrendModal
        visible={priceTrendModalVisible}
        itemSupplierId={selectedItemSupplier?.id || 0}
        onCancel={() => setPriceTrendModalVisible(false)}
      />
    </Card>
  );
};

export default PriceManagement; 