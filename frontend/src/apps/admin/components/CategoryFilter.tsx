import React, { useState, useEffect, useRef } from 'react';
import { Spin, Empty } from 'antd';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import { apiClient } from '../services/authService';

interface PrimaryCategory {
  id: number;
  name: string;
  description?: string;
  code_prefix: string;
  is_active: boolean;
}

interface Category {
  id: number;
  name: string;
  description?: string;
  primary_category_id: number;
  is_active: boolean;
  primary_category?: PrimaryCategory;
}

interface CategoryFilterProps {
  selectedPrimaryCategory?: number;
  selectedCategory?: number;
  onPrimaryCategorySelect: (primaryCategoryId: number | undefined) => void;
  onCategorySelect: (categoryId: number | undefined) => void;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  selectedPrimaryCategory,
  selectedCategory,
  onPrimaryCategorySelect,
  onCategorySelect,
}) => {
  const { t } = useTranslation();
  const [primaryCategories, setPrimaryCategories] = useState<PrimaryCategory[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [hoveredPrimaryId, setHoveredPrimaryId] = useState<number | null>(null);
  const [hideTimeout, setHideTimeout] = useState<NodeJS.Timeout | null>(null);
  const [panelPosition, setPanelPosition] = useState({ top: 120, left: 244 });
  const panelRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isMouseInPanel = useRef(false);
  const isMouseInCategoryItem = useRef(false);
  const lastHoveredPrimaryId = useRef<number | null>(null);
  const mouseTrackingTimeout = useRef<NodeJS.Timeout | null>(null);

  // 获取所有分类数据
  const fetchAllCategories = async () => {
    setLoading(true);
    try {
      // 并行获取一级分类和所有二级分类
      const [primaryResponse, categoriesResponse] = await Promise.all([
        apiClient.get('/items/primary-categories'),
        apiClient.get('/items/categories')
      ]);
      
      const primaryCategories = primaryResponse.data.filter((cat: PrimaryCategory) => cat.is_active);
      const categories = categoriesResponse.data.filter((cat: Category) => cat.is_active);
      
      // 为每个二级分类添加一级分类信息
      const categoriesWithPrimary = categories.map((category: Category) => {
        const primaryCategory = primaryCategories.find((pc: PrimaryCategory) => pc.id === category.primary_category_id);
        return {
          ...category,
          primary_category: primaryCategory
        };
      });
      
      // 按一级分类 -> 二级分类进行排序
      const sortedCategories = categoriesWithPrimary.sort((a: Category & { primary_category?: PrimaryCategory }, b: Category & { primary_category?: PrimaryCategory }) => {
        // 首先按一级分类名称排序
        const primaryA = a.primary_category?.name || '';
        const primaryB = b.primary_category?.name || '';
        const primaryCompare = primaryA.localeCompare(primaryB, 'zh-CN');
        
        if (primaryCompare !== 0) {
          return primaryCompare;
        }
        
        // 一级分类相同时，按二级分类名称排序
        return a.name.localeCompare(b.name, 'zh-CN');
      });
      
      setPrimaryCategories(primaryCategories);
      setCategories(sortedCategories);
    } catch (error) {
      console.error('获取分类数据错误:', error);
      message.error(t('categoryManagement.getCategoryDataFailed'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAllCategories();
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (hideTimeout) {
        clearTimeout(hideTimeout);
      }
      if (mouseTrackingTimeout.current) {
        clearTimeout(mouseTrackingTimeout.current);
      }
    };
  }, [hideTimeout]);

  // 清除隐藏定时器
  const clearHideTimeout = () => {
    if (hideTimeout) {
      clearTimeout(hideTimeout);
      setHideTimeout(null);
    }
    if (mouseTrackingTimeout.current) {
      clearTimeout(mouseTrackingTimeout.current);
      mouseTrackingTimeout.current = null;
    }
  };

  // 处理鼠标悬停一级分类
  const handlePrimaryHover = (primaryId: number, event: React.MouseEvent) => {
    
    // 清除之前的隐藏定时器
    clearHideTimeout();
    
    // 计算面板位置 - 优化定位逻辑
    const rect = event.currentTarget.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const panelHeight = 400; // 预估面板高度
    
    // 确保面板不会超出视口底部
    let panelTop = Math.max(120, rect.top - 20);
    if (panelTop + panelHeight > viewportHeight - 40) {
      panelTop = viewportHeight - panelHeight - 40;
    }
    
    // 计算水平位置，让弹出窗对齐到分类项的右边缘
    const panelLeft = rect.right;
    
    setPanelPosition({ top: panelTop, left: panelLeft });
    
    setHoveredPrimaryId(primaryId);
    lastHoveredPrimaryId.current = primaryId;
    isMouseInCategoryItem.current = true;
  };

  // 处理鼠标进入分类项
  const handleCategoryItemEnter = (primaryId: number) => {
    isMouseInCategoryItem.current = true;
    clearHideTimeout();
  };

  // 处理鼠标离开分类项
  const handleCategoryItemLeave = (primaryId: number) => {
    isMouseInCategoryItem.current = false;
    
    // 使用更短的延迟，提高响应性
    const timeout = setTimeout(() => {
      // 检查鼠标是否在面板内或仍在分类项内
      if (!isMouseInPanel.current && !isMouseInCategoryItem.current) {
        setHoveredPrimaryId(null);
        lastHoveredPrimaryId.current = null;
      }
    }, 150); // 减少延迟时间到150ms，提高响应性
    setHideTimeout(timeout);
  };

  // 处理二级分类点击
  const handleCategoryClick = (categoryId: number) => {
    onCategorySelect(categoryId);
    
    // 选择后立即隐藏弹出窗
    setHoveredPrimaryId(null);
    lastHoveredPrimaryId.current = null;
    clearHideTimeout();
  };

  // 清除选择
  const handleClearSelection = () => {
    onPrimaryCategorySelect(undefined);
    onCategorySelect(undefined);
  };

  // 获取当前选中的分类名称
  const getSelectedCategoryName = () => {
    if (selectedCategory) {
      const category = categories.find(cat => cat.id === selectedCategory);
      if (category) {
        return `${category.primary_category?.name} > ${category.name}`;
      }
    }
    return null;
  };

  // 处理整个容器的鼠标移动
  const handleContainerMouseMove = (event: React.MouseEvent) => {
    if (!hoveredPrimaryId) return;
    
    // 清除之前的跟踪定时器
    if (mouseTrackingTimeout.current) {
      clearTimeout(mouseTrackingTimeout.current);
    }
    
    // 检查鼠标是否在面板或分类项区域内
    const containerRect = containerRef.current?.getBoundingClientRect();
    const panelRect = panelRef.current?.getBoundingClientRect();
    
    if (containerRect && panelRect) {
      const mouseX = event.clientX;
      const mouseY = event.clientY;
      
      // 检查鼠标是否在分类项区域内
      const inCategoryArea = mouseX >= containerRect.left && mouseX <= containerRect.left + 220; // 分类区域宽度
      
      // 检查鼠标是否在面板区域内
      const inPanelArea = mouseX >= panelRect.left - 10 && mouseX <= panelRect.right + 10 && 
                         mouseY >= panelRect.top - 10 && mouseY <= panelRect.bottom + 10;
      
      if (inCategoryArea || inPanelArea) {
        // 鼠标在安全区域内，保持面板显示
        clearHideTimeout();
      } else {
        // 鼠标离开安全区域，设置延迟隐藏
        mouseTrackingTimeout.current = setTimeout(() => {
          if (!isMouseInPanel.current && !isMouseInCategoryItem.current) {
            setHoveredPrimaryId(null);
            lastHoveredPrimaryId.current = null;
          }
        }, 200);
      }
    }
  };

  return (
    <div 
      className="category-container" 
      ref={containerRef}
      onMouseMove={handleContainerMouseMove}
    >
      {/* 左侧一级分类导航 */}
      <div className="main-category">
        <div className="category-header">
          <span className="header-title">{t('categoryManagement.productCategory')}</span>
        </div>
        
        <div className="category-list">
          {loading ? (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Spin />
            </div>
          ) : primaryCategories.length === 0 ? (
            <Empty description={t('categoryManagement.noCategoryData')} />
          ) : (
            primaryCategories.map(primaryCat => (
              <div 
                key={primaryCat.id}
                className={`category-item ${hoveredPrimaryId === primaryCat.id ? 'hovered' : ''} ${selectedPrimaryCategory === primaryCat.id ? 'selected' : ''}`}
                onMouseEnter={(e) => {
                  handleCategoryItemEnter(primaryCat.id);
                  handlePrimaryHover(primaryCat.id, e);
                }}
                onMouseLeave={() => handleCategoryItemLeave(primaryCat.id)}
              >
                <span className="category-name">{primaryCat.name}</span>
                <span className="category-arrow">▶</span>
              </div>
            ))
          )}
        </div>

        {/* 当前筛选提示 - 放在分类列表下方 */}
        {getSelectedCategoryName() && (
          <div className="current-filter">
            <div className="filter-info">
              <strong>{t('categoryManagement.currentFilter')}</strong>{getSelectedCategoryName()}
            </div>
            <button className="clear-button" onClick={handleClearSelection}>
              {t('categoryManagement.clear')}
            </button>
          </div>
        )}
      </div>

      {/* 右侧悬浮二级分类面板 */}
      {hoveredPrimaryId && (
        <div 
          ref={panelRef}
          className="sub-category-panel"
          style={{ top: `${panelPosition.top}px`, left: `${panelPosition.left}px` }}
          onMouseEnter={() => {
            isMouseInPanel.current = true;
            clearHideTimeout();
          }}
          onMouseLeave={() => {
            isMouseInPanel.current = false;
            // 面板离开时也设置延迟
            const timeout = setTimeout(() => {
              if (!isMouseInPanel.current && !isMouseInCategoryItem.current) {
                setHoveredPrimaryId(null);
                lastHoveredPrimaryId.current = null;
              }
            }, 150);
            setHideTimeout(timeout);
          }}
        >
            {/* 二级分类分组 */}
            <div className="category-group">
              <div className="items">
                {categories
                  .filter(cat => cat.primary_category_id === hoveredPrimaryId)
                  .map(cat => (
                    <span 
                      key={cat.id}
                      className={`item-link ${selectedCategory === cat.id ? 'selected' : ''}`}
                      onClick={() => handleCategoryClick(cat.id)}
                    >
                      {cat.name}
                    </span>
                  ))
                }
              </div>
            </div>
          </div>
      )}
    </div>
  );
};

export default CategoryFilter; 