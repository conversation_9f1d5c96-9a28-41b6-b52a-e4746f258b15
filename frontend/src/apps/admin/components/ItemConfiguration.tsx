import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  InputNumber,
  Select,
  Button,
  Row,
  Col,
  message,
  Typography
} from 'antd';
import { SaveOutlined } from '@ant-design/icons';
import { 
  ItemSupplier,
  ItemSupplierUpdate,
  supplierService
} from '@admin/services/supplierService';
import { apiClient } from '../services/authService';

const { Option } = Select;
const { Text } = Typography;

interface ItemConfigurationProps {
  selectedItemSupplier: ItemSupplier | null;
  onRefresh: () => void;
}



const ItemConfiguration: React.FC<ItemConfigurationProps> = ({ 
  selectedItemSupplier, 
  onRefresh 
}) => {
  const { t } = useTranslation();
  const [editForm] = Form.useForm();
  const [saving, setSaving] = useState(false);

  const [isFormModified, setIsFormModified] = useState(false);



  // 当选中物品变化时，更新表单数据
  useEffect(() => {
    if (selectedItemSupplier) {
      editForm.setFieldsValue({
        delivery_days: selectedItemSupplier.delivery_days,
        quality_rating: selectedItemSupplier.quality_rating,
        spq: selectedItemSupplier.spq,
        moq: selectedItemSupplier.moq,
        status: selectedItemSupplier.status,
        priority: selectedItemSupplier.priority // 添加优先级字段
      });
      // 重置表单修改状态
      setIsFormModified(false);
    } else {
      editForm.resetFields();
      setIsFormModified(false);
    }
  }, [selectedItemSupplier]);



  // 保存物品信息
  const handleSaveItem = async (values: any) => {
    if (!selectedItemSupplier) return;
    setSaving(true);
    try {
      await supplierService.updateItemSupplier(selectedItemSupplier.id, values);
      message.success(t('item.itemInfoUpdatedSuccessfully'));
      // 重置表单修改状态
      setIsFormModified(false);
      onRefresh();
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('item.updateFailed'));
    } finally {
      setSaving(false);
    }
  };



  return (
    <Card 
      title={t('item.itemConfiguration')} 
      bodyStyle={{ padding: '16px' }}
      extra={
        selectedItemSupplier && (
          <Button 
            type="primary" 
            icon={<SaveOutlined />}
            loading={saving}
            disabled={!isFormModified}
            onClick={() => editForm.submit()}
            size="small"
          >
                          {t('item.saveConfiguration')}
          </Button>
        )
      }
      style={{ marginBottom: '8px' }}
    >
      {selectedItemSupplier ? (
        <div>
          <Form
            form={editForm}
            layout="horizontal"
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            onFinish={handleSaveItem}
            onValuesChange={() => {
              if (selectedItemSupplier) {
                const currentValues = editForm.getFieldsValue();
                const originalValues = {
                  delivery_days: selectedItemSupplier.delivery_days,
                  quality_rating: selectedItemSupplier.quality_rating,
                  spq: selectedItemSupplier.spq,
                  moq: selectedItemSupplier.moq,
                  status: selectedItemSupplier.status,
                  priority: selectedItemSupplier.priority // 添加优先级字段
                };
                
                // 比较当前值和原始值
                const hasChanged = 
                  currentValues.delivery_days !== originalValues.delivery_days ||
                  currentValues.quality_rating !== originalValues.quality_rating ||
                  currentValues.spq !== originalValues.spq ||
                  currentValues.moq !== originalValues.moq ||
                  currentValues.status !== originalValues.status ||
                  currentValues.priority !== originalValues.priority; // 添加优先级比较
                
                setIsFormModified(hasChanged);
              }
            }}
            size="small"
          >
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label={t('item.priority')}
                >
                  <div style={{ 
                    padding: '4px 11px', 
                    border: '1px solid #d9d9d9', 
                    borderRadius: '6px',
                    backgroundColor: '#f5f5f5',
                    color: '#666'
                  }}>
                    {selectedItemSupplier?.priority === 0 ? t('item.preferred') : 
                     selectedItemSupplier?.priority ? `${t('item.alternative')}${selectedItemSupplier.priority}` : 
                     t('item.notSet')}
                  </div>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="spq"
                  label="SPQ"
                  rules={[
                            { required: true, message: t('item.pleaseEnterSPQ') },
        { type: 'integer', message: t('item.spqMustBeInteger') },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        // 确保值是数字类型
                        const numValue = Number(value);
                        if (isNaN(numValue) || numValue <= 0) {
                          return Promise.reject(new Error(t('item.spqMustBeGreaterThanZero')));
                        }
                        
                        const moq = getFieldValue('moq');
                        if (!moq) {
                          return Promise.resolve();
                        }
                        
                        const numMoq = Number(moq);
                        if (isNaN(numMoq) || numMoq <= 0) {
                          return Promise.resolve();
                        }
                        
                        if (numMoq % numValue !== 0) {
                          return Promise.reject(new Error(t('item.moqMustBeMultipleOfSPQ', { moq: numMoq })));
                        }
                        return Promise.resolve();
                      },
                    }),
                  ]}
                >
                  <InputNumber
                    placeholder={t('item.standardPackagingQuantity')}
                    style={{ width: '100%' }}
                    min={1}
                    step={1}
                    precision={0}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="moq"
                  label="MOQ"
                  rules={[
                            { required: true, message: t('item.pleaseEnterMOQ') },
        { type: 'integer', message: t('item.moqMustBeInteger') },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        // 确保值是数字类型
                        const numValue = Number(value);
                        if (isNaN(numValue) || numValue <= 0) {
                          return Promise.reject(new Error(t('item.moqMustBeGreaterThanZero')));
                        }
                        
                        const spq = getFieldValue('spq');
                        if (!spq) {
                          return Promise.resolve();
                        }
                        
                        const numSpq = Number(spq);
                        if (isNaN(numSpq) || numSpq <= 0) {
                          return Promise.resolve();
                        }
                        
                        if (numValue % numSpq !== 0) {
                          return Promise.reject(new Error(`MOQ必须是SPQ的整数倍，当前SPQ: ${numSpq}`));
                        }
                        return Promise.resolve();
                      },
                    }),
                  ]}
                >
                  <InputNumber
                    placeholder={t('item.minimumOrderQuantity')}
                    style={{ width: '100%' }}
                    min={1}
                    step={1}
                    precision={0}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="delivery_days"
                                label={t('item.deliveryDays')}
              rules={[{ required: true, message: t('item.pleaseEnterDeliveryDays') }]}
                >
                  <InputNumber
                    placeholder={t('item.deliveryDays')}
                    style={{ width: '100%' }}
                    min={1}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="quality_rating"
                                label={t('item.qualityRating')}
              rules={[{ required: true, message: t('item.pleaseSelectQualityRating') }]}
                >
                  <Select style={{ width: '100%' }}>
                                    <Option value={1}>{t('item.oneStar')}</Option>
                <Option value={2}>{t('item.twoStar')}</Option>
                <Option value={3}>{t('item.threeStar')}</Option>
                <Option value={4}>{t('item.fourStar')}</Option>
                <Option value={5}>{t('item.fiveStar')}</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="status"
                                label={t('item.status')}
              rules={[{ required: true, message: t('item.pleaseSelectStatus') }]}
                >
                  <Select style={{ width: '100%' }}>
                                    <Option value="active">{t('item.active')}</Option>
                <Option value="inactive">{t('item.inactive')}</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      ) : (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">请从左侧选择一个物品进行配置</Text>
        </div>
      )}
    </Card>
  );
};

export default ItemConfiguration; 