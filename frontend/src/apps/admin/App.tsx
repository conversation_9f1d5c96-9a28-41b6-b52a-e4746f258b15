import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';
import { useTranslation } from 'react-i18next';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import PermissionRoute from '@shared/components/PermissionRoute';
import { MODULE_PERMISSIONS } from '@shared/config/permissions';
import DashboardLayout from '@shared/components/DashboardLayout';
import SuperAdminRoute from './components/SuperAdminRoute';

// 导入Admin页面
import LoginPage from './pages/LoginPage';
import ItemManagement from './pages/ItemManagement';
import ItemDetail from './pages/ItemDetail';
import ItemEdit from './pages/ItemEdit';
import CategoryManagement from './pages/CategoryManagement';
import PrimaryCategoryManagement from './pages/PrimaryCategoryManagement';
import SupplierManagement from './pages/SupplierManagement';
import SupplierDetail from './pages/SupplierDetail';
import SupplierEdit from './pages/SupplierEdit';
import SupplierItems from './pages/SupplierItems';
import SupplierItemPrices from './pages/SupplierItemPrices';
import InventoryManagement from './pages/InventoryManagement';
import PurchaseCart from './pages/PurchaseCart';
import PurchaseWorkbench from './pages/PurchaseWorkbench';
import EditPurchaseRequest from './pages/EditPurchaseRequest';
import PurchaseRequestDetail from './pages/PurchaseRequestDetail';
import PurchaseRequestExportPDF from './pages/PurchaseRequestExportPDF';
import PurchaseRequestSummary from './pages/PurchaseRequestSummary';
import PurchaseExecution from './pages/PurchaseExecution';
import PurchaseExecutionBatchDetail from './pages/PurchaseExecutionBatchDetail';
import TimeManipulation from './pages/TimeManipulation';

import ReportsDashboard from './pages/ReportsDashboard';
import UserManagement from './pages/UserManagement';
import RoleManagement from './pages/RoleManagement';
import RoleDetail from './pages/RoleDetail';
import DepartmentManagement from './pages/DepartmentManagement';
import DepartmentDetail from './pages/DepartmentDetail';
import ImageManagement from './pages/ImageManagement';
import NotificationList from './pages/NotificationList';
import NotificationDetail from './pages/NotificationDetail';
import NotificationConfig from './pages/NotificationConfig';
import NotificationManagement from './pages/NotificationManagement';
import ExchangeRateManagement from './pages/ExchangeRateManagement';

// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  
  if (loading) {
    return <div>加载中...</div>;
  }
  
  return isAuthenticated ? <>{children}</> : <Navigate to="/admin/login" />;
};

// Admin主应用组件
const AdminMainApp: React.FC = () => {
  const { i18n } = useTranslation();
  
  // 根据当前语言选择Ant Design的locale
  const antdLocale = i18n.language === 'zh' ? zhCN : enUS;

  return (
    <ConfigProvider locale={antdLocale}>
      <Routes>
        {/* 其他页面使用DashboardLayout */}
        <Route path="*" element={
          <ProtectedRoute>
            <DashboardLayout>
              <Suspense fallback={<div>加载中...</div>}>
                <Routes>
                  <Route index element={<Navigate to="items" replace />} />
                  
                  {/* 物品管理 */}
                  <Route path="items" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.ITEMS}>
                      <ItemManagement />
                    </PermissionRoute>
                  } />
                  <Route path="items/new" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.ITEMS}>
                      <ItemEdit />
                    </PermissionRoute>
                  } />
                  <Route path="items/:id" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.ITEMS}>
                      <ItemDetail />
                    </PermissionRoute>
                  } />
                  <Route path="items/:id/edit" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.ITEMS}>
                      <ItemEdit />
                    </PermissionRoute>
                  } />
                  
                  {/* 分类管理 */}
                  <Route path="categories" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.CATEGORIES}>
                      <CategoryManagement />
                    </PermissionRoute>
                  } />
                  <Route path="primary-categories" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.ITEMS}>
                      <PrimaryCategoryManagement />
                    </PermissionRoute>
                  } />
                  
                  {/* 供应商管理 */}
                  <Route path="suppliers" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.SUPPLIERS}>
                      <SupplierManagement />
                    </PermissionRoute>
                  } />
                  <Route path="suppliers/:id" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.SUPPLIERS}>
                      <SupplierDetail />
                    </PermissionRoute>
                  } />
                  <Route path="suppliers/new" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.SUPPLIERS}>
                      <SupplierEdit />
                    </PermissionRoute>
                  } />
                  <Route path="suppliers/:id/edit" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.SUPPLIERS}>
                      <SupplierEdit />
                    </PermissionRoute>
                  } />
                  <Route path="suppliers/:id/items" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.SUPPLIERS}>
                      <SupplierItems />
                    </PermissionRoute>
                  } />
                  <Route path="suppliers/:id/items/:itemId" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.SUPPLIERS}>
                      <SupplierItems />
                    </PermissionRoute>
                  } />
                  <Route path="suppliers/:id/items/:itemId/prices" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.SUPPLIERS}>
                      <SupplierItemPrices />
                    </PermissionRoute>
                  } />
                  
                  {/* 库存管理 */}
                  <Route path="inventory" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.INVENTORY}>
                      <InventoryManagement />
                    </PermissionRoute>
                  } />
                  
                  {/* 采购工作台 */}
                  <Route path="purchase-requests" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.PURCHASE_REQUESTS}>
                      <PurchaseWorkbench />
                    </PermissionRoute>
                  } />
                  
                  {/* 购物车管理 */}
                  <Route path="purchase-cart" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.PURCHASE_REQUESTS}>
                      <PurchaseCart />
                    </PermissionRoute>
                  } />
                  
                  {/* 编辑采购申请 */}
                  <Route path="purchase-requests/:id/edit" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.PURCHASE_REQUESTS}>
                      <EditPurchaseRequest />
                    </PermissionRoute>
                  } />
                  <Route path="purchase-requests/:id" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.PURCHASE_REQUESTS}>
                      <PurchaseRequestDetail />
                    </PermissionRoute>
                  } />
                  
                  {/* PDF导出页面 - 独立布局，不显示系统菜单 */}
                  <Route path="purchase-requests/:id/export-pdf" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.PURCHASE_REQUESTS}>
                      <PurchaseRequestExportPDF />
                    </PermissionRoute>
                  } />
                  
                  {/* 采购申请汇总分析 */}
                  <Route path="purchase-requests/summary" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.PURCHASE_REQUESTS}>
                      <PurchaseRequestSummary />
                    </PermissionRoute>
                  } />
                  
                  {/* 采购执行 */}
                  <Route path="purchase-execution" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.PURCHASE_EXECUTION}>
                      <PurchaseExecution />
                    </PermissionRoute>
                  } />
                  <Route path="purchase-execution/:batchId" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.PURCHASE_EXECUTION}>
                      <PurchaseExecutionBatchDetail />
                    </PermissionRoute>
                  } />
                  {/* Handle old format directly */}
                  <Route path="purchase-execution/:batchId" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.PURCHASE_EXECUTION}>
                      <PurchaseExecutionBatchDetail />
                    </PermissionRoute>
                  } />

                  
                  {/* 报表仪表板 */}
                  <Route path="reports" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.REPORTS}>
                      <ReportsDashboard />
                    </PermissionRoute>
                  } />
                  
                  {/* 用户管理 */}
                  <Route path="users" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.USERS}>
                      <UserManagement />
                    </PermissionRoute>
                  } />
                  
                  {/* 角色管理 */}
                  <Route path="roles" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.ROLES}>
                      <RoleManagement />
                    </PermissionRoute>
                  } />
                  <Route path="roles/:id" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.ROLES}>
                      <RoleDetail />
                    </PermissionRoute>
                  } />
                  
                  {/* 部门管理 */}
                  <Route path="departments" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.DEPARTMENTS}>
                      <DepartmentManagement />
                    </PermissionRoute>
                  } />
                  <Route path="departments/:id" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.DEPARTMENTS}>
                      <DepartmentDetail />
                    </PermissionRoute>
                  } />
                  
                  {/* 图片管理 */}
                  <Route path="image-management" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.SYSTEM_MANAGEMENT}>
                      <ImageManagement />
                    </PermissionRoute>
                  } />

                  {/* 汇率管理 */}
                  <Route path="exchange-rates" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.SYSTEM_MANAGEMENT}>
                      <ExchangeRateManagement />
                    </PermissionRoute>
                  } />

                  {/* 申请单时间修改 - 仅限super_admin角色 */}
                  <Route path="purchase-requests/:id/time-manipulation" element={
                    <SuperAdminRoute>
                      <TimeManipulation />
                    </SuperAdminRoute>
                  } />
                  
                  {/* 通知管理 */}
                  <Route path="notifications" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.NOTIFICATIONS}>
                      <NotificationList />
                    </PermissionRoute>
                  } />
                  <Route path="notifications/:id" element={
                    <PermissionRoute permissions={MODULE_PERMISSIONS.NOTIFICATIONS}>
                      <NotificationDetail />
                    </PermissionRoute>
                  } />
                  
                  {/* 通知配置 - 仅限super_admin角色 */}
                  <Route path="notification-config" element={
                    <SuperAdminRoute>
                      <NotificationConfig />
                    </SuperAdminRoute>
                  } />
                  
                  {/* 通知管理 - 仅限super_admin角色 */}
                  <Route path="notification-management" element={
                    <SuperAdminRoute>
                      <NotificationManagement />
                    </SuperAdminRoute>
                  } />
                </Routes>
              </Suspense>
            </DashboardLayout>
          </ProtectedRoute>
        } />
      </Routes>
    </ConfigProvider>
  );
};

export default function AdminApp() {
  return (
    <AuthProvider>
      <Routes>
        <Route path="login" element={<LoginPage />} />
        <Route path="*" element={<AdminMainApp />} />
      </Routes>
    </AuthProvider>
  );
}
