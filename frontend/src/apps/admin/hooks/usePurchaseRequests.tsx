import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { useAuth } from '../contexts/AuthContext';
import { purchaseRequestService, PurchaseRequest, RequestSummary } from '@admin/services/purchaseRequestService';

export interface RequestStats {
  totalRequests: number;
  pendingRequests: number;
  completedRequests: number;
  totalAmount: number;
  reviewPending: number;
  principlePending: number;
  finalPending: number;
}

export const usePurchaseRequests = () => {
  const { user, hasPermission } = useAuth();
  const [requests, setRequests] = useState<PurchaseRequest[]>([]);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<RequestStats>({
    totalRequests: 0,
    pendingRequests: 0,
    completedRequests: 0,
    totalAmount: 0,
    reviewPending: 0,
    principlePending: 0,
    finalPending: 0
  });

  // 获取申请列表
  const fetchRequests = useCallback(async () => {
    try {
      setLoading(true);
      
      // 调用真实的API服务
      const response = await purchaseRequestService.getPurchaseRequests();
      
      // 根据用户权限过滤数据
      let filteredRequests = response;
      
      // 如果用户没有查看所有申请的权限，只显示本部门的申请
      if (!hasPermission('purchase.read_all')) {
        filteredRequests = response.filter(req => 
          req.department_id === user?.department_id || 
          hasPermission('purchase.read_department')
        );
      }

      setRequests(filteredRequests);
      
      // 计算统计数据
      calculateStats(filteredRequests);
      
    } catch (error) {
      console.error('获取申请列表失败:', error);
      message.error('获取申请列表失败');
    } finally {
      setLoading(false);
    }
  }, [user, hasPermission]);

  // 获取申请统计信息
  const fetchStats = useCallback(async () => {
    try {
      const departmentId = hasPermission('purchase.read_all') ? undefined : user?.department_id;
      const summary = await purchaseRequestService.getRequestsSummary(departmentId);
      
      setStats({
        totalRequests: summary.total_requests,
        pendingRequests: summary.under_review + summary.under_principle_approval + summary.under_final_approval,
        completedRequests: summary.approved,
        totalAmount: 0, // 这个需要从申请明细计算
        reviewPending: summary.under_review,
        principlePending: summary.under_principle_approval,
        finalPending: summary.under_final_approval
      });
    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  }, [user, hasPermission]);

  // 计算统计数据
  const calculateStats = useCallback((requestList: PurchaseRequest[]) => {
    const totalRequests = requestList.length;
    const pendingRequests = requestList.filter(req => 
      ['under_review', 'under_principle_approval', 'under_final_approval'].includes(req.status)
    ).length;
    const completedRequests = requestList.filter(req => req.status === 'approved').length;
    const totalAmount = requestList.reduce((sum, req) => sum + Number(req.final_total || 0), 0);
    const reviewPending = requestList.filter(req => req.status === 'under_review').length;
    const principlePending = requestList.filter(req => req.status === 'under_principle_approval').length;
    const finalPending = requestList.filter(req => req.status === 'under_final_approval').length;

    setStats({
      totalRequests,
      pendingRequests,
      completedRequests,
      totalAmount,
      reviewPending,
      principlePending,
      finalPending
    });
  }, []);

  // 创建申请
  const createRequest = useCallback(async (requestData: Partial<PurchaseRequest>) => {
    try {
      setLoading(true);
      
      // 调用真实的API服务
      const newRequest = await purchaseRequestService.createRequestFromCart(requestData as any);
      
      setRequests(prev => [newRequest, ...prev]);
      calculateStats([newRequest, ...requests]);
      
      message.success('申请创建成功');
      return newRequest;
    } catch (error) {
      console.error('创建申请失败:', error);
      message.error('创建申请失败');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [requests, calculateStats]);

  // 更新申请
  const updateRequest = useCallback(async (id: number, data: any) => {
    try {
      setLoading(true);
      
      // 调用真实的API服务
      const response = await purchaseRequestService.updateRequest(id, data);

      // 更新本地状态
      const updatedRequest = {
        ...response
      };

      const updatedRequests = requests.map(req => 
        req.id === id ? updatedRequest : req
      );
      
      setRequests(updatedRequests);
      calculateStats(updatedRequests);
      
      message.success('申请更新成功');
    } catch (error) {
      console.error('更新申请失败:', error);
      message.error('更新申请失败');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [requests, calculateStats]);

  // 删除申请
  const deleteRequest = useCallback(async (id: number, returnToCart: boolean = false) => {
    try {
      setLoading(true);
      
      // 调用真实的删除API服务
      const result = await purchaseRequestService.deleteRequest(id, returnToCart);
      
      // 更新本地状态
      const updatedRequests = requests.filter(req => req.id !== id);
      setRequests(updatedRequests);
      calculateStats(updatedRequests);
      
      message.success(result.message);
    } catch (error) {
      console.error('删除申请失败:', error);
      message.error('删除申请失败');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [requests, calculateStats]);

  // 审批申请
  const approveRequest = useCallback(async (id: number, action: 'approve' | 'reject' | 'return', comments: string) => {
    try {
      setLoading(true);
      
      // 调用真实的API服务
      const response = await purchaseRequestService.approveRequest(id, action, comments);

      // 更新本地状态
      const updatedRequest = {
        ...response
      };

      const updatedRequests = requests.map(req => 
        req.id === id ? updatedRequest : req
      );
      
      setRequests(updatedRequests);
      calculateStats(updatedRequests);
      
      // 成功消息由ApprovalForm的onSuccess回调处理，这里不需要显示
    } catch (error) {
      console.error('审批申请失败:', error);
      message.error('审批申请失败');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [requests, calculateStats]);

  // 撤回申请
  const withdrawRequest = useCallback(async (id: number) => {
    try {
      setLoading(true);
      
      // 调用真实的API服务
      const response = await purchaseRequestService.withdrawRequest(id);

      // 更新本地状态
      const updatedRequest = {
        ...response
      };

      const updatedRequests = requests.map(req => 
        req.id === id ? updatedRequest : req
      );
      
      setRequests(updatedRequests);
      calculateStats(updatedRequests);
      
      message.success('申请已撤回');
    } catch (error) {
      console.error('撤回申请失败:', error);
      message.error('撤回申请失败');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [requests, calculateStats]);

  // 撤销申请回到待提交状态
  const withdrawToPending = useCallback(async (id: number) => {
    try {
      setLoading(true);
      
      // 调用真实的API服务
      const response = await purchaseRequestService.withdrawToPending(id);

      // 更新本地状态
      const updatedRequest = {
        ...response
      };

      const updatedRequests = requests.map(req => 
        req.id === id ? updatedRequest : req
      );
      
      setRequests(updatedRequests);
      calculateStats(updatedRequests);
      
      message.success('申请已撤销回待提交状态');
    } catch (error) {
      console.error('撤销申请失败:', error);
      message.error('撤销申请失败');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [requests, calculateStats]);

  // 提交申请
  const submitRequest = useCallback(async (id: number) => {
    try {
      setLoading(true);
      
      // 调用真实的API服务
      const response = await purchaseRequestService.submitRequest(id);

      // 更新本地状态
      const updatedRequest = {
        ...response
      };

      const updatedRequests = requests.map(req => 
        req.id === id ? updatedRequest : req
      );
      
      setRequests(updatedRequests);
      calculateStats(updatedRequests);
      
      message.success('申请已提交');
    } catch (error) {
      console.error('提交申请失败:', error);
      message.error('提交申请失败');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [requests, calculateStats]);

  // 获取当前审批级别
  const getCurrentApprovalLevel = (status: string) => {
    switch (status) {
      case 'under_review':
        return 'review';
      case 'under_principle_approval':
        return 'principle_approval';
      case 'under_final_approval':
        return 'final_approval';
      default:
        return 'review';
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchRequests();
    fetchStats();
  }, [fetchRequests, fetchStats]);

  return {
    requests,
    loading,
    stats,
    createRequest,
    updateRequest,
    deleteRequest,
    approveRequest,
    withdrawRequest,
    withdrawToPending,
    submitRequest,
    refreshRequests: fetchRequests,
    refreshStats: fetchStats
  };
};
