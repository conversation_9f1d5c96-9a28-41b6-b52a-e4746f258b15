/**
 * 采购申请审批权限检查工具
 * 根据申请状态和用户权限决定是否显示审批按钮
 */

// 审批级别枚举
export enum ApprovalLevel {
  REVIEW = 'review',                    // 部门经理复核
  PRINCIPLE_APPROVAL = 'principle_approval',  // 物品管理员主管审批
  FINAL_APPROVAL = 'final_approval'    // 公司主管最终审批
}

// 申请状态与审批级别的映射
export const STATUS_TO_APPROVAL_LEVEL: Record<string, ApprovalLevel> = {
  'under_review': ApprovalLevel.REVIEW,
  'under_principle_approval': ApprovalLevel.PRINCIPLE_APPROVAL,
  'under_final_approval': ApprovalLevel.FINAL_APPROVAL
};

// 角色与审批级别的映射
export const ROLE_TO_APPROVAL_LEVEL: Record<string, ApprovalLevel[]> = {
  'department_manager': [ApprovalLevel.REVIEW],
  'item_manager': [ApprovalLevel.PRINCIPLE_APPROVAL],
  'company_supervisor': [ApprovalLevel.FINAL_APPROVAL],
  'super_admin': [ApprovalLevel.REVIEW, ApprovalLevel.PRINCIPLE_APPROVAL, ApprovalLevel.FINAL_APPROVAL],
  'system_admin': [ApprovalLevel.REVIEW, ApprovalLevel.PRINCIPLE_APPROVAL, ApprovalLevel.FINAL_APPROVAL]
};

// 权限与审批级别的映射
export const PERMISSION_TO_APPROVAL_LEVEL: Record<string, ApprovalLevel[]> = {
  'purchase.review': [ApprovalLevel.REVIEW],
  'purchase.principle_approve': [ApprovalLevel.PRINCIPLE_APPROVAL],
  'purchase.approve': [ApprovalLevel.FINAL_APPROVAL],
  'purchase.final_approve': [ApprovalLevel.FINAL_APPROVAL]
};

/**
 * 检查用户是否有权限审批特定状态的采购申请
 * @param requestStatus 申请状态
 * @param userRoleCode 用户角色代码
 * @param userPermissions 用户权限列表
 * @returns 是否有审批权限
 */
export function canApproveRequest(
  requestStatus: string,
  userRoleCode?: string,
  userPermissions: string[] = []
): boolean {
  // 获取申请状态对应的审批级别
  const requiredLevel = STATUS_TO_APPROVAL_LEVEL[requestStatus];
  if (!requiredLevel) {
    return false; // 该状态不需要审批
  }

  // 检查角色权限
  if (userRoleCode) {
    const roleLevels = ROLE_TO_APPROVAL_LEVEL[userRoleCode];
    if (roleLevels && roleLevels.includes(requiredLevel)) {
      return true;
    }
  }

  // 检查具体权限
  for (const permission of userPermissions) {
    const permissionLevels = PERMISSION_TO_APPROVAL_LEVEL[permission];
    if (permissionLevels && permissionLevels.includes(requiredLevel)) {
      return true;
    }
  }

  return false;
}

/**
 * 获取申请状态对应的审批级别名称
 * @param status 申请状态
 * @returns 审批级别名称
 */
export function getApprovalLevelName(status: string): string {
  const level = STATUS_TO_APPROVAL_LEVEL[status];
  switch (level) {
    case ApprovalLevel.REVIEW:
      return '部门经理复核';
    case ApprovalLevel.PRINCIPLE_APPROVAL:
      return '物品管理员主管审批';
    case ApprovalLevel.FINAL_APPROVAL:
      return '公司主管最终审批';
    default:
      return '未知级别';
  }
}

/**
 * 获取用户可审批的状态列表
 * @param userRoleCode 用户角色代码
 * @param userPermissions 用户权限列表
 * @returns 可审批的状态列表
 */
export function getApprovableStatuses(
  userRoleCode?: string,
  userPermissions: string[] = []
): string[] {
  const approvableStatuses: string[] = [];

  // 检查每个状态
  Object.entries(STATUS_TO_APPROVAL_LEVEL).forEach(([status, level]) => {
    if (canApproveRequest(status, userRoleCode, userPermissions)) {
      approvableStatuses.push(status);
    }
  });

  return approvableStatuses;
}
