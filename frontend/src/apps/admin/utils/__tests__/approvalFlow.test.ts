import { canApproveRequest, getApprovalLevelName } from '../approvalPermissionUtils';

describe('Approval Flow Integration', () => {
  describe('Department Manager Approval Flow', () => {
    it('should allow department manager to approve under_review requests', () => {
      const canApprove = canApproveRequest('under_review', 'department_manager', []);
      expect(canApprove).toBe(true);
    });

    it('should not allow department manager to approve under_principle_approval requests', () => {
      const canApprove = canApproveRequest('under_principle_approval', 'department_manager', []);
      expect(canApprove).toBe(false);
    });

    it('should not allow department manager to approve under_final_approval requests', () => {
      const canApprove = canApproveRequest('under_final_approval', 'department_manager', []);
      expect(canApprove).toBe(false);
    });
  });

  describe('Item Manager Approval Flow', () => {
    it('should not allow item manager to approve under_review requests', () => {
      const canApprove = canApproveRequest('under_review', 'item_manager', []);
      expect(canApprove).toBe(false);
    });

    it('should allow item manager to approve under_principle_approval requests', () => {
      const canApprove = canApproveRequest('under_principle_approval', 'item_manager', []);
      expect(canApprove).toBe(true);
    });

    it('should not allow item manager to approve under_final_approval requests', () => {
      const canApprove = canApproveRequest('under_final_approval', 'item_manager', []);
      expect(canApprove).toBe(false);
    });
  });

  describe('Company Supervisor Approval Flow', () => {
    it('should not allow company supervisor to approve under_review requests', () => {
      const canApprove = canApproveRequest('under_review', 'company_supervisor', []);
      expect(canApprove).toBe(false);
    });

    it('should not allow company supervisor to approve under_principle_approval requests', () => {
      const canApprove = canApproveRequest('under_principle_approval', 'company_supervisor', []);
      expect(canApprove).toBe(false);
    });

    it('should allow company supervisor to approve under_final_approval requests', () => {
      const canApprove = canApproveRequest('under_final_approval', 'company_supervisor', []);
      expect(canApprove).toBe(true);
    });
  });

  describe('Approval Level Names', () => {
    it('should return correct approval level names', () => {
      expect(getApprovalLevelName('review')).toBe('部门经理复核');
      expect(getApprovalLevelName('principle_approval')).toBe('物品管理员主管审批');
      expect(getApprovalLevelName('final_approval')).toBe('公司主管最终审批');
    });
  });

  describe('Permission-based Approval', () => {
    it('should allow approval with purchase.review permission', () => {
      const canApprove = canApproveRequest('under_review', undefined, ['purchase.review']);
      expect(canApprove).toBe(true);
    });

    it('should allow approval with purchase.principle_approve permission', () => {
      const canApprove = canApproveRequest('under_principle_approval', undefined, ['purchase.principle_approve']);
      expect(canApprove).toBe(true);
    });

    it('should allow approval with purchase.approve permission', () => {
      const canApprove = canApproveRequest('under_final_approval', undefined, ['purchase.approve']);
      expect(canApprove).toBe(true);
    });
  });
});
