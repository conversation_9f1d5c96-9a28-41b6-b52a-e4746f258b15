import { 
  canApproveRequest, 
  getApprovalLevelName, 
  getApprovableStatuses,
  ApprovalLevel,
  STATUS_TO_APPROVAL_LEVEL,
  ROLE_TO_APPROVAL_LEVEL,
  PERMISSION_TO_APPROVAL_LEVEL
} from '../approvalPermissionUtils';

describe('approvalPermissionUtils', () => {
  describe('STATUS_TO_APPROVAL_LEVEL', () => {
    it('should correctly map status to approval levels', () => {
      expect(STATUS_TO_APPROVAL_LEVEL['under_review']).toBe(ApprovalLevel.REVIEW);
      expect(STATUS_TO_APPROVAL_LEVEL['under_principle_approval']).toBe(ApprovalLevel.PRINCIPLE_APPROVAL);
      expect(STATUS_TO_APPROVAL_LEVEL['under_final_approval']).toBe(ApprovalLevel.FINAL_APPROVAL);
    });
  });

  describe('ROLE_TO_APPROVAL_LEVEL', () => {
    it('should correctly map roles to approval levels', () => {
      expect(ROLE_TO_APPROVAL_LEVEL['department_manager']).toEqual([ApprovalLevel.REVIEW]);
      expect(ROLE_TO_APPROVAL_LEVEL['item_manager']).toEqual([ApprovalLevel.PRINCIPLE_APPROVAL]);
      expect(ROLE_TO_APPROVAL_LEVEL['company_supervisor']).toEqual([ApprovalLevel.FINAL_APPROVAL]);
      expect(ROLE_TO_APPROVAL_LEVEL['super_admin']).toEqual([
        ApprovalLevel.REVIEW, 
        ApprovalLevel.PRINCIPLE_APPROVAL, 
        ApprovalLevel.FINAL_APPROVAL
      ]);
    });
  });

  describe('PERMISSION_TO_APPROVAL_LEVEL', () => {
    it('should correctly map permissions to approval levels', () => {
      expect(PERMISSION_TO_APPROVAL_LEVEL['purchase.review']).toEqual([ApprovalLevel.REVIEW]);
      expect(PERMISSION_TO_APPROVAL_LEVEL['purchase.principle_approve']).toEqual([ApprovalLevel.PRINCIPLE_APPROVAL]);
      expect(PERMISSION_TO_APPROVAL_LEVEL['purchase.approve']).toEqual([ApprovalLevel.FINAL_APPROVAL]);
    });
  });

  describe('canApproveRequest', () => {
    it('should return false for invalid status', () => {
      expect(canApproveRequest('invalid_status', 'department_manager', [])).toBe(false);
    });

    it('should return true for department manager reviewing under_review', () => {
      expect(canApproveRequest('under_review', 'department_manager', [])).toBe(true);
    });

    it('should return false for department manager trying to approve principle approval', () => {
      expect(canApproveRequest('under_principle_approval', 'department_manager', [])).toBe(false);
    });

    it('should return true for item manager approving principle approval', () => {
      expect(canApproveRequest('under_principle_approval', 'item_manager', [])).toBe(true);
    });

    it('should return true for company supervisor approving final approval', () => {
      expect(canApproveRequest('under_final_approval', 'company_supervisor', [])).toBe(true);
    });

    it('should return false for company supervisor trying to approve review', () => {
      expect(canApproveRequest('under_review', 'company_supervisor', [])).toBe(false);
    });

    it('should return true for super admin approving any level', () => {
      expect(canApproveRequest('under_review', 'super_admin', [])).toBe(true);
      expect(canApproveRequest('under_principle_approval', 'super_admin', [])).toBe(true);
      expect(canApproveRequest('under_final_approval', 'super_admin', [])).toBe(true);
    });

    it('should work with permissions instead of role', () => {
      expect(canApproveRequest('under_review', undefined, ['purchase.review'])).toBe(true);
      expect(canApproveRequest('under_principle_approval', undefined, ['purchase.principle_approve'])).toBe(true);
      expect(canApproveRequest('under_final_approval', undefined, ['purchase.approve'])).toBe(true);
    });

    it('should work with both role and permissions', () => {
      expect(canApproveRequest('under_review', 'department_manager', ['purchase.principle_approve'])).toBe(true);
    });
  });

  describe('getApprovalLevelName', () => {
    it('should return correct approval level names', () => {
      expect(getApprovalLevelName('under_review')).toBe('部门经理复核');
      expect(getApprovalLevelName('under_principle_approval')).toBe('物品管理员主管审批');
      expect(getApprovalLevelName('under_final_approval')).toBe('公司主管最终审批');
      expect(getApprovalLevelName('invalid_status')).toBe('未知级别');
    });
  });

  describe('getApprovableStatuses', () => {
    it('should return correct approvable statuses for department manager', () => {
      const statuses = getApprovableStatuses('department_manager', []);
      expect(statuses).toEqual(['under_review']);
    });

    it('should return correct approvable statuses for item manager', () => {
      const statuses = getApprovableStatuses('item_manager', []);
      expect(statuses).toEqual(['under_principle_approval']);
    });

    it('should return correct approvable statuses for company supervisor', () => {
      const statuses = getApprovableStatuses('company_supervisor', []);
      expect(statuses).toEqual(['under_final_approval']);
    });

    it('should return all statuses for super admin', () => {
      const statuses = getApprovableStatuses('super_admin', []);
      expect(statuses).toEqual(['under_review', 'under_principle_approval', 'under_final_approval']);
    });

    it('should work with permissions instead of role', () => {
      const statuses = getApprovableStatuses(undefined, ['purchase.review', 'purchase.principle_approve']);
      expect(statuses).toEqual(['under_review', 'under_principle_approval']);
    });
  });
});
