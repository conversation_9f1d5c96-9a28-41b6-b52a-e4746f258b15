// 采购申请状态配置类型
export interface StatusConfig {
  color: string;
  text: string;
  iconName: string;
}

// 采购申请状态配置
export const requestStatusConfig: Record<string, StatusConfig> = {
  draft: { color: 'default', text: '草稿', iconName: 'EditOutlined' },
  pending_submission: { color: 'blue', text: '待提交', iconName: 'EditOutlined' },
  under_review: { color: 'orange', text: '待审批', iconName: 'CheckCircleOutlined' },
  under_principle_approval: { color: 'orange', text: '主管审批', iconName: 'CheckCircleOutlined' },
  under_final_approval: { color: 'orange', text: '最终审批', iconName: 'CheckCircleOutlined' },
  approved: { color: 'green', text: '已批准', iconName: 'CheckCircleOutlined' },
  rejected: { color: 'red', text: '已拒绝', iconName: 'CloseCircleOutlined' },
  executed: { color: 'purple', text: '已执行', iconName: 'CheckCircleOutlined' }
};

// 获取状态配置的函数
export const getStatusConfig = (status: string): StatusConfig => {
  return requestStatusConfig[status] || { 
    color: 'default', 
    text: status, 
    iconName: 'EditOutlined'
  };
};

// 优先级配置类型
export interface PriorityConfig {
  color: string;
  text: string;
}

// 优先级配置
export const priorityConfig: Record<string, PriorityConfig> = {
  normal: { color: 'blue', text: '普通' },
  urgent: { color: 'orange', text: '紧急' },
  emergency: { color: 'red', text: '特急' }
};

// 获取优先级配置的函数
export const getPriorityConfig = (priority: string): PriorityConfig => {
  return priorityConfig[priority] || { color: 'default', text: priority };
};
