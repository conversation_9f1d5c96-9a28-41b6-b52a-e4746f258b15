# 审批权限检查工具

## 概述

`approvalPermissionUtils.ts` 是一个用于检查采购申请审批权限的工具模块，确保用户只能看到他们有权限审批的申请对应的审批按钮。

## 核心功能

### 1. 审批级别枚举

```typescript
export enum ApprovalLevel {
  REVIEW = 'review',                    // 部门经理复核
  PRINCIPLE_APPROVAL = 'principle_approval',  // 物品管理员主管审批
  FINAL_APPROVAL = 'final_approval'    // 公司主管最终审批
}
```

### 2. 状态与审批级别映射

```typescript
export const STATUS_TO_APPROVAL_LEVEL: Record<string, ApprovalLevel> = {
  'under_review': ApprovalLevel.REVIEW,
  'under_principle_approval': ApprovalLevel.PRINCIPLE_APPROVAL,
  'under_final_approval': ApprovalLevel.FINAL_APPROVAL
};
```

### 3. 角色与审批级别映射

```typescript
export const ROLE_TO_APPROVAL_LEVEL: Record<string, ApprovalLevel[]> = {
  'department_manager': [ApprovalLevel.REVIEW],
  'item_manager': [ApprovalLevel.PRINCIPLE_APPROVAL],
  'company_supervisor': [ApprovalLevel.FINAL_APPROVAL],
  'super_admin': [ApprovalLevel.REVIEW, ApprovalLevel.PRINCIPLE_APPROVAL, ApprovalLevel.FINAL_APPROVAL],
  'system_admin': [ApprovalLevel.REVIEW, ApprovalLevel.PRINCIPLE_APPROVAL, ApprovalLevel.FINAL_APPROVAL]
};
```

### 4. 权限与审批级别映射

```typescript
export const PERMISSION_TO_APPROVAL_LEVEL: Record<string, ApprovalLevel[]> = {
  'purchase.review': [ApprovalLevel.REVIEW],
  'purchase.principle_approve': [ApprovalLevel.PRINCIPLE_APPROVAL],
  'purchase.approve': [ApprovalLevel.FINAL_APPROVAL],
  'purchase.final_approve': [ApprovalLevel.FINAL_APPROVAL]
};
```

## 主要函数

### `canApproveRequest(requestStatus, userRoleCode, userPermissions)`

检查用户是否有权限审批特定状态的采购申请。

**参数：**
- `requestStatus`: 申请状态 (string)
- `userRoleCode`: 用户角色代码 (string, 可选)
- `userPermissions`: 用户权限列表 (string[])

**返回值：**
- `boolean`: 是否有审批权限

**使用示例：**
```typescript
import { canApproveRequest } from './approvalPermissionUtils';

// 检查部门经理是否可以审批待复核的申请
const canReview = canApproveRequest('under_review', 'department_manager', []);
// 返回: true

// 检查部门经理是否可以审批主管审批中的申请
const canPrincipleApprove = canApproveRequest('under_principle_approval', 'department_manager', []);
// 返回: false
```

### `getApprovalLevelName(status)`

获取申请状态对应的审批级别名称。

**参数：**
- `status`: 申请状态 (string)

**返回值：**
- `string`: 审批级别名称

**使用示例：**
```typescript
import { getApprovalLevelName } from './approvalPermissionUtils';

const levelName = getApprovalLevelName('under_review');
// 返回: "部门经理复核"
```

### `getApprovableStatuses(userRoleCode, userPermissions)`

获取用户可审批的状态列表。

**参数：**
- `userRoleCode`: 用户角色代码 (string, 可选)
- `userPermissions`: 用户权限列表 (string[])

**返回值：**
- `string[]`: 可审批的状态列表

**使用示例：**
```typescript
import { getApprovableStatuses } from './approvalPermissionUtils';

const approvableStatuses = getApprovableStatuses('department_manager', []);
// 返回: ["under_review"]
```

## 在组件中的使用

### 1. 采购申请表格

```typescript
import { canApproveRequest, getApprovalLevelName } from '../../utils/approvalPermissionUtils';

// 在审批按钮的渲染逻辑中
{showApprovalActions && 
 ['under_review', 'under_principle_approval', 'under_final_approval'].includes(record.status) && 
 canApproveRequest(record.status, user?.role_code, user?.permissions) && (
  <Tooltip title={`${t('purchase.approveApplication')} (${getApprovalLevelName(record.status)})`}>
    <Button
      type="text"
      size="small"
      icon={<CheckCircleOutlined />}
      onClick={() => handleApproval(record)}
    />
  </Tooltip>
)}
```

### 2. 审批表单

```typescript
import { canApproveRequest } from '../../utils/approvalPermissionUtils';

// 检查用户是否有权限审批此申请
const hasApprovalPermission = canApproveRequest(request.status, user?.role_code, user?.permissions);

if (!hasApprovalPermission) {
  return (
    <Modal title={t('purchase.approvalPermissionDenied')} /* ... */>
      <div>
        <p>{t('purchase.noApprovalPermission')}</p>
        <p>当前审批级别: {getApprovalLevelName(request.status)}</p>
      </div>
    </Modal>
  );
}
```

### 3. 操作按钮组件

```typescript
import { canApproveRequest } from '../../utils/approvalPermissionUtils';

// 审批按钮 - 根据当前状态和用户权限显示
{canApprove && request.status === 'under_review' && 
 canApproveRequest(request.status, user?.role_code, user?.permissions) && (
  <Button type="primary" onClick={() => onApprove('review')}>
    {t('purchase.departmentManagerReview')}
  </Button>
)}
```

## 权限检查逻辑

1. **状态检查**: 首先检查申请状态是否需要审批
2. **角色检查**: 检查用户角色是否有对应的审批级别权限
3. **权限检查**: 检查用户是否有具体的审批权限
4. **权限合并**: 角色权限和具体权限都支持，满足任一条件即可

## 测试

运行测试文件来验证权限检查逻辑：

```bash
npm test approvalPermissionUtils.test.ts
```

## 注意事项

1. **权限优先级**: 角色权限和具体权限是"或"的关系，满足任一条件即可
2. **状态匹配**: 只有特定状态的申请才需要审批，其他状态不显示审批按钮
3. **用户体验**: 当用户没有权限时，会显示友好的提示信息，说明当前审批级别
4. **扩展性**: 可以轻松添加新的角色、权限和审批级别
