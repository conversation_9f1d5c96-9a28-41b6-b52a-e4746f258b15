import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider, useAuth } from './contexts/AuthContext';

// 懒加载页面组件
const LoginPage = React.lazy(() => import('./pages/LoginPage'));
const MainPage = React.lazy(() => import('./pages/MainPage'));
const HistoryPage = React.lazy(() => import('./pages/HistoryPage'));

// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading, user, token } = useAuth();
  
  console.log('[ProtectedRoute] 路由守卫检查:', {
    loading,
    isAuthenticated,
    hasUser: !!user,
    hasToken: !!token,
    username: user?.username
  });
  
  // 如果还在加载认证状态，显示加载界面
  if (loading) {
    console.log('[ProtectedRoute] 认证状态加载中，显示加载界面');
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        fontSize: '16px',
        color: '#666'
      }}>
        正在验证登录状态...
      </div>
    );
  }
  
  if (!isAuthenticated) {
    console.log('[ProtectedRoute] 用户未认证，重定向到登录页');
    return <Navigate to="/kiosk/login" replace />;
  }
  
  console.log('[ProtectedRoute] 用户已认证，允许访问');
  return <>{children}</>;
};

// 主应用组件
const ClientAppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<div>加载中...</div>}>
      <Routes>
        <Route path="login" element={<LoginPage />} />
        <Route path="/" element={
          <ProtectedRoute>
            <MainPage />
          </ProtectedRoute>
        } />
        <Route path="history" element={
          <ProtectedRoute>
            <HistoryPage />
          </ProtectedRoute>
        } />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Suspense>
  );
};

// 根应用组件
export default function ClientApp() {
  return (
    <ConfigProvider locale={zhCN}>
      <AuthProvider>
        <ClientAppRoutes />
      </AuthProvider>
    </ConfigProvider>
  );
}
