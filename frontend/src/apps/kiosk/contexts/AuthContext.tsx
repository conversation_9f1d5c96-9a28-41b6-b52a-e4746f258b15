import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI } from '../services/api';

interface User {
  user_id: number;
  username: string;
  full_name: string;
  department_id: number;
  department_name: string;
  permissions: Array<{
    code: string;
    name: string;
    module: string;
  }>;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  hasPermission: (permissionCode: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // 检查本地存储的认证信息
  useEffect(() => {
    const initAuth = async () => {
      console.log('[AuthContext] 初始化认证状态');
      const storedToken = localStorage.getItem('kiosk_token');
      const storedUser = localStorage.getItem('kiosk_user');
      
      if (storedToken && storedUser) {
        try {
          const userData = JSON.parse(storedUser);
          console.log('[AuthContext] 发现本地存储的认证信息', { token: !!storedToken, user: userData.username });
          
          setToken(storedToken);
          setUser(userData);
          setIsAuthenticated(true);
          
          // 验证token是否有效
          try {
            await authAPI.verify();
            console.log('[AuthContext] Token验证成功，用户认证状态有效');
          } catch (error) {
            console.warn('[AuthContext] Token验证失败，清除认证状态', error);
            // 401错误时清除认证状态，要求用户重新登录
            handleLogout('token_verification_failed');
          }
        } catch (error) {
          console.error('[AuthContext] 解析存储的用户信息失败:', error);
          // 清除损坏的数据
          handleLogout('data_corrupted');
        }
      } else {
        console.log('[AuthContext] 未发现本地认证信息');
      }
      
      setLoading(false);
      console.log('[AuthContext] 认证状态初始化完成');
    };

    initAuth();
  }, []);

  // 监听全局登出事件
  useEffect(() => {
    const handleGlobalLogout = (event: CustomEvent) => {
      console.log('[AuthContext] 收到全局登出事件:', event.detail);
      handleLogout(event.detail.reason);
    };

    window.addEventListener('auth:logout', handleGlobalLogout as EventListener);
    
    return () => {
      window.removeEventListener('auth:logout', handleGlobalLogout as EventListener);
    };
  }, []);

  const login = async (username: string, password: string) => {
    try {
      const response = await authAPI.login(username, password);
      // 由于响应拦截器已经返回了response.data，所以直接访问
      const { access_token, user_info } = response as any;
      
      // 保存认证信息
      localStorage.setItem('kiosk_token', access_token);
      localStorage.setItem('kiosk_user', JSON.stringify(user_info));
      
      setToken(access_token);
      setUser(user_info);
      setIsAuthenticated(true);
    } catch (error) {
      throw error;
    }
  };

  const handleLogout = (reason: string = 'manual') => {
    console.log('[AuthContext] 执行登出操作，原因:', reason);
    
    // 清除本地存储
    localStorage.removeItem('kiosk_token');
    localStorage.removeItem('kiosk_user');
    
    // 重置状态
    setToken(null);
    setUser(null);
    setIsAuthenticated(false);
    
    console.log('[AuthContext] 登出完成，状态已重置');
  };

  const logout = () => {
    // 手动登出
    handleLogout('manual');
  };

  const hasPermission = (permissionCode: string): boolean => {
    if (!user) return false;
    return user.permissions.some(p => p.code === permissionCode);
  };

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated,
    loading,
    login,
    logout,
    hasPermission,
  };

  // 调试信息：监控认证状态变化
  useEffect(() => {
    console.log('[AuthContext] 认证状态变化:', {
      isAuthenticated,
      loading,
      hasUser: !!user,
      hasToken: !!token,
      username: user?.username
    });
  }, [isAuthenticated, loading, user, token]);

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
