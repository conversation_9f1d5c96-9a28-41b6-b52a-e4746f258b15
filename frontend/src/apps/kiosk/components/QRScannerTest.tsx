import React, { useState } from 'react';
import { Button, Space } from 'antd';
import QRScanner from './QRScanner';

const QRScannerTest: React.FC = () => {
  const [visible, setVisible] = useState(false);

  const handleScan = (qrCode: string) => {
    console.log('扫描结果:', qrCode);
    setVisible(false);
  };

  return (
    <div style={{ padding: 24 }}>
      <h2>QR扫描器测试页面</h2>
      <Space>
        <Button type="primary" onClick={() => setVisible(true)}>
          打开扫码器
        </Button>
      </Space>

      <QRScanner
        visible={visible}
        onClose={() => setVisible(false)}
        onScan={handleScan}
        title="测试扫码器 - 极简版本"
      />
    </div>
  );
};

export default QRScannerTest;
