import React, { useState, useRef, useEffect } from 'react';
import { Modal, message, Typography } from 'antd';
import { Html5QrcodeScanner, Html5QrcodeScanType, Html5QrcodeScannerState } from 'html5-qrcode';
import './QRScanner.css';

const { Text } = Typography;

interface QRScannerProps {
  visible: boolean;
  onClose: () => void;
  onScan: (qrCode: string) => void;
  title: string;
}

const QRScanner: React.FC<QRScannerProps> = ({ visible, onClose, onScan, title }) => {
  const [isScanning, setIsScanning] = useState(false);
  const scannerRef = useRef<Html5QrcodeScanner | null>(null);
  const scannerElementId = "html5qr-code-scanner";

  // 隐藏不必要的按钮
  const hideUnnecessaryButtons = () => {
    // 暂时不隐藏任何UI元素，让用户能看到所有按钮
    console.log('[QRScanner] 隐藏UI元素功能已禁用，保持所有按钮可见');
    return;
    
    // 以下是原来的隐藏逻辑，暂时注释掉
    /*
    try {
      const scannerElement = document.getElementById(scannerElementId);
      if (!scannerElement) return;

      // 移除所有按钮（除了可能的手电筒按钮）
      const buttons = scannerElement.querySelectorAll('button');
      buttons.forEach(button => {
        const buttonText = button.textContent?.toLowerCase() || '';
        const buttonId = button.id?.toLowerCase() || '';
        const buttonClass = button.className?.toLowerCase() || '';
        
        // 保留手电筒按钮，移除其他所有按钮
        if (!buttonText.includes('torch') && 
            !buttonText.includes('flash') && 
            !buttonId.includes('torch') && 
            !buttonId.includes('flash') &&
            !buttonClass.includes('torch') &&
            !buttonClass.includes('flash')) {
          button.style.display = 'none';
        }
      });

      // 移除文件输入
      const fileInputs = scannerElement.querySelectorAll('input[type="file"]');
      fileInputs.forEach(input => {
        (input as HTMLElement).style.display = 'none';
      });

      // 移除下拉选择框
      const selects = scannerElement.querySelectorAll('select');
      selects.forEach(select => {
        (select as HTMLElement).style.display = 'none';
      });

      // 移除状态文本
      const statusSpans = scannerElement.querySelectorAll('[id*="status"]');
      statusSpans.forEach(span => {
        (span as HTMLElement).style.display = 'none';
      });

      console.log('[QRScanner] 已隐藏不必要的UI元素');
    } catch (error) {
      console.warn('[QRScanner] 隐藏按钮时出错:', error);
    }
    */
  };

  // 清理扫描器 - 防御性清理策略
  const cleanupScanner = async () => {
    if (scannerRef.current) {
      try {
        // 先检查扫描器状态
        const state = scannerRef.current.getState();
        console.log('[QRScanner] 开始清理扫描器，当前状态:', state);
        
        if (state === Html5QrcodeScannerState.SCANNING) {
          // 如果正在扫描，先停止扫描
          await scannerRef.current.clear();
          console.log('[QRScanner] 扫描器已停止并清理');
        } else if (state === Html5QrcodeScannerState.NOT_STARTED) {
          // 如果还没开始，直接清理
          console.log('[QRScanner] 扫描器未启动，直接清理');
        }
        
        // 额外的安全措施：手动清理DOM元素
        const scannerElement = document.getElementById(scannerElementId);
        if (scannerElement) {
          // 清空内容而不是删除元素本身
          scannerElement.innerHTML = '';
          console.log('[QRScanner] 手动清理了扫描器DOM内容');
        }
        
      } catch (error) {
        console.warn('[QRScanner] 清理扫描器时出错:', error);
        
        // 如果正常清理失败，强制清理DOM
        try {
          const scannerElement = document.getElementById(scannerElementId);
          if (scannerElement) {
            scannerElement.innerHTML = '';
            console.log('[QRScanner] 强制清理了扫描器DOM');
          }
        } catch (domError) {
          console.error('[QRScanner] 强制清理DOM也失败:', domError);
        }
      } finally {
        // 无论如何都要清空引用
        scannerRef.current = null;
        setIsScanning(false);
      }
    }
  };

  // 启动扫描器 - 增强版本
  const startScanner = () => {
    // 基础检查
    if (!visible || scannerRef.current) {
      console.log('[QRScanner] 跳过启动扫描器:', { 
        visible, 
        hasScanner: !!scannerRef.current 
      });
      return;
    }

    // 检查DOM元素是否存在
    const scannerElement = document.getElementById(scannerElementId);
    if (!scannerElement) {
      console.warn('[QRScanner] 扫描器DOM元素不存在，延迟重试');
      setTimeout(() => startScanner(), 100);
      return;
    }

    try {
      console.log('[QRScanner] 启动html5-qrcode扫描器');
      setIsScanning(true);
      
      const scanner = new Html5QrcodeScanner(
        scannerElementId,
        {
          fps: 10, // 扫描帧率
          qrbox: { width: 300, height: 300 }, // 扫描框大小 - 调大一些适配400px容器
          aspectRatio: 1.0, // 摄像头宽高比 - 强制正方形
          supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA], // 仅支持摄像头扫描
          showTorchButtonIfSupported: false, // 不显示手电筒按钮
          showZoomSliderIfSupported: false, // 不显示缩放滑块
          defaultZoomValueIfSupported: 1, // 默认缩放值
          rememberLastUsedCamera: true, // 记住上次使用的摄像头
          videoConstraints: {
            width: { ideal: 400 },
            height: { ideal: 400 },
            aspectRatio: 1.0
          }, // 强制视频约束为正方形
          experimentalFeatures: {
            useBarCodeDetectorIfSupported: true // 使用浏览器原生条码检测器
          }
        },
        false // 不显示详细日志
      );

      // 成功扫描回调
      const onScanSuccess = (decodedText: string, decodedResult: any) => {
        console.log('[QRScanner] 扫描成功', {
          decodedText,
          textLength: decodedText.length,
          format: decodedResult?.format?.formatName || 'unknown'
        });

        // 检查二维码格式
        if (!decodedText || decodedText.trim() === '') {
          console.warn('[QRScanner] 扫描到空内容，继续扫描');
          message.info('请扫描系统生成的二维码');
          return;
        }

        // 验证系统二维码格式 - 支持新的简化格式
        const validPrefixes = ['IDM', 'EMP', 'SUP', 'DEPT', 'IT', 'HR', 'PUR', 'WH', 'ME', 'SI', 'AD', 'QA', 'PR'];
        const hasValidPrefix = validPrefixes.some(prefix => decodedText.startsWith(prefix));
        
        if (!hasValidPrefix) {
          console.warn('[QRScanner] 二维码格式不正确', { decodedText: decodedText.substring(0, 50) });
          message.warning('检测到无效格式的二维码，请扫描系统生成的二维码');
          return;
        }

        // 扫描成功，处理结果
        console.log('[QRScanner] 有效二维码识别成功');
        onScan(decodedText);
      };

      // 扫描错误回调（静默处理）
      const onScanFailure = (error: string) => {
        // 不输出扫描失败日志，避免控制台噪音
        // console.log('[QRScanner] 扫描失败:', error);
      };

      // 渲染扫描器
      scanner.render(onScanSuccess, onScanFailure);
      scannerRef.current = scanner;

      // 等待扫描器完全渲染后，移除不需要的按钮
      setTimeout(() => {
        hideUnnecessaryButtons();
      }, 500);

    } catch (error) {
      console.error('[QRScanner] 启动扫描器失败:', error);
      message.error('摄像头启动失败，请检查摄像头权限');
      setIsScanning(false);
    }
  };

  // 处理visible状态变化
  useEffect(() => {
    console.log('[QRScanner] 状态变化', { visible, isScanning });
    
    if (visible) {
      // 延迟启动，确保DOM元素已渲染
      const timer = setTimeout(() => {
        startScanner();
      }, 100);
      
      return () => {
        clearTimeout(timer);
        cleanupScanner();
      };
    } else {
      cleanupScanner();
      setIsScanning(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      cleanupScanner();
    };
  }, []);

  const handleClose = async () => {
    console.log('[QRScanner] 开始关闭Modal');
    
    try {
      // 先清理扫描器
      await cleanupScanner();
      
      // 重置状态
      setIsScanning(false);
      
      // 给一点时间让DOM清理完成
      setTimeout(() => {
        onClose();
      }, 50);
      
    } catch (error) {
      console.error('[QRScanner] 关闭时清理失败:', error);
      // 即使清理失败也要关闭
      onClose();
    }
  };

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={700}
      destroyOnHidden
      style={{ top: 20 }}
    >
      <div style={{ textAlign: 'center' }}>
        {/* html5-qrcode 扫描器容器 */}
        <div style={{ 
          marginBottom: 24,
          border: '2px solid #1890ff',
          borderRadius: '12px',
          overflow: 'hidden',
          width: '440px',
          height: '440px',
          margin: '0 auto',
          position: 'relative',
          background: '#f5f5f5',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          {/* 扫描器渲染容器 */}
          <div 
            id={scannerElementId}
            style={{ 
              width: '400px',
              height: '400px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center'
            }}
          />
          
          {/* 加载指示器 */}
          {!isScanning && (
            <div style={{ 
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              color: '#666', 
              fontSize: '16px',
              textAlign: 'center',
              pointerEvents: 'none',
              zIndex: 1
            }}>
              正在启动摄像头...
            </div>
          )}
        </div>

        <div style={{ marginTop: 16 }}>
          <Text type="secondary" style={{ fontSize: '16px' }}>
            将二维码对准扫描框进行识别
          </Text>
        </div>
      </div>
    </Modal>
  );
};

export default QRScanner;
