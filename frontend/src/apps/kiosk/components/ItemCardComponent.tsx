import React from 'react';
import { <PERSON>, Button } from 'antd';
import { ScanOutlined, EditOutlined } from '@ant-design/icons';

interface ItemInfo {
  item_id: number;
  item_name: string;
  item_code: string;
  category_name: string;
  brand: string;
  spec_material: string;
  size_dimension: string;
  purchase_unit: string;
  inventory_unit: string;
  qty_per_up: number;
  available_stock: number;
  min_quantity: number;
  image_url?: string;
}

interface ItemCardComponentProps {
  itemInfo: ItemInfo | null;
  onScanClick: () => void;
  onManualSelectClick: () => void;
}

const ItemCardComponent: React.FC<ItemCardComponentProps> = ({
  itemInfo,
  onScanClick,
  onManualSelectClick
}) => {
  return (
    <Card 
      size="default" 
      hoverable={true}
      onClick={onScanClick}
      style={{ 
        borderRadius: '12px',
        background: itemInfo ? '#fff' : '#fafafa',
        border: itemInfo ? '1px solid #f0f0f0' : '2px dashed #d9d9d9',
        width: '280px',
        height: 'auto',
        position: 'relative',
        cursor: !itemInfo ? 'pointer' : 'default'
      }}
      bodyStyle={{ padding: '12px' }}
    >
      {itemInfo ? (
        <div>
          {/* 图片区域 */}
          <div style={{ 
            width: '256px',
            height: '256px', 
            overflow: 'hidden', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            backgroundColor: '#f5f5f5',
            position: 'relative',
            borderRadius: '6px',
            marginBottom: '12px'
          }}>
            <img
              src={itemInfo.image_url || '/default-item.png'}
              alt={itemInfo.item_name}
              style={{ 
                width: '100%', 
                height: '100%', 
                objectFit: 'cover',
                borderRadius: '6px'
              }}
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = '/default-item.png';
              }}
            />
            
            {/* 分类标签 - 左上角 */}
            {itemInfo.category_name && (
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                zIndex: 2
              }}>
                <div style={{
                  background: '#1890ff',
                  color: 'white',
                  padding: '2px 6px',
                  fontSize: '10px',
                  fontWeight: '500',
                  borderRadius: '0 0 8px 0',
                  border: '1px solid rgba(255,255,255,0.2)',
                  boxShadow: '0 2px 4px rgba(24,144,255,0.3)',
                  maxWidth: '140px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  lineHeight: '1.2'
                }}>
                  {itemInfo.category_name}
                </div>
              </div>
            )}
            
            {/* 库存状态标签 - 右上角 */}
            <div style={{
              position: 'absolute',
              top: 0,
              right: 0,
              zIndex: 2,
              display: 'flex',
              flexDirection: 'column',
              gap: '2px'
            }}>
              {itemInfo.available_stock <= itemInfo.min_quantity && (
                <div style={{
                  background: '#ff4d4f',
                  color: 'white',
                  padding: '2px 6px',
                  fontSize: '10px',
                  fontWeight: '500',
                  borderRadius: '0 0 0 8px',
                  border: '1px solid rgba(255,255,255,0.2)',
                  boxShadow: '0 2px 4px rgba(255,77,79,0.3)',
                  whiteSpace: 'nowrap'
                }}>
                  库存不足
                </div>
              )}
              {itemInfo.available_stock > 0 && 
               itemInfo.available_stock <= itemInfo.min_quantity * 1.5 && 
               itemInfo.available_stock > itemInfo.min_quantity && (
                <div style={{
                  background: '#fa8c16',
                  color: 'white',
                  padding: '2px 6px',
                  fontSize: '10px',
                  fontWeight: '500',
                  borderRadius: '0 0 0 8px',
                  border: '1px solid rgba(255,255,255,0.2)',
                  boxShadow: '0 2px 4px rgba(250,140,22,0.3)',
                  whiteSpace: 'nowrap'
                }}>
                  库存偏低
                </div>
              )}
            </div>
            
            {/* 属性标签 - 左下角 */}
            {(itemInfo.brand || itemInfo.spec_material || itemInfo.size_dimension) && (
              <div style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                zIndex: 2,
                display: 'flex',
                flexWrap: 'wrap',
                gap: '2px',
                maxWidth: '80%'
              }}>
                {itemInfo.brand && itemInfo.brand.trim() && (
                  <div style={{
                    background: '#722ed1',
                    color: 'white',
                    padding: '2px 6px',
                    fontSize: '10px',
                    fontWeight: '500',
                    borderRadius: '0 8px 0 0',
                    border: '1px solid rgba(255,255,255,0.2)',
                    boxShadow: '0 2px 4px rgba(114,46,209,0.3)',
                    whiteSpace: 'nowrap'
                  }}>
                    {itemInfo.brand}
                  </div>
                )}
                {itemInfo.spec_material && itemInfo.spec_material.trim() && (
                  <div style={{
                    background: '#13c2c2',
                    color: 'white',
                    padding: '2px 6px',
                    fontSize: '10px',
                    fontWeight: '500',
                    borderRadius: '0 8px 0 0',
                    border: '1px solid rgba(255,255,255,0.2)',
                    boxShadow: '0 2px 4px rgba(19,194,194,0.3)',
                    whiteSpace: 'nowrap'
                  }}>
                    {itemInfo.spec_material}
                  </div>
                )}
                {itemInfo.size_dimension && itemInfo.size_dimension.trim() && (
                  <div style={{
                    background: '#1890ff',
                    color: 'white',
                    padding: '2px 6px',
                    fontSize: '10px',
                    fontWeight: '500',
                    borderRadius: '0 8px 0 0',
                    border: '1px solid rgba(255,255,255,0.2)',
                    boxShadow: '0 2px 4px rgba(24,144,255,0.3)',
                    whiteSpace: 'nowrap'
                  }}>
                    {itemInfo.size_dimension}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* 内容区域 */}
          <div>
            {/* 物品名称和编码 */}
            <div style={{ 
              marginBottom: '8px',
              height: '40px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'flex-start'
            }}>
              <div style={{
                textAlign: 'left',
                lineHeight: '1.4',
                height: '40px',
                overflow: 'hidden'
              }}>
                <div style={{ 
                  fontSize: '14px',
                  wordBreak: 'break-word',
                  lineHeight: '1.4',
                  fontWeight: 'bold',
                  color: '#333'
                }}>
                  {itemInfo.item_name}
                  {itemInfo.item_code && (
                    <span style={{ 
                      fontSize: '12px',
                      marginLeft: '4px',
                      color: '#666',
                      fontWeight: 'normal'
                    }}>
                      ({itemInfo.item_code})
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* 库存信息 */}
            <div style={{ marginTop: '8px', textAlign: 'left' }}>
              <div style={{ 
                lineHeight: '1.2',
                fontSize: '16px',
                fontWeight: 'bold',
                color: itemInfo.available_stock > itemInfo.min_quantity ? '#52c41a' : '#ff4d4f',
                textAlign: 'left'
              }}>
                库存: {itemInfo.available_stock}
                {itemInfo.inventory_unit && (
                  <span style={{ 
                    fontSize: '12px', 
                    marginLeft: '2px', 
                    color: '#666',
                    fontWeight: 'normal'
                  }}>
                    {itemInfo.inventory_unit}
                    {/* 只有当采购单位与库存单位不一致时才显示转换后的采购单位数量 */}
                    {itemInfo.purchase_unit && 
                     itemInfo.inventory_unit && 
                     itemInfo.purchase_unit !== itemInfo.inventory_unit && 
                     itemInfo.qty_per_up && 
                     itemInfo.qty_per_up !== 1 && (
                      <span style={{ color: '#999', marginLeft: '4px' }}>
                        ({(() => {
                          const purchaseQty = itemInfo.available_stock / itemInfo.qty_per_up;
                          return purchaseQty % 1 === 0 ? purchaseQty.toString() : purchaseQty.toFixed(2);
                        })()}{itemInfo.purchase_unit})
                      </span>
                    )}
                  </span>
                )}
              </div>
              {/* 最小库存信息 */}
              <div style={{ marginTop: '1px', textAlign: 'left' }}>
                <div style={{ 
                  fontSize: '13px', 
                  color: '#666',
                  textAlign: 'left'
                }}>
                  最小库存: {itemInfo.min_quantity} {itemInfo.inventory_unit}
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div style={{ 
          height: '360px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#8c8c8c'
        }}>
          <ScanOutlined style={{ fontSize: '48px', color: '#bfbfbf', marginBottom: '16px' }} />
          <div style={{ fontSize: '16px', textAlign: 'center' }}>
            点击扫描物品二维码
          </div>
        </div>
      )}

      {/* 右下角手动选择按钮 */}
      <Button
        type="text"
        icon={<EditOutlined />}
        onClick={(e) => {
          e.stopPropagation();
          onManualSelectClick();
        }}
        title="手动选择物品"
        style={{ 
          position: 'absolute',
          bottom: '8px',
          right: '8px',
          width: '32px',
          height: '32px',
          minWidth: '32px',
          padding: '0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#1890ff',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderRadius: '50%',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          zIndex: 3
        }}
      />
    </Card>
  );
};

export default ItemCardComponent;
