/* QRScanner 样式 - 重新设计布局 */

/* 重置html5-qrcode的默认布局 */
#html5qr-code-scanner {
  background: transparent !important;
  display: block !important;
  width: 100% !important;
  height: auto !important;
}

/* 让视频画面使用全宽度 */
#html5qr-code-scanner video {
  width: 400px !important;
  height: 400px !important;
  object-fit: cover !important;
  border-radius: 8px;
  display: block !important;
  margin: 0 auto !important;
}

/* 确保扫描区域使用全宽度 */
#html5qr-code-scanner__scan_region {
  border: none !important;
  margin: 0 auto !important;
  text-align: center !important;
  width: 400px !important;
  height: 400px !important;
  overflow: hidden !important;
}

/* 强制所有容器使用全宽度 */
#html5qr-code-scanner > div {
  width: 100% !important;
  display: block !important;
}

/* 修复控制面板布局 */
#html5qr-code-scanner__dashboard_section,
#html5qr-code-scanner__dashboard_section_csr,
#html5qr-code-scanner__dashboard_section_fsr {
  width: 100% !important;
  display: block !important;
  text-align: center !important;
  margin: 10px 0 !important;
}

/* 让按钮在下方居中排列 */
#html5qr-code-scanner button {
  display: inline-block !important;
  margin: 5px 10px !important;
  vertical-align: middle !important;
}

/* 调整文件输入和下拉框位置 */
#html5qr-code-scanner input[type="file"],
#html5qr-code-scanner select {
  display: inline-block !important;
  margin: 5px 10px !important;
  vertical-align: middle !important;
}

/* 确保canvas元素也是正方形 */
#html5qr-code-scanner canvas {
  width: 400px !important;
  height: 400px !important;
  object-fit: cover !important;
}

/* 隐藏不必要的状态文本 */
#html5qr-code-scanner__status_span {
  display: none !important;
}
