import React from 'react';
import { Modal, Input, Space, Card } from 'antd';

interface ItemInfo {
  item_id: number;
  item_name: string;
  item_code: string;
  category_name: string;
  brand: string;
  spec_material: string;
  size_dimension: string;
  purchase_unit: string;
  inventory_unit: string;
  qty_per_up: number;
  available_stock: number;
  min_quantity: number;
  image_url?: string;
}

interface ItemSearchModalProps {
  visible: boolean;
  onClose: () => void;
  departmentName?: string;
  searchQuery: string;
  setSearchQuery: (value: string) => void;
  searchResults: ItemInfo[];
  searchLoading: boolean;
  onSearch: () => void;
  onSelectItem: (item: ItemInfo) => void;
}

const ItemSearchModal: React.FC<ItemSearchModalProps> = ({
  visible,
  onClose,
  departmentName,
  searchQuery,
  setSearchQuery,
  searchResults,
  searchLoading,
  onSearch,
  onSelectItem
}) => {
  return (
    <Modal
      title={`手动选择物品 - ${departmentName || '本部门'}库存`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ 
          background: '#f0f8ff', 
          padding: '8px 12px', 
          borderRadius: '6px', 
          border: '1px solid #d6e4ff',
          fontSize: '14px',
          color: '#1890ff'
        }}>
          当前显示：{departmentName || '本部门'} 有库存的物品
        </div>
        <Input.Search
          placeholder="输入物品编码或名称进行筛选（可选）"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onSearch={onSearch}
          loading={searchLoading}
          size="large"
          enterButton="筛选"
        />
        
        {searchResults.length > 0 && (
          <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
            {searchResults.map((item, index) => (
              <Card
                key={index}
                size="small"
                style={{ marginBottom: '8px', cursor: 'pointer' }}
                onClick={() => onSelectItem(item)}
                hoverable
              >
                <div>
                  <div><strong>{item.item_name}</strong></div>
                  <div>编码: {item.item_code}</div>
                  <div>规格: {item.spec_material || '未知'}</div>
                  <div>品牌: {item.brand || '未知'}</div>
                  <div style={{ color: '#52c41a' }}>
                    库存: {item.available_stock} {item.inventory_unit}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </Space>
    </Modal>
  );
};

export default ItemSearchModal;
