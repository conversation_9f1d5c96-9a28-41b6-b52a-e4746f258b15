import React from 'react';
import { Card, Button } from 'antd';
import { UserOutlined, EditOutlined } from '@ant-design/icons';

interface EmployeeInfo {
  employee_id: number;
  employee_code: string;
  employee_name: string;
  department_id: number;
  department_name: string;
  position: string;
}

interface EmployeeCardComponentProps {
  employeeInfo: EmployeeInfo | null;
  onScanClick: () => void;
  onManualSelectClick: () => void;
}

const EmployeeCardComponent: React.FC<EmployeeCardComponentProps> = ({
  employeeInfo,
  onScanClick,
  onManualSelectClick
}) => {
  return (
    <Card 
      size="default" 
      hoverable={true}
      onClick={onScanClick}
      style={{ 
        borderRadius: '12px',
        background: employeeInfo ? '#fff' : '#fafafa',
        border: employeeInfo ? '1px solid #f0f0f0' : '2px dashed #d9d9d9',
        height: 'auto',
        width: '280px',
        position: 'relative',
        cursor: !employeeInfo ? 'pointer' : 'default'
      }}
      bodyStyle={{ padding: '12px' }}
    >
      {employeeInfo ? (
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column',
          justifyContent: 'center',
          gap: '16px',
          minHeight: '360px'
        }}>
          {/* 员工头像区域 */}
          <div style={{ 
            width: '80px',
            height: '80px',
            borderRadius: '50%',
            background: '#1890ff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto',
            color: 'white',
            fontSize: '32px',
            fontWeight: 'bold'
          }}>
            <UserOutlined />
          </div>

          {/* 员工信息 */}
          <div style={{ textAlign: 'center' }}>
            <div style={{ 
              fontSize: '18px', 
              fontWeight: 'bold', 
              color: '#333',
              marginBottom: '8px'
            }}>
              {employeeInfo.employee_name}
            </div>
            
            <div style={{ 
              fontSize: '14px', 
              color: '#666',
              marginBottom: '4px'
            }}>
              工号: {employeeInfo.employee_code}
            </div>
            
            <div style={{ 
              fontSize: '14px', 
              color: '#666',
              marginBottom: '4px'
            }}>
              部门: {employeeInfo.department_name}
            </div>
            
            {employeeInfo.position && (
              <div style={{ 
                fontSize: '14px', 
                color: '#666'
              }}>
                职位: {employeeInfo.position}
              </div>
            )}
          </div>
        </div>
      ) : (
        <div style={{ 
          minHeight: '360px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#8c8c8c'
        }}>
          <UserOutlined style={{ fontSize: '48px', color: '#bfbfbf', marginBottom: '16px' }} />
          <div style={{ fontSize: '16px', textAlign: 'center' }}>
            点击扫描员工工卡
          </div>
        </div>
      )}

      {/* 右下角手动选择按钮 */}
      <Button
        type="text"
        icon={<EditOutlined />}
        onClick={(e) => {
          e.stopPropagation();
          onManualSelectClick();
        }}
        title="手动选择员工"
        style={{ 
          position: 'absolute',
          bottom: '8px',
          right: '8px',
          width: '32px',
          height: '32px',
          minWidth: '32px',
          padding: '0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#1890ff',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderRadius: '50%',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          zIndex: 3
        }}
      />
    </Card>
  );
};

export default EmployeeCardComponent;
