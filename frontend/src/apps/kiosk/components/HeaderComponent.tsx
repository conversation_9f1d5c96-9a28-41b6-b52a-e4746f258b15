import React from 'react';
import { Layout, Button, Typography } from 'antd';
import { HistoryOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Header } = Layout;
const { Title } = Typography;

interface HeaderComponentProps {
  departmentName?: string;
}

const HeaderComponent: React.FC<HeaderComponentProps> = ({ departmentName }) => {
  const navigate = useNavigate();

  return (
    <Header style={{ 
      background: '#fff', 
      padding: '0 24px', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'space-between',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      height: '80px'
    }}>
      <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
        <Title level={2} style={{ 
          margin: 0, 
          color: '#1890ff', 
          fontSize: '28px'
        }}>
          {departmentName} - 物品领取系统
        </Title>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px', flexShrink: 0 }}>
        <Button 
          icon={<HistoryOutlined />} 
          onClick={() => navigate('/kiosk/history')}
          type="text"
          size="large"
          style={{ fontSize: '16px', height: '50px' }}
        >
          历史记录
        </Button>
      </div>
    </Header>
  );
};

export default HeaderComponent;
