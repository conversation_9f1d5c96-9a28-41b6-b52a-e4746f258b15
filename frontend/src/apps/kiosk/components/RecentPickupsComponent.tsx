import React from 'react';
import { Button } from 'antd';
import dayjs from 'dayjs';

interface PickupRecord {
  record_id: number;
  employee_name: string;
  usage_time: string;
  item_name: string;
  quantity: number;
  unit: string;
  item_id: number;
  employee_id: number;
  status?: string; // 记录状态：confirmed（已确认）、cancelled（已撤销）
  // 支持多种可能的字段名
  itemId?: number;
  employeeId?: number;
  quantity_unit?: string;
  inventory_unit?: string;
}

interface RecentPickupsComponentProps {
  recentPickups: PickupRecord[];
  pickupLoading: boolean;
  onUndoPickup: (record: PickupRecord) => void;
}

const RecentPickupsComponent: React.FC<RecentPickupsComponentProps> = ({
  recentPickups,
  pickupLoading,
  onUndoPickup
}) => {
  // 检查是否在允许撤销的时间范围内（5分钟）
  const isWithinTimeLimit = (usageTime: string): boolean => {
    const usageDate = new Date(usageTime);
    const now = new Date();
    const timeDiff = now.getTime() - usageDate.getTime();
    return timeDiff <= 5 * 60 * 1000; // 5分钟 = 300000毫秒
  };
  if (pickupLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '8px', fontSize: '12px', color: '#999' }}>
        加载中...
      </div>
    );
  }

  if (recentPickups.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '8px', fontSize: '12px', color: '#999' }}>
        暂无领取记录
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '6px' }}>
      {recentPickups.slice(0, 3).reverse().map((record, index) => ( // 只显示前3条记录，并反转顺序让新的在下面
        <div 
          key={record.record_id} 
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '8px 12px',
            background: index % 2 === 0 ? '#fff' : '#fafafa',
            borderRadius: '4px',
            border: '1px solid #f0f0f0',
            fontSize: '13px',
            height: '32px', // 固定高度确保每条记录只占一行
            minHeight: '32px'
          }}
        >
          <div style={{ 
            color: '#666', 
            flex: 1, 
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            marginRight: '12px',
            textAlign: 'left'
          }}>
            <span style={{ fontWeight: 'bold', color: '#333' }}>{record.employee_name}</span>
            于
            <span style={{ color: '#1890ff' }}>
              {dayjs(record.usage_time).format('MM-DD HH:mm')}
            </span>
            领取
            <span style={{ fontWeight: 'bold', color: '#333' }}>{record.item_name}</span>
            <span style={{ fontWeight: 'bold', color: '#52c41a' }}> {record.quantity} {record.unit}</span>
          </div>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            flexShrink: 0,
            gap: '8px'
          }}>
            {record.status !== 'cancelled' && isWithinTimeLimit(record.usage_time) && (
              <Button
                type="primary"
                danger
                size="small"
                onClick={() => onUndoPickup(record)}
                style={{ fontSize: '11px', height: '20px', padding: '0 6px' }}
              >
                撤销
              </Button>
            )}
            {record.status === 'cancelled' && (
              <span style={{ 
                fontSize: '11px', 
                color: '#999', 
                fontStyle: 'italic'
              }}>
                已撤销
              </span>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default RecentPickupsComponent;
