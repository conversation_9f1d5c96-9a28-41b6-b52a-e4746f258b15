import React from 'react';
import { Button } from 'antd';
import { CheckOutlined } from '@ant-design/icons';

interface NumberKeyboardComponentProps {
  quantity: number;
  setQuantity: (value: number | ((prev: number) => number)) => void;
  isPositiveMode: boolean;
  setIsPositiveMode: (value: boolean | ((prev: boolean) => boolean)) => void;
  itemInfo: { available_stock: number; inventory_unit: string } | null;
  employeeInfo: any | null;
  loading: boolean;
  onConfirm: () => void;
}

const NumberKeyboardComponent: React.FC<NumberKeyboardComponentProps> = ({
  quantity,
  setQuantity,
  isPositiveMode,
  setIsPositiveMode,
  itemInfo,
  employeeInfo,
  loading,
  onConfirm
}) => {
  const handleNumberClick = (num: string) => {
    setQuantity(prev => {
      const str = prev === 0 ? num : prev.toString() + num;
      return parseFloat(str.slice(0, 10)) || 0;
    });
  };

  const handleDecimalClick = () => {
    const currentStr = quantity.toString();
    if (!currentStr.includes('.')) {
      setQuantity(parseFloat(currentStr + '.'));
    }
  };

  const handleBackspaceClick = () => {
    const currentStr = quantity.toString();
    if (currentStr.length > 1) {
      setQuantity(parseFloat(currentStr.slice(0, -1)) || 0);
    } else {
      setQuantity(0);
    }
  };

  const handleOperationClick = (operation: '+' | '-', amount: number) => {
    setQuantity(prev => {
      if (operation === '+') return prev + amount;
      return Math.max(0, prev - amount);
    });
  };

  const isButtonDisabled = !itemInfo || !employeeInfo;
  const isConfirmDisabled = isButtonDisabled || quantity <= 0 || quantity > (itemInfo?.available_stock || 0);

  return (
    <div style={{
      position: 'fixed',
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: '#fff',
      borderTop: '1px solid #f0f0f0',
      padding: '16px',
      zIndex: 1000,
      boxShadow: '0 -4px 12px rgba(0,0,0,0.1)'
    }}>
      <div style={{ textAlign: 'center', marginBottom: '16px' }}>
        {/* 数量显示 */}
        <div style={{ 
          fontSize: '32px',
          fontWeight: 'bold',
          color: itemInfo && employeeInfo ? '#1890ff' : '#bfbfbf',
          marginBottom: '8px'
        }}>
          {itemInfo && employeeInfo ? `${quantity} ${itemInfo?.inventory_unit}` : '--'}
        </div>
        <div style={{
          fontSize: '14px',
          color: '#666'
        }}>
          {itemInfo ? `可用库存: ${itemInfo?.available_stock} ${itemInfo?.inventory_unit}` : '请先选择物品和员工'}
        </div>
      </div>

      {/* 自定义数字键盘 */}
      <div style={{ width: '100%', margin: '0', opacity: itemInfo && employeeInfo ? 1 : 0.5 }}>
        <div style={{ display: 'flex', gap: '8px' }}>
          {/* 第一行: 7, 8, 9, +10, 确认按钮(跨行) */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', flex: '4' }}>
            {/* 第一行 */}
            <div style={{ display: 'flex', gap: '8px' }}>
              <Button
                size="large" 
                disabled={isButtonDisabled}
                onClick={() => handleNumberClick('7')}
                style={{ flex: 1, height: '80px', fontSize: '20px' }}
              >
                7
              </Button>
              <Button 
                size="large" 
                disabled={isButtonDisabled}
                onClick={() => handleNumberClick('8')}
                style={{ flex: 1, height: '80px', fontSize: '20px' }}
              >
                8
              </Button>
              <Button 
                size="large" 
                disabled={isButtonDisabled}
                onClick={() => handleNumberClick('9')}
                style={{ flex: 1, height: '80px', fontSize: '20px' }}
              >
                9
              </Button>
              <Button 
                size="large" 
                type="primary"
                disabled={isButtonDisabled}
                onClick={() => handleOperationClick(isPositiveMode ? '+' : '-', 10)}
                style={{ flex: 1, height: '80px', fontSize: '16px', background: '#52c41a', borderColor: '#52c41a' }}
              >
                {isPositiveMode ? '+10' : '-10'}
              </Button>
            </div>
            
            {/* 第二行 */}
            <div style={{ display: 'flex', gap: '8px' }}>
              <Button 
                size="large" 
                disabled={isButtonDisabled}
                onClick={() => handleNumberClick('4')}
                style={{ flex: 1, height: '80px', fontSize: '20px' }}
              >
                4
              </Button>
              <Button 
                size="large" 
                disabled={isButtonDisabled}
                onClick={() => handleNumberClick('5')}
                style={{ flex: 1, height: '80px', fontSize: '20px' }}
              >
                5
              </Button>
              <Button 
                size="large" 
                disabled={isButtonDisabled}
                onClick={() => handleNumberClick('6')}
                style={{ flex: 1, height: '80px', fontSize: '20px' }}
              >
                6
              </Button>
              <Button 
                size="large" 
                type="primary"
                disabled={isButtonDisabled}
                onClick={() => handleOperationClick(isPositiveMode ? '+' : '-', 5)}
                style={{ flex: 1, height: '80px', fontSize: '16px', background: '#52c41a', borderColor: '#52c41a' }}
              >
                {isPositiveMode ? '+5' : '-5'}
              </Button>
            </div>

            {/* 第三行 */}
            <div style={{ display: 'flex', gap: '8px' }}>
              <Button 
                size="large" 
                disabled={isButtonDisabled}
                onClick={() => handleNumberClick('1')}
                style={{ flex: 1, height: '80px', fontSize: '20px' }}
              >
                1
              </Button>
              <Button 
                size="large" 
                disabled={isButtonDisabled}
                onClick={() => handleNumberClick('2')}
                style={{ flex: 1, height: '80px', fontSize: '20px' }}
              >
                2
              </Button>
              <Button 
                size="large" 
                disabled={isButtonDisabled}
                onClick={() => handleNumberClick('3')}
                style={{ flex: 1, height: '80px', fontSize: '20px' }}
              >
                3
              </Button>
              <Button 
                size="large" 
                type="primary"
                disabled={isButtonDisabled}
                onClick={() => handleOperationClick(isPositiveMode ? '+' : '-', 1)}
                style={{ flex: 1, height: '80px', fontSize: '16px', background: '#52c41a', borderColor: '#52c41a' }}
              >
                {isPositiveMode ? '+1' : '-1'}
              </Button>
            </div>

            {/* 第四行 */}
            <div style={{ display: 'flex', gap: '8px' }}>
              <Button 
                size="large" 
                disabled={isButtonDisabled}
                onClick={handleDecimalClick}
                style={{ flex: 1, height: '80px', fontSize: '20px' }}
              >
                .
              </Button>
              <Button 
                size="large" 
                disabled={isButtonDisabled}
                onClick={() => handleNumberClick('0')}
                style={{ flex: 1, height: '80px', fontSize: '20px' }}
              >
                0
              </Button>
              <Button 
                size="large" 
                disabled={isButtonDisabled}
                onClick={handleBackspaceClick}
                style={{ flex: 1, height: '80px', fontSize: '20px', color: '#ff4d4f' }}
              >
                ←
              </Button>
              <Button 
                size="large" 
                disabled={isButtonDisabled}
                onClick={() => setIsPositiveMode(prev => !prev)}
                style={{ 
                  flex: 1, 
                  height: '80px', 
                  fontSize: '16px',
                  background: isPositiveMode ? '#f0f0f0' : '#ffebe6',
                  borderColor: isPositiveMode ? '#d9d9d9' : '#ff7875',
                  color: isPositiveMode ? '#595959' : '#ff4d4f'
                }}
              >
                +/-
              </Button>
            </div>
          </div>

          {/* 右侧确认按钮 - 跨越所有行 */}
          <div style={{ flex: '1' }}>
            <Button
              type="primary"
              size="large"
              loading={loading}
              onClick={onConfirm}
              disabled={isConfirmDisabled}
              style={{ 
                width: '100%', 
                height: '344px', // 4 rows * 80px + 3 gaps * 8px = 344px
                fontSize: '20px',
                borderRadius: '12px',
                fontWeight: 'bold',
                boxShadow: '0 4px 12px rgba(24, 144, 255, 0.3)',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px'
              }}
            >
              <div style={{ fontSize: '24px' }}>确认</div>
              <div style={{ fontSize: '24px' }}>领取</div>
              <CheckOutlined style={{ fontSize: '32px' }} />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NumberKeyboardComponent;
