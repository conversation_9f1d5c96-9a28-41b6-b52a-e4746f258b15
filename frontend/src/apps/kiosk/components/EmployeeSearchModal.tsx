import React from 'react';
import { Modal, Input, Space, Card } from 'antd';

interface EmployeeInfo {
  employee_id: number;
  employee_code: string;
  employee_name: string;
  department_id: number;
  department_name: string;
  position: string;
}

interface EmployeeSearchModalProps {
  visible: boolean;
  onClose: () => void;
  departmentName?: string;
  searchQuery: string;
  setSearchQuery: (value: string) => void;
  searchResults: EmployeeInfo[];
  searchLoading: boolean;
  onSearch: () => void;
  onSelectEmployee: (employee: EmployeeInfo) => void;
}

const EmployeeSearchModal: React.FC<EmployeeSearchModalProps> = ({
  visible,
  onClose,
  departmentName,
  searchQuery,
  setSearchQuery,
  searchResults,
  searchLoading,
  onSearch,
  onSelectEmployee
}) => {
  return (
    <Modal
      title={`手动输入员工 - ${departmentName || '本部门'}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={500}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ 
          background: '#f0f8ff', 
          padding: '8px 12px', 
          borderRadius: '6px', 
          border: '1px solid #d6e4ff',
          fontSize: '14px',
          color: '#1890ff'
        }}>
          当前显示：{departmentName || '本部门'} 的员工
        </div>
        <Input.Search
          placeholder="输入员工用户名"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onSearch={onSearch}
          loading={searchLoading}
          size="large"
          enterButton="搜索"
        />
        
        {searchResults.length > 0 && (
          <div>
            {searchResults.map((employee, index) => (
              <Card
                key={index}
                size="small"
                style={{ marginBottom: '8px', cursor: 'pointer' }}
                onClick={() => onSelectEmployee(employee)}
                hoverable
              >
                <div>
                  <div><strong>{employee.employee_name}</strong></div>
                  <div>工号: {employee.employee_code}</div>
                  <div>部门: {employee.department_name || '未知'}</div>
                  <div>职位: {employee.position || '未知'}</div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </Space>
    </Modal>
  );
};

export default EmployeeSearchModal;
