/**
 * Kiosk二维码服务
 * 用于解析扫描到的二维码内容
 */

import { QRCodeService, QRCodeType, QRCodeData } from '@shared/services/qrCodeService';

export class KioskQRCodeService {
  /**
   * 解析物品二维码
   */
  static parseItemQRCode(qrContent: string): QRCodeData {
    try {
      const parsed = QRCodeService.parseQRCode(qrContent);
      
      if (parsed.type !== QRCodeType.ITEM) {
        throw new Error(`二维码类型不匹配，期望物品类型，实际为：${parsed.type}`);
      }
      
      return parsed;
    } catch (error) {
      throw new Error(`物品二维码解析失败：${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 解析员工二维码
   */
  static parseEmployeeQRCode(qrContent: string): QRCodeData {
    try {
      const parsed = QRCodeService.parseQRCode(qrContent);
      
      if (parsed.type !== QRCodeType.CUSTOMER) {
        throw new Error(`二维码类型不匹配，期望员工类型，实际为：${parsed.type}`);
      }
      
      return parsed;
    } catch (error) {
      throw new Error(`员工二维码解析失败：${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 验证二维码是否为有效格式
   */
  static isValidQRCode(qrContent: string): boolean {
    return QRCodeService.isValidQRCode(qrContent);
  }

  /**
   * 获取二维码类型
   */
  static getQRCodeType(qrContent: string): QRCodeType | null {
    return QRCodeService.detectQRCodeType(qrContent);
  }

  /**
   * 从二维码中提取物品编码
   */
  static extractItemCodeFromQRCode(qrContent: string): string | null {
    try {
      const parsed = QRCodeService.parseQRCode(qrContent);
      if (parsed.type === QRCodeType.ITEM) {
        return parsed.code;
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 从二维码中提取员工工号
   */
  static extractEmployeeUsernameFromQRCode(qrContent: string): string | null {
    try {
      const parsed = QRCodeService.parseQRCode(qrContent);
      if (parsed.type === QRCodeType.CUSTOMER) {
        return parsed.code;
      }
      return null;
    } catch (error) {
      return null;
    }
  }
}
