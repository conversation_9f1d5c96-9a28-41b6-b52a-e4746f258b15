import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: '/api/kiosk',
  timeout: 10000,
});

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('kiosk_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      console.log('API请求401错误，需要退出登录状态');
      
      // 清除本地存储的认证信息
      localStorage.removeItem('kiosk_token');
      localStorage.removeItem('kiosk_user');
      
      // 触发全局事件，通知AuthContext更新状态
      window.dispatchEvent(new CustomEvent('auth:logout', {
        detail: { reason: 'token_expired' }
      }));
      
      // 跳转到登录页面，让用户重新登录
      // 注意：这里使用replace避免用户通过后退按钮回到需要认证的页面
      window.location.replace('/kiosk/login');
    }
    return Promise.reject(error);
  }
);

// 认证相关API
export const authAPI = {
  // 登录
  login: (username: string, password: string) =>
    api.post('/auth/login', { username, password }),
  
  // 验证token
  verify: () => api.get('/auth/verify'),
};

// 物品相关API
export const itemAPI = {
  // 扫描物品
  scan: (qrCode: string) =>
    api.post('/items/scan', { qr_code: qrCode }),
};

// 物品选择API
export const itemSelectAPI = {
  // 获取有库存的物品列表
  getAvailable: (query?: string) =>
    api.get('/items/available', { params: { query: query || '' } }),
};

// 员工搜索API
export const employeeSearchAPI = {
  // 搜索员工
  search: (query: string) =>
    api.get('/employees/search', { params: { query } }),
};

// 员工相关API
export const employeeAPI = {
  // 扫描员工工卡
  scan: (qrCode: string) =>
    api.post('/employees/scan', { qr_code: qrCode }),
};

// 领取相关API
export const pickupAPI = {
  // 执行领取
  execute: (data: any) =>
    api.post('/pickup/execute', data),
  
  // 撤销领取记录
  undo: (recordId: string) =>
    api.post('/pickup/undo', { record_id: recordId }),
  
  // 获取历史记录
  getHistory: (params?: any) =>
    api.get('/history/pickup', { params }),
};

export default api;
