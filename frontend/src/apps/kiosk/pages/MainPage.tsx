import React, { useState, useEffect } from 'react';
import { Layout, message, Row, Col } from 'antd';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { itemAPI, employeeAPI, pickupAPI, itemSelectAPI, employeeSearchAPI } from '../services/api';
import QRScanner from '../components/QRScanner';
import HeaderComponent from '../components/HeaderComponent';
import RecentPickupsComponent from '../components/RecentPickupsComponent';
import ItemCardComponent from '../components/ItemCardComponent';
import EmployeeCardComponent from '../components/EmployeeCardComponent';
import NumberKeyboardComponent from '../components/NumberKeyboardComponent';
import ItemSearchModal from '../components/ItemSearchModal';
import EmployeeSearchModal from '../components/EmployeeSearchModal';
import { KioskQRCodeService } from '../services/qrCodeService';
import { QRCodeType } from '@shared/services/qrCodeService';

const { Content } = Layout;

interface ItemInfo {
  item_id: number;
  item_name: string;
  item_code: string;
  category_name: string;
  brand: string;
  spec_material: string;
  size_dimension: string;
  purchase_unit: string;
  inventory_unit: string;
  qty_per_up: number;
  available_stock: number;
  min_quantity: number;
  image_url?: string;
}

interface EmployeeInfo {
  employee_id: number;
  employee_code: string;
  employee_name: string;
  department_id: number;
  department_name: string;
  position: string;
}

const MainPage: React.FC = () => {
  const { user, hasPermission } = useAuth();
  const navigate = useNavigate();
  
  const [itemInfo, setItemInfo] = useState<ItemInfo | null>(null);
  const [employeeInfo, setEmployeeInfo] = useState<EmployeeInfo | null>(null);
  const [quantity, setQuantity] = useState<number>(1);
  const [isPositiveMode, setIsPositiveMode] = useState<boolean>(true);
  const [loading, setLoading] = useState(false);
  const [showUnifiedScanner, setShowUnifiedScanner] = useState(false);
  const [showItemSearch, setShowItemSearch] = useState(false);
  const [showEmployeeSearch, setShowEmployeeSearch] = useState(false);
  const [itemSearchQuery, setItemSearchQuery] = useState('');
  const [employeeSearchQuery, setEmployeeSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [recentPickups, setRecentPickups] = useState<any[]>([]);
  const [pickupLoading, setPickupLoading] = useState(false);

  // 弹窗打开时自动加载物品列表
  useEffect(() => {
    if (showItemSearch && searchResults.length === 0) {
      handleItemSelect();
    }
  }, [showItemSearch]);

  // 检查权限
  useEffect(() => {
    if (!hasPermission('item.read')) {
      message.error('权限不足，无法访问此页面');
      // 不退出，只显示错误提示
      return;
    }
  }, [hasPermission]);

  // 加载最近领取记录
  useEffect(() => {
    loadRecentPickups();
  }, []);

  // 加载最近领取记录
  const loadRecentPickups = async () => {
    try {
      setPickupLoading(true);
      const response = await pickupAPI.getHistory({ limit: 3 });
      
      // 确保响应数据是数组格式
      if (Array.isArray(response)) {
        console.log('领取历史数据（直接数组）:', response);
        setRecentPickups(response);
      } else if (response && typeof response === 'object' && Array.isArray((response as any).items)) {
        // 如果响应是分页格式
        console.log('领取历史数据（分页格式）:', (response as any).items);
        setRecentPickups((response as any).items);
      } else if (response && typeof response === 'object' && (response as any).data && Array.isArray((response as any).data)) {
        // 如果响应包装在data字段中
        console.log('领取历史数据（包装格式）:', (response as any).data);
        setRecentPickups((response as any).data);
      } else {
        console.warn('领取历史数据格式异常:', response);
        setRecentPickups([]);
      }
    } catch (error) {
      console.error('加载最近领取记录失败:', error);
      setRecentPickups([]);
    } finally {
      setPickupLoading(false);
    }
  };

  // 撤销领取记录
  const handleUndoPickup = async (record: any) => {
    try {
      // 检查记录ID是否存在
      if (!record.record_id) {
        message.error('撤销失败：缺少记录ID');
        return;
      }
      
      console.log('撤销领取记录:', record.record_id);
      
      // 调用撤销API，传递记录ID
      const response = await pickupAPI.undo(record.record_id);
      
      if (response && (response as any).success) {
        message.success('撤销领取成功！');
        // 重新加载最近记录
        loadRecentPickups();
      } else {
        message.error((response as any)?.message || '撤销失败');
      }
    } catch (error: any) {
      console.error('撤销领取失败:', error);
      message.error(error.response?.data?.detail || '撤销操作失败');
    }
  };

  // 统一处理扫描（自动识别物品码和员工卡）
  const handleUnifiedScan = async (qrCode: string) => {
    try {
      // 首先验证二维码格式
      if (!KioskQRCodeService.isValidQRCode(qrCode)) {
        message.error('无效的二维码格式');
        return;
      }

      // 获取二维码类型
      const qrType = KioskQRCodeService.getQRCodeType(qrCode);
      console.log('检测到二维码类型:', qrType);

      if (qrType === QRCodeType.ITEM) {
        // 处理物品二维码
        await handleItemScanLogic(qrCode);
      } else if (qrType === QRCodeType.CUSTOMER) {
        // 处理员工二维码
        await handleEmployeeScanLogic(qrCode);
      } else {
        message.error('无法识别二维码类型，请扫描系统生成的物品或员工二维码');
        return;
      }
      
    } catch (error: any) {
      console.error('统一扫描错误:', error);
      message.error('扫描失败，请重试');
    }
    setShowUnifiedScanner(false);
  };

  // 物品扫描逻辑（从原handleItemScan提取）
  const handleItemScanLogic = async (qrCode: string) => {
    // 从二维码中提取物品编码
    const itemCode = KioskQRCodeService.extractItemCodeFromQRCode(qrCode);
    if (!itemCode) {
      message.error('二维码格式错误，无法提取物品编码');
      return;
    }

    console.log('从二维码提取的物品编码:', itemCode);

    // 调用后端接口查询物品信息
    const response = await itemSelectAPI.getAvailable(itemCode);
    console.log('物品查询响应:', response);

    // 由于响应拦截器已经返回了response.data，所以直接访问
    const data = response as any;
    if (data && Array.isArray(data) && data.length > 0) {
      // 找到物品，设置第一个匹配的结果
      setItemInfo(data[0]);
      setQuantity(1); // 重置数量
      message.success('物品扫描成功！');
    } else {
      message.error('未找到对应的物品信息');
    }
  };

  // 员工扫描逻辑（从原handleEmployeeScan提取）
  const handleEmployeeScanLogic = async (qrCode: string) => {
    // 从二维码中提取员工工号
    const employeeCode = KioskQRCodeService.extractEmployeeUsernameFromQRCode(qrCode);
    if (!employeeCode) {
      message.error('二维码格式错误，无法提取员工工号');
      return;
    }

    console.log('从二维码提取的员工工号:', employeeCode);

    // 调用后端接口查询员工信息（支持工号搜索）
    const response = await employeeSearchAPI.search(employeeCode);
    console.log('员工查询响应:', response);

    // 由于响应拦截器已经返回了response.data，所以直接访问
    const data = response as any;
    if (data && Array.isArray(data) && data.length > 0) {
      // 找到员工，设置第一个匹配的结果
      setEmployeeInfo(data[0]);
      message.success('员工验证成功！');
    } else {
      message.error('未找到对应的员工信息');
    }
  };

  // 手动选择物品
  const handleItemSelect = async () => {
    setSearchLoading(true);
    try {
      // 获取有库存的物品列表，支持筛选
      const response = await itemSelectAPI.getAvailable(itemSearchQuery.trim());
      const data = response as any;
      
      // 确保数据是数组格式
      if (Array.isArray(data)) {
        setSearchResults(data);
        if (data.length > 0) {
          message.success(`找到 ${data.length} piece 有库存的物品`);
        } else {
          message.warning('未找到有库存的物品');
        }
      } else {
        console.warn('物品搜索响应格式异常:', data);
        setSearchResults([]);
        message.warning('未找到有库存的物品');
      }
    } catch (error: any) {
      console.error('获取物品列表错误:', error);
      message.error('获取失败');
    } finally {
      setSearchLoading(false);
    }
  };

  // 选择物品
  const handleSelectItem = (item: any) => {
    setItemInfo(item);
    setQuantity(1); // 重置数量
    setShowItemSearch(false);
    setItemSearchQuery('');
    setSearchResults([]);
    message.success('物品选择成功！');
  };

  // 手动搜索员工
  const handleEmployeeSearch = async () => {
    if (!employeeSearchQuery.trim()) {
      message.error('请输入员工用户名或工号');
      return;
    }

    setSearchLoading(true);
    try {
      const response = await employeeSearchAPI.search(employeeSearchQuery.trim());
      const data = response as any;
      
      // 确保数据是数组格式
      if (Array.isArray(data)) {
        setSearchResults(data);
        if (data.length > 0) {
          message.success(`找到员工: ${data[0].employee_name}`);
        } else {
          message.warning('未找到匹配的员工');
        }
      } else {
        console.warn('员工搜索响应格式异常:', data);
        setSearchResults([]);
        message.warning('未找到匹配的员工');
      }
    } catch (error: any) {
      console.error('搜索员工错误:', error);
      message.error('搜索失败');
    } finally {
      setSearchLoading(false);
    }
  };

  // 选择员工
  const handleSelectEmployee = (employee: any) => {
    setEmployeeInfo(employee);
    setShowEmployeeSearch(false);
    setEmployeeSearchQuery('');
    setSearchResults([]);
    message.success('员工选择成功！');
  };

  // 执行领取操作
  const handlePickup = async () => {
    if (!itemInfo || !employeeInfo) {
      message.error('请先扫描物品和员工');
      return;
    }

    if (quantity <= 0) {
      message.error('请输入有效的数量');
      return;
    }

    if (quantity > itemInfo.available_stock) {
      message.error(`库存不足，当前可用库存: ${itemInfo.available_stock}`);
      return;
    }

    setLoading(true);
    try {
      const response = await pickupAPI.execute({
        item_id: itemInfo.item_id,
        employee_id: employeeInfo.employee_id,
        quantity: quantity,
        unit: itemInfo.inventory_unit,
        usage_purpose: '日常使用',
        item_qr_code: 'SCANNED',
        user_card_code: 'SCANNED',
        location: user?.department_name || '未知',
        device_info: 'Kiosk终端',
        notes: '通过Kiosk终端领取'
      });

      // 由于响应拦截器已经返回了response.data，所以直接访问
      const data = response as any;
      if (data && data.success) {
        message.success('物品领取成功！');
        // 重置状态
        setItemInfo(null);
        setEmployeeInfo(null);
        setQuantity(1);
        // 刷新最近领取记录
        loadRecentPickups();
        // 刷新库存信息
        // 这里可以重新扫描物品来获取最新库存
      } else {
        message.error(data?.message || '领取失败');
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '领取操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout style={{ 
      height: '100vh', 
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <HeaderComponent departmentName={user?.department_name} />

      <Content style={{ 
        padding: '8px', 
        background: '#f5f5f5',
        flex: 1,
        overflow: 'auto',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* 最近领取记录 - 限制高度为三条记录的空间 */}
        <div style={{ 
          marginBottom: '24px', 
          flexShrink: 0,
          height: '120px', // 三条记录的高度：3 * 32px + 2 * 6px + 16px padding
          overflow: 'hidden'
        }}>
          <RecentPickupsComponent 
            recentPickups={recentPickups}
            pickupLoading={pickupLoading}
            onUndoPickup={handleUndoPickup}
          />
        </div>

        {/* 上方：物品和员工信息卡片 */}
        <Row gutter={[24, 24]} style={{ 
          marginBottom: '32px', 
          flexShrink: 0,
          flex: 1,
          display: 'flex',
          alignItems: 'flex-start'
        }}>
          {/* 物品卡片 - 左半边，居中显示 */}
          <Col span={12} style={{ display: 'flex', justifyContent: 'center' }}>
            <ItemCardComponent 
              itemInfo={itemInfo}
              onScanClick={() => setShowUnifiedScanner(true)}
              onManualSelectClick={() => setShowItemSearch(true)}
            />
          </Col>

          {/* 员工卡片 - 右半边，居中显示 */}
          <Col span={12} style={{ display: 'flex', justifyContent: 'center' }}>
            <EmployeeCardComponent 
              employeeInfo={employeeInfo}
              onScanClick={() => setShowUnifiedScanner(true)}
              onManualSelectClick={() => setShowEmployeeSearch(true)}
            />
          </Col>
        </Row>
      </Content>

      {/* 底部：数量输入键盘 */}
      <NumberKeyboardComponent 
        quantity={quantity}
        setQuantity={setQuantity}
        isPositiveMode={isPositiveMode}
        setIsPositiveMode={setIsPositiveMode}
        itemInfo={itemInfo}
        employeeInfo={employeeInfo}
        loading={loading}
        onConfirm={handlePickup}
      />

      {/* 统一扫描弹窗 */}
      {showUnifiedScanner && (
        <QRScanner
          visible={showUnifiedScanner}
          onClose={() => setShowUnifiedScanner(false)}
          onScan={handleUnifiedScan}
          title="智能扫描 - 物品或员工"
        />
      )}

      {/* 物品选择弹窗 */}
      <ItemSearchModal 
        visible={showItemSearch}
        onClose={() => {
          setShowItemSearch(false);
          setItemSearchQuery('');
          setSearchResults([]);
        }}
        departmentName={user?.department_name}
        searchQuery={itemSearchQuery}
        setSearchQuery={setItemSearchQuery}
        searchResults={searchResults}
        searchLoading={searchLoading}
        onSearch={handleItemSelect}
        onSelectItem={handleSelectItem}
      />

      {/* 员工搜索弹窗 */}
      <EmployeeSearchModal 
        visible={showEmployeeSearch}
        onClose={() => {
          setShowEmployeeSearch(false);
          setEmployeeSearchQuery('');
          setSearchResults([]);
        }}
        departmentName={user?.department_name}
        searchQuery={employeeSearchQuery}
        setSearchQuery={setEmployeeSearchQuery}
        searchResults={searchResults}
        searchLoading={searchLoading}
        onSearch={handleEmployeeSearch}
        onSelectEmployee={handleSelectEmployee}
      />
    </Layout>
  );
};

export default MainPage;
