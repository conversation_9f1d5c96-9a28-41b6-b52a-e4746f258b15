import React, { useState, useEffect } from 'react';
import { Layout, Card, Table, Button, DatePicker, Input, Space, Typography, message } from 'antd';
import { ArrowLeftOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { pickupAPI } from '../services/api';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@shared/config/dateFormats';

const { Header, Content } = Layout;
const { Title } = Typography;
const { RangePicker } = DatePicker;

interface HistoryRecord {
  record_id: string;
  item_name: string;
  item_code: string;
  employee_name: string;
  quantity: number;
  unit: string;
  usage_time: string;
  location: string;
  notes: string;
}

const HistoryPage: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState<HistoryRecord[]>([]);
  const [searchText, setSearchText] = useState('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

  // 加载历史记录
  const loadHistory = async () => {
    setLoading(true);
    try {
      const response = await pickupAPI.getHistory({ limit: 100 });
      // 由于响应拦截器已经返回了response.data，所以直接访问
      setRecords((response as any) || []);
    } catch (error: any) {
      message.error('加载历史记录失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadHistory();
  }, []);

  // 过滤记录
  const filteredRecords = records.filter(record => {
    const matchesSearch = !searchText || 
      record.item_name.toLowerCase().includes(searchText.toLowerCase()) ||
      record.item_code.toLowerCase().includes(searchText.toLowerCase()) ||
      record.employee_name.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesDate = !dateRange || (
      dayjs(record.usage_time).isAfter(dateRange[0], 'day') &&
      dayjs(record.usage_time).isBefore(dateRange[1], 'day')
    );
    
    return matchesSearch && matchesDate;
  });

  const columns = [
    {
      title: '记录ID',
      dataIndex: 'record_id',
      key: 'record_id',
      width: 180,
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '物品名称',
      dataIndex: 'item_name',
      key: 'item_name',
      width: 150,
    },
    {
      title: '物品编码',
      dataIndex: 'item_code',
      key: 'item_code',
      width: 120,
    },
    {
      title: '领取人',
      dataIndex: 'employee_name',
      key: 'employee_name',
      width: 120,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      render: (quantity: number, record: HistoryRecord) => (
        <span>{quantity} {record.unit}</span>
      ),
    },
    {
      title: '领取时间',
      dataIndex: 'usage_time',
      key: 'usage_time',
      width: 180,
      render: (text: string) => dayjs(text).format(DATE_FORMATS.DATE_TIME),
    },
    {
      title: '领取地点',
      dataIndex: 'location',
      key: 'location',
      width: 120,
    },
    {
      title: '备注',
      dataIndex: 'notes',
      key: 'notes',
      ellipsis: true,
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        background: '#fff', 
        padding: '0 24px', 
        display: 'flex', 
        alignItems: 'center',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        height: '80px'
      }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/kiosk/')}
          style={{ marginRight: 16, height: '50px', fontSize: '16px' }}
          size="large"
        >
          返回
        </Button>
        <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
          领取历史记录
        </Title>
      </Header>

      <Content style={{ padding: '32px', background: '#f5f5f5' }}>
        <Card>
          {/* 搜索和筛选区域 */}
          <div style={{ marginBottom: 32 }}>
            <Space wrap size="large">
              <Input
                placeholder="搜索物品名称、编码或员工姓名"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                prefix={<SearchOutlined />}
                style={{ width: 350, height: '50px', fontSize: '16px' }}
                allowClear
                size="large"
              />
              <RangePicker
                value={dateRange}
                onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
                placeholder={['开始日期', '结束日期']}
                allowClear
                style={{ height: '50px' }}
              />
              <Button
                icon={<ReloadOutlined />}
                onClick={loadHistory}
                loading={loading}
                size="large"
                style={{ height: '50px', fontSize: '16px' }}
              >
                刷新
              </Button>
            </Space>
          </div>

          {/* 统计信息 */}
          <div style={{ marginBottom: 24 }}>
            <Space size="large">
              <span style={{ fontSize: '16px' }}>
                总记录数：<strong style={{ fontSize: '18px' }}>{filteredRecords.length}</strong>
              </span>
              <span style={{ fontSize: '16px' }}>
                总领取数量：<strong style={{ fontSize: '18px' }}>
                  {filteredRecords.reduce((sum, record) => sum + record.quantity, 0)}
                </strong>
              </span>
            </Space>
          </div>

          {/* 数据表格 */}
          <Table
            columns={columns}
            dataSource={filteredRecords}
            rowKey="record_id"
            loading={loading}
            pagination={{
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            }}
            scroll={{ x: 1200 }}
            size="middle"
          />
        </Card>
      </Content>
    </Layout>
  );
};

export default HistoryPage;
