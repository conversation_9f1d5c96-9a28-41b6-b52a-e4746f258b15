import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';
import AdminApp from './apps/admin/App';
import KioskApp from './apps/kiosk/App';
import InboundApp from './apps/inbound/App';
import './App.css';

function App() {
  return (
    <ConfigProvider locale={enUS}>
      <Router>
        <div className="App">
          <Routes>
            {/* 默认重定向到管理端 */}
            <Route path="/" element={<Navigate to="/admin" replace />} />
            
            {/* Admin 管理端 - 包含登录和主应用，支持语言切换 */}
            <Route path="/admin/*" element={<AdminApp />} />
            
            {/* Kiosk 领用端 - 默认英文，不支持语言切换 */}
            <Route path="/kiosk/*" element={<KioskApp />} />
            
            {/* Inbound 入库端 - 默认英文，不支持语言切换 */}
            <Route path="/inbound/*" element={<InboundApp />} />
          </Routes>
        </div>
      </Router>
    </ConfigProvider>
  );
}

export default App; 