.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 自定义样式 */
.ant-layout {
  background: #f0f2f5;
}

.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 顶部导航栏样式 - 参考淘宝、阿里巴巴风格 */
.ant-layout-header {
  background: #001529 !important;
  border-bottom: none !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
}

/* 顶部菜单样式 */
.ant-menu-horizontal {
  background: transparent !important;
  border-bottom: none !important;
  line-height: 64px !important;
}

.ant-menu-horizontal > .ant-menu-item,
.ant-menu-horizontal > .ant-menu-submenu {
  color: rgba(255, 255, 255, 0.85) !important;
  border-bottom: none !important;
  margin: 0 4px !important;
  padding: 0 16px !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
}

.ant-menu-horizontal > .ant-menu-item:hover,
.ant-menu-horizontal > .ant-menu-submenu:hover {
  color: #fff !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

.ant-menu-horizontal > .ant-menu-item-selected {
  color: #fff !important;
  background: rgba(255, 255, 255, 0.2) !important;
  font-weight: 600 !important;
}

.ant-menu-horizontal > .ant-menu-item-selected::after {
  border-bottom: none !important;
}

/* 用户信息区域样式 */
.ant-dropdown-trigger {
  color: rgba(255, 255, 255, 0.85) !important;
}

.ant-dropdown-trigger:hover {
  color: #fff !important;
}

/* Logo和品牌名称样式 */
.brand-logo {
  color: #fff !important;
  font-weight: 600 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-header {
    padding: 0 12px !important;
  }
  
  .ant-menu-horizontal > .ant-menu-item,
  .ant-menu-horizontal > .ant-menu-submenu {
    padding: 0 8px !important;
    margin: 0 2px !important;
  }
}

/* 修复整体布局 - 确保顶部菜单和内容区域正确排布 */
.ant-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.ant-layout-header {
  flex-shrink: 0;
  height: 64px;
}

.ant-layout-content {
  flex: 1;
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
  overflow-x: hidden; /* 防止水平滚动条 */
}

/* 内容区域容器 */
.content-container {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  min-height: calc(100vh - 112px); /* 减去header高度(64px)和padding(48px) */
  overflow: hidden; /* 防止整体滚动条 */
}

/* 淘宝风格分类导航布局 */
.category-container {
  position: relative;
  display: flex;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* 固定分类筛选器 */
.category-filter-fixed {
  position: fixed;
  left: 24px;
  top: 112px; /* 在顶部导航栏下方 */
  width: 180px;
  height: calc(100vh - 144px);
  z-index: 1000;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* 内容区域样式 */
.content-area-with-filter {
  margin-left: 200px;
  padding-top: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .category-filter-fixed {
    width: 160px;
    left: 16px;
  }
  .content-area-with-filter {
    margin-left: 180px;
  }
  .sub-category-panel {
    left: 180px;
  }
}

@media (max-width: 768px) {
  .category-filter-fixed {
    position: relative;
    left: auto;
    top: auto;
    width: 100%;
    height: auto;
    margin-bottom: 16px;
  }
  .content-area-with-filter {
    margin-left: 0;
  }
}

/* 左侧一级分类导航 */
.main-category {
  width: 100%;
  background: #fafafa;
  border-right: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  position: relative;
  z-index: 1;
  height: 100%;
}

.category-header {
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.category-list {
  flex: 1;
  overflow-y: auto;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid #f0f0f0;
}

.category-item:hover {
  background: #f5f5f5;
}

.category-item.hovered {
  background: #e6f7ff;
  border-right: 3px solid #1890ff;
}

.category-item.selected {
  background: #e6f7ff;
  border-right: 3px solid #1890ff;
  font-weight: 600;
}

.category-name {
  flex: 1;
  color: #333;
  font-size: 14px;
}

.category-arrow {
  color: #999;
  font-size: 12px;
  transition: transform 0.3s ease;
  margin-left: 4px;
}

.category-item:hover .category-arrow {
  transform: translateX(2px);
}

.bottom-recommendation {
  padding: 16px;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
}

.recommendation-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  cursor: pointer;
  color: #666;
}

.recommendation-item:hover {
  color: #1890ff;
}

.recommendation-icon {
  margin-right: 8px;
  font-size: 14px;
}

.recommendation-text {
  font-size: 14px;
}

/* 二级分类面板 - 修复遮挡问题 */
.sub-category-panel {
  position: fixed;
  width: 800px;
  max-height: calc(100vh - 140px);
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.2);
  z-index: 1000; /* 使用合理的z-index值 */
  animation: slideIn 0.3s ease;
  overflow: hidden;
  pointer-events: auto;
  margin-left: 0;
  border-left: none;
  /* 确保不被任何元素遮挡 */
  isolation: isolate;
  /* 确保面板与分类项之间没有间隙 */
  transform: translateZ(0); /* 创建新的层叠上下文 */
  /* 确保面板始终可见 */
  will-change: transform;
  /* 防止被其他元素遮挡 */
  contain: layout style paint;
  /* 添加一个小的左边距，确保与一级分类有连接 */
  margin-left: -4px;
  /* 添加一个透明的连接区域 */
  &::before {
    content: '';
    position: absolute;
    left: -4px;
    top: 0;
    width: 4px;
    height: 100%;
    background: transparent;
    pointer-events: auto;
  }
}

/* 确保面板内容可滚动 */
.sub-category-panel .category-group {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  /* 确保滚动条样式正确 */
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 #f5f5f5;
}

/* 自定义滚动条样式 */
.sub-category-panel .category-group::-webkit-scrollbar {
  width: 6px;
}

.sub-category-panel .category-group::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.sub-category-panel .category-group::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.sub-category-panel .category-group::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 二级分类分组 */
.category-group {
  padding: 20px;
}

.group-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.items {
  display: flex;
  flex-wrap: wrap;
}

.item-link {
  display: inline-block;
  padding: 6px 12px;
  margin-right: 12px;
  margin-bottom: 8px;
  color: #666;
  background: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.item-link:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.item-link.selected {
  background: #1890ff;
  color: #fff;
}

.current-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  margin: 12px 16px;
}

.filter-info {
  color: #52c41a;
  font-size: 14px;
}

.clear-button {
  background: none;
  border: 1px solid #52c41a;
  color: #52c41a;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.clear-button:hover {
  background: #52c41a;
  color: #fff;
}

.category-filter-sider {
  background: #fff;
  border-right: 1px solid #f0f0f0;
  overflow: hidden;
}

@media (max-width: 1200px) {
  .main-category {
    width: 100%;
  }
  
  .sub-category-panel {
    left: 180px;
    width: 600px;
  }
  
  .category-item {
    padding: 10px 12px;
  }
  
  .item-link {
    padding: 4px 8px;
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .category-container {
    flex-direction: column;
  }
  
  .main-category {
    width: 100%;
    height: auto;
  }
  
  .sub-category-panel {
    position: relative;
    left: 0;
    width: 100%;
    max-height: 300px;
  }
  
  .category-item {
    padding: 12px 16px;
  }
  
  .item-link {
    padding: 6px 10px;
    font-size: 14px;
  }
}

/* 物品卡片网格布局 */
.item-cards-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  padding: 16px 0;
  position: relative;
  z-index: 1; /* 确保在二级分类面板之下 */
}

.item-card-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  z-index: 1; /* 确保在二级分类面板之下 */
}

/* 确保卡片不会遮挡面板 */
.item-card-wrapper .ant-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  z-index: 1; /* 确保在二级分类面板之下 */
}

/* 确保表格不会遮挡面板 */
.ant-table-wrapper {
  position: relative;
  z-index: 1; /* 确保在二级分类面板之下 */
}

.ant-table {
  position: relative;
  z-index: 1; /* 确保在二级分类面板之下 */
}

/* 确保分页器不会遮挡面板 */
.ant-pagination {
  position: relative;
  z-index: 1; /* 确保在二级分类面板之下 */
}

/* 确保搜索框和按钮不会遮挡面板 */
.ant-input-search {
  position: relative;
  z-index: 1; /* 确保在二级分类面板之下 */
}

.ant-btn {
  position: relative;
  z-index: 1; /* 确保在二级分类面板之下 */
}

/* 确保抽屉不会遮挡面板 */
.ant-drawer {
  z-index: 1000; /* 确保在二级分类面板之上 */
}

/* 确保模态框不会遮挡面板 */
.ant-modal {
  z-index: 1000; /* 确保在二级分类面板之上 */
}

.item-card-wrapper .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background: #fff;
}

.ant-card-body {
  padding: 16px;
  background: #fff;
}

.ant-image {
  border-radius: 4px;
  overflow: hidden;
}

.ant-typography h5 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.ant-tag {
  margin: 2px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.2;
  padding: 2px 6px;
  border: none;
}

@media (max-width: 576px) {
  .ant-card-body {
    padding: 12px;
  }
  
  .ant-typography h5 {
    font-size: 14px;
    margin-bottom: 6px;
  }
  
  .ant-tag {
    font-size: 11px;
    padding: 1px 4px;
    margin: 1px;
  }
  
  .ant-btn-sm {
    font-size: 12px;
    padding: 2px 6px;
    height: 24px;
  }
}

@media (min-width: 577px) and (max-width: 768px) {
  .ant-card-body {
    padding: 14px;
  }
  
  .ant-typography h5 {
    font-size: 15px;
    margin-bottom: 7px;
  }
  
  .ant-tag {
    font-size: 11px;
    padding: 2px 5px;
  }
}

.item-card-wrapper .ant-card-body > div:last-child {
  margin-top: auto;
}

.item-cards-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  padding: 16px 0;
}

.item-card-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.item-card-wrapper .ant-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.item-card-wrapper .ant-card-cover {
  flex-shrink: 0;
  height: 200px;
  overflow: hidden;
}

.item-card-wrapper .ant-card-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.item-card-wrapper .ant-btn {
  font-size: 12px;
  padding: 4px 8px;
  height: 28px;
  border-radius: 4px;
}

.item-card-wrapper .ant-space {
  margin-top: 8px;
  flex-wrap: wrap;
}

.item-card-wrapper .ant-typography {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 576px) {
  .item-cards-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .item-card-wrapper {
    min-height: auto;
  }
  
  /* 移动端确保面板在最上层 */
  .sub-category-panel {
    z-index: 1000;
  }
}

@media (min-width: 577px) and (max-width: 768px) {
  .item-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 12px;
  }
  
  .item-card-wrapper {
    min-height: auto;
  }
  
  /* 平板端确保面板在最上层 */
  .sub-category-panel {
    z-index: 1000;
  }
}

@media (min-width: 769px) and (max-width: 992px) {
  .item-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
  
  .item-card-wrapper {
    min-height: auto;
  }
  
  /* 中等屏幕确保面板在最上层 */
  .sub-category-panel {
    z-index: 1000;
  }
}

@media (min-width: 993px) and (max-width: 1200px) {
  .item-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
  
  .item-card-wrapper {
    min-height: auto;
  }
  
  /* 大屏幕确保面板在最上层 */
  .sub-category-panel {
    z-index: 1000;
  }
}

@media (min-width: 1201px) {
  .item-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
  
  .item-card-wrapper {
    min-height: auto;
  }
  
  /* 超大屏幕确保面板在最上层 */
  .sub-category-panel {
    z-index: 1000;
  }
}

.item-card-wrapper .ant-tag[color="purple"] {
  background: #f9f0ff;
  color: #722ed1;
  border: 1px solid #d3adf7;
}

.item-card-wrapper .ant-tag[color="cyan"] {
  background: #e6fffb;
  color: #13c2c2;
  border: 1px solid #87e8de;
}

.item-card-wrapper .ant-tag[color="geekblue"] {
  background: #f0f5ff;
  color: #2f54eb;
  border: 1px solid #adc6ff;
}

.item-card-wrapper .ant-space {
  margin-top: 8px;
  flex-wrap: wrap;
}

.item-card-wrapper .ant-space .ant-tag {
  margin: 2px;
  border-radius: 4px;
  font-size: 11px;
  line-height: 1.2;
  padding: 2px 6px;
  border: none;
}

.ant-descriptions-item-label {
  font-weight: 600;
  color: #333;
}

.ant-descriptions-item-content {
  color: #666;
}

.ant-descriptions-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.item-detail-header {
  margin-bottom: 24px;
}

.item-detail-image {
  border-radius: 8px;
  overflow: hidden;
}

.item-detail-tabs {
  margin-top: 24px;
}

.item-detail-tabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 600;
}

.item-detail-tabs .ant-tabs-content-holder {
  padding: 24px 0;
}

.item-detail-timeline {
  margin-top: 24px;
}

.item-detail-timeline .ant-timeline-item {
  padding-bottom: 16px;
}

.item-detail-timeline .ant-timeline-item-dot {
  background: #1890ff;
  border-color: #1890ff;
}

.item-detail-timeline .ant-avatar {
  background: #1890ff;
}

.item-detail-descriptions {
  background: #fafafa;
  padding: 24px;
  border-radius: 8px;
  margin-top: 24px;
}

.item-detail-descriptions .ant-descriptions-title {
  color: #333;
  font-size: 16px;
  margin-bottom: 16px;
}

.item-detail-descriptions .ant-descriptions-item-label {
  color: #666;
  font-weight: 600;
}

.item-detail-descriptions .ant-descriptions-item-content {
  color: #333;
}

.item-detail-tag {
  margin: 4px;
} 