const path = require('path');

module.exports = {
  webpack: {
    alias: {
      '@shared': path.resolve(__dirname, 'src/shared'),
      '@admin': path.resolve(__dirname, 'src/apps/admin'),
      '@kiosk': path.resolve(__dirname, 'src/apps/kiosk'),
      '@inbound': path.resolve(__dirname, 'src/apps/inbound'),
    },
  },
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
      },
      '/static/sample-images': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
      },
      '/uploads': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/uploads': '/uploads',
        },
      },
    },
  },
};
