# 采购申请状态流程 i18n 测试

## 修复内容

修复了 `RequestStatusSteps` 组件中状态说明的国际化支持，现在所有状态说明都使用 i18n 翻译而不是硬编码的中文。

## 修复前的问题

- 状态说明硬编码为中文："进行中"、"已通过"、"等待中"
- 不支持多语言切换
- 不符合系统的国际化要求

## 修复后的改进

### 1. 状态说明完全国际化
- **进行中**: `t('purchase.processing')`
- **已通过**: `t('purchase.approved')`  
- **等待中**: `t('purchase.waiting')`
- **错误**: `t('purchase.error')`

### 2. 支持的语言
- **中文**: 进行中、已通过、等待中、错误
- **英文**: Processing、Approved、Waiting、Error

### 3. 使用的翻译键
```typescript
step.status === 'process' ? t('purchase.processing') :  // 进行中/Processing
step.status === 'finish' ? t('purchase.approved') :     // 已通过/Approved
step.status === 'error' ? t('purchase.error') :         // 错误/Error
step.status === 'wait' ? t('purchase.waiting') :        // 等待中/Waiting
```

## 测试用例

### 测试1: 中文环境
- 语言设置: 中文
- 预期结果:
  - 进行中 → "进行中"
  - 已通过 → "已通过"
  - 等待中 → "等待中"
  - 错误 → "错误"

### 测试2: 英文环境
- 语言设置: 英文
- 预期结果:
  - 进行中 → "Processing"
  - 已通过 → "Approved"
  - 等待中 → "Waiting"
  - 错误 → "Error"

### 测试3: 语言切换
- 从中文切换到英文
- 预期结果: 状态说明自动更新为英文
- 从英文切换到中文
- 预期结果: 状态说明自动更新为中文

## 验证方法

1. **查看状态流程组件**
   - 在采购申请详情页面查看状态流程
   - 确认状态说明显示为当前语言

2. **切换语言**
   - 在系统设置中切换语言
   - 确认状态说明自动更新

3. **检查翻译完整性**
   - 确认所有状态都有对应的翻译
   - 确认翻译文本准确无误

## 相关语言文件

### 中文翻译 (purchase.json)
```json
{
  "processing": "进行中",
  "approved": "已批准",
  "waiting": "等待中",
  "error": "错误"
}
```

### 英文翻译 (purchase.en.json)
```json
{
  "processing": "Processing",
  "approved": "Approved", 
  "waiting": "Waiting",
  "error": "Error"
}
```

## 预期效果

修复后，状态流程应该能够：
- ✅ 根据当前语言显示相应的状态说明
- ✅ 支持中英文切换
- ✅ 提供一致的用户体验
- ✅ 符合系统的国际化标准

## 技术实现

- 使用 `useTranslation` hook 获取翻译函数
- 所有状态说明都通过 `t()` 函数获取翻译
- 支持动态语言切换
- 遵循 i18n 最佳实践
